
// The following array has to be sorted by increasing
// opcodes. Otherwise the binary_search will fail.
//
// Additional instructions only supported on HD6309 PAGE1
static const inst_pageX g_hd6309_inst_overlay_table[] = {
	{ 0x01, M680X_INS_OIM, imm8_hid, dir_hid },
	{ 0x02, M680X_INS_AIM, imm8_hid, dir_hid },
	{ 0x05, M680X_INS_EIM, imm8_hid, dir_hid },
	{ 0x0B, M680X_INS_TIM, imm8_hid, dir_hid },
	{ 0x14, M680X_INS_SEXW, inh_hid, inh_hid },
	{ 0x61, M680X_INS_OIM, imm8_hid, idx09_hid },
	{ 0x62, M680X_INS_AIM, imm8_hid, idx09_hid },
	{ 0x65, M680X_INS_EIM, imm8_hid, idx09_hid },
	{ 0x6B, M680X_INS_TIM, imm8_hid, idx09_hid },
	{ 0x71, M680X_INS_OIM, imm8_hid, ext_hid },
	{ 0x72, M680X_INS_AIM, imm8_hid, ext_hid },
	{ 0x75, M680X_INS_EIM, imm8_hid, ext_hid },
	{ 0x7B, M680X_INS_TIM, imm8_hid, ext_hid },
	{ 0xCD, M680X_INS_LDQ, imm32_hid, inh_hid },
};

// The following array has to be sorted by increasing
// opcodes. Otherwise the binary_search will fail.
//
// HD6309 PAGE2 instructions (with prefix 0x10)
static const inst_pageX g_hd6309_inst_page2_table[] = {
	// 0x2x, relative long branch instructions
	{ 0x21, M680X_INS_LBRN, rel16_hid, inh_hid },
	{ 0x22, M680X_INS_LBHI, rel16_hid, inh_hid },
	{ 0x23, M680X_INS_LBLS, rel16_hid, inh_hid },
	{ 0x24, M680X_INS_LBCC, rel16_hid, inh_hid },
	{ 0x25, M680X_INS_LBCS, rel16_hid, inh_hid },
	{ 0x26, M680X_INS_LBNE, rel16_hid, inh_hid },
	{ 0x27, M680X_INS_LBEQ, rel16_hid, inh_hid },
	{ 0x28, M680X_INS_LBVC, rel16_hid, inh_hid },
	{ 0x29, M680X_INS_LBVS, rel16_hid, inh_hid },
	{ 0x2a, M680X_INS_LBPL, rel16_hid, inh_hid },
	{ 0x2b, M680X_INS_LBMI, rel16_hid, inh_hid },
	{ 0x2c, M680X_INS_LBGE, rel16_hid, inh_hid },
	{ 0x2d, M680X_INS_LBLT, rel16_hid, inh_hid },
	{ 0x2e, M680X_INS_LBGT, rel16_hid, inh_hid },
	{ 0x2f, M680X_INS_LBLE, rel16_hid, inh_hid },
	// 0x3x
	{ 0x30, M680X_INS_ADDR, rr09_hid, inh_hid },
	{ 0x31, M680X_INS_ADCR, rr09_hid, inh_hid },
	{ 0x32, M680X_INS_SUBR, rr09_hid, inh_hid },
	{ 0x33, M680X_INS_SBCR, rr09_hid, inh_hid },
	{ 0x34, M680X_INS_ANDR, rr09_hid, inh_hid },
	{ 0x35, M680X_INS_ORR, rr09_hid, inh_hid },
	{ 0x36, M680X_INS_EORR, rr09_hid, inh_hid },
	{ 0x37, M680X_INS_CMPR, rr09_hid, inh_hid },
	{ 0x38, M680X_INS_PSHSW, inh_hid, inh_hid },
	{ 0x39, M680X_INS_PULSW, inh_hid, inh_hid },
	{ 0x3a, M680X_INS_PSHUW, inh_hid, inh_hid },
	{ 0x3b, M680X_INS_PULUW, inh_hid, inh_hid },
	{ 0x3f, M680X_INS_SWI2, inh_hid, inh_hid },
	// 0x4x, Register D instructions
	{ 0x40, M680X_INS_NEGD, inh_hid, inh_hid },
	{ 0x43, M680X_INS_COMD, inh_hid, inh_hid },
	{ 0x44, M680X_INS_LSRD, inh_hid, inh_hid },
	{ 0x46, M680X_INS_RORD, inh_hid, inh_hid },
	{ 0x47, M680X_INS_ASRD, inh_hid, inh_hid },
	{ 0x48, M680X_INS_LSLD, inh_hid, inh_hid },
	{ 0x49, M680X_INS_ROLD, inh_hid, inh_hid },
	{ 0x4a, M680X_INS_DECD, inh_hid, inh_hid },
	{ 0x4c, M680X_INS_INCD, inh_hid, inh_hid },
	{ 0x4d, M680X_INS_TSTD, inh_hid, inh_hid },
	{ 0x4f, M680X_INS_CLRD, inh_hid, inh_hid },
	// 0x5x, Register W instructions
	{ 0x53, M680X_INS_COMW, inh_hid, inh_hid },
	{ 0x54, M680X_INS_LSRW, inh_hid, inh_hid },
	{ 0x56, M680X_INS_RORW, inh_hid, inh_hid },
	{ 0x59, M680X_INS_ROLW, inh_hid, inh_hid },
	{ 0x5a, M680X_INS_DECW, inh_hid, inh_hid },
	{ 0x5c, M680X_INS_INCW, inh_hid, inh_hid },
	{ 0x5d, M680X_INS_TSTW, inh_hid, inh_hid },
	{ 0x5f, M680X_INS_CLRW, inh_hid, inh_hid },
	// 0x8x, immediate instructionY with register D,W,Y
	{ 0x80, M680X_INS_SUBW, imm16_hid, inh_hid },
	{ 0x81, M680X_INS_CMPW, imm16_hid, inh_hid },
	{ 0x82, M680X_INS_SBCD, imm16_hid, inh_hid },
	{ 0x83, M680X_INS_CMPD, imm16_hid, inh_hid },
	{ 0x84, M680X_INS_ANDD, imm16_hid, inh_hid },
	{ 0x85, M680X_INS_BITD, imm16_hid, inh_hid },
	{ 0x86, M680X_INS_LDW, imm16_hid, inh_hid },
	{ 0x88, M680X_INS_EORD, imm16_hid, inh_hid },
	{ 0x89, M680X_INS_ADCD, imm16_hid, inh_hid },
	{ 0x8a, M680X_INS_ORD, imm16_hid, inh_hid },
	{ 0x8b, M680X_INS_ADDW, imm16_hid, inh_hid },
	{ 0x8c, M680X_INS_CMPY, imm16_hid, inh_hid },
	{ 0x8e, M680X_INS_LDY, imm16_hid, inh_hid },
	// 0x9x, direct instructions with register D,W,Y
	{ 0x90, M680X_INS_SUBW, dir_hid, inh_hid },
	{ 0x91, M680X_INS_CMPW, dir_hid, inh_hid },
	{ 0x92, M680X_INS_SBCD, dir_hid, inh_hid },
	{ 0x93, M680X_INS_CMPD, dir_hid, inh_hid },
	{ 0x94, M680X_INS_ANDD, dir_hid, inh_hid },
	{ 0x95, M680X_INS_BITD, dir_hid, inh_hid },
	{ 0x96, M680X_INS_LDW, dir_hid, inh_hid },
	{ 0x97, M680X_INS_STW, dir_hid, inh_hid },
	{ 0x98, M680X_INS_EORD, dir_hid, inh_hid },
	{ 0x99, M680X_INS_ADCD, dir_hid, inh_hid },
	{ 0x9a, M680X_INS_ORD, dir_hid, inh_hid },
	{ 0x9b, M680X_INS_ADDW, dir_hid, inh_hid },
	{ 0x9c, M680X_INS_CMPY, dir_hid, inh_hid },
	{ 0x9e, M680X_INS_LDY, dir_hid, inh_hid },
	{ 0x9f, M680X_INS_STY, dir_hid, inh_hid },
	// 0xAx, indexed instructions with register D,W,Y
	{ 0xa0, M680X_INS_SUBW, idx09_hid, inh_hid },
	{ 0xa1, M680X_INS_CMPW, idx09_hid, inh_hid },
	{ 0xa2, M680X_INS_SBCD, idx09_hid, inh_hid },
	{ 0xa3, M680X_INS_CMPD, idx09_hid, inh_hid },
	{ 0xa4, M680X_INS_ANDD, idx09_hid, inh_hid },
	{ 0xa5, M680X_INS_BITD, idx09_hid, inh_hid },
	{ 0xa6, M680X_INS_LDW, idx09_hid, inh_hid },
	{ 0xa7, M680X_INS_STW, idx09_hid, inh_hid },
	{ 0xa8, M680X_INS_EORD, idx09_hid, inh_hid },
	{ 0xa9, M680X_INS_ADCD, idx09_hid, inh_hid },
	{ 0xaa, M680X_INS_ORD, idx09_hid, inh_hid },
	{ 0xab, M680X_INS_ADDW, idx09_hid, inh_hid },
	{ 0xac, M680X_INS_CMPY, idx09_hid, inh_hid },
	{ 0xae, M680X_INS_LDY, idx09_hid, inh_hid },
	{ 0xaf, M680X_INS_STY, idx09_hid, inh_hid },
	// 0xBx, extended instructions with register D,W,Y
	{ 0xb0, M680X_INS_SUBW, ext_hid, inh_hid },
	{ 0xb1, M680X_INS_CMPW, ext_hid, inh_hid },
	{ 0xb2, M680X_INS_SBCD, ext_hid, inh_hid },
	{ 0xb3, M680X_INS_CMPD, ext_hid, inh_hid },
	{ 0xb4, M680X_INS_ANDD, ext_hid, inh_hid },
	{ 0xb5, M680X_INS_BITD, ext_hid, inh_hid },
	{ 0xb6, M680X_INS_LDW, ext_hid, inh_hid },
	{ 0xb7, M680X_INS_STW, ext_hid, inh_hid },
	{ 0xb8, M680X_INS_EORD, ext_hid, inh_hid },
	{ 0xb9, M680X_INS_ADCD, ext_hid, inh_hid },
	{ 0xba, M680X_INS_ORD, ext_hid, inh_hid },
	{ 0xbb, M680X_INS_ADDW, ext_hid, inh_hid },
	{ 0xbc, M680X_INS_CMPY, ext_hid, inh_hid },
	{ 0xbe, M680X_INS_LDY, ext_hid, inh_hid },
	{ 0xbf, M680X_INS_STY, ext_hid, inh_hid },
	// 0xCx, immediate instructions with register S
	{ 0xce, M680X_INS_LDS, imm16_hid, inh_hid },
	// 0xDx, direct instructions with register S,Q
	{ 0xdc, M680X_INS_LDQ, dir_hid, inh_hid },
	{ 0xdd, M680X_INS_STQ, dir_hid, inh_hid },
	{ 0xde, M680X_INS_LDS, dir_hid, inh_hid },
	{ 0xdf, M680X_INS_STS, dir_hid, inh_hid },
	// 0xEx, indexed instructions with register S,Q
	{ 0xec, M680X_INS_LDQ, idx09_hid, inh_hid },
	{ 0xed, M680X_INS_STQ, idx09_hid, inh_hid },
	{ 0xee, M680X_INS_LDS, idx09_hid, inh_hid },
	{ 0xef, M680X_INS_STS, idx09_hid, inh_hid },
	// 0xFx, extended instructions with register S,Q
	{ 0xfc, M680X_INS_LDQ, ext_hid, inh_hid },
	{ 0xfd, M680X_INS_STQ, ext_hid, inh_hid },
	{ 0xfe, M680X_INS_LDS, ext_hid, inh_hid },
	{ 0xff, M680X_INS_STS, ext_hid, inh_hid },
};

// The following array has to be sorted by increasing
// opcodes. Otherwise the binary_search will fail.
//
// HD6309 PAGE3 instructions (with prefix 0x11)
static const inst_pageX g_hd6309_inst_page3_table[] = {
	{ 0x30, M680X_INS_BAND, bitmv_hid, inh_hid },
	{ 0x31, M680X_INS_BIAND, bitmv_hid, inh_hid },
	{ 0x32, M680X_INS_BOR, bitmv_hid, inh_hid },
	{ 0x33, M680X_INS_BIOR, bitmv_hid, inh_hid },
	{ 0x34, M680X_INS_BEOR, bitmv_hid, inh_hid },
	{ 0x35, M680X_INS_BIEOR, bitmv_hid, inh_hid },
	{ 0x36, M680X_INS_LDBT, bitmv_hid, inh_hid },
	{ 0x37, M680X_INS_STBT, bitmv_hid, inh_hid },
	{ 0x38, M680X_INS_TFM, tfm_hid, inh_hid },
	{ 0x39, M680X_INS_TFM, tfm_hid, inh_hid },
	{ 0x3a, M680X_INS_TFM, tfm_hid, inh_hid },
	{ 0x3b, M680X_INS_TFM, tfm_hid, inh_hid },
	{ 0x3c, M680X_INS_BITMD, imm8_hid, inh_hid },
	{ 0x3d, M680X_INS_LDMD, imm8_hid, inh_hid },
	{ 0x3f, M680X_INS_SWI3, inh_hid, inh_hid },
	// 0x4x, Register E instructions
	{ 0x43, M680X_INS_COME, inh_hid, inh_hid },
	{ 0x4a, M680X_INS_DECE, inh_hid, inh_hid },
	{ 0x4c, M680X_INS_INCE, inh_hid, inh_hid },
	{ 0x4d, M680X_INS_TSTE, inh_hid, inh_hid },
	{ 0x4f, M680X_INS_CLRE, inh_hid, inh_hid },
	// 0x5x, Register F instructions
	{ 0x53, M680X_INS_COMF, inh_hid, inh_hid },
	{ 0x5a, M680X_INS_DECF, inh_hid, inh_hid },
	{ 0x5c, M680X_INS_INCF, inh_hid, inh_hid },
	{ 0x5d, M680X_INS_TSTF, inh_hid, inh_hid },
	{ 0x5f, M680X_INS_CLRF, inh_hid, inh_hid },
	// 0x8x, immediate instructions with register U,S,E
	{ 0x80, M680X_INS_SUBE, imm8_hid, inh_hid },
	{ 0x81, M680X_INS_CMPE, imm8_hid, inh_hid },
	{ 0x83, M680X_INS_CMPU, imm16_hid, inh_hid },
	{ 0x86, M680X_INS_LDE, imm8_hid, inh_hid },
	{ 0x8b, M680X_INS_ADDE, imm8_hid, inh_hid },
	{ 0x8c, M680X_INS_CMPS, imm16_hid, inh_hid },
	{ 0x8d, M680X_INS_DIVD, imm8_hid, inh_hid },
	{ 0x8e, M680X_INS_DIVQ, imm16_hid, inh_hid },
	{ 0x8f, M680X_INS_MULD, imm16_hid, inh_hid },
	// 0x9x, direct instructions with register U,S,E,Q
	{ 0x90, M680X_INS_SUBE, dir_hid, inh_hid },
	{ 0x91, M680X_INS_CMPE, dir_hid, inh_hid },
	{ 0x93, M680X_INS_CMPU, dir_hid, inh_hid },
	{ 0x96, M680X_INS_LDE, dir_hid, inh_hid },
	{ 0x97, M680X_INS_STE, dir_hid, inh_hid },
	{ 0x9b, M680X_INS_ADDE, dir_hid, inh_hid },
	{ 0x9c, M680X_INS_CMPS, dir_hid, inh_hid },
	{ 0x9d, M680X_INS_DIVD, dir_hid, inh_hid },
	{ 0x9e, M680X_INS_DIVQ, dir_hid, inh_hid },
	{ 0x9f, M680X_INS_MULD, dir_hid, inh_hid },
	// 0xAx, indexed instructions with register U,S,D,Q
	{ 0xa0, M680X_INS_SUBE, idx09_hid, inh_hid },
	{ 0xa1, M680X_INS_CMPE, idx09_hid, inh_hid },
	{ 0xa3, M680X_INS_CMPU, idx09_hid, inh_hid },
	{ 0xa6, M680X_INS_LDE, idx09_hid, inh_hid },
	{ 0xa7, M680X_INS_STE, idx09_hid, inh_hid },
	{ 0xab, M680X_INS_ADDE, idx09_hid, inh_hid },
	{ 0xac, M680X_INS_CMPS, idx09_hid, inh_hid },
	{ 0xad, M680X_INS_DIVD, idx09_hid, inh_hid },
	{ 0xae, M680X_INS_DIVQ, idx09_hid, inh_hid },
	{ 0xaf, M680X_INS_MULD, idx09_hid, inh_hid },
	// 0xBx, extended instructions with register U,S,D,Q
	{ 0xb0, M680X_INS_SUBE, ext_hid, inh_hid },
	{ 0xb1, M680X_INS_CMPE, ext_hid, inh_hid },
	{ 0xb3, M680X_INS_CMPU, ext_hid, inh_hid },
	{ 0xb6, M680X_INS_LDE, ext_hid, inh_hid },
	{ 0xb7, M680X_INS_STE, ext_hid, inh_hid },
	{ 0xbb, M680X_INS_ADDE, ext_hid, inh_hid },
	{ 0xbc, M680X_INS_CMPS, ext_hid, inh_hid },
	{ 0xbd, M680X_INS_DIVD, ext_hid, inh_hid },
	{ 0xbe, M680X_INS_DIVQ, ext_hid, inh_hid },
	{ 0xbf, M680X_INS_MULD, ext_hid, inh_hid },
	// 0xCx, immediate instructions with register F
	{ 0xc0, M680X_INS_SUBF, imm8_hid, inh_hid },
	{ 0xc1, M680X_INS_CMPF, imm8_hid, inh_hid },
	{ 0xc6, M680X_INS_LDF, imm8_hid, inh_hid },
	{ 0xcb, M680X_INS_ADDF, imm8_hid, inh_hid },
	// 0xDx, direct instructions with register F
	{ 0xd0, M680X_INS_SUBF, dir_hid, inh_hid },
	{ 0xd1, M680X_INS_CMPF, dir_hid, inh_hid },
	{ 0xd6, M680X_INS_LDF, dir_hid, inh_hid },
	{ 0xd7, M680X_INS_STF, dir_hid, inh_hid },
	{ 0xdb, M680X_INS_ADDF, dir_hid, inh_hid },
	// 0xEx, indexed instructions with register F
	{ 0xe0, M680X_INS_SUBF, idx09_hid, inh_hid },
	{ 0xe1, M680X_INS_CMPF, idx09_hid, inh_hid },
	{ 0xe6, M680X_INS_LDF, idx09_hid, inh_hid },
	{ 0xe7, M680X_INS_STF, idx09_hid, inh_hid },
	{ 0xeb, M680X_INS_ADDF, idx09_hid, inh_hid },
	// 0xFx, extended instructions with register F
	{ 0xf0, M680X_INS_SUBF, ext_hid, inh_hid },
	{ 0xf1, M680X_INS_CMPF, ext_hid, inh_hid },
	{ 0xf6, M680X_INS_LDF, ext_hid, inh_hid },
	{ 0xf7, M680X_INS_STF, ext_hid, inh_hid },
	{ 0xfb, M680X_INS_ADDF, ext_hid, inh_hid },
};

