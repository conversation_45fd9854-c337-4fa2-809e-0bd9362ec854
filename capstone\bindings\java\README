This has been tested with OpenJDK version 6 & 7 on Ubuntu-12.04 and
Arch Linux-3.11, 64-bit.

- OpenJDK is required to compile and run this test code.
  For example, install OpenJDK 6 with:

       $ sudo apt-get install openjdk-6-jre-headless openjdk-6-jdk

- Java Native Access is required to run the code, you can install it with:

       $ sudo apt-get install libjna-java

- To compile and run this Java test code:

       $ make
       $ ./run.sh


This directory contains some test code to show how to use Capstone API.

- TestBasic.java
  This code shows the most simple form of API where we only want to get basic
  information out of disassembled instruction, such as address, mnemonic and
  operand string.

- Test<arch>.java
  These code show how to retrieve architecture-specific information for each
  architecture.
