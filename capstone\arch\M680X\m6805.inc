
// M68HC05 instructions
static const inst_page1 g_m6805_inst_page1_table[256] = {
	// 0x0x, bit manipulation instructions
	{ M680X_INS_BRSET, opidxdr_hid, inh_hid },
	{ M680X_INS_BRCLR, opidxdr_hid, inh_hid },
	{ M680X_INS_BRSET, opidxdr_hid, inh_hid },
	{ M680X_INS_BRCLR, opidxdr_hid, inh_hid },
	{ M680X_INS_BRSET, opidxdr_hid, inh_hid },
	{ M680X_INS_BRCLR, opidxdr_hid, inh_hid },
	{ M680X_INS_BRSET, opidxdr_hid, inh_hid },
	{ M680X_INS_BRCLR, opidxdr_hid, inh_hid },
	{ M680X_INS_BRSET, opidxdr_hid, inh_hid },
	{ M680X_INS_BRCLR, opidxdr_hid, inh_hid },
	{ M680X_INS_BRSET, opidxdr_hid, inh_hid },
	{ M680X_INS_BRCLR, opidxdr_hid, inh_hid },
	{ M680X_INS_BRSET, opidxdr_hid, inh_hid },
	{ M680X_INS_BRCLR, opidxdr_hid, inh_hid },
	{ M680X_INS_BRSET, opidxdr_hid, inh_hid },
	{ M680X_INS_BRCLR, opidxdr_hid, inh_hid },
	// 0x1x, bit set/clear instructions
	{ M680X_INS_BCLR, opidx_hid, dir_hid },
	{ M680X_INS_BSET, opidx_hid, dir_hid },
	{ M680X_INS_BCLR, opidx_hid, dir_hid },
	{ M680X_INS_BSET, opidx_hid, dir_hid },
	{ M680X_INS_BCLR, opidx_hid, dir_hid },
	{ M680X_INS_BSET, opidx_hid, dir_hid },
	{ M680X_INS_BCLR, opidx_hid, dir_hid },
	{ M680X_INS_BSET, opidx_hid, dir_hid },
	{ M680X_INS_BCLR, opidx_hid, dir_hid },
	{ M680X_INS_BSET, opidx_hid, dir_hid },
	{ M680X_INS_BCLR, opidx_hid, dir_hid },
	{ M680X_INS_BSET, opidx_hid, dir_hid },
	{ M680X_INS_BCLR, opidx_hid, dir_hid },
	{ M680X_INS_BSET, opidx_hid, dir_hid },
	{ M680X_INS_BCLR, opidx_hid, dir_hid },
	{ M680X_INS_BSET, opidx_hid, dir_hid },
	// 0x2x, relative branch instructions
	{ M680X_INS_BRA, rel8_hid, inh_hid },
	{ M680X_INS_BRN, rel8_hid, inh_hid },
	{ M680X_INS_BHI, rel8_hid, inh_hid },
	{ M680X_INS_BLS, rel8_hid, inh_hid },
	{ M680X_INS_BCC, rel8_hid, inh_hid },
	{ M680X_INS_BCS, rel8_hid, inh_hid },
	{ M680X_INS_BNE, rel8_hid, inh_hid },
	{ M680X_INS_BEQ, rel8_hid, inh_hid },
	{ M680X_INS_BHCC, rel8_hid, inh_hid },
	{ M680X_INS_BHCS, rel8_hid, inh_hid },
	{ M680X_INS_BPL, rel8_hid, inh_hid },
	{ M680X_INS_BMI, rel8_hid, inh_hid },
	{ M680X_INS_BMC, rel8_hid, inh_hid },
	{ M680X_INS_BMS, rel8_hid, inh_hid },
	{ M680X_INS_BIL, rel8_hid, inh_hid },
	{ M680X_INS_BIH, rel8_hid, inh_hid },
	// 0x3x, direct instructions
	{ M680X_INS_NEG, dir_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_COM, dir_hid, inh_hid },
	{ M680X_INS_LSR, dir_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ROR, dir_hid, inh_hid },
	{ M680X_INS_ASR, dir_hid, inh_hid },
	{ M680X_INS_LSL, dir_hid, inh_hid },
	{ M680X_INS_ROL, dir_hid, inh_hid },
	{ M680X_INS_DEC, dir_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_INC, dir_hid, inh_hid },
	{ M680X_INS_TST, dir_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_CLR, dir_hid, inh_hid },
	// 0x4x, inherent instructions
	{ M680X_INS_NEGA, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_MUL, inh_hid, inh_hid },
	{ M680X_INS_COMA, inh_hid, inh_hid },
	{ M680X_INS_LSRA, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_RORA, inh_hid, inh_hid },
	{ M680X_INS_ASRA, inh_hid, inh_hid },
	{ M680X_INS_LSLA, inh_hid, inh_hid },
	{ M680X_INS_ROLA, inh_hid, inh_hid },
	{ M680X_INS_DECA, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_INCA, inh_hid, inh_hid },
	{ M680X_INS_TSTA, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_CLRA, inh_hid, inh_hid },
	// 0x5x, inherent instructions
	{ M680X_INS_NEGX, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_COMX, inh_hid, inh_hid },
	{ M680X_INS_LSRX, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_RORX, inh_hid, inh_hid },
	{ M680X_INS_ASRX, inh_hid, inh_hid },
	{ M680X_INS_LSLX, inh_hid, inh_hid },
	{ M680X_INS_ROLX, inh_hid, inh_hid },
	{ M680X_INS_DECX, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_INCX, inh_hid, inh_hid },
	{ M680X_INS_TSTX, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_CLRX, inh_hid, inh_hid },
	// 0x6x, indexed, 1 byte offset instructions
	{ M680X_INS_NEG, idxX_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_COM, idxX_hid, inh_hid },
	{ M680X_INS_LSR, idxX_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ROR, idxX_hid, inh_hid },
	{ M680X_INS_ASR, idxX_hid, inh_hid },
	{ M680X_INS_LSL, idxX_hid, inh_hid },
	{ M680X_INS_ROL, idxX_hid, inh_hid },
	{ M680X_INS_DEC, idxX_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_INC, idxX_hid, inh_hid },
	{ M680X_INS_TST, idxX_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_CLR, idxX_hid, inh_hid },
	// 0x7x, indexed, no offset instructions
	{ M680X_INS_NEG, idxX0_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_COM, idxX0_hid, inh_hid },
	{ M680X_INS_LSR, idxX0_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ROR, idxX0_hid, inh_hid },
	{ M680X_INS_ASR, idxX0_hid, inh_hid },
	{ M680X_INS_LSL, idxX0_hid, inh_hid },
	{ M680X_INS_ROL, idxX0_hid, inh_hid },
	{ M680X_INS_DEC, idxX0_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_INC, idxX0_hid, inh_hid },
	{ M680X_INS_TST, idxX0_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_CLR, idxX0_hid, inh_hid },
	// 0x8x, inherent instructions
	{ M680X_INS_RTI, inh_hid, inh_hid },
	{ M680X_INS_RTS, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_SWI, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_STOP, inh_hid, inh_hid },
	{ M680X_INS_WAIT, inh_hid, inh_hid },
	// 0x9x, inherent instructions
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_TAX, inh_hid, inh_hid },
	{ M680X_INS_CLC, inh_hid, inh_hid },
	{ M680X_INS_SEC, inh_hid, inh_hid },
	{ M680X_INS_CLI, inh_hid, inh_hid },
	{ M680X_INS_SEI, inh_hid, inh_hid },
	{ M680X_INS_RSP, inh_hid, inh_hid },
	{ M680X_INS_NOP, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_TXA, inh_hid, inh_hid },
	// 0xAx, immediate instructions with reg. A
	{ M680X_INS_SUB, imm8_hid, inh_hid },
	{ M680X_INS_CMP, imm8_hid, inh_hid },
	{ M680X_INS_SBC, imm8_hid, inh_hid },
	{ M680X_INS_CPX, imm8_hid, inh_hid },
	{ M680X_INS_AND, imm8_hid, inh_hid },
	{ M680X_INS_BIT, imm8_hid, inh_hid },
	{ M680X_INS_LDA, imm8_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_EOR, imm8_hid, inh_hid },
	{ M680X_INS_ADC, imm8_hid, inh_hid },
	{ M680X_INS_ORA, imm8_hid, inh_hid },
	{ M680X_INS_ADD, imm8_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_BSR, rel8_hid, inh_hid },
	{ M680X_INS_LDX, imm8_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	// 0xBx, direct instructions with reg. A
	{ M680X_INS_SUB, dir_hid, inh_hid },
	{ M680X_INS_CMP, dir_hid, inh_hid },
	{ M680X_INS_SBC, dir_hid, inh_hid },
	{ M680X_INS_CPX, dir_hid, inh_hid },
	{ M680X_INS_AND, dir_hid, inh_hid },
	{ M680X_INS_BIT, dir_hid, inh_hid },
	{ M680X_INS_LDA, dir_hid, inh_hid },
	{ M680X_INS_STA, dir_hid, inh_hid },
	{ M680X_INS_EOR, dir_hid, inh_hid },
	{ M680X_INS_ADC, dir_hid, inh_hid },
	{ M680X_INS_ORA, dir_hid, inh_hid },
	{ M680X_INS_ADD, dir_hid, inh_hid },
	{ M680X_INS_JMP, dir_hid, inh_hid },
	{ M680X_INS_JSR, dir_hid, inh_hid },
	{ M680X_INS_LDX, dir_hid, inh_hid },
	{ M680X_INS_STX, dir_hid, inh_hid },
	// 0xCx, extended instructions with reg. A
	{ M680X_INS_SUB, ext_hid, inh_hid },
	{ M680X_INS_CMP, ext_hid, inh_hid },
	{ M680X_INS_SBC, ext_hid, inh_hid },
	{ M680X_INS_CPX, ext_hid, inh_hid },
	{ M680X_INS_AND, ext_hid, inh_hid },
	{ M680X_INS_BIT, ext_hid, inh_hid },
	{ M680X_INS_LDA, ext_hid, inh_hid },
	{ M680X_INS_STA, ext_hid, inh_hid },
	{ M680X_INS_EOR, ext_hid, inh_hid },
	{ M680X_INS_ADC, ext_hid, inh_hid },
	{ M680X_INS_ORA, ext_hid, inh_hid },
	{ M680X_INS_ADD, ext_hid, inh_hid },
	{ M680X_INS_JMP, ext_hid, inh_hid },
	{ M680X_INS_JSR, ext_hid, inh_hid },
	{ M680X_INS_LDX, ext_hid, inh_hid },
	{ M680X_INS_STX, ext_hid, inh_hid },
	// 0xDx, indexed with 2 byte offset instructions with reg. A
	{ M680X_INS_SUB, idxX16_hid, inh_hid },
	{ M680X_INS_CMP, idxX16_hid, inh_hid },
	{ M680X_INS_SBC, idxX16_hid, inh_hid },
	{ M680X_INS_CPX, idxX16_hid, inh_hid },
	{ M680X_INS_AND, idxX16_hid, inh_hid },
	{ M680X_INS_BIT, idxX16_hid, inh_hid },
	{ M680X_INS_LDA, idxX16_hid, inh_hid },
	{ M680X_INS_STA, idxX16_hid, inh_hid },
	{ M680X_INS_EOR, idxX16_hid, inh_hid },
	{ M680X_INS_ADC, idxX16_hid, inh_hid },
	{ M680X_INS_ORA, idxX16_hid, inh_hid },
	{ M680X_INS_ADD, idxX16_hid, inh_hid },
	{ M680X_INS_JMP, idxX16_hid, inh_hid },
	{ M680X_INS_JSR, idxX16_hid, inh_hid },
	{ M680X_INS_LDX, idxX16_hid, inh_hid },
	{ M680X_INS_STX, idxX16_hid, inh_hid },
	// 0xEx, indexed with 1 byte offset instructions with reg. A
	{ M680X_INS_SUB, idxX_hid, inh_hid },
	{ M680X_INS_CMP, idxX_hid, inh_hid },
	{ M680X_INS_SBC, idxX_hid, inh_hid },
	{ M680X_INS_CPX, idxX_hid, inh_hid },
	{ M680X_INS_AND, idxX_hid, inh_hid },
	{ M680X_INS_BIT, idxX_hid, inh_hid },
	{ M680X_INS_LDA, idxX_hid, inh_hid },
	{ M680X_INS_STA, idxX_hid, inh_hid },
	{ M680X_INS_EOR, idxX_hid, inh_hid },
	{ M680X_INS_ADC, idxX_hid, inh_hid },
	{ M680X_INS_ORA, idxX_hid, inh_hid },
	{ M680X_INS_ADD, idxX_hid, inh_hid },
	{ M680X_INS_JMP, idxX_hid, inh_hid },
	{ M680X_INS_JSR, idxX_hid, inh_hid },
	{ M680X_INS_LDX, idxX_hid, inh_hid },
	{ M680X_INS_STX, idxX_hid, inh_hid },
	// 0xFx, indexed without offset instructions with reg. A
	{ M680X_INS_SUB, idxX0_hid, inh_hid },
	{ M680X_INS_CMP, idxX0_hid, inh_hid },
	{ M680X_INS_SBC, idxX0_hid, inh_hid },
	{ M680X_INS_CPX, idxX0_hid, inh_hid },
	{ M680X_INS_AND, idxX0_hid, inh_hid },
	{ M680X_INS_BIT, idxX0_hid, inh_hid },
	{ M680X_INS_LDA, idxX0_hid, inh_hid },
	{ M680X_INS_STA, idxX0_hid, inh_hid },
	{ M680X_INS_EOR, idxX0_hid, inh_hid },
	{ M680X_INS_ADC, idxX0_hid, inh_hid },
	{ M680X_INS_ORA, idxX0_hid, inh_hid },
	{ M680X_INS_ADD, idxX0_hid, inh_hid },
	{ M680X_INS_JMP, idxX0_hid, inh_hid },
	{ M680X_INS_JSR, idxX0_hid, inh_hid },
	{ M680X_INS_LDX, idxX0_hid, inh_hid },
	{ M680X_INS_STX, idxX0_hid, inh_hid },
};

