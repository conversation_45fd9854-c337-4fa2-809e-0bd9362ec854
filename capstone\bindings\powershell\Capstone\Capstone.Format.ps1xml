<?xml version="1.0" encoding="utf-8" ?>
<Configuration>
  <DefaultSettings>
    <EnumerableExpansions>
        <EnumerableExpansion>
            <Expand>Both</Expand>
        </EnumerableExpansion>
    </EnumerableExpansions>
  </DefaultSettings>
    <ViewDefinitions>
        <View>
            <Name>CapstoneDisassemblyViewSimple</Name>
                <ViewSelectedBy>
                    <TypeName>CapstoneDisassembly.Simple</TypeName>
                </ViewSelectedBy>
                <ListControl>
                <ListEntries>
                    <ListEntry>
                        <ListItems>
                            <ListItem>
                                <PropertyName>Address</PropertyName>
                                <FormatString>0x{0:X}</FormatString>
                            </ListItem>
                            <ListItem>
                                <PropertyName>Instruction</PropertyName>
                            </ListItem>
                        </ListItems>
                    </ListEntry>
                </ListEntries>
            </ListControl>
        </View>
        <View>
            <Name>CapstoneDisassemblyViewSimple</Name>
                <ViewSelectedBy>
                    <TypeName>CapstoneDisassembly.Simple</TypeName>
                </ViewSelectedBy>
            <TableControl>
                <TableHeaders>
                    <TableColumnHeader>
                        <Label>Address</Label>
                    </TableColumnHeader>
                    <TableColumnHeader>
                        <Label>Instruction</Label>
                    </TableColumnHeader>
                </TableHeaders>
                <TableRowEntries>
                    <TableRowEntry>
                        <TableColumnItems>
                            <TableColumnItem>
                                <PropertyName>Address</PropertyName>
                                <FormatString>0x{0:x}</FormatString>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Instruction</PropertyName>
                            </TableColumnItem>
                        </TableColumnItems>
                    </TableRowEntry>
                 </TableRowEntries>
            </TableControl>
        </View>
        <View>
            <Name>CapstoneDisassemblyViewDetailed</Name>
                <ViewSelectedBy>
                    <TypeName>CapstoneDisassembly.Detailed</TypeName>
                </ViewSelectedBy>
                <ListControl>
                <ListEntries>
                    <ListEntry>
                        <ListItems>
                            <ListItem>
                                <PropertyName>Address</PropertyName>
                                <FormatString>0x{0:X}</FormatString>
                            </ListItem>
                            <ListItem>
                                <PropertyName>Mnemonic</PropertyName>
                            </ListItem>
                            <ListItem>
                                <PropertyName>Operands</PropertyName>
                            </ListItem>
                            <ListItem>
                                <PropertyName>Bytes</PropertyName>
                            </ListItem>
                            <ListItem>
                                <PropertyName>Size</PropertyName>
                            </ListItem>
                            <ListItem>
                                <PropertyName>RegRead</PropertyName>
                            </ListItem>
                            <ListItem>
                                <PropertyName>RegWrite</PropertyName>
                            </ListItem>
                        </ListItems>
                    </ListEntry>
                </ListEntries>
            </ListControl>
        </View>
        <View>
            <Name>CapstoneDisassemblyViewDetailed</Name>
                <ViewSelectedBy>
                    <TypeName>CapstoneDisassembly.Detailed</TypeName>
                </ViewSelectedBy>
            <TableControl>
                <TableHeaders>
                    <TableColumnHeader>
                        <Label>Address</Label>
                    </TableColumnHeader>
                    <TableColumnHeader>
                        <Label>Mnemonic</Label>
                    </TableColumnHeader>
                    <TableColumnHeader>
                        <Label>Operands</Label>
                    </TableColumnHeader>
                    <TableColumnHeader>
                        <Label>Bytes</Label>
                    </TableColumnHeader>
                    <TableColumnHeader>
                        <Label>Size</Label>
                    </TableColumnHeader>
                    <TableColumnHeader>
                        <Label>RegRead</Label>
                    </TableColumnHeader>
                    <TableColumnHeader>
                        <Label>RegWrite</Label>
                    </TableColumnHeader>
                </TableHeaders>
                <TableRowEntries>
                    <TableRowEntry>
                        <TableColumnItems>
                            <TableColumnItem>
                                <PropertyName>Address</PropertyName>
                                <FormatString>0x{0:x}</FormatString>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Mnemonic</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Operands</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Bytes</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Size</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>RegRead</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>RegWrite</PropertyName>
                            </TableColumnItem>
                        </TableColumnItems>
                    </TableRowEntry>
                 </TableRowEntries>
            </TableControl>
        </View>
    </ViewDefinitions>
</Configuration>