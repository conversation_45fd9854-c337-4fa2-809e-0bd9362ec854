// This is auto-gen data for Capstone engine (www.capstone-engine.org)
// By <PERSON><PERSON><PERSON> <<EMAIL>>

{
	XCore_ADD_2rus, XCORE_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_ADD_3r, XCORE_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_ANDNOT_2r, XCORE_INS_ANDNOT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_AND_3r, XCORE_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_ASHR_l2rus, XCORE_INS_ASHR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_ASHR_l3r, <PERSON>CORE_INS_ASHR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_BAU_1r, XCORE_INS_BAU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 1
#endif
},
{
	XCore_BITREV_l2r, XCORE_INS_BITREV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_BLACP_lu10, XCORE_INS_BLA,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { XCORE_REG_R0, XCORE_REG_R1, XCORE_REG_R2, XCORE_REG_R3, XCORE_REG_R11, XCORE_REG_LR, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_BLACP_u10, XCORE_INS_BLA,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { XCORE_REG_R0, XCORE_REG_R1, XCORE_REG_R2, XCORE_REG_R3, XCORE_REG_R11, XCORE_REG_LR, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_BLAT_lu6, XCORE_INS_BLAT,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_R11, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_BLAT_u6, XCORE_INS_BLAT,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_R11, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_BLA_1r, XCORE_INS_BLA,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { XCORE_REG_R0, XCORE_REG_R1, XCORE_REG_R2, XCORE_REG_R3, XCORE_REG_R11, XCORE_REG_LR, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_BLRB_lu10, XCORE_INS_BL,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { XCORE_REG_R0, XCORE_REG_R1, XCORE_REG_R2, XCORE_REG_R3, XCORE_REG_R11, XCORE_REG_LR, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_BLRB_u10, XCORE_INS_BL,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { XCORE_REG_R0, XCORE_REG_R1, XCORE_REG_R2, XCORE_REG_R3, XCORE_REG_R11, XCORE_REG_LR, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_BLRF_lu10, XCORE_INS_BL,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { XCORE_REG_R0, XCORE_REG_R1, XCORE_REG_R2, XCORE_REG_R3, XCORE_REG_R11, XCORE_REG_LR, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_BLRF_u10, XCORE_INS_BL,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { XCORE_REG_R0, XCORE_REG_R1, XCORE_REG_R2, XCORE_REG_R3, XCORE_REG_R11, XCORE_REG_LR, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_BRBF_lru6, XCORE_INS_BF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	XCore_BRBF_ru6, XCORE_INS_BF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	XCore_BRBT_lru6, XCORE_INS_BT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	XCore_BRBT_ru6, XCORE_INS_BT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	XCore_BRBU_lu6, XCORE_INS_BU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	XCore_BRBU_u6, XCORE_INS_BU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	XCore_BRFF_lru6, XCORE_INS_BF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	XCore_BRFF_ru6, XCORE_INS_BF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	XCore_BRFT_lru6, XCORE_INS_BT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	XCore_BRFT_ru6, XCORE_INS_BT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	XCore_BRFU_lu6, XCORE_INS_BU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	XCore_BRFU_u6, XCORE_INS_BU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	XCore_BRU_1r, XCORE_INS_BRU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 1
#endif
},
{
	XCore_BYTEREV_l2r, XCORE_INS_BYTEREV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_CHKCT_2r, XCORE_INS_CHKCT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_CHKCT_rus, XCORE_INS_CHKCT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_CLRE_0R, XCORE_INS_CLRE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_CLRPT_1R, XCORE_INS_CLRPT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_CLRSR_branch_lu6, XCORE_INS_CLRSR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 1
#endif
},
{
	XCore_CLRSR_branch_u6, XCORE_INS_CLRSR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 1
#endif
},
{
	XCore_CLRSR_lu6, XCORE_INS_CLRSR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_CLRSR_u6, XCORE_INS_CLRSR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_CLZ_l2r, XCORE_INS_CLZ,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_CRC8_l4r, XCORE_INS_CRC8,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_CRC_l3r, XCORE_INS_CRC32,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_DCALL_0R, XCORE_INS_DCALL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_DENTSP_0R, XCORE_INS_DENTSP,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { XCORE_REG_SP, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_DGETREG_1r, XCORE_INS_DGETREG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_DIVS_l3r, XCORE_INS_DIVS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_DIVU_l3r, XCORE_INS_DIVU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_DRESTSP_0R, XCORE_INS_DRESTSP,
#ifndef CAPSTONE_DIET
	{ 0 }, { XCORE_REG_SP, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_DRET_0R, XCORE_INS_DRET,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_ECALLF_1r, XCORE_INS_ECALLF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_ECALLT_1r, XCORE_INS_ECALLT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_EDU_1r, XCORE_INS_EDU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_EEF_2r, XCORE_INS_EEF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_EET_2r, XCORE_INS_EET,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_EEU_1r, XCORE_INS_EEU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_ENDIN_2r, XCORE_INS_ENDIN,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_ENTSP_lu6, XCORE_INS_ENTSP,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { XCORE_REG_SP, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_ENTSP_u6, XCORE_INS_ENTSP,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { XCORE_REG_SP, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_EQ_2rus, XCORE_INS_EQ,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_EQ_3r, XCORE_INS_EQ,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_EXTDP_lu6, XCORE_INS_EXTDP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_EXTDP_u6, XCORE_INS_EXTDP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_EXTSP_lu6, XCORE_INS_EXTSP,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { XCORE_REG_SP, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_EXTSP_u6, XCORE_INS_EXTSP,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { XCORE_REG_SP, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_FREER_1r, XCORE_INS_FREER,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_FREET_0R, XCORE_INS_FREET,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_GETD_l2r, XCORE_INS_GETD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_GETED_0R, XCORE_INS_GET,
#ifndef CAPSTONE_DIET
	{ 0 }, { XCORE_REG_R11, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_GETET_0R, XCORE_INS_GET,
#ifndef CAPSTONE_DIET
	{ 0 }, { XCORE_REG_R11, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_GETID_0R, XCORE_INS_GET,
#ifndef CAPSTONE_DIET
	{ 0 }, { XCORE_REG_R11, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_GETKEP_0R, XCORE_INS_GET,
#ifndef CAPSTONE_DIET
	{ 0 }, { XCORE_REG_R11, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_GETKSP_0R, XCORE_INS_GET,
#ifndef CAPSTONE_DIET
	{ 0 }, { XCORE_REG_R11, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_GETN_l2r, XCORE_INS_GETN,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_GETPS_l2r, XCORE_INS_GET,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_GETR_rus, XCORE_INS_GETR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_GETSR_lu6, XCORE_INS_GETSR,
#ifndef CAPSTONE_DIET
	{ 0 }, { XCORE_REG_R11, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_GETSR_u6, XCORE_INS_GETSR,
#ifndef CAPSTONE_DIET
	{ 0 }, { XCORE_REG_R11, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_GETST_2r, XCORE_INS_GETST,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_GETTS_2r, XCORE_INS_GETTS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_INCT_2r, XCORE_INS_INCT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_INITCP_2r, XCORE_INS_INIT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_INITDP_2r, XCORE_INS_INIT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_INITLR_l2r, XCORE_INS_INIT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_INITPC_2r, XCORE_INS_INIT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_INITSP_2r, XCORE_INS_INIT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_INPW_l2rus, XCORE_INS_INPW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_INSHR_2r, XCORE_INS_INSHR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_INT_2r, XCORE_INS_INT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_IN_2r, XCORE_INS_IN,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_KCALL_1r, XCORE_INS_KCALL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_KCALL_lu6, XCORE_INS_KCALL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_KCALL_u6, XCORE_INS_KCALL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_KENTSP_lu6, XCORE_INS_KENTSP,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { XCORE_REG_SP, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_KENTSP_u6, XCORE_INS_KENTSP,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { XCORE_REG_SP, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_KRESTSP_lu6, XCORE_INS_KRESTSP,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { XCORE_REG_SP, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_KRESTSP_u6, XCORE_INS_KRESTSP,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { XCORE_REG_SP, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_KRET_0R, XCORE_INS_KRET,
#ifndef CAPSTONE_DIET
	{ 0 }, { XCORE_REG_SP, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LADD_l5r, XCORE_INS_LADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LD16S_3r, XCORE_INS_LD16S,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LD8U_3r, XCORE_INS_LD8U,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDA16B_l3r, XCORE_INS_LDA16,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDA16F_l3r, XCORE_INS_LDA16,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDAPB_lu10, XCORE_INS_LDAP,
#ifndef CAPSTONE_DIET
	{ 0 }, { XCORE_REG_R11, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDAPB_u10, XCORE_INS_LDAP,
#ifndef CAPSTONE_DIET
	{ 0 }, { XCORE_REG_R11, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDAPF_lu10, XCORE_INS_LDAP,
#ifndef CAPSTONE_DIET
	{ 0 }, { XCORE_REG_R11, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDAPF_lu10_ba, XCORE_INS_LDAP,
#ifndef CAPSTONE_DIET
	{ 0 }, { XCORE_REG_R11, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDAPF_u10, XCORE_INS_LDAP,
#ifndef CAPSTONE_DIET
	{ 0 }, { XCORE_REG_R11, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDAWB_l2rus, XCORE_INS_LDAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDAWB_l3r, XCORE_INS_LDAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDAWCP_lu6, XCORE_INS_LDAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { XCORE_REG_R11, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDAWCP_u6, XCORE_INS_LDAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { XCORE_REG_R11, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDAWDP_lru6, XCORE_INS_LDAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDAWDP_ru6, XCORE_INS_LDAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDAWF_l2rus, XCORE_INS_LDAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDAWF_l3r, XCORE_INS_LDAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDAWSP_lru6, XCORE_INS_LDAW,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDAWSP_ru6, XCORE_INS_LDAW,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDC_lru6, XCORE_INS_LDC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDC_ru6, XCORE_INS_LDC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDET_0R, XCORE_INS_LDW,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDIVU_l5r, XCORE_INS_LDIVU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDSED_0R, XCORE_INS_LDW,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDSPC_0R, XCORE_INS_LDW,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDSSR_0R, XCORE_INS_LDW,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDWCP_lru6, XCORE_INS_LDW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDWCP_lu10, XCORE_INS_LDW,
#ifndef CAPSTONE_DIET
	{ 0 }, { XCORE_REG_R11, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDWCP_ru6, XCORE_INS_LDW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDWCP_u10, XCORE_INS_LDW,
#ifndef CAPSTONE_DIET
	{ 0 }, { XCORE_REG_R11, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDWDP_lru6, XCORE_INS_LDW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDWDP_ru6, XCORE_INS_LDW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDWSP_lru6, XCORE_INS_LDW,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDWSP_ru6, XCORE_INS_LDW,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDW_2rus, XCORE_INS_LDW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LDW_3r, XCORE_INS_LDW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LMUL_l6r, XCORE_INS_LMUL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LSS_3r, XCORE_INS_LSS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LSUB_l5r, XCORE_INS_LSUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_LSU_3r, XCORE_INS_LSU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_MACCS_l4r, XCORE_INS_MACCS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_MACCU_l4r, XCORE_INS_MACCU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_MJOIN_1r, XCORE_INS_MJOIN,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_MKMSK_2r, XCORE_INS_MKMSK,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_MKMSK_rus, XCORE_INS_MKMSK,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_MSYNC_1r, XCORE_INS_MSYNC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_MUL_l3r, XCORE_INS_MUL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_NEG, XCORE_INS_NEG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_NOT, XCORE_INS_NOT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_OR_3r, XCORE_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_OUTCT_2r, XCORE_INS_OUTCT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_OUTCT_rus, XCORE_INS_OUTCT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_OUTPW_l2rus, XCORE_INS_OUTPW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_OUTSHR_2r, XCORE_INS_OUTSHR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_OUTT_2r, XCORE_INS_OUTT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_OUT_2r, XCORE_INS_OUT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_PEEK_2r, XCORE_INS_PEEK,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_REMS_l3r, XCORE_INS_REMS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_REMU_l3r, XCORE_INS_REMU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_RETSP_lu6, XCORE_INS_RETSP,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { XCORE_REG_SP, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_RETSP_u6, XCORE_INS_RETSP,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { XCORE_REG_SP, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SETCLK_l2r, XCORE_INS_SETCLK,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SETCP_1r, XCORE_INS_SET,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SETC_l2r, XCORE_INS_SETC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SETC_lru6, XCORE_INS_SETC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SETC_ru6, XCORE_INS_SETC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SETDP_1r, XCORE_INS_SET,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SETD_2r, XCORE_INS_SETD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SETEV_1r, XCORE_INS_SETEV,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_R11, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SETKEP_0R, XCORE_INS_SET,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_R11, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SETN_l2r, XCORE_INS_SETN,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SETPSC_2r, XCORE_INS_SETPSC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SETPS_l2r, XCORE_INS_SET,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SETPT_2r, XCORE_INS_SETPT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SETRDY_l2r, XCORE_INS_SETRDY,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SETSP_1r, XCORE_INS_SET,
#ifndef CAPSTONE_DIET
	{ 0 }, { XCORE_REG_SP, 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SETSR_branch_lu6, XCORE_INS_SETSR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 1
#endif
},
{
	XCore_SETSR_branch_u6, XCORE_INS_SETSR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 1
#endif
},
{
	XCore_SETSR_lu6, XCORE_INS_SETSR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SETSR_u6, XCORE_INS_SETSR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SETTW_l2r, XCORE_INS_SETTW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SETV_1r, XCORE_INS_SETV,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_R11, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SEXT_2r, XCORE_INS_SEXT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SEXT_rus, XCORE_INS_SEXT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SHL_2rus, XCORE_INS_SHL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SHL_3r, XCORE_INS_SHL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SHR_2rus, XCORE_INS_SHR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SHR_3r, XCORE_INS_SHR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SSYNC_0r, XCORE_INS_SSYNC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_ST16_l3r, XCORE_INS_ST16,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_ST8_l3r, XCORE_INS_ST8,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_STET_0R, XCORE_INS_STW,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_STSED_0R, XCORE_INS_STW,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_STSPC_0R, XCORE_INS_STW,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_STSSR_0R, XCORE_INS_STW,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_STWDP_lru6, XCORE_INS_STW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_STWDP_ru6, XCORE_INS_STW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_STWSP_lru6, XCORE_INS_STW,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_STWSP_ru6, XCORE_INS_STW,
#ifndef CAPSTONE_DIET
	{ XCORE_REG_SP, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_STW_2rus, XCORE_INS_STW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_STW_l3r, XCORE_INS_STW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SUB_2rus, XCORE_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SUB_3r, XCORE_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_SYNCR_1r, XCORE_INS_SYNCR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_TESTCT_2r, XCORE_INS_TESTCT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_TESTLCL_l2r, XCORE_INS_TESTLCL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_TESTWCT_2r, XCORE_INS_TESTWCT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_TSETMR_2r, XCORE_INS_TSETMR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_TSETR_3r, XCORE_INS_SET,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_TSTART_1R, XCORE_INS_START,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_WAITEF_1R, XCORE_INS_WAITEF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_WAITET_1R, XCORE_INS_WAITET,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_WAITEU_0R, XCORE_INS_WAITEU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 1
#endif
},
{
	XCore_XOR_l3r, XCORE_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_ZEXT_2r, XCORE_INS_ZEXT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	XCore_ZEXT_rus, XCORE_INS_ZEXT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
