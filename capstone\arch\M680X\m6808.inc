
// Additional instructions only supported on M68HC08
static const inst_pageX g_m6808_inst_overlay_table[] = {
	{ 0x31, M680X_INS_CBEQ, dir_hid, rel8_hid },
	{ 0x35, M680X_INS_STHX, dir_hid, inh_hid },
	{ 0x3b, M680X_INS_DBNZ, dir_hid, rel8_hid },
	{ 0x41, M680X_INS_CBEQA, imm8rel_hid, inh_hid },
	{ 0x45, M680X_INS_LDHX, imm16_hid, inh_hid },
	{ 0x4b, M680X_INS_DBNZA, rel8_hid, inh_hid },
	{ 0x4e, M680X_INS_MOV, dir_hid, dir_hid },
	{ 0x51, M680X_INS_CBEQX, imm8rel_hid, inh_hid },
	{ 0x52, M680X_INS_DIV, inh_hid, inh_hid },
	{ 0x55, M680X_INS_LDHX, dir_hid, inh_hid },
	{ 0x5b, M680X_INS_DBNZX, rel8_hid, inh_hid },
	{ 0x5e, M680X_INS_MOV, dir_hid, idxX0p_hid },
	{ 0x61, M680X_INS_CBEQ, idxXp_hid, rel8_hid },
	{ 0x62, M680X_INS_NSA, inh_hid, inh_hid },
	{ 0x65, M680X_INS_CPHX, imm16_hid, inh_hid },
	{ 0x6b, M680X_INS_DBNZ, idxX_hid, rel8_hid },
	{ 0x6e, M680X_INS_MOV, imm8_hid, dir_hid },
	{ 0x71, M680X_INS_CBEQ, idxX0p_hid, rel8_hid },
	{ 0x72, M680X_INS_DAA, inh_hid, inh_hid },
	{ 0x75, M680X_INS_CPHX, dir_hid, inh_hid },
	{ 0x7b, M680X_INS_DBNZ, idxX0_hid, rel8_hid },
	{ 0x7e, M680X_INS_MOV, idxX0p_hid, dir_hid },
	{ 0x84, M680X_INS_TAP, inh_hid, inh_hid },
	{ 0x85, M680X_INS_TPA, inh_hid, inh_hid },
	{ 0x86, M680X_INS_PULA, inh_hid, inh_hid },
	{ 0x87, M680X_INS_PSHA, inh_hid, inh_hid },
	{ 0x88, M680X_INS_PULX, inh_hid, inh_hid },
	{ 0x89, M680X_INS_PSHX, inh_hid, inh_hid },
	{ 0x8a, M680X_INS_PULH, inh_hid, inh_hid },
	{ 0x8b, M680X_INS_PSHH, inh_hid, inh_hid },
	{ 0x8c, M680X_INS_CLRH, inh_hid, inh_hid },
	{ 0x90, M680X_INS_BGE, rel8_hid, inh_hid },
	{ 0x91, M680X_INS_BLT, rel8_hid, inh_hid },
	{ 0x92, M680X_INS_BGT, rel8_hid, inh_hid },
	{ 0x93, M680X_INS_BLE, rel8_hid, inh_hid },
	{ 0x94, M680X_INS_TXS, inh_hid, inh_hid },
	{ 0x95, M680X_INS_TSX, inh_hid, inh_hid },
	{ 0x97, M680X_INS_TAX, inh_hid, inh_hid },
	{ 0x9f, M680X_INS_TXA, inh_hid, inh_hid },
	{ 0xa7, M680X_INS_AIS, imm8_hid, inh_hid },
	{ 0xaf, M680X_INS_AIX, imm8_hid, inh_hid },
};

// M68HC08 PAGE2 instructions (prefix 0x9E)
static const inst_pageX g_m6808_inst_page2_table[] = {
	{ 0x60, M680X_INS_NEG, idxS_hid, inh_hid },
	{ 0x61, M680X_INS_CBEQ, idxS_hid, rel8_hid },
	{ 0x63, M680X_INS_COM, idxS_hid, inh_hid },
	{ 0x64, M680X_INS_LSR, idxS_hid, inh_hid },
	{ 0x66, M680X_INS_ROR, idxS_hid, inh_hid },
	{ 0x67, M680X_INS_ASR, idxS_hid, inh_hid },
	{ 0x68, M680X_INS_LSL, idxS_hid, inh_hid },
	{ 0x69, M680X_INS_ROL, idxS_hid, inh_hid },
	{ 0x6a, M680X_INS_DEC, idxS_hid, inh_hid },
	{ 0x6b, M680X_INS_DBNZ, idxS_hid, rel8_hid },
	{ 0x6c, M680X_INS_INC, idxS_hid, inh_hid },
	{ 0x6d, M680X_INS_TST, idxS_hid, inh_hid },
	{ 0x6f, M680X_INS_CLR, idxS_hid, inh_hid },
	{ 0xd0, M680X_INS_SUB, idxS16_hid, inh_hid },
	{ 0xd1, M680X_INS_CMP, idxS16_hid, inh_hid },
	{ 0xd2, M680X_INS_SBC, idxS16_hid, inh_hid },
	{ 0xd3, M680X_INS_CPX, idxS16_hid, inh_hid },
	{ 0xd4, M680X_INS_AND, idxS16_hid, inh_hid },
	{ 0xd5, M680X_INS_BIT, idxS16_hid, inh_hid },
	{ 0xd6, M680X_INS_LDA, idxS16_hid, inh_hid },
	{ 0xd7, M680X_INS_STA, idxS16_hid, inh_hid },
	{ 0xd8, M680X_INS_EOR, idxS16_hid, inh_hid },
	{ 0xd9, M680X_INS_ADC, idxS16_hid, inh_hid },
	{ 0xda, M680X_INS_ORA, idxS16_hid, inh_hid },
	{ 0xdb, M680X_INS_ADD, idxS16_hid, inh_hid },
	{ 0xde, M680X_INS_LDX, idxS16_hid, inh_hid },
	{ 0xdf, M680X_INS_STX, idxS16_hid, inh_hid },
	{ 0xe0, M680X_INS_SUB, idxS_hid, inh_hid },
	{ 0xe1, M680X_INS_CMP, idxS_hid, inh_hid },
	{ 0xe2, M680X_INS_SBC, idxS_hid, inh_hid },
	{ 0xe3, M680X_INS_CPX, idxS_hid, inh_hid },
	{ 0xe4, M680X_INS_AND, idxS_hid, inh_hid },
	{ 0xe5, M680X_INS_BIT, idxS_hid, inh_hid },
	{ 0xe6, M680X_INS_LDA, idxS_hid, inh_hid },
	{ 0xe7, M680X_INS_STA, idxS_hid, inh_hid },
	{ 0xe8, M680X_INS_EOR, idxS_hid, inh_hid },
	{ 0xe9, M680X_INS_ADC, idxS_hid, inh_hid },
	{ 0xea, M680X_INS_ORA, idxS_hid, inh_hid },
	{ 0xeb, M680X_INS_ADD, idxS_hid, inh_hid },
	{ 0xee, M680X_INS_LDX, idxS_hid, inh_hid },
	{ 0xef, M680X_INS_STX, idxS_hid, inh_hid },
};

