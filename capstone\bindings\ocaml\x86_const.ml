(* For Capstone Engine. AUTO-GENERATED FILE, DO NOT EDIT [x86_const.ml] *)

(* X86 registers *)

let _X86_REG_INVALID = 0;;
let _X86_REG_AH = 1;;
let _X86_REG_AL = 2;;
let _X86_REG_AX = 3;;
let _X86_REG_BH = 4;;
let _X86_REG_BL = 5;;
let _X86_REG_BP = 6;;
let _X86_REG_BPL = 7;;
let _X86_REG_BX = 8;;
let _X86_REG_CH = 9;;
let _X86_REG_CL = 10;;
let _X86_REG_CS = 11;;
let _X86_REG_CX = 12;;
let _X86_REG_DH = 13;;
let _X86_REG_DI = 14;;
let _X86_REG_DIL = 15;;
let _X86_REG_DL = 16;;
let _X86_REG_DS = 17;;
let _X86_REG_DX = 18;;
let _X86_REG_EAX = 19;;
let _X86_REG_EBP = 20;;
let _X86_REG_EBX = 21;;
let _X86_REG_ECX = 22;;
let _X86_REG_EDI = 23;;
let _X86_REG_EDX = 24;;
let _X86_REG_EFLAGS = 25;;
let _X86_REG_EIP = 26;;
let _X86_REG_EIZ = 27;;
let _X86_REG_ES = 28;;
let _X86_REG_ESI = 29;;
let _X86_REG_ESP = 30;;
let _X86_REG_FPSW = 31;;
let _X86_REG_FS = 32;;
let _X86_REG_GS = 33;;
let _X86_REG_IP = 34;;
let _X86_REG_RAX = 35;;
let _X86_REG_RBP = 36;;
let _X86_REG_RBX = 37;;
let _X86_REG_RCX = 38;;
let _X86_REG_RDI = 39;;
let _X86_REG_RDX = 40;;
let _X86_REG_RIP = 41;;
let _X86_REG_RIZ = 42;;
let _X86_REG_RSI = 43;;
let _X86_REG_RSP = 44;;
let _X86_REG_SI = 45;;
let _X86_REG_SIL = 46;;
let _X86_REG_SP = 47;;
let _X86_REG_SPL = 48;;
let _X86_REG_SS = 49;;
let _X86_REG_CR0 = 50;;
let _X86_REG_CR1 = 51;;
let _X86_REG_CR2 = 52;;
let _X86_REG_CR3 = 53;;
let _X86_REG_CR4 = 54;;
let _X86_REG_CR5 = 55;;
let _X86_REG_CR6 = 56;;
let _X86_REG_CR7 = 57;;
let _X86_REG_CR8 = 58;;
let _X86_REG_CR9 = 59;;
let _X86_REG_CR10 = 60;;
let _X86_REG_CR11 = 61;;
let _X86_REG_CR12 = 62;;
let _X86_REG_CR13 = 63;;
let _X86_REG_CR14 = 64;;
let _X86_REG_CR15 = 65;;
let _X86_REG_DR0 = 66;;
let _X86_REG_DR1 = 67;;
let _X86_REG_DR2 = 68;;
let _X86_REG_DR3 = 69;;
let _X86_REG_DR4 = 70;;
let _X86_REG_DR5 = 71;;
let _X86_REG_DR6 = 72;;
let _X86_REG_DR7 = 73;;
let _X86_REG_DR8 = 74;;
let _X86_REG_DR9 = 75;;
let _X86_REG_DR10 = 76;;
let _X86_REG_DR11 = 77;;
let _X86_REG_DR12 = 78;;
let _X86_REG_DR13 = 79;;
let _X86_REG_DR14 = 80;;
let _X86_REG_DR15 = 81;;
let _X86_REG_FP0 = 82;;
let _X86_REG_FP1 = 83;;
let _X86_REG_FP2 = 84;;
let _X86_REG_FP3 = 85;;
let _X86_REG_FP4 = 86;;
let _X86_REG_FP5 = 87;;
let _X86_REG_FP6 = 88;;
let _X86_REG_FP7 = 89;;
let _X86_REG_K0 = 90;;
let _X86_REG_K1 = 91;;
let _X86_REG_K2 = 92;;
let _X86_REG_K3 = 93;;
let _X86_REG_K4 = 94;;
let _X86_REG_K5 = 95;;
let _X86_REG_K6 = 96;;
let _X86_REG_K7 = 97;;
let _X86_REG_MM0 = 98;;
let _X86_REG_MM1 = 99;;
let _X86_REG_MM2 = 100;;
let _X86_REG_MM3 = 101;;
let _X86_REG_MM4 = 102;;
let _X86_REG_MM5 = 103;;
let _X86_REG_MM6 = 104;;
let _X86_REG_MM7 = 105;;
let _X86_REG_R8 = 106;;
let _X86_REG_R9 = 107;;
let _X86_REG_R10 = 108;;
let _X86_REG_R11 = 109;;
let _X86_REG_R12 = 110;;
let _X86_REG_R13 = 111;;
let _X86_REG_R14 = 112;;
let _X86_REG_R15 = 113;;
let _X86_REG_ST0 = 114;;
let _X86_REG_ST1 = 115;;
let _X86_REG_ST2 = 116;;
let _X86_REG_ST3 = 117;;
let _X86_REG_ST4 = 118;;
let _X86_REG_ST5 = 119;;
let _X86_REG_ST6 = 120;;
let _X86_REG_ST7 = 121;;
let _X86_REG_XMM0 = 122;;
let _X86_REG_XMM1 = 123;;
let _X86_REG_XMM2 = 124;;
let _X86_REG_XMM3 = 125;;
let _X86_REG_XMM4 = 126;;
let _X86_REG_XMM5 = 127;;
let _X86_REG_XMM6 = 128;;
let _X86_REG_XMM7 = 129;;
let _X86_REG_XMM8 = 130;;
let _X86_REG_XMM9 = 131;;
let _X86_REG_XMM10 = 132;;
let _X86_REG_XMM11 = 133;;
let _X86_REG_XMM12 = 134;;
let _X86_REG_XMM13 = 135;;
let _X86_REG_XMM14 = 136;;
let _X86_REG_XMM15 = 137;;
let _X86_REG_XMM16 = 138;;
let _X86_REG_XMM17 = 139;;
let _X86_REG_XMM18 = 140;;
let _X86_REG_XMM19 = 141;;
let _X86_REG_XMM20 = 142;;
let _X86_REG_XMM21 = 143;;
let _X86_REG_XMM22 = 144;;
let _X86_REG_XMM23 = 145;;
let _X86_REG_XMM24 = 146;;
let _X86_REG_XMM25 = 147;;
let _X86_REG_XMM26 = 148;;
let _X86_REG_XMM27 = 149;;
let _X86_REG_XMM28 = 150;;
let _X86_REG_XMM29 = 151;;
let _X86_REG_XMM30 = 152;;
let _X86_REG_XMM31 = 153;;
let _X86_REG_YMM0 = 154;;
let _X86_REG_YMM1 = 155;;
let _X86_REG_YMM2 = 156;;
let _X86_REG_YMM3 = 157;;
let _X86_REG_YMM4 = 158;;
let _X86_REG_YMM5 = 159;;
let _X86_REG_YMM6 = 160;;
let _X86_REG_YMM7 = 161;;
let _X86_REG_YMM8 = 162;;
let _X86_REG_YMM9 = 163;;
let _X86_REG_YMM10 = 164;;
let _X86_REG_YMM11 = 165;;
let _X86_REG_YMM12 = 166;;
let _X86_REG_YMM13 = 167;;
let _X86_REG_YMM14 = 168;;
let _X86_REG_YMM15 = 169;;
let _X86_REG_YMM16 = 170;;
let _X86_REG_YMM17 = 171;;
let _X86_REG_YMM18 = 172;;
let _X86_REG_YMM19 = 173;;
let _X86_REG_YMM20 = 174;;
let _X86_REG_YMM21 = 175;;
let _X86_REG_YMM22 = 176;;
let _X86_REG_YMM23 = 177;;
let _X86_REG_YMM24 = 178;;
let _X86_REG_YMM25 = 179;;
let _X86_REG_YMM26 = 180;;
let _X86_REG_YMM27 = 181;;
let _X86_REG_YMM28 = 182;;
let _X86_REG_YMM29 = 183;;
let _X86_REG_YMM30 = 184;;
let _X86_REG_YMM31 = 185;;
let _X86_REG_ZMM0 = 186;;
let _X86_REG_ZMM1 = 187;;
let _X86_REG_ZMM2 = 188;;
let _X86_REG_ZMM3 = 189;;
let _X86_REG_ZMM4 = 190;;
let _X86_REG_ZMM5 = 191;;
let _X86_REG_ZMM6 = 192;;
let _X86_REG_ZMM7 = 193;;
let _X86_REG_ZMM8 = 194;;
let _X86_REG_ZMM9 = 195;;
let _X86_REG_ZMM10 = 196;;
let _X86_REG_ZMM11 = 197;;
let _X86_REG_ZMM12 = 198;;
let _X86_REG_ZMM13 = 199;;
let _X86_REG_ZMM14 = 200;;
let _X86_REG_ZMM15 = 201;;
let _X86_REG_ZMM16 = 202;;
let _X86_REG_ZMM17 = 203;;
let _X86_REG_ZMM18 = 204;;
let _X86_REG_ZMM19 = 205;;
let _X86_REG_ZMM20 = 206;;
let _X86_REG_ZMM21 = 207;;
let _X86_REG_ZMM22 = 208;;
let _X86_REG_ZMM23 = 209;;
let _X86_REG_ZMM24 = 210;;
let _X86_REG_ZMM25 = 211;;
let _X86_REG_ZMM26 = 212;;
let _X86_REG_ZMM27 = 213;;
let _X86_REG_ZMM28 = 214;;
let _X86_REG_ZMM29 = 215;;
let _X86_REG_ZMM30 = 216;;
let _X86_REG_ZMM31 = 217;;
let _X86_REG_R8B = 218;;
let _X86_REG_R9B = 219;;
let _X86_REG_R10B = 220;;
let _X86_REG_R11B = 221;;
let _X86_REG_R12B = 222;;
let _X86_REG_R13B = 223;;
let _X86_REG_R14B = 224;;
let _X86_REG_R15B = 225;;
let _X86_REG_R8D = 226;;
let _X86_REG_R9D = 227;;
let _X86_REG_R10D = 228;;
let _X86_REG_R11D = 229;;
let _X86_REG_R12D = 230;;
let _X86_REG_R13D = 231;;
let _X86_REG_R14D = 232;;
let _X86_REG_R15D = 233;;
let _X86_REG_R8W = 234;;
let _X86_REG_R9W = 235;;
let _X86_REG_R10W = 236;;
let _X86_REG_R11W = 237;;
let _X86_REG_R12W = 238;;
let _X86_REG_R13W = 239;;
let _X86_REG_R14W = 240;;
let _X86_REG_R15W = 241;;
let _X86_REG_ENDING = 242;;

(* Sub-flags of EFLAGS *)
let _X86_EFLAGS_MODIFY_AF = 1 lsl 0;;
let _X86_EFLAGS_MODIFY_CF = 1 lsl 1;;
let _X86_EFLAGS_MODIFY_SF = 1 lsl 2;;
let _X86_EFLAGS_MODIFY_ZF = 1 lsl 3;;
let _X86_EFLAGS_MODIFY_PF = 1 lsl 4;;
let _X86_EFLAGS_MODIFY_OF = 1 lsl 5;;
let _X86_EFLAGS_MODIFY_TF = 1 lsl 6;;
let _X86_EFLAGS_MODIFY_IF = 1 lsl 7;;
let _X86_EFLAGS_MODIFY_DF = 1 lsl 8;;
let _X86_EFLAGS_MODIFY_NT = 1 lsl 9;;
let _X86_EFLAGS_MODIFY_RF = 1 lsl 10;;
let _X86_EFLAGS_PRIOR_OF = 1 lsl 11;;
let _X86_EFLAGS_PRIOR_SF = 1 lsl 12;;
let _X86_EFLAGS_PRIOR_ZF = 1 lsl 13;;
let _X86_EFLAGS_PRIOR_AF = 1 lsl 14;;
let _X86_EFLAGS_PRIOR_PF = 1 lsl 15;;
let _X86_EFLAGS_PRIOR_CF = 1 lsl 16;;
let _X86_EFLAGS_PRIOR_TF = 1 lsl 17;;
let _X86_EFLAGS_PRIOR_IF = 1 lsl 18;;
let _X86_EFLAGS_PRIOR_DF = 1 lsl 19;;
let _X86_EFLAGS_PRIOR_NT = 1 lsl 20;;
let _X86_EFLAGS_RESET_OF = 1 lsl 21;;
let _X86_EFLAGS_RESET_CF = 1 lsl 22;;
let _X86_EFLAGS_RESET_DF = 1 lsl 23;;
let _X86_EFLAGS_RESET_IF = 1 lsl 24;;
let _X86_EFLAGS_RESET_SF = 1 lsl 25;;
let _X86_EFLAGS_RESET_AF = 1 lsl 26;;
let _X86_EFLAGS_RESET_TF = 1 lsl 27;;
let _X86_EFLAGS_RESET_NT = 1 lsl 28;;
let _X86_EFLAGS_RESET_PF = 1 lsl 29;;
let _X86_EFLAGS_SET_CF = 1 lsl 30;;
let _X86_EFLAGS_SET_DF = 1 lsl 31;;
let _X86_EFLAGS_SET_IF = 1 lsl 32;;
let _X86_EFLAGS_TEST_OF = 1 lsl 33;;
let _X86_EFLAGS_TEST_SF = 1 lsl 34;;
let _X86_EFLAGS_TEST_ZF = 1 lsl 35;;
let _X86_EFLAGS_TEST_PF = 1 lsl 36;;
let _X86_EFLAGS_TEST_CF = 1 lsl 37;;
let _X86_EFLAGS_TEST_NT = 1 lsl 38;;
let _X86_EFLAGS_TEST_DF = 1 lsl 39;;
let _X86_EFLAGS_UNDEFINED_OF = 1 lsl 40;;
let _X86_EFLAGS_UNDEFINED_SF = 1 lsl 41;;
let _X86_EFLAGS_UNDEFINED_ZF = 1 lsl 42;;
let _X86_EFLAGS_UNDEFINED_PF = 1 lsl 43;;
let _X86_EFLAGS_UNDEFINED_AF = 1 lsl 44;;
let _X86_EFLAGS_UNDEFINED_CF = 1 lsl 45;;
let _X86_EFLAGS_RESET_RF = 1 lsl 46;;
let _X86_EFLAGS_TEST_RF = 1 lsl 47;;
let _X86_EFLAGS_TEST_IF = 1 lsl 48;;
let _X86_EFLAGS_TEST_TF = 1 lsl 49;;
let _X86_EFLAGS_TEST_AF = 1 lsl 50;;
let _X86_EFLAGS_RESET_ZF = 1 lsl 51;;
let _X86_EFLAGS_SET_OF = 1 lsl 52;;
let _X86_EFLAGS_SET_SF = 1 lsl 53;;
let _X86_EFLAGS_SET_ZF = 1 lsl 54;;
let _X86_EFLAGS_SET_AF = 1 lsl 55;;
let _X86_EFLAGS_SET_PF = 1 lsl 56;;
let _X86_EFLAGS_RESET_0F = 1 lsl 57;;
let _X86_EFLAGS_RESET_AC = 1 lsl 58;;
let _X86_FPU_FLAGS_MODIFY_C0 = 1 lsl 0;;
let _X86_FPU_FLAGS_MODIFY_C1 = 1 lsl 1;;
let _X86_FPU_FLAGS_MODIFY_C2 = 1 lsl 2;;
let _X86_FPU_FLAGS_MODIFY_C3 = 1 lsl 3;;
let _X86_FPU_FLAGS_RESET_C0 = 1 lsl 4;;
let _X86_FPU_FLAGS_RESET_C1 = 1 lsl 5;;
let _X86_FPU_FLAGS_RESET_C2 = 1 lsl 6;;
let _X86_FPU_FLAGS_RESET_C3 = 1 lsl 7;;
let _X86_FPU_FLAGS_SET_C0 = 1 lsl 8;;
let _X86_FPU_FLAGS_SET_C1 = 1 lsl 9;;
let _X86_FPU_FLAGS_SET_C2 = 1 lsl 10;;
let _X86_FPU_FLAGS_SET_C3 = 1 lsl 11;;
let _X86_FPU_FLAGS_UNDEFINED_C0 = 1 lsl 12;;
let _X86_FPU_FLAGS_UNDEFINED_C1 = 1 lsl 13;;
let _X86_FPU_FLAGS_UNDEFINED_C2 = 1 lsl 14;;
let _X86_FPU_FLAGS_UNDEFINED_C3 = 1 lsl 15;;
let _X86_FPU_FLAGS_TEST_C0 = 1 lsl 16;;
let _X86_FPU_FLAGS_TEST_C1 = 1 lsl 17;;
let _X86_FPU_FLAGS_TEST_C2 = 1 lsl 18;;
let _X86_FPU_FLAGS_TEST_C3 = 1 lsl 19;;

(* Operand type for instruction's operands *)

let _X86_OP_INVALID = 0;;
let _X86_OP_REG = 1;;
let _X86_OP_IMM = 2;;
let _X86_OP_MEM = 3;;

(* XOP Code Condition type *)

let _X86_XOP_CC_INVALID = 0;;
let _X86_XOP_CC_LT = 1;;
let _X86_XOP_CC_LE = 2;;
let _X86_XOP_CC_GT = 3;;
let _X86_XOP_CC_GE = 4;;
let _X86_XOP_CC_EQ = 5;;
let _X86_XOP_CC_NEQ = 6;;
let _X86_XOP_CC_FALSE = 7;;
let _X86_XOP_CC_TRUE = 8;;

(* AVX broadcast type *)

let _X86_AVX_BCAST_INVALID = 0;;
let _X86_AVX_BCAST_2 = 1;;
let _X86_AVX_BCAST_4 = 2;;
let _X86_AVX_BCAST_8 = 3;;
let _X86_AVX_BCAST_16 = 4;;

(* SSE Code Condition type *)

let _X86_SSE_CC_INVALID = 0;;
let _X86_SSE_CC_EQ = 1;;
let _X86_SSE_CC_LT = 2;;
let _X86_SSE_CC_LE = 3;;
let _X86_SSE_CC_UNORD = 4;;
let _X86_SSE_CC_NEQ = 5;;
let _X86_SSE_CC_NLT = 6;;
let _X86_SSE_CC_NLE = 7;;
let _X86_SSE_CC_ORD = 8;;

(* AVX Code Condition type *)

let _X86_AVX_CC_INVALID = 0;;
let _X86_AVX_CC_EQ = 1;;
let _X86_AVX_CC_LT = 2;;
let _X86_AVX_CC_LE = 3;;
let _X86_AVX_CC_UNORD = 4;;
let _X86_AVX_CC_NEQ = 5;;
let _X86_AVX_CC_NLT = 6;;
let _X86_AVX_CC_NLE = 7;;
let _X86_AVX_CC_ORD = 8;;
let _X86_AVX_CC_EQ_UQ = 9;;
let _X86_AVX_CC_NGE = 10;;
let _X86_AVX_CC_NGT = 11;;
let _X86_AVX_CC_FALSE = 12;;
let _X86_AVX_CC_NEQ_OQ = 13;;
let _X86_AVX_CC_GE = 14;;
let _X86_AVX_CC_GT = 15;;
let _X86_AVX_CC_TRUE = 16;;
let _X86_AVX_CC_EQ_OS = 17;;
let _X86_AVX_CC_LT_OQ = 18;;
let _X86_AVX_CC_LE_OQ = 19;;
let _X86_AVX_CC_UNORD_S = 20;;
let _X86_AVX_CC_NEQ_US = 21;;
let _X86_AVX_CC_NLT_UQ = 22;;
let _X86_AVX_CC_NLE_UQ = 23;;
let _X86_AVX_CC_ORD_S = 24;;
let _X86_AVX_CC_EQ_US = 25;;
let _X86_AVX_CC_NGE_UQ = 26;;
let _X86_AVX_CC_NGT_UQ = 27;;
let _X86_AVX_CC_FALSE_OS = 28;;
let _X86_AVX_CC_NEQ_OS = 29;;
let _X86_AVX_CC_GE_OQ = 30;;
let _X86_AVX_CC_GT_OQ = 31;;
let _X86_AVX_CC_TRUE_US = 32;;

(* AVX static rounding mode type *)

let _X86_AVX_RM_INVALID = 0;;
let _X86_AVX_RM_RN = 1;;
let _X86_AVX_RM_RD = 2;;
let _X86_AVX_RM_RU = 3;;
let _X86_AVX_RM_RZ = 4;;

(* Instruction prefixes - to be used in cs_x86.prefix[] *)
let _X86_PREFIX_LOCK = 0xf0;;
let _X86_PREFIX_REP = 0xf3;;
let _X86_PREFIX_REPE = 0xf3;;
let _X86_PREFIX_REPNE = 0xf2;;
let _X86_PREFIX_CS = 0x2e;;
let _X86_PREFIX_SS = 0x36;;
let _X86_PREFIX_DS = 0x3e;;
let _X86_PREFIX_ES = 0x26;;
let _X86_PREFIX_FS = 0x64;;
let _X86_PREFIX_GS = 0x65;;
let _X86_PREFIX_OPSIZE = 0x66;;
let _X86_PREFIX_ADDRSIZE = 0x67;;

(* X86 instructions *)

let _X86_INS_INVALID = 0;;
let _X86_INS_AAA = 1;;
let _X86_INS_AAD = 2;;
let _X86_INS_AAM = 3;;
let _X86_INS_AAS = 4;;
let _X86_INS_FABS = 5;;
let _X86_INS_ADC = 6;;
let _X86_INS_ADCX = 7;;
let _X86_INS_ADD = 8;;
let _X86_INS_ADDPD = 9;;
let _X86_INS_ADDPS = 10;;
let _X86_INS_ADDSD = 11;;
let _X86_INS_ADDSS = 12;;
let _X86_INS_ADDSUBPD = 13;;
let _X86_INS_ADDSUBPS = 14;;
let _X86_INS_FADD = 15;;
let _X86_INS_FIADD = 16;;
let _X86_INS_FADDP = 17;;
let _X86_INS_ADOX = 18;;
let _X86_INS_AESDECLAST = 19;;
let _X86_INS_AESDEC = 20;;
let _X86_INS_AESENCLAST = 21;;
let _X86_INS_AESENC = 22;;
let _X86_INS_AESIMC = 23;;
let _X86_INS_AESKEYGENASSIST = 24;;
let _X86_INS_AND = 25;;
let _X86_INS_ANDN = 26;;
let _X86_INS_ANDNPD = 27;;
let _X86_INS_ANDNPS = 28;;
let _X86_INS_ANDPD = 29;;
let _X86_INS_ANDPS = 30;;
let _X86_INS_ARPL = 31;;
let _X86_INS_BEXTR = 32;;
let _X86_INS_BLCFILL = 33;;
let _X86_INS_BLCI = 34;;
let _X86_INS_BLCIC = 35;;
let _X86_INS_BLCMSK = 36;;
let _X86_INS_BLCS = 37;;
let _X86_INS_BLENDPD = 38;;
let _X86_INS_BLENDPS = 39;;
let _X86_INS_BLENDVPD = 40;;
let _X86_INS_BLENDVPS = 41;;
let _X86_INS_BLSFILL = 42;;
let _X86_INS_BLSI = 43;;
let _X86_INS_BLSIC = 44;;
let _X86_INS_BLSMSK = 45;;
let _X86_INS_BLSR = 46;;
let _X86_INS_BOUND = 47;;
let _X86_INS_BSF = 48;;
let _X86_INS_BSR = 49;;
let _X86_INS_BSWAP = 50;;
let _X86_INS_BT = 51;;
let _X86_INS_BTC = 52;;
let _X86_INS_BTR = 53;;
let _X86_INS_BTS = 54;;
let _X86_INS_BZHI = 55;;
let _X86_INS_CALL = 56;;
let _X86_INS_CBW = 57;;
let _X86_INS_CDQ = 58;;
let _X86_INS_CDQE = 59;;
let _X86_INS_FCHS = 60;;
let _X86_INS_CLAC = 61;;
let _X86_INS_CLC = 62;;
let _X86_INS_CLD = 63;;
let _X86_INS_CLFLUSH = 64;;
let _X86_INS_CLFLUSHOPT = 65;;
let _X86_INS_CLGI = 66;;
let _X86_INS_CLI = 67;;
let _X86_INS_CLTS = 68;;
let _X86_INS_CLWB = 69;;
let _X86_INS_CMC = 70;;
let _X86_INS_CMOVA = 71;;
let _X86_INS_CMOVAE = 72;;
let _X86_INS_CMOVB = 73;;
let _X86_INS_CMOVBE = 74;;
let _X86_INS_FCMOVBE = 75;;
let _X86_INS_FCMOVB = 76;;
let _X86_INS_CMOVE = 77;;
let _X86_INS_FCMOVE = 78;;
let _X86_INS_CMOVG = 79;;
let _X86_INS_CMOVGE = 80;;
let _X86_INS_CMOVL = 81;;
let _X86_INS_CMOVLE = 82;;
let _X86_INS_FCMOVNBE = 83;;
let _X86_INS_FCMOVNB = 84;;
let _X86_INS_CMOVNE = 85;;
let _X86_INS_FCMOVNE = 86;;
let _X86_INS_CMOVNO = 87;;
let _X86_INS_CMOVNP = 88;;
let _X86_INS_FCMOVNU = 89;;
let _X86_INS_CMOVNS = 90;;
let _X86_INS_CMOVO = 91;;
let _X86_INS_CMOVP = 92;;
let _X86_INS_FCMOVU = 93;;
let _X86_INS_CMOVS = 94;;
let _X86_INS_CMP = 95;;
let _X86_INS_CMPSB = 96;;
let _X86_INS_CMPSQ = 97;;
let _X86_INS_CMPSW = 98;;
let _X86_INS_CMPXCHG16B = 99;;
let _X86_INS_CMPXCHG = 100;;
let _X86_INS_CMPXCHG8B = 101;;
let _X86_INS_COMISD = 102;;
let _X86_INS_COMISS = 103;;
let _X86_INS_FCOMP = 104;;
let _X86_INS_FCOMIP = 105;;
let _X86_INS_FCOMI = 106;;
let _X86_INS_FCOM = 107;;
let _X86_INS_FCOS = 108;;
let _X86_INS_CPUID = 109;;
let _X86_INS_CQO = 110;;
let _X86_INS_CRC32 = 111;;
let _X86_INS_CVTDQ2PD = 112;;
let _X86_INS_CVTDQ2PS = 113;;
let _X86_INS_CVTPD2DQ = 114;;
let _X86_INS_CVTPD2PS = 115;;
let _X86_INS_CVTPS2DQ = 116;;
let _X86_INS_CVTPS2PD = 117;;
let _X86_INS_CVTSD2SI = 118;;
let _X86_INS_CVTSD2SS = 119;;
let _X86_INS_CVTSI2SD = 120;;
let _X86_INS_CVTSI2SS = 121;;
let _X86_INS_CVTSS2SD = 122;;
let _X86_INS_CVTSS2SI = 123;;
let _X86_INS_CVTTPD2DQ = 124;;
let _X86_INS_CVTTPS2DQ = 125;;
let _X86_INS_CVTTSD2SI = 126;;
let _X86_INS_CVTTSS2SI = 127;;
let _X86_INS_CWD = 128;;
let _X86_INS_CWDE = 129;;
let _X86_INS_DAA = 130;;
let _X86_INS_DAS = 131;;
let _X86_INS_DATA16 = 132;;
let _X86_INS_DEC = 133;;
let _X86_INS_DIV = 134;;
let _X86_INS_DIVPD = 135;;
let _X86_INS_DIVPS = 136;;
let _X86_INS_FDIVR = 137;;
let _X86_INS_FIDIVR = 138;;
let _X86_INS_FDIVRP = 139;;
let _X86_INS_DIVSD = 140;;
let _X86_INS_DIVSS = 141;;
let _X86_INS_FDIV = 142;;
let _X86_INS_FIDIV = 143;;
let _X86_INS_FDIVP = 144;;
let _X86_INS_DPPD = 145;;
let _X86_INS_DPPS = 146;;
let _X86_INS_RET = 147;;
let _X86_INS_ENCLS = 148;;
let _X86_INS_ENCLU = 149;;
let _X86_INS_ENTER = 150;;
let _X86_INS_EXTRACTPS = 151;;
let _X86_INS_EXTRQ = 152;;
let _X86_INS_F2XM1 = 153;;
let _X86_INS_LCALL = 154;;
let _X86_INS_LJMP = 155;;
let _X86_INS_FBLD = 156;;
let _X86_INS_FBSTP = 157;;
let _X86_INS_FCOMPP = 158;;
let _X86_INS_FDECSTP = 159;;
let _X86_INS_FEMMS = 160;;
let _X86_INS_FFREE = 161;;
let _X86_INS_FICOM = 162;;
let _X86_INS_FICOMP = 163;;
let _X86_INS_FINCSTP = 164;;
let _X86_INS_FLDCW = 165;;
let _X86_INS_FLDENV = 166;;
let _X86_INS_FLDL2E = 167;;
let _X86_INS_FLDL2T = 168;;
let _X86_INS_FLDLG2 = 169;;
let _X86_INS_FLDLN2 = 170;;
let _X86_INS_FLDPI = 171;;
let _X86_INS_FNCLEX = 172;;
let _X86_INS_FNINIT = 173;;
let _X86_INS_FNOP = 174;;
let _X86_INS_FNSTCW = 175;;
let _X86_INS_FNSTSW = 176;;
let _X86_INS_FPATAN = 177;;
let _X86_INS_FPREM = 178;;
let _X86_INS_FPREM1 = 179;;
let _X86_INS_FPTAN = 180;;
let _X86_INS_FFREEP = 181;;
let _X86_INS_FRNDINT = 182;;
let _X86_INS_FRSTOR = 183;;
let _X86_INS_FNSAVE = 184;;
let _X86_INS_FSCALE = 185;;
let _X86_INS_FSETPM = 186;;
let _X86_INS_FSINCOS = 187;;
let _X86_INS_FNSTENV = 188;;
let _X86_INS_FXAM = 189;;
let _X86_INS_FXRSTOR = 190;;
let _X86_INS_FXRSTOR64 = 191;;
let _X86_INS_FXSAVE = 192;;
let _X86_INS_FXSAVE64 = 193;;
let _X86_INS_FXTRACT = 194;;
let _X86_INS_FYL2X = 195;;
let _X86_INS_FYL2XP1 = 196;;
let _X86_INS_MOVAPD = 197;;
let _X86_INS_MOVAPS = 198;;
let _X86_INS_ORPD = 199;;
let _X86_INS_ORPS = 200;;
let _X86_INS_VMOVAPD = 201;;
let _X86_INS_VMOVAPS = 202;;
let _X86_INS_XORPD = 203;;
let _X86_INS_XORPS = 204;;
let _X86_INS_GETSEC = 205;;
let _X86_INS_HADDPD = 206;;
let _X86_INS_HADDPS = 207;;
let _X86_INS_HLT = 208;;
let _X86_INS_HSUBPD = 209;;
let _X86_INS_HSUBPS = 210;;
let _X86_INS_IDIV = 211;;
let _X86_INS_FILD = 212;;
let _X86_INS_IMUL = 213;;
let _X86_INS_IN = 214;;
let _X86_INS_INC = 215;;
let _X86_INS_INSB = 216;;
let _X86_INS_INSERTPS = 217;;
let _X86_INS_INSERTQ = 218;;
let _X86_INS_INSD = 219;;
let _X86_INS_INSW = 220;;
let _X86_INS_INT = 221;;
let _X86_INS_INT1 = 222;;
let _X86_INS_INT3 = 223;;
let _X86_INS_INTO = 224;;
let _X86_INS_INVD = 225;;
let _X86_INS_INVEPT = 226;;
let _X86_INS_INVLPG = 227;;
let _X86_INS_INVLPGA = 228;;
let _X86_INS_INVPCID = 229;;
let _X86_INS_INVVPID = 230;;
let _X86_INS_IRET = 231;;
let _X86_INS_IRETD = 232;;
let _X86_INS_IRETQ = 233;;
let _X86_INS_FISTTP = 234;;
let _X86_INS_FIST = 235;;
let _X86_INS_FISTP = 236;;
let _X86_INS_UCOMISD = 237;;
let _X86_INS_UCOMISS = 238;;
let _X86_INS_VCOMISD = 239;;
let _X86_INS_VCOMISS = 240;;
let _X86_INS_VCVTSD2SS = 241;;
let _X86_INS_VCVTSI2SD = 242;;
let _X86_INS_VCVTSI2SS = 243;;
let _X86_INS_VCVTSS2SD = 244;;
let _X86_INS_VCVTTSD2SI = 245;;
let _X86_INS_VCVTTSD2USI = 246;;
let _X86_INS_VCVTTSS2SI = 247;;
let _X86_INS_VCVTTSS2USI = 248;;
let _X86_INS_VCVTUSI2SD = 249;;
let _X86_INS_VCVTUSI2SS = 250;;
let _X86_INS_VUCOMISD = 251;;
let _X86_INS_VUCOMISS = 252;;
let _X86_INS_JAE = 253;;
let _X86_INS_JA = 254;;
let _X86_INS_JBE = 255;;
let _X86_INS_JB = 256;;
let _X86_INS_JCXZ = 257;;
let _X86_INS_JECXZ = 258;;
let _X86_INS_JE = 259;;
let _X86_INS_JGE = 260;;
let _X86_INS_JG = 261;;
let _X86_INS_JLE = 262;;
let _X86_INS_JL = 263;;
let _X86_INS_JMP = 264;;
let _X86_INS_JNE = 265;;
let _X86_INS_JNO = 266;;
let _X86_INS_JNP = 267;;
let _X86_INS_JNS = 268;;
let _X86_INS_JO = 269;;
let _X86_INS_JP = 270;;
let _X86_INS_JRCXZ = 271;;
let _X86_INS_JS = 272;;
let _X86_INS_KANDB = 273;;
let _X86_INS_KANDD = 274;;
let _X86_INS_KANDNB = 275;;
let _X86_INS_KANDND = 276;;
let _X86_INS_KANDNQ = 277;;
let _X86_INS_KANDNW = 278;;
let _X86_INS_KANDQ = 279;;
let _X86_INS_KANDW = 280;;
let _X86_INS_KMOVB = 281;;
let _X86_INS_KMOVD = 282;;
let _X86_INS_KMOVQ = 283;;
let _X86_INS_KMOVW = 284;;
let _X86_INS_KNOTB = 285;;
let _X86_INS_KNOTD = 286;;
let _X86_INS_KNOTQ = 287;;
let _X86_INS_KNOTW = 288;;
let _X86_INS_KORB = 289;;
let _X86_INS_KORD = 290;;
let _X86_INS_KORQ = 291;;
let _X86_INS_KORTESTB = 292;;
let _X86_INS_KORTESTD = 293;;
let _X86_INS_KORTESTQ = 294;;
let _X86_INS_KORTESTW = 295;;
let _X86_INS_KORW = 296;;
let _X86_INS_KSHIFTLB = 297;;
let _X86_INS_KSHIFTLD = 298;;
let _X86_INS_KSHIFTLQ = 299;;
let _X86_INS_KSHIFTLW = 300;;
let _X86_INS_KSHIFTRB = 301;;
let _X86_INS_KSHIFTRD = 302;;
let _X86_INS_KSHIFTRQ = 303;;
let _X86_INS_KSHIFTRW = 304;;
let _X86_INS_KUNPCKBW = 305;;
let _X86_INS_KXNORB = 306;;
let _X86_INS_KXNORD = 307;;
let _X86_INS_KXNORQ = 308;;
let _X86_INS_KXNORW = 309;;
let _X86_INS_KXORB = 310;;
let _X86_INS_KXORD = 311;;
let _X86_INS_KXORQ = 312;;
let _X86_INS_KXORW = 313;;
let _X86_INS_LAHF = 314;;
let _X86_INS_LAR = 315;;
let _X86_INS_LDDQU = 316;;
let _X86_INS_LDMXCSR = 317;;
let _X86_INS_LDS = 318;;
let _X86_INS_FLDZ = 319;;
let _X86_INS_FLD1 = 320;;
let _X86_INS_FLD = 321;;
let _X86_INS_LEA = 322;;
let _X86_INS_LEAVE = 323;;
let _X86_INS_LES = 324;;
let _X86_INS_LFENCE = 325;;
let _X86_INS_LFS = 326;;
let _X86_INS_LGDT = 327;;
let _X86_INS_LGS = 328;;
let _X86_INS_LIDT = 329;;
let _X86_INS_LLDT = 330;;
let _X86_INS_LMSW = 331;;
let _X86_INS_OR = 332;;
let _X86_INS_SUB = 333;;
let _X86_INS_XOR = 334;;
let _X86_INS_LODSB = 335;;
let _X86_INS_LODSD = 336;;
let _X86_INS_LODSQ = 337;;
let _X86_INS_LODSW = 338;;
let _X86_INS_LOOP = 339;;
let _X86_INS_LOOPE = 340;;
let _X86_INS_LOOPNE = 341;;
let _X86_INS_RETF = 342;;
let _X86_INS_RETFQ = 343;;
let _X86_INS_LSL = 344;;
let _X86_INS_LSS = 345;;
let _X86_INS_LTR = 346;;
let _X86_INS_XADD = 347;;
let _X86_INS_LZCNT = 348;;
let _X86_INS_MASKMOVDQU = 349;;
let _X86_INS_MAXPD = 350;;
let _X86_INS_MAXPS = 351;;
let _X86_INS_MAXSD = 352;;
let _X86_INS_MAXSS = 353;;
let _X86_INS_MFENCE = 354;;
let _X86_INS_MINPD = 355;;
let _X86_INS_MINPS = 356;;
let _X86_INS_MINSD = 357;;
let _X86_INS_MINSS = 358;;
let _X86_INS_CVTPD2PI = 359;;
let _X86_INS_CVTPI2PD = 360;;
let _X86_INS_CVTPI2PS = 361;;
let _X86_INS_CVTPS2PI = 362;;
let _X86_INS_CVTTPD2PI = 363;;
let _X86_INS_CVTTPS2PI = 364;;
let _X86_INS_EMMS = 365;;
let _X86_INS_MASKMOVQ = 366;;
let _X86_INS_MOVD = 367;;
let _X86_INS_MOVDQ2Q = 368;;
let _X86_INS_MOVNTQ = 369;;
let _X86_INS_MOVQ2DQ = 370;;
let _X86_INS_MOVQ = 371;;
let _X86_INS_PABSB = 372;;
let _X86_INS_PABSD = 373;;
let _X86_INS_PABSW = 374;;
let _X86_INS_PACKSSDW = 375;;
let _X86_INS_PACKSSWB = 376;;
let _X86_INS_PACKUSWB = 377;;
let _X86_INS_PADDB = 378;;
let _X86_INS_PADDD = 379;;
let _X86_INS_PADDQ = 380;;
let _X86_INS_PADDSB = 381;;
let _X86_INS_PADDSW = 382;;
let _X86_INS_PADDUSB = 383;;
let _X86_INS_PADDUSW = 384;;
let _X86_INS_PADDW = 385;;
let _X86_INS_PALIGNR = 386;;
let _X86_INS_PANDN = 387;;
let _X86_INS_PAND = 388;;
let _X86_INS_PAVGB = 389;;
let _X86_INS_PAVGW = 390;;
let _X86_INS_PCMPEQB = 391;;
let _X86_INS_PCMPEQD = 392;;
let _X86_INS_PCMPEQW = 393;;
let _X86_INS_PCMPGTB = 394;;
let _X86_INS_PCMPGTD = 395;;
let _X86_INS_PCMPGTW = 396;;
let _X86_INS_PEXTRW = 397;;
let _X86_INS_PHADDSW = 398;;
let _X86_INS_PHADDW = 399;;
let _X86_INS_PHADDD = 400;;
let _X86_INS_PHSUBD = 401;;
let _X86_INS_PHSUBSW = 402;;
let _X86_INS_PHSUBW = 403;;
let _X86_INS_PINSRW = 404;;
let _X86_INS_PMADDUBSW = 405;;
let _X86_INS_PMADDWD = 406;;
let _X86_INS_PMAXSW = 407;;
let _X86_INS_PMAXUB = 408;;
let _X86_INS_PMINSW = 409;;
let _X86_INS_PMINUB = 410;;
let _X86_INS_PMOVMSKB = 411;;
let _X86_INS_PMULHRSW = 412;;
let _X86_INS_PMULHUW = 413;;
let _X86_INS_PMULHW = 414;;
let _X86_INS_PMULLW = 415;;
let _X86_INS_PMULUDQ = 416;;
let _X86_INS_POR = 417;;
let _X86_INS_PSADBW = 418;;
let _X86_INS_PSHUFB = 419;;
let _X86_INS_PSHUFW = 420;;
let _X86_INS_PSIGNB = 421;;
let _X86_INS_PSIGND = 422;;
let _X86_INS_PSIGNW = 423;;
let _X86_INS_PSLLD = 424;;
let _X86_INS_PSLLQ = 425;;
let _X86_INS_PSLLW = 426;;
let _X86_INS_PSRAD = 427;;
let _X86_INS_PSRAW = 428;;
let _X86_INS_PSRLD = 429;;
let _X86_INS_PSRLQ = 430;;
let _X86_INS_PSRLW = 431;;
let _X86_INS_PSUBB = 432;;
let _X86_INS_PSUBD = 433;;
let _X86_INS_PSUBQ = 434;;
let _X86_INS_PSUBSB = 435;;
let _X86_INS_PSUBSW = 436;;
let _X86_INS_PSUBUSB = 437;;
let _X86_INS_PSUBUSW = 438;;
let _X86_INS_PSUBW = 439;;
let _X86_INS_PUNPCKHBW = 440;;
let _X86_INS_PUNPCKHDQ = 441;;
let _X86_INS_PUNPCKHWD = 442;;
let _X86_INS_PUNPCKLBW = 443;;
let _X86_INS_PUNPCKLDQ = 444;;
let _X86_INS_PUNPCKLWD = 445;;
let _X86_INS_PXOR = 446;;
let _X86_INS_MONITOR = 447;;
let _X86_INS_MONTMUL = 448;;
let _X86_INS_MOV = 449;;
let _X86_INS_MOVABS = 450;;
let _X86_INS_MOVBE = 451;;
let _X86_INS_MOVDDUP = 452;;
let _X86_INS_MOVDQA = 453;;
let _X86_INS_MOVDQU = 454;;
let _X86_INS_MOVHLPS = 455;;
let _X86_INS_MOVHPD = 456;;
let _X86_INS_MOVHPS = 457;;
let _X86_INS_MOVLHPS = 458;;
let _X86_INS_MOVLPD = 459;;
let _X86_INS_MOVLPS = 460;;
let _X86_INS_MOVMSKPD = 461;;
let _X86_INS_MOVMSKPS = 462;;
let _X86_INS_MOVNTDQA = 463;;
let _X86_INS_MOVNTDQ = 464;;
let _X86_INS_MOVNTI = 465;;
let _X86_INS_MOVNTPD = 466;;
let _X86_INS_MOVNTPS = 467;;
let _X86_INS_MOVNTSD = 468;;
let _X86_INS_MOVNTSS = 469;;
let _X86_INS_MOVSB = 470;;
let _X86_INS_MOVSD = 471;;
let _X86_INS_MOVSHDUP = 472;;
let _X86_INS_MOVSLDUP = 473;;
let _X86_INS_MOVSQ = 474;;
let _X86_INS_MOVSS = 475;;
let _X86_INS_MOVSW = 476;;
let _X86_INS_MOVSX = 477;;
let _X86_INS_MOVSXD = 478;;
let _X86_INS_MOVUPD = 479;;
let _X86_INS_MOVUPS = 480;;
let _X86_INS_MOVZX = 481;;
let _X86_INS_MPSADBW = 482;;
let _X86_INS_MUL = 483;;
let _X86_INS_MULPD = 484;;
let _X86_INS_MULPS = 485;;
let _X86_INS_MULSD = 486;;
let _X86_INS_MULSS = 487;;
let _X86_INS_MULX = 488;;
let _X86_INS_FMUL = 489;;
let _X86_INS_FIMUL = 490;;
let _X86_INS_FMULP = 491;;
let _X86_INS_MWAIT = 492;;
let _X86_INS_NEG = 493;;
let _X86_INS_NOP = 494;;
let _X86_INS_NOT = 495;;
let _X86_INS_OUT = 496;;
let _X86_INS_OUTSB = 497;;
let _X86_INS_OUTSD = 498;;
let _X86_INS_OUTSW = 499;;
let _X86_INS_PACKUSDW = 500;;
let _X86_INS_PAUSE = 501;;
let _X86_INS_PAVGUSB = 502;;
let _X86_INS_PBLENDVB = 503;;
let _X86_INS_PBLENDW = 504;;
let _X86_INS_PCLMULQDQ = 505;;
let _X86_INS_PCMPEQQ = 506;;
let _X86_INS_PCMPESTRI = 507;;
let _X86_INS_PCMPESTRM = 508;;
let _X86_INS_PCMPGTQ = 509;;
let _X86_INS_PCMPISTRI = 510;;
let _X86_INS_PCMPISTRM = 511;;
let _X86_INS_PCOMMIT = 512;;
let _X86_INS_PDEP = 513;;
let _X86_INS_PEXT = 514;;
let _X86_INS_PEXTRB = 515;;
let _X86_INS_PEXTRD = 516;;
let _X86_INS_PEXTRQ = 517;;
let _X86_INS_PF2ID = 518;;
let _X86_INS_PF2IW = 519;;
let _X86_INS_PFACC = 520;;
let _X86_INS_PFADD = 521;;
let _X86_INS_PFCMPEQ = 522;;
let _X86_INS_PFCMPGE = 523;;
let _X86_INS_PFCMPGT = 524;;
let _X86_INS_PFMAX = 525;;
let _X86_INS_PFMIN = 526;;
let _X86_INS_PFMUL = 527;;
let _X86_INS_PFNACC = 528;;
let _X86_INS_PFPNACC = 529;;
let _X86_INS_PFRCPIT1 = 530;;
let _X86_INS_PFRCPIT2 = 531;;
let _X86_INS_PFRCP = 532;;
let _X86_INS_PFRSQIT1 = 533;;
let _X86_INS_PFRSQRT = 534;;
let _X86_INS_PFSUBR = 535;;
let _X86_INS_PFSUB = 536;;
let _X86_INS_PHMINPOSUW = 537;;
let _X86_INS_PI2FD = 538;;
let _X86_INS_PI2FW = 539;;
let _X86_INS_PINSRB = 540;;
let _X86_INS_PINSRD = 541;;
let _X86_INS_PINSRQ = 542;;
let _X86_INS_PMAXSB = 543;;
let _X86_INS_PMAXSD = 544;;
let _X86_INS_PMAXUD = 545;;
let _X86_INS_PMAXUW = 546;;
let _X86_INS_PMINSB = 547;;
let _X86_INS_PMINSD = 548;;
let _X86_INS_PMINUD = 549;;
let _X86_INS_PMINUW = 550;;
let _X86_INS_PMOVSXBD = 551;;
let _X86_INS_PMOVSXBQ = 552;;
let _X86_INS_PMOVSXBW = 553;;
let _X86_INS_PMOVSXDQ = 554;;
let _X86_INS_PMOVSXWD = 555;;
let _X86_INS_PMOVSXWQ = 556;;
let _X86_INS_PMOVZXBD = 557;;
let _X86_INS_PMOVZXBQ = 558;;
let _X86_INS_PMOVZXBW = 559;;
let _X86_INS_PMOVZXDQ = 560;;
let _X86_INS_PMOVZXWD = 561;;
let _X86_INS_PMOVZXWQ = 562;;
let _X86_INS_PMULDQ = 563;;
let _X86_INS_PMULHRW = 564;;
let _X86_INS_PMULLD = 565;;
let _X86_INS_POP = 566;;
let _X86_INS_POPAW = 567;;
let _X86_INS_POPAL = 568;;
let _X86_INS_POPCNT = 569;;
let _X86_INS_POPF = 570;;
let _X86_INS_POPFD = 571;;
let _X86_INS_POPFQ = 572;;
let _X86_INS_PREFETCH = 573;;
let _X86_INS_PREFETCHNTA = 574;;
let _X86_INS_PREFETCHT0 = 575;;
let _X86_INS_PREFETCHT1 = 576;;
let _X86_INS_PREFETCHT2 = 577;;
let _X86_INS_PREFETCHW = 578;;
let _X86_INS_PSHUFD = 579;;
let _X86_INS_PSHUFHW = 580;;
let _X86_INS_PSHUFLW = 581;;
let _X86_INS_PSLLDQ = 582;;
let _X86_INS_PSRLDQ = 583;;
let _X86_INS_PSWAPD = 584;;
let _X86_INS_PTEST = 585;;
let _X86_INS_PUNPCKHQDQ = 586;;
let _X86_INS_PUNPCKLQDQ = 587;;
let _X86_INS_PUSH = 588;;
let _X86_INS_PUSHAW = 589;;
let _X86_INS_PUSHAL = 590;;
let _X86_INS_PUSHF = 591;;
let _X86_INS_PUSHFD = 592;;
let _X86_INS_PUSHFQ = 593;;
let _X86_INS_RCL = 594;;
let _X86_INS_RCPPS = 595;;
let _X86_INS_RCPSS = 596;;
let _X86_INS_RCR = 597;;
let _X86_INS_RDFSBASE = 598;;
let _X86_INS_RDGSBASE = 599;;
let _X86_INS_RDMSR = 600;;
let _X86_INS_RDPMC = 601;;
let _X86_INS_RDRAND = 602;;
let _X86_INS_RDSEED = 603;;
let _X86_INS_RDTSC = 604;;
let _X86_INS_RDTSCP = 605;;
let _X86_INS_ROL = 606;;
let _X86_INS_ROR = 607;;
let _X86_INS_RORX = 608;;
let _X86_INS_ROUNDPD = 609;;
let _X86_INS_ROUNDPS = 610;;
let _X86_INS_ROUNDSD = 611;;
let _X86_INS_ROUNDSS = 612;;
let _X86_INS_RSM = 613;;
let _X86_INS_RSQRTPS = 614;;
let _X86_INS_RSQRTSS = 615;;
let _X86_INS_SAHF = 616;;
let _X86_INS_SAL = 617;;
let _X86_INS_SALC = 618;;
let _X86_INS_SAR = 619;;
let _X86_INS_SARX = 620;;
let _X86_INS_SBB = 621;;
let _X86_INS_SCASB = 622;;
let _X86_INS_SCASD = 623;;
let _X86_INS_SCASQ = 624;;
let _X86_INS_SCASW = 625;;
let _X86_INS_SETAE = 626;;
let _X86_INS_SETA = 627;;
let _X86_INS_SETBE = 628;;
let _X86_INS_SETB = 629;;
let _X86_INS_SETE = 630;;
let _X86_INS_SETGE = 631;;
let _X86_INS_SETG = 632;;
let _X86_INS_SETLE = 633;;
let _X86_INS_SETL = 634;;
let _X86_INS_SETNE = 635;;
let _X86_INS_SETNO = 636;;
let _X86_INS_SETNP = 637;;
let _X86_INS_SETNS = 638;;
let _X86_INS_SETO = 639;;
let _X86_INS_SETP = 640;;
let _X86_INS_SETS = 641;;
let _X86_INS_SFENCE = 642;;
let _X86_INS_SGDT = 643;;
let _X86_INS_SHA1MSG1 = 644;;
let _X86_INS_SHA1MSG2 = 645;;
let _X86_INS_SHA1NEXTE = 646;;
let _X86_INS_SHA1RNDS4 = 647;;
let _X86_INS_SHA256MSG1 = 648;;
let _X86_INS_SHA256MSG2 = 649;;
let _X86_INS_SHA256RNDS2 = 650;;
let _X86_INS_SHL = 651;;
let _X86_INS_SHLD = 652;;
let _X86_INS_SHLX = 653;;
let _X86_INS_SHR = 654;;
let _X86_INS_SHRD = 655;;
let _X86_INS_SHRX = 656;;
let _X86_INS_SHUFPD = 657;;
let _X86_INS_SHUFPS = 658;;
let _X86_INS_SIDT = 659;;
let _X86_INS_FSIN = 660;;
let _X86_INS_SKINIT = 661;;
let _X86_INS_SLDT = 662;;
let _X86_INS_SMSW = 663;;
let _X86_INS_SQRTPD = 664;;
let _X86_INS_SQRTPS = 665;;
let _X86_INS_SQRTSD = 666;;
let _X86_INS_SQRTSS = 667;;
let _X86_INS_FSQRT = 668;;
let _X86_INS_STAC = 669;;
let _X86_INS_STC = 670;;
let _X86_INS_STD = 671;;
let _X86_INS_STGI = 672;;
let _X86_INS_STI = 673;;
let _X86_INS_STMXCSR = 674;;
let _X86_INS_STOSB = 675;;
let _X86_INS_STOSD = 676;;
let _X86_INS_STOSQ = 677;;
let _X86_INS_STOSW = 678;;
let _X86_INS_STR = 679;;
let _X86_INS_FST = 680;;
let _X86_INS_FSTP = 681;;
let _X86_INS_FSTPNCE = 682;;
let _X86_INS_FXCH = 683;;
let _X86_INS_SUBPD = 684;;
let _X86_INS_SUBPS = 685;;
let _X86_INS_FSUBR = 686;;
let _X86_INS_FISUBR = 687;;
let _X86_INS_FSUBRP = 688;;
let _X86_INS_SUBSD = 689;;
let _X86_INS_SUBSS = 690;;
let _X86_INS_FSUB = 691;;
let _X86_INS_FISUB = 692;;
let _X86_INS_FSUBP = 693;;
let _X86_INS_SWAPGS = 694;;
let _X86_INS_SYSCALL = 695;;
let _X86_INS_SYSENTER = 696;;
let _X86_INS_SYSEXIT = 697;;
let _X86_INS_SYSRET = 698;;
let _X86_INS_T1MSKC = 699;;
let _X86_INS_TEST = 700;;
let _X86_INS_UD2 = 701;;
let _X86_INS_FTST = 702;;
let _X86_INS_TZCNT = 703;;
let _X86_INS_TZMSK = 704;;
let _X86_INS_FUCOMIP = 705;;
let _X86_INS_FUCOMI = 706;;
let _X86_INS_FUCOMPP = 707;;
let _X86_INS_FUCOMP = 708;;
let _X86_INS_FUCOM = 709;;
let _X86_INS_UD2B = 710;;
let _X86_INS_UNPCKHPD = 711;;
let _X86_INS_UNPCKHPS = 712;;
let _X86_INS_UNPCKLPD = 713;;
let _X86_INS_UNPCKLPS = 714;;
let _X86_INS_VADDPD = 715;;
let _X86_INS_VADDPS = 716;;
let _X86_INS_VADDSD = 717;;
let _X86_INS_VADDSS = 718;;
let _X86_INS_VADDSUBPD = 719;;
let _X86_INS_VADDSUBPS = 720;;
let _X86_INS_VAESDECLAST = 721;;
let _X86_INS_VAESDEC = 722;;
let _X86_INS_VAESENCLAST = 723;;
let _X86_INS_VAESENC = 724;;
let _X86_INS_VAESIMC = 725;;
let _X86_INS_VAESKEYGENASSIST = 726;;
let _X86_INS_VALIGND = 727;;
let _X86_INS_VALIGNQ = 728;;
let _X86_INS_VANDNPD = 729;;
let _X86_INS_VANDNPS = 730;;
let _X86_INS_VANDPD = 731;;
let _X86_INS_VANDPS = 732;;
let _X86_INS_VBLENDMPD = 733;;
let _X86_INS_VBLENDMPS = 734;;
let _X86_INS_VBLENDPD = 735;;
let _X86_INS_VBLENDPS = 736;;
let _X86_INS_VBLENDVPD = 737;;
let _X86_INS_VBLENDVPS = 738;;
let _X86_INS_VBROADCASTF128 = 739;;
let _X86_INS_VBROADCASTI32X4 = 740;;
let _X86_INS_VBROADCASTI64X4 = 741;;
let _X86_INS_VBROADCASTSD = 742;;
let _X86_INS_VBROADCASTSS = 743;;
let _X86_INS_VCOMPRESSPD = 744;;
let _X86_INS_VCOMPRESSPS = 745;;
let _X86_INS_VCVTDQ2PD = 746;;
let _X86_INS_VCVTDQ2PS = 747;;
let _X86_INS_VCVTPD2DQX = 748;;
let _X86_INS_VCVTPD2DQ = 749;;
let _X86_INS_VCVTPD2PSX = 750;;
let _X86_INS_VCVTPD2PS = 751;;
let _X86_INS_VCVTPD2UDQ = 752;;
let _X86_INS_VCVTPH2PS = 753;;
let _X86_INS_VCVTPS2DQ = 754;;
let _X86_INS_VCVTPS2PD = 755;;
let _X86_INS_VCVTPS2PH = 756;;
let _X86_INS_VCVTPS2UDQ = 757;;
let _X86_INS_VCVTSD2SI = 758;;
let _X86_INS_VCVTSD2USI = 759;;
let _X86_INS_VCVTSS2SI = 760;;
let _X86_INS_VCVTSS2USI = 761;;
let _X86_INS_VCVTTPD2DQX = 762;;
let _X86_INS_VCVTTPD2DQ = 763;;
let _X86_INS_VCVTTPD2UDQ = 764;;
let _X86_INS_VCVTTPS2DQ = 765;;
let _X86_INS_VCVTTPS2UDQ = 766;;
let _X86_INS_VCVTUDQ2PD = 767;;
let _X86_INS_VCVTUDQ2PS = 768;;
let _X86_INS_VDIVPD = 769;;
let _X86_INS_VDIVPS = 770;;
let _X86_INS_VDIVSD = 771;;
let _X86_INS_VDIVSS = 772;;
let _X86_INS_VDPPD = 773;;
let _X86_INS_VDPPS = 774;;
let _X86_INS_VERR = 775;;
let _X86_INS_VERW = 776;;
let _X86_INS_VEXP2PD = 777;;
let _X86_INS_VEXP2PS = 778;;
let _X86_INS_VEXPANDPD = 779;;
let _X86_INS_VEXPANDPS = 780;;
let _X86_INS_VEXTRACTF128 = 781;;
let _X86_INS_VEXTRACTF32X4 = 782;;
let _X86_INS_VEXTRACTF64X4 = 783;;
let _X86_INS_VEXTRACTI128 = 784;;
let _X86_INS_VEXTRACTI32X4 = 785;;
let _X86_INS_VEXTRACTI64X4 = 786;;
let _X86_INS_VEXTRACTPS = 787;;
let _X86_INS_VFMADD132PD = 788;;
let _X86_INS_VFMADD132PS = 789;;
let _X86_INS_VFMADDPD = 790;;
let _X86_INS_VFMADD213PD = 791;;
let _X86_INS_VFMADD231PD = 792;;
let _X86_INS_VFMADDPS = 793;;
let _X86_INS_VFMADD213PS = 794;;
let _X86_INS_VFMADD231PS = 795;;
let _X86_INS_VFMADDSD = 796;;
let _X86_INS_VFMADD213SD = 797;;
let _X86_INS_VFMADD132SD = 798;;
let _X86_INS_VFMADD231SD = 799;;
let _X86_INS_VFMADDSS = 800;;
let _X86_INS_VFMADD213SS = 801;;
let _X86_INS_VFMADD132SS = 802;;
let _X86_INS_VFMADD231SS = 803;;
let _X86_INS_VFMADDSUB132PD = 804;;
let _X86_INS_VFMADDSUB132PS = 805;;
let _X86_INS_VFMADDSUBPD = 806;;
let _X86_INS_VFMADDSUB213PD = 807;;
let _X86_INS_VFMADDSUB231PD = 808;;
let _X86_INS_VFMADDSUBPS = 809;;
let _X86_INS_VFMADDSUB213PS = 810;;
let _X86_INS_VFMADDSUB231PS = 811;;
let _X86_INS_VFMSUB132PD = 812;;
let _X86_INS_VFMSUB132PS = 813;;
let _X86_INS_VFMSUBADD132PD = 814;;
let _X86_INS_VFMSUBADD132PS = 815;;
let _X86_INS_VFMSUBADDPD = 816;;
let _X86_INS_VFMSUBADD213PD = 817;;
let _X86_INS_VFMSUBADD231PD = 818;;
let _X86_INS_VFMSUBADDPS = 819;;
let _X86_INS_VFMSUBADD213PS = 820;;
let _X86_INS_VFMSUBADD231PS = 821;;
let _X86_INS_VFMSUBPD = 822;;
let _X86_INS_VFMSUB213PD = 823;;
let _X86_INS_VFMSUB231PD = 824;;
let _X86_INS_VFMSUBPS = 825;;
let _X86_INS_VFMSUB213PS = 826;;
let _X86_INS_VFMSUB231PS = 827;;
let _X86_INS_VFMSUBSD = 828;;
let _X86_INS_VFMSUB213SD = 829;;
let _X86_INS_VFMSUB132SD = 830;;
let _X86_INS_VFMSUB231SD = 831;;
let _X86_INS_VFMSUBSS = 832;;
let _X86_INS_VFMSUB213SS = 833;;
let _X86_INS_VFMSUB132SS = 834;;
let _X86_INS_VFMSUB231SS = 835;;
let _X86_INS_VFNMADD132PD = 836;;
let _X86_INS_VFNMADD132PS = 837;;
let _X86_INS_VFNMADDPD = 838;;
let _X86_INS_VFNMADD213PD = 839;;
let _X86_INS_VFNMADD231PD = 840;;
let _X86_INS_VFNMADDPS = 841;;
let _X86_INS_VFNMADD213PS = 842;;
let _X86_INS_VFNMADD231PS = 843;;
let _X86_INS_VFNMADDSD = 844;;
let _X86_INS_VFNMADD213SD = 845;;
let _X86_INS_VFNMADD132SD = 846;;
let _X86_INS_VFNMADD231SD = 847;;
let _X86_INS_VFNMADDSS = 848;;
let _X86_INS_VFNMADD213SS = 849;;
let _X86_INS_VFNMADD132SS = 850;;
let _X86_INS_VFNMADD231SS = 851;;
let _X86_INS_VFNMSUB132PD = 852;;
let _X86_INS_VFNMSUB132PS = 853;;
let _X86_INS_VFNMSUBPD = 854;;
let _X86_INS_VFNMSUB213PD = 855;;
let _X86_INS_VFNMSUB231PD = 856;;
let _X86_INS_VFNMSUBPS = 857;;
let _X86_INS_VFNMSUB213PS = 858;;
let _X86_INS_VFNMSUB231PS = 859;;
let _X86_INS_VFNMSUBSD = 860;;
let _X86_INS_VFNMSUB213SD = 861;;
let _X86_INS_VFNMSUB132SD = 862;;
let _X86_INS_VFNMSUB231SD = 863;;
let _X86_INS_VFNMSUBSS = 864;;
let _X86_INS_VFNMSUB213SS = 865;;
let _X86_INS_VFNMSUB132SS = 866;;
let _X86_INS_VFNMSUB231SS = 867;;
let _X86_INS_VFRCZPD = 868;;
let _X86_INS_VFRCZPS = 869;;
let _X86_INS_VFRCZSD = 870;;
let _X86_INS_VFRCZSS = 871;;
let _X86_INS_VORPD = 872;;
let _X86_INS_VORPS = 873;;
let _X86_INS_VXORPD = 874;;
let _X86_INS_VXORPS = 875;;
let _X86_INS_VGATHERDPD = 876;;
let _X86_INS_VGATHERDPS = 877;;
let _X86_INS_VGATHERPF0DPD = 878;;
let _X86_INS_VGATHERPF0DPS = 879;;
let _X86_INS_VGATHERPF0QPD = 880;;
let _X86_INS_VGATHERPF0QPS = 881;;
let _X86_INS_VGATHERPF1DPD = 882;;
let _X86_INS_VGATHERPF1DPS = 883;;
let _X86_INS_VGATHERPF1QPD = 884;;
let _X86_INS_VGATHERPF1QPS = 885;;
let _X86_INS_VGATHERQPD = 886;;
let _X86_INS_VGATHERQPS = 887;;
let _X86_INS_VHADDPD = 888;;
let _X86_INS_VHADDPS = 889;;
let _X86_INS_VHSUBPD = 890;;
let _X86_INS_VHSUBPS = 891;;
let _X86_INS_VINSERTF128 = 892;;
let _X86_INS_VINSERTF32X4 = 893;;
let _X86_INS_VINSERTF32X8 = 894;;
let _X86_INS_VINSERTF64X2 = 895;;
let _X86_INS_VINSERTF64X4 = 896;;
let _X86_INS_VINSERTI128 = 897;;
let _X86_INS_VINSERTI32X4 = 898;;
let _X86_INS_VINSERTI32X8 = 899;;
let _X86_INS_VINSERTI64X2 = 900;;
let _X86_INS_VINSERTI64X4 = 901;;
let _X86_INS_VINSERTPS = 902;;
let _X86_INS_VLDDQU = 903;;
let _X86_INS_VLDMXCSR = 904;;
let _X86_INS_VMASKMOVDQU = 905;;
let _X86_INS_VMASKMOVPD = 906;;
let _X86_INS_VMASKMOVPS = 907;;
let _X86_INS_VMAXPD = 908;;
let _X86_INS_VMAXPS = 909;;
let _X86_INS_VMAXSD = 910;;
let _X86_INS_VMAXSS = 911;;
let _X86_INS_VMCALL = 912;;
let _X86_INS_VMCLEAR = 913;;
let _X86_INS_VMFUNC = 914;;
let _X86_INS_VMINPD = 915;;
let _X86_INS_VMINPS = 916;;
let _X86_INS_VMINSD = 917;;
let _X86_INS_VMINSS = 918;;
let _X86_INS_VMLAUNCH = 919;;
let _X86_INS_VMLOAD = 920;;
let _X86_INS_VMMCALL = 921;;
let _X86_INS_VMOVQ = 922;;
let _X86_INS_VMOVDDUP = 923;;
let _X86_INS_VMOVD = 924;;
let _X86_INS_VMOVDQA32 = 925;;
let _X86_INS_VMOVDQA64 = 926;;
let _X86_INS_VMOVDQA = 927;;
let _X86_INS_VMOVDQU16 = 928;;
let _X86_INS_VMOVDQU32 = 929;;
let _X86_INS_VMOVDQU64 = 930;;
let _X86_INS_VMOVDQU8 = 931;;
let _X86_INS_VMOVDQU = 932;;
let _X86_INS_VMOVHLPS = 933;;
let _X86_INS_VMOVHPD = 934;;
let _X86_INS_VMOVHPS = 935;;
let _X86_INS_VMOVLHPS = 936;;
let _X86_INS_VMOVLPD = 937;;
let _X86_INS_VMOVLPS = 938;;
let _X86_INS_VMOVMSKPD = 939;;
let _X86_INS_VMOVMSKPS = 940;;
let _X86_INS_VMOVNTDQA = 941;;
let _X86_INS_VMOVNTDQ = 942;;
let _X86_INS_VMOVNTPD = 943;;
let _X86_INS_VMOVNTPS = 944;;
let _X86_INS_VMOVSD = 945;;
let _X86_INS_VMOVSHDUP = 946;;
let _X86_INS_VMOVSLDUP = 947;;
let _X86_INS_VMOVSS = 948;;
let _X86_INS_VMOVUPD = 949;;
let _X86_INS_VMOVUPS = 950;;
let _X86_INS_VMPSADBW = 951;;
let _X86_INS_VMPTRLD = 952;;
let _X86_INS_VMPTRST = 953;;
let _X86_INS_VMREAD = 954;;
let _X86_INS_VMRESUME = 955;;
let _X86_INS_VMRUN = 956;;
let _X86_INS_VMSAVE = 957;;
let _X86_INS_VMULPD = 958;;
let _X86_INS_VMULPS = 959;;
let _X86_INS_VMULSD = 960;;
let _X86_INS_VMULSS = 961;;
let _X86_INS_VMWRITE = 962;;
let _X86_INS_VMXOFF = 963;;
let _X86_INS_VMXON = 964;;
let _X86_INS_VPABSB = 965;;
let _X86_INS_VPABSD = 966;;
let _X86_INS_VPABSQ = 967;;
let _X86_INS_VPABSW = 968;;
let _X86_INS_VPACKSSDW = 969;;
let _X86_INS_VPACKSSWB = 970;;
let _X86_INS_VPACKUSDW = 971;;
let _X86_INS_VPACKUSWB = 972;;
let _X86_INS_VPADDB = 973;;
let _X86_INS_VPADDD = 974;;
let _X86_INS_VPADDQ = 975;;
let _X86_INS_VPADDSB = 976;;
let _X86_INS_VPADDSW = 977;;
let _X86_INS_VPADDUSB = 978;;
let _X86_INS_VPADDUSW = 979;;
let _X86_INS_VPADDW = 980;;
let _X86_INS_VPALIGNR = 981;;
let _X86_INS_VPANDD = 982;;
let _X86_INS_VPANDND = 983;;
let _X86_INS_VPANDNQ = 984;;
let _X86_INS_VPANDN = 985;;
let _X86_INS_VPANDQ = 986;;
let _X86_INS_VPAND = 987;;
let _X86_INS_VPAVGB = 988;;
let _X86_INS_VPAVGW = 989;;
let _X86_INS_VPBLENDD = 990;;
let _X86_INS_VPBLENDMB = 991;;
let _X86_INS_VPBLENDMD = 992;;
let _X86_INS_VPBLENDMQ = 993;;
let _X86_INS_VPBLENDMW = 994;;
let _X86_INS_VPBLENDVB = 995;;
let _X86_INS_VPBLENDW = 996;;
let _X86_INS_VPBROADCASTB = 997;;
let _X86_INS_VPBROADCASTD = 998;;
let _X86_INS_VPBROADCASTMB2Q = 999;;
let _X86_INS_VPBROADCASTMW2D = 1000;;
let _X86_INS_VPBROADCASTQ = 1001;;
let _X86_INS_VPBROADCASTW = 1002;;
let _X86_INS_VPCLMULQDQ = 1003;;
let _X86_INS_VPCMOV = 1004;;
let _X86_INS_VPCMPB = 1005;;
let _X86_INS_VPCMPD = 1006;;
let _X86_INS_VPCMPEQB = 1007;;
let _X86_INS_VPCMPEQD = 1008;;
let _X86_INS_VPCMPEQQ = 1009;;
let _X86_INS_VPCMPEQW = 1010;;
let _X86_INS_VPCMPESTRI = 1011;;
let _X86_INS_VPCMPESTRM = 1012;;
let _X86_INS_VPCMPGTB = 1013;;
let _X86_INS_VPCMPGTD = 1014;;
let _X86_INS_VPCMPGTQ = 1015;;
let _X86_INS_VPCMPGTW = 1016;;
let _X86_INS_VPCMPISTRI = 1017;;
let _X86_INS_VPCMPISTRM = 1018;;
let _X86_INS_VPCMPQ = 1019;;
let _X86_INS_VPCMPUB = 1020;;
let _X86_INS_VPCMPUD = 1021;;
let _X86_INS_VPCMPUQ = 1022;;
let _X86_INS_VPCMPUW = 1023;;
let _X86_INS_VPCMPW = 1024;;
let _X86_INS_VPCOMB = 1025;;
let _X86_INS_VPCOMD = 1026;;
let _X86_INS_VPCOMPRESSD = 1027;;
let _X86_INS_VPCOMPRESSQ = 1028;;
let _X86_INS_VPCOMQ = 1029;;
let _X86_INS_VPCOMUB = 1030;;
let _X86_INS_VPCOMUD = 1031;;
let _X86_INS_VPCOMUQ = 1032;;
let _X86_INS_VPCOMUW = 1033;;
let _X86_INS_VPCOMW = 1034;;
let _X86_INS_VPCONFLICTD = 1035;;
let _X86_INS_VPCONFLICTQ = 1036;;
let _X86_INS_VPERM2F128 = 1037;;
let _X86_INS_VPERM2I128 = 1038;;
let _X86_INS_VPERMD = 1039;;
let _X86_INS_VPERMI2D = 1040;;
let _X86_INS_VPERMI2PD = 1041;;
let _X86_INS_VPERMI2PS = 1042;;
let _X86_INS_VPERMI2Q = 1043;;
let _X86_INS_VPERMIL2PD = 1044;;
let _X86_INS_VPERMIL2PS = 1045;;
let _X86_INS_VPERMILPD = 1046;;
let _X86_INS_VPERMILPS = 1047;;
let _X86_INS_VPERMPD = 1048;;
let _X86_INS_VPERMPS = 1049;;
let _X86_INS_VPERMQ = 1050;;
let _X86_INS_VPERMT2D = 1051;;
let _X86_INS_VPERMT2PD = 1052;;
let _X86_INS_VPERMT2PS = 1053;;
let _X86_INS_VPERMT2Q = 1054;;
let _X86_INS_VPEXPANDD = 1055;;
let _X86_INS_VPEXPANDQ = 1056;;
let _X86_INS_VPEXTRB = 1057;;
let _X86_INS_VPEXTRD = 1058;;
let _X86_INS_VPEXTRQ = 1059;;
let _X86_INS_VPEXTRW = 1060;;
let _X86_INS_VPGATHERDD = 1061;;
let _X86_INS_VPGATHERDQ = 1062;;
let _X86_INS_VPGATHERQD = 1063;;
let _X86_INS_VPGATHERQQ = 1064;;
let _X86_INS_VPHADDBD = 1065;;
let _X86_INS_VPHADDBQ = 1066;;
let _X86_INS_VPHADDBW = 1067;;
let _X86_INS_VPHADDDQ = 1068;;
let _X86_INS_VPHADDD = 1069;;
let _X86_INS_VPHADDSW = 1070;;
let _X86_INS_VPHADDUBD = 1071;;
let _X86_INS_VPHADDUBQ = 1072;;
let _X86_INS_VPHADDUBW = 1073;;
let _X86_INS_VPHADDUDQ = 1074;;
let _X86_INS_VPHADDUWD = 1075;;
let _X86_INS_VPHADDUWQ = 1076;;
let _X86_INS_VPHADDWD = 1077;;
let _X86_INS_VPHADDWQ = 1078;;
let _X86_INS_VPHADDW = 1079;;
let _X86_INS_VPHMINPOSUW = 1080;;
let _X86_INS_VPHSUBBW = 1081;;
let _X86_INS_VPHSUBDQ = 1082;;
let _X86_INS_VPHSUBD = 1083;;
let _X86_INS_VPHSUBSW = 1084;;
let _X86_INS_VPHSUBWD = 1085;;
let _X86_INS_VPHSUBW = 1086;;
let _X86_INS_VPINSRB = 1087;;
let _X86_INS_VPINSRD = 1088;;
let _X86_INS_VPINSRQ = 1089;;
let _X86_INS_VPINSRW = 1090;;
let _X86_INS_VPLZCNTD = 1091;;
let _X86_INS_VPLZCNTQ = 1092;;
let _X86_INS_VPMACSDD = 1093;;
let _X86_INS_VPMACSDQH = 1094;;
let _X86_INS_VPMACSDQL = 1095;;
let _X86_INS_VPMACSSDD = 1096;;
let _X86_INS_VPMACSSDQH = 1097;;
let _X86_INS_VPMACSSDQL = 1098;;
let _X86_INS_VPMACSSWD = 1099;;
let _X86_INS_VPMACSSWW = 1100;;
let _X86_INS_VPMACSWD = 1101;;
let _X86_INS_VPMACSWW = 1102;;
let _X86_INS_VPMADCSSWD = 1103;;
let _X86_INS_VPMADCSWD = 1104;;
let _X86_INS_VPMADDUBSW = 1105;;
let _X86_INS_VPMADDWD = 1106;;
let _X86_INS_VPMASKMOVD = 1107;;
let _X86_INS_VPMASKMOVQ = 1108;;
let _X86_INS_VPMAXSB = 1109;;
let _X86_INS_VPMAXSD = 1110;;
let _X86_INS_VPMAXSQ = 1111;;
let _X86_INS_VPMAXSW = 1112;;
let _X86_INS_VPMAXUB = 1113;;
let _X86_INS_VPMAXUD = 1114;;
let _X86_INS_VPMAXUQ = 1115;;
let _X86_INS_VPMAXUW = 1116;;
let _X86_INS_VPMINSB = 1117;;
let _X86_INS_VPMINSD = 1118;;
let _X86_INS_VPMINSQ = 1119;;
let _X86_INS_VPMINSW = 1120;;
let _X86_INS_VPMINUB = 1121;;
let _X86_INS_VPMINUD = 1122;;
let _X86_INS_VPMINUQ = 1123;;
let _X86_INS_VPMINUW = 1124;;
let _X86_INS_VPMOVDB = 1125;;
let _X86_INS_VPMOVDW = 1126;;
let _X86_INS_VPMOVM2B = 1127;;
let _X86_INS_VPMOVM2D = 1128;;
let _X86_INS_VPMOVM2Q = 1129;;
let _X86_INS_VPMOVM2W = 1130;;
let _X86_INS_VPMOVMSKB = 1131;;
let _X86_INS_VPMOVQB = 1132;;
let _X86_INS_VPMOVQD = 1133;;
let _X86_INS_VPMOVQW = 1134;;
let _X86_INS_VPMOVSDB = 1135;;
let _X86_INS_VPMOVSDW = 1136;;
let _X86_INS_VPMOVSQB = 1137;;
let _X86_INS_VPMOVSQD = 1138;;
let _X86_INS_VPMOVSQW = 1139;;
let _X86_INS_VPMOVSXBD = 1140;;
let _X86_INS_VPMOVSXBQ = 1141;;
let _X86_INS_VPMOVSXBW = 1142;;
let _X86_INS_VPMOVSXDQ = 1143;;
let _X86_INS_VPMOVSXWD = 1144;;
let _X86_INS_VPMOVSXWQ = 1145;;
let _X86_INS_VPMOVUSDB = 1146;;
let _X86_INS_VPMOVUSDW = 1147;;
let _X86_INS_VPMOVUSQB = 1148;;
let _X86_INS_VPMOVUSQD = 1149;;
let _X86_INS_VPMOVUSQW = 1150;;
let _X86_INS_VPMOVZXBD = 1151;;
let _X86_INS_VPMOVZXBQ = 1152;;
let _X86_INS_VPMOVZXBW = 1153;;
let _X86_INS_VPMOVZXDQ = 1154;;
let _X86_INS_VPMOVZXWD = 1155;;
let _X86_INS_VPMOVZXWQ = 1156;;
let _X86_INS_VPMULDQ = 1157;;
let _X86_INS_VPMULHRSW = 1158;;
let _X86_INS_VPMULHUW = 1159;;
let _X86_INS_VPMULHW = 1160;;
let _X86_INS_VPMULLD = 1161;;
let _X86_INS_VPMULLQ = 1162;;
let _X86_INS_VPMULLW = 1163;;
let _X86_INS_VPMULUDQ = 1164;;
let _X86_INS_VPORD = 1165;;
let _X86_INS_VPORQ = 1166;;
let _X86_INS_VPOR = 1167;;
let _X86_INS_VPPERM = 1168;;
let _X86_INS_VPROTB = 1169;;
let _X86_INS_VPROTD = 1170;;
let _X86_INS_VPROTQ = 1171;;
let _X86_INS_VPROTW = 1172;;
let _X86_INS_VPSADBW = 1173;;
let _X86_INS_VPSCATTERDD = 1174;;
let _X86_INS_VPSCATTERDQ = 1175;;
let _X86_INS_VPSCATTERQD = 1176;;
let _X86_INS_VPSCATTERQQ = 1177;;
let _X86_INS_VPSHAB = 1178;;
let _X86_INS_VPSHAD = 1179;;
let _X86_INS_VPSHAQ = 1180;;
let _X86_INS_VPSHAW = 1181;;
let _X86_INS_VPSHLB = 1182;;
let _X86_INS_VPSHLD = 1183;;
let _X86_INS_VPSHLQ = 1184;;
let _X86_INS_VPSHLW = 1185;;
let _X86_INS_VPSHUFB = 1186;;
let _X86_INS_VPSHUFD = 1187;;
let _X86_INS_VPSHUFHW = 1188;;
let _X86_INS_VPSHUFLW = 1189;;
let _X86_INS_VPSIGNB = 1190;;
let _X86_INS_VPSIGND = 1191;;
let _X86_INS_VPSIGNW = 1192;;
let _X86_INS_VPSLLDQ = 1193;;
let _X86_INS_VPSLLD = 1194;;
let _X86_INS_VPSLLQ = 1195;;
let _X86_INS_VPSLLVD = 1196;;
let _X86_INS_VPSLLVQ = 1197;;
let _X86_INS_VPSLLW = 1198;;
let _X86_INS_VPSRAD = 1199;;
let _X86_INS_VPSRAQ = 1200;;
let _X86_INS_VPSRAVD = 1201;;
let _X86_INS_VPSRAVQ = 1202;;
let _X86_INS_VPSRAW = 1203;;
let _X86_INS_VPSRLDQ = 1204;;
let _X86_INS_VPSRLD = 1205;;
let _X86_INS_VPSRLQ = 1206;;
let _X86_INS_VPSRLVD = 1207;;
let _X86_INS_VPSRLVQ = 1208;;
let _X86_INS_VPSRLW = 1209;;
let _X86_INS_VPSUBB = 1210;;
let _X86_INS_VPSUBD = 1211;;
let _X86_INS_VPSUBQ = 1212;;
let _X86_INS_VPSUBSB = 1213;;
let _X86_INS_VPSUBSW = 1214;;
let _X86_INS_VPSUBUSB = 1215;;
let _X86_INS_VPSUBUSW = 1216;;
let _X86_INS_VPSUBW = 1217;;
let _X86_INS_VPTESTMD = 1218;;
let _X86_INS_VPTESTMQ = 1219;;
let _X86_INS_VPTESTNMD = 1220;;
let _X86_INS_VPTESTNMQ = 1221;;
let _X86_INS_VPTEST = 1222;;
let _X86_INS_VPUNPCKHBW = 1223;;
let _X86_INS_VPUNPCKHDQ = 1224;;
let _X86_INS_VPUNPCKHQDQ = 1225;;
let _X86_INS_VPUNPCKHWD = 1226;;
let _X86_INS_VPUNPCKLBW = 1227;;
let _X86_INS_VPUNPCKLDQ = 1228;;
let _X86_INS_VPUNPCKLQDQ = 1229;;
let _X86_INS_VPUNPCKLWD = 1230;;
let _X86_INS_VPXORD = 1231;;
let _X86_INS_VPXORQ = 1232;;
let _X86_INS_VPXOR = 1233;;
let _X86_INS_VRCP14PD = 1234;;
let _X86_INS_VRCP14PS = 1235;;
let _X86_INS_VRCP14SD = 1236;;
let _X86_INS_VRCP14SS = 1237;;
let _X86_INS_VRCP28PD = 1238;;
let _X86_INS_VRCP28PS = 1239;;
let _X86_INS_VRCP28SD = 1240;;
let _X86_INS_VRCP28SS = 1241;;
let _X86_INS_VRCPPS = 1242;;
let _X86_INS_VRCPSS = 1243;;
let _X86_INS_VRNDSCALEPD = 1244;;
let _X86_INS_VRNDSCALEPS = 1245;;
let _X86_INS_VRNDSCALESD = 1246;;
let _X86_INS_VRNDSCALESS = 1247;;
let _X86_INS_VROUNDPD = 1248;;
let _X86_INS_VROUNDPS = 1249;;
let _X86_INS_VROUNDSD = 1250;;
let _X86_INS_VROUNDSS = 1251;;
let _X86_INS_VRSQRT14PD = 1252;;
let _X86_INS_VRSQRT14PS = 1253;;
let _X86_INS_VRSQRT14SD = 1254;;
let _X86_INS_VRSQRT14SS = 1255;;
let _X86_INS_VRSQRT28PD = 1256;;
let _X86_INS_VRSQRT28PS = 1257;;
let _X86_INS_VRSQRT28SD = 1258;;
let _X86_INS_VRSQRT28SS = 1259;;
let _X86_INS_VRSQRTPS = 1260;;
let _X86_INS_VRSQRTSS = 1261;;
let _X86_INS_VSCATTERDPD = 1262;;
let _X86_INS_VSCATTERDPS = 1263;;
let _X86_INS_VSCATTERPF0DPD = 1264;;
let _X86_INS_VSCATTERPF0DPS = 1265;;
let _X86_INS_VSCATTERPF0QPD = 1266;;
let _X86_INS_VSCATTERPF0QPS = 1267;;
let _X86_INS_VSCATTERPF1DPD = 1268;;
let _X86_INS_VSCATTERPF1DPS = 1269;;
let _X86_INS_VSCATTERPF1QPD = 1270;;
let _X86_INS_VSCATTERPF1QPS = 1271;;
let _X86_INS_VSCATTERQPD = 1272;;
let _X86_INS_VSCATTERQPS = 1273;;
let _X86_INS_VSHUFPD = 1274;;
let _X86_INS_VSHUFPS = 1275;;
let _X86_INS_VSQRTPD = 1276;;
let _X86_INS_VSQRTPS = 1277;;
let _X86_INS_VSQRTSD = 1278;;
let _X86_INS_VSQRTSS = 1279;;
let _X86_INS_VSTMXCSR = 1280;;
let _X86_INS_VSUBPD = 1281;;
let _X86_INS_VSUBPS = 1282;;
let _X86_INS_VSUBSD = 1283;;
let _X86_INS_VSUBSS = 1284;;
let _X86_INS_VTESTPD = 1285;;
let _X86_INS_VTESTPS = 1286;;
let _X86_INS_VUNPCKHPD = 1287;;
let _X86_INS_VUNPCKHPS = 1288;;
let _X86_INS_VUNPCKLPD = 1289;;
let _X86_INS_VUNPCKLPS = 1290;;
let _X86_INS_VZEROALL = 1291;;
let _X86_INS_VZEROUPPER = 1292;;
let _X86_INS_WAIT = 1293;;
let _X86_INS_WBINVD = 1294;;
let _X86_INS_WRFSBASE = 1295;;
let _X86_INS_WRGSBASE = 1296;;
let _X86_INS_WRMSR = 1297;;
let _X86_INS_XABORT = 1298;;
let _X86_INS_XACQUIRE = 1299;;
let _X86_INS_XBEGIN = 1300;;
let _X86_INS_XCHG = 1301;;
let _X86_INS_XCRYPTCBC = 1302;;
let _X86_INS_XCRYPTCFB = 1303;;
let _X86_INS_XCRYPTCTR = 1304;;
let _X86_INS_XCRYPTECB = 1305;;
let _X86_INS_XCRYPTOFB = 1306;;
let _X86_INS_XEND = 1307;;
let _X86_INS_XGETBV = 1308;;
let _X86_INS_XLATB = 1309;;
let _X86_INS_XRELEASE = 1310;;
let _X86_INS_XRSTOR = 1311;;
let _X86_INS_XRSTOR64 = 1312;;
let _X86_INS_XRSTORS = 1313;;
let _X86_INS_XRSTORS64 = 1314;;
let _X86_INS_XSAVE = 1315;;
let _X86_INS_XSAVE64 = 1316;;
let _X86_INS_XSAVEC = 1317;;
let _X86_INS_XSAVEC64 = 1318;;
let _X86_INS_XSAVEOPT = 1319;;
let _X86_INS_XSAVEOPT64 = 1320;;
let _X86_INS_XSAVES = 1321;;
let _X86_INS_XSAVES64 = 1322;;
let _X86_INS_XSETBV = 1323;;
let _X86_INS_XSHA1 = 1324;;
let _X86_INS_XSHA256 = 1325;;
let _X86_INS_XSTORE = 1326;;
let _X86_INS_XTEST = 1327;;
let _X86_INS_FDISI8087_NOP = 1328;;
let _X86_INS_FENI8087_NOP = 1329;;
let _X86_INS_CMPSS = 1330;;
let _X86_INS_CMPEQSS = 1331;;
let _X86_INS_CMPLTSS = 1332;;
let _X86_INS_CMPLESS = 1333;;
let _X86_INS_CMPUNORDSS = 1334;;
let _X86_INS_CMPNEQSS = 1335;;
let _X86_INS_CMPNLTSS = 1336;;
let _X86_INS_CMPNLESS = 1337;;
let _X86_INS_CMPORDSS = 1338;;
let _X86_INS_CMPSD = 1339;;
let _X86_INS_CMPEQSD = 1340;;
let _X86_INS_CMPLTSD = 1341;;
let _X86_INS_CMPLESD = 1342;;
let _X86_INS_CMPUNORDSD = 1343;;
let _X86_INS_CMPNEQSD = 1344;;
let _X86_INS_CMPNLTSD = 1345;;
let _X86_INS_CMPNLESD = 1346;;
let _X86_INS_CMPORDSD = 1347;;
let _X86_INS_CMPPS = 1348;;
let _X86_INS_CMPEQPS = 1349;;
let _X86_INS_CMPLTPS = 1350;;
let _X86_INS_CMPLEPS = 1351;;
let _X86_INS_CMPUNORDPS = 1352;;
let _X86_INS_CMPNEQPS = 1353;;
let _X86_INS_CMPNLTPS = 1354;;
let _X86_INS_CMPNLEPS = 1355;;
let _X86_INS_CMPORDPS = 1356;;
let _X86_INS_CMPPD = 1357;;
let _X86_INS_CMPEQPD = 1358;;
let _X86_INS_CMPLTPD = 1359;;
let _X86_INS_CMPLEPD = 1360;;
let _X86_INS_CMPUNORDPD = 1361;;
let _X86_INS_CMPNEQPD = 1362;;
let _X86_INS_CMPNLTPD = 1363;;
let _X86_INS_CMPNLEPD = 1364;;
let _X86_INS_CMPORDPD = 1365;;
let _X86_INS_VCMPSS = 1366;;
let _X86_INS_VCMPEQSS = 1367;;
let _X86_INS_VCMPLTSS = 1368;;
let _X86_INS_VCMPLESS = 1369;;
let _X86_INS_VCMPUNORDSS = 1370;;
let _X86_INS_VCMPNEQSS = 1371;;
let _X86_INS_VCMPNLTSS = 1372;;
let _X86_INS_VCMPNLESS = 1373;;
let _X86_INS_VCMPORDSS = 1374;;
let _X86_INS_VCMPEQ_UQSS = 1375;;
let _X86_INS_VCMPNGESS = 1376;;
let _X86_INS_VCMPNGTSS = 1377;;
let _X86_INS_VCMPFALSESS = 1378;;
let _X86_INS_VCMPNEQ_OQSS = 1379;;
let _X86_INS_VCMPGESS = 1380;;
let _X86_INS_VCMPGTSS = 1381;;
let _X86_INS_VCMPTRUESS = 1382;;
let _X86_INS_VCMPEQ_OSSS = 1383;;
let _X86_INS_VCMPLT_OQSS = 1384;;
let _X86_INS_VCMPLE_OQSS = 1385;;
let _X86_INS_VCMPUNORD_SSS = 1386;;
let _X86_INS_VCMPNEQ_USSS = 1387;;
let _X86_INS_VCMPNLT_UQSS = 1388;;
let _X86_INS_VCMPNLE_UQSS = 1389;;
let _X86_INS_VCMPORD_SSS = 1390;;
let _X86_INS_VCMPEQ_USSS = 1391;;
let _X86_INS_VCMPNGE_UQSS = 1392;;
let _X86_INS_VCMPNGT_UQSS = 1393;;
let _X86_INS_VCMPFALSE_OSSS = 1394;;
let _X86_INS_VCMPNEQ_OSSS = 1395;;
let _X86_INS_VCMPGE_OQSS = 1396;;
let _X86_INS_VCMPGT_OQSS = 1397;;
let _X86_INS_VCMPTRUE_USSS = 1398;;
let _X86_INS_VCMPSD = 1399;;
let _X86_INS_VCMPEQSD = 1400;;
let _X86_INS_VCMPLTSD = 1401;;
let _X86_INS_VCMPLESD = 1402;;
let _X86_INS_VCMPUNORDSD = 1403;;
let _X86_INS_VCMPNEQSD = 1404;;
let _X86_INS_VCMPNLTSD = 1405;;
let _X86_INS_VCMPNLESD = 1406;;
let _X86_INS_VCMPORDSD = 1407;;
let _X86_INS_VCMPEQ_UQSD = 1408;;
let _X86_INS_VCMPNGESD = 1409;;
let _X86_INS_VCMPNGTSD = 1410;;
let _X86_INS_VCMPFALSESD = 1411;;
let _X86_INS_VCMPNEQ_OQSD = 1412;;
let _X86_INS_VCMPGESD = 1413;;
let _X86_INS_VCMPGTSD = 1414;;
let _X86_INS_VCMPTRUESD = 1415;;
let _X86_INS_VCMPEQ_OSSD = 1416;;
let _X86_INS_VCMPLT_OQSD = 1417;;
let _X86_INS_VCMPLE_OQSD = 1418;;
let _X86_INS_VCMPUNORD_SSD = 1419;;
let _X86_INS_VCMPNEQ_USSD = 1420;;
let _X86_INS_VCMPNLT_UQSD = 1421;;
let _X86_INS_VCMPNLE_UQSD = 1422;;
let _X86_INS_VCMPORD_SSD = 1423;;
let _X86_INS_VCMPEQ_USSD = 1424;;
let _X86_INS_VCMPNGE_UQSD = 1425;;
let _X86_INS_VCMPNGT_UQSD = 1426;;
let _X86_INS_VCMPFALSE_OSSD = 1427;;
let _X86_INS_VCMPNEQ_OSSD = 1428;;
let _X86_INS_VCMPGE_OQSD = 1429;;
let _X86_INS_VCMPGT_OQSD = 1430;;
let _X86_INS_VCMPTRUE_USSD = 1431;;
let _X86_INS_VCMPPS = 1432;;
let _X86_INS_VCMPEQPS = 1433;;
let _X86_INS_VCMPLTPS = 1434;;
let _X86_INS_VCMPLEPS = 1435;;
let _X86_INS_VCMPUNORDPS = 1436;;
let _X86_INS_VCMPNEQPS = 1437;;
let _X86_INS_VCMPNLTPS = 1438;;
let _X86_INS_VCMPNLEPS = 1439;;
let _X86_INS_VCMPORDPS = 1440;;
let _X86_INS_VCMPEQ_UQPS = 1441;;
let _X86_INS_VCMPNGEPS = 1442;;
let _X86_INS_VCMPNGTPS = 1443;;
let _X86_INS_VCMPFALSEPS = 1444;;
let _X86_INS_VCMPNEQ_OQPS = 1445;;
let _X86_INS_VCMPGEPS = 1446;;
let _X86_INS_VCMPGTPS = 1447;;
let _X86_INS_VCMPTRUEPS = 1448;;
let _X86_INS_VCMPEQ_OSPS = 1449;;
let _X86_INS_VCMPLT_OQPS = 1450;;
let _X86_INS_VCMPLE_OQPS = 1451;;
let _X86_INS_VCMPUNORD_SPS = 1452;;
let _X86_INS_VCMPNEQ_USPS = 1453;;
let _X86_INS_VCMPNLT_UQPS = 1454;;
let _X86_INS_VCMPNLE_UQPS = 1455;;
let _X86_INS_VCMPORD_SPS = 1456;;
let _X86_INS_VCMPEQ_USPS = 1457;;
let _X86_INS_VCMPNGE_UQPS = 1458;;
let _X86_INS_VCMPNGT_UQPS = 1459;;
let _X86_INS_VCMPFALSE_OSPS = 1460;;
let _X86_INS_VCMPNEQ_OSPS = 1461;;
let _X86_INS_VCMPGE_OQPS = 1462;;
let _X86_INS_VCMPGT_OQPS = 1463;;
let _X86_INS_VCMPTRUE_USPS = 1464;;
let _X86_INS_VCMPPD = 1465;;
let _X86_INS_VCMPEQPD = 1466;;
let _X86_INS_VCMPLTPD = 1467;;
let _X86_INS_VCMPLEPD = 1468;;
let _X86_INS_VCMPUNORDPD = 1469;;
let _X86_INS_VCMPNEQPD = 1470;;
let _X86_INS_VCMPNLTPD = 1471;;
let _X86_INS_VCMPNLEPD = 1472;;
let _X86_INS_VCMPORDPD = 1473;;
let _X86_INS_VCMPEQ_UQPD = 1474;;
let _X86_INS_VCMPNGEPD = 1475;;
let _X86_INS_VCMPNGTPD = 1476;;
let _X86_INS_VCMPFALSEPD = 1477;;
let _X86_INS_VCMPNEQ_OQPD = 1478;;
let _X86_INS_VCMPGEPD = 1479;;
let _X86_INS_VCMPGTPD = 1480;;
let _X86_INS_VCMPTRUEPD = 1481;;
let _X86_INS_VCMPEQ_OSPD = 1482;;
let _X86_INS_VCMPLT_OQPD = 1483;;
let _X86_INS_VCMPLE_OQPD = 1484;;
let _X86_INS_VCMPUNORD_SPD = 1485;;
let _X86_INS_VCMPNEQ_USPD = 1486;;
let _X86_INS_VCMPNLT_UQPD = 1487;;
let _X86_INS_VCMPNLE_UQPD = 1488;;
let _X86_INS_VCMPORD_SPD = 1489;;
let _X86_INS_VCMPEQ_USPD = 1490;;
let _X86_INS_VCMPNGE_UQPD = 1491;;
let _X86_INS_VCMPNGT_UQPD = 1492;;
let _X86_INS_VCMPFALSE_OSPD = 1493;;
let _X86_INS_VCMPNEQ_OSPD = 1494;;
let _X86_INS_VCMPGE_OQPD = 1495;;
let _X86_INS_VCMPGT_OQPD = 1496;;
let _X86_INS_VCMPTRUE_USPD = 1497;;
let _X86_INS_UD0 = 1498;;
let _X86_INS_ENDBR32 = 1499;;
let _X86_INS_ENDBR64 = 1500;;
let _X86_INS_ENDING = 1501;;

(* Group of X86 instructions *)

let _X86_GRP_INVALID = 0;;

(* Generic groups *)
let _X86_GRP_JUMP = 1;;
let _X86_GRP_CALL = 2;;
let _X86_GRP_RET = 3;;
let _X86_GRP_INT = 4;;
let _X86_GRP_IRET = 5;;
let _X86_GRP_PRIVILEGE = 6;;
let _X86_GRP_BRANCH_RELATIVE = 7;;

(* Architecture-specific groups *)
let _X86_GRP_VM = 128;;
let _X86_GRP_3DNOW = 129;;
let _X86_GRP_AES = 130;;
let _X86_GRP_ADX = 131;;
let _X86_GRP_AVX = 132;;
let _X86_GRP_AVX2 = 133;;
let _X86_GRP_AVX512 = 134;;
let _X86_GRP_BMI = 135;;
let _X86_GRP_BMI2 = 136;;
let _X86_GRP_CMOV = 137;;
let _X86_GRP_F16C = 138;;
let _X86_GRP_FMA = 139;;
let _X86_GRP_FMA4 = 140;;
let _X86_GRP_FSGSBASE = 141;;
let _X86_GRP_HLE = 142;;
let _X86_GRP_MMX = 143;;
let _X86_GRP_MODE32 = 144;;
let _X86_GRP_MODE64 = 145;;
let _X86_GRP_RTM = 146;;
let _X86_GRP_SHA = 147;;
let _X86_GRP_SSE1 = 148;;
let _X86_GRP_SSE2 = 149;;
let _X86_GRP_SSE3 = 150;;
let _X86_GRP_SSE41 = 151;;
let _X86_GRP_SSE42 = 152;;
let _X86_GRP_SSE4A = 153;;
let _X86_GRP_SSSE3 = 154;;
let _X86_GRP_PCLMUL = 155;;
let _X86_GRP_XOP = 156;;
let _X86_GRP_CDI = 157;;
let _X86_GRP_ERI = 158;;
let _X86_GRP_TBM = 159;;
let _X86_GRP_16BITMODE = 160;;
let _X86_GRP_NOT64BITMODE = 161;;
let _X86_GRP_SGX = 162;;
let _X86_GRP_DQI = 163;;
let _X86_GRP_BWI = 164;;
let _X86_GRP_PFI = 165;;
let _X86_GRP_VLX = 166;;
let _X86_GRP_SMAP = 167;;
let _X86_GRP_NOVLX = 168;;
let _X86_GRP_FPU = 169;;
let _X86_GRP_ENDING = 170;;
