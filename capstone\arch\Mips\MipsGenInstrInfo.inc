/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Target Instruction Enum Values                                              *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine, http://www.capstone-engine.org */
/* By <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2015 */


#ifdef GET_INSTRINFO_ENUM
#undef GET_INSTRINFO_ENUM

enum {
    Mips_PHI	= 0,
    Mips_INLINEASM	= 1,
    Mips_CFI_INSTRUCTION	= 2,
    Mips_EH_LABEL	= 3,
    Mips_GC_LABEL	= 4,
    Mips_KILL	= 5,
    Mips_EXTRACT_SUBREG	= 6,
    Mips_INSERT_SUBREG	= 7,
    Mips_IMPLICIT_DEF	= 8,
    Mips_SUBREG_TO_REG	= 9,
    Mips_COPY_TO_REGCLASS	= 10,
    Mips_DBG_VALUE	= 11,
    Mips_REG_SEQUENCE	= 12,
    Mips_COPY	= 13,
    Mips_BUNDLE	= 14,
    Mips_LIFETIME_START	= 15,
    Mips_LIFETIME_END	= 16,
    Mips_STACKMAP	= 17,
    Mips_PATCHPOINT	= 18,
    Mips_LOAD_STACK_GUARD	= 19,
    Mips_STATEPOINT	= 20,
    Mips_FRAME_ALLOC	= 21,
    Mips_ABSQ_S_PH	= 22,
    Mips_ABSQ_S_QB	= 23,
    Mips_ABSQ_S_W	= 24,
    Mips_ADD	= 25,
    Mips_ADDIUPC	= 26,
    Mips_ADDIUPC_MM	= 27,
    Mips_ADDIUR1SP_MM	= 28,
    Mips_ADDIUR2_MM	= 29,
    Mips_ADDIUS5_MM	= 30,
    Mips_ADDIUSP_MM	= 31,
    Mips_ADDQH_PH	= 32,
    Mips_ADDQH_R_PH	= 33,
    Mips_ADDQH_R_W	= 34,
    Mips_ADDQH_W	= 35,
    Mips_ADDQ_PH	= 36,
    Mips_ADDQ_S_PH	= 37,
    Mips_ADDQ_S_W	= 38,
    Mips_ADDSC	= 39,
    Mips_ADDS_A_B	= 40,
    Mips_ADDS_A_D	= 41,
    Mips_ADDS_A_H	= 42,
    Mips_ADDS_A_W	= 43,
    Mips_ADDS_S_B	= 44,
    Mips_ADDS_S_D	= 45,
    Mips_ADDS_S_H	= 46,
    Mips_ADDS_S_W	= 47,
    Mips_ADDS_U_B	= 48,
    Mips_ADDS_U_D	= 49,
    Mips_ADDS_U_H	= 50,
    Mips_ADDS_U_W	= 51,
    Mips_ADDU16_MM	= 52,
    Mips_ADDUH_QB	= 53,
    Mips_ADDUH_R_QB	= 54,
    Mips_ADDU_PH	= 55,
    Mips_ADDU_QB	= 56,
    Mips_ADDU_S_PH	= 57,
    Mips_ADDU_S_QB	= 58,
    Mips_ADDVI_B	= 59,
    Mips_ADDVI_D	= 60,
    Mips_ADDVI_H	= 61,
    Mips_ADDVI_W	= 62,
    Mips_ADDV_B	= 63,
    Mips_ADDV_D	= 64,
    Mips_ADDV_H	= 65,
    Mips_ADDV_W	= 66,
    Mips_ADDWC	= 67,
    Mips_ADD_A_B	= 68,
    Mips_ADD_A_D	= 69,
    Mips_ADD_A_H	= 70,
    Mips_ADD_A_W	= 71,
    Mips_ADD_MM	= 72,
    Mips_ADDi	= 73,
    Mips_ADDi_MM	= 74,
    Mips_ADDiu	= 75,
    Mips_ADDiu_MM	= 76,
    Mips_ADDu	= 77,
    Mips_ADDu_MM	= 78,
    Mips_ADJCALLSTACKDOWN	= 79,
    Mips_ADJCALLSTACKUP	= 80,
    Mips_ALIGN	= 81,
    Mips_ALUIPC	= 82,
    Mips_AND	= 83,
    Mips_AND16_MM	= 84,
    Mips_AND64	= 85,
    Mips_ANDI16_MM	= 86,
    Mips_ANDI_B	= 87,
    Mips_AND_MM	= 88,
    Mips_AND_V	= 89,
    Mips_AND_V_D_PSEUDO	= 90,
    Mips_AND_V_H_PSEUDO	= 91,
    Mips_AND_V_W_PSEUDO	= 92,
    Mips_ANDi	= 93,
    Mips_ANDi64	= 94,
    Mips_ANDi_MM	= 95,
    Mips_APPEND	= 96,
    Mips_ASUB_S_B	= 97,
    Mips_ASUB_S_D	= 98,
    Mips_ASUB_S_H	= 99,
    Mips_ASUB_S_W	= 100,
    Mips_ASUB_U_B	= 101,
    Mips_ASUB_U_D	= 102,
    Mips_ASUB_U_H	= 103,
    Mips_ASUB_U_W	= 104,
    Mips_ATOMIC_CMP_SWAP_I16	= 105,
    Mips_ATOMIC_CMP_SWAP_I32	= 106,
    Mips_ATOMIC_CMP_SWAP_I64	= 107,
    Mips_ATOMIC_CMP_SWAP_I8	= 108,
    Mips_ATOMIC_LOAD_ADD_I16	= 109,
    Mips_ATOMIC_LOAD_ADD_I32	= 110,
    Mips_ATOMIC_LOAD_ADD_I64	= 111,
    Mips_ATOMIC_LOAD_ADD_I8	= 112,
    Mips_ATOMIC_LOAD_AND_I16	= 113,
    Mips_ATOMIC_LOAD_AND_I32	= 114,
    Mips_ATOMIC_LOAD_AND_I64	= 115,
    Mips_ATOMIC_LOAD_AND_I8	= 116,
    Mips_ATOMIC_LOAD_NAND_I16	= 117,
    Mips_ATOMIC_LOAD_NAND_I32	= 118,
    Mips_ATOMIC_LOAD_NAND_I64	= 119,
    Mips_ATOMIC_LOAD_NAND_I8	= 120,
    Mips_ATOMIC_LOAD_OR_I16	= 121,
    Mips_ATOMIC_LOAD_OR_I32	= 122,
    Mips_ATOMIC_LOAD_OR_I64	= 123,
    Mips_ATOMIC_LOAD_OR_I8	= 124,
    Mips_ATOMIC_LOAD_SUB_I16	= 125,
    Mips_ATOMIC_LOAD_SUB_I32	= 126,
    Mips_ATOMIC_LOAD_SUB_I64	= 127,
    Mips_ATOMIC_LOAD_SUB_I8	= 128,
    Mips_ATOMIC_LOAD_XOR_I16	= 129,
    Mips_ATOMIC_LOAD_XOR_I32	= 130,
    Mips_ATOMIC_LOAD_XOR_I64	= 131,
    Mips_ATOMIC_LOAD_XOR_I8	= 132,
    Mips_ATOMIC_SWAP_I16	= 133,
    Mips_ATOMIC_SWAP_I32	= 134,
    Mips_ATOMIC_SWAP_I64	= 135,
    Mips_ATOMIC_SWAP_I8	= 136,
    Mips_AUI	= 137,
    Mips_AUIPC	= 138,
    Mips_AVER_S_B	= 139,
    Mips_AVER_S_D	= 140,
    Mips_AVER_S_H	= 141,
    Mips_AVER_S_W	= 142,
    Mips_AVER_U_B	= 143,
    Mips_AVER_U_D	= 144,
    Mips_AVER_U_H	= 145,
    Mips_AVER_U_W	= 146,
    Mips_AVE_S_B	= 147,
    Mips_AVE_S_D	= 148,
    Mips_AVE_S_H	= 149,
    Mips_AVE_S_W	= 150,
    Mips_AVE_U_B	= 151,
    Mips_AVE_U_D	= 152,
    Mips_AVE_U_H	= 153,
    Mips_AVE_U_W	= 154,
    Mips_AddiuRxImmX16	= 155,
    Mips_AddiuRxPcImmX16	= 156,
    Mips_AddiuRxRxImm16	= 157,
    Mips_AddiuRxRxImmX16	= 158,
    Mips_AddiuRxRyOffMemX16	= 159,
    Mips_AddiuSpImm16	= 160,
    Mips_AddiuSpImmX16	= 161,
    Mips_AdduRxRyRz16	= 162,
    Mips_AndRxRxRy16	= 163,
    Mips_B	= 164,
    Mips_B16_MM	= 165,
    Mips_BADDu	= 166,
    Mips_BAL	= 167,
    Mips_BALC	= 168,
    Mips_BALIGN	= 169,
    Mips_BAL_BR	= 170,
    Mips_BBIT0	= 171,
    Mips_BBIT032	= 172,
    Mips_BBIT1	= 173,
    Mips_BBIT132	= 174,
    Mips_BC	= 175,
    Mips_BC0F	= 176,
    Mips_BC0FL	= 177,
    Mips_BC0T	= 178,
    Mips_BC0TL	= 179,
    Mips_BC1EQZ	= 180,
    Mips_BC1F	= 181,
    Mips_BC1FL	= 182,
    Mips_BC1F_MM	= 183,
    Mips_BC1NEZ	= 184,
    Mips_BC1T	= 185,
    Mips_BC1TL	= 186,
    Mips_BC1T_MM	= 187,
    Mips_BC2EQZ	= 188,
    Mips_BC2F	= 189,
    Mips_BC2FL	= 190,
    Mips_BC2NEZ	= 191,
    Mips_BC2T	= 192,
    Mips_BC2TL	= 193,
    Mips_BC3F	= 194,
    Mips_BC3FL	= 195,
    Mips_BC3T	= 196,
    Mips_BC3TL	= 197,
    Mips_BCLRI_B	= 198,
    Mips_BCLRI_D	= 199,
    Mips_BCLRI_H	= 200,
    Mips_BCLRI_W	= 201,
    Mips_BCLR_B	= 202,
    Mips_BCLR_D	= 203,
    Mips_BCLR_H	= 204,
    Mips_BCLR_W	= 205,
    Mips_BEQ	= 206,
    Mips_BEQ64	= 207,
    Mips_BEQC	= 208,
    Mips_BEQL	= 209,
    Mips_BEQZ16_MM	= 210,
    Mips_BEQZALC	= 211,
    Mips_BEQZC	= 212,
    Mips_BEQZC_MM	= 213,
    Mips_BEQ_MM	= 214,
    Mips_BGEC	= 215,
    Mips_BGEUC	= 216,
    Mips_BGEZ	= 217,
    Mips_BGEZ64	= 218,
    Mips_BGEZAL	= 219,
    Mips_BGEZALC	= 220,
    Mips_BGEZALL	= 221,
    Mips_BGEZALS_MM	= 222,
    Mips_BGEZAL_MM	= 223,
    Mips_BGEZC	= 224,
    Mips_BGEZL	= 225,
    Mips_BGEZ_MM	= 226,
    Mips_BGTZ	= 227,
    Mips_BGTZ64	= 228,
    Mips_BGTZALC	= 229,
    Mips_BGTZC	= 230,
    Mips_BGTZL	= 231,
    Mips_BGTZ_MM	= 232,
    Mips_BINSLI_B	= 233,
    Mips_BINSLI_D	= 234,
    Mips_BINSLI_H	= 235,
    Mips_BINSLI_W	= 236,
    Mips_BINSL_B	= 237,
    Mips_BINSL_D	= 238,
    Mips_BINSL_H	= 239,
    Mips_BINSL_W	= 240,
    Mips_BINSRI_B	= 241,
    Mips_BINSRI_D	= 242,
    Mips_BINSRI_H	= 243,
    Mips_BINSRI_W	= 244,
    Mips_BINSR_B	= 245,
    Mips_BINSR_D	= 246,
    Mips_BINSR_H	= 247,
    Mips_BINSR_W	= 248,
    Mips_BITREV	= 249,
    Mips_BITSWAP	= 250,
    Mips_BLEZ	= 251,
    Mips_BLEZ64	= 252,
    Mips_BLEZALC	= 253,
    Mips_BLEZC	= 254,
    Mips_BLEZL	= 255,
    Mips_BLEZ_MM	= 256,
    Mips_BLTC	= 257,
    Mips_BLTUC	= 258,
    Mips_BLTZ	= 259,
    Mips_BLTZ64	= 260,
    Mips_BLTZAL	= 261,
    Mips_BLTZALC	= 262,
    Mips_BLTZALL	= 263,
    Mips_BLTZALS_MM	= 264,
    Mips_BLTZAL_MM	= 265,
    Mips_BLTZC	= 266,
    Mips_BLTZL	= 267,
    Mips_BLTZ_MM	= 268,
    Mips_BMNZI_B	= 269,
    Mips_BMNZ_V	= 270,
    Mips_BMZI_B	= 271,
    Mips_BMZ_V	= 272,
    Mips_BNE	= 273,
    Mips_BNE64	= 274,
    Mips_BNEC	= 275,
    Mips_BNEGI_B	= 276,
    Mips_BNEGI_D	= 277,
    Mips_BNEGI_H	= 278,
    Mips_BNEGI_W	= 279,
    Mips_BNEG_B	= 280,
    Mips_BNEG_D	= 281,
    Mips_BNEG_H	= 282,
    Mips_BNEG_W	= 283,
    Mips_BNEL	= 284,
    Mips_BNEZ16_MM	= 285,
    Mips_BNEZALC	= 286,
    Mips_BNEZC	= 287,
    Mips_BNEZC_MM	= 288,
    Mips_BNE_MM	= 289,
    Mips_BNVC	= 290,
    Mips_BNZ_B	= 291,
    Mips_BNZ_D	= 292,
    Mips_BNZ_H	= 293,
    Mips_BNZ_V	= 294,
    Mips_BNZ_W	= 295,
    Mips_BOVC	= 296,
    Mips_BPOSGE32	= 297,
    Mips_BPOSGE32_PSEUDO	= 298,
    Mips_BREAK	= 299,
    Mips_BREAK16_MM	= 300,
    Mips_BREAK_MM	= 301,
    Mips_BSELI_B	= 302,
    Mips_BSEL_D_PSEUDO	= 303,
    Mips_BSEL_FD_PSEUDO	= 304,
    Mips_BSEL_FW_PSEUDO	= 305,
    Mips_BSEL_H_PSEUDO	= 306,
    Mips_BSEL_V	= 307,
    Mips_BSEL_W_PSEUDO	= 308,
    Mips_BSETI_B	= 309,
    Mips_BSETI_D	= 310,
    Mips_BSETI_H	= 311,
    Mips_BSETI_W	= 312,
    Mips_BSET_B	= 313,
    Mips_BSET_D	= 314,
    Mips_BSET_H	= 315,
    Mips_BSET_W	= 316,
    Mips_BZ_B	= 317,
    Mips_BZ_D	= 318,
    Mips_BZ_H	= 319,
    Mips_BZ_V	= 320,
    Mips_BZ_W	= 321,
    Mips_B_MM_Pseudo	= 322,
    Mips_BeqzRxImm16	= 323,
    Mips_BeqzRxImmX16	= 324,
    Mips_Bimm16	= 325,
    Mips_BimmX16	= 326,
    Mips_BnezRxImm16	= 327,
    Mips_BnezRxImmX16	= 328,
    Mips_Break16	= 329,
    Mips_Bteqz16	= 330,
    Mips_BteqzT8CmpX16	= 331,
    Mips_BteqzT8CmpiX16	= 332,
    Mips_BteqzT8SltX16	= 333,
    Mips_BteqzT8SltiX16	= 334,
    Mips_BteqzT8SltiuX16	= 335,
    Mips_BteqzT8SltuX16	= 336,
    Mips_BteqzX16	= 337,
    Mips_Btnez16	= 338,
    Mips_BtnezT8CmpX16	= 339,
    Mips_BtnezT8CmpiX16	= 340,
    Mips_BtnezT8SltX16	= 341,
    Mips_BtnezT8SltiX16	= 342,
    Mips_BtnezT8SltiuX16	= 343,
    Mips_BtnezT8SltuX16	= 344,
    Mips_BtnezX16	= 345,
    Mips_BuildPairF64	= 346,
    Mips_BuildPairF64_64	= 347,
    Mips_CACHE	= 348,
    Mips_CACHE_MM	= 349,
    Mips_CACHE_R6	= 350,
    Mips_CEIL_L_D64	= 351,
    Mips_CEIL_L_S	= 352,
    Mips_CEIL_W_D32	= 353,
    Mips_CEIL_W_D64	= 354,
    Mips_CEIL_W_MM	= 355,
    Mips_CEIL_W_S	= 356,
    Mips_CEIL_W_S_MM	= 357,
    Mips_CEQI_B	= 358,
    Mips_CEQI_D	= 359,
    Mips_CEQI_H	= 360,
    Mips_CEQI_W	= 361,
    Mips_CEQ_B	= 362,
    Mips_CEQ_D	= 363,
    Mips_CEQ_H	= 364,
    Mips_CEQ_W	= 365,
    Mips_CFC1	= 366,
    Mips_CFC1_MM	= 367,
    Mips_CFCMSA	= 368,
    Mips_CINS	= 369,
    Mips_CINS32	= 370,
    Mips_CLASS_D	= 371,
    Mips_CLASS_S	= 372,
    Mips_CLEI_S_B	= 373,
    Mips_CLEI_S_D	= 374,
    Mips_CLEI_S_H	= 375,
    Mips_CLEI_S_W	= 376,
    Mips_CLEI_U_B	= 377,
    Mips_CLEI_U_D	= 378,
    Mips_CLEI_U_H	= 379,
    Mips_CLEI_U_W	= 380,
    Mips_CLE_S_B	= 381,
    Mips_CLE_S_D	= 382,
    Mips_CLE_S_H	= 383,
    Mips_CLE_S_W	= 384,
    Mips_CLE_U_B	= 385,
    Mips_CLE_U_D	= 386,
    Mips_CLE_U_H	= 387,
    Mips_CLE_U_W	= 388,
    Mips_CLO	= 389,
    Mips_CLO_MM	= 390,
    Mips_CLO_R6	= 391,
    Mips_CLTI_S_B	= 392,
    Mips_CLTI_S_D	= 393,
    Mips_CLTI_S_H	= 394,
    Mips_CLTI_S_W	= 395,
    Mips_CLTI_U_B	= 396,
    Mips_CLTI_U_D	= 397,
    Mips_CLTI_U_H	= 398,
    Mips_CLTI_U_W	= 399,
    Mips_CLT_S_B	= 400,
    Mips_CLT_S_D	= 401,
    Mips_CLT_S_H	= 402,
    Mips_CLT_S_W	= 403,
    Mips_CLT_U_B	= 404,
    Mips_CLT_U_D	= 405,
    Mips_CLT_U_H	= 406,
    Mips_CLT_U_W	= 407,
    Mips_CLZ	= 408,
    Mips_CLZ_MM	= 409,
    Mips_CLZ_R6	= 410,
    Mips_CMPGDU_EQ_QB	= 411,
    Mips_CMPGDU_LE_QB	= 412,
    Mips_CMPGDU_LT_QB	= 413,
    Mips_CMPGU_EQ_QB	= 414,
    Mips_CMPGU_LE_QB	= 415,
    Mips_CMPGU_LT_QB	= 416,
    Mips_CMPU_EQ_QB	= 417,
    Mips_CMPU_LE_QB	= 418,
    Mips_CMPU_LT_QB	= 419,
    Mips_CMP_EQ_D	= 420,
    Mips_CMP_EQ_PH	= 421,
    Mips_CMP_EQ_S	= 422,
    Mips_CMP_F_D	= 423,
    Mips_CMP_F_S	= 424,
    Mips_CMP_LE_D	= 425,
    Mips_CMP_LE_PH	= 426,
    Mips_CMP_LE_S	= 427,
    Mips_CMP_LT_D	= 428,
    Mips_CMP_LT_PH	= 429,
    Mips_CMP_LT_S	= 430,
    Mips_CMP_SAF_D	= 431,
    Mips_CMP_SAF_S	= 432,
    Mips_CMP_SEQ_D	= 433,
    Mips_CMP_SEQ_S	= 434,
    Mips_CMP_SLE_D	= 435,
    Mips_CMP_SLE_S	= 436,
    Mips_CMP_SLT_D	= 437,
    Mips_CMP_SLT_S	= 438,
    Mips_CMP_SUEQ_D	= 439,
    Mips_CMP_SUEQ_S	= 440,
    Mips_CMP_SULE_D	= 441,
    Mips_CMP_SULE_S	= 442,
    Mips_CMP_SULT_D	= 443,
    Mips_CMP_SULT_S	= 444,
    Mips_CMP_SUN_D	= 445,
    Mips_CMP_SUN_S	= 446,
    Mips_CMP_UEQ_D	= 447,
    Mips_CMP_UEQ_S	= 448,
    Mips_CMP_ULE_D	= 449,
    Mips_CMP_ULE_S	= 450,
    Mips_CMP_ULT_D	= 451,
    Mips_CMP_ULT_S	= 452,
    Mips_CMP_UN_D	= 453,
    Mips_CMP_UN_S	= 454,
    Mips_CONSTPOOL_ENTRY	= 455,
    Mips_COPY_FD_PSEUDO	= 456,
    Mips_COPY_FW_PSEUDO	= 457,
    Mips_COPY_S_B	= 458,
    Mips_COPY_S_D	= 459,
    Mips_COPY_S_H	= 460,
    Mips_COPY_S_W	= 461,
    Mips_COPY_U_B	= 462,
    Mips_COPY_U_D	= 463,
    Mips_COPY_U_H	= 464,
    Mips_COPY_U_W	= 465,
    Mips_CTC1	= 466,
    Mips_CTC1_MM	= 467,
    Mips_CTCMSA	= 468,
    Mips_CVT_D32_S	= 469,
    Mips_CVT_D32_W	= 470,
    Mips_CVT_D32_W_MM	= 471,
    Mips_CVT_D64_L	= 472,
    Mips_CVT_D64_S	= 473,
    Mips_CVT_D64_W	= 474,
    Mips_CVT_D_S_MM	= 475,
    Mips_CVT_L_D64	= 476,
    Mips_CVT_L_D64_MM	= 477,
    Mips_CVT_L_S	= 478,
    Mips_CVT_L_S_MM	= 479,
    Mips_CVT_S_D32	= 480,
    Mips_CVT_S_D32_MM	= 481,
    Mips_CVT_S_D64	= 482,
    Mips_CVT_S_L	= 483,
    Mips_CVT_S_W	= 484,
    Mips_CVT_S_W_MM	= 485,
    Mips_CVT_W_D32	= 486,
    Mips_CVT_W_D64	= 487,
    Mips_CVT_W_MM	= 488,
    Mips_CVT_W_S	= 489,
    Mips_CVT_W_S_MM	= 490,
    Mips_C_EQ_D32	= 491,
    Mips_C_EQ_D64	= 492,
    Mips_C_EQ_S	= 493,
    Mips_C_F_D32	= 494,
    Mips_C_F_D64	= 495,
    Mips_C_F_S	= 496,
    Mips_C_LE_D32	= 497,
    Mips_C_LE_D64	= 498,
    Mips_C_LE_S	= 499,
    Mips_C_LT_D32	= 500,
    Mips_C_LT_D64	= 501,
    Mips_C_LT_S	= 502,
    Mips_C_NGE_D32	= 503,
    Mips_C_NGE_D64	= 504,
    Mips_C_NGE_S	= 505,
    Mips_C_NGLE_D32	= 506,
    Mips_C_NGLE_D64	= 507,
    Mips_C_NGLE_S	= 508,
    Mips_C_NGL_D32	= 509,
    Mips_C_NGL_D64	= 510,
    Mips_C_NGL_S	= 511,
    Mips_C_NGT_D32	= 512,
    Mips_C_NGT_D64	= 513,
    Mips_C_NGT_S	= 514,
    Mips_C_OLE_D32	= 515,
    Mips_C_OLE_D64	= 516,
    Mips_C_OLE_S	= 517,
    Mips_C_OLT_D32	= 518,
    Mips_C_OLT_D64	= 519,
    Mips_C_OLT_S	= 520,
    Mips_C_SEQ_D32	= 521,
    Mips_C_SEQ_D64	= 522,
    Mips_C_SEQ_S	= 523,
    Mips_C_SF_D32	= 524,
    Mips_C_SF_D64	= 525,
    Mips_C_SF_S	= 526,
    Mips_C_UEQ_D32	= 527,
    Mips_C_UEQ_D64	= 528,
    Mips_C_UEQ_S	= 529,
    Mips_C_ULE_D32	= 530,
    Mips_C_ULE_D64	= 531,
    Mips_C_ULE_S	= 532,
    Mips_C_ULT_D32	= 533,
    Mips_C_ULT_D64	= 534,
    Mips_C_ULT_S	= 535,
    Mips_C_UN_D32	= 536,
    Mips_C_UN_D64	= 537,
    Mips_C_UN_S	= 538,
    Mips_CmpRxRy16	= 539,
    Mips_CmpiRxImm16	= 540,
    Mips_CmpiRxImmX16	= 541,
    Mips_Constant32	= 542,
    Mips_DADD	= 543,
    Mips_DADDi	= 544,
    Mips_DADDiu	= 545,
    Mips_DADDu	= 546,
    Mips_DAHI	= 547,
    Mips_DALIGN	= 548,
    Mips_DATI	= 549,
    Mips_DAUI	= 550,
    Mips_DBITSWAP	= 551,
    Mips_DCLO	= 552,
    Mips_DCLO_R6	= 553,
    Mips_DCLZ	= 554,
    Mips_DCLZ_R6	= 555,
    Mips_DDIV	= 556,
    Mips_DDIVU	= 557,
    Mips_DERET	= 558,
    Mips_DERET_MM	= 559,
    Mips_DEXT	= 560,
    Mips_DEXTM	= 561,
    Mips_DEXTU	= 562,
    Mips_DI	= 563,
    Mips_DINS	= 564,
    Mips_DINSM	= 565,
    Mips_DINSU	= 566,
    Mips_DIV	= 567,
    Mips_DIVU	= 568,
    Mips_DIV_S_B	= 569,
    Mips_DIV_S_D	= 570,
    Mips_DIV_S_H	= 571,
    Mips_DIV_S_W	= 572,
    Mips_DIV_U_B	= 573,
    Mips_DIV_U_D	= 574,
    Mips_DIV_U_H	= 575,
    Mips_DIV_U_W	= 576,
    Mips_DI_MM	= 577,
    Mips_DLSA	= 578,
    Mips_DLSA_R6	= 579,
    Mips_DMFC0	= 580,
    Mips_DMFC1	= 581,
    Mips_DMFC2	= 582,
    Mips_DMOD	= 583,
    Mips_DMODU	= 584,
    Mips_DMTC0	= 585,
    Mips_DMTC1	= 586,
    Mips_DMTC2	= 587,
    Mips_DMUH	= 588,
    Mips_DMUHU	= 589,
    Mips_DMUL	= 590,
    Mips_DMULT	= 591,
    Mips_DMULTu	= 592,
    Mips_DMULU	= 593,
    Mips_DMUL_R6	= 594,
    Mips_DOTP_S_D	= 595,
    Mips_DOTP_S_H	= 596,
    Mips_DOTP_S_W	= 597,
    Mips_DOTP_U_D	= 598,
    Mips_DOTP_U_H	= 599,
    Mips_DOTP_U_W	= 600,
    Mips_DPADD_S_D	= 601,
    Mips_DPADD_S_H	= 602,
    Mips_DPADD_S_W	= 603,
    Mips_DPADD_U_D	= 604,
    Mips_DPADD_U_H	= 605,
    Mips_DPADD_U_W	= 606,
    Mips_DPAQX_SA_W_PH	= 607,
    Mips_DPAQX_S_W_PH	= 608,
    Mips_DPAQ_SA_L_W	= 609,
    Mips_DPAQ_S_W_PH	= 610,
    Mips_DPAU_H_QBL	= 611,
    Mips_DPAU_H_QBR	= 612,
    Mips_DPAX_W_PH	= 613,
    Mips_DPA_W_PH	= 614,
    Mips_DPOP	= 615,
    Mips_DPSQX_SA_W_PH	= 616,
    Mips_DPSQX_S_W_PH	= 617,
    Mips_DPSQ_SA_L_W	= 618,
    Mips_DPSQ_S_W_PH	= 619,
    Mips_DPSUB_S_D	= 620,
    Mips_DPSUB_S_H	= 621,
    Mips_DPSUB_S_W	= 622,
    Mips_DPSUB_U_D	= 623,
    Mips_DPSUB_U_H	= 624,
    Mips_DPSUB_U_W	= 625,
    Mips_DPSU_H_QBL	= 626,
    Mips_DPSU_H_QBR	= 627,
    Mips_DPSX_W_PH	= 628,
    Mips_DPS_W_PH	= 629,
    Mips_DROTR	= 630,
    Mips_DROTR32	= 631,
    Mips_DROTRV	= 632,
    Mips_DSBH	= 633,
    Mips_DSDIV	= 634,
    Mips_DSHD	= 635,
    Mips_DSLL	= 636,
    Mips_DSLL32	= 637,
    Mips_DSLL64_32	= 638,
    Mips_DSLLV	= 639,
    Mips_DSRA	= 640,
    Mips_DSRA32	= 641,
    Mips_DSRAV	= 642,
    Mips_DSRL	= 643,
    Mips_DSRL32	= 644,
    Mips_DSRLV	= 645,
    Mips_DSUB	= 646,
    Mips_DSUBu	= 647,
    Mips_DUDIV	= 648,
    Mips_DivRxRy16	= 649,
    Mips_DivuRxRy16	= 650,
    Mips_EHB	= 651,
    Mips_EHB_MM	= 652,
    Mips_EI	= 653,
    Mips_EI_MM	= 654,
    Mips_ERET	= 655,
    Mips_ERET_MM	= 656,
    Mips_EXT	= 657,
    Mips_EXTP	= 658,
    Mips_EXTPDP	= 659,
    Mips_EXTPDPV	= 660,
    Mips_EXTPV	= 661,
    Mips_EXTRV_RS_W	= 662,
    Mips_EXTRV_R_W	= 663,
    Mips_EXTRV_S_H	= 664,
    Mips_EXTRV_W	= 665,
    Mips_EXTR_RS_W	= 666,
    Mips_EXTR_R_W	= 667,
    Mips_EXTR_S_H	= 668,
    Mips_EXTR_W	= 669,
    Mips_EXTS	= 670,
    Mips_EXTS32	= 671,
    Mips_EXT_MM	= 672,
    Mips_ExtractElementF64	= 673,
    Mips_ExtractElementF64_64	= 674,
    Mips_FABS_D	= 675,
    Mips_FABS_D32	= 676,
    Mips_FABS_D64	= 677,
    Mips_FABS_MM	= 678,
    Mips_FABS_S	= 679,
    Mips_FABS_S_MM	= 680,
    Mips_FABS_W	= 681,
    Mips_FADD_D	= 682,
    Mips_FADD_D32	= 683,
    Mips_FADD_D64	= 684,
    Mips_FADD_MM	= 685,
    Mips_FADD_S	= 686,
    Mips_FADD_S_MM	= 687,
    Mips_FADD_W	= 688,
    Mips_FCAF_D	= 689,
    Mips_FCAF_W	= 690,
    Mips_FCEQ_D	= 691,
    Mips_FCEQ_W	= 692,
    Mips_FCLASS_D	= 693,
    Mips_FCLASS_W	= 694,
    Mips_FCLE_D	= 695,
    Mips_FCLE_W	= 696,
    Mips_FCLT_D	= 697,
    Mips_FCLT_W	= 698,
    Mips_FCMP_D32	= 699,
    Mips_FCMP_D32_MM	= 700,
    Mips_FCMP_D64	= 701,
    Mips_FCMP_S32	= 702,
    Mips_FCMP_S32_MM	= 703,
    Mips_FCNE_D	= 704,
    Mips_FCNE_W	= 705,
    Mips_FCOR_D	= 706,
    Mips_FCOR_W	= 707,
    Mips_FCUEQ_D	= 708,
    Mips_FCUEQ_W	= 709,
    Mips_FCULE_D	= 710,
    Mips_FCULE_W	= 711,
    Mips_FCULT_D	= 712,
    Mips_FCULT_W	= 713,
    Mips_FCUNE_D	= 714,
    Mips_FCUNE_W	= 715,
    Mips_FCUN_D	= 716,
    Mips_FCUN_W	= 717,
    Mips_FDIV_D	= 718,
    Mips_FDIV_D32	= 719,
    Mips_FDIV_D64	= 720,
    Mips_FDIV_MM	= 721,
    Mips_FDIV_S	= 722,
    Mips_FDIV_S_MM	= 723,
    Mips_FDIV_W	= 724,
    Mips_FEXDO_H	= 725,
    Mips_FEXDO_W	= 726,
    Mips_FEXP2_D	= 727,
    Mips_FEXP2_D_1_PSEUDO	= 728,
    Mips_FEXP2_W	= 729,
    Mips_FEXP2_W_1_PSEUDO	= 730,
    Mips_FEXUPL_D	= 731,
    Mips_FEXUPL_W	= 732,
    Mips_FEXUPR_D	= 733,
    Mips_FEXUPR_W	= 734,
    Mips_FFINT_S_D	= 735,
    Mips_FFINT_S_W	= 736,
    Mips_FFINT_U_D	= 737,
    Mips_FFINT_U_W	= 738,
    Mips_FFQL_D	= 739,
    Mips_FFQL_W	= 740,
    Mips_FFQR_D	= 741,
    Mips_FFQR_W	= 742,
    Mips_FILL_B	= 743,
    Mips_FILL_D	= 744,
    Mips_FILL_FD_PSEUDO	= 745,
    Mips_FILL_FW_PSEUDO	= 746,
    Mips_FILL_H	= 747,
    Mips_FILL_W	= 748,
    Mips_FLOG2_D	= 749,
    Mips_FLOG2_W	= 750,
    Mips_FLOOR_L_D64	= 751,
    Mips_FLOOR_L_S	= 752,
    Mips_FLOOR_W_D32	= 753,
    Mips_FLOOR_W_D64	= 754,
    Mips_FLOOR_W_MM	= 755,
    Mips_FLOOR_W_S	= 756,
    Mips_FLOOR_W_S_MM	= 757,
    Mips_FMADD_D	= 758,
    Mips_FMADD_W	= 759,
    Mips_FMAX_A_D	= 760,
    Mips_FMAX_A_W	= 761,
    Mips_FMAX_D	= 762,
    Mips_FMAX_W	= 763,
    Mips_FMIN_A_D	= 764,
    Mips_FMIN_A_W	= 765,
    Mips_FMIN_D	= 766,
    Mips_FMIN_W	= 767,
    Mips_FMOV_D32	= 768,
    Mips_FMOV_D32_MM	= 769,
    Mips_FMOV_D64	= 770,
    Mips_FMOV_S	= 771,
    Mips_FMOV_S_MM	= 772,
    Mips_FMSUB_D	= 773,
    Mips_FMSUB_W	= 774,
    Mips_FMUL_D	= 775,
    Mips_FMUL_D32	= 776,
    Mips_FMUL_D64	= 777,
    Mips_FMUL_MM	= 778,
    Mips_FMUL_S	= 779,
    Mips_FMUL_S_MM	= 780,
    Mips_FMUL_W	= 781,
    Mips_FNEG_D32	= 782,
    Mips_FNEG_D64	= 783,
    Mips_FNEG_MM	= 784,
    Mips_FNEG_S	= 785,
    Mips_FNEG_S_MM	= 786,
    Mips_FRCP_D	= 787,
    Mips_FRCP_W	= 788,
    Mips_FRINT_D	= 789,
    Mips_FRINT_W	= 790,
    Mips_FRSQRT_D	= 791,
    Mips_FRSQRT_W	= 792,
    Mips_FSAF_D	= 793,
    Mips_FSAF_W	= 794,
    Mips_FSEQ_D	= 795,
    Mips_FSEQ_W	= 796,
    Mips_FSLE_D	= 797,
    Mips_FSLE_W	= 798,
    Mips_FSLT_D	= 799,
    Mips_FSLT_W	= 800,
    Mips_FSNE_D	= 801,
    Mips_FSNE_W	= 802,
    Mips_FSOR_D	= 803,
    Mips_FSOR_W	= 804,
    Mips_FSQRT_D	= 805,
    Mips_FSQRT_D32	= 806,
    Mips_FSQRT_D64	= 807,
    Mips_FSQRT_MM	= 808,
    Mips_FSQRT_S	= 809,
    Mips_FSQRT_S_MM	= 810,
    Mips_FSQRT_W	= 811,
    Mips_FSUB_D	= 812,
    Mips_FSUB_D32	= 813,
    Mips_FSUB_D64	= 814,
    Mips_FSUB_MM	= 815,
    Mips_FSUB_S	= 816,
    Mips_FSUB_S_MM	= 817,
    Mips_FSUB_W	= 818,
    Mips_FSUEQ_D	= 819,
    Mips_FSUEQ_W	= 820,
    Mips_FSULE_D	= 821,
    Mips_FSULE_W	= 822,
    Mips_FSULT_D	= 823,
    Mips_FSULT_W	= 824,
    Mips_FSUNE_D	= 825,
    Mips_FSUNE_W	= 826,
    Mips_FSUN_D	= 827,
    Mips_FSUN_W	= 828,
    Mips_FTINT_S_D	= 829,
    Mips_FTINT_S_W	= 830,
    Mips_FTINT_U_D	= 831,
    Mips_FTINT_U_W	= 832,
    Mips_FTQ_H	= 833,
    Mips_FTQ_W	= 834,
    Mips_FTRUNC_S_D	= 835,
    Mips_FTRUNC_S_W	= 836,
    Mips_FTRUNC_U_D	= 837,
    Mips_FTRUNC_U_W	= 838,
    Mips_GotPrologue16	= 839,
    Mips_HADD_S_D	= 840,
    Mips_HADD_S_H	= 841,
    Mips_HADD_S_W	= 842,
    Mips_HADD_U_D	= 843,
    Mips_HADD_U_H	= 844,
    Mips_HADD_U_W	= 845,
    Mips_HSUB_S_D	= 846,
    Mips_HSUB_S_H	= 847,
    Mips_HSUB_S_W	= 848,
    Mips_HSUB_U_D	= 849,
    Mips_HSUB_U_H	= 850,
    Mips_HSUB_U_W	= 851,
    Mips_ILVEV_B	= 852,
    Mips_ILVEV_D	= 853,
    Mips_ILVEV_H	= 854,
    Mips_ILVEV_W	= 855,
    Mips_ILVL_B	= 856,
    Mips_ILVL_D	= 857,
    Mips_ILVL_H	= 858,
    Mips_ILVL_W	= 859,
    Mips_ILVOD_B	= 860,
    Mips_ILVOD_D	= 861,
    Mips_ILVOD_H	= 862,
    Mips_ILVOD_W	= 863,
    Mips_ILVR_B	= 864,
    Mips_ILVR_D	= 865,
    Mips_ILVR_H	= 866,
    Mips_ILVR_W	= 867,
    Mips_INS	= 868,
    Mips_INSERT_B	= 869,
    Mips_INSERT_B_VIDX_PSEUDO	= 870,
    Mips_INSERT_D	= 871,
    Mips_INSERT_D_VIDX_PSEUDO	= 872,
    Mips_INSERT_FD_PSEUDO	= 873,
    Mips_INSERT_FD_VIDX_PSEUDO	= 874,
    Mips_INSERT_FW_PSEUDO	= 875,
    Mips_INSERT_FW_VIDX_PSEUDO	= 876,
    Mips_INSERT_H	= 877,
    Mips_INSERT_H_VIDX_PSEUDO	= 878,
    Mips_INSERT_W	= 879,
    Mips_INSERT_W_VIDX_PSEUDO	= 880,
    Mips_INSV	= 881,
    Mips_INSVE_B	= 882,
    Mips_INSVE_D	= 883,
    Mips_INSVE_H	= 884,
    Mips_INSVE_W	= 885,
    Mips_INS_MM	= 886,
    Mips_J	= 887,
    Mips_JAL	= 888,
    Mips_JALR	= 889,
    Mips_JALR16_MM	= 890,
    Mips_JALR64	= 891,
    Mips_JALR64Pseudo	= 892,
    Mips_JALRPseudo	= 893,
    Mips_JALRS16_MM	= 894,
    Mips_JALRS_MM	= 895,
    Mips_JALR_HB	= 896,
    Mips_JALR_MM	= 897,
    Mips_JALS_MM	= 898,
    Mips_JALX	= 899,
    Mips_JALX_MM	= 900,
    Mips_JAL_MM	= 901,
    Mips_JIALC	= 902,
    Mips_JIC	= 903,
    Mips_JR	= 904,
    Mips_JR16_MM	= 905,
    Mips_JR64	= 906,
    Mips_JRADDIUSP	= 907,
    Mips_JRC16_MM	= 908,
    Mips_JR_HB	= 909,
    Mips_JR_HB_R6	= 910,
    Mips_JR_MM	= 911,
    Mips_J_MM	= 912,
    Mips_Jal16	= 913,
    Mips_JalB16	= 914,
    Mips_JalOneReg	= 915,
    Mips_JalTwoReg	= 916,
    Mips_JrRa16	= 917,
    Mips_JrcRa16	= 918,
    Mips_JrcRx16	= 919,
    Mips_JumpLinkReg16	= 920,
    Mips_LB	= 921,
    Mips_LB64	= 922,
    Mips_LBU16_MM	= 923,
    Mips_LBUX	= 924,
    Mips_LB_MM	= 925,
    Mips_LBu	= 926,
    Mips_LBu64	= 927,
    Mips_LBu_MM	= 928,
    Mips_LD	= 929,
    Mips_LDC1	= 930,
    Mips_LDC164	= 931,
    Mips_LDC1_MM	= 932,
    Mips_LDC2	= 933,
    Mips_LDC2_R6	= 934,
    Mips_LDC3	= 935,
    Mips_LDI_B	= 936,
    Mips_LDI_D	= 937,
    Mips_LDI_H	= 938,
    Mips_LDI_W	= 939,
    Mips_LDL	= 940,
    Mips_LDPC	= 941,
    Mips_LDR	= 942,
    Mips_LDXC1	= 943,
    Mips_LDXC164	= 944,
    Mips_LD_B	= 945,
    Mips_LD_D	= 946,
    Mips_LD_H	= 947,
    Mips_LD_W	= 948,
    Mips_LEA_ADDiu	= 949,
    Mips_LEA_ADDiu64	= 950,
    Mips_LEA_ADDiu_MM	= 951,
    Mips_LH	= 952,
    Mips_LH64	= 953,
    Mips_LHU16_MM	= 954,
    Mips_LHX	= 955,
    Mips_LH_MM	= 956,
    Mips_LHu	= 957,
    Mips_LHu64	= 958,
    Mips_LHu_MM	= 959,
    Mips_LI16_MM	= 960,
    Mips_LL	= 961,
    Mips_LLD	= 962,
    Mips_LLD_R6	= 963,
    Mips_LL_MM	= 964,
    Mips_LL_R6	= 965,
    Mips_LOAD_ACC128	= 966,
    Mips_LOAD_ACC64	= 967,
    Mips_LOAD_ACC64DSP	= 968,
    Mips_LOAD_CCOND_DSP	= 969,
    Mips_LONG_BRANCH_ADDiu	= 970,
    Mips_LONG_BRANCH_DADDiu	= 971,
    Mips_LONG_BRANCH_LUi	= 972,
    Mips_LSA	= 973,
    Mips_LSA_R6	= 974,
    Mips_LUXC1	= 975,
    Mips_LUXC164	= 976,
    Mips_LUXC1_MM	= 977,
    Mips_LUi	= 978,
    Mips_LUi64	= 979,
    Mips_LUi_MM	= 980,
    Mips_LW	= 981,
    Mips_LW16_MM	= 982,
    Mips_LW64	= 983,
    Mips_LWC1	= 984,
    Mips_LWC1_MM	= 985,
    Mips_LWC2	= 986,
    Mips_LWC2_R6	= 987,
    Mips_LWC3	= 988,
    Mips_LWGP_MM	= 989,
    Mips_LWL	= 990,
    Mips_LWL64	= 991,
    Mips_LWL_MM	= 992,
    Mips_LWM16_MM	= 993,
    Mips_LWM32_MM	= 994,
    Mips_LWM_MM	= 995,
    Mips_LWPC	= 996,
    Mips_LWP_MM	= 997,
    Mips_LWR	= 998,
    Mips_LWR64	= 999,
    Mips_LWR_MM	= 1000,
    Mips_LWSP_MM	= 1001,
    Mips_LWUPC	= 1002,
    Mips_LWU_MM	= 1003,
    Mips_LWX	= 1004,
    Mips_LWXC1	= 1005,
    Mips_LWXC1_MM	= 1006,
    Mips_LWXS_MM	= 1007,
    Mips_LW_MM	= 1008,
    Mips_LWu	= 1009,
    Mips_LbRxRyOffMemX16	= 1010,
    Mips_LbuRxRyOffMemX16	= 1011,
    Mips_LhRxRyOffMemX16	= 1012,
    Mips_LhuRxRyOffMemX16	= 1013,
    Mips_LiRxImm16	= 1014,
    Mips_LiRxImmAlignX16	= 1015,
    Mips_LiRxImmX16	= 1016,
    Mips_LoadAddr32Imm	= 1017,
    Mips_LoadAddr32Reg	= 1018,
    Mips_LoadImm32Reg	= 1019,
    Mips_LoadImm64Reg	= 1020,
    Mips_LwConstant32	= 1021,
    Mips_LwRxPcTcp16	= 1022,
    Mips_LwRxPcTcpX16	= 1023,
    Mips_LwRxRyOffMemX16	= 1024,
    Mips_LwRxSpImmX16	= 1025,
    Mips_MADD	= 1026,
    Mips_MADDF_D	= 1027,
    Mips_MADDF_S	= 1028,
    Mips_MADDR_Q_H	= 1029,
    Mips_MADDR_Q_W	= 1030,
    Mips_MADDU	= 1031,
    Mips_MADDU_DSP	= 1032,
    Mips_MADDU_MM	= 1033,
    Mips_MADDV_B	= 1034,
    Mips_MADDV_D	= 1035,
    Mips_MADDV_H	= 1036,
    Mips_MADDV_W	= 1037,
    Mips_MADD_D32	= 1038,
    Mips_MADD_D32_MM	= 1039,
    Mips_MADD_D64	= 1040,
    Mips_MADD_DSP	= 1041,
    Mips_MADD_MM	= 1042,
    Mips_MADD_Q_H	= 1043,
    Mips_MADD_Q_W	= 1044,
    Mips_MADD_S	= 1045,
    Mips_MADD_S_MM	= 1046,
    Mips_MAQ_SA_W_PHL	= 1047,
    Mips_MAQ_SA_W_PHR	= 1048,
    Mips_MAQ_S_W_PHL	= 1049,
    Mips_MAQ_S_W_PHR	= 1050,
    Mips_MAXA_D	= 1051,
    Mips_MAXA_S	= 1052,
    Mips_MAXI_S_B	= 1053,
    Mips_MAXI_S_D	= 1054,
    Mips_MAXI_S_H	= 1055,
    Mips_MAXI_S_W	= 1056,
    Mips_MAXI_U_B	= 1057,
    Mips_MAXI_U_D	= 1058,
    Mips_MAXI_U_H	= 1059,
    Mips_MAXI_U_W	= 1060,
    Mips_MAX_A_B	= 1061,
    Mips_MAX_A_D	= 1062,
    Mips_MAX_A_H	= 1063,
    Mips_MAX_A_W	= 1064,
    Mips_MAX_D	= 1065,
    Mips_MAX_S	= 1066,
    Mips_MAX_S_B	= 1067,
    Mips_MAX_S_D	= 1068,
    Mips_MAX_S_H	= 1069,
    Mips_MAX_S_W	= 1070,
    Mips_MAX_U_B	= 1071,
    Mips_MAX_U_D	= 1072,
    Mips_MAX_U_H	= 1073,
    Mips_MAX_U_W	= 1074,
    Mips_MFC0	= 1075,
    Mips_MFC1	= 1076,
    Mips_MFC1_MM	= 1077,
    Mips_MFC2	= 1078,
    Mips_MFHC1_D32	= 1079,
    Mips_MFHC1_D64	= 1080,
    Mips_MFHC1_MM	= 1081,
    Mips_MFHI	= 1082,
    Mips_MFHI16_MM	= 1083,
    Mips_MFHI64	= 1084,
    Mips_MFHI_DSP	= 1085,
    Mips_MFHI_MM	= 1086,
    Mips_MFLO	= 1087,
    Mips_MFLO16_MM	= 1088,
    Mips_MFLO64	= 1089,
    Mips_MFLO_DSP	= 1090,
    Mips_MFLO_MM	= 1091,
    Mips_MINA_D	= 1092,
    Mips_MINA_S	= 1093,
    Mips_MINI_S_B	= 1094,
    Mips_MINI_S_D	= 1095,
    Mips_MINI_S_H	= 1096,
    Mips_MINI_S_W	= 1097,
    Mips_MINI_U_B	= 1098,
    Mips_MINI_U_D	= 1099,
    Mips_MINI_U_H	= 1100,
    Mips_MINI_U_W	= 1101,
    Mips_MIN_A_B	= 1102,
    Mips_MIN_A_D	= 1103,
    Mips_MIN_A_H	= 1104,
    Mips_MIN_A_W	= 1105,
    Mips_MIN_D	= 1106,
    Mips_MIN_S	= 1107,
    Mips_MIN_S_B	= 1108,
    Mips_MIN_S_D	= 1109,
    Mips_MIN_S_H	= 1110,
    Mips_MIN_S_W	= 1111,
    Mips_MIN_U_B	= 1112,
    Mips_MIN_U_D	= 1113,
    Mips_MIN_U_H	= 1114,
    Mips_MIN_U_W	= 1115,
    Mips_MIPSeh_return32	= 1116,
    Mips_MIPSeh_return64	= 1117,
    Mips_MOD	= 1118,
    Mips_MODSUB	= 1119,
    Mips_MODU	= 1120,
    Mips_MOD_S_B	= 1121,
    Mips_MOD_S_D	= 1122,
    Mips_MOD_S_H	= 1123,
    Mips_MOD_S_W	= 1124,
    Mips_MOD_U_B	= 1125,
    Mips_MOD_U_D	= 1126,
    Mips_MOD_U_H	= 1127,
    Mips_MOD_U_W	= 1128,
    Mips_MOVE16_MM	= 1129,
    Mips_MOVEP_MM	= 1130,
    Mips_MOVE_V	= 1131,
    Mips_MOVF_D32	= 1132,
    Mips_MOVF_D32_MM	= 1133,
    Mips_MOVF_D64	= 1134,
    Mips_MOVF_I	= 1135,
    Mips_MOVF_I64	= 1136,
    Mips_MOVF_I_MM	= 1137,
    Mips_MOVF_S	= 1138,
    Mips_MOVF_S_MM	= 1139,
    Mips_MOVN_I64_D64	= 1140,
    Mips_MOVN_I64_I	= 1141,
    Mips_MOVN_I64_I64	= 1142,
    Mips_MOVN_I64_S	= 1143,
    Mips_MOVN_I_D32	= 1144,
    Mips_MOVN_I_D32_MM	= 1145,
    Mips_MOVN_I_D64	= 1146,
    Mips_MOVN_I_I	= 1147,
    Mips_MOVN_I_I64	= 1148,
    Mips_MOVN_I_MM	= 1149,
    Mips_MOVN_I_S	= 1150,
    Mips_MOVN_I_S_MM	= 1151,
    Mips_MOVT_D32	= 1152,
    Mips_MOVT_D32_MM	= 1153,
    Mips_MOVT_D64	= 1154,
    Mips_MOVT_I	= 1155,
    Mips_MOVT_I64	= 1156,
    Mips_MOVT_I_MM	= 1157,
    Mips_MOVT_S	= 1158,
    Mips_MOVT_S_MM	= 1159,
    Mips_MOVZ_I64_D64	= 1160,
    Mips_MOVZ_I64_I	= 1161,
    Mips_MOVZ_I64_I64	= 1162,
    Mips_MOVZ_I64_S	= 1163,
    Mips_MOVZ_I_D32	= 1164,
    Mips_MOVZ_I_D32_MM	= 1165,
    Mips_MOVZ_I_D64	= 1166,
    Mips_MOVZ_I_I	= 1167,
    Mips_MOVZ_I_I64	= 1168,
    Mips_MOVZ_I_MM	= 1169,
    Mips_MOVZ_I_S	= 1170,
    Mips_MOVZ_I_S_MM	= 1171,
    Mips_MSUB	= 1172,
    Mips_MSUBF_D	= 1173,
    Mips_MSUBF_S	= 1174,
    Mips_MSUBR_Q_H	= 1175,
    Mips_MSUBR_Q_W	= 1176,
    Mips_MSUBU	= 1177,
    Mips_MSUBU_DSP	= 1178,
    Mips_MSUBU_MM	= 1179,
    Mips_MSUBV_B	= 1180,
    Mips_MSUBV_D	= 1181,
    Mips_MSUBV_H	= 1182,
    Mips_MSUBV_W	= 1183,
    Mips_MSUB_D32	= 1184,
    Mips_MSUB_D32_MM	= 1185,
    Mips_MSUB_D64	= 1186,
    Mips_MSUB_DSP	= 1187,
    Mips_MSUB_MM	= 1188,
    Mips_MSUB_Q_H	= 1189,
    Mips_MSUB_Q_W	= 1190,
    Mips_MSUB_S	= 1191,
    Mips_MSUB_S_MM	= 1192,
    Mips_MTC0	= 1193,
    Mips_MTC1	= 1194,
    Mips_MTC1_MM	= 1195,
    Mips_MTC2	= 1196,
    Mips_MTHC1_D32	= 1197,
    Mips_MTHC1_D64	= 1198,
    Mips_MTHC1_MM	= 1199,
    Mips_MTHI	= 1200,
    Mips_MTHI64	= 1201,
    Mips_MTHI_DSP	= 1202,
    Mips_MTHI_MM	= 1203,
    Mips_MTHLIP	= 1204,
    Mips_MTLO	= 1205,
    Mips_MTLO64	= 1206,
    Mips_MTLO_DSP	= 1207,
    Mips_MTLO_MM	= 1208,
    Mips_MTM0	= 1209,
    Mips_MTM1	= 1210,
    Mips_MTM2	= 1211,
    Mips_MTP0	= 1212,
    Mips_MTP1	= 1213,
    Mips_MTP2	= 1214,
    Mips_MUH	= 1215,
    Mips_MUHU	= 1216,
    Mips_MUL	= 1217,
    Mips_MULEQ_S_W_PHL	= 1218,
    Mips_MULEQ_S_W_PHR	= 1219,
    Mips_MULEU_S_PH_QBL	= 1220,
    Mips_MULEU_S_PH_QBR	= 1221,
    Mips_MULQ_RS_PH	= 1222,
    Mips_MULQ_RS_W	= 1223,
    Mips_MULQ_S_PH	= 1224,
    Mips_MULQ_S_W	= 1225,
    Mips_MULR_Q_H	= 1226,
    Mips_MULR_Q_W	= 1227,
    Mips_MULSAQ_S_W_PH	= 1228,
    Mips_MULSA_W_PH	= 1229,
    Mips_MULT	= 1230,
    Mips_MULTU_DSP	= 1231,
    Mips_MULT_DSP	= 1232,
    Mips_MULT_MM	= 1233,
    Mips_MULTu	= 1234,
    Mips_MULTu_MM	= 1235,
    Mips_MULU	= 1236,
    Mips_MULV_B	= 1237,
    Mips_MULV_D	= 1238,
    Mips_MULV_H	= 1239,
    Mips_MULV_W	= 1240,
    Mips_MUL_MM	= 1241,
    Mips_MUL_PH	= 1242,
    Mips_MUL_Q_H	= 1243,
    Mips_MUL_Q_W	= 1244,
    Mips_MUL_R6	= 1245,
    Mips_MUL_S_PH	= 1246,
    Mips_Mfhi16	= 1247,
    Mips_Mflo16	= 1248,
    Mips_Move32R16	= 1249,
    Mips_MoveR3216	= 1250,
    Mips_MultRxRy16	= 1251,
    Mips_MultRxRyRz16	= 1252,
    Mips_MultuRxRy16	= 1253,
    Mips_MultuRxRyRz16	= 1254,
    Mips_NLOC_B	= 1255,
    Mips_NLOC_D	= 1256,
    Mips_NLOC_H	= 1257,
    Mips_NLOC_W	= 1258,
    Mips_NLZC_B	= 1259,
    Mips_NLZC_D	= 1260,
    Mips_NLZC_H	= 1261,
    Mips_NLZC_W	= 1262,
    Mips_NMADD_D32	= 1263,
    Mips_NMADD_D32_MM	= 1264,
    Mips_NMADD_D64	= 1265,
    Mips_NMADD_S	= 1266,
    Mips_NMADD_S_MM	= 1267,
    Mips_NMSUB_D32	= 1268,
    Mips_NMSUB_D32_MM	= 1269,
    Mips_NMSUB_D64	= 1270,
    Mips_NMSUB_S	= 1271,
    Mips_NMSUB_S_MM	= 1272,
    Mips_NOP	= 1273,
    Mips_NOR	= 1274,
    Mips_NOR64	= 1275,
    Mips_NORI_B	= 1276,
    Mips_NOR_MM	= 1277,
    Mips_NOR_V	= 1278,
    Mips_NOR_V_D_PSEUDO	= 1279,
    Mips_NOR_V_H_PSEUDO	= 1280,
    Mips_NOR_V_W_PSEUDO	= 1281,
    Mips_NOT16_MM	= 1282,
    Mips_NegRxRy16	= 1283,
    Mips_NotRxRy16	= 1284,
    Mips_OR	= 1285,
    Mips_OR16_MM	= 1286,
    Mips_OR64	= 1287,
    Mips_ORI_B	= 1288,
    Mips_OR_MM	= 1289,
    Mips_OR_V	= 1290,
    Mips_OR_V_D_PSEUDO	= 1291,
    Mips_OR_V_H_PSEUDO	= 1292,
    Mips_OR_V_W_PSEUDO	= 1293,
    Mips_ORi	= 1294,
    Mips_ORi64	= 1295,
    Mips_ORi_MM	= 1296,
    Mips_OrRxRxRy16	= 1297,
    Mips_PACKRL_PH	= 1298,
    Mips_PAUSE	= 1299,
    Mips_PAUSE_MM	= 1300,
    Mips_PCKEV_B	= 1301,
    Mips_PCKEV_D	= 1302,
    Mips_PCKEV_H	= 1303,
    Mips_PCKEV_W	= 1304,
    Mips_PCKOD_B	= 1305,
    Mips_PCKOD_D	= 1306,
    Mips_PCKOD_H	= 1307,
    Mips_PCKOD_W	= 1308,
    Mips_PCNT_B	= 1309,
    Mips_PCNT_D	= 1310,
    Mips_PCNT_H	= 1311,
    Mips_PCNT_W	= 1312,
    Mips_PICK_PH	= 1313,
    Mips_PICK_QB	= 1314,
    Mips_POP	= 1315,
    Mips_PRECEQU_PH_QBL	= 1316,
    Mips_PRECEQU_PH_QBLA	= 1317,
    Mips_PRECEQU_PH_QBR	= 1318,
    Mips_PRECEQU_PH_QBRA	= 1319,
    Mips_PRECEQ_W_PHL	= 1320,
    Mips_PRECEQ_W_PHR	= 1321,
    Mips_PRECEU_PH_QBL	= 1322,
    Mips_PRECEU_PH_QBLA	= 1323,
    Mips_PRECEU_PH_QBR	= 1324,
    Mips_PRECEU_PH_QBRA	= 1325,
    Mips_PRECRQU_S_QB_PH	= 1326,
    Mips_PRECRQ_PH_W	= 1327,
    Mips_PRECRQ_QB_PH	= 1328,
    Mips_PRECRQ_RS_PH_W	= 1329,
    Mips_PRECR_QB_PH	= 1330,
    Mips_PRECR_SRA_PH_W	= 1331,
    Mips_PRECR_SRA_R_PH_W	= 1332,
    Mips_PREF	= 1333,
    Mips_PREF_MM	= 1334,
    Mips_PREF_R6	= 1335,
    Mips_PREPEND	= 1336,
    Mips_PseudoCMPU_EQ_QB	= 1337,
    Mips_PseudoCMPU_LE_QB	= 1338,
    Mips_PseudoCMPU_LT_QB	= 1339,
    Mips_PseudoCMP_EQ_PH	= 1340,
    Mips_PseudoCMP_LE_PH	= 1341,
    Mips_PseudoCMP_LT_PH	= 1342,
    Mips_PseudoCVT_D32_W	= 1343,
    Mips_PseudoCVT_D64_L	= 1344,
    Mips_PseudoCVT_D64_W	= 1345,
    Mips_PseudoCVT_S_L	= 1346,
    Mips_PseudoCVT_S_W	= 1347,
    Mips_PseudoDMULT	= 1348,
    Mips_PseudoDMULTu	= 1349,
    Mips_PseudoDSDIV	= 1350,
    Mips_PseudoDUDIV	= 1351,
    Mips_PseudoIndirectBranch	= 1352,
    Mips_PseudoIndirectBranch64	= 1353,
    Mips_PseudoMADD	= 1354,
    Mips_PseudoMADDU	= 1355,
    Mips_PseudoMFHI	= 1356,
    Mips_PseudoMFHI64	= 1357,
    Mips_PseudoMFLO	= 1358,
    Mips_PseudoMFLO64	= 1359,
    Mips_PseudoMSUB	= 1360,
    Mips_PseudoMSUBU	= 1361,
    Mips_PseudoMTLOHI	= 1362,
    Mips_PseudoMTLOHI64	= 1363,
    Mips_PseudoMTLOHI_DSP	= 1364,
    Mips_PseudoMULT	= 1365,
    Mips_PseudoMULTu	= 1366,
    Mips_PseudoPICK_PH	= 1367,
    Mips_PseudoPICK_QB	= 1368,
    Mips_PseudoReturn	= 1369,
    Mips_PseudoReturn64	= 1370,
    Mips_PseudoSDIV	= 1371,
    Mips_PseudoSELECTFP_F_D32	= 1372,
    Mips_PseudoSELECTFP_F_D64	= 1373,
    Mips_PseudoSELECTFP_F_I	= 1374,
    Mips_PseudoSELECTFP_F_I64	= 1375,
    Mips_PseudoSELECTFP_F_S	= 1376,
    Mips_PseudoSELECTFP_T_D32	= 1377,
    Mips_PseudoSELECTFP_T_D64	= 1378,
    Mips_PseudoSELECTFP_T_I	= 1379,
    Mips_PseudoSELECTFP_T_I64	= 1380,
    Mips_PseudoSELECTFP_T_S	= 1381,
    Mips_PseudoSELECT_D32	= 1382,
    Mips_PseudoSELECT_D64	= 1383,
    Mips_PseudoSELECT_I	= 1384,
    Mips_PseudoSELECT_I64	= 1385,
    Mips_PseudoSELECT_S	= 1386,
    Mips_PseudoUDIV	= 1387,
    Mips_RADDU_W_QB	= 1388,
    Mips_RDDSP	= 1389,
    Mips_RDHWR	= 1390,
    Mips_RDHWR64	= 1391,
    Mips_RDHWR_MM	= 1392,
    Mips_REPLV_PH	= 1393,
    Mips_REPLV_QB	= 1394,
    Mips_REPL_PH	= 1395,
    Mips_REPL_QB	= 1396,
    Mips_RINT_D	= 1397,
    Mips_RINT_S	= 1398,
    Mips_ROTR	= 1399,
    Mips_ROTRV	= 1400,
    Mips_ROTRV_MM	= 1401,
    Mips_ROTR_MM	= 1402,
    Mips_ROUND_L_D64	= 1403,
    Mips_ROUND_L_S	= 1404,
    Mips_ROUND_W_D32	= 1405,
    Mips_ROUND_W_D64	= 1406,
    Mips_ROUND_W_MM	= 1407,
    Mips_ROUND_W_S	= 1408,
    Mips_ROUND_W_S_MM	= 1409,
    Mips_Restore16	= 1410,
    Mips_RestoreX16	= 1411,
    Mips_RetRA	= 1412,
    Mips_RetRA16	= 1413,
    Mips_SAT_S_B	= 1414,
    Mips_SAT_S_D	= 1415,
    Mips_SAT_S_H	= 1416,
    Mips_SAT_S_W	= 1417,
    Mips_SAT_U_B	= 1418,
    Mips_SAT_U_D	= 1419,
    Mips_SAT_U_H	= 1420,
    Mips_SAT_U_W	= 1421,
    Mips_SB	= 1422,
    Mips_SB16_MM	= 1423,
    Mips_SB64	= 1424,
    Mips_SB_MM	= 1425,
    Mips_SC	= 1426,
    Mips_SCD	= 1427,
    Mips_SCD_R6	= 1428,
    Mips_SC_MM	= 1429,
    Mips_SC_R6	= 1430,
    Mips_SD	= 1431,
    Mips_SDBBP	= 1432,
    Mips_SDBBP16_MM	= 1433,
    Mips_SDBBP_MM	= 1434,
    Mips_SDBBP_R6	= 1435,
    Mips_SDC1	= 1436,
    Mips_SDC164	= 1437,
    Mips_SDC1_MM	= 1438,
    Mips_SDC2	= 1439,
    Mips_SDC2_R6	= 1440,
    Mips_SDC3	= 1441,
    Mips_SDIV	= 1442,
    Mips_SDIV_MM	= 1443,
    Mips_SDL	= 1444,
    Mips_SDR	= 1445,
    Mips_SDXC1	= 1446,
    Mips_SDXC164	= 1447,
    Mips_SEB	= 1448,
    Mips_SEB64	= 1449,
    Mips_SEB_MM	= 1450,
    Mips_SEH	= 1451,
    Mips_SEH64	= 1452,
    Mips_SEH_MM	= 1453,
    Mips_SELEQZ	= 1454,
    Mips_SELEQZ64	= 1455,
    Mips_SELEQZ_D	= 1456,
    Mips_SELEQZ_S	= 1457,
    Mips_SELNEZ	= 1458,
    Mips_SELNEZ64	= 1459,
    Mips_SELNEZ_D	= 1460,
    Mips_SELNEZ_S	= 1461,
    Mips_SEL_D	= 1462,
    Mips_SEL_S	= 1463,
    Mips_SEQ	= 1464,
    Mips_SEQi	= 1465,
    Mips_SH	= 1466,
    Mips_SH16_MM	= 1467,
    Mips_SH64	= 1468,
    Mips_SHF_B	= 1469,
    Mips_SHF_H	= 1470,
    Mips_SHF_W	= 1471,
    Mips_SHILO	= 1472,
    Mips_SHILOV	= 1473,
    Mips_SHLLV_PH	= 1474,
    Mips_SHLLV_QB	= 1475,
    Mips_SHLLV_S_PH	= 1476,
    Mips_SHLLV_S_W	= 1477,
    Mips_SHLL_PH	= 1478,
    Mips_SHLL_QB	= 1479,
    Mips_SHLL_S_PH	= 1480,
    Mips_SHLL_S_W	= 1481,
    Mips_SHRAV_PH	= 1482,
    Mips_SHRAV_QB	= 1483,
    Mips_SHRAV_R_PH	= 1484,
    Mips_SHRAV_R_QB	= 1485,
    Mips_SHRAV_R_W	= 1486,
    Mips_SHRA_PH	= 1487,
    Mips_SHRA_QB	= 1488,
    Mips_SHRA_R_PH	= 1489,
    Mips_SHRA_R_QB	= 1490,
    Mips_SHRA_R_W	= 1491,
    Mips_SHRLV_PH	= 1492,
    Mips_SHRLV_QB	= 1493,
    Mips_SHRL_PH	= 1494,
    Mips_SHRL_QB	= 1495,
    Mips_SH_MM	= 1496,
    Mips_SLDI_B	= 1497,
    Mips_SLDI_D	= 1498,
    Mips_SLDI_H	= 1499,
    Mips_SLDI_W	= 1500,
    Mips_SLD_B	= 1501,
    Mips_SLD_D	= 1502,
    Mips_SLD_H	= 1503,
    Mips_SLD_W	= 1504,
    Mips_SLL	= 1505,
    Mips_SLL16_MM	= 1506,
    Mips_SLL64_32	= 1507,
    Mips_SLL64_64	= 1508,
    Mips_SLLI_B	= 1509,
    Mips_SLLI_D	= 1510,
    Mips_SLLI_H	= 1511,
    Mips_SLLI_W	= 1512,
    Mips_SLLV	= 1513,
    Mips_SLLV_MM	= 1514,
    Mips_SLL_B	= 1515,
    Mips_SLL_D	= 1516,
    Mips_SLL_H	= 1517,
    Mips_SLL_MM	= 1518,
    Mips_SLL_W	= 1519,
    Mips_SLT	= 1520,
    Mips_SLT64	= 1521,
    Mips_SLT_MM	= 1522,
    Mips_SLTi	= 1523,
    Mips_SLTi64	= 1524,
    Mips_SLTi_MM	= 1525,
    Mips_SLTiu	= 1526,
    Mips_SLTiu64	= 1527,
    Mips_SLTiu_MM	= 1528,
    Mips_SLTu	= 1529,
    Mips_SLTu64	= 1530,
    Mips_SLTu_MM	= 1531,
    Mips_SNE	= 1532,
    Mips_SNEi	= 1533,
    Mips_SNZ_B_PSEUDO	= 1534,
    Mips_SNZ_D_PSEUDO	= 1535,
    Mips_SNZ_H_PSEUDO	= 1536,
    Mips_SNZ_V_PSEUDO	= 1537,
    Mips_SNZ_W_PSEUDO	= 1538,
    Mips_SPLATI_B	= 1539,
    Mips_SPLATI_D	= 1540,
    Mips_SPLATI_H	= 1541,
    Mips_SPLATI_W	= 1542,
    Mips_SPLAT_B	= 1543,
    Mips_SPLAT_D	= 1544,
    Mips_SPLAT_H	= 1545,
    Mips_SPLAT_W	= 1546,
    Mips_SRA	= 1547,
    Mips_SRAI_B	= 1548,
    Mips_SRAI_D	= 1549,
    Mips_SRAI_H	= 1550,
    Mips_SRAI_W	= 1551,
    Mips_SRARI_B	= 1552,
    Mips_SRARI_D	= 1553,
    Mips_SRARI_H	= 1554,
    Mips_SRARI_W	= 1555,
    Mips_SRAR_B	= 1556,
    Mips_SRAR_D	= 1557,
    Mips_SRAR_H	= 1558,
    Mips_SRAR_W	= 1559,
    Mips_SRAV	= 1560,
    Mips_SRAV_MM	= 1561,
    Mips_SRA_B	= 1562,
    Mips_SRA_D	= 1563,
    Mips_SRA_H	= 1564,
    Mips_SRA_MM	= 1565,
    Mips_SRA_W	= 1566,
    Mips_SRL	= 1567,
    Mips_SRL16_MM	= 1568,
    Mips_SRLI_B	= 1569,
    Mips_SRLI_D	= 1570,
    Mips_SRLI_H	= 1571,
    Mips_SRLI_W	= 1572,
    Mips_SRLRI_B	= 1573,
    Mips_SRLRI_D	= 1574,
    Mips_SRLRI_H	= 1575,
    Mips_SRLRI_W	= 1576,
    Mips_SRLR_B	= 1577,
    Mips_SRLR_D	= 1578,
    Mips_SRLR_H	= 1579,
    Mips_SRLR_W	= 1580,
    Mips_SRLV	= 1581,
    Mips_SRLV_MM	= 1582,
    Mips_SRL_B	= 1583,
    Mips_SRL_D	= 1584,
    Mips_SRL_H	= 1585,
    Mips_SRL_MM	= 1586,
    Mips_SRL_W	= 1587,
    Mips_SSNOP	= 1588,
    Mips_SSNOP_MM	= 1589,
    Mips_STORE_ACC128	= 1590,
    Mips_STORE_ACC64	= 1591,
    Mips_STORE_ACC64DSP	= 1592,
    Mips_STORE_CCOND_DSP	= 1593,
    Mips_ST_B	= 1594,
    Mips_ST_D	= 1595,
    Mips_ST_H	= 1596,
    Mips_ST_W	= 1597,
    Mips_SUB	= 1598,
    Mips_SUBQH_PH	= 1599,
    Mips_SUBQH_R_PH	= 1600,
    Mips_SUBQH_R_W	= 1601,
    Mips_SUBQH_W	= 1602,
    Mips_SUBQ_PH	= 1603,
    Mips_SUBQ_S_PH	= 1604,
    Mips_SUBQ_S_W	= 1605,
    Mips_SUBSUS_U_B	= 1606,
    Mips_SUBSUS_U_D	= 1607,
    Mips_SUBSUS_U_H	= 1608,
    Mips_SUBSUS_U_W	= 1609,
    Mips_SUBSUU_S_B	= 1610,
    Mips_SUBSUU_S_D	= 1611,
    Mips_SUBSUU_S_H	= 1612,
    Mips_SUBSUU_S_W	= 1613,
    Mips_SUBS_S_B	= 1614,
    Mips_SUBS_S_D	= 1615,
    Mips_SUBS_S_H	= 1616,
    Mips_SUBS_S_W	= 1617,
    Mips_SUBS_U_B	= 1618,
    Mips_SUBS_U_D	= 1619,
    Mips_SUBS_U_H	= 1620,
    Mips_SUBS_U_W	= 1621,
    Mips_SUBU16_MM	= 1622,
    Mips_SUBUH_QB	= 1623,
    Mips_SUBUH_R_QB	= 1624,
    Mips_SUBU_PH	= 1625,
    Mips_SUBU_QB	= 1626,
    Mips_SUBU_S_PH	= 1627,
    Mips_SUBU_S_QB	= 1628,
    Mips_SUBVI_B	= 1629,
    Mips_SUBVI_D	= 1630,
    Mips_SUBVI_H	= 1631,
    Mips_SUBVI_W	= 1632,
    Mips_SUBV_B	= 1633,
    Mips_SUBV_D	= 1634,
    Mips_SUBV_H	= 1635,
    Mips_SUBV_W	= 1636,
    Mips_SUB_MM	= 1637,
    Mips_SUBu	= 1638,
    Mips_SUBu_MM	= 1639,
    Mips_SUXC1	= 1640,
    Mips_SUXC164	= 1641,
    Mips_SUXC1_MM	= 1642,
    Mips_SW	= 1643,
    Mips_SW16_MM	= 1644,
    Mips_SW64	= 1645,
    Mips_SWC1	= 1646,
    Mips_SWC1_MM	= 1647,
    Mips_SWC2	= 1648,
    Mips_SWC2_R6	= 1649,
    Mips_SWC3	= 1650,
    Mips_SWL	= 1651,
    Mips_SWL64	= 1652,
    Mips_SWL_MM	= 1653,
    Mips_SWM16_MM	= 1654,
    Mips_SWM32_MM	= 1655,
    Mips_SWM_MM	= 1656,
    Mips_SWP_MM	= 1657,
    Mips_SWR	= 1658,
    Mips_SWR64	= 1659,
    Mips_SWR_MM	= 1660,
    Mips_SWSP_MM	= 1661,
    Mips_SWXC1	= 1662,
    Mips_SWXC1_MM	= 1663,
    Mips_SW_MM	= 1664,
    Mips_SYNC	= 1665,
    Mips_SYNCI	= 1666,
    Mips_SYNC_MM	= 1667,
    Mips_SYSCALL	= 1668,
    Mips_SYSCALL_MM	= 1669,
    Mips_SZ_B_PSEUDO	= 1670,
    Mips_SZ_D_PSEUDO	= 1671,
    Mips_SZ_H_PSEUDO	= 1672,
    Mips_SZ_V_PSEUDO	= 1673,
    Mips_SZ_W_PSEUDO	= 1674,
    Mips_Save16	= 1675,
    Mips_SaveX16	= 1676,
    Mips_SbRxRyOffMemX16	= 1677,
    Mips_SebRx16	= 1678,
    Mips_SehRx16	= 1679,
    Mips_SelBeqZ	= 1680,
    Mips_SelBneZ	= 1681,
    Mips_SelTBteqZCmp	= 1682,
    Mips_SelTBteqZCmpi	= 1683,
    Mips_SelTBteqZSlt	= 1684,
    Mips_SelTBteqZSlti	= 1685,
    Mips_SelTBteqZSltiu	= 1686,
    Mips_SelTBteqZSltu	= 1687,
    Mips_SelTBtneZCmp	= 1688,
    Mips_SelTBtneZCmpi	= 1689,
    Mips_SelTBtneZSlt	= 1690,
    Mips_SelTBtneZSlti	= 1691,
    Mips_SelTBtneZSltiu	= 1692,
    Mips_SelTBtneZSltu	= 1693,
    Mips_ShRxRyOffMemX16	= 1694,
    Mips_SllX16	= 1695,
    Mips_SllvRxRy16	= 1696,
    Mips_SltCCRxRy16	= 1697,
    Mips_SltRxRy16	= 1698,
    Mips_SltiCCRxImmX16	= 1699,
    Mips_SltiRxImm16	= 1700,
    Mips_SltiRxImmX16	= 1701,
    Mips_SltiuCCRxImmX16	= 1702,
    Mips_SltiuRxImm16	= 1703,
    Mips_SltiuRxImmX16	= 1704,
    Mips_SltuCCRxRy16	= 1705,
    Mips_SltuRxRy16	= 1706,
    Mips_SltuRxRyRz16	= 1707,
    Mips_SraX16	= 1708,
    Mips_SravRxRy16	= 1709,
    Mips_SrlX16	= 1710,
    Mips_SrlvRxRy16	= 1711,
    Mips_SubuRxRyRz16	= 1712,
    Mips_SwRxRyOffMemX16	= 1713,
    Mips_SwRxSpImmX16	= 1714,
    Mips_TAILCALL	= 1715,
    Mips_TAILCALL64_R	= 1716,
    Mips_TAILCALL_R	= 1717,
    Mips_TEQ	= 1718,
    Mips_TEQI	= 1719,
    Mips_TEQI_MM	= 1720,
    Mips_TEQ_MM	= 1721,
    Mips_TGE	= 1722,
    Mips_TGEI	= 1723,
    Mips_TGEIU	= 1724,
    Mips_TGEIU_MM	= 1725,
    Mips_TGEI_MM	= 1726,
    Mips_TGEU	= 1727,
    Mips_TGEU_MM	= 1728,
    Mips_TGE_MM	= 1729,
    Mips_TLBP	= 1730,
    Mips_TLBP_MM	= 1731,
    Mips_TLBR	= 1732,
    Mips_TLBR_MM	= 1733,
    Mips_TLBWI	= 1734,
    Mips_TLBWI_MM	= 1735,
    Mips_TLBWR	= 1736,
    Mips_TLBWR_MM	= 1737,
    Mips_TLT	= 1738,
    Mips_TLTI	= 1739,
    Mips_TLTIU_MM	= 1740,
    Mips_TLTI_MM	= 1741,
    Mips_TLTU	= 1742,
    Mips_TLTU_MM	= 1743,
    Mips_TLT_MM	= 1744,
    Mips_TNE	= 1745,
    Mips_TNEI	= 1746,
    Mips_TNEI_MM	= 1747,
    Mips_TNE_MM	= 1748,
    Mips_TRAP	= 1749,
    Mips_TRUNC_L_D64	= 1750,
    Mips_TRUNC_L_S	= 1751,
    Mips_TRUNC_W_D32	= 1752,
    Mips_TRUNC_W_D64	= 1753,
    Mips_TRUNC_W_MM	= 1754,
    Mips_TRUNC_W_S	= 1755,
    Mips_TRUNC_W_S_MM	= 1756,
    Mips_TTLTIU	= 1757,
    Mips_UDIV	= 1758,
    Mips_UDIV_MM	= 1759,
    Mips_V3MULU	= 1760,
    Mips_VMM0	= 1761,
    Mips_VMULU	= 1762,
    Mips_VSHF_B	= 1763,
    Mips_VSHF_D	= 1764,
    Mips_VSHF_H	= 1765,
    Mips_VSHF_W	= 1766,
    Mips_WAIT	= 1767,
    Mips_WAIT_MM	= 1768,
    Mips_WRDSP	= 1769,
    Mips_WSBH	= 1770,
    Mips_WSBH_MM	= 1771,
    Mips_XOR	= 1772,
    Mips_XOR16_MM	= 1773,
    Mips_XOR64	= 1774,
    Mips_XORI_B	= 1775,
    Mips_XOR_MM	= 1776,
    Mips_XOR_V	= 1777,
    Mips_XOR_V_D_PSEUDO	= 1778,
    Mips_XOR_V_H_PSEUDO	= 1779,
    Mips_XOR_V_W_PSEUDO	= 1780,
    Mips_XORi	= 1781,
    Mips_XORi64	= 1782,
    Mips_XORi_MM	= 1783,
    Mips_XorRxRxRy16	= 1784,
    Mips_INSTRUCTION_LIST_END = 1785
};

#endif // GET_INSTRINFO_ENUM
