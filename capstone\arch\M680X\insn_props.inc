
// These temporary defines keep the following table short and handy.
#define NOG M680X_GRP_INVALID
#define NOR M680X_REG_INVALID

static const insn_props g_insn_props[] = {
	{ NOG, uuuu, NOR, NOR, false, false }, // INVLD
	{ NOG, rmmm, M680X_REG_B, M680X_REG_A, true, false }, // ABA
	{ NOG, rmmm, M680X_REG_B, M680X_REG_X, false, false }, // ABX
	{ NOG, rmmm, M680X_REG_B, M680X_REG_Y, false, false }, // ABY
	{ NOG, mrrr, M680X_REG_A, NOR, true, false }, // ADC
	{ NOG, mrrr, M680X_REG_A, NOR, true, false }, // ADCA
	{ NOG, mrrr, M680X_REG_B, NOR, true, false }, // ADCB
	{ NOG, mrrr, M680X_REG_D, NOR, true, false }, // ADCD
	{ NOG, rmmm, NOR, NOR, true, false }, // ADCR
	{ NOG, mrrr, M680X_REG_A, NOR, true, false }, // ADD
	{ NOG, mrrr, M680X_REG_A, NOR, true, false }, // ADDA
	{ NOG, mrrr, M680X_REG_B, NOR, true, false }, // ADDB
	{ NOG, mrrr, M680X_REG_D, NOR, true, false }, // ADDD
	{ NOG, mrrr, M680X_REG_E, NOR, true, false }, // ADDE
	{ NOG, mrrr, M680X_REG_F, NOR, true, false }, // ADDF
	{ NOG, rmmm, NOR, NOR, true, false }, // ADDR
	{ NOG, mrrr, M680X_REG_W, NOR, true, false }, // ADDW
	{ NOG, rmmm, NOR, NOR, true, false }, // AIM
	{ NOG, mrrr, M680X_REG_S, NOR, false, false }, // AIS
	{ NOG, mrrr, M680X_REG_HX, NOR, false, false }, // AIX
	{ NOG, mrrr, M680X_REG_A, NOR, true, false }, // AND
	{ NOG, mrrr, M680X_REG_A, NOR, true, false }, // ANDA
	{ NOG, mrrr, M680X_REG_B, NOR, true, false }, // ANDB
	{ NOG, mrrr, M680X_REG_CC, NOR, true, false }, // ANDCC
	{ NOG, mrrr, M680X_REG_D, NOR, true, false }, // ANDD
	{ NOG, rmmm, NOR, NOR, true, false }, // ANDR
	{ NOG, mrrr, NOR, NOR, true, false }, // ASL
	{ NOG, mrrr, M680X_REG_A, NOR, true, false }, // ASLA
	{ NOG, mrrr, M680X_REG_B, NOR, true, false }, // ASLB
	{ NOG, mrrr, M680X_REG_D, NOR, true, false }, // ASLD
	{ NOG, mrrr, NOR, NOR, true, false }, // ASR
	{ NOG, mrrr, M680X_REG_A, NOR, true, false }, // ASRA
	{ NOG, mrrr, M680X_REG_B, NOR, true, false }, // ASRB
	{ NOG, mrrr, M680X_REG_D, NOR, true, false }, // ASRD
	{ NOG, mrrr, M680X_REG_X, NOR, true, false }, // ASRX
	{ NOG, mrrr, NOR, NOR, false, false }, // BAND
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // BCC
	{ NOG, mrrr, NOR, NOR, true, false }, // BCLR
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // BCS
	{ NOG, mrrr, NOR, NOR, false, false }, // BEOR
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // BEQ
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // BGE
	{ NOG, uuuu, NOR, NOR, false, false }, // BGND
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // BGT
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // BHCC
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // BHCS
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // BHI
	{ NOG, mrrr, NOR, NOR, false, false }, // BIAND
	{ NOG, mrrr, NOR, NOR, false, false }, // BIEOR
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // BIH
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // BIL
	{ NOG, mrrr, NOR, NOR, false, false }, // BIOR
	{ NOG, rrrr, M680X_REG_A, NOR, true, false }, // BIT
	{ NOG, rrrr, M680X_REG_A, NOR, true, false }, // BITA
	{ NOG, rrrr, M680X_REG_B, NOR, true, false }, // BITB
	{ NOG, rrrr, M680X_REG_D, NOR, true, false }, // BITD
	{ NOG, rrrr, M680X_REG_MD, NOR, true, false }, // BITMD
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // BLE
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // BLS
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // BLT
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // BMC
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // BMI
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // BMS
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // BNE
	{ NOG, mrrr, NOR, NOR, false, false }, // BOR
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // BPL
	{ M680X_GRP_JUMP, rruu, NOR, NOR, false, false }, // BRCLR
	{ M680X_GRP_JUMP, rruu, NOR, NOR, false, false }, // BRSET
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // BRA
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // BRN never branches
	{ NOG, mrrr, NOR, NOR, true, false }, // BSET
	{ M680X_GRP_CALL, uuuu, NOR, NOR, false, true }, // BSR
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // BVC
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // BVS
	{ M680X_GRP_CALL, uuuu, NOR, NOR, false, true }, // CALL
	{ NOG, rrrr, M680X_REG_B, M680X_REG_A, true, false }, // CBA
	{ M680X_GRP_JUMP, rruu, M680X_REG_A, NOR, false, false }, // CBEQ
	{ M680X_GRP_JUMP, rruu, M680X_REG_A, NOR, false, false }, // CBEQA
	{ M680X_GRP_JUMP, rruu, M680X_REG_X, NOR, false, false }, // CBEQX
	{ NOG, uuuu, NOR, NOR, true, false }, // CLC
	{ NOG, uuuu, NOR, NOR, true, false }, // CLI
	{ NOG, wrrr, NOR, NOR, true, false }, // CLR
	{ NOG, wrrr, M680X_REG_A, NOR, true, false }, // CLRA
	{ NOG, wrrr, M680X_REG_B, NOR, true, false }, // CLRB
	{ NOG, wrrr, M680X_REG_D, NOR, true, false }, // CLRD
	{ NOG, wrrr, M680X_REG_E, NOR, true, false }, // CLRE
	{ NOG, wrrr, M680X_REG_F, NOR, true, false }, // CLRF
	{ NOG, wrrr, M680X_REG_H, NOR, true, false }, // CLRH
	{ NOG, wrrr, M680X_REG_W, NOR, true, false }, // CLRW
	{ NOG, wrrr, M680X_REG_X, NOR, true, false }, // CLRX
	{ NOG, uuuu, NOR, NOR, true, false }, // CLV
	{ NOG, rrrr, M680X_REG_A, NOR, true, false }, // CMP
	{ NOG, rrrr, M680X_REG_A, NOR, true, false }, // CMPA
	{ NOG, rrrr, M680X_REG_B, NOR, true, false }, // CMPB
	{ NOG, rrrr, M680X_REG_D, NOR, true, false }, // CMPD
	{ NOG, rrrr, M680X_REG_E, NOR, true, false }, // CMPE
	{ NOG, rrrr, M680X_REG_F, NOR, true, false }, // CMPF
	{ NOG, rrrr, NOR, NOR, true, false }, // CMPR
	{ NOG, rrrr, M680X_REG_S, NOR, true, false }, // CMPS
	{ NOG, rrrr, M680X_REG_U, NOR, true, false }, // CMPU
	{ NOG, rrrr, M680X_REG_W, NOR, true, false }, // CMPW
	{ NOG, rrrr, M680X_REG_X, NOR, true, false }, // CMPX
	{ NOG, rrrr, M680X_REG_Y, NOR, true, false }, // CMPY
	{ NOG, mrrr, NOR, NOR, true, false }, // COM
	{ NOG, mrrr, M680X_REG_A, NOR, true, false }, // COMA
	{ NOG, mrrr, M680X_REG_B, NOR, true, false }, // COMB
	{ NOG, mrrr, M680X_REG_D, NOR, true, false }, // COMD
	{ NOG, mrrr, M680X_REG_E, NOR, true, false }, // COME
	{ NOG, mrrr, M680X_REG_F, NOR, true, false }, // COMF
	{ NOG, mrrr, M680X_REG_W, NOR, true, false }, // COMW
	{ NOG, mrrr, M680X_REG_X, NOR, true, false }, // COMX
	{ NOG, rrrr, M680X_REG_D, NOR, true, false }, // CPD
	{ NOG, rrrr, M680X_REG_HX, NOR, true, false }, // CPHX
	{ NOG, rrrr, M680X_REG_S, NOR, true, false }, // CPS
	{ NOG, rrrr, M680X_REG_X, NOR, true, false }, // CPX
	{ NOG, rrrr, M680X_REG_Y, NOR, true, false }, // CPY
	{ NOG, mrrr, NOR, NOR, true, true }, // CWAI
	{ NOG, mrrr, NOR, NOR, true, true }, // DAA
	{ M680X_GRP_JUMP, muuu, NOR, NOR, false, false }, // DBEQ
	{ M680X_GRP_JUMP, muuu, NOR, NOR, false, false }, // DBNE
	{ M680X_GRP_JUMP, muuu, NOR, NOR, false, false }, // DBNZ
	{ M680X_GRP_JUMP, muuu, M680X_REG_A, NOR, false, false }, // DBNZA
	{ M680X_GRP_JUMP, muuu, M680X_REG_X, NOR, false, false }, // DBNZX
	{ NOG, mrrr, NOR, NOR, true, false }, // DEC
	{ NOG, mrrr, M680X_REG_A, NOR, true, false }, // DECA
	{ NOG, mrrr, M680X_REG_B, NOR, true, false }, // DECB
	{ NOG, mrrr, M680X_REG_D, NOR, true, false }, // DECD
	{ NOG, mrrr, M680X_REG_E, NOR, true, false }, // DECE
	{ NOG, mrrr, M680X_REG_F, NOR, true, false }, // DECF
	{ NOG, mrrr, M680X_REG_W, NOR, true, false }, // DECW
	{ NOG, mrrr, M680X_REG_X, NOR, true, false }, // DECX
	{ NOG, mrrr, M680X_REG_S, NOR, false, false }, // DES
	{ NOG, mrrr, M680X_REG_X, NOR, true, false }, // DEX
	{ NOG, mrrr, M680X_REG_Y, NOR, true, false }, // DEY
	{ NOG, mmrr, NOR, NOR, true, true }, // DIV
	{ NOG, mrrr, M680X_REG_D, NOR, true, false }, // DIVD
	{ NOG, mrrr, M680X_REG_Q, NOR, true, false }, // DIVQ
	{ NOG, mmrr, NOR, NOR, true, true }, // EDIV
	{ NOG, mmrr, NOR, NOR, true, true }, // EDIVS
	{ NOG, rmmm, NOR, NOR, true, false }, // EIM
	{ NOG, mrrr, NOR, NOR, true, true }, // EMACS
	{ NOG, mrrr, M680X_REG_D, NOR, true, false }, // EMAXD
	{ NOG, mrrr, NOR, NOR, true, true }, // EMAXM
	{ NOG, mrrr, M680X_REG_D, NOR, true, false }, // EMIND
	{ NOG, mrrr, NOR, NOR, true, true }, // EMINM
	{ NOG, mmrr, NOR, NOR, true, true }, // EMUL
	{ NOG, mmrr, NOR, NOR, true, true }, // EMULS
	{ NOG, mrrr, M680X_REG_A, NOR, true, false }, // EOR
	{ NOG, mrrr, M680X_REG_A, NOR, true, false }, // EORA
	{ NOG, mrrr, M680X_REG_B, NOR, true, false }, // EORB
	{ NOG, mrrr, M680X_REG_D, NOR, true, false }, // EORD
	{ NOG, rmmm, NOR, NOR, true, false }, // EORR
	{ NOG, rmmm, NOR, NOR, true, true }, // ETBL
	{ NOG, mmmm, NOR, NOR, false, false }, // EXG
	{ NOG, mmmm, NOR, NOR, true, true }, // FDIV
	{ M680X_GRP_JUMP, muuu, NOR, NOR, false, false }, // IBEQ
	{ M680X_GRP_JUMP, muuu, NOR, NOR, false, false }, // IBNE
	{ NOG, mmmm, NOR, NOR, true, true }, // IDIV
	{ NOG, mmmm, NOR, NOR, true, true }, // IDIVS
	{ NOG, uuuu, NOR, NOR, false, false }, // ILLGL
	{ NOG, mrrr, NOR, NOR, true, false }, // INC
	{ NOG, mrrr, M680X_REG_A, NOR, true, false }, // INCA
	{ NOG, mrrr, M680X_REG_B, NOR, true, false }, // INCB
	{ NOG, mrrr, M680X_REG_D, NOR, true, false }, // INCD
	{ NOG, mrrr, M680X_REG_E, NOR, true, false }, // INCE
	{ NOG, mrrr, M680X_REG_F, NOR, true, false }, // INCF
	{ NOG, mrrr, M680X_REG_W, NOR, true, false }, // INCW
	{ NOG, mrrr, M680X_REG_X, NOR, true, false }, // INCX
	{ NOG, mrrr, M680X_REG_S, NOR, false, false }, // INS
	{ NOG, mrrr, M680X_REG_X, NOR, true, false }, // INX
	{ NOG, mrrr, M680X_REG_Y, NOR, true, false }, // INY
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // JMP
	{ M680X_GRP_CALL, uuuu, NOR, NOR, false, true }, // JSR
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // LBCC
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // LBCS
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // LBEQ
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // LBGE
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // LBGT
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // LBHI
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // LBLE
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // LBLS
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // LBLT
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // LBMI
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // LBNE
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // LBPL
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // LBRA
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // LBRN never branches
	{ M680X_GRP_CALL, uuuu, NOR, NOR, false, true }, // LBSR
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // LBVC
	{ M680X_GRP_JUMP, uuuu, NOR, NOR, false, false }, // LBVS
	{ NOG, wrrr, M680X_REG_A, NOR, true, false }, // LDA
	{ NOG, wrrr, M680X_REG_A, NOR, true, false }, // LDAA
	{ NOG, wrrr, M680X_REG_B, NOR, true, false }, // LDAB
	{ NOG, wrrr, M680X_REG_B, NOR, true, false }, // LDB
	{ NOG, mrrr, NOR, NOR, false, false }, // LDBT
	{ NOG, wrrr, M680X_REG_D, NOR, true, false }, // LDD
	{ NOG, wrrr, M680X_REG_E, NOR, true, false }, // LDE
	{ NOG, wrrr, M680X_REG_F, NOR, true, false }, // LDF
	{ NOG, wrrr, M680X_REG_HX, NOR, true, false }, // LDHX
	{ NOG, mrrr, M680X_REG_MD, NOR, false, false }, // LDMD
	{ NOG, wrrr, M680X_REG_Q, NOR, true, false }, // LDQ
	{ NOG, wrrr, M680X_REG_S, NOR, true, false }, // LDS
	{ NOG, wrrr, M680X_REG_U, NOR, true, false }, // LDU
	{ NOG, wrrr, M680X_REG_W, NOR, true, false }, // LDW
	{ NOG, wrrr, M680X_REG_X, NOR, true, false }, // LDX
	{ NOG, wrrr, M680X_REG_Y, NOR, true, false }, // LDY
	{ NOG, wrrr, M680X_REG_S, NOR, false, false }, // LEAS
	{ NOG, wrrr, M680X_REG_U, NOR, false, false }, // LEAU
	{ NOG, wrrr, M680X_REG_X, NOR, false, false }, // LEAX
	{ NOG, wrrr, M680X_REG_Y, NOR, false, false }, // LEAY
	{ NOG, mrrr, NOR, NOR, true, false }, // LSL
	{ NOG, mrrr, M680X_REG_A, NOR, true, false }, // LSLA
	{ NOG, mrrr, M680X_REG_B, NOR, true, false }, // LSLB
	{ NOG, mrrr, M680X_REG_D, NOR, true, false }, // LSLD
	{ NOG, mrrr, M680X_REG_X, NOR, true, false }, // LSLX
	{ NOG, mrrr, NOR, NOR, true, false }, // LSR
	{ NOG, mrrr, M680X_REG_A, NOR, true, false }, // LSRA
	{ NOG, mrrr, M680X_REG_B, NOR, true, false }, // LSRB
	{ NOG, mrrr, M680X_REG_D, NOR, true, false }, // LSRD
	{ NOG, mrrr, M680X_REG_W, NOR, true, false }, // LSRW
	{ NOG, mrrr, M680X_REG_X, NOR, true, false }, // LSRX
	{ NOG, mrrr, M680X_REG_A, NOR, true, false }, // MAXA
	{ NOG, mrrr, NOR, NOR, true, true }, // MAXM
	{ NOG, mmrr, NOR, NOR, true, true }, // MEM
	{ NOG, mrrr, M680X_REG_A, NOR, true, false }, // MINA
	{ NOG, mrrr, NOR, NOR, true, true }, // MINM
	{ NOG, rwww, NOR, NOR, true, false }, // MOV
	{ NOG, rwww, NOR, NOR, false, false }, // MOVB
	{ NOG, rwww, NOR, NOR, false, false }, // MOVW
	{ NOG, mmmm, NOR, NOR, true, true }, // MUL
	{ NOG, mwrr, M680X_REG_D, NOR, true, true }, // MULD
	{ NOG, mrrr, NOR, NOR, true, false }, // NEG
	{ NOG, mrrr, M680X_REG_A, NOR, true, false }, // NEGA
	{ NOG, mrrr, M680X_REG_B, NOR, true, false }, // NEGB
	{ NOG, mrrr, M680X_REG_D, NOR, true, false }, // NEGD
	{ NOG, mrrr, M680X_REG_X, NOR, true, false }, // NEGX
	{ NOG, uuuu, NOR, NOR, false, false }, // NOP
	{ NOG, mrrr, M680X_REG_A, NOR, true, false }, // NSA
	{ NOG, rmmm, NOR, NOR, true, false }, // OIM
	{ NOG, mrrr, M680X_REG_A, NOR, true, false }, // ORA
	{ NOG, mrrr, M680X_REG_A, NOR, true, false }, // ORAA
	{ NOG, mrrr, M680X_REG_B, NOR, true, false }, // ORAB
	{ NOG, mrrr, M680X_REG_B, NOR, true, false }, // ORB
	{ NOG, mrrr, M680X_REG_CC, NOR, true, false }, // ORCC
	{ NOG, mrrr, M680X_REG_D, NOR, true, false }, // ORD
	{ NOG, rmmm, NOR, NOR, true, false }, // ORR
	{ NOG, rmmm, M680X_REG_A, NOR, false, true }, // PSHA
	{ NOG, rmmm, M680X_REG_B, NOR, false, true }, // PSHB
	{ NOG, rmmm, M680X_REG_CC, NOR, false, true }, // PSHC
	{ NOG, rmmm, M680X_REG_D, NOR, false, true }, // PSHD
	{ NOG, rmmm, M680X_REG_H, NOR, false, true }, // PSHH
	{ NOG, mrrr, M680X_REG_S, NOR, false, false }, // PSHS
	{ NOG, mrrr, M680X_REG_S, M680X_REG_W, false, false }, // PSHSW
	{ NOG, mrrr, M680X_REG_U, NOR, false, false }, // PSHU
	{ NOG, mrrr, M680X_REG_U, M680X_REG_W, false, false }, // PSHUW
	{ NOG, rmmm, M680X_REG_X, NOR, false, true }, // PSHX
	{ NOG, rmmm, M680X_REG_Y, NOR, false, true }, // PSHY
	{ NOG, wmmm, M680X_REG_A, NOR, false, true }, // PULA
	{ NOG, wmmm, M680X_REG_B, NOR, false, true }, // PULB
	{ NOG, wmmm, M680X_REG_CC, NOR, false, true }, // PULC
	{ NOG, wmmm, M680X_REG_D, NOR, false, true }, // PULD
	{ NOG, wmmm, M680X_REG_H, NOR, false, true }, // PULH
	{ NOG, mwww, M680X_REG_S, NOR, false, false }, // PULS
	{ NOG, mwww, M680X_REG_S, M680X_REG_W, false, false }, // PULSW
	{ NOG, mwww, M680X_REG_U, NOR, false, false }, // PULU
	{ NOG, mwww, M680X_REG_U, M680X_REG_W, false, false }, // PULUW
	{ NOG, wmmm, M680X_REG_X, NOR, false, true }, // PULX
	{ NOG, wmmm, M680X_REG_Y, NOR, false, true }, // PULY
	{ NOG, mmrr, NOR, NOR, true, true }, // REV
	{ NOG, mmmm, NOR, NOR, true, true }, // REVW
	{ NOG, mrrr, NOR, NOR, true, false }, // ROL
	{ NOG, mrrr, M680X_REG_A, NOR, true, false }, // ROLA
	{ NOG, mrrr, M680X_REG_B, NOR, true, false }, // ROLB
	{ NOG, mrrr, M680X_REG_D, NOR, true, false }, // ROLD
	{ NOG, mrrr, M680X_REG_W, NOR, true, false }, // ROLW
	{ NOG, mrrr, M680X_REG_X, NOR, true, false }, // ROLX
	{ NOG, mrrr, NOR, NOR, true, false }, // ROR
	{ NOG, mrrr, M680X_REG_A, NOR, true, false }, // RORA
	{ NOG, mrrr, M680X_REG_B, NOR, true, false }, // RORB
	{ NOG, mrrr, M680X_REG_D, NOR, true, false }, // RORD
	{ NOG, mrrr, M680X_REG_W, NOR, true, false }, // RORW
	{ NOG, mrrr, M680X_REG_X, NOR, true, false }, // RORX
	{ NOG, wrrr, M680X_REG_S, NOR, false, false }, // RSP
	{ M680X_GRP_RET, mwww, NOR, NOR, false, true }, // RTC
	{ M680X_GRP_IRET, mwww, NOR, NOR, false, true }, // RTI
	{ M680X_GRP_RET, mwww, NOR, NOR, false, true }, // RTS
	{ NOG, rmmm, M680X_REG_B, M680X_REG_A, true, false }, // SBA
	{ NOG, mrrr, M680X_REG_A, NOR, true, false }, // SBC
	{ NOG, mrrr, M680X_REG_A, NOR, true, false }, // SBCA
	{ NOG, mrrr, M680X_REG_B, NOR, true, false }, // SBCB
	{ NOG, mrrr, M680X_REG_D, NOR, true, false }, // SBCD
	{ NOG, rmmm, NOR, NOR, true, false }, // SBCR
	{ NOG, uuuu, NOR, NOR, true, false }, // SEC
	{ NOG, uuuu, NOR, NOR, true, false }, // SEI
	{ NOG, uuuu, NOR, NOR, true, false }, // SEV
	{ NOG, wrrr, NOR, NOR, true, true }, // SEX
	{ NOG, rwww, M680X_REG_W, NOR, true, true }, // SEXW
	{ NOG, uuuu, NOR, NOR, false, false }, // SLP
	{ NOG, rwww, M680X_REG_A, NOR, true, false }, // STA
	{ NOG, rwww, M680X_REG_A, NOR, true, false }, // STAA
	{ NOG, rwww, M680X_REG_B, NOR, true, false }, // STAB
	{ NOG, rwww, M680X_REG_B, NOR, true, false }, // STB
	{ NOG, rrrm, NOR, NOR, false, false }, // STBT
	{ NOG, rwww, M680X_REG_D, NOR, true, false }, // STD
	{ NOG, rwww, M680X_REG_E, NOR, true, false }, // STE
	{ NOG, rwww, M680X_REG_F, NOR, true, false }, // STF
	{ NOG, uuuu, NOR, NOR, false, false }, // STOP
	{ NOG, rwww, M680X_REG_HX, NOR, true, false }, // STHX
	{ NOG, rwww, M680X_REG_Q, NOR, true, false }, // STQ
	{ NOG, rwww, M680X_REG_S, NOR, true, false }, // STS
	{ NOG, rwww, M680X_REG_U, NOR, true, false }, // STU
	{ NOG, rwww, M680X_REG_W, NOR, true, false }, // STW
	{ NOG, rwww, M680X_REG_X, NOR, true, false }, // STX
	{ NOG, rwww, M680X_REG_Y, NOR, true, false }, // STY
	{ NOG, mrrr, M680X_REG_A, NOR, true, false }, // SUB
	{ NOG, mrrr, M680X_REG_A, NOR, true, false }, // SUBA
	{ NOG, mrrr, M680X_REG_B, NOR, true, false }, // SUBB
	{ NOG, mrrr, M680X_REG_D, NOR, true, false }, // SUBD
	{ NOG, mrrr, M680X_REG_E, NOR, true, false }, // SUBE
	{ NOG, mrrr, M680X_REG_F, NOR, true, false }, // SUBF
	{ NOG, rmmm, NOR, NOR, true, false }, // SUBR
	{ NOG, mrrr, M680X_REG_W, NOR, true, false }, // SUBW
	{ M680X_GRP_INT, mmrr, NOR, NOR, true, true }, // SWI
	{ M680X_GRP_INT, mmrr, NOR, NOR, true, true }, // SWI2
	{ M680X_GRP_INT, mmrr, NOR, NOR, true, true }, // SWI3
	{ NOG, uuuu, NOR, NOR, false, false }, // SYNC
	{ NOG, rwww, M680X_REG_A, M680X_REG_B, true, false }, // TAB
	{ NOG, rwww, M680X_REG_A, M680X_REG_CC, false, false }, // TAP
	{ NOG, rwww, M680X_REG_A, M680X_REG_X, false, false }, // TAX
	{ NOG, rwww, M680X_REG_B, M680X_REG_A, true, false }, // TBA
	{ M680X_GRP_JUMP, muuu, NOR, NOR, false, false }, // TBEQ
	{ NOG, rmmm, NOR, NOR, true, true }, // TBL
	{ M680X_GRP_JUMP, muuu, NOR, NOR, false, false }, // TBNE
	{ NOG, uuuu, NOR, NOR, false, false }, // TEST
	{ NOG, rwww, NOR, NOR, false, false }, // TFM
	{ NOG, rwww, NOR, NOR, false, false }, // TFR
	{ NOG, rrrr, NOR, NOR, true, false }, // TIM
	{ NOG, rwww, M680X_REG_CC, M680X_REG_A, false, false }, // TPA
	{ NOG, rrrr, NOR, NOR, true, false }, // TST
	{ NOG, rrrr, M680X_REG_A, NOR, true, false }, // TSTA
	{ NOG, rrrr, M680X_REG_B, NOR, true, false }, // TSTB
	{ NOG, rrrr, M680X_REG_D, NOR, true, false }, // TSTD
	{ NOG, rrrr, M680X_REG_E, NOR, true, false }, // TSTE
	{ NOG, rrrr, M680X_REG_F, NOR, true, false }, // TSTF
	{ NOG, rrrr, M680X_REG_W, NOR, true, false }, // TSTW
	{ NOG, rrrr, M680X_REG_X, NOR, true, false }, // TSTX
	{ NOG, rwww, M680X_REG_S, M680X_REG_HX, false, false }, // TSX
	{ NOG, rwww, M680X_REG_S, M680X_REG_Y, false, false }, // TSY
	{ NOG, rwww, M680X_REG_X, M680X_REG_A, false, false }, // TXA
	{ NOG, rwww, M680X_REG_HX, M680X_REG_S, false, false }, // TXS
	{ NOG, rwww, M680X_REG_Y, M680X_REG_S, false, false }, // TYS
	{ NOG, mrrr, NOR, NOR, true, true }, // WAI
	{ NOG, uuuu, NOR, NOR, true, false }, // WAIT
	{ NOG, uuuu, NOR, NOR, true, true }, // WAV
	{ NOG, uuuu, NOR, NOR, true, true }, // WAVR
	{ NOG, mmmm, M680X_REG_D, M680X_REG_X, false, false }, // XGDX
	{ NOG, mmmm, M680X_REG_D, M680X_REG_Y, false, false }, // XGDY
};
#undef NOR
#undef NOG

