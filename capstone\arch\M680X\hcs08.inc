
// Additional instructions only supported on HCS08
static const inst_pageX g_hcs08_inst_overlay_table[] = {
	{ 0x32, M680X_INS_LDHX, ext_hid, inh_hid },
	{ 0x3e, M680X_INS_CPHX, ext_hid, inh_hid },
	{ 0x82, M680X_INS_BGND, inh_hid, inh_hid },
	{ 0x96, M680X_INS_STHX, ext_hid, inh_hid },
};

// HCS08 PAGE2 instructions (prefix 0x9E)
static const inst_pageX g_hcs08_inst_page2_table[] = {
	{ 0x60, M680X_INS_NEG, idxS_hid, inh_hid },
	{ 0x61, M680X_INS_CBEQ, idxS_hid,rel8_hid },
	{ 0x63, M680X_INS_COM, idxS_hid, inh_hid },
	{ 0x64, M680X_INS_LSR, idxS_hid, inh_hid },
	{ 0x66, M680X_INS_ROR, idxS_hid, inh_hid },
	{ 0x67, M680X_INS_ASR, idxS_hid, inh_hid },
	{ 0x68, M680X_INS_LSL, idxS_hid, inh_hid },
	{ 0x69, M680X_INS_ROL, idxS_hid, inh_hid },
	{ 0x6a, M680X_INS_DEC, idxS_hid, inh_hid },
	{ 0x6b, M680X_INS_DBNZ, idxS_hid,rel8_hid },
	{ 0x6c, M680X_INS_INC, idxS_hid, inh_hid },
	{ 0x6d, M680X_INS_TST, idxS_hid, inh_hid },
	{ 0x6f, M680X_INS_CLR, idxS_hid, inh_hid },
	{ 0xae, M680X_INS_LDHX, idxX0_hid, inh_hid },
	{ 0xbe, M680X_INS_LDHX, idxX16_hid, inh_hid },
	{ 0xce, M680X_INS_LDHX, idxX_hid, inh_hid },
	{ 0xd0, M680X_INS_SUB, idxS16_hid, inh_hid },
	{ 0xd1, M680X_INS_CMP, idxS16_hid, inh_hid },
	{ 0xd2, M680X_INS_SBC, idxS16_hid, inh_hid },
	{ 0xd3, M680X_INS_CPX, idxS16_hid, inh_hid },
	{ 0xd4, M680X_INS_AND, idxS16_hid, inh_hid },
	{ 0xd5, M680X_INS_BIT, idxS16_hid, inh_hid },
	{ 0xd6, M680X_INS_LDA, idxS16_hid, inh_hid },
	{ 0xd7, M680X_INS_STA, idxS16_hid, inh_hid },
	{ 0xd8, M680X_INS_EOR, idxS16_hid, inh_hid },
	{ 0xd9, M680X_INS_ADC, idxS16_hid, inh_hid },
	{ 0xda, M680X_INS_ORA, idxS16_hid, inh_hid },
	{ 0xdb, M680X_INS_ADD, idxS16_hid, inh_hid },
	{ 0xde, M680X_INS_LDX, idxS16_hid, inh_hid },
	{ 0xdf, M680X_INS_STX, idxS16_hid, inh_hid },
	{ 0xe0, M680X_INS_SUB, idxS_hid, inh_hid },
	{ 0xe1, M680X_INS_CMP, idxS_hid, inh_hid },
	{ 0xe2, M680X_INS_SBC, idxS_hid, inh_hid },
	{ 0xe3, M680X_INS_CPX, idxS_hid, inh_hid },
	{ 0xe4, M680X_INS_AND, idxS_hid, inh_hid },
	{ 0xe5, M680X_INS_BIT, idxS_hid, inh_hid },
	{ 0xe6, M680X_INS_LDA, idxS_hid, inh_hid },
	{ 0xe7, M680X_INS_STA, idxS_hid, inh_hid },
	{ 0xe8, M680X_INS_EOR, idxS_hid, inh_hid },
	{ 0xe9, M680X_INS_ADC, idxS_hid, inh_hid },
	{ 0xea, M680X_INS_ORA, idxS_hid, inh_hid },
	{ 0xeb, M680X_INS_ADD, idxS_hid, inh_hid },
	{ 0xee, M680X_INS_LDX, idxS_hid, inh_hid },
	{ 0xef, M680X_INS_STX, idxS_hid, inh_hid },
	{ 0xf3, M680X_INS_CPHX, idxS_hid, inh_hid },
	{ 0xfe, M680X_INS_LDHX, idxS_hid, inh_hid },
	{ 0xff, M680X_INS_STHX, idxS_hid, inh_hid },
};

