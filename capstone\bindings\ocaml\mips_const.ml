(* For Capstone Engine. AUTO-GENERATED FILE, DO NOT EDIT [mips_const.ml] *)

(* Operand type for instruction's operands *)

let _MIPS_OP_INVALID = 0;;
let _MIPS_OP_REG = 1;;
let _MIPS_OP_IMM = 2;;
let _MIPS_OP_MEM = 3;;

(* MIPS registers *)

let _MIPS_REG_INVALID = 0;;

(* General purpose registers *)
let _MIPS_REG_PC = 1;;
let _MIPS_REG_0 = 2;;
let _MIPS_REG_1 = 3;;
let _MIPS_REG_2 = 4;;
let _MIPS_REG_3 = 5;;
let _MIPS_REG_4 = 6;;
let _MIPS_REG_5 = 7;;
let _MIPS_REG_6 = 8;;
let _MIPS_REG_7 = 9;;
let _MIPS_REG_8 = 10;;
let _MIPS_REG_9 = 11;;
let _MIPS_REG_10 = 12;;
let _MIPS_REG_11 = 13;;
let _MIPS_REG_12 = 14;;
let _MIPS_REG_13 = 15;;
let _MIPS_REG_14 = 16;;
let _MIPS_REG_15 = 17;;
let _MIPS_REG_16 = 18;;
let _MIPS_REG_17 = 19;;
let _MIPS_REG_18 = 20;;
let _MIPS_REG_19 = 21;;
let _MIPS_REG_20 = 22;;
let _MIPS_REG_21 = 23;;
let _MIPS_REG_22 = 24;;
let _MIPS_REG_23 = 25;;
let _MIPS_REG_24 = 26;;
let _MIPS_REG_25 = 27;;
let _MIPS_REG_26 = 28;;
let _MIPS_REG_27 = 29;;
let _MIPS_REG_28 = 30;;
let _MIPS_REG_29 = 31;;
let _MIPS_REG_30 = 32;;
let _MIPS_REG_31 = 33;;

(* DSP registers *)
let _MIPS_REG_DSPCCOND = 34;;
let _MIPS_REG_DSPCARRY = 35;;
let _MIPS_REG_DSPEFI = 36;;
let _MIPS_REG_DSPOUTFLAG = 37;;
let _MIPS_REG_DSPOUTFLAG16_19 = 38;;
let _MIPS_REG_DSPOUTFLAG20 = 39;;
let _MIPS_REG_DSPOUTFLAG21 = 40;;
let _MIPS_REG_DSPOUTFLAG22 = 41;;
let _MIPS_REG_DSPOUTFLAG23 = 42;;
let _MIPS_REG_DSPPOS = 43;;
let _MIPS_REG_DSPSCOUNT = 44;;

(* ACC registers *)
let _MIPS_REG_AC0 = 45;;
let _MIPS_REG_AC1 = 46;;
let _MIPS_REG_AC2 = 47;;
let _MIPS_REG_AC3 = 48;;

(* COP registers *)
let _MIPS_REG_CC0 = 49;;
let _MIPS_REG_CC1 = 50;;
let _MIPS_REG_CC2 = 51;;
let _MIPS_REG_CC3 = 52;;
let _MIPS_REG_CC4 = 53;;
let _MIPS_REG_CC5 = 54;;
let _MIPS_REG_CC6 = 55;;
let _MIPS_REG_CC7 = 56;;

(* FPU registers *)
let _MIPS_REG_F0 = 57;;
let _MIPS_REG_F1 = 58;;
let _MIPS_REG_F2 = 59;;
let _MIPS_REG_F3 = 60;;
let _MIPS_REG_F4 = 61;;
let _MIPS_REG_F5 = 62;;
let _MIPS_REG_F6 = 63;;
let _MIPS_REG_F7 = 64;;
let _MIPS_REG_F8 = 65;;
let _MIPS_REG_F9 = 66;;
let _MIPS_REG_F10 = 67;;
let _MIPS_REG_F11 = 68;;
let _MIPS_REG_F12 = 69;;
let _MIPS_REG_F13 = 70;;
let _MIPS_REG_F14 = 71;;
let _MIPS_REG_F15 = 72;;
let _MIPS_REG_F16 = 73;;
let _MIPS_REG_F17 = 74;;
let _MIPS_REG_F18 = 75;;
let _MIPS_REG_F19 = 76;;
let _MIPS_REG_F20 = 77;;
let _MIPS_REG_F21 = 78;;
let _MIPS_REG_F22 = 79;;
let _MIPS_REG_F23 = 80;;
let _MIPS_REG_F24 = 81;;
let _MIPS_REG_F25 = 82;;
let _MIPS_REG_F26 = 83;;
let _MIPS_REG_F27 = 84;;
let _MIPS_REG_F28 = 85;;
let _MIPS_REG_F29 = 86;;
let _MIPS_REG_F30 = 87;;
let _MIPS_REG_F31 = 88;;
let _MIPS_REG_FCC0 = 89;;
let _MIPS_REG_FCC1 = 90;;
let _MIPS_REG_FCC2 = 91;;
let _MIPS_REG_FCC3 = 92;;
let _MIPS_REG_FCC4 = 93;;
let _MIPS_REG_FCC5 = 94;;
let _MIPS_REG_FCC6 = 95;;
let _MIPS_REG_FCC7 = 96;;

(* AFPR128 *)
let _MIPS_REG_W0 = 97;;
let _MIPS_REG_W1 = 98;;
let _MIPS_REG_W2 = 99;;
let _MIPS_REG_W3 = 100;;
let _MIPS_REG_W4 = 101;;
let _MIPS_REG_W5 = 102;;
let _MIPS_REG_W6 = 103;;
let _MIPS_REG_W7 = 104;;
let _MIPS_REG_W8 = 105;;
let _MIPS_REG_W9 = 106;;
let _MIPS_REG_W10 = 107;;
let _MIPS_REG_W11 = 108;;
let _MIPS_REG_W12 = 109;;
let _MIPS_REG_W13 = 110;;
let _MIPS_REG_W14 = 111;;
let _MIPS_REG_W15 = 112;;
let _MIPS_REG_W16 = 113;;
let _MIPS_REG_W17 = 114;;
let _MIPS_REG_W18 = 115;;
let _MIPS_REG_W19 = 116;;
let _MIPS_REG_W20 = 117;;
let _MIPS_REG_W21 = 118;;
let _MIPS_REG_W22 = 119;;
let _MIPS_REG_W23 = 120;;
let _MIPS_REG_W24 = 121;;
let _MIPS_REG_W25 = 122;;
let _MIPS_REG_W26 = 123;;
let _MIPS_REG_W27 = 124;;
let _MIPS_REG_W28 = 125;;
let _MIPS_REG_W29 = 126;;
let _MIPS_REG_W30 = 127;;
let _MIPS_REG_W31 = 128;;
let _MIPS_REG_HI = 129;;
let _MIPS_REG_LO = 130;;
let _MIPS_REG_P0 = 131;;
let _MIPS_REG_P1 = 132;;
let _MIPS_REG_P2 = 133;;
let _MIPS_REG_MPL0 = 134;;
let _MIPS_REG_MPL1 = 135;;
let _MIPS_REG_MPL2 = 136;;
let _MIPS_REG_ENDING = 137;;
let _MIPS_REG_ZERO = _MIPS_REG_0;;
let _MIPS_REG_AT = _MIPS_REG_1;;
let _MIPS_REG_V0 = _MIPS_REG_2;;
let _MIPS_REG_V1 = _MIPS_REG_3;;
let _MIPS_REG_A0 = _MIPS_REG_4;;
let _MIPS_REG_A1 = _MIPS_REG_5;;
let _MIPS_REG_A2 = _MIPS_REG_6;;
let _MIPS_REG_A3 = _MIPS_REG_7;;
let _MIPS_REG_T0 = _MIPS_REG_8;;
let _MIPS_REG_T1 = _MIPS_REG_9;;
let _MIPS_REG_T2 = _MIPS_REG_10;;
let _MIPS_REG_T3 = _MIPS_REG_11;;
let _MIPS_REG_T4 = _MIPS_REG_12;;
let _MIPS_REG_T5 = _MIPS_REG_13;;
let _MIPS_REG_T6 = _MIPS_REG_14;;
let _MIPS_REG_T7 = _MIPS_REG_15;;
let _MIPS_REG_S0 = _MIPS_REG_16;;
let _MIPS_REG_S1 = _MIPS_REG_17;;
let _MIPS_REG_S2 = _MIPS_REG_18;;
let _MIPS_REG_S3 = _MIPS_REG_19;;
let _MIPS_REG_S4 = _MIPS_REG_20;;
let _MIPS_REG_S5 = _MIPS_REG_21;;
let _MIPS_REG_S6 = _MIPS_REG_22;;
let _MIPS_REG_S7 = _MIPS_REG_23;;
let _MIPS_REG_T8 = _MIPS_REG_24;;
let _MIPS_REG_T9 = _MIPS_REG_25;;
let _MIPS_REG_K0 = _MIPS_REG_26;;
let _MIPS_REG_K1 = _MIPS_REG_27;;
let _MIPS_REG_GP = _MIPS_REG_28;;
let _MIPS_REG_SP = _MIPS_REG_29;;
let _MIPS_REG_FP = _MIPS_REG_30;;
let _MIPS_REG_S8 = _MIPS_REG_30;;
let _MIPS_REG_RA = _MIPS_REG_31;;
let _MIPS_REG_HI0 = _MIPS_REG_AC0;;
let _MIPS_REG_HI1 = _MIPS_REG_AC1;;
let _MIPS_REG_HI2 = _MIPS_REG_AC2;;
let _MIPS_REG_HI3 = _MIPS_REG_AC3;;
let _MIPS_REG_LO0 = _MIPS_REG_HI0;;
let _MIPS_REG_LO1 = _MIPS_REG_HI1;;
let _MIPS_REG_LO2 = _MIPS_REG_HI2;;
let _MIPS_REG_LO3 = _MIPS_REG_HI3;;

(* MIPS instruction *)

let _MIPS_INS_INVALID = 0;;
let _MIPS_INS_ABSQ_S = 1;;
let _MIPS_INS_ADD = 2;;
let _MIPS_INS_ADDIUPC = 3;;
let _MIPS_INS_ADDIUR1SP = 4;;
let _MIPS_INS_ADDIUR2 = 5;;
let _MIPS_INS_ADDIUS5 = 6;;
let _MIPS_INS_ADDIUSP = 7;;
let _MIPS_INS_ADDQH = 8;;
let _MIPS_INS_ADDQH_R = 9;;
let _MIPS_INS_ADDQ = 10;;
let _MIPS_INS_ADDQ_S = 11;;
let _MIPS_INS_ADDSC = 12;;
let _MIPS_INS_ADDS_A = 13;;
let _MIPS_INS_ADDS_S = 14;;
let _MIPS_INS_ADDS_U = 15;;
let _MIPS_INS_ADDU16 = 16;;
let _MIPS_INS_ADDUH = 17;;
let _MIPS_INS_ADDUH_R = 18;;
let _MIPS_INS_ADDU = 19;;
let _MIPS_INS_ADDU_S = 20;;
let _MIPS_INS_ADDVI = 21;;
let _MIPS_INS_ADDV = 22;;
let _MIPS_INS_ADDWC = 23;;
let _MIPS_INS_ADD_A = 24;;
let _MIPS_INS_ADDI = 25;;
let _MIPS_INS_ADDIU = 26;;
let _MIPS_INS_ALIGN = 27;;
let _MIPS_INS_ALUIPC = 28;;
let _MIPS_INS_AND = 29;;
let _MIPS_INS_AND16 = 30;;
let _MIPS_INS_ANDI16 = 31;;
let _MIPS_INS_ANDI = 32;;
let _MIPS_INS_APPEND = 33;;
let _MIPS_INS_ASUB_S = 34;;
let _MIPS_INS_ASUB_U = 35;;
let _MIPS_INS_AUI = 36;;
let _MIPS_INS_AUIPC = 37;;
let _MIPS_INS_AVER_S = 38;;
let _MIPS_INS_AVER_U = 39;;
let _MIPS_INS_AVE_S = 40;;
let _MIPS_INS_AVE_U = 41;;
let _MIPS_INS_B16 = 42;;
let _MIPS_INS_BADDU = 43;;
let _MIPS_INS_BAL = 44;;
let _MIPS_INS_BALC = 45;;
let _MIPS_INS_BALIGN = 46;;
let _MIPS_INS_BBIT0 = 47;;
let _MIPS_INS_BBIT032 = 48;;
let _MIPS_INS_BBIT1 = 49;;
let _MIPS_INS_BBIT132 = 50;;
let _MIPS_INS_BC = 51;;
let _MIPS_INS_BC0F = 52;;
let _MIPS_INS_BC0FL = 53;;
let _MIPS_INS_BC0T = 54;;
let _MIPS_INS_BC0TL = 55;;
let _MIPS_INS_BC1EQZ = 56;;
let _MIPS_INS_BC1F = 57;;
let _MIPS_INS_BC1FL = 58;;
let _MIPS_INS_BC1NEZ = 59;;
let _MIPS_INS_BC1T = 60;;
let _MIPS_INS_BC1TL = 61;;
let _MIPS_INS_BC2EQZ = 62;;
let _MIPS_INS_BC2F = 63;;
let _MIPS_INS_BC2FL = 64;;
let _MIPS_INS_BC2NEZ = 65;;
let _MIPS_INS_BC2T = 66;;
let _MIPS_INS_BC2TL = 67;;
let _MIPS_INS_BC3F = 68;;
let _MIPS_INS_BC3FL = 69;;
let _MIPS_INS_BC3T = 70;;
let _MIPS_INS_BC3TL = 71;;
let _MIPS_INS_BCLRI = 72;;
let _MIPS_INS_BCLR = 73;;
let _MIPS_INS_BEQ = 74;;
let _MIPS_INS_BEQC = 75;;
let _MIPS_INS_BEQL = 76;;
let _MIPS_INS_BEQZ16 = 77;;
let _MIPS_INS_BEQZALC = 78;;
let _MIPS_INS_BEQZC = 79;;
let _MIPS_INS_BGEC = 80;;
let _MIPS_INS_BGEUC = 81;;
let _MIPS_INS_BGEZ = 82;;
let _MIPS_INS_BGEZAL = 83;;
let _MIPS_INS_BGEZALC = 84;;
let _MIPS_INS_BGEZALL = 85;;
let _MIPS_INS_BGEZALS = 86;;
let _MIPS_INS_BGEZC = 87;;
let _MIPS_INS_BGEZL = 88;;
let _MIPS_INS_BGTZ = 89;;
let _MIPS_INS_BGTZALC = 90;;
let _MIPS_INS_BGTZC = 91;;
let _MIPS_INS_BGTZL = 92;;
let _MIPS_INS_BINSLI = 93;;
let _MIPS_INS_BINSL = 94;;
let _MIPS_INS_BINSRI = 95;;
let _MIPS_INS_BINSR = 96;;
let _MIPS_INS_BITREV = 97;;
let _MIPS_INS_BITSWAP = 98;;
let _MIPS_INS_BLEZ = 99;;
let _MIPS_INS_BLEZALC = 100;;
let _MIPS_INS_BLEZC = 101;;
let _MIPS_INS_BLEZL = 102;;
let _MIPS_INS_BLTC = 103;;
let _MIPS_INS_BLTUC = 104;;
let _MIPS_INS_BLTZ = 105;;
let _MIPS_INS_BLTZAL = 106;;
let _MIPS_INS_BLTZALC = 107;;
let _MIPS_INS_BLTZALL = 108;;
let _MIPS_INS_BLTZALS = 109;;
let _MIPS_INS_BLTZC = 110;;
let _MIPS_INS_BLTZL = 111;;
let _MIPS_INS_BMNZI = 112;;
let _MIPS_INS_BMNZ = 113;;
let _MIPS_INS_BMZI = 114;;
let _MIPS_INS_BMZ = 115;;
let _MIPS_INS_BNE = 116;;
let _MIPS_INS_BNEC = 117;;
let _MIPS_INS_BNEGI = 118;;
let _MIPS_INS_BNEG = 119;;
let _MIPS_INS_BNEL = 120;;
let _MIPS_INS_BNEZ16 = 121;;
let _MIPS_INS_BNEZALC = 122;;
let _MIPS_INS_BNEZC = 123;;
let _MIPS_INS_BNVC = 124;;
let _MIPS_INS_BNZ = 125;;
let _MIPS_INS_BOVC = 126;;
let _MIPS_INS_BPOSGE32 = 127;;
let _MIPS_INS_BREAK = 128;;
let _MIPS_INS_BREAK16 = 129;;
let _MIPS_INS_BSELI = 130;;
let _MIPS_INS_BSEL = 131;;
let _MIPS_INS_BSETI = 132;;
let _MIPS_INS_BSET = 133;;
let _MIPS_INS_BZ = 134;;
let _MIPS_INS_BEQZ = 135;;
let _MIPS_INS_B = 136;;
let _MIPS_INS_BNEZ = 137;;
let _MIPS_INS_BTEQZ = 138;;
let _MIPS_INS_BTNEZ = 139;;
let _MIPS_INS_CACHE = 140;;
let _MIPS_INS_CEIL = 141;;
let _MIPS_INS_CEQI = 142;;
let _MIPS_INS_CEQ = 143;;
let _MIPS_INS_CFC1 = 144;;
let _MIPS_INS_CFCMSA = 145;;
let _MIPS_INS_CINS = 146;;
let _MIPS_INS_CINS32 = 147;;
let _MIPS_INS_CLASS = 148;;
let _MIPS_INS_CLEI_S = 149;;
let _MIPS_INS_CLEI_U = 150;;
let _MIPS_INS_CLE_S = 151;;
let _MIPS_INS_CLE_U = 152;;
let _MIPS_INS_CLO = 153;;
let _MIPS_INS_CLTI_S = 154;;
let _MIPS_INS_CLTI_U = 155;;
let _MIPS_INS_CLT_S = 156;;
let _MIPS_INS_CLT_U = 157;;
let _MIPS_INS_CLZ = 158;;
let _MIPS_INS_CMPGDU = 159;;
let _MIPS_INS_CMPGU = 160;;
let _MIPS_INS_CMPU = 161;;
let _MIPS_INS_CMP = 162;;
let _MIPS_INS_COPY_S = 163;;
let _MIPS_INS_COPY_U = 164;;
let _MIPS_INS_CTC1 = 165;;
let _MIPS_INS_CTCMSA = 166;;
let _MIPS_INS_CVT = 167;;
let _MIPS_INS_C = 168;;
let _MIPS_INS_CMPI = 169;;
let _MIPS_INS_DADD = 170;;
let _MIPS_INS_DADDI = 171;;
let _MIPS_INS_DADDIU = 172;;
let _MIPS_INS_DADDU = 173;;
let _MIPS_INS_DAHI = 174;;
let _MIPS_INS_DALIGN = 175;;
let _MIPS_INS_DATI = 176;;
let _MIPS_INS_DAUI = 177;;
let _MIPS_INS_DBITSWAP = 178;;
let _MIPS_INS_DCLO = 179;;
let _MIPS_INS_DCLZ = 180;;
let _MIPS_INS_DDIV = 181;;
let _MIPS_INS_DDIVU = 182;;
let _MIPS_INS_DERET = 183;;
let _MIPS_INS_DEXT = 184;;
let _MIPS_INS_DEXTM = 185;;
let _MIPS_INS_DEXTU = 186;;
let _MIPS_INS_DI = 187;;
let _MIPS_INS_DINS = 188;;
let _MIPS_INS_DINSM = 189;;
let _MIPS_INS_DINSU = 190;;
let _MIPS_INS_DIV = 191;;
let _MIPS_INS_DIVU = 192;;
let _MIPS_INS_DIV_S = 193;;
let _MIPS_INS_DIV_U = 194;;
let _MIPS_INS_DLSA = 195;;
let _MIPS_INS_DMFC0 = 196;;
let _MIPS_INS_DMFC1 = 197;;
let _MIPS_INS_DMFC2 = 198;;
let _MIPS_INS_DMOD = 199;;
let _MIPS_INS_DMODU = 200;;
let _MIPS_INS_DMTC0 = 201;;
let _MIPS_INS_DMTC1 = 202;;
let _MIPS_INS_DMTC2 = 203;;
let _MIPS_INS_DMUH = 204;;
let _MIPS_INS_DMUHU = 205;;
let _MIPS_INS_DMUL = 206;;
let _MIPS_INS_DMULT = 207;;
let _MIPS_INS_DMULTU = 208;;
let _MIPS_INS_DMULU = 209;;
let _MIPS_INS_DOTP_S = 210;;
let _MIPS_INS_DOTP_U = 211;;
let _MIPS_INS_DPADD_S = 212;;
let _MIPS_INS_DPADD_U = 213;;
let _MIPS_INS_DPAQX_SA = 214;;
let _MIPS_INS_DPAQX_S = 215;;
let _MIPS_INS_DPAQ_SA = 216;;
let _MIPS_INS_DPAQ_S = 217;;
let _MIPS_INS_DPAU = 218;;
let _MIPS_INS_DPAX = 219;;
let _MIPS_INS_DPA = 220;;
let _MIPS_INS_DPOP = 221;;
let _MIPS_INS_DPSQX_SA = 222;;
let _MIPS_INS_DPSQX_S = 223;;
let _MIPS_INS_DPSQ_SA = 224;;
let _MIPS_INS_DPSQ_S = 225;;
let _MIPS_INS_DPSUB_S = 226;;
let _MIPS_INS_DPSUB_U = 227;;
let _MIPS_INS_DPSU = 228;;
let _MIPS_INS_DPSX = 229;;
let _MIPS_INS_DPS = 230;;
let _MIPS_INS_DROTR = 231;;
let _MIPS_INS_DROTR32 = 232;;
let _MIPS_INS_DROTRV = 233;;
let _MIPS_INS_DSBH = 234;;
let _MIPS_INS_DSHD = 235;;
let _MIPS_INS_DSLL = 236;;
let _MIPS_INS_DSLL32 = 237;;
let _MIPS_INS_DSLLV = 238;;
let _MIPS_INS_DSRA = 239;;
let _MIPS_INS_DSRA32 = 240;;
let _MIPS_INS_DSRAV = 241;;
let _MIPS_INS_DSRL = 242;;
let _MIPS_INS_DSRL32 = 243;;
let _MIPS_INS_DSRLV = 244;;
let _MIPS_INS_DSUB = 245;;
let _MIPS_INS_DSUBU = 246;;
let _MIPS_INS_EHB = 247;;
let _MIPS_INS_EI = 248;;
let _MIPS_INS_ERET = 249;;
let _MIPS_INS_EXT = 250;;
let _MIPS_INS_EXTP = 251;;
let _MIPS_INS_EXTPDP = 252;;
let _MIPS_INS_EXTPDPV = 253;;
let _MIPS_INS_EXTPV = 254;;
let _MIPS_INS_EXTRV_RS = 255;;
let _MIPS_INS_EXTRV_R = 256;;
let _MIPS_INS_EXTRV_S = 257;;
let _MIPS_INS_EXTRV = 258;;
let _MIPS_INS_EXTR_RS = 259;;
let _MIPS_INS_EXTR_R = 260;;
let _MIPS_INS_EXTR_S = 261;;
let _MIPS_INS_EXTR = 262;;
let _MIPS_INS_EXTS = 263;;
let _MIPS_INS_EXTS32 = 264;;
let _MIPS_INS_ABS = 265;;
let _MIPS_INS_FADD = 266;;
let _MIPS_INS_FCAF = 267;;
let _MIPS_INS_FCEQ = 268;;
let _MIPS_INS_FCLASS = 269;;
let _MIPS_INS_FCLE = 270;;
let _MIPS_INS_FCLT = 271;;
let _MIPS_INS_FCNE = 272;;
let _MIPS_INS_FCOR = 273;;
let _MIPS_INS_FCUEQ = 274;;
let _MIPS_INS_FCULE = 275;;
let _MIPS_INS_FCULT = 276;;
let _MIPS_INS_FCUNE = 277;;
let _MIPS_INS_FCUN = 278;;
let _MIPS_INS_FDIV = 279;;
let _MIPS_INS_FEXDO = 280;;
let _MIPS_INS_FEXP2 = 281;;
let _MIPS_INS_FEXUPL = 282;;
let _MIPS_INS_FEXUPR = 283;;
let _MIPS_INS_FFINT_S = 284;;
let _MIPS_INS_FFINT_U = 285;;
let _MIPS_INS_FFQL = 286;;
let _MIPS_INS_FFQR = 287;;
let _MIPS_INS_FILL = 288;;
let _MIPS_INS_FLOG2 = 289;;
let _MIPS_INS_FLOOR = 290;;
let _MIPS_INS_FMADD = 291;;
let _MIPS_INS_FMAX_A = 292;;
let _MIPS_INS_FMAX = 293;;
let _MIPS_INS_FMIN_A = 294;;
let _MIPS_INS_FMIN = 295;;
let _MIPS_INS_MOV = 296;;
let _MIPS_INS_FMSUB = 297;;
let _MIPS_INS_FMUL = 298;;
let _MIPS_INS_MUL = 299;;
let _MIPS_INS_NEG = 300;;
let _MIPS_INS_FRCP = 301;;
let _MIPS_INS_FRINT = 302;;
let _MIPS_INS_FRSQRT = 303;;
let _MIPS_INS_FSAF = 304;;
let _MIPS_INS_FSEQ = 305;;
let _MIPS_INS_FSLE = 306;;
let _MIPS_INS_FSLT = 307;;
let _MIPS_INS_FSNE = 308;;
let _MIPS_INS_FSOR = 309;;
let _MIPS_INS_FSQRT = 310;;
let _MIPS_INS_SQRT = 311;;
let _MIPS_INS_FSUB = 312;;
let _MIPS_INS_SUB = 313;;
let _MIPS_INS_FSUEQ = 314;;
let _MIPS_INS_FSULE = 315;;
let _MIPS_INS_FSULT = 316;;
let _MIPS_INS_FSUNE = 317;;
let _MIPS_INS_FSUN = 318;;
let _MIPS_INS_FTINT_S = 319;;
let _MIPS_INS_FTINT_U = 320;;
let _MIPS_INS_FTQ = 321;;
let _MIPS_INS_FTRUNC_S = 322;;
let _MIPS_INS_FTRUNC_U = 323;;
let _MIPS_INS_HADD_S = 324;;
let _MIPS_INS_HADD_U = 325;;
let _MIPS_INS_HSUB_S = 326;;
let _MIPS_INS_HSUB_U = 327;;
let _MIPS_INS_ILVEV = 328;;
let _MIPS_INS_ILVL = 329;;
let _MIPS_INS_ILVOD = 330;;
let _MIPS_INS_ILVR = 331;;
let _MIPS_INS_INS = 332;;
let _MIPS_INS_INSERT = 333;;
let _MIPS_INS_INSV = 334;;
let _MIPS_INS_INSVE = 335;;
let _MIPS_INS_J = 336;;
let _MIPS_INS_JAL = 337;;
let _MIPS_INS_JALR = 338;;
let _MIPS_INS_JALRS16 = 339;;
let _MIPS_INS_JALRS = 340;;
let _MIPS_INS_JALS = 341;;
let _MIPS_INS_JALX = 342;;
let _MIPS_INS_JIALC = 343;;
let _MIPS_INS_JIC = 344;;
let _MIPS_INS_JR = 345;;
let _MIPS_INS_JR16 = 346;;
let _MIPS_INS_JRADDIUSP = 347;;
let _MIPS_INS_JRC = 348;;
let _MIPS_INS_JALRC = 349;;
let _MIPS_INS_LB = 350;;
let _MIPS_INS_LBU16 = 351;;
let _MIPS_INS_LBUX = 352;;
let _MIPS_INS_LBU = 353;;
let _MIPS_INS_LD = 354;;
let _MIPS_INS_LDC1 = 355;;
let _MIPS_INS_LDC2 = 356;;
let _MIPS_INS_LDC3 = 357;;
let _MIPS_INS_LDI = 358;;
let _MIPS_INS_LDL = 359;;
let _MIPS_INS_LDPC = 360;;
let _MIPS_INS_LDR = 361;;
let _MIPS_INS_LDXC1 = 362;;
let _MIPS_INS_LH = 363;;
let _MIPS_INS_LHU16 = 364;;
let _MIPS_INS_LHX = 365;;
let _MIPS_INS_LHU = 366;;
let _MIPS_INS_LI16 = 367;;
let _MIPS_INS_LL = 368;;
let _MIPS_INS_LLD = 369;;
let _MIPS_INS_LSA = 370;;
let _MIPS_INS_LUXC1 = 371;;
let _MIPS_INS_LUI = 372;;
let _MIPS_INS_LW = 373;;
let _MIPS_INS_LW16 = 374;;
let _MIPS_INS_LWC1 = 375;;
let _MIPS_INS_LWC2 = 376;;
let _MIPS_INS_LWC3 = 377;;
let _MIPS_INS_LWL = 378;;
let _MIPS_INS_LWM16 = 379;;
let _MIPS_INS_LWM32 = 380;;
let _MIPS_INS_LWPC = 381;;
let _MIPS_INS_LWP = 382;;
let _MIPS_INS_LWR = 383;;
let _MIPS_INS_LWUPC = 384;;
let _MIPS_INS_LWU = 385;;
let _MIPS_INS_LWX = 386;;
let _MIPS_INS_LWXC1 = 387;;
let _MIPS_INS_LWXS = 388;;
let _MIPS_INS_LI = 389;;
let _MIPS_INS_MADD = 390;;
let _MIPS_INS_MADDF = 391;;
let _MIPS_INS_MADDR_Q = 392;;
let _MIPS_INS_MADDU = 393;;
let _MIPS_INS_MADDV = 394;;
let _MIPS_INS_MADD_Q = 395;;
let _MIPS_INS_MAQ_SA = 396;;
let _MIPS_INS_MAQ_S = 397;;
let _MIPS_INS_MAXA = 398;;
let _MIPS_INS_MAXI_S = 399;;
let _MIPS_INS_MAXI_U = 400;;
let _MIPS_INS_MAX_A = 401;;
let _MIPS_INS_MAX = 402;;
let _MIPS_INS_MAX_S = 403;;
let _MIPS_INS_MAX_U = 404;;
let _MIPS_INS_MFC0 = 405;;
let _MIPS_INS_MFC1 = 406;;
let _MIPS_INS_MFC2 = 407;;
let _MIPS_INS_MFHC1 = 408;;
let _MIPS_INS_MFHI = 409;;
let _MIPS_INS_MFLO = 410;;
let _MIPS_INS_MINA = 411;;
let _MIPS_INS_MINI_S = 412;;
let _MIPS_INS_MINI_U = 413;;
let _MIPS_INS_MIN_A = 414;;
let _MIPS_INS_MIN = 415;;
let _MIPS_INS_MIN_S = 416;;
let _MIPS_INS_MIN_U = 417;;
let _MIPS_INS_MOD = 418;;
let _MIPS_INS_MODSUB = 419;;
let _MIPS_INS_MODU = 420;;
let _MIPS_INS_MOD_S = 421;;
let _MIPS_INS_MOD_U = 422;;
let _MIPS_INS_MOVE = 423;;
let _MIPS_INS_MOVEP = 424;;
let _MIPS_INS_MOVF = 425;;
let _MIPS_INS_MOVN = 426;;
let _MIPS_INS_MOVT = 427;;
let _MIPS_INS_MOVZ = 428;;
let _MIPS_INS_MSUB = 429;;
let _MIPS_INS_MSUBF = 430;;
let _MIPS_INS_MSUBR_Q = 431;;
let _MIPS_INS_MSUBU = 432;;
let _MIPS_INS_MSUBV = 433;;
let _MIPS_INS_MSUB_Q = 434;;
let _MIPS_INS_MTC0 = 435;;
let _MIPS_INS_MTC1 = 436;;
let _MIPS_INS_MTC2 = 437;;
let _MIPS_INS_MTHC1 = 438;;
let _MIPS_INS_MTHI = 439;;
let _MIPS_INS_MTHLIP = 440;;
let _MIPS_INS_MTLO = 441;;
let _MIPS_INS_MTM0 = 442;;
let _MIPS_INS_MTM1 = 443;;
let _MIPS_INS_MTM2 = 444;;
let _MIPS_INS_MTP0 = 445;;
let _MIPS_INS_MTP1 = 446;;
let _MIPS_INS_MTP2 = 447;;
let _MIPS_INS_MUH = 448;;
let _MIPS_INS_MUHU = 449;;
let _MIPS_INS_MULEQ_S = 450;;
let _MIPS_INS_MULEU_S = 451;;
let _MIPS_INS_MULQ_RS = 452;;
let _MIPS_INS_MULQ_S = 453;;
let _MIPS_INS_MULR_Q = 454;;
let _MIPS_INS_MULSAQ_S = 455;;
let _MIPS_INS_MULSA = 456;;
let _MIPS_INS_MULT = 457;;
let _MIPS_INS_MULTU = 458;;
let _MIPS_INS_MULU = 459;;
let _MIPS_INS_MULV = 460;;
let _MIPS_INS_MUL_Q = 461;;
let _MIPS_INS_MUL_S = 462;;
let _MIPS_INS_NLOC = 463;;
let _MIPS_INS_NLZC = 464;;
let _MIPS_INS_NMADD = 465;;
let _MIPS_INS_NMSUB = 466;;
let _MIPS_INS_NOR = 467;;
let _MIPS_INS_NORI = 468;;
let _MIPS_INS_NOT16 = 469;;
let _MIPS_INS_NOT = 470;;
let _MIPS_INS_OR = 471;;
let _MIPS_INS_OR16 = 472;;
let _MIPS_INS_ORI = 473;;
let _MIPS_INS_PACKRL = 474;;
let _MIPS_INS_PAUSE = 475;;
let _MIPS_INS_PCKEV = 476;;
let _MIPS_INS_PCKOD = 477;;
let _MIPS_INS_PCNT = 478;;
let _MIPS_INS_PICK = 479;;
let _MIPS_INS_POP = 480;;
let _MIPS_INS_PRECEQU = 481;;
let _MIPS_INS_PRECEQ = 482;;
let _MIPS_INS_PRECEU = 483;;
let _MIPS_INS_PRECRQU_S = 484;;
let _MIPS_INS_PRECRQ = 485;;
let _MIPS_INS_PRECRQ_RS = 486;;
let _MIPS_INS_PRECR = 487;;
let _MIPS_INS_PRECR_SRA = 488;;
let _MIPS_INS_PRECR_SRA_R = 489;;
let _MIPS_INS_PREF = 490;;
let _MIPS_INS_PREPEND = 491;;
let _MIPS_INS_RADDU = 492;;
let _MIPS_INS_RDDSP = 493;;
let _MIPS_INS_RDHWR = 494;;
let _MIPS_INS_REPLV = 495;;
let _MIPS_INS_REPL = 496;;
let _MIPS_INS_RINT = 497;;
let _MIPS_INS_ROTR = 498;;
let _MIPS_INS_ROTRV = 499;;
let _MIPS_INS_ROUND = 500;;
let _MIPS_INS_SAT_S = 501;;
let _MIPS_INS_SAT_U = 502;;
let _MIPS_INS_SB = 503;;
let _MIPS_INS_SB16 = 504;;
let _MIPS_INS_SC = 505;;
let _MIPS_INS_SCD = 506;;
let _MIPS_INS_SD = 507;;
let _MIPS_INS_SDBBP = 508;;
let _MIPS_INS_SDBBP16 = 509;;
let _MIPS_INS_SDC1 = 510;;
let _MIPS_INS_SDC2 = 511;;
let _MIPS_INS_SDC3 = 512;;
let _MIPS_INS_SDL = 513;;
let _MIPS_INS_SDR = 514;;
let _MIPS_INS_SDXC1 = 515;;
let _MIPS_INS_SEB = 516;;
let _MIPS_INS_SEH = 517;;
let _MIPS_INS_SELEQZ = 518;;
let _MIPS_INS_SELNEZ = 519;;
let _MIPS_INS_SEL = 520;;
let _MIPS_INS_SEQ = 521;;
let _MIPS_INS_SEQI = 522;;
let _MIPS_INS_SH = 523;;
let _MIPS_INS_SH16 = 524;;
let _MIPS_INS_SHF = 525;;
let _MIPS_INS_SHILO = 526;;
let _MIPS_INS_SHILOV = 527;;
let _MIPS_INS_SHLLV = 528;;
let _MIPS_INS_SHLLV_S = 529;;
let _MIPS_INS_SHLL = 530;;
let _MIPS_INS_SHLL_S = 531;;
let _MIPS_INS_SHRAV = 532;;
let _MIPS_INS_SHRAV_R = 533;;
let _MIPS_INS_SHRA = 534;;
let _MIPS_INS_SHRA_R = 535;;
let _MIPS_INS_SHRLV = 536;;
let _MIPS_INS_SHRL = 537;;
let _MIPS_INS_SLDI = 538;;
let _MIPS_INS_SLD = 539;;
let _MIPS_INS_SLL = 540;;
let _MIPS_INS_SLL16 = 541;;
let _MIPS_INS_SLLI = 542;;
let _MIPS_INS_SLLV = 543;;
let _MIPS_INS_SLT = 544;;
let _MIPS_INS_SLTI = 545;;
let _MIPS_INS_SLTIU = 546;;
let _MIPS_INS_SLTU = 547;;
let _MIPS_INS_SNE = 548;;
let _MIPS_INS_SNEI = 549;;
let _MIPS_INS_SPLATI = 550;;
let _MIPS_INS_SPLAT = 551;;
let _MIPS_INS_SRA = 552;;
let _MIPS_INS_SRAI = 553;;
let _MIPS_INS_SRARI = 554;;
let _MIPS_INS_SRAR = 555;;
let _MIPS_INS_SRAV = 556;;
let _MIPS_INS_SRL = 557;;
let _MIPS_INS_SRL16 = 558;;
let _MIPS_INS_SRLI = 559;;
let _MIPS_INS_SRLRI = 560;;
let _MIPS_INS_SRLR = 561;;
let _MIPS_INS_SRLV = 562;;
let _MIPS_INS_SSNOP = 563;;
let _MIPS_INS_ST = 564;;
let _MIPS_INS_SUBQH = 565;;
let _MIPS_INS_SUBQH_R = 566;;
let _MIPS_INS_SUBQ = 567;;
let _MIPS_INS_SUBQ_S = 568;;
let _MIPS_INS_SUBSUS_U = 569;;
let _MIPS_INS_SUBSUU_S = 570;;
let _MIPS_INS_SUBS_S = 571;;
let _MIPS_INS_SUBS_U = 572;;
let _MIPS_INS_SUBU16 = 573;;
let _MIPS_INS_SUBUH = 574;;
let _MIPS_INS_SUBUH_R = 575;;
let _MIPS_INS_SUBU = 576;;
let _MIPS_INS_SUBU_S = 577;;
let _MIPS_INS_SUBVI = 578;;
let _MIPS_INS_SUBV = 579;;
let _MIPS_INS_SUXC1 = 580;;
let _MIPS_INS_SW = 581;;
let _MIPS_INS_SW16 = 582;;
let _MIPS_INS_SWC1 = 583;;
let _MIPS_INS_SWC2 = 584;;
let _MIPS_INS_SWC3 = 585;;
let _MIPS_INS_SWL = 586;;
let _MIPS_INS_SWM16 = 587;;
let _MIPS_INS_SWM32 = 588;;
let _MIPS_INS_SWP = 589;;
let _MIPS_INS_SWR = 590;;
let _MIPS_INS_SWXC1 = 591;;
let _MIPS_INS_SYNC = 592;;
let _MIPS_INS_SYNCI = 593;;
let _MIPS_INS_SYSCALL = 594;;
let _MIPS_INS_TEQ = 595;;
let _MIPS_INS_TEQI = 596;;
let _MIPS_INS_TGE = 597;;
let _MIPS_INS_TGEI = 598;;
let _MIPS_INS_TGEIU = 599;;
let _MIPS_INS_TGEU = 600;;
let _MIPS_INS_TLBP = 601;;
let _MIPS_INS_TLBR = 602;;
let _MIPS_INS_TLBWI = 603;;
let _MIPS_INS_TLBWR = 604;;
let _MIPS_INS_TLT = 605;;
let _MIPS_INS_TLTI = 606;;
let _MIPS_INS_TLTIU = 607;;
let _MIPS_INS_TLTU = 608;;
let _MIPS_INS_TNE = 609;;
let _MIPS_INS_TNEI = 610;;
let _MIPS_INS_TRUNC = 611;;
let _MIPS_INS_V3MULU = 612;;
let _MIPS_INS_VMM0 = 613;;
let _MIPS_INS_VMULU = 614;;
let _MIPS_INS_VSHF = 615;;
let _MIPS_INS_WAIT = 616;;
let _MIPS_INS_WRDSP = 617;;
let _MIPS_INS_WSBH = 618;;
let _MIPS_INS_XOR = 619;;
let _MIPS_INS_XOR16 = 620;;
let _MIPS_INS_XORI = 621;;

(* some alias instructions *)
let _MIPS_INS_NOP = 622;;
let _MIPS_INS_NEGU = 623;;

(* special instructions *)
let _MIPS_INS_JALR_HB = 624;;
let _MIPS_INS_JR_HB = 625;;
let _MIPS_INS_ENDING = 626;;

(* Group of MIPS instructions *)

let _MIPS_GRP_INVALID = 0;;

(* Generic groups *)
let _MIPS_GRP_JUMP = 1;;
let _MIPS_GRP_CALL = 2;;
let _MIPS_GRP_RET = 3;;
let _MIPS_GRP_INT = 4;;
let _MIPS_GRP_IRET = 5;;
let _MIPS_GRP_PRIVILEGE = 6;;
let _MIPS_GRP_BRANCH_RELATIVE = 7;;

(* Architecture-specific groups *)
let _MIPS_GRP_BITCOUNT = 128;;
let _MIPS_GRP_DSP = 129;;
let _MIPS_GRP_DSPR2 = 130;;
let _MIPS_GRP_FPIDX = 131;;
let _MIPS_GRP_MSA = 132;;
let _MIPS_GRP_MIPS32R2 = 133;;
let _MIPS_GRP_MIPS64 = 134;;
let _MIPS_GRP_MIPS64R2 = 135;;
let _MIPS_GRP_SEINREG = 136;;
let _MIPS_GRP_STDENC = 137;;
let _MIPS_GRP_SWAP = 138;;
let _MIPS_GRP_MICROMIPS = 139;;
let _MIPS_GRP_MIPS16MODE = 140;;
let _MIPS_GRP_FP64BIT = 141;;
let _MIPS_GRP_NONANSFPMATH = 142;;
let _MIPS_GRP_NOTFP64BIT = 143;;
let _MIPS_GRP_NOTINMICROMIPS = 144;;
let _MIPS_GRP_NOTNACL = 145;;
let _MIPS_GRP_NOTMIPS32R6 = 146;;
let _MIPS_GRP_NOTMIPS64R6 = 147;;
let _MIPS_GRP_CNMIPS = 148;;
let _MIPS_GRP_MIPS32 = 149;;
let _MIPS_GRP_MIPS32R6 = 150;;
let _MIPS_GRP_MIPS64R6 = 151;;
let _MIPS_GRP_MIPS2 = 152;;
let _MIPS_GRP_MIPS3 = 153;;
let _MIPS_GRP_MIPS3_32 = 154;;
let _MIPS_GRP_MIPS3_32R2 = 155;;
let _MIPS_GRP_MIPS4_32 = 156;;
let _MIPS_GRP_MIPS4_32R2 = 157;;
let _MIPS_GRP_MIPS5_32R2 = 158;;
let _MIPS_GRP_GP32BIT = 159;;
let _MIPS_GRP_GP64BIT = 160;;
let _MIPS_GRP_ENDING = 161;;
