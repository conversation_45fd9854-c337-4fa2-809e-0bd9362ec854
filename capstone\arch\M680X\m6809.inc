
// M6809/HD6309 PAGE1 instructions
static const inst_page1 g_m6809_inst_page1_table[256] = {
	// 0x0x, direct instructions
	{ M680X_INS_NEG, dir_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_COM, dir_hid, inh_hid },
	{ M680X_INS_LSR, dir_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ROR, dir_hid, inh_hid },
	{ M680X_INS_ASR, dir_hid, inh_hid },
	{ M680X_INS_LSL, dir_hid, inh_hid },
	{ M680X_INS_ROL, dir_hid, inh_hid },
	{ M680X_INS_DEC, dir_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_INC, dir_hid, inh_hid },
	{ M680X_INS_TST, dir_hid, inh_hid },
	{ M680X_INS_JMP, dir_hid, inh_hid },
	{ M680X_INS_CLR, dir_hid, inh_hid },
	// 0x1x, misc instructions
	{ M680X_INS_ILLGL, illgl_hid, inh_hid }, // PAGE2
	{ M680X_INS_ILLGL, illgl_hid, inh_hid }, // PAGE3
	{ M680X_INS_NOP, inh_hid, inh_hid },
	{ M680X_INS_SYNC, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_LBRA, rel16_hid, inh_hid },
	{ M680X_INS_LBSR, rel16_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_DAA, inh_hid, inh_hid },
	{ M680X_INS_ORCC, imm8_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ANDCC, imm8_hid, inh_hid },
	{ M680X_INS_SEX, inh_hid, inh_hid },
	{ M680X_INS_EXG, rr09_hid, inh_hid },
	{ M680X_INS_TFR, rr09_hid, inh_hid },
	// 0x2x, relative branch instructions
	{ M680X_INS_BRA, rel8_hid, inh_hid },
	{ M680X_INS_BRN, rel8_hid, inh_hid },
	{ M680X_INS_BHI, rel8_hid, inh_hid },
	{ M680X_INS_BLS, rel8_hid, inh_hid },
	{ M680X_INS_BCC, rel8_hid, inh_hid },
	{ M680X_INS_BCS, rel8_hid, inh_hid },
	{ M680X_INS_BNE, rel8_hid, inh_hid },
	{ M680X_INS_BEQ, rel8_hid, inh_hid },
	{ M680X_INS_BVC, rel8_hid, inh_hid },
	{ M680X_INS_BVS, rel8_hid, inh_hid },
	{ M680X_INS_BPL, rel8_hid, inh_hid },
	{ M680X_INS_BMI, rel8_hid, inh_hid },
	{ M680X_INS_BGE, rel8_hid, inh_hid },
	{ M680X_INS_BLT, rel8_hid, inh_hid },
	{ M680X_INS_BGT, rel8_hid, inh_hid },
	{ M680X_INS_BLE, rel8_hid, inh_hid },
	// 0x3x, misc instructions
	{ M680X_INS_LEAX, idx09_hid, inh_hid },
	{ M680X_INS_LEAY, idx09_hid, inh_hid },
	{ M680X_INS_LEAS, idx09_hid, inh_hid },
	{ M680X_INS_LEAU, idx09_hid, inh_hid },
	{ M680X_INS_PSHS, rbits_hid, inh_hid },
	{ M680X_INS_PULS, rbits_hid, inh_hid },
	{ M680X_INS_PSHU, rbits_hid, inh_hid },
	{ M680X_INS_PULU, rbits_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_RTS, inh_hid, inh_hid },
	{ M680X_INS_ABX, inh_hid, inh_hid },
	{ M680X_INS_RTI, inh_hid, inh_hid },
	{ M680X_INS_CWAI, imm8_hid, inh_hid },
	{ M680X_INS_MUL, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_SWI, inh_hid, inh_hid },
	// 0x4x, Register A instructions
	{ M680X_INS_NEGA, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_COMA, inh_hid, inh_hid },
	{ M680X_INS_LSRA, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_RORA, inh_hid, inh_hid },
	{ M680X_INS_ASRA, inh_hid, inh_hid },
	{ M680X_INS_LSLA, inh_hid, inh_hid },
	{ M680X_INS_ROLA, inh_hid, inh_hid },
	{ M680X_INS_DECA, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_INCA, inh_hid, inh_hid },
	{ M680X_INS_TSTA, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_CLRA, inh_hid, inh_hid },
	// 0x5x, Register B instructions
	{ M680X_INS_NEGB, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_COMB, inh_hid, inh_hid },
	{ M680X_INS_LSRB, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_RORB, inh_hid, inh_hid },
	{ M680X_INS_ASRB, inh_hid, inh_hid },
	{ M680X_INS_LSLB, inh_hid, inh_hid },
	{ M680X_INS_ROLB, inh_hid, inh_hid },
	{ M680X_INS_DECB, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_INCB, inh_hid, inh_hid },
	{ M680X_INS_TSTB, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_CLRB, inh_hid, inh_hid },
	// 0x6x, indexed instructions
	{ M680X_INS_NEG, idx09_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_COM, idx09_hid, inh_hid },
	{ M680X_INS_LSR, idx09_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ROR, idx09_hid, inh_hid },
	{ M680X_INS_ASR, idx09_hid, inh_hid },
	{ M680X_INS_LSL, idx09_hid, inh_hid },
	{ M680X_INS_ROL, idx09_hid, inh_hid },
	{ M680X_INS_DEC, idx09_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_INC, idx09_hid, inh_hid },
	{ M680X_INS_TST, idx09_hid, inh_hid },
	{ M680X_INS_JMP, idx09_hid, inh_hid },
	{ M680X_INS_CLR, idx09_hid, inh_hid },
	// 0x7x, extended instructions
	{ M680X_INS_NEG, ext_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_COM, ext_hid, inh_hid },
	{ M680X_INS_LSR, ext_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ROR, ext_hid, inh_hid },
	{ M680X_INS_ASR, ext_hid, inh_hid },
	{ M680X_INS_LSL, ext_hid, inh_hid },
	{ M680X_INS_ROL, ext_hid, inh_hid },
	{ M680X_INS_DEC, ext_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_INC, ext_hid, inh_hid },
	{ M680X_INS_TST, ext_hid, inh_hid },
	{ M680X_INS_JMP, ext_hid, inh_hid },
	{ M680X_INS_CLR, ext_hid, inh_hid },
	// 0x8x, immediate instructions with Register A,D,X
	{ M680X_INS_SUBA, imm8_hid, inh_hid },
	{ M680X_INS_CMPA, imm8_hid, inh_hid },
	{ M680X_INS_SBCA, imm8_hid, inh_hid },
	{ M680X_INS_SUBD, imm16_hid, inh_hid },
	{ M680X_INS_ANDA, imm8_hid, inh_hid },
	{ M680X_INS_BITA, imm8_hid, inh_hid },
	{ M680X_INS_LDA, imm8_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_EORA, imm8_hid, inh_hid },
	{ M680X_INS_ADCA, imm8_hid, inh_hid },
	{ M680X_INS_ORA, imm8_hid, inh_hid },
	{ M680X_INS_ADDA, imm8_hid, inh_hid },
	{ M680X_INS_CMPX, imm16_hid, inh_hid },
	{ M680X_INS_BSR, rel8_hid, inh_hid },
	{ M680X_INS_LDX, imm16_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	// 0x9x, direct instructions with register A,D,X
	{ M680X_INS_SUBA, dir_hid, inh_hid },
	{ M680X_INS_CMPA, dir_hid, inh_hid },
	{ M680X_INS_SBCA, dir_hid, inh_hid },
	{ M680X_INS_SUBD, dir_hid, inh_hid },
	{ M680X_INS_ANDA, dir_hid, inh_hid },
	{ M680X_INS_BITA, dir_hid, inh_hid },
	{ M680X_INS_LDA, dir_hid, inh_hid },
	{ M680X_INS_STA, dir_hid, inh_hid },
	{ M680X_INS_EORA, dir_hid, inh_hid },
	{ M680X_INS_ADCA, dir_hid, inh_hid },
	{ M680X_INS_ORA, dir_hid, inh_hid },
	{ M680X_INS_ADDA, dir_hid, inh_hid },
	{ M680X_INS_CMPX, dir_hid, inh_hid },
	{ M680X_INS_JSR, dir_hid, inh_hid },
	{ M680X_INS_LDX, dir_hid, inh_hid },
	{ M680X_INS_STX, dir_hid, inh_hid },
	// 0xAx, indexed instructions with Register A,D,X
	{ M680X_INS_SUBA, idx09_hid, inh_hid },
	{ M680X_INS_CMPA, idx09_hid, inh_hid },
	{ M680X_INS_SBCA, idx09_hid, inh_hid },
	{ M680X_INS_SUBD, idx09_hid, inh_hid },
	{ M680X_INS_ANDA, idx09_hid, inh_hid },
	{ M680X_INS_BITA, idx09_hid, inh_hid },
	{ M680X_INS_LDA, idx09_hid, inh_hid },
	{ M680X_INS_STA, idx09_hid, inh_hid },
	{ M680X_INS_EORA, idx09_hid, inh_hid },
	{ M680X_INS_ADCA, idx09_hid, inh_hid },
	{ M680X_INS_ORA, idx09_hid, inh_hid },
	{ M680X_INS_ADDA, idx09_hid, inh_hid },
	{ M680X_INS_CMPX, idx09_hid, inh_hid },
	{ M680X_INS_JSR, idx09_hid, inh_hid },
	{ M680X_INS_LDX, idx09_hid, inh_hid },
	{ M680X_INS_STX, idx09_hid, inh_hid },
	// 0xBx, extended instructions with register A,D,X
	{ M680X_INS_SUBA, ext_hid, inh_hid },
	{ M680X_INS_CMPA, ext_hid, inh_hid },
	{ M680X_INS_SBCA, ext_hid, inh_hid },
	{ M680X_INS_SUBD, ext_hid, inh_hid },
	{ M680X_INS_ANDA, ext_hid, inh_hid },
	{ M680X_INS_BITA, ext_hid, inh_hid },
	{ M680X_INS_LDA, ext_hid, inh_hid },
	{ M680X_INS_STA, ext_hid, inh_hid },
	{ M680X_INS_EORA, ext_hid, inh_hid },
	{ M680X_INS_ADCA, ext_hid, inh_hid },
	{ M680X_INS_ORA, ext_hid, inh_hid },
	{ M680X_INS_ADDA, ext_hid, inh_hid },
	{ M680X_INS_CMPX, ext_hid, inh_hid },
	{ M680X_INS_JSR, ext_hid, inh_hid },
	{ M680X_INS_LDX, ext_hid, inh_hid },
	{ M680X_INS_STX, ext_hid, inh_hid },
	// 0xCx, immediate instructions with register B,D,U
	{ M680X_INS_SUBB, imm8_hid, inh_hid },
	{ M680X_INS_CMPB, imm8_hid, inh_hid },
	{ M680X_INS_SBCB, imm8_hid, inh_hid },
	{ M680X_INS_ADDD, imm16_hid, inh_hid },
	{ M680X_INS_ANDB, imm8_hid, inh_hid },
	{ M680X_INS_BITB, imm8_hid, inh_hid },
	{ M680X_INS_LDB, imm8_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_EORB, imm8_hid, inh_hid },
	{ M680X_INS_ADCB, imm8_hid, inh_hid },
	{ M680X_INS_ORB, imm8_hid, inh_hid },
	{ M680X_INS_ADDB, imm8_hid, inh_hid },
	{ M680X_INS_LDD, imm16_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_LDU, imm16_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	// 0xDx direct instructions with register B,D,U
	{ M680X_INS_SUBB, dir_hid, inh_hid },
	{ M680X_INS_CMPB, dir_hid, inh_hid },
	{ M680X_INS_SBCB, dir_hid, inh_hid },
	{ M680X_INS_ADDD, dir_hid, inh_hid },
	{ M680X_INS_ANDB, dir_hid, inh_hid },
	{ M680X_INS_BITB, dir_hid, inh_hid },
	{ M680X_INS_LDB, dir_hid, inh_hid },
	{ M680X_INS_STB, dir_hid, inh_hid },
	{ M680X_INS_EORB, dir_hid, inh_hid },
	{ M680X_INS_ADCB, dir_hid, inh_hid },
	{ M680X_INS_ORB, dir_hid, inh_hid },
	{ M680X_INS_ADDB, dir_hid, inh_hid },
	{ M680X_INS_LDD, dir_hid, inh_hid },
	{ M680X_INS_STD, dir_hid, inh_hid },
	{ M680X_INS_LDU, dir_hid, inh_hid },
	{ M680X_INS_STU, dir_hid, inh_hid },
	// 0xEx, indexed instruction with register B,D,U
	{ M680X_INS_SUBB, idx09_hid, inh_hid },
	{ M680X_INS_CMPB, idx09_hid, inh_hid },
	{ M680X_INS_SBCB, idx09_hid, inh_hid },
	{ M680X_INS_ADDD, idx09_hid, inh_hid },
	{ M680X_INS_ANDB, idx09_hid, inh_hid },
	{ M680X_INS_BITB, idx09_hid, inh_hid },
	{ M680X_INS_LDB, idx09_hid, inh_hid },
	{ M680X_INS_STB, idx09_hid, inh_hid },
	{ M680X_INS_EORB, idx09_hid, inh_hid },
	{ M680X_INS_ADCB, idx09_hid, inh_hid },
	{ M680X_INS_ORB, idx09_hid, inh_hid },
	{ M680X_INS_ADDB, idx09_hid, inh_hid },
	{ M680X_INS_LDD, idx09_hid, inh_hid },
	{ M680X_INS_STD, idx09_hid, inh_hid },
	{ M680X_INS_LDU, idx09_hid, inh_hid },
	{ M680X_INS_STU, idx09_hid, inh_hid },
	// 0xFx, extended instructions with register B,D,U
	{ M680X_INS_SUBB, ext_hid, inh_hid },
	{ M680X_INS_CMPB, ext_hid, inh_hid },
	{ M680X_INS_SBCB, ext_hid, inh_hid },
	{ M680X_INS_ADDD, ext_hid, inh_hid },
	{ M680X_INS_ANDB, ext_hid, inh_hid },
	{ M680X_INS_BITB, ext_hid, inh_hid },
	{ M680X_INS_LDB, ext_hid, inh_hid },
	{ M680X_INS_STB, ext_hid, inh_hid },
	{ M680X_INS_EORB, ext_hid, inh_hid },
	{ M680X_INS_ADCB, ext_hid, inh_hid },
	{ M680X_INS_ORB, ext_hid, inh_hid },
	{ M680X_INS_ADDB, ext_hid, inh_hid },
	{ M680X_INS_LDD, ext_hid, inh_hid },
	{ M680X_INS_STD, ext_hid, inh_hid },
	{ M680X_INS_LDU, ext_hid, inh_hid },
	{ M680X_INS_STU, ext_hid, inh_hid },
};

// The following array has to be sorted by increasing
// opcodes. Otherwise the binary_search will fail.
//
// M6809 PAGE2 instructions (with prefix 0x10)
static const inst_pageX g_m6809_inst_page2_table[] = {
	// 0x2x, relative long branch instructions
	{ 0x21, M680X_INS_LBRN, rel16_hid, inh_hid },
	{ 0x22, M680X_INS_LBHI, rel16_hid, inh_hid },
	{ 0x23, M680X_INS_LBLS, rel16_hid, inh_hid },
	{ 0x24, M680X_INS_LBCC, rel16_hid, inh_hid },
	{ 0x25, M680X_INS_LBCS, rel16_hid, inh_hid },
	{ 0x26, M680X_INS_LBNE, rel16_hid, inh_hid },
	{ 0x27, M680X_INS_LBEQ, rel16_hid, inh_hid },
	{ 0x28, M680X_INS_LBVC, rel16_hid, inh_hid },
	{ 0x29, M680X_INS_LBVS, rel16_hid, inh_hid },
	{ 0x2a, M680X_INS_LBPL, rel16_hid, inh_hid },
	{ 0x2b, M680X_INS_LBMI, rel16_hid, inh_hid },
	{ 0x2c, M680X_INS_LBGE, rel16_hid, inh_hid },
	{ 0x2d, M680X_INS_LBLT, rel16_hid, inh_hid },
	{ 0x2e, M680X_INS_LBGT, rel16_hid, inh_hid },
	{ 0x2f, M680X_INS_LBLE, rel16_hid, inh_hid },
	// 0x3x
	{ 0x3f, M680X_INS_SWI2, inh_hid, inh_hid },
	// 0x8x, immediate instructions with register D,Y
	{ 0x83, M680X_INS_CMPD, imm16_hid, inh_hid },
	{ 0x8c, M680X_INS_CMPY, imm16_hid, inh_hid },
	{ 0x8e, M680X_INS_LDY, imm16_hid, inh_hid },
	// 0x9x, direct instructions with register D,Y
	{ 0x93, M680X_INS_CMPD, dir_hid, inh_hid },
	{ 0x9c, M680X_INS_CMPY, dir_hid, inh_hid },
	{ 0x9e, M680X_INS_LDY, dir_hid, inh_hid },
	{ 0x9f, M680X_INS_STY, dir_hid, inh_hid },
	// 0xAx, indexed instructions with register D,Y
	{ 0xa3, M680X_INS_CMPD, idx09_hid, inh_hid },
	{ 0xac, M680X_INS_CMPY, idx09_hid, inh_hid },
	{ 0xae, M680X_INS_LDY, idx09_hid, inh_hid },
	{ 0xaf, M680X_INS_STY, idx09_hid, inh_hid },
	// 0xBx, extended instructions with register D,Y
	{ 0xb3, M680X_INS_CMPD, ext_hid, inh_hid },
	{ 0xbc, M680X_INS_CMPY, ext_hid, inh_hid },
	{ 0xbe, M680X_INS_LDY, ext_hid, inh_hid },
	{ 0xbf, M680X_INS_STY, ext_hid, inh_hid },
	// 0xCx, immediate instructions with register S
	{ 0xce, M680X_INS_LDS, imm16_hid, inh_hid },
	// 0xDx, direct instructions with register S
	{ 0xde, M680X_INS_LDS, dir_hid, inh_hid },
	{ 0xdf, M680X_INS_STS, dir_hid, inh_hid },
	// 0xEx, indexed instructions with register S
	{ 0xee, M680X_INS_LDS, idx09_hid, inh_hid },
	{ 0xef, M680X_INS_STS, idx09_hid, inh_hid },
	// 0xFx, extended instructions with register S
	{ 0xfe, M680X_INS_LDS, ext_hid, inh_hid },
	{ 0xff, M680X_INS_STS, ext_hid, inh_hid },
};

// The following array has to be sorted by increasing
// opcodes. Otherwise the binary_search will fail.
//
// M6809 PAGE3 instructions (with prefix 0x11)
static const inst_pageX g_m6809_inst_page3_table[] = {
	{ 0x3f, M680X_INS_SWI3, inh_hid, inh_hid },
	// 0x8x, immediate instructions with register U,S
	{ 0x83, M680X_INS_CMPU, imm16_hid, inh_hid },
	{ 0x8c, M680X_INS_CMPS, imm16_hid, inh_hid },
	// 0x9x, direct instructions with register U,S
	{ 0x93, M680X_INS_CMPU, dir_hid, inh_hid },
	{ 0x9c, M680X_INS_CMPS, dir_hid, inh_hid },
	// 0xAx, indexed instructions with register U,S
	{ 0xa3, M680X_INS_CMPU, idx09_hid, inh_hid },
	{ 0xac, M680X_INS_CMPS, idx09_hid, inh_hid },
	// 0xBx, extended instructions with register U,S
	{ 0xb3, M680X_INS_CMPU, ext_hid, inh_hid },
	{ 0xbc, M680X_INS_CMPS, ext_hid, inh_hid },
};

