/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Assembly Writer Source Fragment                                             *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine */
/* By <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2015 */

#include <stdio.h>	// debug
#include <capstone/platform.h>


/// printInstruction - This method is automatically generated by tablegen
/// from the instruction set description.
static void printInstruction(MCInst *MI, SStream *O, MCRegisterInfo *MRI)
{
  static const uint32_t OpInfo[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    3946U,	// DBG_VALUE
    0U,	// REG_SEQUENCE
    0U,	// COPY
    3939U,	// BUNDLE
    3956U,	// LIFETIME_START
    3926U,	// LIFETIME_END
    0U,	// STACKMAP
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    0U,	// STATEPOINT
    0U,	// FRAME_ALLOC
    4099U,	// A
    4160U,	// ADB
    1055559U,	// ADBR
    0U,	// ADJCALLSTACKDOWN
    0U,	// ADJCALLSTACKUP
    0U,	// ADJDYNALLOC
    4205U,	// AEB
    1055678U,	// AEBR
    0U,	// AEXT128_64
    2103171U,	// AFI
    0U,	// AFIMux
    5195U,	// AG
    5068U,	// AGF
    2103181U,	// AGFI
    1056088U,	// AGFR
    3151831U,	// AGHI
    37755030U,	// AGHIK
    1056164U,	// AGR
    171972799U,	// AGRK
    75807U,	// AGSI
    5397U,	// AH
    3151821U,	// AHI
    37755024U,	// AHIK
    0U,	// AHIMux
    0U,	// AHIMuxK
    7917U,	// AHY
    2102663U,	// AIH
    6413U,	// AL
    4271U,	// ALC
    5239U,	// ALCG
    1056176U,	// ALCGR
    1056003U,	// ALCR
    5248949U,	// ALFI
    5272U,	// ALG
    5078U,	// ALGF
    5248921U,	// ALGFI
    1056101U,	// ALGFR
    37755037U,	// ALGHSIK
    1056196U,	// ALGR
    171972805U,	// ALGRK
    37755046U,	// ALHSIK
    1056312U,	// ALR
    171972843U,	// ALRK
    7975U,	// ALY
    1055554U,	// AR
    171972794U,	// ARK
    75802U,	// ASI
    0U,	// ATOMIC_CMP_SWAPW
    0U,	// ATOMIC_LOADW_AFI
    0U,	// ATOMIC_LOADW_AR
    0U,	// ATOMIC_LOADW_MAX
    0U,	// ATOMIC_LOADW_MIN
    0U,	// ATOMIC_LOADW_NILH
    0U,	// ATOMIC_LOADW_NILHi
    0U,	// ATOMIC_LOADW_NR
    0U,	// ATOMIC_LOADW_NRi
    0U,	// ATOMIC_LOADW_OILH
    0U,	// ATOMIC_LOADW_OR
    0U,	// ATOMIC_LOADW_SR
    0U,	// ATOMIC_LOADW_UMAX
    0U,	// ATOMIC_LOADW_UMIN
    0U,	// ATOMIC_LOADW_XILF
    0U,	// ATOMIC_LOADW_XR
    0U,	// ATOMIC_LOAD_AFI
    0U,	// ATOMIC_LOAD_AGFI
    0U,	// ATOMIC_LOAD_AGHI
    0U,	// ATOMIC_LOAD_AGR
    0U,	// ATOMIC_LOAD_AHI
    0U,	// ATOMIC_LOAD_AR
    0U,	// ATOMIC_LOAD_MAX_32
    0U,	// ATOMIC_LOAD_MAX_64
    0U,	// ATOMIC_LOAD_MIN_32
    0U,	// ATOMIC_LOAD_MIN_64
    0U,	// ATOMIC_LOAD_NGR
    0U,	// ATOMIC_LOAD_NGRi
    0U,	// ATOMIC_LOAD_NIHF64
    0U,	// ATOMIC_LOAD_NIHF64i
    0U,	// ATOMIC_LOAD_NIHH64
    0U,	// ATOMIC_LOAD_NIHH64i
    0U,	// ATOMIC_LOAD_NIHL64
    0U,	// ATOMIC_LOAD_NIHL64i
    0U,	// ATOMIC_LOAD_NILF
    0U,	// ATOMIC_LOAD_NILF64
    0U,	// ATOMIC_LOAD_NILF64i
    0U,	// ATOMIC_LOAD_NILFi
    0U,	// ATOMIC_LOAD_NILH
    0U,	// ATOMIC_LOAD_NILH64
    0U,	// ATOMIC_LOAD_NILH64i
    0U,	// ATOMIC_LOAD_NILHi
    0U,	// ATOMIC_LOAD_NILL
    0U,	// ATOMIC_LOAD_NILL64
    0U,	// ATOMIC_LOAD_NILL64i
    0U,	// ATOMIC_LOAD_NILLi
    0U,	// ATOMIC_LOAD_NR
    0U,	// ATOMIC_LOAD_NRi
    0U,	// ATOMIC_LOAD_OGR
    0U,	// ATOMIC_LOAD_OIHF64
    0U,	// ATOMIC_LOAD_OIHH64
    0U,	// ATOMIC_LOAD_OIHL64
    0U,	// ATOMIC_LOAD_OILF
    0U,	// ATOMIC_LOAD_OILF64
    0U,	// ATOMIC_LOAD_OILH
    0U,	// ATOMIC_LOAD_OILH64
    0U,	// ATOMIC_LOAD_OILL
    0U,	// ATOMIC_LOAD_OILL64
    0U,	// ATOMIC_LOAD_OR
    0U,	// ATOMIC_LOAD_SGR
    0U,	// ATOMIC_LOAD_SR
    0U,	// ATOMIC_LOAD_UMAX_32
    0U,	// ATOMIC_LOAD_UMAX_64
    0U,	// ATOMIC_LOAD_UMIN_32
    0U,	// ATOMIC_LOAD_UMIN_64
    0U,	// ATOMIC_LOAD_XGR
    0U,	// ATOMIC_LOAD_XIHF64
    0U,	// ATOMIC_LOAD_XILF
    0U,	// ATOMIC_LOAD_XILF64
    0U,	// ATOMIC_LOAD_XR
    0U,	// ATOMIC_SWAPW
    0U,	// ATOMIC_SWAP_32
    0U,	// ATOMIC_SWAP_64
    1055887U,	// AXBR
    7880U,	// AY
    6438135U,	// AsmBCR
    209101U,	// AsmBRC
    211230U,	// AsmBRCL
    74455135U,	// AsmCGIJ
    306190455U,	// AsmCGRJ
    74455130U,	// AsmCIJ
    75503717U,	// AsmCLGIJ
    306190461U,	// AsmCLGRJ
    75503724U,	// AsmCLIJ
    306190468U,	// AsmCLRJ
    306190450U,	// AsmCRJ
    269613U,	// AsmEBR
    16910U,	// AsmEJ
    16650U,	// AsmEJG
    9441518U,	// AsmELOC
    9441531U,	// AsmELOCG
    1053631U,	// AsmELOCGR
    1053624U,	// AsmELOCR
    10490100U,	// AsmESTOC
    10490114U,	// AsmESTOCG
    269843U,	// AsmHBR
    269618U,	// AsmHEBR
    16695U,	// AsmHEJ
    16687U,	// AsmHEJG
    9441551U,	// AsmHELOC
    9441566U,	// AsmHELOCG
    1053187U,	// AsmHELOCGR
    1053179U,	// AsmHELOCR
    10490134U,	// AsmHESTOC
    10490150U,	// AsmHESTOCG
    17817U,	// AsmHJ
    17745U,	// AsmHJG
    9442596U,	// AsmHLOC
    9442626U,	// AsmHLOCG
    1054578U,	// AsmHLOCGR
    1054571U,	// AsmHLOCR
    10491178U,	// AsmHSTOC
    10491209U,	// AsmHSTOCG
    108009100U,	// AsmJEAltCGI
    440407728U,	// AsmJEAltCGR
    108009092U,	// AsmJEAltCI
    109057685U,	// AsmJEAltCLGI
    440407737U,	// AsmJEAltCLGR
    109057695U,	// AsmJEAltCLI
    440407747U,	// AsmJEAltCLR
    440407720U,	// AsmJEAltCR
    108007954U,	// AsmJECGI
    440406574U,	// AsmJECGR
    108007948U,	// AsmJECI
    109056537U,	// AsmJECLGI
    440406581U,	// AsmJECLGR
    109056545U,	// AsmJECLI
    440406589U,	// AsmJECLR
    440406568U,	// AsmJECR
    108008157U,	// AsmJHAltCGI
    440406785U,	// AsmJHAltCGR
    108008149U,	// AsmJHAltCI
    109056742U,	// AsmJHAltCLGI
    440406794U,	// AsmJHAltCLGR
    109056752U,	// AsmJHAltCLI
    440406804U,	// AsmJHAltCLR
    440406777U,	// AsmJHAltCR
    108008861U,	// AsmJHCGI
    440407481U,	// AsmJHCGR
    108008855U,	// AsmJHCI
    109057444U,	// AsmJHCLGI
    440407488U,	// AsmJHCLGR
    109057452U,	// AsmJHCLI
    440407496U,	// AsmJHCLR
    440407475U,	// AsmJHCR
    108009961U,	// AsmJHEAltCGI
    440408585U,	// AsmJHEAltCGR
    108009954U,	// AsmJHEAltCI
    109058545U,	// AsmJHEAltCLGI
    440408593U,	// AsmJHEAltCLGR
    109058554U,	// AsmJHEAltCLI
    440408602U,	// AsmJHEAltCLR
    440408578U,	// AsmJHEAltCR
    108007740U,	// AsmJHECGI
    440406364U,	// AsmJHECGR
    108007733U,	// AsmJHECI
    109056324U,	// AsmJHECLGI
    440406372U,	// AsmJHECLGR
    109056333U,	// AsmJHECLI
    440406381U,	// AsmJHECLR
    440406357U,	// AsmJHECR
    108007848U,	// AsmJLAltCGI
    440406476U,	// AsmJLAltCGR
    108007840U,	// AsmJLAltCI
    109056433U,	// AsmJLAltCLGI
    440406485U,	// AsmJLAltCLGR
    109056443U,	// AsmJLAltCLI
    440406495U,	// AsmJLAltCLR
    440406468U,	// AsmJLAltCR
    108009825U,	// AsmJLCGI
    440408445U,	// AsmJLCGR
    108009819U,	// AsmJLCI
    109058408U,	// AsmJLCLGI
    440408452U,	// AsmJLCLGR
    109058416U,	// AsmJLCLI
    440408460U,	// AsmJLCLR
    440408439U,	// AsmJLCR
    108009249U,	// AsmJLEAltCGI
    440407873U,	// AsmJLEAltCGR
    108009242U,	// AsmJLEAltCI
    109057833U,	// AsmJLEAltCLGI
    440407881U,	// AsmJLEAltCLGR
    109057842U,	// AsmJLEAltCLI
    440407890U,	// AsmJLEAltCLR
    440407866U,	// AsmJLEAltCR
    108008049U,	// AsmJLECGI
    440406673U,	// AsmJLECGR
    108008042U,	// AsmJLECI
    109056633U,	// AsmJLECLGI
    440406681U,	// AsmJLECLGR
    109056642U,	// AsmJLECLI
    440406690U,	// AsmJLECLR
    440406666U,	// AsmJLECR
    108008302U,	// AsmJLHAltCGI
    440406926U,	// AsmJLHAltCGR
    108008295U,	// AsmJLHAltCI
    109056886U,	// AsmJLHAltCLGI
    440406934U,	// AsmJLHAltCLGR
    109056895U,	// AsmJLHAltCLI
    440406943U,	// AsmJLHAltCLR
    440406919U,	// AsmJLHAltCR
    108008981U,	// AsmJLHCGI
    440407605U,	// AsmJLHCGR
    108008974U,	// AsmJLHCI
    109057565U,	// AsmJLHCLGI
    440407613U,	// AsmJLHCLGR
    109057574U,	// AsmJLHCLI
    440407622U,	// AsmJLHCLR
    440407598U,	// AsmJLHCR
    269885U,	// AsmLBR
    269631U,	// AsmLEBR
    17004U,	// AsmLEJ
    16996U,	// AsmLEJG
    9441860U,	// AsmLELOC
    9441875U,	// AsmLELOCG
    1053496U,	// AsmLELOCGR
    1053488U,	// AsmLELOCR
    10490443U,	// AsmLESTOC
    10490459U,	// AsmLESTOCG
    269855U,	// AsmLHBR
    17936U,	// AsmLHJ
    17903U,	// AsmLHJG
    9442767U,	// AsmLHLOC
    9442782U,	// AsmLHLOCG
    1054439U,	// AsmLHLOCGR
    1054431U,	// AsmLHLOCR
    10491350U,	// AsmLHSTOC
    10491366U,	// AsmLHSTOCG
    18781U,	// AsmLJ
    18743U,	// AsmLJG
    9443601U,	// AsmLLOC
    9443624U,	// AsmLLOCG
    1055326U,	// AsmLLOCGR
    1055289U,	// AsmLLOCR
    579866818U,	// AsmLOC
    579867773U,	// AsmLOCG
    705699255U,	// AsmLOCGR
    705699087U,	// AsmLOCR
    10492183U,	// AsmLSTOC
    10492207U,	// AsmLSTOCG
    269644U,	// AsmNEBR
    17257U,	// AsmNEJ
    17249U,	// AsmNEJG
    9442113U,	// AsmNELOC
    9442128U,	// AsmNELOCG
    1053615U,	// AsmNELOCGR
    1053607U,	// AsmNELOCR
    10490696U,	// AsmNESTOC
    10490712U,	// AsmNESTOCG
    269874U,	// AsmNHBR
    269624U,	// AsmNHEBR
    16802U,	// AsmNHEJ
    16793U,	// AsmNHEJG
    9441653U,	// AsmNHELOC
    9441670U,	// AsmNHELOCG
    1053169U,	// AsmNHELOCGR
    1053160U,	// AsmNHELOCR
    10490237U,	// AsmNHESTOC
    10490255U,	// AsmNHESTOCG
    18204U,	// AsmNHJ
    18196U,	// AsmNHJG
    9443060U,	// AsmNHLOC
    9443075U,	// AsmNHLOCG
    1054562U,	// AsmNHLOCGR
    1054554U,	// AsmNHLOCR
    10491643U,	// AsmNHSTOC
    10491659U,	// AsmNHSTOCG
    269900U,	// AsmNLBR
    269637U,	// AsmNLEBR
    17111U,	// AsmNLEJ
    17102U,	// AsmNLEJG
    9441962U,	// AsmNLELOC
    9441979U,	// AsmNLELOCG
    1053478U,	// AsmNLELOCGR
    1053469U,	// AsmNLELOCR
    10490546U,	// AsmNLESTOC
    10490564U,	// AsmNLESTOCG
    269867U,	// AsmNLHBR
    18054U,	// AsmNLHJ
    18045U,	// AsmNLHJG
    9442905U,	// AsmNLHLOC
    9442922U,	// AsmNLHLOCG
    1054421U,	// AsmNLHLOCGR
    1054412U,	// AsmNLHLOCR
    10491489U,	// AsmNLHSTOC
    10491507U,	// AsmNLHSTOCG
    18916U,	// AsmNLJ
    18908U,	// AsmNLJG
    9443772U,	// AsmNLLOC
    9443787U,	// AsmNLLOCG
    1055274U,	// AsmNLLOCGR
    1055266U,	// AsmNLLOCR
    10492355U,	// AsmNLSTOC
    10492371U,	// AsmNLSTOCG
    269921U,	// AsmNOBR
    19228U,	// AsmNOJ
    19222U,	// AsmNOJG
    9444086U,	// AsmNOLOC
    9444101U,	// AsmNOLOCG
    1055529U,	// AsmNOLOCGR
    1055521U,	// AsmNOLOCR
    10492669U,	// AsmNOSTOC
    10492685U,	// AsmNOSTOCG
    269916U,	// AsmOBR
    19186U,	// AsmOJ
    19181U,	// AsmOJG
    9444049U,	// AsmOLOC
    9444062U,	// AsmOLOCG
    1055545U,	// AsmOLOCGR
    1055538U,	// AsmOLOCR
    10492631U,	// AsmOSTOC
    10492645U,	// AsmOSTOCG
    715133127U,	// AsmSTOC
    715134083U,	// AsmSTOCG
    4202092U,	// BASR
    269129U,	// BR
    11542157U,	// BRAS
    11541175U,	// BRASL
    24476U,	// BRC
    24471U,	// BRCL
    12590747U,	// BRCT
    12588273U,	// BRCTG
    13635752U,	// C
    13635653U,	// CDB
    4201294U,	// CDBR
    4201519U,	// CDFBR
    4201564U,	// CDGBR
    719330365U,	// CDLFBR
    719330410U,	// CDLGBR
    13635698U,	// CEB
    4201413U,	// CEBR
    4201526U,	// CEFBR
    4201571U,	// CEGBR
    719330373U,	// CELFBR
    719330418U,	// CELGBR
    14687073U,	// CFDBR
    14687200U,	// CFEBR
    15734664U,	// CFI
    0U,	// CFIMux
    14687402U,	// CFXBR
    13636729U,	// CG
    14687088U,	// CGDBR
    14687215U,	// CGEBR
    13636561U,	// CGF
    15734675U,	// CGFI
    4201823U,	// CGFR
    16783943U,	// CGFRL
    13636932U,	// CGH
    17831901U,	// CGHI
    16783994U,	// CGHRL
    337971U,	// CGHSI
    421790U,	// CGIJ
    4201906U,	// CGR
    19034034U,	// CGRJ
    16783968U,	// CGRL
    14687417U,	// CGXBR
    13636896U,	// CH
    13636608U,	// CHF
    337986U,	// CHHSI
    17831890U,	// CHI
    16783988U,	// CHRL
    337957U,	// CHSI
    13639410U,	// CHY
    15734156U,	// CIH
    421786U,	// CIJ
    13637907U,	// CL
    28852U,	// CLC
    0U,	// CLCLoop
    0U,	// CLCSequence
    719330152U,	// CLFDBR
    719330279U,	// CLFEBR
    469035U,	// CLFHSI
    19929019U,	// CLFI
    0U,	// CLFIMux
    719330481U,	// CLFXBR
    13636773U,	// CLG
    719330167U,	// CLGDBR
    719330294U,	// CLGEBR
    13636572U,	// CLGF
    19928992U,	// CLGFI
    4201836U,	// CLGFR
    16783950U,	// CLGFRL
    16784001U,	// CLGHRL
    469050U,	// CLGHSI
    552867U,	// CLGIJ
    4201930U,	// CLGR
    19034039U,	// CLGRJ
    16783974U,	// CLGRL
    719330496U,	// CLGXBR
    13636644U,	// CLHF
    469065U,	// CLHHSI
    16784017U,	// CLHRL
    600077U,	// CLI
    19928465U,	// CLIH
    552873U,	// CLIJ
    601868U,	// CLIY
    0U,	// CLMux
    4202050U,	// CLR
    19034045U,	// CLRJ
    16784038U,	// CLRL
    4202149U,	// CLST
    0U,	// CLSTLoop
    13639468U,	// CLY
    0U,	// CMux
    171973920U,	// CPSDRdd
    171973920U,	// CPSDRds
    171973920U,	// CPSDRsd
    171973920U,	// CPSDRss
    4201720U,	// CR
    19034030U,	// CRJ
    16783931U,	// CRL
    839917203U,	// CS
    839914722U,	// CSG
    839917379U,	// CSY
    4201622U,	// CXBR
    4201557U,	// CXFBR
    4201602U,	// CXGBR
    719330381U,	// CXLFBR
    719330426U,	// CXLGBR
    13639373U,	// CY
    0U,	// CallBASR
    0U,	// CallBR
    0U,	// CallBRASL
    0U,	// CallJG
    0U,	// CondStore16
    0U,	// CondStore16Inv
    0U,	// CondStore16Mux
    0U,	// CondStore16MuxInv
    0U,	// CondStore32
    0U,	// CondStore32Inv
    0U,	// CondStore64
    0U,	// CondStore64Inv
    0U,	// CondStore8
    0U,	// CondStore8Inv
    0U,	// CondStore8Mux
    0U,	// CondStore8MuxInv
    0U,	// CondStoreF32
    0U,	// CondStoreF32Inv
    0U,	// CondStoreF64
    0U,	// CondStoreF64Inv
    4170U,	// DDB
    1055572U,	// DDBR
    4216U,	// DEB
    1055692U,	// DEBR
    6436U,	// DL
    5290U,	// DLG
    1056208U,	// DLGR
    1056327U,	// DLR
    5351U,	// DSG
    5102U,	// DSGF
    1056143U,	// DSGFR
    1056245U,	// DSGR
    1055901U,	// DXBR
    20978497U,	// EAR
    14687103U,	// FIDBR
    719327250U,	// FIDBRA
    14687230U,	// FIEBR
    719327258U,	// FIEBRA
    14687432U,	// FIXBR
    719327282U,	// FIXBRA
    4201960U,	// FLOGR
    0U,	// GOT
    4267U,	// IC
    4267U,	// IC32
    7884U,	// IC32Y
    7884U,	// ICY
    0U,	// IIFMux
    19928069U,	// IIHF
    0U,	// IIHF64
    22025564U,	// IIHH
    0U,	// IIHH64
    22026556U,	// IIHL
    0U,	// IIHL64
    0U,	// IIHMux
    19928106U,	// IILF
    0U,	// IILF64
    22025717U,	// IILH
    0U,	// IILH64
    22026643U,	// IILL
    0U,	// IILL64
    0U,	// IILMux
    268990U,	// IPM
    18524U,	// J
    17554U,	// JG
    13637902U,	// L
    0U,	// L128
    13635590U,	// LA
    977276929U,	// LAA
    977278025U,	// LAAG
    977279243U,	// LAAL
    977278102U,	// LAALG
    977279687U,	// LAN
    977278166U,	// LANG
    977279692U,	// LAO
    977278172U,	// LAOG
    16783923U,	// LARL
    977280706U,	// LAX
    977278223U,	// LAXG
    13639367U,	// LAY
    13635745U,	// LB
    13636889U,	// LBH
    0U,	// LBMux
    4201610U,	// LBR
    4201293U,	// LCDBR
    4201412U,	// LCEBR
    4201822U,	// LCGFR
    4201905U,	// LCGR
    4201732U,	// LCR
    4201621U,	// LCXBR
    13635813U,	// LD
    13635703U,	// LDEB
    4201419U,	// LDEBR
    4201918U,	// LDGR
    4201755U,	// LDR
    4201628U,	// LDXBR
    719327266U,	// LDXBRA
    13639383U,	// LDY
    13636167U,	// LE
    4201306U,	// LEDBR
    719327242U,	// LEDBRA
    4201792U,	// LER
    4201635U,	// LEXBR
    719327274U,	// LEXBRA
    13639394U,	// LEY
    13636919U,	// LFH
    13636761U,	// LG
    13635739U,	// LGB
    4201580U,	// LGBR
    4201749U,	// LGDR
    13636567U,	// LGF
    15734682U,	// LGFI
    4201830U,	// LGFR
    16783951U,	// LGFRL
    13636951U,	// LGH
    17831907U,	// LGHI
    4202009U,	// LGHR
    16784002U,	// LGHRL
    4201925U,	// LGR
    16783975U,	// LGRL
    13637074U,	// LH
    13636982U,	// LHH
    17831933U,	// LHI
    0U,	// LHIMux
    0U,	// LHMux
    4202016U,	// LHR
    16784018U,	// LHRL
    13639415U,	// LHY
    13635769U,	// LLC
    13636894U,	// LLCH
    0U,	// LLCMux
    4201737U,	// LLCR
    0U,	// LLCRMux
    13635749U,	// LLGC
    4201724U,	// LLGCR
    13636578U,	// LLGF
    4201843U,	// LLGFR
    16783958U,	// LLGFRL
    13636950U,	// LLGH
    4202008U,	// LLGHR
    16784009U,	// LLGHRL
    13637198U,	// LLH
    13636981U,	// LLHH
    0U,	// LLHMux
    4202021U,	// LLHR
    16784024U,	// LLHRL
    0U,	// LLHRMux
    19928075U,	// LLIHF
    23074146U,	// LLIHH
    23075138U,	// LLIHL
    19928112U,	// LLILF
    23074299U,	// LLILH
    23075225U,	// LLILL
    977278155U,	// LMG
    0U,	// LMux
    4201356U,	// LNDBR
    4201477U,	// LNEBR
    4201857U,	// LNGFR
    4201954U,	// LNGR
    4202071U,	// LNR
    4201685U,	// LNXBR
    36739U,	// LOC
    36748U,	// LOCG
    40903U,	// LOCGR
    40898U,	// LOCR
    4201363U,	// LPDBR
    4201484U,	// LPEBR
    4201864U,	// LPGFR
    4201967U,	// LPGR
    4202087U,	// LPR
    4201692U,	// LPXBR
    4202041U,	// LR
    16784039U,	// LRL
    0U,	// LRMux
    13639351U,	// LRV
    13636866U,	// LRVG
    4201991U,	// LRVGR
    4202108U,	// LRVR
    13639329U,	// LT
    4201384U,	// LTDBR
    4201384U,	// LTDBRCompare
    4201505U,	// LTEBR
    4201505U,	// LTEBRCompare
    13636856U,	// LTG
    13636602U,	// LTGF
    4201885U,	// LTGFR
    4201985U,	// LTGR
    4202103U,	// LTR
    4201712U,	// LTXBR
    4201712U,	// LTXBRCompare
    0U,	// LX
    13635680U,	// LXDB
    4201391U,	// LXDBR
    13635733U,	// LXEB
    4201512U,	// LXEBR
    4202114U,	// LXR
    13639464U,	// LY
    269607U,	// LZDR
    269650U,	// LZER
    269959U,	// LZXR
    1108348991U,	// MADB
    1242569542U,	// MADBR
    1108349036U,	// MAEB
    1242569661U,	// MAEBR
    4175U,	// MDB
    1055622U,	// MDBR
    4221U,	// MDEB
    1055698U,	// MDEBR
    4227U,	// MEEB
    1055705U,	// MEEBR
    3151849U,	// MGHI
    5872U,	// MH
    3151874U,	// MHI
    7932U,	// MHY
    5307U,	// MLG
    1056214U,	// MLGR
    7831U,	// MS
    1108349018U,	// MSDB
    1242569633U,	// MSDBR
    1108349071U,	// MSEB
    1242569754U,	// MSEBR
    2103239U,	// MSFI
    5356U,	// MSG
    5108U,	// MSGF
    2103214U,	// MSGFI
    1056150U,	// MSGFR
    1056251U,	// MSGR
    1056370U,	// MSR
    8008U,	// MSY
    28887U,	// MVC
    0U,	// MVCLoop
    0U,	// MVCSequence
    337903U,	// MVGHI
    337910U,	// MVHHI
    337927U,	// MVHI
    600145U,	// MVI
    601884U,	// MVIY
    4202161U,	// MVST
    0U,	// MVSTLoop
    1055951U,	// MXBR
    4198U,	// MXDB
    1055670U,	// MXDBR
    6857U,	// N
    28862U,	// NC
    0U,	// NCLoop
    0U,	// NCSequence
    5336U,	// NG
    1056227U,	// NGR
    171972819U,	// NGRK
    600082U,	// NI
    0U,	// NIFMux
    5248018U,	// NIHF
    0U,	// NIHF64
    22025577U,	// NIHH
    0U,	// NIHH64
    22026569U,	// NIHL
    0U,	// NIHL64
    0U,	// NIHMux
    5248055U,	// NILF
    0U,	// NILF64
    22025730U,	// NILH
    0U,	// NILH64
    22026656U,	// NILL
    0U,	// NILL64
    0U,	// NILMux
    601874U,	// NIY
    1056344U,	// NR
    171972855U,	// NRK
    7995U,	// NY
    6862U,	// O
    28867U,	// OC
    0U,	// OCLoop
    0U,	// OCSequence
    5342U,	// OG
    1056234U,	// OGR
    171972825U,	// OGRK
    600086U,	// OI
    0U,	// OIFMux
    5248024U,	// OIHF
    0U,	// OIHF64
    22025583U,	// OIHH
    0U,	// OIHH64
    22026575U,	// OIHL
    0U,	// OIHL64
    0U,	// OIHMux
    5248061U,	// OILF
    0U,	// OILF64
    22025736U,	// OILH
    0U,	// OILH64
    22026662U,	// OILL
    0U,	// OILL64
    0U,	// OILMux
    601879U,	// OIY
    1056349U,	// OR
    171972860U,	// ORK
    7999U,	// OY
    667872U,	// PFD
    211520U,	// PFDRL
    1376785499U,	// RISBG
    1376785499U,	// RISBG32
    1376785546U,	// RISBHG
    0U,	// RISBHH
    0U,	// RISBHL
    1376785565U,	// RISBLG
    0U,	// RISBLH
    0U,	// RISBLL
    0U,	// RISBMux
    977279410U,	// RLL
    977278127U,	// RLLG
    1376785506U,	// RNSBG
    1376785513U,	// ROSBG
    1376785520U,	// RXSBG
    0U,	// Return
    7824U,	// S
    4187U,	// SDB
    1055650U,	// SDBR
    4240U,	// SEB
    1055771U,	// SEBR
    5347U,	// SG
    5103U,	// SGF
    1056144U,	// SGFR
    1056246U,	// SGR
    171972831U,	// SGRK
    6010U,	// SH
    7937U,	// SHY
    6842U,	// SL
    4256U,	// SLB
    5205U,	// SLBG
    1055881U,	// SLBR
    5248961U,	// SLFI
    5318U,	// SLG
    1056169U,	// SLGBR
    5096U,	// SLGF
    5248935U,	// SLGFI
    1056122U,	// SLGFR
    1056220U,	// SLGR
    171972812U,	// SLGRK
    9443767U,	// SLL
    977278133U,	// SLLG
    977279150U,	// SLLK
    1056338U,	// SLR
    171972849U,	// SLRK
    7985U,	// SLY
    13635668U,	// SQDB
    4201370U,	// SQDBR
    13635721U,	// SQEB
    4201491U,	// SQEBR
    4201699U,	// SQXBR
    1056366U,	// SR
    9441338U,	// SRA
    977278031U,	// SRAG
    977279114U,	// SRAK
    171972865U,	// SRK
    9444012U,	// SRL
    977278144U,	// SRLG
    977279156U,	// SRLK
    4202155U,	// SRST
    0U,	// SRSTLoop
    13639335U,	// ST
    0U,	// ST128
    13635794U,	// STC
    13636913U,	// STCH
    0U,	// STCMux
    13639377U,	// STCY
    13635817U,	// STD
    13639388U,	// STDY
    13636551U,	// STE
    13639399U,	// STEY
    13636924U,	// STFH
    13636861U,	// STG
    16783981U,	// STGRL
    13637502U,	// STH
    13636993U,	// STHH
    0U,	// STHMux
    16784031U,	// STHRL
    13639430U,	// STHY
    977278160U,	// STMG
    0U,	// STMux
    44935U,	// STOC
    44945U,	// STOCG
    16784049U,	// STRL
    13639356U,	// STRV
    13636872U,	// STRVG
    0U,	// STX
    13639501U,	// STY
    1055978U,	// SXBR
    8004U,	// SY
    0U,	// Select32
    0U,	// Select32Mux
    0U,	// Select64
    0U,	// SelectF128
    0U,	// SelectF32
    0U,	// SelectF64
    0U,	// Serialize
    0U,	// TLS_GDCALL
    0U,	// TLS_LDCALL
    600771U,	// TM
    23074171U,	// TMHH
    0U,	// TMHH64
    23075157U,	// TMHL
    0U,	// TMHL64
    0U,	// TMHMux
    23074387U,	// TMLH
    0U,	// TMLH64
    23075244U,	// TMLL
    0U,	// TMLL64
    0U,	// TMLMux
    601910U,	// TMY
    7876U,	// X
    28892U,	// XC
    0U,	// XCLoop
    0U,	// XCSequence
    5393U,	// XG
    1056270U,	// XGR
    171972837U,	// XGRK
    600150U,	// XI
    0U,	// XIFMux
    5248030U,	// XIHF
    0U,	// XIHF64
    5248067U,	// XILF
    0U,	// XILF64
    601890U,	// XIY
    1056387U,	// XR
    171972870U,	// XRK
    8018U,	// XY
    0U,	// ZEXT128_32
    0U,	// ZEXT128_64
    0U
  };

#ifndef CAPSTONE_DIET
  static const char AsmStrs[] = {
  /* 0 */ 'l', 'a', 'a', 9, 0,
  /* 5 */ 'l', 'a', 9, 0,
  /* 9 */ 'l', 'e', 'd', 'b', 'r', 'a', 9, 0,
  /* 17 */ 'f', 'i', 'd', 'b', 'r', 'a', 9, 0,
  /* 25 */ 'f', 'i', 'e', 'b', 'r', 'a', 9, 0,
  /* 33 */ 'l', 'd', 'x', 'b', 'r', 'a', 9, 0,
  /* 41 */ 'l', 'e', 'x', 'b', 'r', 'a', 9, 0,
  /* 49 */ 'f', 'i', 'x', 'b', 'r', 'a', 9, 0,
  /* 57 */ 's', 'r', 'a', 9, 0,
  /* 62 */ 'm', 'a', 'd', 'b', 9, 0,
  /* 68 */ 'c', 'd', 'b', 9, 0,
  /* 73 */ 'd', 'd', 'b', 9, 0,
  /* 78 */ 'm', 'd', 'b', 9, 0,
  /* 83 */ 's', 'q', 'd', 'b', 9, 0,
  /* 89 */ 'm', 's', 'd', 'b', 9, 0,
  /* 95 */ 'l', 'x', 'd', 'b', 9, 0,
  /* 101 */ 'm', 'x', 'd', 'b', 9, 0,
  /* 107 */ 'm', 'a', 'e', 'b', 9, 0,
  /* 113 */ 'c', 'e', 'b', 9, 0,
  /* 118 */ 'l', 'd', 'e', 'b', 9, 0,
  /* 124 */ 'm', 'd', 'e', 'b', 9, 0,
  /* 130 */ 'm', 'e', 'e', 'b', 9, 0,
  /* 136 */ 's', 'q', 'e', 'b', 9, 0,
  /* 142 */ 'm', 's', 'e', 'b', 9, 0,
  /* 148 */ 'l', 'x', 'e', 'b', 9, 0,
  /* 154 */ 'l', 'g', 'b', 9, 0,
  /* 159 */ 's', 'l', 'b', 9, 0,
  /* 164 */ 'l', 'l', 'g', 'c', 9, 0,
  /* 170 */ 'i', 'c', 9, 0,
  /* 174 */ 'a', 'l', 'c', 9, 0,
  /* 179 */ 'c', 'l', 'c', 9, 0,
  /* 184 */ 'l', 'l', 'c', 9, 0,
  /* 189 */ 'n', 'c', 9, 0,
  /* 193 */ 'l', 'o', 'c', 9, 0,
  /* 198 */ 's', 't', 'o', 'c', 9, 0,
  /* 204 */ 'b', 'r', 'c', 9, 0,
  /* 209 */ 's', 't', 'c', 9, 0,
  /* 214 */ 'm', 'v', 'c', 9, 0,
  /* 219 */ 'x', 'c', 9, 0,
  /* 223 */ 'p', 'f', 'd', 9, 0,
  /* 228 */ 'l', 'd', 9, 0,
  /* 232 */ 's', 't', 'd', 9, 0,
  /* 237 */ 'l', 'o', 'c', 'e', 9, 0,
  /* 243 */ 's', 't', 'o', 'c', 'e', 9, 0,
  /* 250 */ 'l', 'o', 'c', 'g', 'e', 9, 0,
  /* 257 */ 's', 't', 'o', 'c', 'g', 'e', 9, 0,
  /* 265 */ 'j', 'g', 'e', 9, 0,
  /* 270 */ 'l', 'o', 'c', 'h', 'e', 9, 0,
  /* 277 */ 's', 't', 'o', 'c', 'h', 'e', 9, 0,
  /* 285 */ 'l', 'o', 'c', 'g', 'h', 'e', 9, 0,
  /* 293 */ 's', 't', 'o', 'c', 'g', 'h', 'e', 9, 0,
  /* 302 */ 'j', 'g', 'h', 'e', 9, 0,
  /* 308 */ 'c', 'i', 'j', 'h', 'e', 9, 0,
  /* 315 */ 'c', 'g', 'i', 'j', 'h', 'e', 9, 0,
  /* 323 */ 'c', 'l', 'g', 'i', 'j', 'h', 'e', 9, 0,
  /* 332 */ 'c', 'l', 'i', 'j', 'h', 'e', 9, 0,
  /* 340 */ 'c', 'r', 'j', 'h', 'e', 9, 0,
  /* 347 */ 'c', 'g', 'r', 'j', 'h', 'e', 9, 0,
  /* 355 */ 'c', 'l', 'g', 'r', 'j', 'h', 'e', 9, 0,
  /* 364 */ 'c', 'l', 'r', 'j', 'h', 'e', 9, 0,
  /* 372 */ 'l', 'o', 'c', 'n', 'h', 'e', 9, 0,
  /* 380 */ 's', 't', 'o', 'c', 'n', 'h', 'e', 9, 0,
  /* 389 */ 'l', 'o', 'c', 'g', 'n', 'h', 'e', 9, 0,
  /* 398 */ 's', 't', 'o', 'c', 'g', 'n', 'h', 'e', 9, 0,
  /* 408 */ 'j', 'g', 'n', 'h', 'e', 9, 0,
  /* 415 */ 'c', 'i', 'j', 'n', 'h', 'e', 9, 0,
  /* 423 */ 'c', 'g', 'i', 'j', 'n', 'h', 'e', 9, 0,
  /* 432 */ 'c', 'l', 'g', 'i', 'j', 'n', 'h', 'e', 9, 0,
  /* 442 */ 'c', 'l', 'i', 'j', 'n', 'h', 'e', 9, 0,
  /* 451 */ 'c', 'r', 'j', 'n', 'h', 'e', 9, 0,
  /* 459 */ 'c', 'g', 'r', 'j', 'n', 'h', 'e', 9, 0,
  /* 468 */ 'c', 'l', 'g', 'r', 'j', 'n', 'h', 'e', 9, 0,
  /* 478 */ 'c', 'l', 'r', 'j', 'n', 'h', 'e', 9, 0,
  /* 487 */ 'l', 'o', 'c', 'r', 'n', 'h', 'e', 9, 0,
  /* 496 */ 'l', 'o', 'c', 'g', 'r', 'n', 'h', 'e', 9, 0,
  /* 506 */ 'l', 'o', 'c', 'r', 'h', 'e', 9, 0,
  /* 514 */ 'l', 'o', 'c', 'g', 'r', 'h', 'e', 9, 0,
  /* 523 */ 'c', 'i', 'j', 'e', 9, 0,
  /* 529 */ 'c', 'g', 'i', 'j', 'e', 9, 0,
  /* 536 */ 'c', 'l', 'g', 'i', 'j', 'e', 9, 0,
  /* 544 */ 'c', 'l', 'i', 'j', 'e', 9, 0,
  /* 551 */ 'c', 'r', 'j', 'e', 9, 0,
  /* 557 */ 'c', 'g', 'r', 'j', 'e', 9, 0,
  /* 564 */ 'c', 'l', 'g', 'r', 'j', 'e', 9, 0,
  /* 572 */ 'c', 'l', 'r', 'j', 'e', 9, 0,
  /* 579 */ 'l', 'o', 'c', 'l', 'e', 9, 0,
  /* 586 */ 's', 't', 'o', 'c', 'l', 'e', 9, 0,
  /* 594 */ 'l', 'o', 'c', 'g', 'l', 'e', 9, 0,
  /* 602 */ 's', 't', 'o', 'c', 'g', 'l', 'e', 9, 0,
  /* 611 */ 'j', 'g', 'l', 'e', 9, 0,
  /* 617 */ 'c', 'i', 'j', 'l', 'e', 9, 0,
  /* 624 */ 'c', 'g', 'i', 'j', 'l', 'e', 9, 0,
  /* 632 */ 'c', 'l', 'g', 'i', 'j', 'l', 'e', 9, 0,
  /* 641 */ 'c', 'l', 'i', 'j', 'l', 'e', 9, 0,
  /* 649 */ 'c', 'r', 'j', 'l', 'e', 9, 0,
  /* 656 */ 'c', 'g', 'r', 'j', 'l', 'e', 9, 0,
  /* 664 */ 'c', 'l', 'g', 'r', 'j', 'l', 'e', 9, 0,
  /* 673 */ 'c', 'l', 'r', 'j', 'l', 'e', 9, 0,
  /* 681 */ 'l', 'o', 'c', 'n', 'l', 'e', 9, 0,
  /* 689 */ 's', 't', 'o', 'c', 'n', 'l', 'e', 9, 0,
  /* 698 */ 'l', 'o', 'c', 'g', 'n', 'l', 'e', 9, 0,
  /* 707 */ 's', 't', 'o', 'c', 'g', 'n', 'l', 'e', 9, 0,
  /* 717 */ 'j', 'g', 'n', 'l', 'e', 9, 0,
  /* 724 */ 'c', 'i', 'j', 'n', 'l', 'e', 9, 0,
  /* 732 */ 'c', 'g', 'i', 'j', 'n', 'l', 'e', 9, 0,
  /* 741 */ 'c', 'l', 'g', 'i', 'j', 'n', 'l', 'e', 9, 0,
  /* 751 */ 'c', 'l', 'i', 'j', 'n', 'l', 'e', 9, 0,
  /* 760 */ 'c', 'r', 'j', 'n', 'l', 'e', 9, 0,
  /* 768 */ 'c', 'g', 'r', 'j', 'n', 'l', 'e', 9, 0,
  /* 777 */ 'c', 'l', 'g', 'r', 'j', 'n', 'l', 'e', 9, 0,
  /* 787 */ 'c', 'l', 'r', 'j', 'n', 'l', 'e', 9, 0,
  /* 796 */ 'l', 'o', 'c', 'r', 'n', 'l', 'e', 9, 0,
  /* 805 */ 'l', 'o', 'c', 'g', 'r', 'n', 'l', 'e', 9, 0,
  /* 815 */ 'l', 'o', 'c', 'r', 'l', 'e', 9, 0,
  /* 823 */ 'l', 'o', 'c', 'g', 'r', 'l', 'e', 9, 0,
  /* 832 */ 'l', 'o', 'c', 'n', 'e', 9, 0,
  /* 839 */ 's', 't', 'o', 'c', 'n', 'e', 9, 0,
  /* 847 */ 'l', 'o', 'c', 'g', 'n', 'e', 9, 0,
  /* 855 */ 's', 't', 'o', 'c', 'g', 'n', 'e', 9, 0,
  /* 864 */ 'j', 'g', 'n', 'e', 9, 0,
  /* 870 */ 'c', 'i', 'j', 'n', 'e', 9, 0,
  /* 877 */ 'c', 'g', 'i', 'j', 'n', 'e', 9, 0,
  /* 885 */ 'c', 'l', 'g', 'i', 'j', 'n', 'e', 9, 0,
  /* 894 */ 'c', 'l', 'i', 'j', 'n', 'e', 9, 0,
  /* 902 */ 'c', 'r', 'j', 'n', 'e', 9, 0,
  /* 909 */ 'c', 'g', 'r', 'j', 'n', 'e', 9, 0,
  /* 917 */ 'c', 'l', 'g', 'r', 'j', 'n', 'e', 9, 0,
  /* 926 */ 'c', 'l', 'r', 'j', 'n', 'e', 9, 0,
  /* 934 */ 'l', 'o', 'c', 'r', 'n', 'e', 9, 0,
  /* 942 */ 'l', 'o', 'c', 'g', 'r', 'n', 'e', 9, 0,
  /* 951 */ 'l', 'o', 'c', 'r', 'e', 9, 0,
  /* 958 */ 'l', 'o', 'c', 'g', 'r', 'e', 9, 0,
  /* 966 */ 's', 't', 'e', 9, 0,
  /* 971 */ 'a', 'g', 'f', 9, 0,
  /* 976 */ 'c', 'g', 'f', 9, 0,
  /* 981 */ 'a', 'l', 'g', 'f', 9, 0,
  /* 987 */ 'c', 'l', 'g', 'f', 9, 0,
  /* 993 */ 'l', 'l', 'g', 'f', 9, 0,
  /* 999 */ 's', 'l', 'g', 'f', 9, 0,
  /* 1005 */ 'd', 's', 'g', 'f', 9, 0,
  /* 1011 */ 'm', 's', 'g', 'f', 9, 0,
  /* 1017 */ 'l', 't', 'g', 'f', 9, 0,
  /* 1023 */ 'c', 'h', 'f', 9, 0,
  /* 1028 */ 'i', 'i', 'h', 'f', 9, 0,
  /* 1034 */ 'l', 'l', 'i', 'h', 'f', 9, 0,
  /* 1041 */ 'n', 'i', 'h', 'f', 9, 0,
  /* 1047 */ 'o', 'i', 'h', 'f', 9, 0,
  /* 1053 */ 'x', 'i', 'h', 'f', 9, 0,
  /* 1059 */ 'c', 'l', 'h', 'f', 9, 0,
  /* 1065 */ 'i', 'i', 'l', 'f', 9, 0,
  /* 1071 */ 'l', 'l', 'i', 'l', 'f', 9, 0,
  /* 1078 */ 'n', 'i', 'l', 'f', 9, 0,
  /* 1084 */ 'o', 'i', 'l', 'f', 9, 0,
  /* 1090 */ 'x', 'i', 'l', 'f', 9, 0,
  /* 1096 */ 'l', 'a', 'a', 'g', 9, 0,
  /* 1102 */ 's', 'r', 'a', 'g', 9, 0,
  /* 1108 */ 's', 'l', 'b', 'g', 9, 0,
  /* 1114 */ 'r', 'i', 's', 'b', 'g', 9, 0,
  /* 1121 */ 'r', 'n', 's', 'b', 'g', 9, 0,
  /* 1128 */ 'r', 'o', 's', 'b', 'g', 9, 0,
  /* 1135 */ 'r', 'x', 's', 'b', 'g', 9, 0,
  /* 1142 */ 'a', 'l', 'c', 'g', 9, 0,
  /* 1148 */ 'l', 'o', 'c', 'g', 9, 0,
  /* 1154 */ 's', 't', 'o', 'c', 'g', 9, 0,
  /* 1161 */ 'r', 'i', 's', 'b', 'h', 'g', 9, 0,
  /* 1169 */ 'j', 'g', 9, 0,
  /* 1173 */ 'l', 'a', 'a', 'l', 'g', 9, 0,
  /* 1180 */ 'r', 'i', 's', 'b', 'l', 'g', 9, 0,
  /* 1188 */ 'c', 'l', 'g', 9, 0,
  /* 1193 */ 'd', 'l', 'g', 9, 0,
  /* 1198 */ 'r', 'l', 'l', 'g', 9, 0,
  /* 1204 */ 's', 'l', 'l', 'g', 9, 0,
  /* 1210 */ 'm', 'l', 'g', 9, 0,
  /* 1215 */ 's', 'r', 'l', 'g', 9, 0,
  /* 1221 */ 's', 'l', 'g', 9, 0,
  /* 1226 */ 'l', 'm', 'g', 9, 0,
  /* 1231 */ 's', 't', 'm', 'g', 9, 0,
  /* 1237 */ 'l', 'a', 'n', 'g', 9, 0,
  /* 1243 */ 'l', 'a', 'o', 'g', 9, 0,
  /* 1249 */ 'c', 's', 'g', 9, 0,
  /* 1254 */ 'd', 's', 'g', 9, 0,
  /* 1259 */ 'm', 's', 'g', 9, 0,
  /* 1264 */ 'b', 'r', 'c', 't', 'g', 9, 0,
  /* 1271 */ 'l', 't', 'g', 9, 0,
  /* 1276 */ 's', 't', 'g', 9, 0,
  /* 1281 */ 'l', 'r', 'v', 'g', 9, 0,
  /* 1287 */ 's', 't', 'r', 'v', 'g', 9, 0,
  /* 1294 */ 'l', 'a', 'x', 'g', 9, 0,
  /* 1300 */ 'a', 'h', 9, 0,
  /* 1304 */ 'l', 'b', 'h', 9, 0,
  /* 1309 */ 'l', 'l', 'c', 'h', 9, 0,
  /* 1315 */ 'l', 'o', 'c', 'h', 9, 0,
  /* 1321 */ 's', 't', 'o', 'c', 'h', 9, 0,
  /* 1328 */ 's', 't', 'c', 'h', 9, 0,
  /* 1334 */ 'l', 'f', 'h', 9, 0,
  /* 1339 */ 's', 't', 'f', 'h', 9, 0,
  /* 1345 */ 'l', 'o', 'c', 'g', 'h', 9, 0,
  /* 1352 */ 's', 't', 'o', 'c', 'g', 'h', 9, 0,
  /* 1360 */ 'j', 'g', 'h', 9, 0,
  /* 1365 */ 'l', 'l', 'g', 'h', 9, 0,
  /* 1371 */ 'i', 'i', 'h', 'h', 9, 0,
  /* 1377 */ 'l', 'l', 'i', 'h', 'h', 9, 0,
  /* 1384 */ 'n', 'i', 'h', 'h', 9, 0,
  /* 1390 */ 'o', 'i', 'h', 'h', 9, 0,
  /* 1396 */ 'l', 'l', 'h', 'h', 9, 0,
  /* 1402 */ 't', 'm', 'h', 'h', 9, 0,
  /* 1408 */ 's', 't', 'h', 'h', 9, 0,
  /* 1414 */ 'a', 'i', 'h', 9, 0,
  /* 1419 */ 'c', 'i', 'h', 9, 0,
  /* 1424 */ 'c', 'l', 'i', 'h', 9, 0,
  /* 1430 */ 'c', 'i', 'j', 'h', 9, 0,
  /* 1436 */ 'c', 'g', 'i', 'j', 'h', 9, 0,
  /* 1443 */ 'c', 'l', 'g', 'i', 'j', 'h', 9, 0,
  /* 1451 */ 'c', 'l', 'i', 'j', 'h', 9, 0,
  /* 1458 */ 'c', 'r', 'j', 'h', 9, 0,
  /* 1464 */ 'c', 'g', 'r', 'j', 'h', 9, 0,
  /* 1471 */ 'c', 'l', 'g', 'r', 'j', 'h', 9, 0,
  /* 1479 */ 'c', 'l', 'r', 'j', 'h', 9, 0,
  /* 1486 */ 'l', 'o', 'c', 'l', 'h', 9, 0,
  /* 1493 */ 's', 't', 'o', 'c', 'l', 'h', 9, 0,
  /* 1501 */ 'l', 'o', 'c', 'g', 'l', 'h', 9, 0,
  /* 1509 */ 's', 't', 'o', 'c', 'g', 'l', 'h', 9, 0,
  /* 1518 */ 'j', 'g', 'l', 'h', 9, 0,
  /* 1524 */ 'i', 'i', 'l', 'h', 9, 0,
  /* 1530 */ 'l', 'l', 'i', 'l', 'h', 9, 0,
  /* 1537 */ 'n', 'i', 'l', 'h', 9, 0,
  /* 1543 */ 'o', 'i', 'l', 'h', 9, 0,
  /* 1549 */ 'c', 'i', 'j', 'l', 'h', 9, 0,
  /* 1556 */ 'c', 'g', 'i', 'j', 'l', 'h', 9, 0,
  /* 1564 */ 'c', 'l', 'g', 'i', 'j', 'l', 'h', 9, 0,
  /* 1573 */ 'c', 'l', 'i', 'j', 'l', 'h', 9, 0,
  /* 1581 */ 'c', 'r', 'j', 'l', 'h', 9, 0,
  /* 1588 */ 'c', 'g', 'r', 'j', 'l', 'h', 9, 0,
  /* 1596 */ 'c', 'l', 'g', 'r', 'j', 'l', 'h', 9, 0,
  /* 1605 */ 'c', 'l', 'r', 'j', 'l', 'h', 9, 0,
  /* 1613 */ 'l', 'l', 'h', 9, 0,
  /* 1618 */ 't', 'm', 'l', 'h', 9, 0,
  /* 1624 */ 'l', 'o', 'c', 'n', 'l', 'h', 9, 0,
  /* 1632 */ 's', 't', 'o', 'c', 'n', 'l', 'h', 9, 0,
  /* 1641 */ 'l', 'o', 'c', 'g', 'n', 'l', 'h', 9, 0,
  /* 1650 */ 's', 't', 'o', 'c', 'g', 'n', 'l', 'h', 9, 0,
  /* 1660 */ 'j', 'g', 'n', 'l', 'h', 9, 0,
  /* 1667 */ 'c', 'i', 'j', 'n', 'l', 'h', 9, 0,
  /* 1675 */ 'c', 'g', 'i', 'j', 'n', 'l', 'h', 9, 0,
  /* 1684 */ 'c', 'l', 'g', 'i', 'j', 'n', 'l', 'h', 9, 0,
  /* 1694 */ 'c', 'l', 'i', 'j', 'n', 'l', 'h', 9, 0,
  /* 1703 */ 'c', 'r', 'j', 'n', 'l', 'h', 9, 0,
  /* 1711 */ 'c', 'g', 'r', 'j', 'n', 'l', 'h', 9, 0,
  /* 1720 */ 'c', 'l', 'g', 'r', 'j', 'n', 'l', 'h', 9, 0,
  /* 1730 */ 'c', 'l', 'r', 'j', 'n', 'l', 'h', 9, 0,
  /* 1739 */ 'l', 'o', 'c', 'r', 'n', 'l', 'h', 9, 0,
  /* 1748 */ 'l', 'o', 'c', 'g', 'r', 'n', 'l', 'h', 9, 0,
  /* 1758 */ 'l', 'o', 'c', 'r', 'l', 'h', 9, 0,
  /* 1766 */ 'l', 'o', 'c', 'g', 'r', 'l', 'h', 9, 0,
  /* 1775 */ 'm', 'h', 9, 0,
  /* 1779 */ 'l', 'o', 'c', 'n', 'h', 9, 0,
  /* 1786 */ 's', 't', 'o', 'c', 'n', 'h', 9, 0,
  /* 1794 */ 'l', 'o', 'c', 'g', 'n', 'h', 9, 0,
  /* 1802 */ 's', 't', 'o', 'c', 'g', 'n', 'h', 9, 0,
  /* 1811 */ 'j', 'g', 'n', 'h', 9, 0,
  /* 1817 */ 'c', 'i', 'j', 'n', 'h', 9, 0,
  /* 1824 */ 'c', 'g', 'i', 'j', 'n', 'h', 9, 0,
  /* 1832 */ 'c', 'l', 'g', 'i', 'j', 'n', 'h', 9, 0,
  /* 1841 */ 'c', 'l', 'i', 'j', 'n', 'h', 9, 0,
  /* 1849 */ 'c', 'r', 'j', 'n', 'h', 9, 0,
  /* 1856 */ 'c', 'g', 'r', 'j', 'n', 'h', 9, 0,
  /* 1864 */ 'c', 'l', 'g', 'r', 'j', 'n', 'h', 9, 0,
  /* 1873 */ 'c', 'l', 'r', 'j', 'n', 'h', 9, 0,
  /* 1881 */ 'l', 'o', 'c', 'r', 'n', 'h', 9, 0,
  /* 1889 */ 'l', 'o', 'c', 'g', 'r', 'n', 'h', 9, 0,
  /* 1898 */ 'l', 'o', 'c', 'r', 'h', 9, 0,
  /* 1905 */ 'l', 'o', 'c', 'g', 'r', 'h', 9, 0,
  /* 1913 */ 's', 'h', 9, 0,
  /* 1917 */ 's', 't', 'h', 9, 0,
  /* 1922 */ 'a', 'f', 'i', 9, 0,
  /* 1927 */ 'c', 'f', 'i', 9, 0,
  /* 1932 */ 'a', 'g', 'f', 'i', 9, 0,
  /* 1938 */ 'c', 'g', 'f', 'i', 9, 0,
  /* 1944 */ 'a', 'l', 'g', 'f', 'i', 9, 0,
  /* 1951 */ 'c', 'l', 'g', 'f', 'i', 9, 0,
  /* 1958 */ 's', 'l', 'g', 'f', 'i', 9, 0,
  /* 1965 */ 'm', 's', 'g', 'f', 'i', 9, 0,
  /* 1972 */ 'a', 'l', 'f', 'i', 9, 0,
  /* 1978 */ 'c', 'l', 'f', 'i', 9, 0,
  /* 1984 */ 's', 'l', 'f', 'i', 9, 0,
  /* 1990 */ 'm', 's', 'f', 'i', 9, 0,
  /* 1996 */ 'a', 'h', 'i', 9, 0,
  /* 2001 */ 'c', 'h', 'i', 9, 0,
  /* 2006 */ 'a', 'g', 'h', 'i', 9, 0,
  /* 2012 */ 'c', 'g', 'h', 'i', 9, 0,
  /* 2018 */ 'l', 'g', 'h', 'i', 9, 0,
  /* 2024 */ 'm', 'g', 'h', 'i', 9, 0,
  /* 2030 */ 'm', 'v', 'g', 'h', 'i', 9, 0,
  /* 2037 */ 'm', 'v', 'h', 'h', 'i', 9, 0,
  /* 2044 */ 'l', 'h', 'i', 9, 0,
  /* 2049 */ 'm', 'h', 'i', 9, 0,
  /* 2054 */ 'm', 'v', 'h', 'i', 9, 0,
  /* 2060 */ 'c', 'l', 'i', 9, 0,
  /* 2065 */ 'n', 'i', 9, 0,
  /* 2069 */ 'o', 'i', 9, 0,
  /* 2073 */ 'a', 's', 'i', 9, 0,
  /* 2078 */ 'a', 'g', 's', 'i', 9, 0,
  /* 2084 */ 'c', 'h', 's', 'i', 9, 0,
  /* 2090 */ 'c', 'l', 'f', 'h', 's', 'i', 9, 0,
  /* 2098 */ 'c', 'g', 'h', 's', 'i', 9, 0,
  /* 2105 */ 'c', 'l', 'g', 'h', 's', 'i', 9, 0,
  /* 2113 */ 'c', 'h', 'h', 's', 'i', 9, 0,
  /* 2120 */ 'c', 'l', 'h', 'h', 's', 'i', 9, 0,
  /* 2128 */ 'm', 'v', 'i', 9, 0,
  /* 2133 */ 'x', 'i', 9, 0,
  /* 2137 */ 'c', 'i', 'j', 9, 0,
  /* 2142 */ 'c', 'g', 'i', 'j', 9, 0,
  /* 2148 */ 'c', 'l', 'g', 'i', 'j', 9, 0,
  /* 2155 */ 'c', 'l', 'i', 'j', 9, 0,
  /* 2161 */ 'c', 'r', 'j', 9, 0,
  /* 2166 */ 'c', 'g', 'r', 'j', 9, 0,
  /* 2172 */ 'c', 'l', 'g', 'r', 'j', 9, 0,
  /* 2179 */ 'c', 'l', 'r', 'j', 9, 0,
  /* 2185 */ 's', 'r', 'a', 'k', 9, 0,
  /* 2191 */ 'a', 'h', 'i', 'k', 9, 0,
  /* 2197 */ 'a', 'g', 'h', 'i', 'k', 9, 0,
  /* 2204 */ 'a', 'l', 'g', 'h', 's', 'i', 'k', 9, 0,
  /* 2213 */ 'a', 'l', 'h', 's', 'i', 'k', 9, 0,
  /* 2221 */ 's', 'l', 'l', 'k', 9, 0,
  /* 2227 */ 's', 'r', 'l', 'k', 9, 0,
  /* 2233 */ 'a', 'r', 'k', 9, 0,
  /* 2238 */ 'a', 'g', 'r', 'k', 9, 0,
  /* 2244 */ 'a', 'l', 'g', 'r', 'k', 9, 0,
  /* 2251 */ 's', 'l', 'g', 'r', 'k', 9, 0,
  /* 2258 */ 'n', 'g', 'r', 'k', 9, 0,
  /* 2264 */ 'o', 'g', 'r', 'k', 9, 0,
  /* 2270 */ 's', 'g', 'r', 'k', 9, 0,
  /* 2276 */ 'x', 'g', 'r', 'k', 9, 0,
  /* 2282 */ 'a', 'l', 'r', 'k', 9, 0,
  /* 2288 */ 's', 'l', 'r', 'k', 9, 0,
  /* 2294 */ 'n', 'r', 'k', 9, 0,
  /* 2299 */ 'o', 'r', 'k', 9, 0,
  /* 2304 */ 's', 'r', 'k', 9, 0,
  /* 2309 */ 'x', 'r', 'k', 9, 0,
  /* 2314 */ 'l', 'a', 'a', 'l', 9, 0,
  /* 2320 */ 'l', 'o', 'c', 'l', 9, 0,
  /* 2326 */ 's', 't', 'o', 'c', 'l', 9, 0,
  /* 2333 */ 'b', 'r', 'c', 'l', 9, 0,
  /* 2339 */ 'd', 'l', 9, 0,
  /* 2343 */ 'l', 'o', 'c', 'g', 'l', 9, 0,
  /* 2350 */ 's', 't', 'o', 'c', 'g', 'l', 9, 0,
  /* 2358 */ 'j', 'g', 'l', 9, 0,
  /* 2363 */ 'i', 'i', 'h', 'l', 9, 0,
  /* 2369 */ 'l', 'l', 'i', 'h', 'l', 9, 0,
  /* 2376 */ 'n', 'i', 'h', 'l', 9, 0,
  /* 2382 */ 'o', 'i', 'h', 'l', 9, 0,
  /* 2388 */ 't', 'm', 'h', 'l', 9, 0,
  /* 2394 */ 'c', 'i', 'j', 'l', 9, 0,
  /* 2400 */ 'c', 'g', 'i', 'j', 'l', 9, 0,
  /* 2407 */ 'c', 'l', 'g', 'i', 'j', 'l', 9, 0,
  /* 2415 */ 'c', 'l', 'i', 'j', 'l', 9, 0,
  /* 2422 */ 'c', 'r', 'j', 'l', 9, 0,
  /* 2428 */ 'c', 'g', 'r', 'j', 'l', 9, 0,
  /* 2435 */ 'c', 'l', 'g', 'r', 'j', 'l', 9, 0,
  /* 2443 */ 'c', 'l', 'r', 'j', 'l', 9, 0,
  /* 2450 */ 'i', 'i', 'l', 'l', 9, 0,
  /* 2456 */ 'l', 'l', 'i', 'l', 'l', 9, 0,
  /* 2463 */ 'n', 'i', 'l', 'l', 9, 0,
  /* 2469 */ 'o', 'i', 'l', 'l', 9, 0,
  /* 2475 */ 't', 'm', 'l', 'l', 9, 0,
  /* 2481 */ 'r', 'l', 'l', 9, 0,
  /* 2486 */ 's', 'l', 'l', 9, 0,
  /* 2491 */ 'l', 'o', 'c', 'n', 'l', 9, 0,
  /* 2498 */ 's', 't', 'o', 'c', 'n', 'l', 9, 0,
  /* 2506 */ 'l', 'o', 'c', 'g', 'n', 'l', 9, 0,
  /* 2514 */ 's', 't', 'o', 'c', 'g', 'n', 'l', 9, 0,
  /* 2523 */ 'j', 'g', 'n', 'l', 9, 0,
  /* 2529 */ 'c', 'i', 'j', 'n', 'l', 9, 0,
  /* 2536 */ 'c', 'g', 'i', 'j', 'n', 'l', 9, 0,
  /* 2544 */ 'c', 'l', 'g', 'i', 'j', 'n', 'l', 9, 0,
  /* 2553 */ 'c', 'l', 'i', 'j', 'n', 'l', 9, 0,
  /* 2561 */ 'c', 'r', 'j', 'n', 'l', 9, 0,
  /* 2568 */ 'c', 'g', 'r', 'j', 'n', 'l', 9, 0,
  /* 2576 */ 'c', 'l', 'g', 'r', 'j', 'n', 'l', 9, 0,
  /* 2585 */ 'c', 'l', 'r', 'j', 'n', 'l', 9, 0,
  /* 2593 */ 'l', 'o', 'c', 'r', 'n', 'l', 9, 0,
  /* 2601 */ 'l', 'o', 'c', 'g', 'r', 'n', 'l', 9, 0,
  /* 2610 */ 'l', 'a', 'r', 'l', 9, 0,
  /* 2616 */ 'l', 'o', 'c', 'r', 'l', 9, 0,
  /* 2623 */ 'p', 'f', 'd', 'r', 'l', 9, 0,
  /* 2630 */ 'c', 'g', 'f', 'r', 'l', 9, 0,
  /* 2637 */ 'c', 'l', 'g', 'f', 'r', 'l', 9, 0,
  /* 2645 */ 'l', 'l', 'g', 'f', 'r', 'l', 9, 0,
  /* 2653 */ 'l', 'o', 'c', 'g', 'r', 'l', 9, 0,
  /* 2661 */ 'c', 'l', 'g', 'r', 'l', 9, 0,
  /* 2668 */ 's', 't', 'g', 'r', 'l', 9, 0,
  /* 2675 */ 'c', 'h', 'r', 'l', 9, 0,
  /* 2681 */ 'c', 'g', 'h', 'r', 'l', 9, 0,
  /* 2688 */ 'c', 'l', 'g', 'h', 'r', 'l', 9, 0,
  /* 2696 */ 'l', 'l', 'g', 'h', 'r', 'l', 9, 0,
  /* 2704 */ 'c', 'l', 'h', 'r', 'l', 9, 0,
  /* 2711 */ 'l', 'l', 'h', 'r', 'l', 9, 0,
  /* 2718 */ 's', 't', 'h', 'r', 'l', 9, 0,
  /* 2725 */ 'c', 'l', 'r', 'l', 9, 0,
  /* 2731 */ 's', 'r', 'l', 9, 0,
  /* 2736 */ 's', 't', 'r', 'l', 9, 0,
  /* 2742 */ 'b', 'r', 'a', 's', 'l', 9, 0,
  /* 2749 */ 'i', 'p', 'm', 9, 0,
  /* 2754 */ 't', 'm', 9, 0,
  /* 2758 */ 'l', 'a', 'n', 9, 0,
  /* 2763 */ 'l', 'a', 'o', 9, 0,
  /* 2768 */ 'l', 'o', 'c', 'o', 9, 0,
  /* 2774 */ 's', 't', 'o', 'c', 'o', 9, 0,
  /* 2781 */ 'l', 'o', 'c', 'g', 'o', 9, 0,
  /* 2788 */ 's', 't', 'o', 'c', 'g', 'o', 9, 0,
  /* 2796 */ 'j', 'g', 'o', 9, 0,
  /* 2801 */ 'j', 'o', 9, 0,
  /* 2805 */ 'l', 'o', 'c', 'n', 'o', 9, 0,
  /* 2812 */ 's', 't', 'o', 'c', 'n', 'o', 9, 0,
  /* 2820 */ 'l', 'o', 'c', 'g', 'n', 'o', 9, 0,
  /* 2828 */ 's', 't', 'o', 'c', 'g', 'n', 'o', 9, 0,
  /* 2837 */ 'j', 'g', 'n', 'o', 9, 0,
  /* 2843 */ 'j', 'n', 'o', 9, 0,
  /* 2848 */ 'l', 'o', 'c', 'r', 'n', 'o', 9, 0,
  /* 2856 */ 'l', 'o', 'c', 'g', 'r', 'n', 'o', 9, 0,
  /* 2865 */ 'l', 'o', 'c', 'r', 'o', 9, 0,
  /* 2872 */ 'l', 'o', 'c', 'g', 'r', 'o', 9, 0,
  /* 2880 */ 'e', 'a', 'r', 9, 0,
  /* 2885 */ 'm', 'a', 'd', 'b', 'r', 9, 0,
  /* 2892 */ 'l', 'c', 'd', 'b', 'r', 9, 0,
  /* 2899 */ 'd', 'd', 'b', 'r', 9, 0,
  /* 2905 */ 'l', 'e', 'd', 'b', 'r', 9, 0,
  /* 2912 */ 'c', 'f', 'd', 'b', 'r', 9, 0,
  /* 2919 */ 'c', 'l', 'f', 'd', 'b', 'r', 9, 0,
  /* 2927 */ 'c', 'g', 'd', 'b', 'r', 9, 0,
  /* 2934 */ 'c', 'l', 'g', 'd', 'b', 'r', 9, 0,
  /* 2942 */ 'f', 'i', 'd', 'b', 'r', 9, 0,
  /* 2949 */ 'm', 'd', 'b', 'r', 9, 0,
  /* 2955 */ 'l', 'n', 'd', 'b', 'r', 9, 0,
  /* 2962 */ 'l', 'p', 'd', 'b', 'r', 9, 0,
  /* 2969 */ 's', 'q', 'd', 'b', 'r', 9, 0,
  /* 2976 */ 'm', 's', 'd', 'b', 'r', 9, 0,
  /* 2983 */ 'l', 't', 'd', 'b', 'r', 9, 0,
  /* 2990 */ 'l', 'x', 'd', 'b', 'r', 9, 0,
  /* 2997 */ 'm', 'x', 'd', 'b', 'r', 9, 0,
  /* 3004 */ 'm', 'a', 'e', 'b', 'r', 9, 0,
  /* 3011 */ 'l', 'c', 'e', 'b', 'r', 9, 0,
  /* 3018 */ 'l', 'd', 'e', 'b', 'r', 9, 0,
  /* 3025 */ 'm', 'd', 'e', 'b', 'r', 9, 0,
  /* 3032 */ 'm', 'e', 'e', 'b', 'r', 9, 0,
  /* 3039 */ 'c', 'f', 'e', 'b', 'r', 9, 0,
  /* 3046 */ 'c', 'l', 'f', 'e', 'b', 'r', 9, 0,
  /* 3054 */ 'c', 'g', 'e', 'b', 'r', 9, 0,
  /* 3061 */ 'c', 'l', 'g', 'e', 'b', 'r', 9, 0,
  /* 3069 */ 'f', 'i', 'e', 'b', 'r', 9, 0,
  /* 3076 */ 'l', 'n', 'e', 'b', 'r', 9, 0,
  /* 3083 */ 'l', 'p', 'e', 'b', 'r', 9, 0,
  /* 3090 */ 's', 'q', 'e', 'b', 'r', 9, 0,
  /* 3097 */ 'm', 's', 'e', 'b', 'r', 9, 0,
  /* 3104 */ 'l', 't', 'e', 'b', 'r', 9, 0,
  /* 3111 */ 'l', 'x', 'e', 'b', 'r', 9, 0,
  /* 3118 */ 'c', 'd', 'f', 'b', 'r', 9, 0,
  /* 3125 */ 'c', 'e', 'f', 'b', 'r', 9, 0,
  /* 3132 */ 'c', 'd', 'l', 'f', 'b', 'r', 9, 0,
  /* 3140 */ 'c', 'e', 'l', 'f', 'b', 'r', 9, 0,
  /* 3148 */ 'c', 'x', 'l', 'f', 'b', 'r', 9, 0,
  /* 3156 */ 'c', 'x', 'f', 'b', 'r', 9, 0,
  /* 3163 */ 'c', 'd', 'g', 'b', 'r', 9, 0,
  /* 3170 */ 'c', 'e', 'g', 'b', 'r', 9, 0,
  /* 3177 */ 'c', 'd', 'l', 'g', 'b', 'r', 9, 0,
  /* 3185 */ 'c', 'e', 'l', 'g', 'b', 'r', 9, 0,
  /* 3193 */ 'c', 'x', 'l', 'g', 'b', 'r', 9, 0,
  /* 3201 */ 'c', 'x', 'g', 'b', 'r', 9, 0,
  /* 3208 */ 's', 'l', 'b', 'r', 9, 0,
  /* 3214 */ 'a', 'x', 'b', 'r', 9, 0,
  /* 3220 */ 'l', 'c', 'x', 'b', 'r', 9, 0,
  /* 3227 */ 'l', 'd', 'x', 'b', 'r', 9, 0,
  /* 3234 */ 'l', 'e', 'x', 'b', 'r', 9, 0,
  /* 3241 */ 'c', 'f', 'x', 'b', 'r', 9, 0,
  /* 3248 */ 'c', 'l', 'f', 'x', 'b', 'r', 9, 0,
  /* 3256 */ 'c', 'g', 'x', 'b', 'r', 9, 0,
  /* 3263 */ 'c', 'l', 'g', 'x', 'b', 'r', 9, 0,
  /* 3271 */ 'f', 'i', 'x', 'b', 'r', 9, 0,
  /* 3278 */ 'm', 'x', 'b', 'r', 9, 0,
  /* 3284 */ 'l', 'n', 'x', 'b', 'r', 9, 0,
  /* 3291 */ 'l', 'p', 'x', 'b', 'r', 9, 0,
  /* 3298 */ 's', 'q', 'x', 'b', 'r', 9, 0,
  /* 3305 */ 's', 'x', 'b', 'r', 9, 0,
  /* 3311 */ 'l', 't', 'x', 'b', 'r', 9, 0,
  /* 3318 */ 'b', 'c', 'r', 9, 0,
  /* 3323 */ 'l', 'l', 'g', 'c', 'r', 9, 0,
  /* 3330 */ 'a', 'l', 'c', 'r', 9, 0,
  /* 3336 */ 'l', 'l', 'c', 'r', 9, 0,
  /* 3342 */ 'l', 'o', 'c', 'r', 9, 0,
  /* 3348 */ 'l', 'g', 'd', 'r', 9, 0,
  /* 3354 */ 'l', 'd', 'r', 9, 0,
  /* 3359 */ 'c', 'p', 's', 'd', 'r', 9, 0,
  /* 3366 */ 'l', 'z', 'd', 'r', 9, 0,
  /* 3372 */ 'b', 'e', 'r', 9, 0,
  /* 3377 */ 'b', 'h', 'e', 'r', 9, 0,
  /* 3383 */ 'b', 'n', 'h', 'e', 'r', 9, 0,
  /* 3390 */ 'b', 'l', 'e', 'r', 9, 0,
  /* 3396 */ 'b', 'n', 'l', 'e', 'r', 9, 0,
  /* 3403 */ 'b', 'n', 'e', 'r', 9, 0,
  /* 3409 */ 'l', 'z', 'e', 'r', 9, 0,
  /* 3415 */ 'a', 'g', 'f', 'r', 9, 0,
  /* 3421 */ 'l', 'c', 'g', 'f', 'r', 9, 0,
  /* 3428 */ 'a', 'l', 'g', 'f', 'r', 9, 0,
  /* 3435 */ 'c', 'l', 'g', 'f', 'r', 9, 0,
  /* 3442 */ 'l', 'l', 'g', 'f', 'r', 9, 0,
  /* 3449 */ 's', 'l', 'g', 'f', 'r', 9, 0,
  /* 3456 */ 'l', 'n', 'g', 'f', 'r', 9, 0,
  /* 3463 */ 'l', 'p', 'g', 'f', 'r', 9, 0,
  /* 3470 */ 'd', 's', 'g', 'f', 'r', 9, 0,
  /* 3477 */ 'm', 's', 'g', 'f', 'r', 9, 0,
  /* 3484 */ 'l', 't', 'g', 'f', 'r', 9, 0,
  /* 3491 */ 'a', 'g', 'r', 9, 0,
  /* 3496 */ 's', 'l', 'b', 'g', 'r', 9, 0,
  /* 3503 */ 'a', 'l', 'c', 'g', 'r', 9, 0,
  /* 3510 */ 'l', 'o', 'c', 'g', 'r', 9, 0,
  /* 3517 */ 'l', 'd', 'g', 'r', 9, 0,
  /* 3523 */ 'a', 'l', 'g', 'r', 9, 0,
  /* 3529 */ 'c', 'l', 'g', 'r', 9, 0,
  /* 3535 */ 'd', 'l', 'g', 'r', 9, 0,
  /* 3541 */ 'm', 'l', 'g', 'r', 9, 0,
  /* 3547 */ 's', 'l', 'g', 'r', 9, 0,
  /* 3553 */ 'l', 'n', 'g', 'r', 9, 0,
  /* 3559 */ 'f', 'l', 'o', 'g', 'r', 9, 0,
  /* 3566 */ 'l', 'p', 'g', 'r', 9, 0,
  /* 3572 */ 'd', 's', 'g', 'r', 9, 0,
  /* 3578 */ 'm', 's', 'g', 'r', 9, 0,
  /* 3584 */ 'l', 't', 'g', 'r', 9, 0,
  /* 3590 */ 'l', 'r', 'v', 'g', 'r', 9, 0,
  /* 3597 */ 'x', 'g', 'r', 9, 0,
  /* 3602 */ 'b', 'h', 'r', 9, 0,
  /* 3607 */ 'l', 'l', 'g', 'h', 'r', 9, 0,
  /* 3614 */ 'b', 'l', 'h', 'r', 9, 0,
  /* 3620 */ 'l', 'l', 'h', 'r', 9, 0,
  /* 3626 */ 'b', 'n', 'l', 'h', 'r', 9, 0,
  /* 3633 */ 'b', 'n', 'h', 'r', 9, 0,
  /* 3639 */ 'a', 'l', 'r', 9, 0,
  /* 3644 */ 'b', 'l', 'r', 9, 0,
  /* 3649 */ 'c', 'l', 'r', 9, 0,
  /* 3654 */ 'd', 'l', 'r', 9, 0,
  /* 3659 */ 'b', 'n', 'l', 'r', 9, 0,
  /* 3665 */ 's', 'l', 'r', 9, 0,
  /* 3670 */ 'l', 'n', 'r', 9, 0,
  /* 3675 */ 'b', 'o', 'r', 9, 0,
  /* 3680 */ 'b', 'n', 'o', 'r', 9, 0,
  /* 3686 */ 'l', 'p', 'r', 9, 0,
  /* 3691 */ 'b', 'a', 's', 'r', 9, 0,
  /* 3697 */ 'm', 's', 'r', 9, 0,
  /* 3702 */ 'l', 't', 'r', 9, 0,
  /* 3707 */ 'l', 'r', 'v', 'r', 9, 0,
  /* 3713 */ 'l', 'x', 'r', 9, 0,
  /* 3718 */ 'l', 'z', 'x', 'r', 9, 0,
  /* 3724 */ 'b', 'r', 'a', 's', 9, 0,
  /* 3730 */ 'c', 's', 9, 0,
  /* 3734 */ 'm', 's', 9, 0,
  /* 3738 */ 'b', 'r', 'c', 't', 9, 0,
  /* 3744 */ 'l', 't', 9, 0,
  /* 3748 */ 'c', 'l', 's', 't', 9, 0,
  /* 3754 */ 's', 'r', 's', 't', 9, 0,
  /* 3760 */ 'm', 'v', 's', 't', 9, 0,
  /* 3766 */ 'l', 'r', 'v', 9, 0,
  /* 3771 */ 's', 't', 'r', 'v', 9, 0,
  /* 3777 */ 'l', 'a', 'x', 9, 0,
  /* 3782 */ 'l', 'a', 'y', 9, 0,
  /* 3787 */ 'i', 'c', 'y', 9, 0,
  /* 3792 */ 's', 't', 'c', 'y', 9, 0,
  /* 3798 */ 'l', 'd', 'y', 9, 0,
  /* 3803 */ 's', 't', 'd', 'y', 9, 0,
  /* 3809 */ 'l', 'e', 'y', 9, 0,
  /* 3814 */ 's', 't', 'e', 'y', 9, 0,
  /* 3820 */ 'a', 'h', 'y', 9, 0,
  /* 3825 */ 'c', 'h', 'y', 9, 0,
  /* 3830 */ 'l', 'h', 'y', 9, 0,
  /* 3835 */ 'm', 'h', 'y', 9, 0,
  /* 3840 */ 's', 'h', 'y', 9, 0,
  /* 3845 */ 's', 't', 'h', 'y', 9, 0,
  /* 3851 */ 'c', 'l', 'i', 'y', 9, 0,
  /* 3857 */ 'n', 'i', 'y', 9, 0,
  /* 3862 */ 'o', 'i', 'y', 9, 0,
  /* 3867 */ 'm', 'v', 'i', 'y', 9, 0,
  /* 3873 */ 'x', 'i', 'y', 9, 0,
  /* 3878 */ 'a', 'l', 'y', 9, 0,
  /* 3883 */ 'c', 'l', 'y', 9, 0,
  /* 3888 */ 's', 'l', 'y', 9, 0,
  /* 3893 */ 't', 'm', 'y', 9, 0,
  /* 3898 */ 'n', 'y', 9, 0,
  /* 3902 */ 'o', 'y', 9, 0,
  /* 3906 */ 'c', 's', 'y', 9, 0,
  /* 3911 */ 'm', 's', 'y', 9, 0,
  /* 3916 */ 's', 't', 'y', 9, 0,
  /* 3921 */ 'x', 'y', 9, 0,
  /* 3925 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'E', 'N', 'D', 0,
  /* 3938 */ 'B', 'U', 'N', 'D', 'L', 'E', 0,
  /* 3945 */ 'D', 'B', 'G', '_', 'V', 'A', 'L', 'U', 'E', 0,
  /* 3955 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'S', 'T', 'A', 'R', 'T', 0,
  /* 3970 */ 'l', 'o', 'c', 0,
  /* 3974 */ 's', 't', 'o', 'c', 0,
  /* 3979 */ 'l', 'o', 'c', 'g', 0,
  /* 3984 */ 's', 't', 'o', 'c', 'g', 0,
  /* 3990 */ 'j', 'g', 0,
  /* 3993 */ 'c', 'i', 'j', 0,
  /* 3997 */ 'c', 'g', 'i', 'j', 0,
  /* 4002 */ 'c', 'l', 'g', 'i', 'j', 0,
  /* 4008 */ 'c', 'l', 'i', 'j', 0,
  /* 4013 */ 'c', 'r', 'j', 0,
  /* 4017 */ 'c', 'g', 'r', 'j', 0,
  /* 4022 */ 'c', 'l', 'g', 'r', 'j', 0,
  /* 4028 */ 'c', 'l', 'r', 'j', 0,
  /* 4033 */ 'l', 'o', 'c', 'r', 0,
  /* 4038 */ 'l', 'o', 'c', 'g', 'r', 0,
  };
#endif

  // Emit the opcode for the instruction.
  uint32_t Bits = OpInfo[MCInst_getOpcode(MI)];
  // assert(Bits != 0 && "Cannot print this instruction.");
#ifndef CAPSTONE_DIET
  SStream_concat0(O, AsmStrs+(Bits & 4095)-1);
#endif


  // Fragment 0 encoded into 4 bits for 11 unique commands.
  //printf("Frag-0: %"PRIu64"\n", (Bits >> 12) & 15);
  switch ((Bits >> 12) & 15) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // DBG_VALUE, BUNDLE, LIFETIME_START, LIFETIME_END
    return;
    break;
  case 1:
    // A, ADB, ADBR, AEB, AEBR, AFI, AG, AGF, AGFI, AGFR, AGHI, AGHIK, AGR, A...
    printOperand(MI, 0, O); 
    break;
  case 2:
    // AGSI, ASI, CGHSI, CHHSI, CHSI, CLFHSI, CLGHSI, CLHHSI, CLI, CLIY, MVGH...
    printBDAddrOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 3:
    // AsmBCR, AsmBRC, AsmBRCL, PFD, PFDRL
    printU4ImmOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 4:
    // AsmEJ, AsmEJG, AsmHEJ, AsmHEJG, AsmHJ, AsmHJG, AsmLEJ, AsmLEJG, AsmLHJ...
    printPCRelOperand(MI, 0, O); 
    return;
    break;
  case 5:
    // BRC, BRCL
    printCond4Operand(MI, 1, O); 
    SStream_concat0(O, "\t"); 
    printPCRelOperand(MI, 2, O); 
    return;
    break;
  case 6:
    // CGIJ, CGRJ, CIJ, CLGIJ, CLGRJ, CLIJ, CLRJ, CRJ
    printCond4Operand(MI, 2, O); 
    SStream_concat0(O, "\t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 7:
    // CLC, MVC, NC, OC, XC
    printBDLAddrOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printBDAddrOperand(MI, 3, O); 
    return;
    break;
  case 8:
    // LOC, LOCG
    printCond4Operand(MI, 5, O); 
    SStream_concat0(O, "\t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printBDAddrOperand(MI, 2, O); 
    return;
    break;
  case 9:
    // LOCGR, LOCR
    printCond4Operand(MI, 3, O); 
    SStream_concat0(O, "\t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 10:
    // STOC, STOCG
    printCond4Operand(MI, 4, O); 
    SStream_concat0(O, "\t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printBDAddrOperand(MI, 1, O); 
    return;
    break;
  }


  // Fragment 1 encoded into 4 bits for 11 unique commands.
  //printf("Frag-1: %"PRIu64"\n", (Bits >> 16) & 15);
  switch ((Bits >> 16) & 15) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // A, ADB, ADBR, AEB, AEBR, AFI, AG, AGF, AGFI, AGFR, AGHI, AGHIK, AGR, A...
    SStream_concat0(O, ", "); 
    break;
  case 1:
    // AGSI, ASI
    printS8ImmOperand(MI, 2, O); 
    return;
    break;
  case 2:
    // AsmBCR, CGRJ, CLGRJ, CLRJ, CRJ
    printOperand(MI, 1, O); 
    break;
  case 3:
    // AsmBRC, AsmBRCL, PFDRL
    printPCRelOperand(MI, 1, O); 
    return;
    break;
  case 4:
    // AsmEBR, AsmHBR, AsmHEBR, AsmLBR, AsmLEBR, AsmLHBR, AsmNEBR, AsmNHBR, A...
    return;
    break;
  case 5:
    // CGHSI, CHHSI, CHSI, MVGHI, MVHHI, MVHI
    printS16ImmOperand(MI, 2, O); 
    return;
    break;
  case 6:
    // CGIJ, CIJ
    printS8ImmOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printPCRelOperand(MI, 3, O); 
    return;
    break;
  case 7:
    // CLFHSI, CLGHSI, CLHHSI
    printU16ImmOperand(MI, 2, O); 
    return;
    break;
  case 8:
    // CLGIJ, CLIJ
    printU8ImmOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printPCRelOperand(MI, 3, O); 
    return;
    break;
  case 9:
    // CLI, CLIY, MVI, MVIY, NI, NIY, OI, OIY, TM, TMY, XI, XIY
    printU8ImmOperand(MI, 2, O); 
    return;
    break;
  case 10:
    // PFD
    printBDXAddrOperand(MI, 1, O); 
    return;
    break;
  }


  // Fragment 2 encoded into 5 bits for 23 unique commands.
  //printf("Frag-2: %"PRIu64"\n", (Bits >> 20) & 31);
  switch ((Bits >> 20) & 31) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // A, ADB, AEB, AG, AGF, AH, AHY, AL, ALC, ALCG, ALG, ALGF, ALY, AY, DDB,...
    printBDXAddrOperand(MI, 2, O); 
    return;
    break;
  case 1:
    // ADBR, AEBR, AGFR, AGR, ALCGR, ALCR, ALGFR, ALGR, ALR, AR, AXBR, AsmELO...
    printOperand(MI, 2, O); 
    break;
  case 2:
    // AFI, AGFI, AIH, MSFI, MSGFI
    printS32ImmOperand(MI, 2, O); 
    return;
    break;
  case 3:
    // AGHI, AHI, MGHI, MHI
    printS16ImmOperand(MI, 2, O); 
    return;
    break;
  case 4:
    // AGHIK, AGRK, AHIK, ALGHSIK, ALGRK, ALHSIK, ALRK, ARK, AsmCGRJ, AsmCLGR...
    printOperand(MI, 1, O); 
    break;
  case 5:
    // ALFI, ALGFI, NIHF, NILF, OIHF, OILF, SLFI, SLGFI, XIHF, XILF
    printU32ImmOperand(MI, 2, O); 
    return;
    break;
  case 6:
    // AsmBCR
    return;
    break;
  case 7:
    // AsmCGIJ, AsmCIJ, AsmJEAltCGI, AsmJEAltCI, AsmJECGI, AsmJECI, AsmJHAltC...
    printS8ImmOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    break;
  case 8:
    // AsmCLGIJ, AsmCLIJ, AsmJEAltCLGI, AsmJEAltCLI, AsmJECLGI, AsmJECLI, Asm...
    printU8ImmOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    break;
  case 9:
    // AsmELOC, AsmELOCG, AsmHELOC, AsmHELOCG, AsmHLOC, AsmHLOCG, AsmLELOC, A...
    printBDAddrOperand(MI, 2, O); 
    break;
  case 10:
    // AsmESTOC, AsmESTOCG, AsmHESTOC, AsmHESTOCG, AsmHSTOC, AsmHSTOCG, AsmLE...
    printBDAddrOperand(MI, 1, O); 
    break;
  case 11:
    // BRAS, BRASL
    printPCRelTLSOperand(MI, 1, O); 
    return;
    break;
  case 12:
    // BRCT, BRCTG
    printPCRelOperand(MI, 2, O); 
    return;
    break;
  case 13:
    // C, CDB, CEB, CG, CGF, CGH, CH, CHF, CHY, CL, CLG, CLGF, CLHF, CLY, CY,...
    printBDXAddrOperand(MI, 1, O); 
    return;
    break;
  case 14:
    // CDLFBR, CDLGBR, CELFBR, CELGBR, CFDBR, CFEBR, CFXBR, CGDBR, CGEBR, CGX...
    printU4ImmOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    break;
  case 15:
    // CFI, CGFI, CIH, LGFI
    printS32ImmOperand(MI, 1, O); 
    return;
    break;
  case 16:
    // CGFRL, CGHRL, CGRL, CHRL, CLGFRL, CLGHRL, CLGRL, CLHRL, CLRL, CRL, LAR...
    printPCRelOperand(MI, 1, O); 
    return;
    break;
  case 17:
    // CGHI, CHI, LGHI, LHI
    printS16ImmOperand(MI, 1, O); 
    return;
    break;
  case 18:
    // CGRJ, CLGRJ, CLRJ, CRJ
    SStream_concat0(O, ", "); 
    printPCRelOperand(MI, 3, O); 
    return;
    break;
  case 19:
    // CLFI, CLGFI, CLIH, IIHF, IILF, LLIHF, LLILF
    printU32ImmOperand(MI, 1, O); 
    return;
    break;
  case 20:
    // EAR
    printAccessRegOperand(MI, 1, O); 
    return;
    break;
  case 21:
    // IIHH, IIHL, IILH, IILL, NIHH, NIHL, NILH, NILL, OIHH, OIHL, OILH, OILL
    printU16ImmOperand(MI, 2, O); 
    return;
    break;
  case 22:
    // LLIHH, LLIHL, LLILH, LLILL, TMHH, TMHL, TMLH, TMLL
    printU16ImmOperand(MI, 1, O); 
    return;
    break;
  }


  // Fragment 3 encoded into 2 bits for 4 unique commands.
  //printf("Frag-3: %"PRIu64"\n", (Bits >> 25) & 3);
  switch ((Bits >> 25) & 3) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // ADBR, AEBR, AGFR, AGR, ALCGR, ALCR, ALGFR, ALGR, ALR, AR, AXBR, AsmELO...
    return;
    break;
  case 1:
    // AGHIK, AGRK, AHIK, ALGHSIK, ALGRK, ALHSIK, ALRK, ARK, AsmCGRJ, AsmCLGR...
    SStream_concat0(O, ", "); 
    break;
  case 2:
    // AsmCGIJ, AsmCIJ, AsmCLGIJ, AsmCLIJ
    printU4ImmOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printPCRelOperand(MI, 3, O); 
    return;
    break;
  case 3:
    // AsmJEAltCGI, AsmJEAltCI, AsmJEAltCLGI, AsmJEAltCLI, AsmJECGI, AsmJECI,...
    printPCRelOperand(MI, 2, O); 
    return;
    break;
  }


  // Fragment 4 encoded into 4 bits for 11 unique commands.
  //printf("Frag-4: %"PRIu64"\n", (Bits >> 27) & 15);
  switch ((Bits >> 27) & 15) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // AGHIK, AHIK, ALGHSIK, ALHSIK
    printS16ImmOperand(MI, 2, O); 
    return;
    break;
  case 1:
    // AGRK, ALGRK, ALRK, ARK, CPSDRdd, CPSDRds, CPSDRsd, CPSDRss, NGRK, NRK,...
    printOperand(MI, 2, O); 
    return;
    break;
  case 2:
    // AsmCGRJ, AsmCLGRJ, AsmCLRJ, AsmCRJ
    printU4ImmOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printPCRelOperand(MI, 3, O); 
    return;
    break;
  case 3:
    // AsmJEAltCGR, AsmJEAltCLGR, AsmJEAltCLR, AsmJEAltCR, AsmJECGR, AsmJECLG...
    printPCRelOperand(MI, 2, O); 
    return;
    break;
  case 4:
    // AsmLOC, AsmLOCG
    printU4ImmOperand(MI, 4, O); 
    return;
    break;
  case 5:
    // AsmLOCGR, AsmLOCR, AsmSTOC, AsmSTOCG, CDLFBR, CDLGBR, CELFBR, CELGBR, ...
    printU4ImmOperand(MI, 3, O); 
    return;
    break;
  case 6:
    // CS, CSG, CSY
    printBDAddrOperand(MI, 3, O); 
    return;
    break;
  case 7:
    // LAA, LAAG, LAAL, LAALG, LAN, LANG, LAO, LAOG, LAX, LAXG, LMG, RLL, RLL...
    printBDAddrOperand(MI, 2, O); 
    return;
    break;
  case 8:
    // MADB, MAEB, MSDB, MSEB
    printBDXAddrOperand(MI, 3, O); 
    return;
    break;
  case 9:
    // MADBR, MAEBR, MSDBR, MSEBR
    printOperand(MI, 3, O); 
    return;
    break;
  case 10:
    // RISBG, RISBG32, RISBHG, RISBLG, RNSBG, ROSBG, RXSBG
    printU8ImmOperand(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printU8ImmOperand(MI, 4, O); 
    SStream_concat0(O, ", "); 
    printU6ImmOperand(MI, 5, O); 
    return;
    break;
  }
}


/// getRegisterName - This method is automatically generated by tblgen
/// from the register set description.  This returns the assembler name
/// for the specified register.
static const char *getRegisterName(unsigned RegNo)
{
  // assert(RegNo && RegNo < 98 && "Invalid register number!");

#ifndef CAPSTONE_DIET
  static const char AsmStrs[] = {
  /* 0 */ 'f', '1', '0', 0,
  /* 4 */ 'r', '1', '0', 0,
  /* 8 */ 'f', '0', 0,
  /* 11 */ 'r', '0', 0,
  /* 14 */ 'f', '1', '1', 0,
  /* 18 */ 'r', '1', '1', 0,
  /* 22 */ 'f', '1', 0,
  /* 25 */ 'r', '1', 0,
  /* 28 */ 'f', '1', '2', 0,
  /* 32 */ 'r', '1', '2', 0,
  /* 36 */ 'f', '2', 0,
  /* 39 */ 'r', '2', 0,
  /* 42 */ 'f', '1', '3', 0,
  /* 46 */ 'r', '1', '3', 0,
  /* 50 */ 'f', '3', 0,
  /* 53 */ 'r', '3', 0,
  /* 56 */ 'f', '1', '4', 0,
  /* 60 */ 'r', '1', '4', 0,
  /* 64 */ 'f', '4', 0,
  /* 67 */ 'r', '4', 0,
  /* 70 */ 'f', '1', '5', 0,
  /* 74 */ 'r', '1', '5', 0,
  /* 78 */ 'f', '5', 0,
  /* 81 */ 'r', '5', 0,
  /* 84 */ 'f', '6', 0,
  /* 87 */ 'r', '6', 0,
  /* 90 */ 'f', '7', 0,
  /* 93 */ 'r', '7', 0,
  /* 96 */ 'f', '8', 0,
  /* 99 */ 'r', '8', 0,
  /* 102 */ 'f', '9', 0,
  /* 105 */ 'r', '9', 0,
  /* 108 */ 'c', 'c', 0,
  };

  static const uint8_t RegAsmOffset[] = {
    108, 8, 22, 36, 50, 64, 78, 84, 90, 96, 102, 0, 14, 28, 
    42, 56, 70, 8, 22, 64, 78, 96, 102, 28, 42, 8, 22, 36, 
    50, 64, 78, 84, 90, 96, 102, 0, 14, 28, 42, 56, 70, 11, 
    25, 39, 53, 67, 81, 87, 93, 99, 105, 4, 18, 32, 46, 60, 
    74, 11, 25, 39, 53, 67, 81, 87, 93, 99, 105, 4, 18, 32, 
    46, 60, 74, 11, 25, 39, 53, 67, 81, 87, 93, 99, 105, 4, 
    18, 32, 46, 60, 74, 11, 39, 67, 87, 99, 4, 32, 60, 
  };

  //int i;
  //for (i = 0; i < sizeof(RegAsmOffset); i++)
  //     printf("%s = %u\n", AsmStrs+RegAsmOffset[i], i + 1);
  //printf("*************************\n");
  return AsmStrs+RegAsmOffset[RegNo-1];
#else
  return NULL;
#endif
}
