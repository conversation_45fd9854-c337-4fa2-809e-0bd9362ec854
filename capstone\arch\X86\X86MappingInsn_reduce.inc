// This is auto-gen data for Capstone disassembly engine (www.capstone-engine.org)
// By <PERSON><PERSON><PERSON> <<EMAIL>>

{
	X86_AAA, X86_INS_AAA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_AAD8i8, X86_INS_AAD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_AAM8i8, X86_INS_AAM,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_AAS, X86_INS_AAS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_ADC16i16, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC16mi, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC16mi8, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC16mr, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC16ri, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC16ri8, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC16rm, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC16rr, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC16rr_REV, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC32i32, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC32mi, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC32mi8, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC32mr, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC32ri, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC32ri8, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC32rm, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC32rr, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC32rr_REV, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC64i32, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC64mi32, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC64mi8, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC64mr, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC64ri32, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC64ri8, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC64rm, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC64rr, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC64rr_REV, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC8i8, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC8mi, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC8mi8, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_ADC8mr, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC8ri, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC8ri8, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_ADC8rm, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC8rr, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADC8rr_REV, X86_INS_ADC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADCX32rm, X86_INS_ADCX,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_ADX, 0 }, 0, 0
#endif
},
{
	X86_ADCX32rr, X86_INS_ADCX,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_ADX, 0 }, 0, 0
#endif
},
{
	X86_ADCX64rm, X86_INS_ADCX,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_ADX, 0 }, 0, 0
#endif
},
{
	X86_ADCX64rr, X86_INS_ADCX,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_ADX, 0 }, 0, 0
#endif
},
{
	X86_ADD16i16, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD16mi, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD16mi8, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD16mr, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD16ri, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD16ri8, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD16rm, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD16rr, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD16rr_REV, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD32i32, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD32mi, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD32mi8, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD32mr, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD32ri, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD32ri8, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD32rm, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD32rr, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD32rr_REV, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD64i32, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD64mi32, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD64mi8, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD64mr, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD64ri32, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD64ri8, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD64rm, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD64rr, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD64rr_REV, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD8i8, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD8mi, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD8mi8, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_ADD8mr, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD8ri, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD8ri8, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_ADD8rm, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD8rr, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADD8rr_REV, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ADOX32rm, X86_INS_ADOX,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_ADX, 0 }, 0, 0
#endif
},
{
	X86_ADOX32rr, X86_INS_ADOX,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_ADX, 0 }, 0, 0
#endif
},
{
	X86_ADOX64rm, X86_INS_ADOX,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_ADX, 0 }, 0, 0
#endif
},
{
	X86_ADOX64rr, X86_INS_ADOX,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_ADX, 0 }, 0, 0
#endif
},
{
	X86_AND16i16, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND16mi, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND16mi8, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND16mr, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND16ri, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND16ri8, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND16rm, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND16rr, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND16rr_REV, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND32i32, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND32mi, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND32mi8, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND32mr, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND32ri, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND32ri8, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND32rm, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND32rr, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND32rr_REV, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND64i32, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND64mi32, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND64mi8, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND64mr, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND64ri32, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND64ri8, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND64rm, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND64rr, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND64rr_REV, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND8i8, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND8mi, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND8mi8, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_AND8mr, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND8ri, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND8ri8, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_AND8rm, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND8rr, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_AND8rr_REV, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ANDN32rm, X86_INS_ANDN,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI, 0 }, 0, 0
#endif
},
{
	X86_ANDN32rr, X86_INS_ANDN,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI, 0 }, 0, 0
#endif
},
{
	X86_ANDN64rm, X86_INS_ANDN,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI, 0 }, 0, 0
#endif
},
{
	X86_ANDN64rr, X86_INS_ANDN,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI, 0 }, 0, 0
#endif
},
{
	X86_ARPL16mr, X86_INS_ARPL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_ARPL16rr, X86_INS_ARPL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_BEXTR32rm, X86_INS_BEXTR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI, 0 }, 0, 0
#endif
},
{
	X86_BEXTR32rr, X86_INS_BEXTR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI, 0 }, 0, 0
#endif
},
{
	X86_BEXTR64rm, X86_INS_BEXTR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI, 0 }, 0, 0
#endif
},
{
	X86_BEXTR64rr, X86_INS_BEXTR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI, 0 }, 0, 0
#endif
},
{
	X86_BEXTRI32mi, X86_INS_BEXTR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BEXTRI32ri, X86_INS_BEXTR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BEXTRI64mi, X86_INS_BEXTR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BEXTRI64ri, X86_INS_BEXTR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLCFILL32rm, X86_INS_BLCFILL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLCFILL32rr, X86_INS_BLCFILL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLCFILL64rm, X86_INS_BLCFILL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLCFILL64rr, X86_INS_BLCFILL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLCI32rm, X86_INS_BLCI,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLCI32rr, X86_INS_BLCI,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLCI64rm, X86_INS_BLCI,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLCI64rr, X86_INS_BLCI,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLCIC32rm, X86_INS_BLCIC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLCIC32rr, X86_INS_BLCIC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLCIC64rm, X86_INS_BLCIC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLCIC64rr, X86_INS_BLCIC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLCMSK32rm, X86_INS_BLCMSK,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLCMSK32rr, X86_INS_BLCMSK,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLCMSK64rm, X86_INS_BLCMSK,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLCMSK64rr, X86_INS_BLCMSK,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLCS32rm, X86_INS_BLCS,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLCS32rr, X86_INS_BLCS,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLCS64rm, X86_INS_BLCS,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLCS64rr, X86_INS_BLCS,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLSFILL32rm, X86_INS_BLSFILL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLSFILL32rr, X86_INS_BLSFILL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLSFILL64rm, X86_INS_BLSFILL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLSFILL64rr, X86_INS_BLSFILL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLSI32rm, X86_INS_BLSI,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI, 0 }, 0, 0
#endif
},
{
	X86_BLSI32rr, X86_INS_BLSI,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI, 0 }, 0, 0
#endif
},
{
	X86_BLSI64rm, X86_INS_BLSI,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI, 0 }, 0, 0
#endif
},
{
	X86_BLSI64rr, X86_INS_BLSI,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI, 0 }, 0, 0
#endif
},
{
	X86_BLSIC32rm, X86_INS_BLSIC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLSIC32rr, X86_INS_BLSIC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLSIC64rm, X86_INS_BLSIC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLSIC64rr, X86_INS_BLSIC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_BLSMSK32rm, X86_INS_BLSMSK,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI, 0 }, 0, 0
#endif
},
{
	X86_BLSMSK32rr, X86_INS_BLSMSK,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI, 0 }, 0, 0
#endif
},
{
	X86_BLSMSK64rm, X86_INS_BLSMSK,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI, 0 }, 0, 0
#endif
},
{
	X86_BLSMSK64rr, X86_INS_BLSMSK,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI, 0 }, 0, 0
#endif
},
{
	X86_BLSR32rm, X86_INS_BLSR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI, 0 }, 0, 0
#endif
},
{
	X86_BLSR32rr, X86_INS_BLSR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI, 0 }, 0, 0
#endif
},
{
	X86_BLSR64rm, X86_INS_BLSR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI, 0 }, 0, 0
#endif
},
{
	X86_BLSR64rr, X86_INS_BLSR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI, 0 }, 0, 0
#endif
},
{
	X86_BOUNDS16rm, X86_INS_BOUND,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_BOUNDS32rm, X86_INS_BOUND,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_BSF16rm, X86_INS_BSF,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BSF16rr, X86_INS_BSF,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BSF32rm, X86_INS_BSF,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BSF32rr, X86_INS_BSF,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BSF64rm, X86_INS_BSF,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BSF64rr, X86_INS_BSF,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BSR16rm, X86_INS_BSR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BSR16rr, X86_INS_BSR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BSR32rm, X86_INS_BSR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BSR32rr, X86_INS_BSR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BSR64rm, X86_INS_BSR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BSR64rr, X86_INS_BSR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BSWAP32r, X86_INS_BSWAP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BSWAP64r, X86_INS_BSWAP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BT16mi8, X86_INS_BT,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BT16mr, X86_INS_BT,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BT16ri8, X86_INS_BT,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BT16rr, X86_INS_BT,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BT32mi8, X86_INS_BT,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BT32mr, X86_INS_BT,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BT32ri8, X86_INS_BT,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BT32rr, X86_INS_BT,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BT64mi8, X86_INS_BT,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BT64mr, X86_INS_BT,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BT64ri8, X86_INS_BT,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BT64rr, X86_INS_BT,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTC16mi8, X86_INS_BTC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTC16mr, X86_INS_BTC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTC16ri8, X86_INS_BTC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTC16rr, X86_INS_BTC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTC32mi8, X86_INS_BTC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTC32mr, X86_INS_BTC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTC32ri8, X86_INS_BTC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTC32rr, X86_INS_BTC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTC64mi8, X86_INS_BTC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTC64mr, X86_INS_BTC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTC64ri8, X86_INS_BTC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTC64rr, X86_INS_BTC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTR16mi8, X86_INS_BTR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTR16mr, X86_INS_BTR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTR16ri8, X86_INS_BTR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTR16rr, X86_INS_BTR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTR32mi8, X86_INS_BTR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTR32mr, X86_INS_BTR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTR32ri8, X86_INS_BTR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTR32rr, X86_INS_BTR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTR64mi8, X86_INS_BTR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTR64mr, X86_INS_BTR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTR64ri8, X86_INS_BTR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTR64rr, X86_INS_BTR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTS16mi8, X86_INS_BTS,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTS16mr, X86_INS_BTS,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTS16ri8, X86_INS_BTS,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTS16rr, X86_INS_BTS,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTS32mi8, X86_INS_BTS,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTS32mr, X86_INS_BTS,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTS32ri8, X86_INS_BTS,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTS32rr, X86_INS_BTS,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTS64mi8, X86_INS_BTS,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTS64mr, X86_INS_BTS,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTS64ri8, X86_INS_BTS,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BTS64rr, X86_INS_BTS,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_BZHI32rm, X86_INS_BZHI,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_BZHI32rr, X86_INS_BZHI,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_BZHI64rm, X86_INS_BZHI,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_BZHI64rr, X86_INS_BZHI,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_CALL16m, X86_INS_CALL,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { X86_REG_ESP, 0 }, { X86_GRP_CALL, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_CALL16r, X86_INS_CALL,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { X86_REG_ESP, 0 }, { X86_GRP_CALL, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_CALL32m, X86_INS_CALL,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { X86_REG_ESP, 0 }, { X86_GRP_CALL, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_CALL32r, X86_INS_CALL,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { X86_REG_ESP, 0 }, { X86_GRP_CALL, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_CALL64m, X86_INS_CALL,
#ifndef CAPSTONE_DIET
	{ X86_REG_RSP, 0 }, { X86_REG_RSP, 0 }, { X86_GRP_CALL, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_CALL64pcrel32, X86_INS_CALL,
#ifndef CAPSTONE_DIET
	{ X86_REG_RSP, X86_REG_RIP, 0 }, { X86_REG_RSP, 0 }, { X86_GRP_CALL, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_CALL64r, X86_INS_CALL,
#ifndef CAPSTONE_DIET
	{ X86_REG_RSP, 0 }, { X86_REG_RSP, 0 }, { X86_GRP_CALL, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_CALLpcrel16, X86_INS_CALL,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, X86_REG_EIP, 0 }, { X86_REG_ESP, 0 }, { X86_GRP_CALL, 0 }, 0, 0
#endif
},
{
	X86_CALLpcrel32, X86_INS_CALL,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, X86_REG_EIP, 0 }, { X86_REG_ESP, 0 }, { X86_GRP_CALL, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_CBW, X86_INS_CBW,
#ifndef CAPSTONE_DIET
	{ X86_REG_AL, 0 }, { X86_REG_AX, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CDQ, X86_INS_CDQ,
#ifndef CAPSTONE_DIET
	{ X86_REG_EAX, 0 }, { X86_REG_EAX, X86_REG_EDX, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CDQE, X86_INS_CDQE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EAX, 0 }, { X86_REG_RAX, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CLAC, X86_INS_CLAC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_CLC, X86_INS_CLC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CLD, X86_INS_CLD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CLFLUSHOPT, X86_INS_CLFLUSHOPT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CLGI, X86_INS_CLGI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, 0 }, 0, 0
#endif
},
{
	X86_CLI, X86_INS_CLI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_CLTS, X86_INS_CLTS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CLWB, X86_INS_CLWB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMC, X86_INS_CMC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMOVA16rm, X86_INS_CMOVA,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVA16rr, X86_INS_CMOVA,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVA32rm, X86_INS_CMOVA,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVA32rr, X86_INS_CMOVA,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVA64rm, X86_INS_CMOVA,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVA64rr, X86_INS_CMOVA,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVAE16rm, X86_INS_CMOVAE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVAE16rr, X86_INS_CMOVAE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVAE32rm, X86_INS_CMOVAE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVAE32rr, X86_INS_CMOVAE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVAE64rm, X86_INS_CMOVAE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVAE64rr, X86_INS_CMOVAE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVB16rm, X86_INS_CMOVB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVB16rr, X86_INS_CMOVB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVB32rm, X86_INS_CMOVB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVB32rr, X86_INS_CMOVB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVB64rm, X86_INS_CMOVB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVB64rr, X86_INS_CMOVB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVBE16rm, X86_INS_CMOVBE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVBE16rr, X86_INS_CMOVBE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVBE32rm, X86_INS_CMOVBE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVBE32rr, X86_INS_CMOVBE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVBE64rm, X86_INS_CMOVBE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVBE64rr, X86_INS_CMOVBE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVE16rm, X86_INS_CMOVE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVE16rr, X86_INS_CMOVE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVE32rm, X86_INS_CMOVE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVE32rr, X86_INS_CMOVE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVE64rm, X86_INS_CMOVE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVE64rr, X86_INS_CMOVE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVG16rm, X86_INS_CMOVG,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVG16rr, X86_INS_CMOVG,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVG32rm, X86_INS_CMOVG,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVG32rr, X86_INS_CMOVG,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVG64rm, X86_INS_CMOVG,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVG64rr, X86_INS_CMOVG,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVGE16rm, X86_INS_CMOVGE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVGE16rr, X86_INS_CMOVGE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVGE32rm, X86_INS_CMOVGE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVGE32rr, X86_INS_CMOVGE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVGE64rm, X86_INS_CMOVGE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVGE64rr, X86_INS_CMOVGE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVL16rm, X86_INS_CMOVL,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVL16rr, X86_INS_CMOVL,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVL32rm, X86_INS_CMOVL,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVL32rr, X86_INS_CMOVL,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVL64rm, X86_INS_CMOVL,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVL64rr, X86_INS_CMOVL,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVLE16rm, X86_INS_CMOVLE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVLE16rr, X86_INS_CMOVLE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVLE32rm, X86_INS_CMOVLE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVLE32rr, X86_INS_CMOVLE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVLE64rm, X86_INS_CMOVLE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVLE64rr, X86_INS_CMOVLE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVNE16rm, X86_INS_CMOVNE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVNE16rr, X86_INS_CMOVNE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVNE32rm, X86_INS_CMOVNE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVNE32rr, X86_INS_CMOVNE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVNE64rm, X86_INS_CMOVNE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVNE64rr, X86_INS_CMOVNE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVNO16rm, X86_INS_CMOVNO,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVNO16rr, X86_INS_CMOVNO,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVNO32rm, X86_INS_CMOVNO,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVNO32rr, X86_INS_CMOVNO,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVNO64rm, X86_INS_CMOVNO,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVNO64rr, X86_INS_CMOVNO,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVNP16rm, X86_INS_CMOVNP,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVNP16rr, X86_INS_CMOVNP,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVNP32rm, X86_INS_CMOVNP,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVNP32rr, X86_INS_CMOVNP,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVNP64rm, X86_INS_CMOVNP,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVNP64rr, X86_INS_CMOVNP,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVNS16rm, X86_INS_CMOVNS,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVNS16rr, X86_INS_CMOVNS,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVNS32rm, X86_INS_CMOVNS,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVNS32rr, X86_INS_CMOVNS,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVNS64rm, X86_INS_CMOVNS,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVNS64rr, X86_INS_CMOVNS,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVO16rm, X86_INS_CMOVO,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVO16rr, X86_INS_CMOVO,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVO32rm, X86_INS_CMOVO,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVO32rr, X86_INS_CMOVO,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVO64rm, X86_INS_CMOVO,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVO64rr, X86_INS_CMOVO,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVP16rm, X86_INS_CMOVP,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVP16rr, X86_INS_CMOVP,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVP32rm, X86_INS_CMOVP,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVP32rr, X86_INS_CMOVP,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVP64rm, X86_INS_CMOVP,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVP64rr, X86_INS_CMOVP,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVS16rm, X86_INS_CMOVS,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVS16rr, X86_INS_CMOVS,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVS32rm, X86_INS_CMOVS,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVS32rr, X86_INS_CMOVS,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVS64rm, X86_INS_CMOVS,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMOVS64rr, X86_INS_CMOVS,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_CMOV, 0 }, 0, 0
#endif
},
{
	X86_CMP16i16, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP16mi, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP16mi8, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP16mr, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP16ri, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP16ri8, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP16rm, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP16rr, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP16rr_REV, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP32i32, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP32mi, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP32mi8, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP32mr, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP32ri, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP32ri8, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP32rm, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP32rr, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP32rr_REV, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP64i32, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP64mi32, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP64mi8, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP64mr, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP64ri32, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP64ri8, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP64rm, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP64rr, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP64rr_REV, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP8i8, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP8mi, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP8mi8, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_CMP8mr, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP8ri, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP8ri8, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_CMP8rm, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP8rr, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMP8rr_REV, X86_INS_CMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMPSB, X86_INS_CMPSB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EDI, X86_REG_ESI, X86_REG_EFLAGS, 0 }, { X86_REG_EDI, X86_REG_ESI, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMPSL, X86_INS_CMPSD,
#ifndef CAPSTONE_DIET
	{ X86_REG_EDI, X86_REG_ESI, X86_REG_EFLAGS, 0 }, { X86_REG_EDI, X86_REG_ESI, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMPSQ, X86_INS_CMPSQ,
#ifndef CAPSTONE_DIET
	{ X86_REG_EDI, X86_REG_ESI, X86_REG_EFLAGS, 0 }, { X86_REG_EDI, X86_REG_ESI, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMPSW, X86_INS_CMPSW,
#ifndef CAPSTONE_DIET
	{ X86_REG_EDI, X86_REG_ESI, X86_REG_EFLAGS, 0 }, { X86_REG_EDI, X86_REG_ESI, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMPXCHG16B, X86_INS_CMPXCHG16B,
#ifndef CAPSTONE_DIET
	{ X86_REG_RAX, X86_REG_RBX, X86_REG_RCX, X86_REG_RDX, 0 }, { X86_REG_RAX, X86_REG_RDX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMPXCHG16rm, X86_INS_CMPXCHG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMPXCHG16rr, X86_INS_CMPXCHG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMPXCHG32rm, X86_INS_CMPXCHG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMPXCHG32rr, X86_INS_CMPXCHG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMPXCHG64rm, X86_INS_CMPXCHG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMPXCHG64rr, X86_INS_CMPXCHG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMPXCHG8B, X86_INS_CMPXCHG8B,
#ifndef CAPSTONE_DIET
	{ X86_REG_EAX, X86_REG_EBX, X86_REG_ECX, X86_REG_EDX, 0 }, { X86_REG_EAX, X86_REG_EDX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMPXCHG8rm, X86_INS_CMPXCHG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CMPXCHG8rr, X86_INS_CMPXCHG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CPUID, X86_INS_CPUID,
#ifndef CAPSTONE_DIET
	{ X86_REG_EAX, X86_REG_ECX, 0 }, { X86_REG_EAX, X86_REG_EBX, X86_REG_ECX, X86_REG_EDX, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CQO, X86_INS_CQO,
#ifndef CAPSTONE_DIET
	{ X86_REG_RAX, 0 }, { X86_REG_RAX, X86_REG_RDX, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CWD, X86_INS_CWD,
#ifndef CAPSTONE_DIET
	{ X86_REG_AX, 0 }, { X86_REG_AX, X86_REG_DX, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_CWDE, X86_INS_CWDE,
#ifndef CAPSTONE_DIET
	{ X86_REG_AX, 0 }, { X86_REG_EAX, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_DAA, X86_INS_DAA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_DAS, X86_INS_DAS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_DATA16_PREFIX, X86_INS_DATA16,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_DEC16m, X86_INS_DEC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_DEC16r, X86_INS_DEC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_DEC16r_alt, X86_INS_DEC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_DEC32m, X86_INS_DEC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_DEC32r, X86_INS_DEC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_DEC32r_alt, X86_INS_DEC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_DEC64m, X86_INS_DEC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_DEC64r, X86_INS_DEC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_DEC8m, X86_INS_DEC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_DEC8r, X86_INS_DEC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_DIV16m, X86_INS_DIV,
#ifndef CAPSTONE_DIET
	{ X86_REG_AX, X86_REG_DX, 0 }, { X86_REG_AX, X86_REG_DX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_DIV16r, X86_INS_DIV,
#ifndef CAPSTONE_DIET
	{ X86_REG_AX, X86_REG_DX, 0 }, { X86_REG_AX, X86_REG_DX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_DIV32m, X86_INS_DIV,
#ifndef CAPSTONE_DIET
	{ X86_REG_EAX, X86_REG_EDX, 0 }, { X86_REG_EAX, X86_REG_EDX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_DIV32r, X86_INS_DIV,
#ifndef CAPSTONE_DIET
	{ X86_REG_EAX, X86_REG_EDX, 0 }, { X86_REG_EAX, X86_REG_EDX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_DIV64m, X86_INS_DIV,
#ifndef CAPSTONE_DIET
	{ X86_REG_RAX, X86_REG_RDX, 0 }, { X86_REG_RAX, X86_REG_RDX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_DIV64r, X86_INS_DIV,
#ifndef CAPSTONE_DIET
	{ X86_REG_RAX, X86_REG_RDX, 0 }, { X86_REG_RAX, X86_REG_RDX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_DIV8m, X86_INS_DIV,
#ifndef CAPSTONE_DIET
	{ X86_REG_AX, 0 }, { X86_REG_AL, X86_REG_AH, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_DIV8r, X86_INS_DIV,
#ifndef CAPSTONE_DIET
	{ X86_REG_AX, 0 }, { X86_REG_AL, X86_REG_AH, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ENTER, X86_INS_ENTER,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_FARCALL16i, X86_INS_LCALL,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { 0 }, { X86_GRP_CALL, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_FARCALL16m, X86_INS_LCALL,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { 0 }, { X86_GRP_CALL, 0 }, 0, 0
#endif
},
{
	X86_FARCALL32i, X86_INS_LCALL,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { 0 }, { X86_GRP_CALL, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_FARCALL32m, X86_INS_LCALL,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { 0 }, { X86_GRP_CALL, 0 }, 0, 0
#endif
},
{
	X86_FARCALL64, X86_INS_LCALL,
#ifndef CAPSTONE_DIET
	{ X86_REG_RSP, 0 }, { 0 }, { X86_GRP_CALL, 0 }, 0, 0
#endif
},
{
	X86_FARJMP16i, X86_INS_LJMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 1, 1
#endif
},
{
	X86_FARJMP16m, X86_INS_LJMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 1
#endif
},
{
	X86_FARJMP32i, X86_INS_LJMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 1, 1
#endif
},
{
	X86_FARJMP32m, X86_INS_LJMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 1
#endif
},
{
	X86_FARJMP64, X86_INS_LJMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 1
#endif
},
{
	X86_FSETPM, X86_INS_FSETPM,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_GETSEC, X86_INS_GETSEC,
#ifndef CAPSTONE_DIET
	{ X86_REG_RAX, X86_REG_RBX, X86_REG_RCX, X86_REG_RDX, 0 }, { X86_REG_RAX, X86_REG_RBX, X86_REG_RCX, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_HLT, X86_INS_HLT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_IDIV16m, X86_INS_IDIV,
#ifndef CAPSTONE_DIET
	{ X86_REG_AX, X86_REG_DX, 0 }, { X86_REG_AX, X86_REG_DX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IDIV16r, X86_INS_IDIV,
#ifndef CAPSTONE_DIET
	{ X86_REG_AX, X86_REG_DX, 0 }, { X86_REG_AX, X86_REG_DX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IDIV32m, X86_INS_IDIV,
#ifndef CAPSTONE_DIET
	{ X86_REG_EAX, X86_REG_EDX, 0 }, { X86_REG_EAX, X86_REG_EDX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IDIV32r, X86_INS_IDIV,
#ifndef CAPSTONE_DIET
	{ X86_REG_EAX, X86_REG_EDX, 0 }, { X86_REG_EAX, X86_REG_EDX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IDIV64m, X86_INS_IDIV,
#ifndef CAPSTONE_DIET
	{ X86_REG_RAX, X86_REG_RDX, 0 }, { X86_REG_RAX, X86_REG_RDX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IDIV64r, X86_INS_IDIV,
#ifndef CAPSTONE_DIET
	{ X86_REG_RAX, X86_REG_RDX, 0 }, { X86_REG_RAX, X86_REG_RDX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IDIV8m, X86_INS_IDIV,
#ifndef CAPSTONE_DIET
	{ X86_REG_AX, 0 }, { X86_REG_AL, X86_REG_AH, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IDIV8r, X86_INS_IDIV,
#ifndef CAPSTONE_DIET
	{ X86_REG_AX, 0 }, { X86_REG_AL, X86_REG_AH, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IMUL16m, X86_INS_IMUL,
#ifndef CAPSTONE_DIET
	{ X86_REG_AX, 0 }, { X86_REG_AX, X86_REG_DX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IMUL16r, X86_INS_IMUL,
#ifndef CAPSTONE_DIET
	{ X86_REG_AX, 0 }, { X86_REG_AX, X86_REG_DX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IMUL16rm, X86_INS_IMUL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IMUL16rmi, X86_INS_IMUL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IMUL16rmi8, X86_INS_IMUL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IMUL16rr, X86_INS_IMUL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IMUL16rri, X86_INS_IMUL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IMUL16rri8, X86_INS_IMUL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IMUL32m, X86_INS_IMUL,
#ifndef CAPSTONE_DIET
	{ X86_REG_EAX, 0 }, { X86_REG_EAX, X86_REG_EDX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IMUL32r, X86_INS_IMUL,
#ifndef CAPSTONE_DIET
	{ X86_REG_EAX, 0 }, { X86_REG_EAX, X86_REG_EDX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IMUL32rm, X86_INS_IMUL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IMUL32rmi, X86_INS_IMUL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IMUL32rmi8, X86_INS_IMUL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IMUL32rr, X86_INS_IMUL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IMUL32rri, X86_INS_IMUL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IMUL32rri8, X86_INS_IMUL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IMUL64m, X86_INS_IMUL,
#ifndef CAPSTONE_DIET
	{ X86_REG_RAX, 0 }, { X86_REG_RAX, X86_REG_RDX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IMUL64r, X86_INS_IMUL,
#ifndef CAPSTONE_DIET
	{ X86_REG_RAX, 0 }, { X86_REG_RAX, X86_REG_RDX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IMUL64rm, X86_INS_IMUL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IMUL64rmi32, X86_INS_IMUL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IMUL64rmi8, X86_INS_IMUL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IMUL64rr, X86_INS_IMUL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IMUL64rri32, X86_INS_IMUL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IMUL64rri8, X86_INS_IMUL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IMUL8m, X86_INS_IMUL,
#ifndef CAPSTONE_DIET
	{ X86_REG_AL, 0 }, { X86_REG_AL, X86_REG_EFLAGS, X86_REG_AX, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IMUL8r, X86_INS_IMUL,
#ifndef CAPSTONE_DIET
	{ X86_REG_AL, 0 }, { X86_REG_AL, X86_REG_EFLAGS, X86_REG_AX, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IN16ri, X86_INS_IN,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_AX, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IN16rr, X86_INS_IN,
#ifndef CAPSTONE_DIET
	{ X86_REG_DX, 0 }, { X86_REG_AX, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IN32ri, X86_INS_IN,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EAX, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IN32rr, X86_INS_IN,
#ifndef CAPSTONE_DIET
	{ X86_REG_DX, 0 }, { X86_REG_EAX, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IN8ri, X86_INS_IN,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_AL, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_IN8rr, X86_INS_IN,
#ifndef CAPSTONE_DIET
	{ X86_REG_DX, 0 }, { X86_REG_AL, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_INC16m, X86_INS_INC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_INC16r, X86_INS_INC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_INC16r_alt, X86_INS_INC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_INC32m, X86_INS_INC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_INC32r, X86_INS_INC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_INC32r_alt, X86_INS_INC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_INC64m, X86_INS_INC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_INC64r, X86_INS_INC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_INC8m, X86_INS_INC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_INC8r, X86_INS_INC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_INSB, X86_INS_INSB,
#ifndef CAPSTONE_DIET
	{ X86_REG_DX, X86_REG_EDI, X86_REG_EFLAGS, 0 }, { X86_REG_EDI, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_INSL, X86_INS_INSD,
#ifndef CAPSTONE_DIET
	{ X86_REG_DX, X86_REG_EDI, X86_REG_EFLAGS, 0 }, { X86_REG_EDI, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_INSW, X86_INS_INSW,
#ifndef CAPSTONE_DIET
	{ X86_REG_DX, X86_REG_EDI, X86_REG_EFLAGS, 0 }, { X86_REG_EDI, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_INT, X86_INS_INT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_INT, 0 }, 0, 0
#endif
},
{
	X86_INT1, X86_INS_INT1,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_INT, 0 }, 0, 0
#endif
},
{
	X86_INT3, X86_INS_INT3,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_INT, 0 }, 0, 0
#endif
},
{
	X86_INTO, X86_INS_INTO,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { X86_GRP_INT, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_INVD, X86_INS_INVD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_INVEPT32, X86_INS_INVEPT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_INVEPT64, X86_INS_INVEPT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_INVLPG, X86_INS_INVLPG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_INVLPGA32, X86_INS_INVLPGA,
#ifndef CAPSTONE_DIET
	{ X86_REG_EAX, X86_REG_ECX, 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_INVLPGA64, X86_INS_INVLPGA,
#ifndef CAPSTONE_DIET
	{ X86_REG_RAX, X86_REG_ECX, 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_INVPCID32, X86_INS_INVPCID,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_INVPCID64, X86_INS_INVPCID,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_INVVPID32, X86_INS_INVVPID,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_INVVPID64, X86_INS_INVVPID,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_IRET16, X86_INS_IRET,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_IRET, 0 }, 0, 0
#endif
},
{
	X86_IRET32, X86_INS_IRETD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_IRET, 0 }, 0, 0
#endif
},
{
	X86_IRET64, X86_INS_IRETQ,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_IRET, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_JAE_1, X86_INS_JAE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JAE_2, X86_INS_JAE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JAE_4, X86_INS_JAE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JA_1, X86_INS_JA,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JA_2, X86_INS_JA,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JA_4, X86_INS_JA,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JBE_1, X86_INS_JBE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JBE_2, X86_INS_JBE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JBE_4, X86_INS_JBE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JB_1, X86_INS_JB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JB_2, X86_INS_JB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JB_4, X86_INS_JB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JCXZ, X86_INS_JCXZ,
#ifndef CAPSTONE_DIET
	{ X86_REG_CX, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JECXZ, X86_INS_JECXZ,
#ifndef CAPSTONE_DIET
	{ X86_REG_ECX, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JE_1, X86_INS_JE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JE_2, X86_INS_JE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JE_4, X86_INS_JE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JGE_1, X86_INS_JGE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JGE_2, X86_INS_JGE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JGE_4, X86_INS_JGE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JG_1, X86_INS_JG,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JG_2, X86_INS_JG,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JG_4, X86_INS_JG,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JLE_1, X86_INS_JLE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JLE_2, X86_INS_JLE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JLE_4, X86_INS_JLE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JL_1, X86_INS_JL,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JL_2, X86_INS_JL,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JL_4, X86_INS_JL,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JMP16m, X86_INS_JMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 1, 1
#endif
},
{
	X86_JMP16r, X86_INS_JMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 1, 1
#endif
},
{
	X86_JMP32m, X86_INS_JMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 1, 1
#endif
},
{
	X86_JMP32r, X86_INS_JMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 1, 1
#endif
},
{
	X86_JMP64m, X86_INS_JMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_MODE64, 0 }, 1, 1
#endif
},
{
	X86_JMP64r, X86_INS_JMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_MODE64, 0 }, 1, 1
#endif
},
{
	X86_JMP_1, X86_INS_JMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JMP_2, X86_INS_JMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JMP_4, X86_INS_JMP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JNE_1, X86_INS_JNE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JNE_2, X86_INS_JNE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JNE_4, X86_INS_JNE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JNO_1, X86_INS_JNO,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JNO_2, X86_INS_JNO,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JNO_4, X86_INS_JNO,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JNP_1, X86_INS_JNP,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JNP_2, X86_INS_JNP,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JNP_4, X86_INS_JNP,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JNS_1, X86_INS_JNS,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JNS_2, X86_INS_JNS,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JNS_4, X86_INS_JNS,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JO_1, X86_INS_JO,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JO_2, X86_INS_JO,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JO_4, X86_INS_JO,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JP_1, X86_INS_JP,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JP_2, X86_INS_JP,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JP_4, X86_INS_JP,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JRCXZ, X86_INS_JRCXZ,
#ifndef CAPSTONE_DIET
	{ X86_REG_RCX, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JS_1, X86_INS_JS,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JS_2, X86_INS_JS,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_JS_4, X86_INS_JS,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	X86_LAHF, X86_INS_LAHF,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_AH, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LAR16rm, X86_INS_LAR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LAR16rr, X86_INS_LAR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LAR32rm, X86_INS_LAR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LAR32rr, X86_INS_LAR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LAR64rm, X86_INS_LAR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LAR64rr, X86_INS_LAR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LCMPXCHG16, X86_INS_CMPXCHG,
#ifndef CAPSTONE_DIET
	{ X86_REG_AX, 0 }, { X86_REG_AX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LCMPXCHG16B, X86_INS_CMPXCHG16B,
#ifndef CAPSTONE_DIET
	{ X86_REG_RAX, X86_REG_RBX, X86_REG_RCX, X86_REG_RDX, 0 }, { X86_REG_RAX, X86_REG_RDX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LCMPXCHG32, X86_INS_CMPXCHG,
#ifndef CAPSTONE_DIET
	{ X86_REG_EAX, 0 }, { X86_REG_EAX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LCMPXCHG64, X86_INS_CMPXCHG,
#ifndef CAPSTONE_DIET
	{ X86_REG_RAX, 0 }, { X86_REG_RAX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LCMPXCHG8, X86_INS_CMPXCHG,
#ifndef CAPSTONE_DIET
	{ X86_REG_AL, 0 }, { X86_REG_AL, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LCMPXCHG8B, X86_INS_CMPXCHG8B,
#ifndef CAPSTONE_DIET
	{ X86_REG_EAX, X86_REG_EBX, X86_REG_ECX, X86_REG_EDX, 0 }, { X86_REG_EAX, X86_REG_EDX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LDS16rm, X86_INS_LDS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LDS32rm, X86_INS_LDS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LEA16r, X86_INS_LEA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LEA32r, X86_INS_LEA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_LEA64_32r, X86_INS_LEA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_LEA64r, X86_INS_LEA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LEAVE, X86_INS_LEAVE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EBP, X86_REG_ESP, 0 }, { X86_REG_EBP, X86_REG_ESP, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_LEAVE64, X86_INS_LEAVE,
#ifndef CAPSTONE_DIET
	{ X86_REG_RBP, X86_REG_RSP, 0 }, { X86_REG_RBP, X86_REG_RSP, 0 }, { X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_LES16rm, X86_INS_LES,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LES32rm, X86_INS_LES,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LFS16rm, X86_INS_LFS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LFS32rm, X86_INS_LFS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LFS64rm, X86_INS_LFS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LGDT16m, X86_INS_LGDT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_LGDT32m, X86_INS_LGDT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_LGDT64m, X86_INS_LGDT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_LGS16rm, X86_INS_LGS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LGS32rm, X86_INS_LGS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LGS64rm, X86_INS_LGS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LIDT16m, X86_INS_LIDT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_LIDT32m, X86_INS_LIDT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_LIDT64m, X86_INS_LIDT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_LLDT16m, X86_INS_LLDT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_LLDT16r, X86_INS_LLDT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_LMSW16m, X86_INS_LMSW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_LMSW16r, X86_INS_LMSW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_LOCK_ADD16mi, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_ADD16mi8, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_ADD16mr, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_ADD32mi, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_ADD32mi8, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_ADD32mr, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_ADD64mi32, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_ADD64mi8, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_ADD64mr, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_ADD8mi, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_ADD8mr, X86_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_AND16mi, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_AND16mi8, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_AND16mr, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_AND32mi, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_AND32mi8, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_AND32mr, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_AND64mi32, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_AND64mi8, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_AND64mr, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_AND8mi, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_AND8mr, X86_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_DEC16m, X86_INS_DEC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_DEC32m, X86_INS_DEC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_DEC64m, X86_INS_DEC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_DEC8m, X86_INS_DEC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_INC16m, X86_INS_INC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_INC32m, X86_INS_INC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_INC64m, X86_INS_INC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_INC8m, X86_INS_INC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_OR16mi, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_OR16mi8, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_OR16mr, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_OR32mi, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_OR32mi8, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_OR32mr, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_OR64mi32, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_OR64mi8, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_OR64mr, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_OR8mi, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_OR8mr, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_SUB16mi, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_SUB16mi8, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_SUB16mr, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_SUB32mi, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_SUB32mi8, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_SUB32mr, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_SUB64mi32, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_SUB64mi8, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_SUB64mr, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_SUB8mi, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_SUB8mr, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_XOR16mi, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_XOR16mi8, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_XOR16mr, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_XOR32mi, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_XOR32mi8, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_XOR32mr, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_XOR64mi32, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_XOR64mi8, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_XOR64mr, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_XOR8mi, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOCK_XOR8mr, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LODSB, X86_INS_LODSB,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESI, X86_REG_EFLAGS, 0 }, { X86_REG_AL, X86_REG_ESI, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LODSL, X86_INS_LODSD,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESI, X86_REG_EFLAGS, 0 }, { X86_REG_EAX, X86_REG_ESI, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LODSQ, X86_INS_LODSQ,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESI, X86_REG_EFLAGS, 0 }, { X86_REG_RAX, X86_REG_ESI, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LODSW, X86_INS_LODSW,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESI, X86_REG_EFLAGS, 0 }, { X86_REG_AX, X86_REG_ESI, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOOP, X86_INS_LOOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOOPE, X86_INS_LOOPE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LOOPNE, X86_INS_LOOPNE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LRETIL, X86_INS_RETF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_RET, 0 }, 0, 0
#endif
},
{
	X86_LRETIQ, X86_INS_RETFQ,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_RET, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_LRETIW, X86_INS_RETF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_RET, 0 }, 0, 0
#endif
},
{
	X86_LRETL, X86_INS_RETF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_RET, 0 }, 0, 0
#endif
},
{
	X86_LRETQ, X86_INS_RETFQ,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_RET, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_LRETW, X86_INS_RETF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_RET, 0 }, 0, 0
#endif
},
{
	X86_LSL16rm, X86_INS_LSL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LSL16rr, X86_INS_LSL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LSL32rm, X86_INS_LSL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LSL32rr, X86_INS_LSL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LSL64rm, X86_INS_LSL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LSL64rr, X86_INS_LSL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LSS16rm, X86_INS_LSS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LSS32rm, X86_INS_LSS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LSS64rm, X86_INS_LSS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LTRm, X86_INS_LTR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_LTRr, X86_INS_LTR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_LXADD16, X86_INS_XADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LXADD32, X86_INS_XADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LXADD64, X86_INS_XADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LXADD8, X86_INS_XADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LZCNT16rm, X86_INS_LZCNT,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LZCNT16rr, X86_INS_LZCNT,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LZCNT32rm, X86_INS_LZCNT,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LZCNT32rr, X86_INS_LZCNT,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LZCNT64rm, X86_INS_LZCNT,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_LZCNT64rr, X86_INS_LZCNT,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MONTMUL, X86_INS_MONTMUL,
#ifndef CAPSTONE_DIET
	{ X86_REG_RAX, X86_REG_RSI, 0 }, { X86_REG_RAX, X86_REG_RDX, X86_REG_RSI, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV16ao16, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV16ao32, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV16ao64, X86_INS_MOVABS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV16mi, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV16mr, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV16ms, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV16o16a, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV16o32a, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV16o64a, X86_INS_MOVABS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV16ri, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV16ri_alt, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV16rm, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV16rr, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV16rr_REV, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV16rs, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV16sm, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_MOV16sr, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_MOV32ao16, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV32ao32, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV32ao64, X86_INS_MOVABS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV32cr, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_MOV32dr, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_MOV32mi, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV32mr, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV32ms, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV32o16a, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV32o32a, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV32o64a, X86_INS_MOVABS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV32rc, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_MOV32rd, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_MOV32ri, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV32ri_alt, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV32rm, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV32rr, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV32rr_REV, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV32rs, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV32sm, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_MOV32sr, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_MOV64ao32, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV64ao64, X86_INS_MOVABS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV64cr, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_MOV64dr, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_MOV64mi32, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV64mr, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV64ms, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV64o32a, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV64o64a, X86_INS_MOVABS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV64rc, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_MOV64rd, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_MOV64ri, X86_INS_MOVABS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV64ri32, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV64rm, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV64rr, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV64rr_REV, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV64rs, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV64sm, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_MOV64sr, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_MOV8ao16, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV8ao32, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV8ao64, X86_INS_MOVABS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV8mi, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV8mr, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV8mr_NOREX, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV8o16a, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV8o32a, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV8o64a, X86_INS_MOVABS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV8ri, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV8ri_alt, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV8rm, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV8rm_NOREX, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV8rr, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV8rr_NOREX, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOV8rr_REV, X86_INS_MOV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVBE16mr, X86_INS_MOVBE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVBE16rm, X86_INS_MOVBE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVBE32mr, X86_INS_MOVBE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVBE32rm, X86_INS_MOVBE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVBE64mr, X86_INS_MOVBE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVBE64rm, X86_INS_MOVBE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVSB, X86_INS_MOVSB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EDI, X86_REG_ESI, X86_REG_EFLAGS, 0 }, { X86_REG_EDI, X86_REG_ESI, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVSL, X86_INS_MOVSD,
#ifndef CAPSTONE_DIET
	{ X86_REG_EDI, X86_REG_ESI, X86_REG_EFLAGS, 0 }, { X86_REG_EDI, X86_REG_ESI, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVSQ, X86_INS_MOVSQ,
#ifndef CAPSTONE_DIET
	{ X86_REG_EDI, X86_REG_ESI, X86_REG_EFLAGS, 0 }, { X86_REG_EDI, X86_REG_ESI, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVSW, X86_INS_MOVSW,
#ifndef CAPSTONE_DIET
	{ X86_REG_EDI, X86_REG_ESI, X86_REG_EFLAGS, 0 }, { X86_REG_EDI, X86_REG_ESI, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVSX16rm8, X86_INS_MOVSX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVSX16rr8, X86_INS_MOVSX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVSX32_NOREXrm8, X86_INS_MOVSX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVSX32_NOREXrr8, X86_INS_MOVSX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVSX32rm16, X86_INS_MOVSX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVSX32rm8, X86_INS_MOVSX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVSX32rr16, X86_INS_MOVSX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVSX32rr8, X86_INS_MOVSX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVSX64_NOREXrr32, X86_INS_MOVSXD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_MOVSX64rm16, X86_INS_MOVSX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVSX64rm32, X86_INS_MOVSXD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_MOVSX64rm32_alt, X86_INS_MOVSXD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_MOVSX64rm8, X86_INS_MOVSX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVSX64rr16, X86_INS_MOVSX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVSX64rr32, X86_INS_MOVSXD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_MOVSX64rr8, X86_INS_MOVSX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVZX16rm8, X86_INS_MOVZX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVZX16rr8, X86_INS_MOVZX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVZX32_NOREXrm8, X86_INS_MOVZX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVZX32_NOREXrr8, X86_INS_MOVZX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVZX32rm16, X86_INS_MOVZX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVZX32rm8, X86_INS_MOVZX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVZX32rr16, X86_INS_MOVZX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVZX32rr8, X86_INS_MOVZX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVZX64rm16_Q, X86_INS_MOVZX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVZX64rm8_Q, X86_INS_MOVZX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVZX64rr16_Q, X86_INS_MOVZX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MOVZX64rr8_Q, X86_INS_MOVZX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MUL16m, X86_INS_MUL,
#ifndef CAPSTONE_DIET
	{ X86_REG_AX, 0 }, { X86_REG_AX, X86_REG_DX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MUL16r, X86_INS_MUL,
#ifndef CAPSTONE_DIET
	{ X86_REG_AX, 0 }, { X86_REG_AX, X86_REG_DX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MUL32m, X86_INS_MUL,
#ifndef CAPSTONE_DIET
	{ X86_REG_EAX, 0 }, { X86_REG_EAX, X86_REG_EDX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MUL32r, X86_INS_MUL,
#ifndef CAPSTONE_DIET
	{ X86_REG_EAX, 0 }, { X86_REG_EAX, X86_REG_EDX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MUL64m, X86_INS_MUL,
#ifndef CAPSTONE_DIET
	{ X86_REG_RAX, 0 }, { X86_REG_RAX, X86_REG_RDX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MUL64r, X86_INS_MUL,
#ifndef CAPSTONE_DIET
	{ X86_REG_RAX, 0 }, { X86_REG_RAX, X86_REG_RDX, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MUL8m, X86_INS_MUL,
#ifndef CAPSTONE_DIET
	{ X86_REG_AL, 0 }, { X86_REG_AL, X86_REG_EFLAGS, X86_REG_AX, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MUL8r, X86_INS_MUL,
#ifndef CAPSTONE_DIET
	{ X86_REG_AL, 0 }, { X86_REG_AL, X86_REG_EFLAGS, X86_REG_AX, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_MULX32rm, X86_INS_MULX,
#ifndef CAPSTONE_DIET
	{ X86_REG_EDX, 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_MULX32rr, X86_INS_MULX,
#ifndef CAPSTONE_DIET
	{ X86_REG_EDX, 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_MULX64rm, X86_INS_MULX,
#ifndef CAPSTONE_DIET
	{ X86_REG_RDX, 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_MULX64rr, X86_INS_MULX,
#ifndef CAPSTONE_DIET
	{ X86_REG_RDX, 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_NEG16m, X86_INS_NEG,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NEG16r, X86_INS_NEG,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NEG32m, X86_INS_NEG,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NEG32r, X86_INS_NEG,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NEG64m, X86_INS_NEG,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NEG64r, X86_INS_NEG,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NEG8m, X86_INS_NEG,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NEG8r, X86_INS_NEG,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOP, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOP18_16m4, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOP18_16m5, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOP18_16m6, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOP18_16m7, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOP18_16r4, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOP18_16r5, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOP18_16r6, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOP18_16r7, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOP18_m4, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOP18_m5, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOP18_m6, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOP18_m7, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOP18_r4, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOP18_r5, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOP18_r6, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOP18_r7, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOP19rr, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOPL, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOPL_19, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOPL_1a, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOPL_1b, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOPL_1c, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOPL_1d, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOPL_1e, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOPW, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOPW_19, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOPW_1a, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOPW_1b, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOPW_1c, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOPW_1d, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOOPW_1e, X86_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOT16m, X86_INS_NOT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOT16r, X86_INS_NOT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOT32m, X86_INS_NOT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOT32r, X86_INS_NOT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOT64m, X86_INS_NOT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOT64r, X86_INS_NOT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOT8m, X86_INS_NOT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_NOT8r, X86_INS_NOT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR16i16, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR16mi, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR16mi8, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR16mr, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR16ri, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR16ri8, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR16rm, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR16rr, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR16rr_REV, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR32i32, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR32mi, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR32mi8, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR32mr, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR32mrLocked, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_OR32ri, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR32ri8, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR32rm, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR32rr, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR32rr_REV, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR64i32, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR64mi32, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR64mi8, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR64mr, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR64ri32, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR64ri8, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR64rm, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR64rr, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR64rr_REV, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR8i8, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR8mi, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR8mi8, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_OR8mr, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR8ri, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR8ri8, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_OR8rm, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR8rr, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OR8rr_REV, X86_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OUT16ir, X86_INS_OUT,
#ifndef CAPSTONE_DIET
	{ X86_REG_AX, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OUT16rr, X86_INS_OUT,
#ifndef CAPSTONE_DIET
	{ X86_REG_DX, X86_REG_AX, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OUT32ir, X86_INS_OUT,
#ifndef CAPSTONE_DIET
	{ X86_REG_EAX, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OUT32rr, X86_INS_OUT,
#ifndef CAPSTONE_DIET
	{ X86_REG_DX, X86_REG_EAX, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OUT8ir, X86_INS_OUT,
#ifndef CAPSTONE_DIET
	{ X86_REG_AL, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OUT8rr, X86_INS_OUT,
#ifndef CAPSTONE_DIET
	{ X86_REG_DX, X86_REG_AL, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OUTSB, X86_INS_OUTSB,
#ifndef CAPSTONE_DIET
	{ X86_REG_DX, X86_REG_ESI, X86_REG_EFLAGS, 0 }, { X86_REG_ESI, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OUTSL, X86_INS_OUTSD,
#ifndef CAPSTONE_DIET
	{ X86_REG_DX, X86_REG_ESI, X86_REG_EFLAGS, 0 }, { X86_REG_ESI, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_OUTSW, X86_INS_OUTSW,
#ifndef CAPSTONE_DIET
	{ X86_REG_DX, X86_REG_ESI, X86_REG_EFLAGS, 0 }, { X86_REG_ESI, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_PCOMMIT, X86_INS_PCOMMIT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_PDEP32rm, X86_INS_PDEP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_PDEP32rr, X86_INS_PDEP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_PDEP64rm, X86_INS_PDEP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_PDEP64rr, X86_INS_PDEP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_PEXT32rm, X86_INS_PEXT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_PEXT32rr, X86_INS_PEXT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_PEXT64rm, X86_INS_PEXT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_PEXT64rr, X86_INS_PEXT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_POP16r, X86_INS_POP,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { X86_REG_ESP, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_POP16rmm, X86_INS_POP,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { X86_REG_ESP, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_POP16rmr, X86_INS_POP,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { X86_REG_ESP, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_POP32r, X86_INS_POP,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { X86_REG_ESP, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_POP32rmm, X86_INS_POP,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { X86_REG_ESP, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_POP32rmr, X86_INS_POP,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { X86_REG_ESP, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_POP64r, X86_INS_POP,
#ifndef CAPSTONE_DIET
	{ X86_REG_RSP, 0 }, { X86_REG_RSP, 0 }, { X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_POP64rmm, X86_INS_POP,
#ifndef CAPSTONE_DIET
	{ X86_REG_RSP, 0 }, { X86_REG_RSP, 0 }, { X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_POP64rmr, X86_INS_POP,
#ifndef CAPSTONE_DIET
	{ X86_REG_RSP, 0 }, { X86_REG_RSP, 0 }, { X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_POPA16, X86_INS_POPAW,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { X86_REG_EDI, X86_REG_ESI, X86_REG_EBP, X86_REG_EBX, X86_REG_EDX, X86_REG_ECX, X86_REG_EAX, X86_REG_ESP, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_POPA32, X86_INS_POPAL,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { X86_REG_EDI, X86_REG_ESI, X86_REG_EBP, X86_REG_EBX, X86_REG_EDX, X86_REG_ECX, X86_REG_EAX, X86_REG_ESP, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_POPDS16, X86_INS_POP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_POPDS32, X86_INS_POP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_POPES16, X86_INS_POP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_POPES32, X86_INS_POP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_POPF16, X86_INS_POPF,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { X86_REG_ESP, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_POPF32, X86_INS_POPFD,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { X86_REG_ESP, X86_REG_EFLAGS, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_POPF64, X86_INS_POPFQ,
#ifndef CAPSTONE_DIET
	{ X86_REG_RSP, 0 }, { X86_REG_RSP, X86_REG_EFLAGS, 0 }, { X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_POPFS16, X86_INS_POP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_POPFS32, X86_INS_POP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_POPFS64, X86_INS_POP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_POPGS16, X86_INS_POP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_POPGS32, X86_INS_POP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_POPGS64, X86_INS_POP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_POPSS16, X86_INS_POP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_POPSS32, X86_INS_POP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_PUSH16i8, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { X86_REG_ESP, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_PUSH16r, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { X86_REG_ESP, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_PUSH16rmm, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { X86_REG_ESP, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_PUSH16rmr, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { X86_REG_ESP, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_PUSH32i8, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { X86_REG_ESP, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_PUSH32r, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { X86_REG_ESP, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_PUSH32rmm, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { X86_REG_ESP, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_PUSH32rmr, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { X86_REG_ESP, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_PUSH64i16, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ X86_REG_RSP, 0 }, { X86_REG_RSP, 0 }, { X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_PUSH64i32, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ X86_REG_RSP, 0 }, { X86_REG_RSP, 0 }, { X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_PUSH64i8, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ X86_REG_RSP, 0 }, { X86_REG_RSP, 0 }, { X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_PUSH64r, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ X86_REG_RSP, 0 }, { X86_REG_RSP, 0 }, { X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_PUSH64rmm, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ X86_REG_RSP, 0 }, { X86_REG_RSP, 0 }, { X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_PUSH64rmr, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ X86_REG_RSP, 0 }, { X86_REG_RSP, 0 }, { X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_PUSHA16, X86_INS_PUSHAW,
#ifndef CAPSTONE_DIET
	{ X86_REG_EDI, X86_REG_ESI, X86_REG_EBP, X86_REG_EBX, X86_REG_EDX, X86_REG_ECX, X86_REG_EAX, X86_REG_ESP, 0 }, { X86_REG_ESP, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_PUSHA32, X86_INS_PUSHAL,
#ifndef CAPSTONE_DIET
	{ X86_REG_EDI, X86_REG_ESI, X86_REG_EBP, X86_REG_EBX, X86_REG_EDX, X86_REG_ECX, X86_REG_EAX, X86_REG_ESP, 0 }, { X86_REG_ESP, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_PUSHCS16, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_PUSHCS32, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_PUSHDS16, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_PUSHDS32, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_PUSHES16, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_PUSHES32, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_PUSHF16, X86_INS_PUSHF,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, X86_REG_EFLAGS, 0 }, { X86_REG_ESP, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_PUSHF32, X86_INS_PUSHFD,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, X86_REG_EFLAGS, 0 }, { X86_REG_ESP, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_PUSHF64, X86_INS_PUSHFQ,
#ifndef CAPSTONE_DIET
	{ X86_REG_RSP, X86_REG_EFLAGS, 0 }, { X86_REG_RSP, 0 }, { X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_PUSHFS16, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_PUSHFS32, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_PUSHFS64, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_PUSHGS16, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_PUSHGS32, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_PUSHGS64, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_PUSHSS16, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_PUSHSS32, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_PUSHi16, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { X86_REG_ESP, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_PUSHi32, X86_INS_PUSH,
#ifndef CAPSTONE_DIET
	{ X86_REG_ESP, 0 }, { X86_REG_ESP, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_RCL16m1, X86_INS_RCL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCL16mCL, X86_INS_RCL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCL16mi, X86_INS_RCL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCL16r1, X86_INS_RCL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCL16rCL, X86_INS_RCL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCL16ri, X86_INS_RCL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCL32m1, X86_INS_RCL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCL32mCL, X86_INS_RCL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCL32mi, X86_INS_RCL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCL32r1, X86_INS_RCL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCL32rCL, X86_INS_RCL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCL32ri, X86_INS_RCL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCL64m1, X86_INS_RCL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCL64mCL, X86_INS_RCL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCL64mi, X86_INS_RCL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCL64r1, X86_INS_RCL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCL64rCL, X86_INS_RCL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCL64ri, X86_INS_RCL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCL8m1, X86_INS_RCL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCL8mCL, X86_INS_RCL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCL8mi, X86_INS_RCL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCL8r1, X86_INS_RCL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCL8rCL, X86_INS_RCL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCL8ri, X86_INS_RCL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCR16m1, X86_INS_RCR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCR16mCL, X86_INS_RCR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCR16mi, X86_INS_RCR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCR16r1, X86_INS_RCR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCR16rCL, X86_INS_RCR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCR16ri, X86_INS_RCR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCR32m1, X86_INS_RCR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCR32mCL, X86_INS_RCR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCR32mi, X86_INS_RCR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCR32r1, X86_INS_RCR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCR32rCL, X86_INS_RCR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCR32ri, X86_INS_RCR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCR64m1, X86_INS_RCR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCR64mCL, X86_INS_RCR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCR64mi, X86_INS_RCR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCR64r1, X86_INS_RCR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCR64rCL, X86_INS_RCR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCR64ri, X86_INS_RCR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCR8m1, X86_INS_RCR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCR8mCL, X86_INS_RCR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCR8mi, X86_INS_RCR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCR8r1, X86_INS_RCR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCR8rCL, X86_INS_RCR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RCR8ri, X86_INS_RCR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RDFSBASE, X86_INS_RDFSBASE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_FSGSBASE, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_RDFSBASE64, X86_INS_RDFSBASE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_FSGSBASE, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_RDGSBASE, X86_INS_RDGSBASE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_FSGSBASE, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_RDGSBASE64, X86_INS_RDGSBASE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_FSGSBASE, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_RDMSR, X86_INS_RDMSR,
#ifndef CAPSTONE_DIET
	{ X86_REG_ECX, 0 }, { X86_REG_EAX, X86_REG_EDX, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RDPMC, X86_INS_RDPMC,
#ifndef CAPSTONE_DIET
	{ X86_REG_ECX, 0 }, { X86_REG_RAX, X86_REG_RDX, 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_RDRAND16r, X86_INS_RDRAND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RDRAND32r, X86_INS_RDRAND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RDRAND64r, X86_INS_RDRAND,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RDSEED16r, X86_INS_RDSEED,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RDSEED32r, X86_INS_RDSEED,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RDSEED64r, X86_INS_RDSEED,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RDTSC, X86_INS_RDTSC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_RAX, X86_REG_RDX, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RDTSCP, X86_INS_RDTSCP,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_RAX, X86_REG_RCX, X86_REG_RDX, 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_RETIL, X86_INS_RET,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_RET, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_RETIQ, X86_INS_RET,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_RET, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_RETIW, X86_INS_RET,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_RET, 0 }, 0, 0
#endif
},
{
	X86_RETL, X86_INS_RET,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_RET, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_RETQ, X86_INS_RET,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_RET, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_RETW, X86_INS_RET,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_RET, 0 }, 0, 0
#endif
},
{
	X86_ROL16m1, X86_INS_ROL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROL16mCL, X86_INS_ROL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROL16mi, X86_INS_ROL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROL16r1, X86_INS_ROL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROL16rCL, X86_INS_ROL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROL16ri, X86_INS_ROL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROL32m1, X86_INS_ROL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROL32mCL, X86_INS_ROL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROL32mi, X86_INS_ROL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROL32r1, X86_INS_ROL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROL32rCL, X86_INS_ROL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROL32ri, X86_INS_ROL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROL64m1, X86_INS_ROL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROL64mCL, X86_INS_ROL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROL64mi, X86_INS_ROL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROL64r1, X86_INS_ROL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROL64rCL, X86_INS_ROL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROL64ri, X86_INS_ROL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROL8m1, X86_INS_ROL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROL8mCL, X86_INS_ROL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROL8mi, X86_INS_ROL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROL8r1, X86_INS_ROL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROL8rCL, X86_INS_ROL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROL8ri, X86_INS_ROL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROR16m1, X86_INS_ROR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROR16mCL, X86_INS_ROR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROR16mi, X86_INS_ROR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROR16r1, X86_INS_ROR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROR16rCL, X86_INS_ROR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROR16ri, X86_INS_ROR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROR32m1, X86_INS_ROR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROR32mCL, X86_INS_ROR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROR32mi, X86_INS_ROR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROR32r1, X86_INS_ROR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROR32rCL, X86_INS_ROR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROR32ri, X86_INS_ROR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROR64m1, X86_INS_ROR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROR64mCL, X86_INS_ROR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROR64mi, X86_INS_ROR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROR64r1, X86_INS_ROR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROR64rCL, X86_INS_ROR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROR64ri, X86_INS_ROR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROR8m1, X86_INS_ROR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROR8mCL, X86_INS_ROR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROR8mi, X86_INS_ROR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROR8r1, X86_INS_ROR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROR8rCL, X86_INS_ROR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_ROR8ri, X86_INS_ROR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_RORX32mi, X86_INS_RORX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_RORX32ri, X86_INS_RORX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_RORX64mi, X86_INS_RORX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_RORX64ri, X86_INS_RORX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_RSM, X86_INS_RSM,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_SAHF, X86_INS_SAHF,
#ifndef CAPSTONE_DIET
	{ X86_REG_AH, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAL16m1, X86_INS_SAL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAL16mCL, X86_INS_SAL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAL16mi, X86_INS_SAL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAL16r1, X86_INS_SAL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAL16rCL, X86_INS_SAL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAL16ri, X86_INS_SAL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAL32m1, X86_INS_SAL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAL32mCL, X86_INS_SAL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAL32mi, X86_INS_SAL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAL32r1, X86_INS_SAL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAL32rCL, X86_INS_SAL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAL32ri, X86_INS_SAL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAL64m1, X86_INS_SAL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAL64mCL, X86_INS_SAL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAL64mi, X86_INS_SAL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAL64r1, X86_INS_SAL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAL64rCL, X86_INS_SAL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAL64ri, X86_INS_SAL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAL8m1, X86_INS_SAL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAL8mCL, X86_INS_SAL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAL8mi, X86_INS_SAL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAL8r1, X86_INS_SAL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAL8rCL, X86_INS_SAL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAL8ri, X86_INS_SAL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SALC, X86_INS_SALC,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_AL, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_SAR16m1, X86_INS_SAR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAR16mCL, X86_INS_SAR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAR16mi, X86_INS_SAR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAR16r1, X86_INS_SAR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAR16rCL, X86_INS_SAR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAR16ri, X86_INS_SAR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAR32m1, X86_INS_SAR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAR32mCL, X86_INS_SAR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAR32mi, X86_INS_SAR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAR32r1, X86_INS_SAR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAR32rCL, X86_INS_SAR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAR32ri, X86_INS_SAR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAR64m1, X86_INS_SAR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAR64mCL, X86_INS_SAR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAR64mi, X86_INS_SAR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAR64r1, X86_INS_SAR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAR64rCL, X86_INS_SAR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAR64ri, X86_INS_SAR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAR8m1, X86_INS_SAR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAR8mCL, X86_INS_SAR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAR8mi, X86_INS_SAR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAR8r1, X86_INS_SAR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAR8rCL, X86_INS_SAR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SAR8ri, X86_INS_SAR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SARX32rm, X86_INS_SARX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_SARX32rr, X86_INS_SARX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_SARX64rm, X86_INS_SARX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_SARX64rr, X86_INS_SARX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_SBB16i16, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB16mi, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB16mi8, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB16mr, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB16ri, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB16ri8, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB16rm, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB16rr, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB16rr_REV, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB32i32, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB32mi, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB32mi8, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB32mr, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB32ri, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB32ri8, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB32rm, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB32rr, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB32rr_REV, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB64i32, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB64mi32, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB64mi8, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB64mr, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB64ri32, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB64ri8, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB64rm, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB64rr, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB64rr_REV, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB8i8, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB8mi, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB8mi8, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_SBB8mr, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB8ri, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB8ri8, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_SBB8rm, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB8rr, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SBB8rr_REV, X86_INS_SBB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SCASB, X86_INS_SCASB,
#ifndef CAPSTONE_DIET
	{ X86_REG_AL, X86_REG_EDI, X86_REG_EFLAGS, 0 }, { X86_REG_EDI, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SCASL, X86_INS_SCASD,
#ifndef CAPSTONE_DIET
	{ X86_REG_EAX, X86_REG_EDI, X86_REG_EFLAGS, 0 }, { X86_REG_EDI, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SCASQ, X86_INS_SCASQ,
#ifndef CAPSTONE_DIET
	{ X86_REG_RAX, X86_REG_EDI, X86_REG_EFLAGS, 0 }, { X86_REG_EDI, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SCASW, X86_INS_SCASW,
#ifndef CAPSTONE_DIET
	{ X86_REG_AX, X86_REG_EDI, X86_REG_EFLAGS, 0 }, { X86_REG_EDI, X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETAEm, X86_INS_SETAE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETAEr, X86_INS_SETAE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETAm, X86_INS_SETA,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETAr, X86_INS_SETA,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETBEm, X86_INS_SETBE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETBEr, X86_INS_SETBE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETBm, X86_INS_SETB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETBr, X86_INS_SETB,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETEm, X86_INS_SETE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETEr, X86_INS_SETE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETGEm, X86_INS_SETGE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETGEr, X86_INS_SETGE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETGm, X86_INS_SETG,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETGr, X86_INS_SETG,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETLEm, X86_INS_SETLE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETLEr, X86_INS_SETLE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETLm, X86_INS_SETL,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETLr, X86_INS_SETL,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETNEm, X86_INS_SETNE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETNEr, X86_INS_SETNE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETNOm, X86_INS_SETNO,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETNOr, X86_INS_SETNO,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETNPm, X86_INS_SETNP,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETNPr, X86_INS_SETNP,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETNSm, X86_INS_SETNS,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETNSr, X86_INS_SETNS,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETOm, X86_INS_SETO,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETOr, X86_INS_SETO,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETPm, X86_INS_SETP,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETPr, X86_INS_SETP,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETSm, X86_INS_SETS,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SETSr, X86_INS_SETS,
#ifndef CAPSTONE_DIET
	{ X86_REG_EFLAGS, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SGDT16m, X86_INS_SGDT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_SGDT32m, X86_INS_SGDT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_SGDT64m, X86_INS_SGDT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_SHL16m1, X86_INS_SHL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHL16mCL, X86_INS_SHL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHL16mi, X86_INS_SHL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHL16r1, X86_INS_SHL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHL16rCL, X86_INS_SHL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHL16ri, X86_INS_SHL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHL32m1, X86_INS_SHL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHL32mCL, X86_INS_SHL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHL32mi, X86_INS_SHL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHL32r1, X86_INS_SHL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHL32rCL, X86_INS_SHL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHL32ri, X86_INS_SHL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHL64m1, X86_INS_SHL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHL64mCL, X86_INS_SHL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHL64mi, X86_INS_SHL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHL64r1, X86_INS_SHL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHL64rCL, X86_INS_SHL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHL64ri, X86_INS_SHL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHL8m1, X86_INS_SHL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHL8mCL, X86_INS_SHL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHL8mi, X86_INS_SHL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHL8r1, X86_INS_SHL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHL8rCL, X86_INS_SHL,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHL8ri, X86_INS_SHL,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHLD16mrCL, X86_INS_SHLD,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHLD16mri8, X86_INS_SHLD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHLD16rrCL, X86_INS_SHLD,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHLD16rri8, X86_INS_SHLD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHLD32mrCL, X86_INS_SHLD,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHLD32mri8, X86_INS_SHLD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHLD32rrCL, X86_INS_SHLD,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHLD32rri8, X86_INS_SHLD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHLD64mrCL, X86_INS_SHLD,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHLD64mri8, X86_INS_SHLD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHLD64rrCL, X86_INS_SHLD,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHLD64rri8, X86_INS_SHLD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHLX32rm, X86_INS_SHLX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_SHLX32rr, X86_INS_SHLX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_SHLX64rm, X86_INS_SHLX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_SHLX64rr, X86_INS_SHLX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_SHR16m1, X86_INS_SHR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHR16mCL, X86_INS_SHR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHR16mi, X86_INS_SHR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHR16r1, X86_INS_SHR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHR16rCL, X86_INS_SHR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHR16ri, X86_INS_SHR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHR32m1, X86_INS_SHR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHR32mCL, X86_INS_SHR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHR32mi, X86_INS_SHR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHR32r1, X86_INS_SHR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHR32rCL, X86_INS_SHR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHR32ri, X86_INS_SHR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHR64m1, X86_INS_SHR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHR64mCL, X86_INS_SHR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHR64mi, X86_INS_SHR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHR64r1, X86_INS_SHR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHR64rCL, X86_INS_SHR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHR64ri, X86_INS_SHR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHR8m1, X86_INS_SHR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHR8mCL, X86_INS_SHR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHR8mi, X86_INS_SHR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHR8r1, X86_INS_SHR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHR8rCL, X86_INS_SHR,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHR8ri, X86_INS_SHR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHRD16mrCL, X86_INS_SHRD,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHRD16mri8, X86_INS_SHRD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHRD16rrCL, X86_INS_SHRD,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHRD16rri8, X86_INS_SHRD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHRD32mrCL, X86_INS_SHRD,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHRD32mri8, X86_INS_SHRD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHRD32rrCL, X86_INS_SHRD,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHRD32rri8, X86_INS_SHRD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHRD64mrCL, X86_INS_SHRD,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHRD64mri8, X86_INS_SHRD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHRD64rrCL, X86_INS_SHRD,
#ifndef CAPSTONE_DIET
	{ X86_REG_CL, 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHRD64rri8, X86_INS_SHRD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SHRX32rm, X86_INS_SHRX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_SHRX32rr, X86_INS_SHRX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_SHRX64rm, X86_INS_SHRX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_SHRX64rr, X86_INS_SHRX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_BMI2, 0 }, 0, 0
#endif
},
{
	X86_SIDT16m, X86_INS_SIDT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_SIDT32m, X86_INS_SIDT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_SIDT64m, X86_INS_SIDT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_SKINIT, X86_INS_SKINIT,
#ifndef CAPSTONE_DIET
	{ X86_REG_EAX, 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, 0 }, 0, 0
#endif
},
{
	X86_SLDT16m, X86_INS_SLDT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SLDT16r, X86_INS_SLDT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SLDT32r, X86_INS_SLDT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SLDT64m, X86_INS_SLDT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SLDT64r, X86_INS_SLDT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SMSW16m, X86_INS_SMSW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SMSW16r, X86_INS_SMSW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SMSW32r, X86_INS_SMSW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SMSW64r, X86_INS_SMSW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_STAC, X86_INS_STAC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_STC, X86_INS_STC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_STD, X86_INS_STD,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_STGI, X86_INS_STGI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, 0 }, 0, 0
#endif
},
{
	X86_STI, X86_INS_STI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_STOSB, X86_INS_STOSB,
#ifndef CAPSTONE_DIET
	{ X86_REG_AL, X86_REG_EDI, X86_REG_EFLAGS, 0 }, { X86_REG_EDI, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_STOSL, X86_INS_STOSD,
#ifndef CAPSTONE_DIET
	{ X86_REG_EAX, X86_REG_EDI, X86_REG_EFLAGS, 0 }, { X86_REG_EDI, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_STOSQ, X86_INS_STOSQ,
#ifndef CAPSTONE_DIET
	{ X86_REG_RAX, X86_REG_RCX, X86_REG_RDI, X86_REG_EFLAGS, 0 }, { X86_REG_RCX, X86_REG_RDI, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_STOSW, X86_INS_STOSW,
#ifndef CAPSTONE_DIET
	{ X86_REG_AX, X86_REG_EDI, X86_REG_EFLAGS, 0 }, { X86_REG_EDI, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_STR16r, X86_INS_STR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_STR32r, X86_INS_STR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_STR64r, X86_INS_STR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_STRm, X86_INS_STR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_SUB16i16, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB16mi, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB16mi8, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB16mr, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB16ri, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB16ri8, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB16rm, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB16rr, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB16rr_REV, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB32i32, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB32mi, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB32mi8, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB32mr, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB32ri, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB32ri8, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB32rm, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB32rr, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB32rr_REV, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB64i32, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB64mi32, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB64mi8, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB64mr, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB64ri32, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB64ri8, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB64rm, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB64rr, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB64rr_REV, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB8i8, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB8mi, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB8mi8, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_SUB8mr, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB8ri, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB8ri8, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_SUB8rm, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB8rr, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SUB8rr_REV, X86_INS_SUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_SWAPGS, X86_INS_SWAPGS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_SYSCALL, X86_INS_SYSCALL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_INT, 0 }, 0, 0
#endif
},
{
	X86_SYSENTER, X86_INS_SYSENTER,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_INT, 0 }, 0, 0
#endif
},
{
	X86_SYSEXIT, X86_INS_SYSEXIT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_IRET, 0 }, 0, 0
#endif
},
{
	X86_SYSEXIT64, X86_INS_SYSEXIT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_IRET, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_SYSRET, X86_INS_SYSRET,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_IRET, 0 }, 0, 0
#endif
},
{
	X86_SYSRET64, X86_INS_SYSRET,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_IRET, X86_GRP_PRIVILEGE, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_T1MSKC32rm, X86_INS_T1MSKC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_T1MSKC32rr, X86_INS_T1MSKC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_T1MSKC64rm, X86_INS_T1MSKC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_T1MSKC64rr, X86_INS_T1MSKC,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_TEST16i16, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TEST16mi, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TEST16mi_alt, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TEST16ri, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TEST16ri_alt, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TEST16rm, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TEST16rr, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TEST32i32, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TEST32mi, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TEST32mi_alt, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TEST32ri, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TEST32ri_alt, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TEST32rm, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TEST32rr, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TEST64i32, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TEST64mi32, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TEST64mi32_alt, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TEST64ri32, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TEST64ri32_alt, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TEST64rm, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TEST64rr, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TEST8i8, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TEST8mi, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TEST8mi_alt, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TEST8ri, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TEST8ri_alt, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TEST8rm, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TEST8rr, X86_INS_TEST,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TRAP, X86_INS_UD2,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_TZCNT16rm, X86_INS_TZCNT,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI, 0 }, 0, 0
#endif
},
{
	X86_TZCNT16rr, X86_INS_TZCNT,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI, 0 }, 0, 0
#endif
},
{
	X86_TZCNT32rm, X86_INS_TZCNT,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI, 0 }, 0, 0
#endif
},
{
	X86_TZCNT32rr, X86_INS_TZCNT,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI, 0 }, 0, 0
#endif
},
{
	X86_TZCNT64rm, X86_INS_TZCNT,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI, 0 }, 0, 0
#endif
},
{
	X86_TZCNT64rr, X86_INS_TZCNT,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_BMI, 0 }, 0, 0
#endif
},
{
	X86_TZMSK32rm, X86_INS_TZMSK,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_TZMSK32rr, X86_INS_TZMSK,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_TZMSK64rm, X86_INS_TZMSK,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_TZMSK64rr, X86_INS_TZMSK,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_TBM, 0 }, 0, 0
#endif
},
{
	X86_UD2B, X86_INS_UD2B,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_VERRm, X86_INS_VERR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_VERRr, X86_INS_VERR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_VERWm, X86_INS_VERW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_VERWr, X86_INS_VERW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_VMCALL, X86_INS_VMCALL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, 0 }, 0, 0
#endif
},
{
	X86_VMCLEARm, X86_INS_VMCLEAR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, 0 }, 0, 0
#endif
},
{
	X86_VMFUNC, X86_INS_VMFUNC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, 0 }, 0, 0
#endif
},
{
	X86_VMLAUNCH, X86_INS_VMLAUNCH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, 0 }, 0, 0
#endif
},
{
	X86_VMLOAD32, X86_INS_VMLOAD,
#ifndef CAPSTONE_DIET
	{ X86_REG_EAX, 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_VMLOAD64, X86_INS_VMLOAD,
#ifndef CAPSTONE_DIET
	{ X86_REG_RAX, 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_VMMCALL, X86_INS_VMMCALL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, 0 }, 0, 0
#endif
},
{
	X86_VMPTRLDm, X86_INS_VMPTRLD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, 0 }, 0, 0
#endif
},
{
	X86_VMPTRSTm, X86_INS_VMPTRST,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, 0 }, 0, 0
#endif
},
{
	X86_VMREAD32rm, X86_INS_VMREAD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_VMREAD32rr, X86_INS_VMREAD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_VMREAD64rm, X86_INS_VMREAD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_VMREAD64rr, X86_INS_VMREAD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_VMRESUME, X86_INS_VMRESUME,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, 0 }, 0, 0
#endif
},
{
	X86_VMRUN32, X86_INS_VMRUN,
#ifndef CAPSTONE_DIET
	{ X86_REG_EAX, 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_VMRUN64, X86_INS_VMRUN,
#ifndef CAPSTONE_DIET
	{ X86_REG_RAX, 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_VMSAVE32, X86_INS_VMSAVE,
#ifndef CAPSTONE_DIET
	{ X86_REG_EAX, 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_VMSAVE64, X86_INS_VMSAVE,
#ifndef CAPSTONE_DIET
	{ X86_REG_RAX, 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_VMWRITE32rm, X86_INS_VMWRITE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_VMWRITE32rr, X86_INS_VMWRITE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_VMWRITE64rm, X86_INS_VMWRITE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_VMWRITE64rr, X86_INS_VMWRITE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_VMXOFF, X86_INS_VMXOFF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, 0 }, 0, 0
#endif
},
{
	X86_VMXON, X86_INS_VMXON,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_VM, 0 }, 0, 0
#endif
},
{
	X86_WBINVD, X86_INS_WBINVD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_WRFSBASE, X86_INS_WRFSBASE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_FSGSBASE, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_WRFSBASE64, X86_INS_WRFSBASE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_FSGSBASE, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_WRGSBASE, X86_INS_WRGSBASE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_FSGSBASE, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_WRGSBASE64, X86_INS_WRGSBASE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_FSGSBASE, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_WRMSR, X86_INS_WRMSR,
#ifndef CAPSTONE_DIET
	{ X86_REG_EAX, X86_REG_ECX, X86_REG_EDX, 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_XADD16rm, X86_INS_XADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XADD16rr, X86_INS_XADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XADD32rm, X86_INS_XADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XADD32rr, X86_INS_XADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XADD64rm, X86_INS_XADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XADD64rr, X86_INS_XADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XADD8rm, X86_INS_XADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XADD8rr, X86_INS_XADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XCHG16ar, X86_INS_XCHG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XCHG16rm, X86_INS_XCHG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XCHG16rr, X86_INS_XCHG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XCHG32ar, X86_INS_XCHG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_XCHG32ar64, X86_INS_XCHG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_XCHG32rm, X86_INS_XCHG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XCHG32rr, X86_INS_XCHG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XCHG64ar, X86_INS_XCHG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XCHG64rm, X86_INS_XCHG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XCHG64rr, X86_INS_XCHG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XCHG8rm, X86_INS_XCHG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XCHG8rr, X86_INS_XCHG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XCRYPTCBC, X86_INS_XCRYPTCBC,
#ifndef CAPSTONE_DIET
	{ X86_REG_RBX, X86_REG_RDX, X86_REG_RSI, X86_REG_RDI, 0 }, { X86_REG_RSI, X86_REG_RDI, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XCRYPTCFB, X86_INS_XCRYPTCFB,
#ifndef CAPSTONE_DIET
	{ X86_REG_RBX, X86_REG_RDX, X86_REG_RSI, X86_REG_RDI, 0 }, { X86_REG_RSI, X86_REG_RDI, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XCRYPTCTR, X86_INS_XCRYPTCTR,
#ifndef CAPSTONE_DIET
	{ X86_REG_RBX, X86_REG_RDX, X86_REG_RSI, X86_REG_RDI, 0 }, { X86_REG_RSI, X86_REG_RDI, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XCRYPTECB, X86_INS_XCRYPTECB,
#ifndef CAPSTONE_DIET
	{ X86_REG_RBX, X86_REG_RDX, X86_REG_RSI, X86_REG_RDI, 0 }, { X86_REG_RSI, X86_REG_RDI, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XCRYPTOFB, X86_INS_XCRYPTOFB,
#ifndef CAPSTONE_DIET
	{ X86_REG_RBX, X86_REG_RDX, X86_REG_RSI, X86_REG_RDI, 0 }, { X86_REG_RSI, X86_REG_RDI, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XGETBV, X86_INS_XGETBV,
#ifndef CAPSTONE_DIET
	{ X86_REG_ECX, 0 }, { X86_REG_EDX, X86_REG_EAX, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XLAT, X86_INS_XLATB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR16i16, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR16mi, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR16mi8, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR16mr, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR16ri, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR16ri8, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR16rm, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR16rr, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR16rr_REV, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR32i32, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR32mi, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR32mi8, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR32mr, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR32ri, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR32ri8, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR32rm, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR32rr, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR32rr_REV, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR64i32, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR64mi32, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR64mi8, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR64mr, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR64ri32, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR64ri8, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR64rm, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR64rr, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR64rr_REV, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR8i8, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR8mi, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR8mi8, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_XOR8mr, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR8ri, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR8ri8, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { X86_GRP_NOT64BITMODE, 0 }, 0, 0
#endif
},
{
	X86_XOR8rm, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR8rr, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XOR8rr_REV, X86_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { X86_REG_EFLAGS, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XRSTOR, X86_INS_XRSTOR,
#ifndef CAPSTONE_DIET
	{ X86_REG_RDX, X86_REG_RAX, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XRSTOR64, X86_INS_XRSTOR64,
#ifndef CAPSTONE_DIET
	{ X86_REG_RDX, X86_REG_RAX, 0 }, { 0 }, { X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_XRSTORS, X86_INS_XRSTORS,
#ifndef CAPSTONE_DIET
	{ X86_REG_RDX, X86_REG_RAX, 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_XRSTORS64, X86_INS_XRSTORS64,
#ifndef CAPSTONE_DIET
	{ X86_REG_RDX, X86_REG_RAX, 0 }, { 0 }, { X86_GRP_PRIVILEGE, X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_XSAVE, X86_INS_XSAVE,
#ifndef CAPSTONE_DIET
	{ X86_REG_RDX, X86_REG_RAX, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XSAVE64, X86_INS_XSAVE64,
#ifndef CAPSTONE_DIET
	{ X86_REG_RDX, X86_REG_RAX, 0 }, { 0 }, { X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_XSAVEC, X86_INS_XSAVEC,
#ifndef CAPSTONE_DIET
	{ X86_REG_RDX, X86_REG_RAX, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XSAVEC64, X86_INS_XSAVEC64,
#ifndef CAPSTONE_DIET
	{ X86_REG_RDX, X86_REG_RAX, 0 }, { 0 }, { X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_XSAVEOPT, X86_INS_XSAVEOPT,
#ifndef CAPSTONE_DIET
	{ X86_REG_RDX, X86_REG_RAX, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XSAVEOPT64, X86_INS_XSAVEOPT64,
#ifndef CAPSTONE_DIET
	{ X86_REG_RDX, X86_REG_RAX, 0 }, { 0 }, { X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_XSAVES, X86_INS_XSAVES,
#ifndef CAPSTONE_DIET
	{ X86_REG_RDX, X86_REG_RAX, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XSAVES64, X86_INS_XSAVES64,
#ifndef CAPSTONE_DIET
	{ X86_REG_RDX, X86_REG_RAX, 0 }, { 0 }, { X86_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	X86_XSETBV, X86_INS_XSETBV,
#ifndef CAPSTONE_DIET
	{ X86_REG_EDX, X86_REG_EAX, X86_REG_ECX, 0 }, { 0 }, { X86_GRP_PRIVILEGE, 0 }, 0, 0
#endif
},
{
	X86_XSHA1, X86_INS_XSHA1,
#ifndef CAPSTONE_DIET
	{ X86_REG_RAX, X86_REG_RSI, X86_REG_RDI, 0 }, { X86_REG_RAX, X86_REG_RSI, X86_REG_RDI, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XSHA256, X86_INS_XSHA256,
#ifndef CAPSTONE_DIET
	{ X86_REG_RAX, X86_REG_RSI, X86_REG_RDI, 0 }, { X86_REG_RAX, X86_REG_RSI, X86_REG_RDI, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_XSTORE, X86_INS_XSTORE,
#ifndef CAPSTONE_DIET
	{ X86_REG_RDX, X86_REG_RDI, 0 }, { X86_REG_RAX, X86_REG_RDI, 0 }, { 0 }, 0, 0
#endif
},
{
	X86_UD0, X86_INS_UD0,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
