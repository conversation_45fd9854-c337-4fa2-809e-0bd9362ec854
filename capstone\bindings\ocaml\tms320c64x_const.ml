(* For Capstone Engine. AUTO-GENERATED FILE, DO NOT EDIT [tms320c64x_const.ml] *)

let _TMS320C64X_OP_INVALID = 0;;
let _TMS320C64X_OP_REG = 1;;
let _TMS320C64X_OP_IMM = 2;;
let _TMS320C64X_OP_MEM = 3;;
let _TMS320C64X_OP_REGPAIR = 64;;

let _TMS320C64X_MEM_DISP_INVALID = 0;;
let _TMS320C64X_MEM_DISP_CONSTANT = 1;;
let _TMS320C64X_MEM_DISP_REGISTER = 2;;

let _TMS320C64X_MEM_DIR_INVALID = 0;;
let _TMS320C64X_MEM_DIR_FW = 1;;
let _TMS320C64X_MEM_DIR_BW = 2;;

let _TMS320C64X_MEM_MOD_INVALID = 0;;
let _TMS320C64X_MEM_MOD_NO = 1;;
let _TMS320C64X_MEM_MOD_PRE = 2;;
let _TMS320C64X_MEM_MOD_POST = 3;;

let _TMS320C64X_REG_INVALID = 0;;
let _TMS320C64X_REG_AMR = 1;;
let _TMS320C64X_REG_CSR = 2;;
let _TMS320C64X_REG_DIER = 3;;
let _TMS320C64X_REG_DNUM = 4;;
let _TMS320C64X_REG_ECR = 5;;
let _TMS320C64X_REG_GFPGFR = 6;;
let _TMS320C64X_REG_GPLYA = 7;;
let _TMS320C64X_REG_GPLYB = 8;;
let _TMS320C64X_REG_ICR = 9;;
let _TMS320C64X_REG_IER = 10;;
let _TMS320C64X_REG_IERR = 11;;
let _TMS320C64X_REG_ILC = 12;;
let _TMS320C64X_REG_IRP = 13;;
let _TMS320C64X_REG_ISR = 14;;
let _TMS320C64X_REG_ISTP = 15;;
let _TMS320C64X_REG_ITSR = 16;;
let _TMS320C64X_REG_NRP = 17;;
let _TMS320C64X_REG_NTSR = 18;;
let _TMS320C64X_REG_REP = 19;;
let _TMS320C64X_REG_RILC = 20;;
let _TMS320C64X_REG_SSR = 21;;
let _TMS320C64X_REG_TSCH = 22;;
let _TMS320C64X_REG_TSCL = 23;;
let _TMS320C64X_REG_TSR = 24;;
let _TMS320C64X_REG_A0 = 25;;
let _TMS320C64X_REG_A1 = 26;;
let _TMS320C64X_REG_A2 = 27;;
let _TMS320C64X_REG_A3 = 28;;
let _TMS320C64X_REG_A4 = 29;;
let _TMS320C64X_REG_A5 = 30;;
let _TMS320C64X_REG_A6 = 31;;
let _TMS320C64X_REG_A7 = 32;;
let _TMS320C64X_REG_A8 = 33;;
let _TMS320C64X_REG_A9 = 34;;
let _TMS320C64X_REG_A10 = 35;;
let _TMS320C64X_REG_A11 = 36;;
let _TMS320C64X_REG_A12 = 37;;
let _TMS320C64X_REG_A13 = 38;;
let _TMS320C64X_REG_A14 = 39;;
let _TMS320C64X_REG_A15 = 40;;
let _TMS320C64X_REG_A16 = 41;;
let _TMS320C64X_REG_A17 = 42;;
let _TMS320C64X_REG_A18 = 43;;
let _TMS320C64X_REG_A19 = 44;;
let _TMS320C64X_REG_A20 = 45;;
let _TMS320C64X_REG_A21 = 46;;
let _TMS320C64X_REG_A22 = 47;;
let _TMS320C64X_REG_A23 = 48;;
let _TMS320C64X_REG_A24 = 49;;
let _TMS320C64X_REG_A25 = 50;;
let _TMS320C64X_REG_A26 = 51;;
let _TMS320C64X_REG_A27 = 52;;
let _TMS320C64X_REG_A28 = 53;;
let _TMS320C64X_REG_A29 = 54;;
let _TMS320C64X_REG_A30 = 55;;
let _TMS320C64X_REG_A31 = 56;;
let _TMS320C64X_REG_B0 = 57;;
let _TMS320C64X_REG_B1 = 58;;
let _TMS320C64X_REG_B2 = 59;;
let _TMS320C64X_REG_B3 = 60;;
let _TMS320C64X_REG_B4 = 61;;
let _TMS320C64X_REG_B5 = 62;;
let _TMS320C64X_REG_B6 = 63;;
let _TMS320C64X_REG_B7 = 64;;
let _TMS320C64X_REG_B8 = 65;;
let _TMS320C64X_REG_B9 = 66;;
let _TMS320C64X_REG_B10 = 67;;
let _TMS320C64X_REG_B11 = 68;;
let _TMS320C64X_REG_B12 = 69;;
let _TMS320C64X_REG_B13 = 70;;
let _TMS320C64X_REG_B14 = 71;;
let _TMS320C64X_REG_B15 = 72;;
let _TMS320C64X_REG_B16 = 73;;
let _TMS320C64X_REG_B17 = 74;;
let _TMS320C64X_REG_B18 = 75;;
let _TMS320C64X_REG_B19 = 76;;
let _TMS320C64X_REG_B20 = 77;;
let _TMS320C64X_REG_B21 = 78;;
let _TMS320C64X_REG_B22 = 79;;
let _TMS320C64X_REG_B23 = 80;;
let _TMS320C64X_REG_B24 = 81;;
let _TMS320C64X_REG_B25 = 82;;
let _TMS320C64X_REG_B26 = 83;;
let _TMS320C64X_REG_B27 = 84;;
let _TMS320C64X_REG_B28 = 85;;
let _TMS320C64X_REG_B29 = 86;;
let _TMS320C64X_REG_B30 = 87;;
let _TMS320C64X_REG_B31 = 88;;
let _TMS320C64X_REG_PCE1 = 89;;
let _TMS320C64X_REG_ENDING = 90;;
let _TMS320C64X_REG_EFR = _TMS320C64X_REG_ECR;;
let _TMS320C64X_REG_IFR = _TMS320C64X_REG_ISR;;

let _TMS320C64X_INS_INVALID = 0;;
let _TMS320C64X_INS_ABS = 1;;
let _TMS320C64X_INS_ABS2 = 2;;
let _TMS320C64X_INS_ADD = 3;;
let _TMS320C64X_INS_ADD2 = 4;;
let _TMS320C64X_INS_ADD4 = 5;;
let _TMS320C64X_INS_ADDAB = 6;;
let _TMS320C64X_INS_ADDAD = 7;;
let _TMS320C64X_INS_ADDAH = 8;;
let _TMS320C64X_INS_ADDAW = 9;;
let _TMS320C64X_INS_ADDK = 10;;
let _TMS320C64X_INS_ADDKPC = 11;;
let _TMS320C64X_INS_ADDU = 12;;
let _TMS320C64X_INS_AND = 13;;
let _TMS320C64X_INS_ANDN = 14;;
let _TMS320C64X_INS_AVG2 = 15;;
let _TMS320C64X_INS_AVGU4 = 16;;
let _TMS320C64X_INS_B = 17;;
let _TMS320C64X_INS_BDEC = 18;;
let _TMS320C64X_INS_BITC4 = 19;;
let _TMS320C64X_INS_BNOP = 20;;
let _TMS320C64X_INS_BPOS = 21;;
let _TMS320C64X_INS_CLR = 22;;
let _TMS320C64X_INS_CMPEQ = 23;;
let _TMS320C64X_INS_CMPEQ2 = 24;;
let _TMS320C64X_INS_CMPEQ4 = 25;;
let _TMS320C64X_INS_CMPGT = 26;;
let _TMS320C64X_INS_CMPGT2 = 27;;
let _TMS320C64X_INS_CMPGTU4 = 28;;
let _TMS320C64X_INS_CMPLT = 29;;
let _TMS320C64X_INS_CMPLTU = 30;;
let _TMS320C64X_INS_DEAL = 31;;
let _TMS320C64X_INS_DOTP2 = 32;;
let _TMS320C64X_INS_DOTPN2 = 33;;
let _TMS320C64X_INS_DOTPNRSU2 = 34;;
let _TMS320C64X_INS_DOTPRSU2 = 35;;
let _TMS320C64X_INS_DOTPSU4 = 36;;
let _TMS320C64X_INS_DOTPU4 = 37;;
let _TMS320C64X_INS_EXT = 38;;
let _TMS320C64X_INS_EXTU = 39;;
let _TMS320C64X_INS_GMPGTU = 40;;
let _TMS320C64X_INS_GMPY4 = 41;;
let _TMS320C64X_INS_LDB = 42;;
let _TMS320C64X_INS_LDBU = 43;;
let _TMS320C64X_INS_LDDW = 44;;
let _TMS320C64X_INS_LDH = 45;;
let _TMS320C64X_INS_LDHU = 46;;
let _TMS320C64X_INS_LDNDW = 47;;
let _TMS320C64X_INS_LDNW = 48;;
let _TMS320C64X_INS_LDW = 49;;
let _TMS320C64X_INS_LMBD = 50;;
let _TMS320C64X_INS_MAX2 = 51;;
let _TMS320C64X_INS_MAXU4 = 52;;
let _TMS320C64X_INS_MIN2 = 53;;
let _TMS320C64X_INS_MINU4 = 54;;
let _TMS320C64X_INS_MPY = 55;;
let _TMS320C64X_INS_MPY2 = 56;;
let _TMS320C64X_INS_MPYH = 57;;
let _TMS320C64X_INS_MPYHI = 58;;
let _TMS320C64X_INS_MPYHIR = 59;;
let _TMS320C64X_INS_MPYHL = 60;;
let _TMS320C64X_INS_MPYHLU = 61;;
let _TMS320C64X_INS_MPYHSLU = 62;;
let _TMS320C64X_INS_MPYHSU = 63;;
let _TMS320C64X_INS_MPYHU = 64;;
let _TMS320C64X_INS_MPYHULS = 65;;
let _TMS320C64X_INS_MPYHUS = 66;;
let _TMS320C64X_INS_MPYLH = 67;;
let _TMS320C64X_INS_MPYLHU = 68;;
let _TMS320C64X_INS_MPYLI = 69;;
let _TMS320C64X_INS_MPYLIR = 70;;
let _TMS320C64X_INS_MPYLSHU = 71;;
let _TMS320C64X_INS_MPYLUHS = 72;;
let _TMS320C64X_INS_MPYSU = 73;;
let _TMS320C64X_INS_MPYSU4 = 74;;
let _TMS320C64X_INS_MPYU = 75;;
let _TMS320C64X_INS_MPYU4 = 76;;
let _TMS320C64X_INS_MPYUS = 77;;
let _TMS320C64X_INS_MVC = 78;;
let _TMS320C64X_INS_MVD = 79;;
let _TMS320C64X_INS_MVK = 80;;
let _TMS320C64X_INS_MVKL = 81;;
let _TMS320C64X_INS_MVKLH = 82;;
let _TMS320C64X_INS_NOP = 83;;
let _TMS320C64X_INS_NORM = 84;;
let _TMS320C64X_INS_OR = 85;;
let _TMS320C64X_INS_PACK2 = 86;;
let _TMS320C64X_INS_PACKH2 = 87;;
let _TMS320C64X_INS_PACKH4 = 88;;
let _TMS320C64X_INS_PACKHL2 = 89;;
let _TMS320C64X_INS_PACKL4 = 90;;
let _TMS320C64X_INS_PACKLH2 = 91;;
let _TMS320C64X_INS_ROTL = 92;;
let _TMS320C64X_INS_SADD = 93;;
let _TMS320C64X_INS_SADD2 = 94;;
let _TMS320C64X_INS_SADDU4 = 95;;
let _TMS320C64X_INS_SADDUS2 = 96;;
let _TMS320C64X_INS_SAT = 97;;
let _TMS320C64X_INS_SET = 98;;
let _TMS320C64X_INS_SHFL = 99;;
let _TMS320C64X_INS_SHL = 100;;
let _TMS320C64X_INS_SHLMB = 101;;
let _TMS320C64X_INS_SHR = 102;;
let _TMS320C64X_INS_SHR2 = 103;;
let _TMS320C64X_INS_SHRMB = 104;;
let _TMS320C64X_INS_SHRU = 105;;
let _TMS320C64X_INS_SHRU2 = 106;;
let _TMS320C64X_INS_SMPY = 107;;
let _TMS320C64X_INS_SMPY2 = 108;;
let _TMS320C64X_INS_SMPYH = 109;;
let _TMS320C64X_INS_SMPYHL = 110;;
let _TMS320C64X_INS_SMPYLH = 111;;
let _TMS320C64X_INS_SPACK2 = 112;;
let _TMS320C64X_INS_SPACKU4 = 113;;
let _TMS320C64X_INS_SSHL = 114;;
let _TMS320C64X_INS_SSHVL = 115;;
let _TMS320C64X_INS_SSHVR = 116;;
let _TMS320C64X_INS_SSUB = 117;;
let _TMS320C64X_INS_STB = 118;;
let _TMS320C64X_INS_STDW = 119;;
let _TMS320C64X_INS_STH = 120;;
let _TMS320C64X_INS_STNDW = 121;;
let _TMS320C64X_INS_STNW = 122;;
let _TMS320C64X_INS_STW = 123;;
let _TMS320C64X_INS_SUB = 124;;
let _TMS320C64X_INS_SUB2 = 125;;
let _TMS320C64X_INS_SUB4 = 126;;
let _TMS320C64X_INS_SUBAB = 127;;
let _TMS320C64X_INS_SUBABS4 = 128;;
let _TMS320C64X_INS_SUBAH = 129;;
let _TMS320C64X_INS_SUBAW = 130;;
let _TMS320C64X_INS_SUBC = 131;;
let _TMS320C64X_INS_SUBU = 132;;
let _TMS320C64X_INS_SWAP4 = 133;;
let _TMS320C64X_INS_UNPKHU4 = 134;;
let _TMS320C64X_INS_UNPKLU4 = 135;;
let _TMS320C64X_INS_XOR = 136;;
let _TMS320C64X_INS_XPND2 = 137;;
let _TMS320C64X_INS_XPND4 = 138;;
let _TMS320C64X_INS_IDLE = 139;;
let _TMS320C64X_INS_MV = 140;;
let _TMS320C64X_INS_NEG = 141;;
let _TMS320C64X_INS_NOT = 142;;
let _TMS320C64X_INS_SWAP2 = 143;;
let _TMS320C64X_INS_ZERO = 144;;
let _TMS320C64X_INS_ENDING = 145;;

let _TMS320C64X_GRP_INVALID = 0;;
let _TMS320C64X_GRP_JUMP = 1;;
let _TMS320C64X_GRP_FUNIT_D = 128;;
let _TMS320C64X_GRP_FUNIT_L = 129;;
let _TMS320C64X_GRP_FUNIT_M = 130;;
let _TMS320C64X_GRP_FUNIT_S = 131;;
let _TMS320C64X_GRP_FUNIT_NO = 132;;
let _TMS320C64X_GRP_ENDING = 133;;

let _TMS320C64X_FUNIT_INVALID = 0;;
let _TMS320C64X_FUNIT_D = 1;;
let _TMS320C64X_FUNIT_L = 2;;
let _TMS320C64X_FUNIT_M = 3;;
let _TMS320C64X_FUNIT_S = 4;;
let _TMS320C64X_FUNIT_NO = 5;;
