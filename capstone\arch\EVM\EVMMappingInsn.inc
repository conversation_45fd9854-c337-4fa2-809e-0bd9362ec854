/* Capstone Disassembly Engine */
/* By <PERSON><PERSON><PERSON>, 2018 */

{ 0, 0, 0 }, // STOP
{ 2, 1, 3 }, // ADD
{ 2, 1, 5 }, // MUL
{ 2, 1, 3 }, // SUB
{ 2, 1, 5 }, // DIV
{ 2, 1, 5 }, // SDIV
{ 2, 1, 5 }, // MOD
{ 2, 1, 5 }, // SMOD
{ 3, 1, 8 }, // ADDMOD
{ 3, 1, 8 }, // MULMOD
{ 2, 1, 10 }, // EXP
{ 2, 1, 5 }, // SIGNEXTEND
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 2, 1, 3 }, // LT
{ 2, 1, 3 }, // GT
{ 2, 1, 3 }, // SLT
{ 2, 1, 3 }, // SGT
{ 2, 1, 3 }, // EQ
{ 1, 1, 3 }, // ISZERO
{ 2, 1, 3 }, // AND
{ 2, 1, 3 }, // OR
{ 2, 1, 3 }, // XOR
{ 1, 1, 3 }, // NOT
{ 2, 1, 3 }, // BYTE
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 2, 1, 30 }, // SHA3
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 1, 2 }, // ADDRESS
{ 1, 1, 20 }, // BALANCE
{ 0, 1, 2 }, // ORIGIN
{ 0, 1, 2 }, // CALLER
{ 0, 1, 2 }, // CALLVALUE
{ 1, 1, 3 }, // CALLDATALOAD
{ 0, 1, 2 }, // CALLDATASIZE
{ 3, 0, 3 }, // CALLDATACOPY
{ 0, 1, 2 }, // CODESIZE
{ 3, 0, 3 }, // CODECOPY
{ 0, 1, 2 }, // GASPRICE
{ 1, 1, 20 }, // EXTCODESIZE
{ 4, 0, 20 }, // EXTCODECOPY
{ 0, 1, 2 }, // RETURNDATASIZE
{ 3, 0, 3 }, // RETURNDATACOPY
{ 0, 0, 0xffffffff }, // unused
{ 1, 1, 20 }, // BLOCKHASH
{ 0, 1, 2 }, // COINBASE
{ 0, 1, 2 }, // TIMESTAMP
{ 0, 1, 2 }, // NUMBER
{ 0, 1, 2 }, // DIFFICULTY
{ 0, 1, 2 }, // GASLIMIT
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 1, 0, 2 }, // POP
{ 1, 1, 3 }, // MLOAD
{ 2, 0, 3 }, // MSTORE
{ 2, 0, 3 }, // MSTORE8
{ 1, 1, 50 }, // SLOAD
{ 2, 0, 0 }, // SSTORE
{ 1, 0, 8 }, // JUMP
{ 2, 0, 10 }, // JUMPI
{ 0, 1, 2 }, // GETPC
{ 0, 1, 2 }, // MSIZE
{ 0, 1, 2 }, // GAS
{ 0, 0, 1 }, // JUMPDEST
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 1, 3 }, // PUSH1
{ 0, 1, 3 }, // PUSH2
{ 0, 1, 3 }, // PUSH3
{ 0, 1, 3 }, // PUSH4
{ 0, 1, 3 }, // PUSH5
{ 0, 1, 3 }, // PUSH6
{ 0, 1, 3 }, // PUSH7
{ 0, 1, 3 }, // PUSH8
{ 0, 1, 3 }, // PUSH9
{ 0, 1, 3 }, // PUSH10
{ 0, 1, 3 }, // PUSH11
{ 0, 1, 3 }, // PUSH12
{ 0, 1, 3 }, // PUSH13
{ 0, 1, 3 }, // PUSH14
{ 0, 1, 3 }, // PUSH15
{ 0, 1, 3 }, // PUSH16
{ 0, 1, 3 }, // PUSH17
{ 0, 1, 3 }, // PUSH18
{ 0, 1, 3 }, // PUSH19
{ 0, 1, 3 }, // PUSH20
{ 0, 1, 3 }, // PUSH21
{ 0, 1, 3 }, // PUSH22
{ 0, 1, 3 }, // PUSH23
{ 0, 1, 3 }, // PUSH24
{ 0, 1, 3 }, // PUSH25
{ 0, 1, 3 }, // PUSH26
{ 0, 1, 3 }, // PUSH27
{ 0, 1, 3 }, // PUSH28
{ 0, 1, 3 }, // PUSH29
{ 0, 1, 3 }, // PUSH30
{ 0, 1, 3 }, // PUSH31
{ 0, 1, 3 }, // PUSH32
{ 1, 2, 3 }, // DUP1
{ 2, 3, 3 }, // DUP2
{ 3, 4, 3 }, // DUP3
{ 4, 5, 3 }, // DUP4
{ 5, 6, 3 }, // DUP5
{ 6, 7, 3 }, // DUP6
{ 7, 8, 3 }, // DUP7
{ 8, 9, 3 }, // DUP8
{ 9, 10, 3 }, // DUP9
{ 10, 11, 3 }, // DUP10
{ 11, 12, 3 }, // DUP11
{ 12, 13, 3 }, // DUP12
{ 13, 14, 3 }, // DUP13
{ 14, 15, 3 }, // DUP14
{ 15, 16, 3 }, // DUP15
{ 16, 17, 3 }, // DUP16
{ 2, 2, 3 }, // SWAP1
{ 3, 3, 3 }, // SWAP2
{ 4, 4, 3 }, // SWAP3
{ 5, 5, 3 }, // SWAP4
{ 6, 6, 3 }, // SWAP5
{ 7, 7, 3 }, // SWAP6
{ 8, 8, 3 }, // SWAP7
{ 9, 9, 3 }, // SWAP8
{ 10, 10, 3 }, // SWAP9
{ 11, 11, 3 }, // SWAP10
{ 12, 12, 3 }, // SWAP11
{ 13, 13, 3 }, // SWAP12
{ 14, 14, 3 }, // SWAP13
{ 15, 15, 3 }, // SWAP14
{ 16, 16, 3 }, // SWAP15
{ 17, 17, 3 }, // SWAP16
{ 2, 0, 375 }, // LOG0
{ 3, 0, 750 }, // LOG1
{ 4, 0, 1125 }, // LOG2
{ 5, 0, 1500 }, // LOG3
{ 6, 0, 1875 }, // LOG4
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 3, 1, 32000 }, // CREATE
{ 7, 1, 40 }, // CALL
{ 7, 1, 40 }, // CALLCODE
{ 2, 0, 0 }, // RETURN
{ 6, 1, 40 }, // DELEGATECALL
{ 7, 1, 40 }, // CALLBLACKBOX
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 6, 1, 40 }, // STATICCALL
{ 0, 0, 0xffffffff }, // unused
{ 0, 0, 0xffffffff }, // unused
{ 2, 0, 0 }, // REVERT
{ 0, 0, 0xffffffff }, // unused
{ 1, 0, 0 }, // SUICIDE
