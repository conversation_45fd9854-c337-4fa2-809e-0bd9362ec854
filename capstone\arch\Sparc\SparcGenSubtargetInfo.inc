/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Subtarget Enumeration Source Fragment                                       *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine, http://www.capstone-engine.org */
/* By <PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2013-2015 */


#ifdef GET_SUBTARGETINFO_ENUM
#undef GET_SUBTARGETINFO_ENUM

enum {
  Sparc_FeatureHardQuad =  1ULL << 0,
  Sparc_FeatureV8Deprecated =  1ULL << 1,
  Sparc_FeatureV9 =  1ULL << 2,
  Sparc_FeatureVIS =  1ULL << 3,
  Sparc_FeatureVIS2 =  1ULL << 4,
  Sparc_FeatureVIS3 =  1ULL << 5,
  Sparc_UsePopc =  1ULL << 6
};

#endif // GET_SUBTARGETINFO_ENUM

