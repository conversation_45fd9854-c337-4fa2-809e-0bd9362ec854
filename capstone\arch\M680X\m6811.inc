
// Additional instructions only supported on M68HC11
static const inst_pageX g_m6811_inst_overlay_table[] = {
	{ 0x00, M680X_INS_TEST, inh_hid, inh_hid },
	{ 0x02, M680X_INS_IDIV, inh_hid, inh_hid },
	{ 0x03, M680X_INS_FDIV, inh_hid, inh_hid },
	{ 0x12, M680X_INS_BRSET, dir_hid, imm8rel_hid },
	{ 0x13, M680X_INS_BRCLR, dir_hid, imm8rel_hid },
	{ 0x14, M680X_INS_BSET, dir_hid, imm8_hid },
	{ 0x15, M680X_INS_BCLR, dir_hid, imm8_hid },
	{ 0x1c, M680X_INS_BSET, idxX_hid, imm8_hid },
	{ 0x1d, M680X_INS_BCLR, idxX_hid, imm8_hid },
	{ 0x1e, M680X_INS_BRSET, idxX_hid, imm8rel_hid },
	{ 0x1f, M680X_INS_BRCLR, idxX_hid, imm8rel_hid },
	{ 0x8f, M680X_INS_XGDX, inh_hid, inh_hid },
	{ 0xcf, M680X_INS_STOP, inh_hid, inh_hid },
};

// M68HC11 PAGE2 instructions
static const inst_pageX g_m6811_inst_page2_table[] = {
	{ 0x08, M680X_INS_INY, inh_hid, inh_hid },
	{ 0x09, M680X_INS_DEY, inh_hid, inh_hid },
	{ 0x1c, M680X_INS_BSET, idxY_hid, imm8_hid },
	{ 0x1d, M680X_INS_BCLR, idxY_hid, imm8_hid },
	{ 0x1e, M680X_INS_BRSET, idxY_hid, imm8rel_hid },
	{ 0x1f, M680X_INS_BRCLR, idxY_hid, imm8rel_hid },
	{ 0x30, M680X_INS_TSY, inh_hid, inh_hid },
	{ 0x35, M680X_INS_TYS, inh_hid, inh_hid },
	{ 0x38, M680X_INS_PULY, inh_hid, inh_hid },
	{ 0x3a, M680X_INS_ABY, inh_hid, inh_hid },
	{ 0x3c, M680X_INS_PSHY, inh_hid, inh_hid },
	{ 0x60, M680X_INS_NEG, idxY_hid, inh_hid },
	{ 0x63, M680X_INS_COM, idxY_hid, inh_hid },
	{ 0x64, M680X_INS_LSR, idxY_hid, inh_hid },
	{ 0x66, M680X_INS_ROR, idxY_hid, inh_hid },
	{ 0x67, M680X_INS_ASR, idxY_hid, inh_hid },
	{ 0x68, M680X_INS_ASL, idxY_hid, inh_hid },
	{ 0x69, M680X_INS_ROL, idxY_hid, inh_hid },
	{ 0x6a, M680X_INS_DEC, idxY_hid, inh_hid },
	{ 0x6c, M680X_INS_INC, idxY_hid, inh_hid },
	{ 0x6d, M680X_INS_TST, idxY_hid, inh_hid },
	{ 0x6e, M680X_INS_JMP, idxY_hid, inh_hid },
	{ 0x6f, M680X_INS_CLR, idxY_hid, inh_hid },
	{ 0x8c, M680X_INS_CPY, imm16_hid, inh_hid },
	{ 0x8f, M680X_INS_XGDY, inh_hid, inh_hid },
	{ 0x9c, M680X_INS_CPY, dir_hid, inh_hid },
	{ 0xa0, M680X_INS_SUBA, idxY_hid, inh_hid },
	{ 0xa1, M680X_INS_CMPA, idxY_hid, inh_hid },
	{ 0xa2, M680X_INS_SBCA, idxY_hid, inh_hid },
	{ 0xa3, M680X_INS_SUBD, idxY_hid, inh_hid },
	{ 0xa4, M680X_INS_ANDA, idxY_hid, inh_hid },
	{ 0xa5, M680X_INS_BITA, idxY_hid, inh_hid },
	{ 0xa6, M680X_INS_LDAA, idxY_hid, inh_hid },
	{ 0xa7, M680X_INS_STAA, idxY_hid, inh_hid },
	{ 0xa8, M680X_INS_EORA, idxY_hid, inh_hid },
	{ 0xa9, M680X_INS_ADCA, idxY_hid, inh_hid },
	{ 0xaa, M680X_INS_ORAA, idxY_hid, inh_hid },
	{ 0xab, M680X_INS_ADDA, idxY_hid, inh_hid },
	{ 0xac, M680X_INS_CPY, idxY_hid, inh_hid },
	{ 0xad, M680X_INS_JSR, idxY_hid, inh_hid },
	{ 0xae, M680X_INS_LDS, idxY_hid, inh_hid },
	{ 0xaf, M680X_INS_STS, idxY_hid, inh_hid },
	{ 0xbc, M680X_INS_CPY, ext_hid, inh_hid },
	{ 0xce, M680X_INS_LDY, imm16_hid, inh_hid },
	{ 0xde, M680X_INS_LDY, dir_hid, inh_hid },
	{ 0xdf, M680X_INS_STY, dir_hid, inh_hid },
	{ 0xe0, M680X_INS_SUBB, idxY_hid, inh_hid },
	{ 0xe1, M680X_INS_CMPB, idxY_hid, inh_hid },
	{ 0xe2, M680X_INS_SBCB, idxY_hid, inh_hid },
	{ 0xe3, M680X_INS_ADDD, idxY_hid, inh_hid },
	{ 0xe4, M680X_INS_ANDB, idxY_hid, inh_hid },
	{ 0xe5, M680X_INS_BITB, idxY_hid, inh_hid },
	{ 0xe6, M680X_INS_LDAB, idxY_hid, inh_hid },
	{ 0xe7, M680X_INS_STAB, idxY_hid, inh_hid },
	{ 0xe8, M680X_INS_EORB, idxY_hid, inh_hid },
	{ 0xe9, M680X_INS_ADCB, idxY_hid, inh_hid },
	{ 0xea, M680X_INS_ORAB, idxY_hid, inh_hid },
	{ 0xeb, M680X_INS_ADDB, idxY_hid, inh_hid },
	{ 0xec, M680X_INS_LDD, idxY_hid, inh_hid },
	{ 0xed, M680X_INS_STD, idxY_hid, inh_hid },
	{ 0xee, M680X_INS_LDY, idxY_hid, inh_hid },
	{ 0xef, M680X_INS_STY, idxY_hid, inh_hid },
	{ 0xfe, M680X_INS_LDY, ext_hid, inh_hid },
	{ 0xff, M680X_INS_STY, ext_hid, inh_hid },
};

// M68HC11 PAGE3 instructions
static const inst_pageX g_m6811_inst_page3_table[] = {
	{ 0x83, M680X_INS_CPD, imm16_hid, inh_hid },
	{ 0x93, M680X_INS_CPD, dir_hid, inh_hid },
	{ 0xa3, M680X_INS_CPD, idxX_hid, inh_hid },
	{ 0xac, M680X_INS_CPY, idxX_hid, inh_hid },
	{ 0xb3, M680X_INS_CPD, ext_hid, inh_hid },
	{ 0xee, M680X_INS_LDY, idxX_hid, inh_hid },
	{ 0xef, M680X_INS_STY, idxX_hid, inh_hid },
};

// M68HC11 PAGE4 instructions
static const inst_pageX g_m6811_inst_page4_table[] = {
	{ 0xa3, M680X_INS_CPD, idxY_hid, inh_hid },
	{ 0xac, M680X_INS_CPX, idxY_hid, inh_hid },
	{ 0xee, M680X_INS_LDX, idxY_hid, inh_hid },
	{ 0xef, M680X_INS_STX, idxY_hid, inh_hid },
};

