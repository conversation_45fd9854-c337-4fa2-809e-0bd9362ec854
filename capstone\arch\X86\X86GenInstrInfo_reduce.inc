/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Target Instruction Enum Values                                              *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine, http://www.capstone-engine.org */
/* By <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2015 */


#ifdef GET_INSTRINFO_ENUM
#undef GET_INSTRINFO_ENUM

enum {
    X86_PHI	= 0,
    X86_INLINEASM	= 1,
    X86_CFI_INSTRUCTION	= 2,
    X86_EH_LABEL	= 3,
    X86_GC_LABEL	= 4,
    X86_KILL	= 5,
    X86_EXTRACT_SUBREG	= 6,
    X86_INSERT_SUBREG	= 7,
    X86_IMPLICIT_DEF	= 8,
    X86_SUBREG_TO_REG	= 9,
    X86_COPY_TO_REGCLASS	= 10,
    X86_DBG_VALUE	= 11,
    X86_REG_SEQUENCE	= 12,
    X86_COPY	= 13,
    X86_BUNDLE	= 14,
    X86_LIFETIME_START	= 15,
    X86_LIFETIME_END	= 16,
    X86_STACKMAP	= 17,
    X86_PATCHPOINT	= 18,
    X86_LOAD_STACK_GUARD	= 19,
    X86_STATEPOINT	= 20,
    X86_FRAME_ALLOC	= 21,
    X86_AAA	= 22,
    X86_AAD8i8	= 23,
    X86_AAM8i8	= 24,
    X86_AAS	= 25,
    X86_ACQUIRE_MOV16rm	= 26,
    X86_ACQUIRE_MOV32rm	= 27,
    X86_ACQUIRE_MOV64rm	= 28,
    X86_ACQUIRE_MOV8rm	= 29,
    X86_ADC16i16	= 30,
    X86_ADC16mi	= 31,
    X86_ADC16mi8	= 32,
    X86_ADC16mr	= 33,
    X86_ADC16ri	= 34,
    X86_ADC16ri8	= 35,
    X86_ADC16rm	= 36,
    X86_ADC16rr	= 37,
    X86_ADC16rr_REV	= 38,
    X86_ADC32i32	= 39,
    X86_ADC32mi	= 40,
    X86_ADC32mi8	= 41,
    X86_ADC32mr	= 42,
    X86_ADC32ri	= 43,
    X86_ADC32ri8	= 44,
    X86_ADC32rm	= 45,
    X86_ADC32rr	= 46,
    X86_ADC32rr_REV	= 47,
    X86_ADC64i32	= 48,
    X86_ADC64mi32	= 49,
    X86_ADC64mi8	= 50,
    X86_ADC64mr	= 51,
    X86_ADC64ri32	= 52,
    X86_ADC64ri8	= 53,
    X86_ADC64rm	= 54,
    X86_ADC64rr	= 55,
    X86_ADC64rr_REV	= 56,
    X86_ADC8i8	= 57,
    X86_ADC8mi	= 58,
    X86_ADC8mi8	= 59,
    X86_ADC8mr	= 60,
    X86_ADC8ri	= 61,
    X86_ADC8ri8	= 62,
    X86_ADC8rm	= 63,
    X86_ADC8rr	= 64,
    X86_ADC8rr_REV	= 65,
    X86_ADCX32rm	= 66,
    X86_ADCX32rr	= 67,
    X86_ADCX64rm	= 68,
    X86_ADCX64rr	= 69,
    X86_ADD16i16	= 70,
    X86_ADD16mi	= 71,
    X86_ADD16mi8	= 72,
    X86_ADD16mr	= 73,
    X86_ADD16ri	= 74,
    X86_ADD16ri8	= 75,
    X86_ADD16ri8_DB	= 76,
    X86_ADD16ri_DB	= 77,
    X86_ADD16rm	= 78,
    X86_ADD16rr	= 79,
    X86_ADD16rr_DB	= 80,
    X86_ADD16rr_REV	= 81,
    X86_ADD32i32	= 82,
    X86_ADD32mi	= 83,
    X86_ADD32mi8	= 84,
    X86_ADD32mr	= 85,
    X86_ADD32ri	= 86,
    X86_ADD32ri8	= 87,
    X86_ADD32ri8_DB	= 88,
    X86_ADD32ri_DB	= 89,
    X86_ADD32rm	= 90,
    X86_ADD32rr	= 91,
    X86_ADD32rr_DB	= 92,
    X86_ADD32rr_REV	= 93,
    X86_ADD64i32	= 94,
    X86_ADD64mi32	= 95,
    X86_ADD64mi8	= 96,
    X86_ADD64mr	= 97,
    X86_ADD64ri32	= 98,
    X86_ADD64ri32_DB	= 99,
    X86_ADD64ri8	= 100,
    X86_ADD64ri8_DB	= 101,
    X86_ADD64rm	= 102,
    X86_ADD64rr	= 103,
    X86_ADD64rr_DB	= 104,
    X86_ADD64rr_REV	= 105,
    X86_ADD8i8	= 106,
    X86_ADD8mi	= 107,
    X86_ADD8mi8	= 108,
    X86_ADD8mr	= 109,
    X86_ADD8ri	= 110,
    X86_ADD8ri8	= 111,
    X86_ADD8rm	= 112,
    X86_ADD8rr	= 113,
    X86_ADD8rr_REV	= 114,
    X86_ADJCALLSTACKDOWN32	= 115,
    X86_ADJCALLSTACKDOWN64	= 116,
    X86_ADJCALLSTACKUP32	= 117,
    X86_ADJCALLSTACKUP64	= 118,
    X86_ADOX32rm	= 119,
    X86_ADOX32rr	= 120,
    X86_ADOX64rm	= 121,
    X86_ADOX64rr	= 122,
    X86_AND16i16	= 123,
    X86_AND16mi	= 124,
    X86_AND16mi8	= 125,
    X86_AND16mr	= 126,
    X86_AND16ri	= 127,
    X86_AND16ri8	= 128,
    X86_AND16rm	= 129,
    X86_AND16rr	= 130,
    X86_AND16rr_REV	= 131,
    X86_AND32i32	= 132,
    X86_AND32mi	= 133,
    X86_AND32mi8	= 134,
    X86_AND32mr	= 135,
    X86_AND32ri	= 136,
    X86_AND32ri8	= 137,
    X86_AND32rm	= 138,
    X86_AND32rr	= 139,
    X86_AND32rr_REV	= 140,
    X86_AND64i32	= 141,
    X86_AND64mi32	= 142,
    X86_AND64mi8	= 143,
    X86_AND64mr	= 144,
    X86_AND64ri32	= 145,
    X86_AND64ri8	= 146,
    X86_AND64rm	= 147,
    X86_AND64rr	= 148,
    X86_AND64rr_REV	= 149,
    X86_AND8i8	= 150,
    X86_AND8mi	= 151,
    X86_AND8mi8	= 152,
    X86_AND8mr	= 153,
    X86_AND8ri	= 154,
    X86_AND8ri8	= 155,
    X86_AND8rm	= 156,
    X86_AND8rr	= 157,
    X86_AND8rr_REV	= 158,
    X86_ANDN32rm	= 159,
    X86_ANDN32rr	= 160,
    X86_ANDN64rm	= 161,
    X86_ANDN64rr	= 162,
    X86_ARPL16mr	= 163,
    X86_ARPL16rr	= 164,
    X86_BEXTR32rm	= 165,
    X86_BEXTR32rr	= 166,
    X86_BEXTR64rm	= 167,
    X86_BEXTR64rr	= 168,
    X86_BEXTRI32mi	= 169,
    X86_BEXTRI32ri	= 170,
    X86_BEXTRI64mi	= 171,
    X86_BEXTRI64ri	= 172,
    X86_BLCFILL32rm	= 173,
    X86_BLCFILL32rr	= 174,
    X86_BLCFILL64rm	= 175,
    X86_BLCFILL64rr	= 176,
    X86_BLCI32rm	= 177,
    X86_BLCI32rr	= 178,
    X86_BLCI64rm	= 179,
    X86_BLCI64rr	= 180,
    X86_BLCIC32rm	= 181,
    X86_BLCIC32rr	= 182,
    X86_BLCIC64rm	= 183,
    X86_BLCIC64rr	= 184,
    X86_BLCMSK32rm	= 185,
    X86_BLCMSK32rr	= 186,
    X86_BLCMSK64rm	= 187,
    X86_BLCMSK64rr	= 188,
    X86_BLCS32rm	= 189,
    X86_BLCS32rr	= 190,
    X86_BLCS64rm	= 191,
    X86_BLCS64rr	= 192,
    X86_BLSFILL32rm	= 193,
    X86_BLSFILL32rr	= 194,
    X86_BLSFILL64rm	= 195,
    X86_BLSFILL64rr	= 196,
    X86_BLSI32rm	= 197,
    X86_BLSI32rr	= 198,
    X86_BLSI64rm	= 199,
    X86_BLSI64rr	= 200,
    X86_BLSIC32rm	= 201,
    X86_BLSIC32rr	= 202,
    X86_BLSIC64rm	= 203,
    X86_BLSIC64rr	= 204,
    X86_BLSMSK32rm	= 205,
    X86_BLSMSK32rr	= 206,
    X86_BLSMSK64rm	= 207,
    X86_BLSMSK64rr	= 208,
    X86_BLSR32rm	= 209,
    X86_BLSR32rr	= 210,
    X86_BLSR64rm	= 211,
    X86_BLSR64rr	= 212,
    X86_BOUNDS16rm	= 213,
    X86_BOUNDS32rm	= 214,
    X86_BSF16rm	= 215,
    X86_BSF16rr	= 216,
    X86_BSF32rm	= 217,
    X86_BSF32rr	= 218,
    X86_BSF64rm	= 219,
    X86_BSF64rr	= 220,
    X86_BSR16rm	= 221,
    X86_BSR16rr	= 222,
    X86_BSR32rm	= 223,
    X86_BSR32rr	= 224,
    X86_BSR64rm	= 225,
    X86_BSR64rr	= 226,
    X86_BSWAP32r	= 227,
    X86_BSWAP64r	= 228,
    X86_BT16mi8	= 229,
    X86_BT16mr	= 230,
    X86_BT16ri8	= 231,
    X86_BT16rr	= 232,
    X86_BT32mi8	= 233,
    X86_BT32mr	= 234,
    X86_BT32ri8	= 235,
    X86_BT32rr	= 236,
    X86_BT64mi8	= 237,
    X86_BT64mr	= 238,
    X86_BT64ri8	= 239,
    X86_BT64rr	= 240,
    X86_BTC16mi8	= 241,
    X86_BTC16mr	= 242,
    X86_BTC16ri8	= 243,
    X86_BTC16rr	= 244,
    X86_BTC32mi8	= 245,
    X86_BTC32mr	= 246,
    X86_BTC32ri8	= 247,
    X86_BTC32rr	= 248,
    X86_BTC64mi8	= 249,
    X86_BTC64mr	= 250,
    X86_BTC64ri8	= 251,
    X86_BTC64rr	= 252,
    X86_BTR16mi8	= 253,
    X86_BTR16mr	= 254,
    X86_BTR16ri8	= 255,
    X86_BTR16rr	= 256,
    X86_BTR32mi8	= 257,
    X86_BTR32mr	= 258,
    X86_BTR32ri8	= 259,
    X86_BTR32rr	= 260,
    X86_BTR64mi8	= 261,
    X86_BTR64mr	= 262,
    X86_BTR64ri8	= 263,
    X86_BTR64rr	= 264,
    X86_BTS16mi8	= 265,
    X86_BTS16mr	= 266,
    X86_BTS16ri8	= 267,
    X86_BTS16rr	= 268,
    X86_BTS32mi8	= 269,
    X86_BTS32mr	= 270,
    X86_BTS32ri8	= 271,
    X86_BTS32rr	= 272,
    X86_BTS64mi8	= 273,
    X86_BTS64mr	= 274,
    X86_BTS64ri8	= 275,
    X86_BTS64rr	= 276,
    X86_BZHI32rm	= 277,
    X86_BZHI32rr	= 278,
    X86_BZHI64rm	= 279,
    X86_BZHI64rr	= 280,
    X86_CALL16m	= 281,
    X86_CALL16r	= 282,
    X86_CALL32m	= 283,
    X86_CALL32r	= 284,
    X86_CALL64m	= 285,
    X86_CALL64pcrel32	= 286,
    X86_CALL64r	= 287,
    X86_CALLpcrel16	= 288,
    X86_CALLpcrel32	= 289,
    X86_CBW	= 290,
    X86_CDQ	= 291,
    X86_CDQE	= 292,
    X86_CLAC	= 293,
    X86_CLC	= 294,
    X86_CLD	= 295,
    X86_CLFLUSHOPT	= 296,
    X86_CLGI	= 297,
    X86_CLI	= 298,
    X86_CLTS	= 299,
    X86_CLWB	= 300,
    X86_CMC	= 301,
    X86_CMOVA16rm	= 302,
    X86_CMOVA16rr	= 303,
    X86_CMOVA32rm	= 304,
    X86_CMOVA32rr	= 305,
    X86_CMOVA64rm	= 306,
    X86_CMOVA64rr	= 307,
    X86_CMOVAE16rm	= 308,
    X86_CMOVAE16rr	= 309,
    X86_CMOVAE32rm	= 310,
    X86_CMOVAE32rr	= 311,
    X86_CMOVAE64rm	= 312,
    X86_CMOVAE64rr	= 313,
    X86_CMOVB16rm	= 314,
    X86_CMOVB16rr	= 315,
    X86_CMOVB32rm	= 316,
    X86_CMOVB32rr	= 317,
    X86_CMOVB64rm	= 318,
    X86_CMOVB64rr	= 319,
    X86_CMOVBE16rm	= 320,
    X86_CMOVBE16rr	= 321,
    X86_CMOVBE32rm	= 322,
    X86_CMOVBE32rr	= 323,
    X86_CMOVBE64rm	= 324,
    X86_CMOVBE64rr	= 325,
    X86_CMOVE16rm	= 326,
    X86_CMOVE16rr	= 327,
    X86_CMOVE32rm	= 328,
    X86_CMOVE32rr	= 329,
    X86_CMOVE64rm	= 330,
    X86_CMOVE64rr	= 331,
    X86_CMOVG16rm	= 332,
    X86_CMOVG16rr	= 333,
    X86_CMOVG32rm	= 334,
    X86_CMOVG32rr	= 335,
    X86_CMOVG64rm	= 336,
    X86_CMOVG64rr	= 337,
    X86_CMOVGE16rm	= 338,
    X86_CMOVGE16rr	= 339,
    X86_CMOVGE32rm	= 340,
    X86_CMOVGE32rr	= 341,
    X86_CMOVGE64rm	= 342,
    X86_CMOVGE64rr	= 343,
    X86_CMOVL16rm	= 344,
    X86_CMOVL16rr	= 345,
    X86_CMOVL32rm	= 346,
    X86_CMOVL32rr	= 347,
    X86_CMOVL64rm	= 348,
    X86_CMOVL64rr	= 349,
    X86_CMOVLE16rm	= 350,
    X86_CMOVLE16rr	= 351,
    X86_CMOVLE32rm	= 352,
    X86_CMOVLE32rr	= 353,
    X86_CMOVLE64rm	= 354,
    X86_CMOVLE64rr	= 355,
    X86_CMOVNE16rm	= 356,
    X86_CMOVNE16rr	= 357,
    X86_CMOVNE32rm	= 358,
    X86_CMOVNE32rr	= 359,
    X86_CMOVNE64rm	= 360,
    X86_CMOVNE64rr	= 361,
    X86_CMOVNO16rm	= 362,
    X86_CMOVNO16rr	= 363,
    X86_CMOVNO32rm	= 364,
    X86_CMOVNO32rr	= 365,
    X86_CMOVNO64rm	= 366,
    X86_CMOVNO64rr	= 367,
    X86_CMOVNP16rm	= 368,
    X86_CMOVNP16rr	= 369,
    X86_CMOVNP32rm	= 370,
    X86_CMOVNP32rr	= 371,
    X86_CMOVNP64rm	= 372,
    X86_CMOVNP64rr	= 373,
    X86_CMOVNS16rm	= 374,
    X86_CMOVNS16rr	= 375,
    X86_CMOVNS32rm	= 376,
    X86_CMOVNS32rr	= 377,
    X86_CMOVNS64rm	= 378,
    X86_CMOVNS64rr	= 379,
    X86_CMOVO16rm	= 380,
    X86_CMOVO16rr	= 381,
    X86_CMOVO32rm	= 382,
    X86_CMOVO32rr	= 383,
    X86_CMOVO64rm	= 384,
    X86_CMOVO64rr	= 385,
    X86_CMOVP16rm	= 386,
    X86_CMOVP16rr	= 387,
    X86_CMOVP32rm	= 388,
    X86_CMOVP32rr	= 389,
    X86_CMOVP64rm	= 390,
    X86_CMOVP64rr	= 391,
    X86_CMOVS16rm	= 392,
    X86_CMOVS16rr	= 393,
    X86_CMOVS32rm	= 394,
    X86_CMOVS32rr	= 395,
    X86_CMOVS64rm	= 396,
    X86_CMOVS64rr	= 397,
    X86_CMOV_FR32	= 398,
    X86_CMOV_FR64	= 399,
    X86_CMOV_GR16	= 400,
    X86_CMOV_GR32	= 401,
    X86_CMOV_GR8	= 402,
    X86_CMOV_RFP32	= 403,
    X86_CMOV_RFP64	= 404,
    X86_CMOV_RFP80	= 405,
    X86_CMOV_V16F32	= 406,
    X86_CMOV_V2F64	= 407,
    X86_CMOV_V2I64	= 408,
    X86_CMOV_V4F32	= 409,
    X86_CMOV_V4F64	= 410,
    X86_CMOV_V4I64	= 411,
    X86_CMOV_V8F32	= 412,
    X86_CMOV_V8F64	= 413,
    X86_CMOV_V8I64	= 414,
    X86_CMP16i16	= 415,
    X86_CMP16mi	= 416,
    X86_CMP16mi8	= 417,
    X86_CMP16mr	= 418,
    X86_CMP16ri	= 419,
    X86_CMP16ri8	= 420,
    X86_CMP16rm	= 421,
    X86_CMP16rr	= 422,
    X86_CMP16rr_REV	= 423,
    X86_CMP32i32	= 424,
    X86_CMP32mi	= 425,
    X86_CMP32mi8	= 426,
    X86_CMP32mr	= 427,
    X86_CMP32ri	= 428,
    X86_CMP32ri8	= 429,
    X86_CMP32rm	= 430,
    X86_CMP32rr	= 431,
    X86_CMP32rr_REV	= 432,
    X86_CMP64i32	= 433,
    X86_CMP64mi32	= 434,
    X86_CMP64mi8	= 435,
    X86_CMP64mr	= 436,
    X86_CMP64ri32	= 437,
    X86_CMP64ri8	= 438,
    X86_CMP64rm	= 439,
    X86_CMP64rr	= 440,
    X86_CMP64rr_REV	= 441,
    X86_CMP8i8	= 442,
    X86_CMP8mi	= 443,
    X86_CMP8mi8	= 444,
    X86_CMP8mr	= 445,
    X86_CMP8ri	= 446,
    X86_CMP8ri8	= 447,
    X86_CMP8rm	= 448,
    X86_CMP8rr	= 449,
    X86_CMP8rr_REV	= 450,
    X86_CMPSB	= 451,
    X86_CMPSL	= 452,
    X86_CMPSQ	= 453,
    X86_CMPSW	= 454,
    X86_CMPXCHG16B	= 455,
    X86_CMPXCHG16rm	= 456,
    X86_CMPXCHG16rr	= 457,
    X86_CMPXCHG32rm	= 458,
    X86_CMPXCHG32rr	= 459,
    X86_CMPXCHG64rm	= 460,
    X86_CMPXCHG64rr	= 461,
    X86_CMPXCHG8B	= 462,
    X86_CMPXCHG8rm	= 463,
    X86_CMPXCHG8rr	= 464,
    X86_CPUID	= 465,
    X86_CQO	= 466,
    X86_CWD	= 467,
    X86_CWDE	= 468,
    X86_DAA	= 469,
    X86_DAS	= 470,
    X86_DATA16_PREFIX	= 471,
    X86_DEC16m	= 472,
    X86_DEC16r	= 473,
    X86_DEC16r_alt	= 474,
    X86_DEC32m	= 475,
    X86_DEC32r	= 476,
    X86_DEC32r_alt	= 477,
    X86_DEC64m	= 478,
    X86_DEC64r	= 479,
    X86_DEC8m	= 480,
    X86_DEC8r	= 481,
    X86_DIV16m	= 482,
    X86_DIV16r	= 483,
    X86_DIV32m	= 484,
    X86_DIV32r	= 485,
    X86_DIV64m	= 486,
    X86_DIV64r	= 487,
    X86_DIV8m	= 488,
    X86_DIV8r	= 489,
    X86_EH_RETURN	= 490,
    X86_EH_RETURN64	= 491,
    X86_EH_SjLj_LongJmp32	= 492,
    X86_EH_SjLj_LongJmp64	= 493,
    X86_EH_SjLj_SetJmp32	= 494,
    X86_EH_SjLj_SetJmp64	= 495,
    X86_EH_SjLj_Setup	= 496,
    X86_ENTER	= 497,
    X86_FARCALL16i	= 498,
    X86_FARCALL16m	= 499,
    X86_FARCALL32i	= 500,
    X86_FARCALL32m	= 501,
    X86_FARCALL64	= 502,
    X86_FARJMP16i	= 503,
    X86_FARJMP16m	= 504,
    X86_FARJMP32i	= 505,
    X86_FARJMP32m	= 506,
    X86_FARJMP64	= 507,
    X86_FSETPM	= 508,
    X86_GETSEC	= 509,
    X86_HLT	= 510,
    X86_IDIV16m	= 511,
    X86_IDIV16r	= 512,
    X86_IDIV32m	= 513,
    X86_IDIV32r	= 514,
    X86_IDIV64m	= 515,
    X86_IDIV64r	= 516,
    X86_IDIV8m	= 517,
    X86_IDIV8r	= 518,
    X86_IMUL16m	= 519,
    X86_IMUL16r	= 520,
    X86_IMUL16rm	= 521,
    X86_IMUL16rmi	= 522,
    X86_IMUL16rmi8	= 523,
    X86_IMUL16rr	= 524,
    X86_IMUL16rri	= 525,
    X86_IMUL16rri8	= 526,
    X86_IMUL32m	= 527,
    X86_IMUL32r	= 528,
    X86_IMUL32rm	= 529,
    X86_IMUL32rmi	= 530,
    X86_IMUL32rmi8	= 531,
    X86_IMUL32rr	= 532,
    X86_IMUL32rri	= 533,
    X86_IMUL32rri8	= 534,
    X86_IMUL64m	= 535,
    X86_IMUL64r	= 536,
    X86_IMUL64rm	= 537,
    X86_IMUL64rmi32	= 538,
    X86_IMUL64rmi8	= 539,
    X86_IMUL64rr	= 540,
    X86_IMUL64rri32	= 541,
    X86_IMUL64rri8	= 542,
    X86_IMUL8m	= 543,
    X86_IMUL8r	= 544,
    X86_IN16ri	= 545,
    X86_IN16rr	= 546,
    X86_IN32ri	= 547,
    X86_IN32rr	= 548,
    X86_IN8ri	= 549,
    X86_IN8rr	= 550,
    X86_INC16m	= 551,
    X86_INC16r	= 552,
    X86_INC16r_alt	= 553,
    X86_INC32m	= 554,
    X86_INC32r	= 555,
    X86_INC32r_alt	= 556,
    X86_INC64m	= 557,
    X86_INC64r	= 558,
    X86_INC8m	= 559,
    X86_INC8r	= 560,
    X86_INSB	= 561,
    X86_INSL	= 562,
    X86_INSW	= 563,
    X86_INT	= 564,
    X86_INT1	= 565,
    X86_INT3	= 566,
    X86_INTO	= 567,
    X86_INVD	= 568,
    X86_INVEPT32	= 569,
    X86_INVEPT64	= 570,
    X86_INVLPG	= 571,
    X86_INVLPGA32	= 572,
    X86_INVLPGA64	= 573,
    X86_INVPCID32	= 574,
    X86_INVPCID64	= 575,
    X86_INVVPID32	= 576,
    X86_INVVPID64	= 577,
    X86_IRET16	= 578,
    X86_IRET32	= 579,
    X86_IRET64	= 580,
    X86_Int_MemBarrier	= 581,
    X86_JAE_1	= 582,
    X86_JAE_2	= 583,
    X86_JAE_4	= 584,
    X86_JA_1	= 585,
    X86_JA_2	= 586,
    X86_JA_4	= 587,
    X86_JBE_1	= 588,
    X86_JBE_2	= 589,
    X86_JBE_4	= 590,
    X86_JB_1	= 591,
    X86_JB_2	= 592,
    X86_JB_4	= 593,
    X86_JCXZ	= 594,
    X86_JECXZ	= 595,
    X86_JE_1	= 596,
    X86_JE_2	= 597,
    X86_JE_4	= 598,
    X86_JGE_1	= 599,
    X86_JGE_2	= 600,
    X86_JGE_4	= 601,
    X86_JG_1	= 602,
    X86_JG_2	= 603,
    X86_JG_4	= 604,
    X86_JLE_1	= 605,
    X86_JLE_2	= 606,
    X86_JLE_4	= 607,
    X86_JL_1	= 608,
    X86_JL_2	= 609,
    X86_JL_4	= 610,
    X86_JMP16m	= 611,
    X86_JMP16r	= 612,
    X86_JMP32m	= 613,
    X86_JMP32r	= 614,
    X86_JMP64m	= 615,
    X86_JMP64r	= 616,
    X86_JMP_1	= 617,
    X86_JMP_2	= 618,
    X86_JMP_4	= 619,
    X86_JNE_1	= 620,
    X86_JNE_2	= 621,
    X86_JNE_4	= 622,
    X86_JNO_1	= 623,
    X86_JNO_2	= 624,
    X86_JNO_4	= 625,
    X86_JNP_1	= 626,
    X86_JNP_2	= 627,
    X86_JNP_4	= 628,
    X86_JNS_1	= 629,
    X86_JNS_2	= 630,
    X86_JNS_4	= 631,
    X86_JO_1	= 632,
    X86_JO_2	= 633,
    X86_JO_4	= 634,
    X86_JP_1	= 635,
    X86_JP_2	= 636,
    X86_JP_4	= 637,
    X86_JRCXZ	= 638,
    X86_JS_1	= 639,
    X86_JS_2	= 640,
    X86_JS_4	= 641,
    X86_LAHF	= 642,
    X86_LAR16rm	= 643,
    X86_LAR16rr	= 644,
    X86_LAR32rm	= 645,
    X86_LAR32rr	= 646,
    X86_LAR64rm	= 647,
    X86_LAR64rr	= 648,
    X86_LCMPXCHG16	= 649,
    X86_LCMPXCHG16B	= 650,
    X86_LCMPXCHG32	= 651,
    X86_LCMPXCHG64	= 652,
    X86_LCMPXCHG8	= 653,
    X86_LCMPXCHG8B	= 654,
    X86_LDS16rm	= 655,
    X86_LDS32rm	= 656,
    X86_LEA16r	= 657,
    X86_LEA32r	= 658,
    X86_LEA64_32r	= 659,
    X86_LEA64r	= 660,
    X86_LEAVE	= 661,
    X86_LEAVE64	= 662,
    X86_LES16rm	= 663,
    X86_LES32rm	= 664,
    X86_LFS16rm	= 665,
    X86_LFS32rm	= 666,
    X86_LFS64rm	= 667,
    X86_LGDT16m	= 668,
    X86_LGDT32m	= 669,
    X86_LGDT64m	= 670,
    X86_LGS16rm	= 671,
    X86_LGS32rm	= 672,
    X86_LGS64rm	= 673,
    X86_LIDT16m	= 674,
    X86_LIDT32m	= 675,
    X86_LIDT64m	= 676,
    X86_LLDT16m	= 677,
    X86_LLDT16r	= 678,
    X86_LMSW16m	= 679,
    X86_LMSW16r	= 680,
    X86_LOCK_ADD16mi	= 681,
    X86_LOCK_ADD16mi8	= 682,
    X86_LOCK_ADD16mr	= 683,
    X86_LOCK_ADD32mi	= 684,
    X86_LOCK_ADD32mi8	= 685,
    X86_LOCK_ADD32mr	= 686,
    X86_LOCK_ADD64mi32	= 687,
    X86_LOCK_ADD64mi8	= 688,
    X86_LOCK_ADD64mr	= 689,
    X86_LOCK_ADD8mi	= 690,
    X86_LOCK_ADD8mr	= 691,
    X86_LOCK_AND16mi	= 692,
    X86_LOCK_AND16mi8	= 693,
    X86_LOCK_AND16mr	= 694,
    X86_LOCK_AND32mi	= 695,
    X86_LOCK_AND32mi8	= 696,
    X86_LOCK_AND32mr	= 697,
    X86_LOCK_AND64mi32	= 698,
    X86_LOCK_AND64mi8	= 699,
    X86_LOCK_AND64mr	= 700,
    X86_LOCK_AND8mi	= 701,
    X86_LOCK_AND8mr	= 702,
    X86_LOCK_DEC16m	= 703,
    X86_LOCK_DEC32m	= 704,
    X86_LOCK_DEC64m	= 705,
    X86_LOCK_DEC8m	= 706,
    X86_LOCK_INC16m	= 707,
    X86_LOCK_INC32m	= 708,
    X86_LOCK_INC64m	= 709,
    X86_LOCK_INC8m	= 710,
    X86_LOCK_OR16mi	= 711,
    X86_LOCK_OR16mi8	= 712,
    X86_LOCK_OR16mr	= 713,
    X86_LOCK_OR32mi	= 714,
    X86_LOCK_OR32mi8	= 715,
    X86_LOCK_OR32mr	= 716,
    X86_LOCK_OR64mi32	= 717,
    X86_LOCK_OR64mi8	= 718,
    X86_LOCK_OR64mr	= 719,
    X86_LOCK_OR8mi	= 720,
    X86_LOCK_OR8mr	= 721,
    X86_LOCK_PREFIX	= 722,
    X86_LOCK_SUB16mi	= 723,
    X86_LOCK_SUB16mi8	= 724,
    X86_LOCK_SUB16mr	= 725,
    X86_LOCK_SUB32mi	= 726,
    X86_LOCK_SUB32mi8	= 727,
    X86_LOCK_SUB32mr	= 728,
    X86_LOCK_SUB64mi32	= 729,
    X86_LOCK_SUB64mi8	= 730,
    X86_LOCK_SUB64mr	= 731,
    X86_LOCK_SUB8mi	= 732,
    X86_LOCK_SUB8mr	= 733,
    X86_LOCK_XOR16mi	= 734,
    X86_LOCK_XOR16mi8	= 735,
    X86_LOCK_XOR16mr	= 736,
    X86_LOCK_XOR32mi	= 737,
    X86_LOCK_XOR32mi8	= 738,
    X86_LOCK_XOR32mr	= 739,
    X86_LOCK_XOR64mi32	= 740,
    X86_LOCK_XOR64mi8	= 741,
    X86_LOCK_XOR64mr	= 742,
    X86_LOCK_XOR8mi	= 743,
    X86_LOCK_XOR8mr	= 744,
    X86_LODSB	= 745,
    X86_LODSL	= 746,
    X86_LODSQ	= 747,
    X86_LODSW	= 748,
    X86_LOOP	= 749,
    X86_LOOPE	= 750,
    X86_LOOPNE	= 751,
    X86_LRETIL	= 752,
    X86_LRETIQ	= 753,
    X86_LRETIW	= 754,
    X86_LRETL	= 755,
    X86_LRETQ	= 756,
    X86_LRETW	= 757,
    X86_LSL16rm	= 758,
    X86_LSL16rr	= 759,
    X86_LSL32rm	= 760,
    X86_LSL32rr	= 761,
    X86_LSL64rm	= 762,
    X86_LSL64rr	= 763,
    X86_LSS16rm	= 764,
    X86_LSS32rm	= 765,
    X86_LSS64rm	= 766,
    X86_LTRm	= 767,
    X86_LTRr	= 768,
    X86_LXADD16	= 769,
    X86_LXADD32	= 770,
    X86_LXADD64	= 771,
    X86_LXADD8	= 772,
    X86_LZCNT16rm	= 773,
    X86_LZCNT16rr	= 774,
    X86_LZCNT32rm	= 775,
    X86_LZCNT32rr	= 776,
    X86_LZCNT64rm	= 777,
    X86_LZCNT64rr	= 778,
    X86_MONTMUL	= 779,
    X86_MORESTACK_RET	= 780,
    X86_MORESTACK_RET_RESTORE_R10	= 781,
    X86_MOV16ao16	= 782,
    X86_MOV16ao32	= 783,
    X86_MOV16ao64	= 784,
    X86_MOV16mi	= 785,
    X86_MOV16mr	= 786,
    X86_MOV16ms	= 787,
    X86_MOV16o16a	= 788,
    X86_MOV16o32a	= 789,
    X86_MOV16o64a	= 790,
    X86_MOV16ri	= 791,
    X86_MOV16ri_alt	= 792,
    X86_MOV16rm	= 793,
    X86_MOV16rr	= 794,
    X86_MOV16rr_REV	= 795,
    X86_MOV16rs	= 796,
    X86_MOV16sm	= 797,
    X86_MOV16sr	= 798,
    X86_MOV32ao16	= 799,
    X86_MOV32ao32	= 800,
    X86_MOV32ao64	= 801,
    X86_MOV32cr	= 802,
    X86_MOV32dr	= 803,
    X86_MOV32mi	= 804,
    X86_MOV32mr	= 805,
    X86_MOV32ms	= 806,
    X86_MOV32o16a	= 807,
    X86_MOV32o32a	= 808,
    X86_MOV32o64a	= 809,
    X86_MOV32r0	= 810,
    X86_MOV32rc	= 811,
    X86_MOV32rd	= 812,
    X86_MOV32ri	= 813,
    X86_MOV32ri64	= 814,
    X86_MOV32ri_alt	= 815,
    X86_MOV32rm	= 816,
    X86_MOV32rr	= 817,
    X86_MOV32rr_REV	= 818,
    X86_MOV32rs	= 819,
    X86_MOV32sm	= 820,
    X86_MOV32sr	= 821,
    X86_MOV64ao32	= 822,
    X86_MOV64ao64	= 823,
    X86_MOV64cr	= 824,
    X86_MOV64dr	= 825,
    X86_MOV64mi32	= 826,
    X86_MOV64mr	= 827,
    X86_MOV64ms	= 828,
    X86_MOV64o32a	= 829,
    X86_MOV64o64a	= 830,
    X86_MOV64rc	= 831,
    X86_MOV64rd	= 832,
    X86_MOV64ri	= 833,
    X86_MOV64ri32	= 834,
    X86_MOV64rm	= 835,
    X86_MOV64rr	= 836,
    X86_MOV64rr_REV	= 837,
    X86_MOV64rs	= 838,
    X86_MOV64sm	= 839,
    X86_MOV64sr	= 840,
    X86_MOV8ao16	= 841,
    X86_MOV8ao32	= 842,
    X86_MOV8ao64	= 843,
    X86_MOV8mi	= 844,
    X86_MOV8mr	= 845,
    X86_MOV8mr_NOREX	= 846,
    X86_MOV8o16a	= 847,
    X86_MOV8o32a	= 848,
    X86_MOV8o64a	= 849,
    X86_MOV8ri	= 850,
    X86_MOV8ri_alt	= 851,
    X86_MOV8rm	= 852,
    X86_MOV8rm_NOREX	= 853,
    X86_MOV8rr	= 854,
    X86_MOV8rr_NOREX	= 855,
    X86_MOV8rr_REV	= 856,
    X86_MOVBE16mr	= 857,
    X86_MOVBE16rm	= 858,
    X86_MOVBE32mr	= 859,
    X86_MOVBE32rm	= 860,
    X86_MOVBE64mr	= 861,
    X86_MOVBE64rm	= 862,
    X86_MOVPC32r	= 863,
    X86_MOVSB	= 864,
    X86_MOVSL	= 865,
    X86_MOVSQ	= 866,
    X86_MOVSW	= 867,
    X86_MOVSX16rm8	= 868,
    X86_MOVSX16rr8	= 869,
    X86_MOVSX32_NOREXrm8	= 870,
    X86_MOVSX32_NOREXrr8	= 871,
    X86_MOVSX32rm16	= 872,
    X86_MOVSX32rm8	= 873,
    X86_MOVSX32rr16	= 874,
    X86_MOVSX32rr8	= 875,
    X86_MOVSX64_NOREXrr32	= 876,
    X86_MOVSX64rm16	= 877,
    X86_MOVSX64rm32	= 878,
    X86_MOVSX64rm32_alt	= 879,
    X86_MOVSX64rm8	= 880,
    X86_MOVSX64rr16	= 881,
    X86_MOVSX64rr32	= 882,
    X86_MOVSX64rr8	= 883,
    X86_MOVZX16rm8	= 884,
    X86_MOVZX16rr8	= 885,
    X86_MOVZX32_NOREXrm8	= 886,
    X86_MOVZX32_NOREXrr8	= 887,
    X86_MOVZX32rm16	= 888,
    X86_MOVZX32rm8	= 889,
    X86_MOVZX32rr16	= 890,
    X86_MOVZX32rr8	= 891,
    X86_MOVZX64rm16_Q	= 892,
    X86_MOVZX64rm8_Q	= 893,
    X86_MOVZX64rr16_Q	= 894,
    X86_MOVZX64rr8_Q	= 895,
    X86_MUL16m	= 896,
    X86_MUL16r	= 897,
    X86_MUL32m	= 898,
    X86_MUL32r	= 899,
    X86_MUL64m	= 900,
    X86_MUL64r	= 901,
    X86_MUL8m	= 902,
    X86_MUL8r	= 903,
    X86_MULX32rm	= 904,
    X86_MULX32rr	= 905,
    X86_MULX64rm	= 906,
    X86_MULX64rr	= 907,
    X86_NEG16m	= 908,
    X86_NEG16r	= 909,
    X86_NEG32m	= 910,
    X86_NEG32r	= 911,
    X86_NEG64m	= 912,
    X86_NEG64r	= 913,
    X86_NEG8m	= 914,
    X86_NEG8r	= 915,
    X86_NOOP	= 916,
    X86_NOOP18_16m4	= 917,
    X86_NOOP18_16m5	= 918,
    X86_NOOP18_16m6	= 919,
    X86_NOOP18_16m7	= 920,
    X86_NOOP18_16r4	= 921,
    X86_NOOP18_16r5	= 922,
    X86_NOOP18_16r6	= 923,
    X86_NOOP18_16r7	= 924,
    X86_NOOP18_m4	= 925,
    X86_NOOP18_m5	= 926,
    X86_NOOP18_m6	= 927,
    X86_NOOP18_m7	= 928,
    X86_NOOP18_r4	= 929,
    X86_NOOP18_r5	= 930,
    X86_NOOP18_r6	= 931,
    X86_NOOP18_r7	= 932,
    X86_NOOP19rr	= 933,
    X86_NOOPL	= 934,
    X86_NOOPL_19	= 935,
    X86_NOOPL_1a	= 936,
    X86_NOOPL_1b	= 937,
    X86_NOOPL_1c	= 938,
    X86_NOOPL_1d	= 939,
    X86_NOOPL_1e	= 940,
    X86_NOOPW	= 941,
    X86_NOOPW_19	= 942,
    X86_NOOPW_1a	= 943,
    X86_NOOPW_1b	= 944,
    X86_NOOPW_1c	= 945,
    X86_NOOPW_1d	= 946,
    X86_NOOPW_1e	= 947,
    X86_NOT16m	= 948,
    X86_NOT16r	= 949,
    X86_NOT32m	= 950,
    X86_NOT32r	= 951,
    X86_NOT64m	= 952,
    X86_NOT64r	= 953,
    X86_NOT8m	= 954,
    X86_NOT8r	= 955,
    X86_OR16i16	= 956,
    X86_OR16mi	= 957,
    X86_OR16mi8	= 958,
    X86_OR16mr	= 959,
    X86_OR16ri	= 960,
    X86_OR16ri8	= 961,
    X86_OR16rm	= 962,
    X86_OR16rr	= 963,
    X86_OR16rr_REV	= 964,
    X86_OR32i32	= 965,
    X86_OR32mi	= 966,
    X86_OR32mi8	= 967,
    X86_OR32mr	= 968,
    X86_OR32mrLocked	= 969,
    X86_OR32ri	= 970,
    X86_OR32ri8	= 971,
    X86_OR32rm	= 972,
    X86_OR32rr	= 973,
    X86_OR32rr_REV	= 974,
    X86_OR64i32	= 975,
    X86_OR64mi32	= 976,
    X86_OR64mi8	= 977,
    X86_OR64mr	= 978,
    X86_OR64ri32	= 979,
    X86_OR64ri8	= 980,
    X86_OR64rm	= 981,
    X86_OR64rr	= 982,
    X86_OR64rr_REV	= 983,
    X86_OR8i8	= 984,
    X86_OR8mi	= 985,
    X86_OR8mi8	= 986,
    X86_OR8mr	= 987,
    X86_OR8ri	= 988,
    X86_OR8ri8	= 989,
    X86_OR8rm	= 990,
    X86_OR8rr	= 991,
    X86_OR8rr_REV	= 992,
    X86_OUT16ir	= 993,
    X86_OUT16rr	= 994,
    X86_OUT32ir	= 995,
    X86_OUT32rr	= 996,
    X86_OUT8ir	= 997,
    X86_OUT8rr	= 998,
    X86_OUTSB	= 999,
    X86_OUTSL	= 1000,
    X86_OUTSW	= 1001,
    X86_PCOMMIT	= 1002,
    X86_PDEP32rm	= 1003,
    X86_PDEP32rr	= 1004,
    X86_PDEP64rm	= 1005,
    X86_PDEP64rr	= 1006,
    X86_PEXT32rm	= 1007,
    X86_PEXT32rr	= 1008,
    X86_PEXT64rm	= 1009,
    X86_PEXT64rr	= 1010,
    X86_POP16r	= 1011,
    X86_POP16rmm	= 1012,
    X86_POP16rmr	= 1013,
    X86_POP32r	= 1014,
    X86_POP32rmm	= 1015,
    X86_POP32rmr	= 1016,
    X86_POP64r	= 1017,
    X86_POP64rmm	= 1018,
    X86_POP64rmr	= 1019,
    X86_POPA16	= 1020,
    X86_POPA32	= 1021,
    X86_POPDS16	= 1022,
    X86_POPDS32	= 1023,
    X86_POPES16	= 1024,
    X86_POPES32	= 1025,
    X86_POPF16	= 1026,
    X86_POPF32	= 1027,
    X86_POPF64	= 1028,
    X86_POPFS16	= 1029,
    X86_POPFS32	= 1030,
    X86_POPFS64	= 1031,
    X86_POPGS16	= 1032,
    X86_POPGS32	= 1033,
    X86_POPGS64	= 1034,
    X86_POPSS16	= 1035,
    X86_POPSS32	= 1036,
    X86_PUSH16i8	= 1037,
    X86_PUSH16r	= 1038,
    X86_PUSH16rmm	= 1039,
    X86_PUSH16rmr	= 1040,
    X86_PUSH32i8	= 1041,
    X86_PUSH32r	= 1042,
    X86_PUSH32rmm	= 1043,
    X86_PUSH32rmr	= 1044,
    X86_PUSH64i16	= 1045,
    X86_PUSH64i32	= 1046,
    X86_PUSH64i8	= 1047,
    X86_PUSH64r	= 1048,
    X86_PUSH64rmm	= 1049,
    X86_PUSH64rmr	= 1050,
    X86_PUSHA16	= 1051,
    X86_PUSHA32	= 1052,
    X86_PUSHCS16	= 1053,
    X86_PUSHCS32	= 1054,
    X86_PUSHDS16	= 1055,
    X86_PUSHDS32	= 1056,
    X86_PUSHES16	= 1057,
    X86_PUSHES32	= 1058,
    X86_PUSHF16	= 1059,
    X86_PUSHF32	= 1060,
    X86_PUSHF64	= 1061,
    X86_PUSHFS16	= 1062,
    X86_PUSHFS32	= 1063,
    X86_PUSHFS64	= 1064,
    X86_PUSHGS16	= 1065,
    X86_PUSHGS32	= 1066,
    X86_PUSHGS64	= 1067,
    X86_PUSHSS16	= 1068,
    X86_PUSHSS32	= 1069,
    X86_PUSHi16	= 1070,
    X86_PUSHi32	= 1071,
    X86_RCL16m1	= 1072,
    X86_RCL16mCL	= 1073,
    X86_RCL16mi	= 1074,
    X86_RCL16r1	= 1075,
    X86_RCL16rCL	= 1076,
    X86_RCL16ri	= 1077,
    X86_RCL32m1	= 1078,
    X86_RCL32mCL	= 1079,
    X86_RCL32mi	= 1080,
    X86_RCL32r1	= 1081,
    X86_RCL32rCL	= 1082,
    X86_RCL32ri	= 1083,
    X86_RCL64m1	= 1084,
    X86_RCL64mCL	= 1085,
    X86_RCL64mi	= 1086,
    X86_RCL64r1	= 1087,
    X86_RCL64rCL	= 1088,
    X86_RCL64ri	= 1089,
    X86_RCL8m1	= 1090,
    X86_RCL8mCL	= 1091,
    X86_RCL8mi	= 1092,
    X86_RCL8r1	= 1093,
    X86_RCL8rCL	= 1094,
    X86_RCL8ri	= 1095,
    X86_RCR16m1	= 1096,
    X86_RCR16mCL	= 1097,
    X86_RCR16mi	= 1098,
    X86_RCR16r1	= 1099,
    X86_RCR16rCL	= 1100,
    X86_RCR16ri	= 1101,
    X86_RCR32m1	= 1102,
    X86_RCR32mCL	= 1103,
    X86_RCR32mi	= 1104,
    X86_RCR32r1	= 1105,
    X86_RCR32rCL	= 1106,
    X86_RCR32ri	= 1107,
    X86_RCR64m1	= 1108,
    X86_RCR64mCL	= 1109,
    X86_RCR64mi	= 1110,
    X86_RCR64r1	= 1111,
    X86_RCR64rCL	= 1112,
    X86_RCR64ri	= 1113,
    X86_RCR8m1	= 1114,
    X86_RCR8mCL	= 1115,
    X86_RCR8mi	= 1116,
    X86_RCR8r1	= 1117,
    X86_RCR8rCL	= 1118,
    X86_RCR8ri	= 1119,
    X86_RDFSBASE	= 1120,
    X86_RDFSBASE64	= 1121,
    X86_RDGSBASE	= 1122,
    X86_RDGSBASE64	= 1123,
    X86_RDMSR	= 1124,
    X86_RDPMC	= 1125,
    X86_RDRAND16r	= 1126,
    X86_RDRAND32r	= 1127,
    X86_RDRAND64r	= 1128,
    X86_RDSEED16r	= 1129,
    X86_RDSEED32r	= 1130,
    X86_RDSEED64r	= 1131,
    X86_RDTSC	= 1132,
    X86_RDTSCP	= 1133,
    X86_RELEASE_ADD32mi	= 1134,
    X86_RELEASE_ADD64mi32	= 1135,
    X86_RELEASE_ADD8mi	= 1136,
    X86_RELEASE_AND32mi	= 1137,
    X86_RELEASE_AND64mi32	= 1138,
    X86_RELEASE_AND8mi	= 1139,
    X86_RELEASE_DEC16m	= 1140,
    X86_RELEASE_DEC32m	= 1141,
    X86_RELEASE_DEC64m	= 1142,
    X86_RELEASE_DEC8m	= 1143,
    X86_RELEASE_INC16m	= 1144,
    X86_RELEASE_INC32m	= 1145,
    X86_RELEASE_INC64m	= 1146,
    X86_RELEASE_INC8m	= 1147,
    X86_RELEASE_MOV16mi	= 1148,
    X86_RELEASE_MOV16mr	= 1149,
    X86_RELEASE_MOV32mi	= 1150,
    X86_RELEASE_MOV32mr	= 1151,
    X86_RELEASE_MOV64mi32	= 1152,
    X86_RELEASE_MOV64mr	= 1153,
    X86_RELEASE_MOV8mi	= 1154,
    X86_RELEASE_MOV8mr	= 1155,
    X86_RELEASE_OR32mi	= 1156,
    X86_RELEASE_OR64mi32	= 1157,
    X86_RELEASE_OR8mi	= 1158,
    X86_RELEASE_XOR32mi	= 1159,
    X86_RELEASE_XOR64mi32	= 1160,
    X86_RELEASE_XOR8mi	= 1161,
    X86_REPNE_PREFIX	= 1162,
    X86_REP_MOVSB_32	= 1163,
    X86_REP_MOVSB_64	= 1164,
    X86_REP_MOVSD_32	= 1165,
    X86_REP_MOVSD_64	= 1166,
    X86_REP_MOVSQ_64	= 1167,
    X86_REP_MOVSW_32	= 1168,
    X86_REP_MOVSW_64	= 1169,
    X86_REP_PREFIX	= 1170,
    X86_REP_STOSB_32	= 1171,
    X86_REP_STOSB_64	= 1172,
    X86_REP_STOSD_32	= 1173,
    X86_REP_STOSD_64	= 1174,
    X86_REP_STOSQ_64	= 1175,
    X86_REP_STOSW_32	= 1176,
    X86_REP_STOSW_64	= 1177,
    X86_RETIL	= 1178,
    X86_RETIQ	= 1179,
    X86_RETIW	= 1180,
    X86_RETL	= 1181,
    X86_RETQ	= 1182,
    X86_RETW	= 1183,
    X86_REX64_PREFIX	= 1184,
    X86_ROL16m1	= 1185,
    X86_ROL16mCL	= 1186,
    X86_ROL16mi	= 1187,
    X86_ROL16r1	= 1188,
    X86_ROL16rCL	= 1189,
    X86_ROL16ri	= 1190,
    X86_ROL32m1	= 1191,
    X86_ROL32mCL	= 1192,
    X86_ROL32mi	= 1193,
    X86_ROL32r1	= 1194,
    X86_ROL32rCL	= 1195,
    X86_ROL32ri	= 1196,
    X86_ROL64m1	= 1197,
    X86_ROL64mCL	= 1198,
    X86_ROL64mi	= 1199,
    X86_ROL64r1	= 1200,
    X86_ROL64rCL	= 1201,
    X86_ROL64ri	= 1202,
    X86_ROL8m1	= 1203,
    X86_ROL8mCL	= 1204,
    X86_ROL8mi	= 1205,
    X86_ROL8r1	= 1206,
    X86_ROL8rCL	= 1207,
    X86_ROL8ri	= 1208,
    X86_ROR16m1	= 1209,
    X86_ROR16mCL	= 1210,
    X86_ROR16mi	= 1211,
    X86_ROR16r1	= 1212,
    X86_ROR16rCL	= 1213,
    X86_ROR16ri	= 1214,
    X86_ROR32m1	= 1215,
    X86_ROR32mCL	= 1216,
    X86_ROR32mi	= 1217,
    X86_ROR32r1	= 1218,
    X86_ROR32rCL	= 1219,
    X86_ROR32ri	= 1220,
    X86_ROR64m1	= 1221,
    X86_ROR64mCL	= 1222,
    X86_ROR64mi	= 1223,
    X86_ROR64r1	= 1224,
    X86_ROR64rCL	= 1225,
    X86_ROR64ri	= 1226,
    X86_ROR8m1	= 1227,
    X86_ROR8mCL	= 1228,
    X86_ROR8mi	= 1229,
    X86_ROR8r1	= 1230,
    X86_ROR8rCL	= 1231,
    X86_ROR8ri	= 1232,
    X86_RORX32mi	= 1233,
    X86_RORX32ri	= 1234,
    X86_RORX64mi	= 1235,
    X86_RORX64ri	= 1236,
    X86_RSM	= 1237,
    X86_SAHF	= 1238,
    X86_SAL16m1	= 1239,
    X86_SAL16mCL	= 1240,
    X86_SAL16mi	= 1241,
    X86_SAL16r1	= 1242,
    X86_SAL16rCL	= 1243,
    X86_SAL16ri	= 1244,
    X86_SAL32m1	= 1245,
    X86_SAL32mCL	= 1246,
    X86_SAL32mi	= 1247,
    X86_SAL32r1	= 1248,
    X86_SAL32rCL	= 1249,
    X86_SAL32ri	= 1250,
    X86_SAL64m1	= 1251,
    X86_SAL64mCL	= 1252,
    X86_SAL64mi	= 1253,
    X86_SAL64r1	= 1254,
    X86_SAL64rCL	= 1255,
    X86_SAL64ri	= 1256,
    X86_SAL8m1	= 1257,
    X86_SAL8mCL	= 1258,
    X86_SAL8mi	= 1259,
    X86_SAL8r1	= 1260,
    X86_SAL8rCL	= 1261,
    X86_SAL8ri	= 1262,
    X86_SALC	= 1263,
    X86_SAR16m1	= 1264,
    X86_SAR16mCL	= 1265,
    X86_SAR16mi	= 1266,
    X86_SAR16r1	= 1267,
    X86_SAR16rCL	= 1268,
    X86_SAR16ri	= 1269,
    X86_SAR32m1	= 1270,
    X86_SAR32mCL	= 1271,
    X86_SAR32mi	= 1272,
    X86_SAR32r1	= 1273,
    X86_SAR32rCL	= 1274,
    X86_SAR32ri	= 1275,
    X86_SAR64m1	= 1276,
    X86_SAR64mCL	= 1277,
    X86_SAR64mi	= 1278,
    X86_SAR64r1	= 1279,
    X86_SAR64rCL	= 1280,
    X86_SAR64ri	= 1281,
    X86_SAR8m1	= 1282,
    X86_SAR8mCL	= 1283,
    X86_SAR8mi	= 1284,
    X86_SAR8r1	= 1285,
    X86_SAR8rCL	= 1286,
    X86_SAR8ri	= 1287,
    X86_SARX32rm	= 1288,
    X86_SARX32rr	= 1289,
    X86_SARX64rm	= 1290,
    X86_SARX64rr	= 1291,
    X86_SBB16i16	= 1292,
    X86_SBB16mi	= 1293,
    X86_SBB16mi8	= 1294,
    X86_SBB16mr	= 1295,
    X86_SBB16ri	= 1296,
    X86_SBB16ri8	= 1297,
    X86_SBB16rm	= 1298,
    X86_SBB16rr	= 1299,
    X86_SBB16rr_REV	= 1300,
    X86_SBB32i32	= 1301,
    X86_SBB32mi	= 1302,
    X86_SBB32mi8	= 1303,
    X86_SBB32mr	= 1304,
    X86_SBB32ri	= 1305,
    X86_SBB32ri8	= 1306,
    X86_SBB32rm	= 1307,
    X86_SBB32rr	= 1308,
    X86_SBB32rr_REV	= 1309,
    X86_SBB64i32	= 1310,
    X86_SBB64mi32	= 1311,
    X86_SBB64mi8	= 1312,
    X86_SBB64mr	= 1313,
    X86_SBB64ri32	= 1314,
    X86_SBB64ri8	= 1315,
    X86_SBB64rm	= 1316,
    X86_SBB64rr	= 1317,
    X86_SBB64rr_REV	= 1318,
    X86_SBB8i8	= 1319,
    X86_SBB8mi	= 1320,
    X86_SBB8mi8	= 1321,
    X86_SBB8mr	= 1322,
    X86_SBB8ri	= 1323,
    X86_SBB8ri8	= 1324,
    X86_SBB8rm	= 1325,
    X86_SBB8rr	= 1326,
    X86_SBB8rr_REV	= 1327,
    X86_SCASB	= 1328,
    X86_SCASL	= 1329,
    X86_SCASQ	= 1330,
    X86_SCASW	= 1331,
    X86_SEG_ALLOCA_32	= 1332,
    X86_SEG_ALLOCA_64	= 1333,
    X86_SEH_EndPrologue	= 1334,
    X86_SEH_Epilogue	= 1335,
    X86_SEH_PushFrame	= 1336,
    X86_SEH_PushReg	= 1337,
    X86_SEH_SaveReg	= 1338,
    X86_SEH_SaveXMM	= 1339,
    X86_SEH_SetFrame	= 1340,
    X86_SEH_StackAlloc	= 1341,
    X86_SETAEm	= 1342,
    X86_SETAEr	= 1343,
    X86_SETAm	= 1344,
    X86_SETAr	= 1345,
    X86_SETBEm	= 1346,
    X86_SETBEr	= 1347,
    X86_SETB_C16r	= 1348,
    X86_SETB_C32r	= 1349,
    X86_SETB_C64r	= 1350,
    X86_SETB_C8r	= 1351,
    X86_SETBm	= 1352,
    X86_SETBr	= 1353,
    X86_SETEm	= 1354,
    X86_SETEr	= 1355,
    X86_SETGEm	= 1356,
    X86_SETGEr	= 1357,
    X86_SETGm	= 1358,
    X86_SETGr	= 1359,
    X86_SETLEm	= 1360,
    X86_SETLEr	= 1361,
    X86_SETLm	= 1362,
    X86_SETLr	= 1363,
    X86_SETNEm	= 1364,
    X86_SETNEr	= 1365,
    X86_SETNOm	= 1366,
    X86_SETNOr	= 1367,
    X86_SETNPm	= 1368,
    X86_SETNPr	= 1369,
    X86_SETNSm	= 1370,
    X86_SETNSr	= 1371,
    X86_SETOm	= 1372,
    X86_SETOr	= 1373,
    X86_SETPm	= 1374,
    X86_SETPr	= 1375,
    X86_SETSm	= 1376,
    X86_SETSr	= 1377,
    X86_SGDT16m	= 1378,
    X86_SGDT32m	= 1379,
    X86_SGDT64m	= 1380,
    X86_SHL16m1	= 1381,
    X86_SHL16mCL	= 1382,
    X86_SHL16mi	= 1383,
    X86_SHL16r1	= 1384,
    X86_SHL16rCL	= 1385,
    X86_SHL16ri	= 1386,
    X86_SHL32m1	= 1387,
    X86_SHL32mCL	= 1388,
    X86_SHL32mi	= 1389,
    X86_SHL32r1	= 1390,
    X86_SHL32rCL	= 1391,
    X86_SHL32ri	= 1392,
    X86_SHL64m1	= 1393,
    X86_SHL64mCL	= 1394,
    X86_SHL64mi	= 1395,
    X86_SHL64r1	= 1396,
    X86_SHL64rCL	= 1397,
    X86_SHL64ri	= 1398,
    X86_SHL8m1	= 1399,
    X86_SHL8mCL	= 1400,
    X86_SHL8mi	= 1401,
    X86_SHL8r1	= 1402,
    X86_SHL8rCL	= 1403,
    X86_SHL8ri	= 1404,
    X86_SHLD16mrCL	= 1405,
    X86_SHLD16mri8	= 1406,
    X86_SHLD16rrCL	= 1407,
    X86_SHLD16rri8	= 1408,
    X86_SHLD32mrCL	= 1409,
    X86_SHLD32mri8	= 1410,
    X86_SHLD32rrCL	= 1411,
    X86_SHLD32rri8	= 1412,
    X86_SHLD64mrCL	= 1413,
    X86_SHLD64mri8	= 1414,
    X86_SHLD64rrCL	= 1415,
    X86_SHLD64rri8	= 1416,
    X86_SHLX32rm	= 1417,
    X86_SHLX32rr	= 1418,
    X86_SHLX64rm	= 1419,
    X86_SHLX64rr	= 1420,
    X86_SHR16m1	= 1421,
    X86_SHR16mCL	= 1422,
    X86_SHR16mi	= 1423,
    X86_SHR16r1	= 1424,
    X86_SHR16rCL	= 1425,
    X86_SHR16ri	= 1426,
    X86_SHR32m1	= 1427,
    X86_SHR32mCL	= 1428,
    X86_SHR32mi	= 1429,
    X86_SHR32r1	= 1430,
    X86_SHR32rCL	= 1431,
    X86_SHR32ri	= 1432,
    X86_SHR64m1	= 1433,
    X86_SHR64mCL	= 1434,
    X86_SHR64mi	= 1435,
    X86_SHR64r1	= 1436,
    X86_SHR64rCL	= 1437,
    X86_SHR64ri	= 1438,
    X86_SHR8m1	= 1439,
    X86_SHR8mCL	= 1440,
    X86_SHR8mi	= 1441,
    X86_SHR8r1	= 1442,
    X86_SHR8rCL	= 1443,
    X86_SHR8ri	= 1444,
    X86_SHRD16mrCL	= 1445,
    X86_SHRD16mri8	= 1446,
    X86_SHRD16rrCL	= 1447,
    X86_SHRD16rri8	= 1448,
    X86_SHRD32mrCL	= 1449,
    X86_SHRD32mri8	= 1450,
    X86_SHRD32rrCL	= 1451,
    X86_SHRD32rri8	= 1452,
    X86_SHRD64mrCL	= 1453,
    X86_SHRD64mri8	= 1454,
    X86_SHRD64rrCL	= 1455,
    X86_SHRD64rri8	= 1456,
    X86_SHRX32rm	= 1457,
    X86_SHRX32rr	= 1458,
    X86_SHRX64rm	= 1459,
    X86_SHRX64rr	= 1460,
    X86_SIDT16m	= 1461,
    X86_SIDT32m	= 1462,
    X86_SIDT64m	= 1463,
    X86_SKINIT	= 1464,
    X86_SLDT16m	= 1465,
    X86_SLDT16r	= 1466,
    X86_SLDT32r	= 1467,
    X86_SLDT64m	= 1468,
    X86_SLDT64r	= 1469,
    X86_SMSW16m	= 1470,
    X86_SMSW16r	= 1471,
    X86_SMSW32r	= 1472,
    X86_SMSW64r	= 1473,
    X86_STAC	= 1474,
    X86_STC	= 1475,
    X86_STD	= 1476,
    X86_STGI	= 1477,
    X86_STI	= 1478,
    X86_STOSB	= 1479,
    X86_STOSL	= 1480,
    X86_STOSQ	= 1481,
    X86_STOSW	= 1482,
    X86_STR16r	= 1483,
    X86_STR32r	= 1484,
    X86_STR64r	= 1485,
    X86_STRm	= 1486,
    X86_SUB16i16	= 1487,
    X86_SUB16mi	= 1488,
    X86_SUB16mi8	= 1489,
    X86_SUB16mr	= 1490,
    X86_SUB16ri	= 1491,
    X86_SUB16ri8	= 1492,
    X86_SUB16rm	= 1493,
    X86_SUB16rr	= 1494,
    X86_SUB16rr_REV	= 1495,
    X86_SUB32i32	= 1496,
    X86_SUB32mi	= 1497,
    X86_SUB32mi8	= 1498,
    X86_SUB32mr	= 1499,
    X86_SUB32ri	= 1500,
    X86_SUB32ri8	= 1501,
    X86_SUB32rm	= 1502,
    X86_SUB32rr	= 1503,
    X86_SUB32rr_REV	= 1504,
    X86_SUB64i32	= 1505,
    X86_SUB64mi32	= 1506,
    X86_SUB64mi8	= 1507,
    X86_SUB64mr	= 1508,
    X86_SUB64ri32	= 1509,
    X86_SUB64ri8	= 1510,
    X86_SUB64rm	= 1511,
    X86_SUB64rr	= 1512,
    X86_SUB64rr_REV	= 1513,
    X86_SUB8i8	= 1514,
    X86_SUB8mi	= 1515,
    X86_SUB8mi8	= 1516,
    X86_SUB8mr	= 1517,
    X86_SUB8ri	= 1518,
    X86_SUB8ri8	= 1519,
    X86_SUB8rm	= 1520,
    X86_SUB8rr	= 1521,
    X86_SUB8rr_REV	= 1522,
    X86_SWAPGS	= 1523,
    X86_SYSCALL	= 1524,
    X86_SYSENTER	= 1525,
    X86_SYSEXIT	= 1526,
    X86_SYSEXIT64	= 1527,
    X86_SYSRET	= 1528,
    X86_SYSRET64	= 1529,
    X86_T1MSKC32rm	= 1530,
    X86_T1MSKC32rr	= 1531,
    X86_T1MSKC64rm	= 1532,
    X86_T1MSKC64rr	= 1533,
    X86_TAILJMPd	= 1534,
    X86_TAILJMPd64	= 1535,
    X86_TAILJMPd64_REX	= 1536,
    X86_TAILJMPm	= 1537,
    X86_TAILJMPm64	= 1538,
    X86_TAILJMPm64_REX	= 1539,
    X86_TAILJMPr	= 1540,
    X86_TAILJMPr64	= 1541,
    X86_TAILJMPr64_REX	= 1542,
    X86_TCRETURNdi	= 1543,
    X86_TCRETURNdi64	= 1544,
    X86_TCRETURNmi	= 1545,
    X86_TCRETURNmi64	= 1546,
    X86_TCRETURNri	= 1547,
    X86_TCRETURNri64	= 1548,
    X86_TEST16i16	= 1549,
    X86_TEST16mi	= 1550,
    X86_TEST16mi_alt	= 1551,
    X86_TEST16ri	= 1552,
    X86_TEST16ri_alt	= 1553,
    X86_TEST16rm	= 1554,
    X86_TEST16rr	= 1555,
    X86_TEST32i32	= 1556,
    X86_TEST32mi	= 1557,
    X86_TEST32mi_alt	= 1558,
    X86_TEST32ri	= 1559,
    X86_TEST32ri_alt	= 1560,
    X86_TEST32rm	= 1561,
    X86_TEST32rr	= 1562,
    X86_TEST64i32	= 1563,
    X86_TEST64mi32	= 1564,
    X86_TEST64mi32_alt	= 1565,
    X86_TEST64ri32	= 1566,
    X86_TEST64ri32_alt	= 1567,
    X86_TEST64rm	= 1568,
    X86_TEST64rr	= 1569,
    X86_TEST8i8	= 1570,
    X86_TEST8mi	= 1571,
    X86_TEST8mi_alt	= 1572,
    X86_TEST8ri	= 1573,
    X86_TEST8ri_NOREX	= 1574,
    X86_TEST8ri_alt	= 1575,
    X86_TEST8rm	= 1576,
    X86_TEST8rr	= 1577,
    X86_TLSCall_32	= 1578,
    X86_TLSCall_64	= 1579,
    X86_TLS_addr32	= 1580,
    X86_TLS_addr64	= 1581,
    X86_TLS_base_addr32	= 1582,
    X86_TLS_base_addr64	= 1583,
    X86_TRAP	= 1584,
    X86_TZCNT16rm	= 1585,
    X86_TZCNT16rr	= 1586,
    X86_TZCNT32rm	= 1587,
    X86_TZCNT32rr	= 1588,
    X86_TZCNT64rm	= 1589,
    X86_TZCNT64rr	= 1590,
    X86_TZMSK32rm	= 1591,
    X86_TZMSK32rr	= 1592,
    X86_TZMSK64rm	= 1593,
    X86_TZMSK64rr	= 1594,
    X86_UD2B	= 1595,
    X86_VAARG_64	= 1596,
    X86_VASTART_SAVE_XMM_REGS	= 1597,
    X86_VERRm	= 1598,
    X86_VERRr	= 1599,
    X86_VERWm	= 1600,
    X86_VERWr	= 1601,
    X86_VMCALL	= 1602,
    X86_VMCLEARm	= 1603,
    X86_VMFUNC	= 1604,
    X86_VMLAUNCH	= 1605,
    X86_VMLOAD32	= 1606,
    X86_VMLOAD64	= 1607,
    X86_VMMCALL	= 1608,
    X86_VMPTRLDm	= 1609,
    X86_VMPTRSTm	= 1610,
    X86_VMREAD32rm	= 1611,
    X86_VMREAD32rr	= 1612,
    X86_VMREAD64rm	= 1613,
    X86_VMREAD64rr	= 1614,
    X86_VMRESUME	= 1615,
    X86_VMRUN32	= 1616,
    X86_VMRUN64	= 1617,
    X86_VMSAVE32	= 1618,
    X86_VMSAVE64	= 1619,
    X86_VMWRITE32rm	= 1620,
    X86_VMWRITE32rr	= 1621,
    X86_VMWRITE64rm	= 1622,
    X86_VMWRITE64rr	= 1623,
    X86_VMXOFF	= 1624,
    X86_VMXON	= 1625,
    X86_WBINVD	= 1626,
    X86_WIN_ALLOCA	= 1627,
    X86_WIN_FTOL_32	= 1628,
    X86_WIN_FTOL_64	= 1629,
    X86_WRFSBASE	= 1630,
    X86_WRFSBASE64	= 1631,
    X86_WRGSBASE	= 1632,
    X86_WRGSBASE64	= 1633,
    X86_WRMSR	= 1634,
    X86_XADD16rm	= 1635,
    X86_XADD16rr	= 1636,
    X86_XADD32rm	= 1637,
    X86_XADD32rr	= 1638,
    X86_XADD64rm	= 1639,
    X86_XADD64rr	= 1640,
    X86_XADD8rm	= 1641,
    X86_XADD8rr	= 1642,
    X86_XCHG16ar	= 1643,
    X86_XCHG16rm	= 1644,
    X86_XCHG16rr	= 1645,
    X86_XCHG32ar	= 1646,
    X86_XCHG32ar64	= 1647,
    X86_XCHG32rm	= 1648,
    X86_XCHG32rr	= 1649,
    X86_XCHG64ar	= 1650,
    X86_XCHG64rm	= 1651,
    X86_XCHG64rr	= 1652,
    X86_XCHG8rm	= 1653,
    X86_XCHG8rr	= 1654,
    X86_XCRYPTCBC	= 1655,
    X86_XCRYPTCFB	= 1656,
    X86_XCRYPTCTR	= 1657,
    X86_XCRYPTECB	= 1658,
    X86_XCRYPTOFB	= 1659,
    X86_XGETBV	= 1660,
    X86_XLAT	= 1661,
    X86_XOR16i16	= 1662,
    X86_XOR16mi	= 1663,
    X86_XOR16mi8	= 1664,
    X86_XOR16mr	= 1665,
    X86_XOR16ri	= 1666,
    X86_XOR16ri8	= 1667,
    X86_XOR16rm	= 1668,
    X86_XOR16rr	= 1669,
    X86_XOR16rr_REV	= 1670,
    X86_XOR32i32	= 1671,
    X86_XOR32mi	= 1672,
    X86_XOR32mi8	= 1673,
    X86_XOR32mr	= 1674,
    X86_XOR32ri	= 1675,
    X86_XOR32ri8	= 1676,
    X86_XOR32rm	= 1677,
    X86_XOR32rr	= 1678,
    X86_XOR32rr_REV	= 1679,
    X86_XOR64i32	= 1680,
    X86_XOR64mi32	= 1681,
    X86_XOR64mi8	= 1682,
    X86_XOR64mr	= 1683,
    X86_XOR64ri32	= 1684,
    X86_XOR64ri8	= 1685,
    X86_XOR64rm	= 1686,
    X86_XOR64rr	= 1687,
    X86_XOR64rr_REV	= 1688,
    X86_XOR8i8	= 1689,
    X86_XOR8mi	= 1690,
    X86_XOR8mi8	= 1691,
    X86_XOR8mr	= 1692,
    X86_XOR8ri	= 1693,
    X86_XOR8ri8	= 1694,
    X86_XOR8rm	= 1695,
    X86_XOR8rr	= 1696,
    X86_XOR8rr_REV	= 1697,
    X86_XRSTOR	= 1698,
    X86_XRSTOR64	= 1699,
    X86_XRSTORS	= 1700,
    X86_XRSTORS64	= 1701,
    X86_XSAVE	= 1702,
    X86_XSAVE64	= 1703,
    X86_XSAVEC	= 1704,
    X86_XSAVEC64	= 1705,
    X86_XSAVEOPT	= 1706,
    X86_XSAVEOPT64	= 1707,
    X86_XSAVES	= 1708,
    X86_XSAVES64	= 1709,
    X86_XSETBV	= 1710,
    X86_XSHA1	= 1711,
    X86_XSHA256	= 1712,
    X86_XSTORE	= 1713,
    X86_UD0	= 1714,
    X86_INSTRUCTION_LIST_END = 1715
};

#endif // GET_INSTRINFO_ENUM


#ifdef GET_INSTRINFO_MC_DESC
#undef GET_INSTRINFO_MC_DESC

typedef struct x86_op_id_pair {
	uint16_t first;
	uint16_t second;
} x86_op_id_pair;

static const x86_op_id_pair x86_16_bit_eq_tbl[] = {
	{ 27, 26 },
	{ 28, 26 },
	{ 39, 30 },
	{ 40, 31 },
	{ 41, 32 },
	{ 42, 33 },
	{ 43, 34 },
	{ 44, 35 },
	{ 45, 36 },
	{ 46, 37 },
	{ 47, 38 },
	{ 48, 30 },
	{ 50, 32 },
	{ 51, 33 },
	{ 53, 35 },
	{ 54, 36 },
	{ 55, 37 },
	{ 56, 38 },
	{ 82, 70 },
	{ 83, 71 },
	{ 84, 72 },
	{ 85, 73 },
	{ 86, 74 },
	{ 87, 75 },
	{ 88, 76 },
	{ 89, 77 },
	{ 90, 78 },
	{ 91, 79 },
	{ 92, 80 },
	{ 93, 81 },
	{ 94, 70 },
	{ 96, 72 },
	{ 97, 73 },
	{ 100, 75 },
	{ 101, 76 },
	{ 102, 78 },
	{ 103, 79 },
	{ 104, 80 },
	{ 105, 81 },
	{ 132, 123 },
	{ 133, 124 },
	{ 134, 125 },
	{ 135, 126 },
	{ 136, 127 },
	{ 137, 128 },
	{ 138, 129 },
	{ 139, 130 },
	{ 140, 131 },
	{ 141, 123 },
	{ 143, 125 },
	{ 144, 126 },
	{ 146, 128 },
	{ 147, 129 },
	{ 148, 130 },
	{ 149, 131 },
	{ 214, 213 },
	{ 217, 215 },
	{ 218, 216 },
	{ 219, 215 },
	{ 220, 216 },
	{ 223, 221 },
	{ 224, 222 },
	{ 225, 221 },
	{ 226, 222 },
	{ 233, 229 },
	{ 234, 230 },
	{ 235, 231 },
	{ 236, 232 },
	{ 237, 229 },
	{ 238, 230 },
	{ 239, 231 },
	{ 240, 232 },
	{ 245, 241 },
	{ 246, 242 },
	{ 247, 243 },
	{ 248, 244 },
	{ 249, 241 },
	{ 250, 242 },
	{ 251, 243 },
	{ 252, 244 },
	{ 257, 253 },
	{ 258, 254 },
	{ 259, 255 },
	{ 260, 256 },
	{ 261, 253 },
	{ 262, 254 },
	{ 263, 255 },
	{ 264, 256 },
	{ 269, 265 },
	{ 270, 266 },
	{ 271, 267 },
	{ 272, 268 },
	{ 273, 265 },
	{ 274, 266 },
	{ 275, 267 },
	{ 276, 268 },
	{ 283, 281 },
	{ 284, 282 },
	{ 285, 281 },
	{ 287, 282 },
	{ 289, 288 },
	{ 295, 467 },
	{ 304, 302 },
	{ 305, 303 },
	{ 306, 302 },
	{ 307, 303 },
	{ 310, 308 },
	{ 311, 309 },
	{ 312, 308 },
	{ 313, 309 },
	{ 316, 314 },
	{ 317, 315 },
	{ 318, 314 },
	{ 319, 315 },
	{ 322, 320 },
	{ 323, 321 },
	{ 324, 320 },
	{ 325, 321 },
	{ 328, 326 },
	{ 329, 327 },
	{ 330, 326 },
	{ 331, 327 },
	{ 334, 332 },
	{ 335, 333 },
	{ 336, 332 },
	{ 337, 333 },
	{ 340, 338 },
	{ 341, 339 },
	{ 342, 338 },
	{ 343, 339 },
	{ 346, 344 },
	{ 347, 345 },
	{ 348, 344 },
	{ 349, 345 },
	{ 352, 350 },
	{ 353, 351 },
	{ 354, 350 },
	{ 355, 351 },
	{ 358, 356 },
	{ 359, 357 },
	{ 360, 356 },
	{ 361, 357 },
	{ 364, 362 },
	{ 365, 363 },
	{ 366, 362 },
	{ 367, 363 },
	{ 370, 368 },
	{ 371, 369 },
	{ 372, 368 },
	{ 373, 369 },
	{ 376, 374 },
	{ 377, 375 },
	{ 378, 374 },
	{ 379, 375 },
	{ 382, 380 },
	{ 383, 381 },
	{ 384, 380 },
	{ 385, 381 },
	{ 388, 386 },
	{ 389, 387 },
	{ 390, 386 },
	{ 391, 387 },
	{ 394, 392 },
	{ 395, 393 },
	{ 396, 392 },
	{ 397, 393 },
	{ 401, 400 },
	{ 424, 415 },
	{ 425, 416 },
	{ 426, 417 },
	{ 427, 418 },
	{ 428, 419 },
	{ 429, 420 },
	{ 430, 421 },
	{ 431, 422 },
	{ 432, 423 },
	{ 433, 415 },
	{ 435, 417 },
	{ 436, 418 },
	{ 438, 420 },
	{ 439, 421 },
	{ 440, 422 },
	{ 441, 423 },
	{ 452, 454 },
	{ 453, 454 },
	{ 458, 456 },
	{ 459, 457 },
	{ 460, 456 },
	{ 461, 457 },
	{ 475, 472 },
	{ 476, 473 },
	{ 477, 474 },
	{ 478, 472 },
	{ 479, 473 },
	{ 484, 482 },
	{ 485, 483 },
	{ 486, 482 },
	{ 487, 483 },
	{ 500, 498 },
	{ 501, 499 },
	{ 505, 503 },
	{ 506, 504 },
	{ 513, 511 },
	{ 514, 512 },
	{ 515, 511 },
	{ 516, 512 },
	{ 527, 519 },
	{ 528, 520 },
	{ 529, 521 },
	{ 530, 522 },
	{ 531, 523 },
	{ 532, 524 },
	{ 533, 525 },
	{ 534, 526 },
	{ 535, 519 },
	{ 536, 520 },
	{ 537, 521 },
	{ 539, 523 },
	{ 540, 524 },
	{ 542, 526 },
	{ 547, 545 },
	{ 548, 546 },
	{ 554, 551 },
	{ 555, 552 },
	{ 556, 553 },
	{ 557, 551 },
	{ 558, 552 },
	{ 562, 563 },
	{ 566, 565 },
	{ 579, 578 },
	{ 580, 578 },
	{ 613, 611 },
	{ 614, 612 },
	{ 615, 611 },
	{ 616, 612 },
	{ 645, 643 },
	{ 646, 644 },
	{ 647, 643 },
	{ 648, 644 },
	{ 651, 649 },
	{ 652, 649 },
	{ 656, 655 },
	{ 658, 657 },
	{ 660, 657 },
	{ 664, 663 },
	{ 666, 665 },
	{ 667, 665 },
	{ 669, 668 },
	{ 670, 668 },
	{ 672, 671 },
	{ 673, 671 },
	{ 675, 674 },
	{ 676, 674 },
	{ 684, 681 },
	{ 685, 682 },
	{ 686, 683 },
	{ 688, 682 },
	{ 689, 683 },
	{ 695, 692 },
	{ 696, 693 },
	{ 697, 694 },
	{ 699, 693 },
	{ 700, 694 },
	{ 704, 703 },
	{ 705, 703 },
	{ 708, 707 },
	{ 709, 707 },
	{ 714, 711 },
	{ 715, 712 },
	{ 716, 713 },
	{ 718, 712 },
	{ 719, 713 },
	{ 726, 723 },
	{ 727, 724 },
	{ 728, 725 },
	{ 730, 724 },
	{ 731, 725 },
	{ 737, 734 },
	{ 738, 735 },
	{ 739, 736 },
	{ 741, 735 },
	{ 742, 736 },
	{ 746, 748 },
	{ 747, 748 },
	{ 752, 754 },
	{ 753, 754 },
	{ 755, 757 },
	{ 756, 757 },
	{ 760, 758 },
	{ 761, 759 },
	{ 762, 758 },
	{ 763, 759 },
	{ 765, 764 },
	{ 766, 764 },
	{ 770, 769 },
	{ 771, 769 },
	{ 775, 773 },
	{ 776, 774 },
	{ 777, 773 },
	{ 778, 774 },
	{ 783, 782 },
	{ 784, 782 },
	{ 789, 788 },
	{ 790, 788 },
	{ 799, 782 },
	{ 800, 782 },
	{ 800, 783 },
	{ 800, 799 },
	{ 801, 782 },
	{ 801, 784 },
	{ 801, 799 },
	{ 804, 785 },
	{ 805, 786 },
	{ 806, 787 },
	{ 807, 788 },
	{ 808, 788 },
	{ 808, 789 },
	{ 808, 807 },
	{ 809, 788 },
	{ 809, 790 },
	{ 809, 807 },
	{ 813, 791 },
	{ 815, 792 },
	{ 816, 793 },
	{ 817, 794 },
	{ 818, 795 },
	{ 819, 796 },
	{ 820, 797 },
	{ 821, 798 },
	{ 822, 782 },
	{ 822, 783 },
	{ 823, 782 },
	{ 823, 784 },
	{ 827, 786 },
	{ 828, 787 },
	{ 829, 788 },
	{ 829, 789 },
	{ 830, 788 },
	{ 830, 790 },
	{ 833, 791 },
	{ 835, 793 },
	{ 836, 794 },
	{ 837, 795 },
	{ 838, 796 },
	{ 839, 797 },
	{ 840, 798 },
	{ 842, 841 },
	{ 843, 841 },
	{ 848, 847 },
	{ 849, 847 },
	{ 859, 857 },
	{ 860, 858 },
	{ 861, 857 },
	{ 862, 858 },
	{ 865, 867 },
	{ 866, 867 },
	{ 873, 868 },
	{ 875, 869 },
	{ 878, 877 },
	{ 880, 868 },
	{ 882, 881 },
	{ 883, 869 },
	{ 889, 884 },
	{ 891, 885 },
	{ 898, 896 },
	{ 899, 897 },
	{ 900, 896 },
	{ 901, 897 },
	{ 910, 908 },
	{ 911, 909 },
	{ 912, 908 },
	{ 913, 909 },
	{ 917, 919 },
	{ 921, 923 },
	{ 925, 927 },
	{ 929, 931 },
	{ 934, 941 },
	{ 935, 942 },
	{ 936, 943 },
	{ 937, 944 },
	{ 938, 945 },
	{ 939, 946 },
	{ 940, 947 },
	{ 950, 948 },
	{ 951, 949 },
	{ 952, 948 },
	{ 953, 949 },
	{ 965, 956 },
	{ 966, 957 },
	{ 967, 958 },
	{ 968, 959 },
	{ 970, 960 },
	{ 971, 961 },
	{ 972, 962 },
	{ 973, 963 },
	{ 974, 964 },
	{ 975, 956 },
	{ 977, 958 },
	{ 978, 959 },
	{ 980, 961 },
	{ 981, 962 },
	{ 982, 963 },
	{ 983, 964 },
	{ 995, 993 },
	{ 996, 994 },
	{ 1000, 1001 },
	{ 1014, 1011 },
	{ 1015, 1012 },
	{ 1016, 1013 },
	{ 1017, 1011 },
	{ 1018, 1012 },
	{ 1019, 1013 },
	{ 1021, 1020 },
	{ 1023, 1022 },
	{ 1025, 1024 },
	{ 1027, 1026 },
	{ 1028, 1026 },
	{ 1030, 1029 },
	{ 1031, 1029 },
	{ 1033, 1032 },
	{ 1034, 1032 },
	{ 1036, 1035 },
	{ 1041, 1037 },
	{ 1042, 1038 },
	{ 1043, 1039 },
	{ 1044, 1040 },
	{ 1046, 1045 },
	{ 1047, 1037 },
	{ 1048, 1038 },
	{ 1049, 1039 },
	{ 1050, 1040 },
	{ 1052, 1051 },
	{ 1054, 1053 },
	{ 1056, 1055 },
	{ 1058, 1057 },
	{ 1060, 1059 },
	{ 1061, 1059 },
	{ 1063, 1062 },
	{ 1064, 1062 },
	{ 1066, 1065 },
	{ 1067, 1065 },
	{ 1069, 1068 },
	{ 1071, 1070 },
	{ 1078, 1072 },
	{ 1079, 1073 },
	{ 1080, 1074 },
	{ 1081, 1075 },
	{ 1082, 1076 },
	{ 1083, 1077 },
	{ 1084, 1072 },
	{ 1085, 1073 },
	{ 1086, 1074 },
	{ 1087, 1075 },
	{ 1088, 1076 },
	{ 1089, 1077 },
	{ 1102, 1096 },
	{ 1103, 1097 },
	{ 1104, 1098 },
	{ 1105, 1099 },
	{ 1106, 1100 },
	{ 1107, 1101 },
	{ 1108, 1096 },
	{ 1109, 1097 },
	{ 1110, 1098 },
	{ 1111, 1099 },
	{ 1112, 1100 },
	{ 1113, 1101 },
	{ 1127, 1126 },
	{ 1128, 1126 },
	{ 1130, 1129 },
	{ 1131, 1129 },
	{ 1141, 1140 },
	{ 1142, 1140 },
	{ 1145, 1144 },
	{ 1146, 1144 },
	{ 1150, 1148 },
	{ 1151, 1149 },
	{ 1153, 1149 },
	{ 1167, 1169 },
	{ 1175, 1177 },
	{ 1178, 1180 },
	{ 1179, 1180 },
	{ 1181, 1183 },
	{ 1182, 1183 },
	{ 1191, 1185 },
	{ 1192, 1186 },
	{ 1193, 1187 },
	{ 1194, 1188 },
	{ 1195, 1189 },
	{ 1196, 1190 },
	{ 1197, 1185 },
	{ 1198, 1186 },
	{ 1199, 1187 },
	{ 1200, 1188 },
	{ 1201, 1189 },
	{ 1202, 1190 },
	{ 1215, 1209 },
	{ 1216, 1210 },
	{ 1217, 1211 },
	{ 1218, 1212 },
	{ 1219, 1213 },
	{ 1220, 1214 },
	{ 1221, 1209 },
	{ 1222, 1210 },
	{ 1223, 1211 },
	{ 1224, 1212 },
	{ 1225, 1213 },
	{ 1226, 1214 },
	{ 1245, 1239 },
	{ 1246, 1240 },
	{ 1247, 1241 },
	{ 1248, 1242 },
	{ 1249, 1243 },
	{ 1250, 1244 },
	{ 1251, 1239 },
	{ 1252, 1240 },
	{ 1253, 1241 },
	{ 1254, 1242 },
	{ 1255, 1243 },
	{ 1256, 1244 },
	{ 1270, 1264 },
	{ 1271, 1265 },
	{ 1272, 1266 },
	{ 1273, 1267 },
	{ 1274, 1268 },
	{ 1275, 1269 },
	{ 1276, 1264 },
	{ 1277, 1265 },
	{ 1278, 1266 },
	{ 1279, 1267 },
	{ 1280, 1268 },
	{ 1281, 1269 },
	{ 1301, 1292 },
	{ 1302, 1293 },
	{ 1303, 1294 },
	{ 1304, 1295 },
	{ 1305, 1296 },
	{ 1306, 1297 },
	{ 1307, 1298 },
	{ 1308, 1299 },
	{ 1309, 1300 },
	{ 1310, 1292 },
	{ 1312, 1294 },
	{ 1313, 1295 },
	{ 1315, 1297 },
	{ 1316, 1298 },
	{ 1317, 1299 },
	{ 1318, 1300 },
	{ 1329, 1331 },
	{ 1330, 1331 },
	{ 1349, 1348 },
	{ 1350, 1348 },
	{ 1379, 1378 },
	{ 1380, 1378 },
	{ 1387, 1381 },
	{ 1388, 1382 },
	{ 1389, 1383 },
	{ 1390, 1384 },
	{ 1391, 1385 },
	{ 1392, 1386 },
	{ 1393, 1381 },
	{ 1394, 1382 },
	{ 1395, 1383 },
	{ 1396, 1384 },
	{ 1397, 1385 },
	{ 1398, 1386 },
	{ 1409, 1405 },
	{ 1410, 1406 },
	{ 1411, 1407 },
	{ 1412, 1408 },
	{ 1413, 1405 },
	{ 1414, 1406 },
	{ 1415, 1407 },
	{ 1416, 1408 },
	{ 1427, 1421 },
	{ 1428, 1422 },
	{ 1429, 1423 },
	{ 1430, 1424 },
	{ 1431, 1425 },
	{ 1432, 1426 },
	{ 1433, 1421 },
	{ 1434, 1422 },
	{ 1435, 1423 },
	{ 1436, 1424 },
	{ 1437, 1425 },
	{ 1438, 1426 },
	{ 1449, 1445 },
	{ 1450, 1446 },
	{ 1451, 1447 },
	{ 1452, 1448 },
	{ 1453, 1445 },
	{ 1454, 1446 },
	{ 1455, 1447 },
	{ 1456, 1448 },
	{ 1462, 1461 },
	{ 1463, 1461 },
	{ 1467, 1466 },
	{ 1468, 1465 },
	{ 1469, 1466 },
	{ 1472, 1471 },
	{ 1473, 1471 },
	{ 1480, 1482 },
	{ 1481, 1482 },
	{ 1484, 1483 },
	{ 1485, 1483 },
	{ 1496, 1487 },
	{ 1497, 1488 },
	{ 1498, 1489 },
	{ 1499, 1490 },
	{ 1500, 1491 },
	{ 1501, 1492 },
	{ 1502, 1493 },
	{ 1503, 1494 },
	{ 1504, 1495 },
	{ 1505, 1487 },
	{ 1507, 1489 },
	{ 1508, 1490 },
	{ 1510, 1492 },
	{ 1511, 1493 },
	{ 1512, 1494 },
	{ 1513, 1495 },
	{ 1556, 1549 },
	{ 1557, 1550 },
	{ 1558, 1551 },
	{ 1559, 1552 },
	{ 1560, 1553 },
	{ 1561, 1554 },
	{ 1562, 1555 },
	{ 1563, 1549 },
	{ 1568, 1554 },
	{ 1569, 1555 },
	{ 1587, 1585 },
	{ 1588, 1586 },
	{ 1589, 1585 },
	{ 1590, 1586 },
	{ 1637, 1635 },
	{ 1638, 1636 },
	{ 1639, 1635 },
	{ 1640, 1636 },
	{ 1646, 1643 },
	{ 1648, 1644 },
	{ 1649, 1645 },
	{ 1650, 1643 },
	{ 1651, 1644 },
	{ 1652, 1645 },
	{ 1671, 1662 },
	{ 1672, 1663 },
	{ 1673, 1664 },
	{ 1674, 1665 },
	{ 1675, 1666 },
	{ 1676, 1667 },
	{ 1677, 1668 },
	{ 1678, 1669 },
	{ 1679, 1670 },
	{ 1680, 1662 },
	{ 1682, 1664 },
	{ 1683, 1665 },
	{ 1685, 1667 },
	{ 1686, 1668 },
	{ 1687, 1669 },
	{ 1688, 1670 },
};

static const uint16_t x86_16_bit_eq_lookup[] = {
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 1, 2, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 
	12, 0, 13, 14, 0, 15, 16, 17, 18, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 20, 
	21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 0, 
	32, 33, 0, 0, 34, 35, 36, 37, 38, 39, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 0, 50, 
	51, 0, 52, 53, 54, 55, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 56, 0, 
	0, 57, 58, 59, 60, 0, 0, 61, 62, 63, 64, 0, 
	0, 0, 0, 0, 0, 65, 66, 67, 68, 69, 70, 71, 
	72, 0, 0, 0, 0, 73, 74, 75, 76, 77, 78, 79, 
	80, 0, 0, 0, 0, 81, 82, 83, 84, 85, 86, 87, 
	88, 0, 0, 0, 0, 89, 90, 91, 92, 93, 94, 95, 
	96, 0, 0, 0, 0, 0, 0, 97, 98, 99, 0, 100, 
	0, 101, 0, 0, 0, 0, 0, 102, 0, 0, 0, 0, 
	0, 0, 0, 0, 103, 104, 105, 106, 0, 0, 107, 108, 
	109, 110, 0, 0, 111, 112, 113, 114, 0, 0, 115, 116, 
	117, 118, 0, 0, 119, 120, 121, 122, 0, 0, 123, 124, 
	125, 126, 0, 0, 127, 128, 129, 130, 0, 0, 131, 132, 
	133, 134, 0, 0, 135, 136, 137, 138, 0, 0, 139, 140, 
	141, 142, 0, 0, 143, 144, 145, 146, 0, 0, 147, 148, 
	149, 150, 0, 0, 151, 152, 153, 154, 0, 0, 155, 156, 
	157, 158, 0, 0, 159, 160, 161, 162, 0, 0, 163, 164, 
	165, 166, 0, 0, 0, 167, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 168, 169, 170, 171, 172, 173, 174, 175, 
	176, 177, 0, 178, 179, 0, 180, 181, 182, 183, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 184, 185, 0, 0, 
	0, 0, 186, 187, 188, 189, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 190, 191, 192, 193, 194, 
	0, 0, 0, 0, 195, 196, 197, 198, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 199, 200, 0, 0, 
	0, 201, 202, 0, 0, 0, 0, 0, 0, 203, 204, 205, 
	206, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 207, 
	208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 0, 218, 
	219, 0, 220, 0, 0, 0, 0, 221, 222, 0, 0, 0, 
	0, 0, 223, 224, 225, 226, 227, 0, 0, 0, 228, 0, 
	0, 0, 229, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 230, 231, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 232, 233, 234, 235, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 236, 237, 238, 
	239, 0, 0, 240, 241, 0, 0, 0, 242, 0, 243, 0, 
	244, 0, 0, 0, 245, 0, 246, 247, 0, 248, 249, 0, 
	250, 251, 0, 252, 253, 0, 0, 0, 0, 0, 0, 0, 
	254, 255, 256, 0, 257, 258, 0, 0, 0, 0, 0, 259, 
	260, 261, 0, 262, 263, 0, 0, 0, 264, 265, 0, 0, 
	266, 267, 0, 0, 0, 0, 268, 269, 270, 0, 271, 272, 
	0, 0, 0, 0, 0, 0, 273, 274, 275, 0, 276, 277, 
	0, 0, 0, 0, 0, 278, 279, 280, 0, 281, 282, 0, 
	0, 0, 283, 284, 0, 0, 0, 0, 285, 286, 0, 287, 
	288, 0, 0, 0, 289, 290, 291, 292, 0, 293, 294, 0, 
	0, 0, 295, 296, 0, 0, 0, 297, 298, 299, 300, 0, 
	0, 0, 0, 301, 302, 0, 0, 0, 0, 303, 304, 0, 
	0, 0, 0, 0, 0, 0, 0, 305, 306, 309, 0, 0, 
	312, 313, 314, 315, 316, 319, 0, 0, 0, 322, 0, 323, 
	324, 325, 326, 327, 328, 329, 330, 332, 0, 0, 0, 334, 
	335, 336, 338, 0, 0, 340, 0, 341, 342, 343, 344, 345, 
	346, 0, 347, 348, 0, 0, 0, 0, 349, 350, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 351, 352, 353, 354, 0, 
	0, 355, 356, 0, 0, 0, 0, 0, 0, 357, 0, 358, 
	0, 0, 359, 0, 360, 0, 361, 362, 0, 0, 0, 0, 
	0, 363, 0, 364, 0, 0, 0, 0, 0, 0, 365, 366, 
	367, 368, 0, 0, 0, 0, 0, 0, 0, 0, 369, 370, 
	371, 372, 0, 0, 0, 373, 0, 0, 0, 374, 0, 0, 
	0, 375, 0, 0, 0, 376, 0, 0, 0, 0, 377, 378, 
	379, 380, 381, 382, 383, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 384, 385, 386, 387, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 388, 389, 390, 391, 0, 392, 393, 
	394, 395, 396, 397, 0, 398, 399, 0, 400, 401, 402, 403, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 404, 
	405, 0, 0, 0, 406, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 407, 408, 409, 410, 411, 412, 
	0, 413, 0, 414, 0, 415, 0, 416, 417, 0, 418, 419, 
	0, 420, 421, 0, 422, 0, 0, 0, 0, 423, 424, 425, 
	426, 0, 427, 428, 429, 430, 431, 0, 432, 0, 433, 0, 
	434, 0, 435, 0, 436, 437, 0, 438, 439, 0, 440, 441, 
	0, 442, 0, 443, 0, 0, 0, 0, 0, 0, 444, 445, 
	446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 456, 457, 
	458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 468, 
	469, 0, 470, 471, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 472, 473, 0, 0, 474, 475, 0, 0, 0, 476, 477, 
	0, 478, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 479, 0, 0, 0, 0, 0, 0, 0, 480, 
	0, 0, 481, 482, 0, 483, 484, 0, 0, 0, 0, 0, 
	0, 0, 0, 485, 486, 487, 488, 489, 490, 491, 492, 493, 
	494, 495, 496, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 497, 498, 499, 500, 501, 502, 503, 504, 505, 
	506, 507, 508, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 509, 510, 511, 
	512, 513, 514, 515, 516, 517, 518, 519, 520, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 521, 522, 
	523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 533, 534, 535, 536, 537, 538, 539, 
	540, 541, 542, 0, 543, 544, 0, 545, 546, 547, 548, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 549, 550, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 551, 552, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 553, 
	554, 0, 0, 0, 0, 0, 0, 555, 556, 557, 558, 559, 
	560, 561, 562, 563, 564, 565, 566, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 567, 568, 569, 570, 571, 572, 573, 
	574, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 575, 
	576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 587, 588, 589, 
	590, 591, 592, 593, 594, 0, 0, 0, 0, 0, 595, 596, 
	0, 0, 0, 597, 598, 599, 0, 0, 600, 601, 0, 0, 
	0, 0, 0, 0, 602, 603, 0, 0, 604, 605, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 606, 607, 608, 609, 
	610, 611, 612, 613, 614, 615, 0, 616, 617, 0, 618, 619, 
	620, 621, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 622, 623, 624, 625, 
	626, 627, 628, 629, 0, 0, 0, 0, 630, 631, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 632, 633, 634, 635, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 636, 637, 638, 639, 0, 0, 0, 
	0, 0, 640, 0, 641, 642, 643, 644, 645, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 646, 647, 648, 649, 650, 651, 652, 653, 654, 
	655, 0, 656, 657, 0, 658, 659, 660, 661, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, };

static const bool is_64bit_insn[] = {
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	true,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	true,
	true,
	false,
	true,
	true,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	true,
	false,
	true,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	true,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	true,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	true,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	true,
	false,
	true,
	true,
	false,
	true,
	false,
	false,
	true,
	false,
	true,
	true,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	true,
	false,
	false,
	true,
	true,
	false,
	true,
	true,
	false,
	true,
	true,
	false,
	true,
	true,
	false,
	true,
	false,
	true,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	true,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	false,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	true,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	true,
	false,
	true,
	false,
	true,
	false,
	true,
	false,
	true,
	false,
	false,
	false,
	false,
};

#endif // GET_INSTRINFO_MC_DESC
