/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Subtarget Enumeration Source Fragment                                       *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine, http://www.capstone-engine.org */
/* By <PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2013-2015 */


#ifdef GET_SUBTARGETINFO_ENUM
#undef GET_SUBTARGETINFO_ENUM

enum {
  AArch64_FeatureCRC =  1ULL << 0,
  AArch64_FeatureCrypto =  1ULL << 1,
  AArch64_FeatureFPARMv8 =  1ULL << 2,
  AArch64_FeatureNEON =  1ULL << 3,
  AArch64_FeatureZCRegMove =  1ULL << 4,
  AArch64_FeatureZCZeroing =  1ULL << 5,
  AArch64_ProcA53 =  1ULL << 6,
  AArch64_ProcA57 =  1ULL << 7,
  AArch64_ProcCyclone =  1ULL << 8
};

#endif // GET_SUBTARGETINFO_ENUM

