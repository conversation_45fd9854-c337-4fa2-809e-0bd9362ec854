/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Assembly Writer Source Fragment                                             *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

#include <stdio.h>

/// printInstruction - This method is automatically generated by tablegen
/// from the instruction set description.
static void printInstruction(MCInst *MI, SStream *O, MCRegisterInfo *MRI) {
  static const uint32_t OpInfo[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    882U,	// DBG_VALUE
    0U,	// REG_SEQUENCE
    0U,	// COPY
    875U,	// BUNDLE
    904U,	// LIFETIME_START
    862U,	// LIFETIME_END
    0U,	// STACKMAP
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    0U,	// STATEPOINT
    0U,	// FRAME_ALLOC
    1126U,	// ABS2_l2_rr
    10847U,	// ABS_l1_pp
    1631U,	// ABS_l1_rr
    85006U,	// ADD2_d2_rrr
    85006U,	// ADD2_l1_rrr_x2
    85006U,	// ADD2_s1_rrr
    85171U,	// ADD4_l1_rrr_x2
    91479U,	// ADDAB_d1_rir
    91479U,	// ADDAB_d1_rrr
    91541U,	// ADDAD_d1_rir
    91541U,	// ADDAD_d1_rrr
    91577U,	// ADDAH_d1_rir
    91577U,	// ADDAH_d1_rrr
    91937U,	// ADDAW_d1_rir
    91937U,	// ADDAW_d1_rrr
    132488U,	// ADDKPC_s3_iir
    1518U,	// ADDK_s2_ir
    233140U,	// ADDU_l1_rpp
    216756U,	// ADDU_l1_rrp_x2
    91555U,	// ADD_d1_rir
    91555U,	// ADD_d1_rrr
    91555U,	// ADD_d2_rir
    85411U,	// ADD_d2_rrr
    232867U,	// ADD_l1_ipp
    85411U,	// ADD_l1_irr
    232867U,	// ADD_l1_rpp
    216483U,	// ADD_l1_rrp_x2
    85411U,	// ADD_l1_rrr_x2
    85411U,	// ADD_s1_irr
    85411U,	// ADD_s1_rrr
    85542U,	// ANDN_d2_rrr
    85542U,	// ANDN_l1_rrr_x2
    85542U,	// ANDN_s4_rrr
    85416U,	// AND_d2_rir
    85416U,	// AND_d2_rrr
    85416U,	// AND_l1_irr
    85416U,	// AND_l1_rrr_x2
    85416U,	// AND_s1_irr
    85416U,	// AND_s1_rrr
    85019U,	// AVG2_m1_rrr
    85232U,	// AVGU4_m1_rrr
    1410U,	// BDEC_s8_ir
    1196U,	// BITC4_m2_rr
    307756U,	// BNOP_s10_ri
    307756U,	// BNOP_s9_ii
    1654U,	// BPOS_s8_ir
    53588U,	// B_s5_i
    53588U,	// B_s6_r
    892U,	// B_s7_irp
    898U,	// B_s7_nrp
    353870U,	// CLR_s15_riir
    91726U,	// CLR_s1_rrr
    85080U,	// CMPEQ2_s1_rrr
    85207U,	// CMPEQ4_s1_rrr
    101938U,	// CMPEQ_l1_ipr
    85554U,	// CMPEQ_l1_irr
    101938U,	// CMPEQ_l1_rpr
    85554U,	// CMPEQ_l1_rrr_x2
    85109U,	// CMPGT2_s1_rrr
    85298U,	// CMPGTU4_s1_rrr
    102037U,	// CMPGT_l1_ipr
    85653U,	// CMPGT_l1_irr
    102037U,	// CMPGT_l1_rpr
    85653U,	// CMPGT_l1_rrr_x2
    102150U,	// CMPLTU_l1_ipr
    85766U,	// CMPLTU_l1_irr
    102150U,	// CMPLTU_l1_rpr
    85766U,	// CMPLTU_l1_rrr_x2
    102044U,	// CMPLT_l1_ipr
    85660U,	// CMPLT_l1_irr
    102044U,	// CMPLT_l1_rpr
    85660U,	// CMPLT_l1_rrr_x2
    1529U,	// DEAL_m2_rr
    216145U,	// DOTP2_m1_rrp
    85073U,	// DOTP2_m1_rrr
    85065U,	// DOTPN2_m1_rrr
    85124U,	// DOTPNRSU2_m1_rrr
    85135U,	// DOTPRSU2_m1_rrr
    85281U,	// DOTPSU4_m1_rrr
    85273U,	// DOTPU4_m1_rrr
    354062U,	// EXTU_s15_riir
    91918U,	// EXTU_s1_rrr
    353955U,	// EXT_s15_riir
    91811U,	// EXT_s1_rrr
    102142U,	// GMPGTU_l1_ipr
    85758U,	// GMPGTU_l1_irr
    102142U,	// GMPGTU_l1_rpr
    85758U,	// GMPGTU_l1_rrr_x2
    85321U,	// GMPY4_m1_rrr
    5800U,	// LDBU_d5_mr
    6824U,	// LDBU_d6_mr
    5470U,	// LDB_d5_mr
    6494U,	// LDB_d6_mr
    14120U,	// LDDW_d7_mp
    5818U,	// LDHU_d5_mr
    6842U,	// LDHU_d6_mr
    5568U,	// LDH_d5_mr
    6592U,	// LDH_d6_mr
    14131U,	// LDNDW_d8_mp
    5959U,	// LDNW_d5_mr
    5934U,	// LDW_d5_mr
    6958U,	// LDW_d6_mr
    85404U,	// LMBD_l1_irr
    85404U,	// LMBD_l1_rrr_x2
    85145U,	// MAX2_l1_rrr_x2
    85307U,	// MAXU4_l1_rrr_x2
    85059U,	// MIN2_l1_rrr_x2
    85266U,	// MINU4_l1_rrr_x2
    216224U,	// MPY2_m1_rrp
    85566U,	// MPYHIR_m1_rrr
    216544U,	// MPYHI_m1_rrp
    85720U,	// MPYHLU_m4_rrr
    85516U,	// MPYHL_m4_rrr
    85728U,	// MPYHSLU_m4_rrr
    85743U,	// MPYHSU_m4_rrr
    85613U,	// MPYHULS_m4_rrr
    85628U,	// MPYHUS_m4_rrr
    85713U,	// MPYHU_m4_rrr
    85466U,	// MPYH_m4_rrr
    85696U,	// MPYLHU_m4_rrr
    85453U,	// MPYLH_m4_rrr
    85574U,	// MPYLIR_m1_rrr
    216551U,	// MPYLI_m1_rrp
    85704U,	// MPYLSHU_m4_rrr
    85604U,	// MPYLUHS_m4_rrr
    216362U,	// MPYSU4_m1_rrp
    85751U,	// MPYSU_m4_irr
    85751U,	// MPYSU_m4_rrr
    216386U,	// MPYU4_m1_rrp
    85636U,	// MPYUS_m4_rrr
    85780U,	// MPYU_m4_rrr
    85849U,	// MPY_m4_irr
    85849U,	// MPY_m4_rrr
    1424U,	// MVC_s1_rr
    1424U,	// MVC_s1_rr2
    1453U,	// MVD_m2_rr
    1477U,	// MVKLH_s12_ir
    1524U,	// MVKL_s12_ir
    1524U,	// MVK_d1_rr
    1524U,	// MVK_l2_ir
    53249U,	// NOP_n
    2592U,	// NORM_l1_pr
    1568U,	// NORM_l1_rr
    85588U,	// OR_d2_rir
    85588U,	// OR_d2_rrr
    85588U,	// OR_l1_irr
    85588U,	// OR_l1_rrr_x2
    85588U,	// OR_s1_irr
    85588U,	// OR_s1_rrr
    85043U,	// PACK2_l1_rrr_x2
    85043U,	// PACK2_s4_rrr
    85025U,	// PACKH2_l1_rrr_x2
    85025U,	// PACKH2_s1_rrr
    85184U,	// PACKH4_l1_rrr_x2
    85050U,	// PACKHL2_l1_rrr_x2
    85050U,	// PACKHL2_s1_rrr
    85192U,	// PACKL4_l1_rrr_x2
    85033U,	// PACKLH2_l1_rrr_x2
    85033U,	// PACKLH2_s1_rrr
    91667U,	// ROTL_m1_rir
    91667U,	// ROTL_m1_rrr
    85005U,	// SADD2_s4_rrr
    85224U,	// SADDU4_s4_rrr
    85100U,	// SADDUS2_s4_rrr
    232866U,	// SADD_l1_ipp
    85410U,	// SADD_l1_irr
    232866U,	// SADD_l1_rpp
    85410U,	// SADD_l1_rrr_x2
    85410U,	// SADD_s1_rrr
    2699U,	// SAT_l1_pr
    353936U,	// SET_s15_riir
    91792U,	// SET_s1_rrr
    1535U,	// SHFL_m2_rr
    85347U,	// SHLMB_l1_rrr_x2
    85347U,	// SHLMB_s4_rrr
    223750U,	// SHL_s1_pip
    223750U,	// SHL_s1_prp
    222726U,	// SHL_s1_rip
    91654U,	// SHL_s1_rir
    222726U,	// SHL_s1_rrp
    91654U,	// SHL_s1_rrr
    91232U,	// SHR2_s1_rir
    91232U,	// SHR2_s4_rrr
    85354U,	// SHRMB_l1_rrr_x2
    85354U,	// SHRMB_s4_rrr
    91261U,	// SHRU2_s1_rir
    91261U,	// SHRU2_s4_rrr
    223977U,	// SHRU_s1_pip
    223977U,	// SHRU_s1_prp
    91881U,	// SHRU_s1_rir
    91881U,	// SHRU_s1_rrr
    223801U,	// SHR_s1_pip
    223801U,	// SHR_s1_prp
    91705U,	// SHR_s1_rir
    91705U,	// SHR_s1_rrr
    216223U,	// SMPY2_m1_rrp
    85515U,	// SMPYHL_m4_rrr
    85465U,	// SMPYH_m4_rrr
    85452U,	// SMPYLH_m4_rrr
    85848U,	// SMPY_m4_rrr
    85042U,	// SPACK2_s4_rrr
    85248U,	// SPACKU4_s4_rrr
    91653U,	// SSHL_s1_rir
    91653U,	// SSHL_s1_rrr
    85529U,	// SSHVL_m1_rrr
    85592U,	// SSHVR_m1_rrr
    232822U,	// SSUB_l1_ipp
    85366U,	// SSUB_l1_irr
    85366U,	// SSUB_l1_rrr_x1
    85366U,	// SSUB_l1_rrr_x2
    438641U,	// STB_d5_rm
    504177U,	// STB_d6_rm
    8001U,	// STDW_d7_pm
    438740U,	// STH_d5_rm
    504276U,	// STH_d6_rm
    7994U,	// STNDW_d8_pm
    439117U,	// STNW_d5_rm
    439123U,	// STW_d5_rm
    504659U,	// STW_d6_rm
    84999U,	// SUB2_d2_rrr
    84999U,	// SUB2_l1_rrr_x2
    84999U,	// SUB2_s1_rrr
    85158U,	// SUB4_l1_rrr_x2
    85215U,	// SUBABS4_l1_rrr_x2
    91472U,	// SUBAB_d1_rir
    91472U,	// SUBAB_d1_rrr
    91472U,	// SUBAH_d1_rir
    91570U,	// SUBAH_d1_rrr
    91472U,	// SUBAW_d1_rir
    91930U,	// SUBAW_d1_rrr
    85372U,	// SUBC_l1_rrr_x2
    216750U,	// SUBU_l1_rrp_x1
    216750U,	// SUBU_l1_rrp_x2
    91511U,	// SUB_d1_rir
    91511U,	// SUB_d1_rrr
    85367U,	// SUB_d2_rrr
    232823U,	// SUB_l1_ipp
    85367U,	// SUB_l1_irr
    216439U,	// SUB_l1_rrp_x1
    216439U,	// SUB_l1_rrp_x2
    85367U,	// SUB_l1_rrr_x1
    85367U,	// SUB_l1_rrr_x2
    85367U,	// SUB_s1_irr
    85367U,	// SUB_s1_rrr
    91511U,	// SUB_s4_rrr
    1232U,	// SWAP4_l2_rr
    1271U,	// UNPKHU4_l2_rr
    1271U,	// UNPKHU4_s14_rr
    1289U,	// UNPKLU4_l2_rr
    1289U,	// UNPKLU4_s14_rr
    85587U,	// XOR_d2_rir
    85587U,	// XOR_d2_rrr
    85587U,	// XOR_l1_irr
    85587U,	// XOR_l1_rrr_x2
    85587U,	// XOR_s1_irr
    85587U,	// XOR_s1_rrr
    1044U,	// XPND2_m2_rr
    1209U,	// XPND4_m2_rr
    0U
  };

  static char AsmStrs[] = {
  /* 0 */ 'N', 'O', 'P', 9, 9, 0,
  /* 6 */ 'S', 'U', 'B', '2', 9, 0,
  /* 12 */ 'S', 'A', 'D', 'D', '2', 9, 0,
  /* 19 */ 'X', 'P', 'N', 'D', '2', 9, 0,
  /* 26 */ 'A', 'V', 'G', '2', 9, 0,
  /* 32 */ 'P', 'A', 'C', 'K', 'H', '2', 9, 0,
  /* 40 */ 'P', 'A', 'C', 'K', 'L', 'H', '2', 9, 0,
  /* 49 */ 'S', 'P', 'A', 'C', 'K', '2', 9, 0,
  /* 57 */ 'P', 'A', 'C', 'K', 'H', 'L', '2', 9, 0,
  /* 66 */ 'M', 'I', 'N', '2', 9, 0,
  /* 72 */ 'D', 'O', 'T', 'P', 'N', '2', 9, 0,
  /* 80 */ 'D', 'O', 'T', 'P', '2', 9, 0,
  /* 87 */ 'C', 'M', 'P', 'E', 'Q', '2', 9, 0,
  /* 95 */ 'S', 'H', 'R', '2', 9, 0,
  /* 101 */ 'A', 'B', 'S', '2', 9, 0,
  /* 107 */ 'S', 'A', 'D', 'D', 'U', 'S', '2', 9, 0,
  /* 116 */ 'C', 'M', 'P', 'G', 'T', '2', 9, 0,
  /* 124 */ 'S', 'H', 'R', 'U', '2', 9, 0,
  /* 131 */ 'D', 'O', 'T', 'P', 'N', 'R', 'S', 'U', '2', 9, 0,
  /* 142 */ 'D', 'O', 'T', 'P', 'R', 'S', 'U', '2', 9, 0,
  /* 152 */ 'M', 'A', 'X', '2', 9, 0,
  /* 158 */ 'S', 'M', 'P', 'Y', '2', 9, 0,
  /* 165 */ 'S', 'U', 'B', '4', 9, 0,
  /* 171 */ 'B', 'I', 'T', 'C', '4', 9, 0,
  /* 178 */ 'A', 'D', 'D', '4', 9, 0,
  /* 184 */ 'X', 'P', 'N', 'D', '4', 9, 0,
  /* 191 */ 'P', 'A', 'C', 'K', 'H', '4', 9, 0,
  /* 199 */ 'P', 'A', 'C', 'K', 'L', '4', 9, 0,
  /* 207 */ 'S', 'W', 'A', 'P', '4', 9, 0,
  /* 214 */ 'C', 'M', 'P', 'E', 'Q', '4', 9, 0,
  /* 222 */ 'S', 'U', 'B', 'A', 'B', 'S', '4', 9, 0,
  /* 231 */ 'S', 'A', 'D', 'D', 'U', '4', 9, 0,
  /* 239 */ 'A', 'V', 'G', 'U', '4', 9, 0,
  /* 246 */ 'U', 'N', 'P', 'K', 'H', 'U', '4', 9, 0,
  /* 255 */ 'S', 'P', 'A', 'C', 'K', 'U', '4', 9, 0,
  /* 264 */ 'U', 'N', 'P', 'K', 'L', 'U', '4', 9, 0,
  /* 273 */ 'M', 'I', 'N', 'U', '4', 9, 0,
  /* 280 */ 'D', 'O', 'T', 'P', 'U', '4', 9, 0,
  /* 288 */ 'D', 'O', 'T', 'P', 'S', 'U', '4', 9, 0,
  /* 297 */ 'M', 'P', 'Y', 'S', 'U', '4', 9, 0,
  /* 305 */ 'C', 'M', 'P', 'G', 'T', 'U', '4', 9, 0,
  /* 314 */ 'M', 'A', 'X', 'U', '4', 9, 0,
  /* 321 */ 'M', 'P', 'Y', 'U', '4', 9, 0,
  /* 328 */ 'G', 'M', 'P', 'Y', '4', 9, 0,
  /* 335 */ 'S', 'U', 'B', 'A', 'B', 9, 0,
  /* 342 */ 'A', 'D', 'D', 'A', 'B', 9, 0,
  /* 349 */ 'L', 'D', 'B', 9, 0,
  /* 354 */ 'S', 'H', 'L', 'M', 'B', 9, 0,
  /* 361 */ 'S', 'H', 'R', 'M', 'B', 9, 0,
  /* 368 */ 'S', 'T', 'B', 9, 0,
  /* 373 */ 'S', 'S', 'U', 'B', 9, 0,
  /* 379 */ 'S', 'U', 'B', 'C', 9, 0,
  /* 385 */ 'B', 'D', 'E', 'C', 9, 0,
  /* 391 */ 'A', 'D', 'D', 'K', 'P', 'C', 9, 0,
  /* 399 */ 'M', 'V', 'C', 9, 0,
  /* 404 */ 'A', 'D', 'D', 'A', 'D', 9, 0,
  /* 411 */ 'L', 'M', 'B', 'D', 9, 0,
  /* 417 */ 'S', 'A', 'D', 'D', 9, 0,
  /* 423 */ 'A', 'N', 'D', 9, 0,
  /* 428 */ 'M', 'V', 'D', 9, 0,
  /* 433 */ 'S', 'U', 'B', 'A', 'H', 9, 0,
  /* 440 */ 'A', 'D', 'D', 'A', 'H', 9, 0,
  /* 447 */ 'L', 'D', 'H', 9, 0,
  /* 452 */ 'M', 'V', 'K', 'L', 'H', 9, 0,
  /* 459 */ 'S', 'M', 'P', 'Y', 'L', 'H', 9, 0,
  /* 467 */ 'S', 'T', 'H', 9, 0,
  /* 472 */ 'S', 'M', 'P', 'Y', 'H', 9, 0,
  /* 479 */ 'M', 'P', 'Y', 'H', 'I', 9, 0,
  /* 486 */ 'M', 'P', 'Y', 'L', 'I', 9, 0,
  /* 493 */ 'A', 'D', 'D', 'K', 9, 0,
  /* 499 */ 'M', 'V', 'K', 9, 0,
  /* 504 */ 'D', 'E', 'A', 'L', 9, 0,
  /* 510 */ 'S', 'H', 'F', 'L', 9, 0,
  /* 516 */ 'S', 'S', 'H', 'L', 9, 0,
  /* 522 */ 'S', 'M', 'P', 'Y', 'H', 'L', 9, 0,
  /* 530 */ 'R', 'O', 'T', 'L', 9, 0,
  /* 536 */ 'S', 'S', 'H', 'V', 'L', 9, 0,
  /* 543 */ 'N', 'O', 'R', 'M', 9, 0,
  /* 549 */ 'A', 'N', 'D', 'N', 9, 0,
  /* 555 */ 'B', 'N', 'O', 'P', 9, 0,
  /* 561 */ 'C', 'M', 'P', 'E', 'Q', 9, 0,
  /* 568 */ 'S', 'H', 'R', 9, 0,
  /* 573 */ 'M', 'P', 'Y', 'H', 'I', 'R', 9, 0,
  /* 581 */ 'M', 'P', 'Y', 'L', 'I', 'R', 9, 0,
  /* 589 */ 'C', 'L', 'R', 9, 0,
  /* 594 */ 'X', 'O', 'R', 9, 0,
  /* 599 */ 'S', 'S', 'H', 'V', 'R', 9, 0,
  /* 606 */ 'A', 'B', 'S', 9, 0,
  /* 611 */ 'M', 'P', 'Y', 'L', 'U', 'H', 'S', 9, 0,
  /* 620 */ 'M', 'P', 'Y', 'H', 'U', 'L', 'S', 9, 0,
  /* 629 */ 'B', 'P', 'O', 'S', 9, 0,
  /* 635 */ 'M', 'P', 'Y', 'H', 'U', 'S', 9, 0,
  /* 643 */ 'M', 'P', 'Y', 'U', 'S', 9, 0,
  /* 650 */ 'S', 'A', 'T', 9, 0,
  /* 655 */ 'S', 'E', 'T', 9, 0,
  /* 660 */ 'C', 'M', 'P', 'G', 'T', 9, 0,
  /* 667 */ 'C', 'M', 'P', 'L', 'T', 9, 0,
  /* 674 */ 'E', 'X', 'T', 9, 0,
  /* 679 */ 'L', 'D', 'B', 'U', 9, 0,
  /* 685 */ 'S', 'U', 'B', 'U', 9, 0,
  /* 691 */ 'A', 'D', 'D', 'U', 9, 0,
  /* 697 */ 'L', 'D', 'H', 'U', 9, 0,
  /* 703 */ 'M', 'P', 'Y', 'L', 'H', 'U', 9, 0,
  /* 711 */ 'M', 'P', 'Y', 'L', 'S', 'H', 'U', 9, 0,
  /* 720 */ 'M', 'P', 'Y', 'H', 'U', 9, 0,
  /* 727 */ 'M', 'P', 'Y', 'H', 'L', 'U', 9, 0,
  /* 735 */ 'M', 'P', 'Y', 'H', 'S', 'L', 'U', 9, 0,
  /* 744 */ 'S', 'H', 'R', 'U', 9, 0,
  /* 750 */ 'M', 'P', 'Y', 'H', 'S', 'U', 9, 0,
  /* 758 */ 'M', 'P', 'Y', 'S', 'U', 9, 0,
  /* 765 */ 'C', 'M', 'P', 'G', 'T', 'U', 9, 0,
  /* 773 */ 'C', 'M', 'P', 'L', 'T', 'U', 9, 0,
  /* 781 */ 'E', 'X', 'T', 'U', 9, 0,
  /* 787 */ 'M', 'P', 'Y', 'U', 9, 0,
  /* 793 */ 'S', 'U', 'B', 'A', 'W', 9, 0,
  /* 800 */ 'A', 'D', 'D', 'A', 'W', 9, 0,
  /* 807 */ 'L', 'D', 'D', 'W', 9, 0,
  /* 813 */ 'L', 'D', 'W', 9, 0,
  /* 818 */ 'L', 'D', 'N', 'D', 'W', 9, 0,
  /* 825 */ 'S', 'T', 'N', 'D', 'W', 9, 0,
  /* 832 */ 'S', 'T', 'D', 'W', 9, 0,
  /* 838 */ 'L', 'D', 'N', 'W', 9, 0,
  /* 844 */ 'S', 'T', 'N', 'W', 9, 0,
  /* 850 */ 'S', 'T', 'W', 9, 0,
  /* 855 */ 'S', 'M', 'P', 'Y', 9, 0,
  /* 861 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'E', 'N', 'D', 0,
  /* 874 */ 'B', 'U', 'N', 'D', 'L', 'E', 0,
  /* 881 */ 'D', 'B', 'G', '_', 'V', 'A', 'L', 'U', 'E', 0,
  /* 891 */ 'B', 9, 'I', 'R', 'P', 0,
  /* 897 */ 'B', 9, 'N', 'R', 'P', 0,
  /* 903 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'S', 'T', 'A', 'R', 'T', 0,
  };

  // Emit the opcode for the instruction.
  uint32_t Bits = OpInfo[MCInst_getOpcode(MI)];
  // assert(Bits != 0 && "Cannot print this instruction.");
#ifndef CAPSTONE_DIET
  SStream_concat0(O, AsmStrs+(Bits & 1023)-1);
#endif


  // Fragment 0 encoded into 3 bits for 8 unique commands.
  switch ((Bits >> 10) & 7) {
  default:
  case 0:
    // DBG_VALUE, BUNDLE, LIFETIME_START, LIFETIME_END, B_s7_irp, B_s7_nrp
    return;
    break;
  case 1:
    // ABS2_l2_rr, ABS_l1_rr, ADDAB_d1_rir, ADDAB_d1_rrr, ADDAD_d1_rir, ADDAD...
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", ");
    break;
  case 2:
    // ABS_l1_pp, NORM_l1_pr, SAT_l1_pr, SHL_s1_pip, SHL_s1_prp, SHRU_s1_pip,...
    printRegPair(MI, 1, O); 
    SStream_concat0(O, ", ");
    break;
  case 3:
    // ADD2_d2_rrr, ADD2_l1_rrr_x2, ADD2_s1_rrr, ADD4_l1_rrr_x2, ADDU_l1_rpp,...
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", ");
    break;
  case 4:
    // BNOP_s10_ri, BNOP_s9_ii, B_s5_i, B_s6_r, NOP_n, STB_d5_rm, STB_d6_rm, ...
    printOperand(MI, 0, O); 
    break;
  case 5:
    // LDBU_d5_mr, LDB_d5_mr, LDDW_d7_mp, LDHU_d5_mr, LDH_d5_mr, LDNDW_d8_mp,...
    printMemOperand(MI, 1, O); 
    SStream_concat0(O, ", ");
    break;
  case 6:
    // LDBU_d6_mr, LDB_d6_mr, LDHU_d6_mr, LDH_d6_mr, LDW_d6_mr
    printMemOperand2(MI, 1, O); 
    SStream_concat0(O, ", ");
    printOperand(MI, 0, O); 
    return;
    break;
  case 7:
    // STDW_d7_pm, STNDW_d8_pm
    printRegPair(MI, 0, O); 
    SStream_concat0(O, ", ");
    printMemOperand(MI, 1, O); 
    return;
    break;
  }


  // Fragment 1 encoded into 3 bits for 7 unique commands.
  switch ((Bits >> 13) & 7) {
  default:
  case 0:
    // ABS2_l2_rr, ABS_l1_rr, ADDKPC_s3_iir, ADDK_s2_ir, BDEC_s8_ir, BITC4_m2...
    printOperand(MI, 0, O); 
    break;
  case 1:
    // ABS_l1_pp, LDDW_d7_mp, LDNDW_d8_mp
    printRegPair(MI, 0, O); 
    return;
    break;
  case 2:
    // ADD2_d2_rrr, ADD2_l1_rrr_x2, ADD2_s1_rrr, ADD4_l1_rrr_x2, ADDU_l1_rrp_...
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", ");
    break;
  case 3:
    // ADDAB_d1_rir, ADDAB_d1_rrr, ADDAD_d1_rir, ADDAD_d1_rrr, ADDAH_d1_rir, ...
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", ");
    break;
  case 4:
    // ADDU_l1_rpp, ADD_l1_ipp, ADD_l1_rpp, CMPEQ_l1_ipr, CMPEQ_l1_rpr, CMPGT...
    printRegPair(MI, 1, O); 
    SStream_concat0(O, ", ");
    break;
  case 5:
    // BNOP_s10_ri, BNOP_s9_ii, STB_d5_rm, STB_d6_rm, STH_d5_rm, STH_d6_rm, S...
    SStream_concat0(O, ", ");
    break;
  case 6:
    // B_s5_i, B_s6_r, NOP_n
    return;
    break;
  }


  // Fragment 2 encoded into 3 bits for 8 unique commands.
  switch ((Bits >> 16) & 7) {
  default:
  case 0:
    // ABS2_l2_rr, ABS_l1_rr, ADDK_s2_ir, BDEC_s8_ir, BITC4_m2_rr, BPOS_s8_ir...
    return;
    break;
  case 1:
    // ADD2_d2_rrr, ADD2_l1_rrr_x2, ADD2_s1_rrr, ADD4_l1_rrr_x2, ADDAB_d1_rir...
    printOperand(MI, 0, O); 
    return;
    break;
  case 2:
    // ADDKPC_s3_iir
    SStream_concat0(O, ", ");
    printOperand(MI, 2, O); 
    return;
    break;
  case 3:
    // ADDU_l1_rpp, ADDU_l1_rrp_x2, ADD_l1_ipp, ADD_l1_rpp, ADD_l1_rrp_x2, DO...
    printRegPair(MI, 0, O); 
    return;
    break;
  case 4:
    // BNOP_s10_ri, BNOP_s9_ii
    printOperand(MI, 1, O); 
    return;
    break;
  case 5:
    // CLR_s15_riir, EXTU_s15_riir, EXT_s15_riir, SET_s15_riir
    printOperand(MI, 3, O); 
    SStream_concat0(O, ", ");
    printOperand(MI, 0, O); 
    return;
    break;
  case 6:
    // STB_d5_rm, STH_d5_rm, STNW_d5_rm, STW_d5_rm
    printMemOperand(MI, 1, O); 
    return;
    break;
  case 7:
    // STB_d6_rm, STH_d6_rm, STW_d6_rm
    printMemOperand2(MI, 1, O); 
    return;
    break;
  }

}


/// getRegisterName - This method is automatically generated by tblgen
/// from the register set description.  This returns the assembler name
/// for the specified register.
static char *getRegisterName(unsigned RegNo) {
#ifndef CAPSTONE_DIET
  static char AsmStrs[] = {
  /* 0 */ 'A', '1', '0', 0,
  /* 4 */ 'B', '1', '0', 0,
  /* 8 */ 'A', '2', '0', 0,
  /* 12 */ 'B', '2', '0', 0,
  /* 16 */ 'A', '3', '0', 0,
  /* 20 */ 'B', '3', '0', 0,
  /* 24 */ 'A', '0', 0,
  /* 27 */ 'B', '0', 0,
  /* 30 */ 'A', '1', '1', 0,
  /* 34 */ 'B', '1', '1', 0,
  /* 38 */ 'A', '2', '1', 0,
  /* 42 */ 'B', '2', '1', 0,
  /* 46 */ 'A', '3', '1', 0,
  /* 50 */ 'B', '3', '1', 0,
  /* 54 */ 'A', '1', 0,
  /* 57 */ 'B', '1', 0,
  /* 60 */ 'P', 'C', 'E', '1', 0,
  /* 65 */ 'A', '1', '2', 0,
  /* 69 */ 'B', '1', '2', 0,
  /* 73 */ 'A', '2', '2', 0,
  /* 77 */ 'B', '2', '2', 0,
  /* 81 */ 'A', '2', 0,
  /* 84 */ 'B', '2', 0,
  /* 87 */ 'A', '1', '3', 0,
  /* 91 */ 'B', '1', '3', 0,
  /* 95 */ 'A', '2', '3', 0,
  /* 99 */ 'B', '2', '3', 0,
  /* 103 */ 'A', '3', 0,
  /* 106 */ 'B', '3', 0,
  /* 109 */ 'A', '1', '4', 0,
  /* 113 */ 'B', '1', '4', 0,
  /* 117 */ 'A', '2', '4', 0,
  /* 121 */ 'B', '2', '4', 0,
  /* 125 */ 'A', '4', 0,
  /* 128 */ 'B', '4', 0,
  /* 131 */ 'A', '1', '5', 0,
  /* 135 */ 'B', '1', '5', 0,
  /* 139 */ 'A', '2', '5', 0,
  /* 143 */ 'B', '2', '5', 0,
  /* 147 */ 'A', '5', 0,
  /* 150 */ 'B', '5', 0,
  /* 153 */ 'A', '1', '6', 0,
  /* 157 */ 'B', '1', '6', 0,
  /* 161 */ 'A', '2', '6', 0,
  /* 165 */ 'B', '2', '6', 0,
  /* 169 */ 'A', '6', 0,
  /* 172 */ 'B', '6', 0,
  /* 175 */ 'A', '1', '7', 0,
  /* 179 */ 'B', '1', '7', 0,
  /* 183 */ 'A', '2', '7', 0,
  /* 187 */ 'B', '2', '7', 0,
  /* 191 */ 'A', '7', 0,
  /* 194 */ 'B', '7', 0,
  /* 197 */ 'A', '1', '8', 0,
  /* 201 */ 'B', '1', '8', 0,
  /* 205 */ 'A', '2', '8', 0,
  /* 209 */ 'B', '2', '8', 0,
  /* 213 */ 'A', '8', 0,
  /* 216 */ 'B', '8', 0,
  /* 219 */ 'A', '1', '9', 0,
  /* 223 */ 'B', '1', '9', 0,
  /* 227 */ 'A', '2', '9', 0,
  /* 231 */ 'B', '2', '9', 0,
  /* 235 */ 'A', '9', 0,
  /* 238 */ 'B', '9', 0,
  /* 241 */ 'G', 'P', 'L', 'Y', 'A', 0,
  /* 247 */ 'G', 'P', 'L', 'Y', 'B', 0,
  /* 253 */ 'R', 'I', 'L', 'C', 0,
  /* 258 */ 'T', 'S', 'C', 'H', 0,
  /* 263 */ 'T', 'S', 'C', 'L', 0,
  /* 268 */ 'D', 'N', 'U', 'M', 0,
  /* 273 */ 'R', 'E', 'P', 0,
  /* 277 */ 'I', 'R', 'P', 0,
  /* 281 */ 'N', 'R', 'P', 0,
  /* 285 */ 'I', 'S', 'T', 'P', 0,
  /* 290 */ 'E', 'C', 'R', 0,
  /* 294 */ 'I', 'C', 'R', 0,
  /* 298 */ 'D', 'I', 'E', 'R', 0,
  /* 303 */ 'G', 'F', 'P', 'G', 'F', 'R', 0,
  /* 310 */ 'A', 'M', 'R', 0,
  /* 314 */ 'I', 'E', 'R', 'R', 0,
  /* 319 */ 'C', 'S', 'R', 0,
  /* 323 */ 'I', 'S', 'R', 0,
  /* 327 */ 'S', 'S', 'R', 0,
  /* 331 */ 'I', 'T', 'S', 'R', 0,
  /* 336 */ 'N', 'T', 'S', 'R', 0,
  };

  static const uint16_t RegAsmOffset[] = {
    310, 319, 298, 268, 290, 303, 241, 247, 294, 299, 314, 254, 277, 323, 
    285, 331, 281, 336, 273, 253, 327, 258, 263, 332, 24, 54, 81, 103, 
    125, 147, 169, 191, 213, 235, 0, 30, 65, 87, 109, 131, 153, 175, 
    197, 219, 8, 38, 73, 95, 117, 139, 161, 183, 205, 227, 16, 46, 
    27, 57, 84, 106, 128, 150, 172, 194, 216, 238, 4, 34, 69, 91, 
    113, 135, 157, 179, 201, 223, 12, 42, 77, 99, 121, 143, 165, 187, 
    209, 231, 20, 50, 60, 
  };

  return AsmStrs+RegAsmOffset[RegNo-1];
#else
  return NULL;
#endif
}
