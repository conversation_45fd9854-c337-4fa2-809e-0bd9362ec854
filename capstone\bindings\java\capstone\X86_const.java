// For Capstone Engine. AUTO-GENERATED FILE, DO NOT EDIT
package capstone;

public class X86_const {

	// X86 registers

	public static final int X86_REG_INVALID = 0;
	public static final int X86_REG_AH = 1;
	public static final int X86_REG_AL = 2;
	public static final int X86_REG_AX = 3;
	public static final int X86_REG_BH = 4;
	public static final int X86_REG_BL = 5;
	public static final int X86_REG_BP = 6;
	public static final int X86_REG_BPL = 7;
	public static final int X86_REG_BX = 8;
	public static final int X86_REG_CH = 9;
	public static final int X86_REG_CL = 10;
	public static final int X86_REG_CS = 11;
	public static final int X86_REG_CX = 12;
	public static final int X86_REG_DH = 13;
	public static final int X86_REG_DI = 14;
	public static final int X86_REG_DIL = 15;
	public static final int X86_REG_DL = 16;
	public static final int X86_REG_DS = 17;
	public static final int X86_REG_DX = 18;
	public static final int X86_REG_EAX = 19;
	public static final int X86_REG_EBP = 20;
	public static final int X86_REG_EBX = 21;
	public static final int X86_REG_ECX = 22;
	public static final int X86_REG_EDI = 23;
	public static final int X86_REG_EDX = 24;
	public static final int X86_REG_EFLAGS = 25;
	public static final int X86_REG_EIP = 26;
	public static final int X86_REG_EIZ = 27;
	public static final int X86_REG_ES = 28;
	public static final int X86_REG_ESI = 29;
	public static final int X86_REG_ESP = 30;
	public static final int X86_REG_FPSW = 31;
	public static final int X86_REG_FS = 32;
	public static final int X86_REG_GS = 33;
	public static final int X86_REG_IP = 34;
	public static final int X86_REG_RAX = 35;
	public static final int X86_REG_RBP = 36;
	public static final int X86_REG_RBX = 37;
	public static final int X86_REG_RCX = 38;
	public static final int X86_REG_RDI = 39;
	public static final int X86_REG_RDX = 40;
	public static final int X86_REG_RIP = 41;
	public static final int X86_REG_RIZ = 42;
	public static final int X86_REG_RSI = 43;
	public static final int X86_REG_RSP = 44;
	public static final int X86_REG_SI = 45;
	public static final int X86_REG_SIL = 46;
	public static final int X86_REG_SP = 47;
	public static final int X86_REG_SPL = 48;
	public static final int X86_REG_SS = 49;
	public static final int X86_REG_CR0 = 50;
	public static final int X86_REG_CR1 = 51;
	public static final int X86_REG_CR2 = 52;
	public static final int X86_REG_CR3 = 53;
	public static final int X86_REG_CR4 = 54;
	public static final int X86_REG_CR5 = 55;
	public static final int X86_REG_CR6 = 56;
	public static final int X86_REG_CR7 = 57;
	public static final int X86_REG_CR8 = 58;
	public static final int X86_REG_CR9 = 59;
	public static final int X86_REG_CR10 = 60;
	public static final int X86_REG_CR11 = 61;
	public static final int X86_REG_CR12 = 62;
	public static final int X86_REG_CR13 = 63;
	public static final int X86_REG_CR14 = 64;
	public static final int X86_REG_CR15 = 65;
	public static final int X86_REG_DR0 = 66;
	public static final int X86_REG_DR1 = 67;
	public static final int X86_REG_DR2 = 68;
	public static final int X86_REG_DR3 = 69;
	public static final int X86_REG_DR4 = 70;
	public static final int X86_REG_DR5 = 71;
	public static final int X86_REG_DR6 = 72;
	public static final int X86_REG_DR7 = 73;
	public static final int X86_REG_DR8 = 74;
	public static final int X86_REG_DR9 = 75;
	public static final int X86_REG_DR10 = 76;
	public static final int X86_REG_DR11 = 77;
	public static final int X86_REG_DR12 = 78;
	public static final int X86_REG_DR13 = 79;
	public static final int X86_REG_DR14 = 80;
	public static final int X86_REG_DR15 = 81;
	public static final int X86_REG_FP0 = 82;
	public static final int X86_REG_FP1 = 83;
	public static final int X86_REG_FP2 = 84;
	public static final int X86_REG_FP3 = 85;
	public static final int X86_REG_FP4 = 86;
	public static final int X86_REG_FP5 = 87;
	public static final int X86_REG_FP6 = 88;
	public static final int X86_REG_FP7 = 89;
	public static final int X86_REG_K0 = 90;
	public static final int X86_REG_K1 = 91;
	public static final int X86_REG_K2 = 92;
	public static final int X86_REG_K3 = 93;
	public static final int X86_REG_K4 = 94;
	public static final int X86_REG_K5 = 95;
	public static final int X86_REG_K6 = 96;
	public static final int X86_REG_K7 = 97;
	public static final int X86_REG_MM0 = 98;
	public static final int X86_REG_MM1 = 99;
	public static final int X86_REG_MM2 = 100;
	public static final int X86_REG_MM3 = 101;
	public static final int X86_REG_MM4 = 102;
	public static final int X86_REG_MM5 = 103;
	public static final int X86_REG_MM6 = 104;
	public static final int X86_REG_MM7 = 105;
	public static final int X86_REG_R8 = 106;
	public static final int X86_REG_R9 = 107;
	public static final int X86_REG_R10 = 108;
	public static final int X86_REG_R11 = 109;
	public static final int X86_REG_R12 = 110;
	public static final int X86_REG_R13 = 111;
	public static final int X86_REG_R14 = 112;
	public static final int X86_REG_R15 = 113;
	public static final int X86_REG_ST0 = 114;
	public static final int X86_REG_ST1 = 115;
	public static final int X86_REG_ST2 = 116;
	public static final int X86_REG_ST3 = 117;
	public static final int X86_REG_ST4 = 118;
	public static final int X86_REG_ST5 = 119;
	public static final int X86_REG_ST6 = 120;
	public static final int X86_REG_ST7 = 121;
	public static final int X86_REG_XMM0 = 122;
	public static final int X86_REG_XMM1 = 123;
	public static final int X86_REG_XMM2 = 124;
	public static final int X86_REG_XMM3 = 125;
	public static final int X86_REG_XMM4 = 126;
	public static final int X86_REG_XMM5 = 127;
	public static final int X86_REG_XMM6 = 128;
	public static final int X86_REG_XMM7 = 129;
	public static final int X86_REG_XMM8 = 130;
	public static final int X86_REG_XMM9 = 131;
	public static final int X86_REG_XMM10 = 132;
	public static final int X86_REG_XMM11 = 133;
	public static final int X86_REG_XMM12 = 134;
	public static final int X86_REG_XMM13 = 135;
	public static final int X86_REG_XMM14 = 136;
	public static final int X86_REG_XMM15 = 137;
	public static final int X86_REG_XMM16 = 138;
	public static final int X86_REG_XMM17 = 139;
	public static final int X86_REG_XMM18 = 140;
	public static final int X86_REG_XMM19 = 141;
	public static final int X86_REG_XMM20 = 142;
	public static final int X86_REG_XMM21 = 143;
	public static final int X86_REG_XMM22 = 144;
	public static final int X86_REG_XMM23 = 145;
	public static final int X86_REG_XMM24 = 146;
	public static final int X86_REG_XMM25 = 147;
	public static final int X86_REG_XMM26 = 148;
	public static final int X86_REG_XMM27 = 149;
	public static final int X86_REG_XMM28 = 150;
	public static final int X86_REG_XMM29 = 151;
	public static final int X86_REG_XMM30 = 152;
	public static final int X86_REG_XMM31 = 153;
	public static final int X86_REG_YMM0 = 154;
	public static final int X86_REG_YMM1 = 155;
	public static final int X86_REG_YMM2 = 156;
	public static final int X86_REG_YMM3 = 157;
	public static final int X86_REG_YMM4 = 158;
	public static final int X86_REG_YMM5 = 159;
	public static final int X86_REG_YMM6 = 160;
	public static final int X86_REG_YMM7 = 161;
	public static final int X86_REG_YMM8 = 162;
	public static final int X86_REG_YMM9 = 163;
	public static final int X86_REG_YMM10 = 164;
	public static final int X86_REG_YMM11 = 165;
	public static final int X86_REG_YMM12 = 166;
	public static final int X86_REG_YMM13 = 167;
	public static final int X86_REG_YMM14 = 168;
	public static final int X86_REG_YMM15 = 169;
	public static final int X86_REG_YMM16 = 170;
	public static final int X86_REG_YMM17 = 171;
	public static final int X86_REG_YMM18 = 172;
	public static final int X86_REG_YMM19 = 173;
	public static final int X86_REG_YMM20 = 174;
	public static final int X86_REG_YMM21 = 175;
	public static final int X86_REG_YMM22 = 176;
	public static final int X86_REG_YMM23 = 177;
	public static final int X86_REG_YMM24 = 178;
	public static final int X86_REG_YMM25 = 179;
	public static final int X86_REG_YMM26 = 180;
	public static final int X86_REG_YMM27 = 181;
	public static final int X86_REG_YMM28 = 182;
	public static final int X86_REG_YMM29 = 183;
	public static final int X86_REG_YMM30 = 184;
	public static final int X86_REG_YMM31 = 185;
	public static final int X86_REG_ZMM0 = 186;
	public static final int X86_REG_ZMM1 = 187;
	public static final int X86_REG_ZMM2 = 188;
	public static final int X86_REG_ZMM3 = 189;
	public static final int X86_REG_ZMM4 = 190;
	public static final int X86_REG_ZMM5 = 191;
	public static final int X86_REG_ZMM6 = 192;
	public static final int X86_REG_ZMM7 = 193;
	public static final int X86_REG_ZMM8 = 194;
	public static final int X86_REG_ZMM9 = 195;
	public static final int X86_REG_ZMM10 = 196;
	public static final int X86_REG_ZMM11 = 197;
	public static final int X86_REG_ZMM12 = 198;
	public static final int X86_REG_ZMM13 = 199;
	public static final int X86_REG_ZMM14 = 200;
	public static final int X86_REG_ZMM15 = 201;
	public static final int X86_REG_ZMM16 = 202;
	public static final int X86_REG_ZMM17 = 203;
	public static final int X86_REG_ZMM18 = 204;
	public static final int X86_REG_ZMM19 = 205;
	public static final int X86_REG_ZMM20 = 206;
	public static final int X86_REG_ZMM21 = 207;
	public static final int X86_REG_ZMM22 = 208;
	public static final int X86_REG_ZMM23 = 209;
	public static final int X86_REG_ZMM24 = 210;
	public static final int X86_REG_ZMM25 = 211;
	public static final int X86_REG_ZMM26 = 212;
	public static final int X86_REG_ZMM27 = 213;
	public static final int X86_REG_ZMM28 = 214;
	public static final int X86_REG_ZMM29 = 215;
	public static final int X86_REG_ZMM30 = 216;
	public static final int X86_REG_ZMM31 = 217;
	public static final int X86_REG_R8B = 218;
	public static final int X86_REG_R9B = 219;
	public static final int X86_REG_R10B = 220;
	public static final int X86_REG_R11B = 221;
	public static final int X86_REG_R12B = 222;
	public static final int X86_REG_R13B = 223;
	public static final int X86_REG_R14B = 224;
	public static final int X86_REG_R15B = 225;
	public static final int X86_REG_R8D = 226;
	public static final int X86_REG_R9D = 227;
	public static final int X86_REG_R10D = 228;
	public static final int X86_REG_R11D = 229;
	public static final int X86_REG_R12D = 230;
	public static final int X86_REG_R13D = 231;
	public static final int X86_REG_R14D = 232;
	public static final int X86_REG_R15D = 233;
	public static final int X86_REG_R8W = 234;
	public static final int X86_REG_R9W = 235;
	public static final int X86_REG_R10W = 236;
	public static final int X86_REG_R11W = 237;
	public static final int X86_REG_R12W = 238;
	public static final int X86_REG_R13W = 239;
	public static final int X86_REG_R14W = 240;
	public static final int X86_REG_R15W = 241;
	public static final int X86_REG_ENDING = 242;

	// Sub-flags of EFLAGS
	public static final int X86_EFLAGS_MODIFY_AF = 1<<0;
	public static final int X86_EFLAGS_MODIFY_CF = 1<<1;
	public static final int X86_EFLAGS_MODIFY_SF = 1<<2;
	public static final int X86_EFLAGS_MODIFY_ZF = 1<<3;
	public static final int X86_EFLAGS_MODIFY_PF = 1<<4;
	public static final int X86_EFLAGS_MODIFY_OF = 1<<5;
	public static final int X86_EFLAGS_MODIFY_TF = 1<<6;
	public static final int X86_EFLAGS_MODIFY_IF = 1<<7;
	public static final int X86_EFLAGS_MODIFY_DF = 1<<8;
	public static final int X86_EFLAGS_MODIFY_NT = 1<<9;
	public static final int X86_EFLAGS_MODIFY_RF = 1<<10;
	public static final int X86_EFLAGS_PRIOR_OF = 1<<11;
	public static final int X86_EFLAGS_PRIOR_SF = 1<<12;
	public static final int X86_EFLAGS_PRIOR_ZF = 1<<13;
	public static final int X86_EFLAGS_PRIOR_AF = 1<<14;
	public static final int X86_EFLAGS_PRIOR_PF = 1<<15;
	public static final int X86_EFLAGS_PRIOR_CF = 1<<16;
	public static final int X86_EFLAGS_PRIOR_TF = 1<<17;
	public static final int X86_EFLAGS_PRIOR_IF = 1<<18;
	public static final int X86_EFLAGS_PRIOR_DF = 1<<19;
	public static final int X86_EFLAGS_PRIOR_NT = 1<<20;
	public static final int X86_EFLAGS_RESET_OF = 1<<21;
	public static final int X86_EFLAGS_RESET_CF = 1<<22;
	public static final int X86_EFLAGS_RESET_DF = 1<<23;
	public static final int X86_EFLAGS_RESET_IF = 1<<24;
	public static final int X86_EFLAGS_RESET_SF = 1<<25;
	public static final int X86_EFLAGS_RESET_AF = 1<<26;
	public static final int X86_EFLAGS_RESET_TF = 1<<27;
	public static final int X86_EFLAGS_RESET_NT = 1<<28;
	public static final int X86_EFLAGS_RESET_PF = 1<<29;
	public static final int X86_EFLAGS_SET_CF = 1<<30;
	public static final int X86_EFLAGS_SET_DF = 1<<31;
	public static final int X86_EFLAGS_SET_IF = 1<<32;
	public static final int X86_EFLAGS_TEST_OF = 1<<33;
	public static final int X86_EFLAGS_TEST_SF = 1<<34;
	public static final int X86_EFLAGS_TEST_ZF = 1<<35;
	public static final int X86_EFLAGS_TEST_PF = 1<<36;
	public static final int X86_EFLAGS_TEST_CF = 1<<37;
	public static final int X86_EFLAGS_TEST_NT = 1<<38;
	public static final int X86_EFLAGS_TEST_DF = 1<<39;
	public static final int X86_EFLAGS_UNDEFINED_OF = 1<<40;
	public static final int X86_EFLAGS_UNDEFINED_SF = 1<<41;
	public static final int X86_EFLAGS_UNDEFINED_ZF = 1<<42;
	public static final int X86_EFLAGS_UNDEFINED_PF = 1<<43;
	public static final int X86_EFLAGS_UNDEFINED_AF = 1<<44;
	public static final int X86_EFLAGS_UNDEFINED_CF = 1<<45;
	public static final int X86_EFLAGS_RESET_RF = 1<<46;
	public static final int X86_EFLAGS_TEST_RF = 1<<47;
	public static final int X86_EFLAGS_TEST_IF = 1<<48;
	public static final int X86_EFLAGS_TEST_TF = 1<<49;
	public static final int X86_EFLAGS_TEST_AF = 1<<50;
	public static final int X86_EFLAGS_RESET_ZF = 1<<51;
	public static final int X86_EFLAGS_SET_OF = 1<<52;
	public static final int X86_EFLAGS_SET_SF = 1<<53;
	public static final int X86_EFLAGS_SET_ZF = 1<<54;
	public static final int X86_EFLAGS_SET_AF = 1<<55;
	public static final int X86_EFLAGS_SET_PF = 1<<56;
	public static final int X86_EFLAGS_RESET_0F = 1<<57;
	public static final int X86_EFLAGS_RESET_AC = 1<<58;
	public static final int X86_FPU_FLAGS_MODIFY_C0 = 1<<0;
	public static final int X86_FPU_FLAGS_MODIFY_C1 = 1<<1;
	public static final int X86_FPU_FLAGS_MODIFY_C2 = 1<<2;
	public static final int X86_FPU_FLAGS_MODIFY_C3 = 1<<3;
	public static final int X86_FPU_FLAGS_RESET_C0 = 1<<4;
	public static final int X86_FPU_FLAGS_RESET_C1 = 1<<5;
	public static final int X86_FPU_FLAGS_RESET_C2 = 1<<6;
	public static final int X86_FPU_FLAGS_RESET_C3 = 1<<7;
	public static final int X86_FPU_FLAGS_SET_C0 = 1<<8;
	public static final int X86_FPU_FLAGS_SET_C1 = 1<<9;
	public static final int X86_FPU_FLAGS_SET_C2 = 1<<10;
	public static final int X86_FPU_FLAGS_SET_C3 = 1<<11;
	public static final int X86_FPU_FLAGS_UNDEFINED_C0 = 1<<12;
	public static final int X86_FPU_FLAGS_UNDEFINED_C1 = 1<<13;
	public static final int X86_FPU_FLAGS_UNDEFINED_C2 = 1<<14;
	public static final int X86_FPU_FLAGS_UNDEFINED_C3 = 1<<15;
	public static final int X86_FPU_FLAGS_TEST_C0 = 1<<16;
	public static final int X86_FPU_FLAGS_TEST_C1 = 1<<17;
	public static final int X86_FPU_FLAGS_TEST_C2 = 1<<18;
	public static final int X86_FPU_FLAGS_TEST_C3 = 1<<19;

	// Operand type for instruction's operands

	public static final int X86_OP_INVALID = 0;
	public static final int X86_OP_REG = 1;
	public static final int X86_OP_IMM = 2;
	public static final int X86_OP_MEM = 3;

	// XOP Code Condition type

	public static final int X86_XOP_CC_INVALID = 0;
	public static final int X86_XOP_CC_LT = 1;
	public static final int X86_XOP_CC_LE = 2;
	public static final int X86_XOP_CC_GT = 3;
	public static final int X86_XOP_CC_GE = 4;
	public static final int X86_XOP_CC_EQ = 5;
	public static final int X86_XOP_CC_NEQ = 6;
	public static final int X86_XOP_CC_FALSE = 7;
	public static final int X86_XOP_CC_TRUE = 8;

	// AVX broadcast type

	public static final int X86_AVX_BCAST_INVALID = 0;
	public static final int X86_AVX_BCAST_2 = 1;
	public static final int X86_AVX_BCAST_4 = 2;
	public static final int X86_AVX_BCAST_8 = 3;
	public static final int X86_AVX_BCAST_16 = 4;

	// SSE Code Condition type

	public static final int X86_SSE_CC_INVALID = 0;
	public static final int X86_SSE_CC_EQ = 1;
	public static final int X86_SSE_CC_LT = 2;
	public static final int X86_SSE_CC_LE = 3;
	public static final int X86_SSE_CC_UNORD = 4;
	public static final int X86_SSE_CC_NEQ = 5;
	public static final int X86_SSE_CC_NLT = 6;
	public static final int X86_SSE_CC_NLE = 7;
	public static final int X86_SSE_CC_ORD = 8;

	// AVX Code Condition type

	public static final int X86_AVX_CC_INVALID = 0;
	public static final int X86_AVX_CC_EQ = 1;
	public static final int X86_AVX_CC_LT = 2;
	public static final int X86_AVX_CC_LE = 3;
	public static final int X86_AVX_CC_UNORD = 4;
	public static final int X86_AVX_CC_NEQ = 5;
	public static final int X86_AVX_CC_NLT = 6;
	public static final int X86_AVX_CC_NLE = 7;
	public static final int X86_AVX_CC_ORD = 8;
	public static final int X86_AVX_CC_EQ_UQ = 9;
	public static final int X86_AVX_CC_NGE = 10;
	public static final int X86_AVX_CC_NGT = 11;
	public static final int X86_AVX_CC_FALSE = 12;
	public static final int X86_AVX_CC_NEQ_OQ = 13;
	public static final int X86_AVX_CC_GE = 14;
	public static final int X86_AVX_CC_GT = 15;
	public static final int X86_AVX_CC_TRUE = 16;
	public static final int X86_AVX_CC_EQ_OS = 17;
	public static final int X86_AVX_CC_LT_OQ = 18;
	public static final int X86_AVX_CC_LE_OQ = 19;
	public static final int X86_AVX_CC_UNORD_S = 20;
	public static final int X86_AVX_CC_NEQ_US = 21;
	public static final int X86_AVX_CC_NLT_UQ = 22;
	public static final int X86_AVX_CC_NLE_UQ = 23;
	public static final int X86_AVX_CC_ORD_S = 24;
	public static final int X86_AVX_CC_EQ_US = 25;
	public static final int X86_AVX_CC_NGE_UQ = 26;
	public static final int X86_AVX_CC_NGT_UQ = 27;
	public static final int X86_AVX_CC_FALSE_OS = 28;
	public static final int X86_AVX_CC_NEQ_OS = 29;
	public static final int X86_AVX_CC_GE_OQ = 30;
	public static final int X86_AVX_CC_GT_OQ = 31;
	public static final int X86_AVX_CC_TRUE_US = 32;

	// AVX static rounding mode type

	public static final int X86_AVX_RM_INVALID = 0;
	public static final int X86_AVX_RM_RN = 1;
	public static final int X86_AVX_RM_RD = 2;
	public static final int X86_AVX_RM_RU = 3;
	public static final int X86_AVX_RM_RZ = 4;

	// Instruction prefixes - to be used in cs_x86.prefix[]
	public static final int X86_PREFIX_LOCK = 0xf0;
	public static final int X86_PREFIX_REP = 0xf3;
	public static final int X86_PREFIX_REPE = 0xf3;
	public static final int X86_PREFIX_REPNE = 0xf2;
	public static final int X86_PREFIX_CS = 0x2e;
	public static final int X86_PREFIX_SS = 0x36;
	public static final int X86_PREFIX_DS = 0x3e;
	public static final int X86_PREFIX_ES = 0x26;
	public static final int X86_PREFIX_FS = 0x64;
	public static final int X86_PREFIX_GS = 0x65;
	public static final int X86_PREFIX_OPSIZE = 0x66;
	public static final int X86_PREFIX_ADDRSIZE = 0x67;

	// X86 instructions

	public static final int X86_INS_INVALID = 0;
	public static final int X86_INS_AAA = 1;
	public static final int X86_INS_AAD = 2;
	public static final int X86_INS_AAM = 3;
	public static final int X86_INS_AAS = 4;
	public static final int X86_INS_FABS = 5;
	public static final int X86_INS_ADC = 6;
	public static final int X86_INS_ADCX = 7;
	public static final int X86_INS_ADD = 8;
	public static final int X86_INS_ADDPD = 9;
	public static final int X86_INS_ADDPS = 10;
	public static final int X86_INS_ADDSD = 11;
	public static final int X86_INS_ADDSS = 12;
	public static final int X86_INS_ADDSUBPD = 13;
	public static final int X86_INS_ADDSUBPS = 14;
	public static final int X86_INS_FADD = 15;
	public static final int X86_INS_FIADD = 16;
	public static final int X86_INS_FADDP = 17;
	public static final int X86_INS_ADOX = 18;
	public static final int X86_INS_AESDECLAST = 19;
	public static final int X86_INS_AESDEC = 20;
	public static final int X86_INS_AESENCLAST = 21;
	public static final int X86_INS_AESENC = 22;
	public static final int X86_INS_AESIMC = 23;
	public static final int X86_INS_AESKEYGENASSIST = 24;
	public static final int X86_INS_AND = 25;
	public static final int X86_INS_ANDN = 26;
	public static final int X86_INS_ANDNPD = 27;
	public static final int X86_INS_ANDNPS = 28;
	public static final int X86_INS_ANDPD = 29;
	public static final int X86_INS_ANDPS = 30;
	public static final int X86_INS_ARPL = 31;
	public static final int X86_INS_BEXTR = 32;
	public static final int X86_INS_BLCFILL = 33;
	public static final int X86_INS_BLCI = 34;
	public static final int X86_INS_BLCIC = 35;
	public static final int X86_INS_BLCMSK = 36;
	public static final int X86_INS_BLCS = 37;
	public static final int X86_INS_BLENDPD = 38;
	public static final int X86_INS_BLENDPS = 39;
	public static final int X86_INS_BLENDVPD = 40;
	public static final int X86_INS_BLENDVPS = 41;
	public static final int X86_INS_BLSFILL = 42;
	public static final int X86_INS_BLSI = 43;
	public static final int X86_INS_BLSIC = 44;
	public static final int X86_INS_BLSMSK = 45;
	public static final int X86_INS_BLSR = 46;
	public static final int X86_INS_BOUND = 47;
	public static final int X86_INS_BSF = 48;
	public static final int X86_INS_BSR = 49;
	public static final int X86_INS_BSWAP = 50;
	public static final int X86_INS_BT = 51;
	public static final int X86_INS_BTC = 52;
	public static final int X86_INS_BTR = 53;
	public static final int X86_INS_BTS = 54;
	public static final int X86_INS_BZHI = 55;
	public static final int X86_INS_CALL = 56;
	public static final int X86_INS_CBW = 57;
	public static final int X86_INS_CDQ = 58;
	public static final int X86_INS_CDQE = 59;
	public static final int X86_INS_FCHS = 60;
	public static final int X86_INS_CLAC = 61;
	public static final int X86_INS_CLC = 62;
	public static final int X86_INS_CLD = 63;
	public static final int X86_INS_CLFLUSH = 64;
	public static final int X86_INS_CLFLUSHOPT = 65;
	public static final int X86_INS_CLGI = 66;
	public static final int X86_INS_CLI = 67;
	public static final int X86_INS_CLTS = 68;
	public static final int X86_INS_CLWB = 69;
	public static final int X86_INS_CMC = 70;
	public static final int X86_INS_CMOVA = 71;
	public static final int X86_INS_CMOVAE = 72;
	public static final int X86_INS_CMOVB = 73;
	public static final int X86_INS_CMOVBE = 74;
	public static final int X86_INS_FCMOVBE = 75;
	public static final int X86_INS_FCMOVB = 76;
	public static final int X86_INS_CMOVE = 77;
	public static final int X86_INS_FCMOVE = 78;
	public static final int X86_INS_CMOVG = 79;
	public static final int X86_INS_CMOVGE = 80;
	public static final int X86_INS_CMOVL = 81;
	public static final int X86_INS_CMOVLE = 82;
	public static final int X86_INS_FCMOVNBE = 83;
	public static final int X86_INS_FCMOVNB = 84;
	public static final int X86_INS_CMOVNE = 85;
	public static final int X86_INS_FCMOVNE = 86;
	public static final int X86_INS_CMOVNO = 87;
	public static final int X86_INS_CMOVNP = 88;
	public static final int X86_INS_FCMOVNU = 89;
	public static final int X86_INS_CMOVNS = 90;
	public static final int X86_INS_CMOVO = 91;
	public static final int X86_INS_CMOVP = 92;
	public static final int X86_INS_FCMOVU = 93;
	public static final int X86_INS_CMOVS = 94;
	public static final int X86_INS_CMP = 95;
	public static final int X86_INS_CMPSB = 96;
	public static final int X86_INS_CMPSQ = 97;
	public static final int X86_INS_CMPSW = 98;
	public static final int X86_INS_CMPXCHG16B = 99;
	public static final int X86_INS_CMPXCHG = 100;
	public static final int X86_INS_CMPXCHG8B = 101;
	public static final int X86_INS_COMISD = 102;
	public static final int X86_INS_COMISS = 103;
	public static final int X86_INS_FCOMP = 104;
	public static final int X86_INS_FCOMIP = 105;
	public static final int X86_INS_FCOMI = 106;
	public static final int X86_INS_FCOM = 107;
	public static final int X86_INS_FCOS = 108;
	public static final int X86_INS_CPUID = 109;
	public static final int X86_INS_CQO = 110;
	public static final int X86_INS_CRC32 = 111;
	public static final int X86_INS_CVTDQ2PD = 112;
	public static final int X86_INS_CVTDQ2PS = 113;
	public static final int X86_INS_CVTPD2DQ = 114;
	public static final int X86_INS_CVTPD2PS = 115;
	public static final int X86_INS_CVTPS2DQ = 116;
	public static final int X86_INS_CVTPS2PD = 117;
	public static final int X86_INS_CVTSD2SI = 118;
	public static final int X86_INS_CVTSD2SS = 119;
	public static final int X86_INS_CVTSI2SD = 120;
	public static final int X86_INS_CVTSI2SS = 121;
	public static final int X86_INS_CVTSS2SD = 122;
	public static final int X86_INS_CVTSS2SI = 123;
	public static final int X86_INS_CVTTPD2DQ = 124;
	public static final int X86_INS_CVTTPS2DQ = 125;
	public static final int X86_INS_CVTTSD2SI = 126;
	public static final int X86_INS_CVTTSS2SI = 127;
	public static final int X86_INS_CWD = 128;
	public static final int X86_INS_CWDE = 129;
	public static final int X86_INS_DAA = 130;
	public static final int X86_INS_DAS = 131;
	public static final int X86_INS_DATA16 = 132;
	public static final int X86_INS_DEC = 133;
	public static final int X86_INS_DIV = 134;
	public static final int X86_INS_DIVPD = 135;
	public static final int X86_INS_DIVPS = 136;
	public static final int X86_INS_FDIVR = 137;
	public static final int X86_INS_FIDIVR = 138;
	public static final int X86_INS_FDIVRP = 139;
	public static final int X86_INS_DIVSD = 140;
	public static final int X86_INS_DIVSS = 141;
	public static final int X86_INS_FDIV = 142;
	public static final int X86_INS_FIDIV = 143;
	public static final int X86_INS_FDIVP = 144;
	public static final int X86_INS_DPPD = 145;
	public static final int X86_INS_DPPS = 146;
	public static final int X86_INS_RET = 147;
	public static final int X86_INS_ENCLS = 148;
	public static final int X86_INS_ENCLU = 149;
	public static final int X86_INS_ENTER = 150;
	public static final int X86_INS_EXTRACTPS = 151;
	public static final int X86_INS_EXTRQ = 152;
	public static final int X86_INS_F2XM1 = 153;
	public static final int X86_INS_LCALL = 154;
	public static final int X86_INS_LJMP = 155;
	public static final int X86_INS_FBLD = 156;
	public static final int X86_INS_FBSTP = 157;
	public static final int X86_INS_FCOMPP = 158;
	public static final int X86_INS_FDECSTP = 159;
	public static final int X86_INS_FEMMS = 160;
	public static final int X86_INS_FFREE = 161;
	public static final int X86_INS_FICOM = 162;
	public static final int X86_INS_FICOMP = 163;
	public static final int X86_INS_FINCSTP = 164;
	public static final int X86_INS_FLDCW = 165;
	public static final int X86_INS_FLDENV = 166;
	public static final int X86_INS_FLDL2E = 167;
	public static final int X86_INS_FLDL2T = 168;
	public static final int X86_INS_FLDLG2 = 169;
	public static final int X86_INS_FLDLN2 = 170;
	public static final int X86_INS_FLDPI = 171;
	public static final int X86_INS_FNCLEX = 172;
	public static final int X86_INS_FNINIT = 173;
	public static final int X86_INS_FNOP = 174;
	public static final int X86_INS_FNSTCW = 175;
	public static final int X86_INS_FNSTSW = 176;
	public static final int X86_INS_FPATAN = 177;
	public static final int X86_INS_FPREM = 178;
	public static final int X86_INS_FPREM1 = 179;
	public static final int X86_INS_FPTAN = 180;
	public static final int X86_INS_FFREEP = 181;
	public static final int X86_INS_FRNDINT = 182;
	public static final int X86_INS_FRSTOR = 183;
	public static final int X86_INS_FNSAVE = 184;
	public static final int X86_INS_FSCALE = 185;
	public static final int X86_INS_FSETPM = 186;
	public static final int X86_INS_FSINCOS = 187;
	public static final int X86_INS_FNSTENV = 188;
	public static final int X86_INS_FXAM = 189;
	public static final int X86_INS_FXRSTOR = 190;
	public static final int X86_INS_FXRSTOR64 = 191;
	public static final int X86_INS_FXSAVE = 192;
	public static final int X86_INS_FXSAVE64 = 193;
	public static final int X86_INS_FXTRACT = 194;
	public static final int X86_INS_FYL2X = 195;
	public static final int X86_INS_FYL2XP1 = 196;
	public static final int X86_INS_MOVAPD = 197;
	public static final int X86_INS_MOVAPS = 198;
	public static final int X86_INS_ORPD = 199;
	public static final int X86_INS_ORPS = 200;
	public static final int X86_INS_VMOVAPD = 201;
	public static final int X86_INS_VMOVAPS = 202;
	public static final int X86_INS_XORPD = 203;
	public static final int X86_INS_XORPS = 204;
	public static final int X86_INS_GETSEC = 205;
	public static final int X86_INS_HADDPD = 206;
	public static final int X86_INS_HADDPS = 207;
	public static final int X86_INS_HLT = 208;
	public static final int X86_INS_HSUBPD = 209;
	public static final int X86_INS_HSUBPS = 210;
	public static final int X86_INS_IDIV = 211;
	public static final int X86_INS_FILD = 212;
	public static final int X86_INS_IMUL = 213;
	public static final int X86_INS_IN = 214;
	public static final int X86_INS_INC = 215;
	public static final int X86_INS_INSB = 216;
	public static final int X86_INS_INSERTPS = 217;
	public static final int X86_INS_INSERTQ = 218;
	public static final int X86_INS_INSD = 219;
	public static final int X86_INS_INSW = 220;
	public static final int X86_INS_INT = 221;
	public static final int X86_INS_INT1 = 222;
	public static final int X86_INS_INT3 = 223;
	public static final int X86_INS_INTO = 224;
	public static final int X86_INS_INVD = 225;
	public static final int X86_INS_INVEPT = 226;
	public static final int X86_INS_INVLPG = 227;
	public static final int X86_INS_INVLPGA = 228;
	public static final int X86_INS_INVPCID = 229;
	public static final int X86_INS_INVVPID = 230;
	public static final int X86_INS_IRET = 231;
	public static final int X86_INS_IRETD = 232;
	public static final int X86_INS_IRETQ = 233;
	public static final int X86_INS_FISTTP = 234;
	public static final int X86_INS_FIST = 235;
	public static final int X86_INS_FISTP = 236;
	public static final int X86_INS_UCOMISD = 237;
	public static final int X86_INS_UCOMISS = 238;
	public static final int X86_INS_VCOMISD = 239;
	public static final int X86_INS_VCOMISS = 240;
	public static final int X86_INS_VCVTSD2SS = 241;
	public static final int X86_INS_VCVTSI2SD = 242;
	public static final int X86_INS_VCVTSI2SS = 243;
	public static final int X86_INS_VCVTSS2SD = 244;
	public static final int X86_INS_VCVTTSD2SI = 245;
	public static final int X86_INS_VCVTTSD2USI = 246;
	public static final int X86_INS_VCVTTSS2SI = 247;
	public static final int X86_INS_VCVTTSS2USI = 248;
	public static final int X86_INS_VCVTUSI2SD = 249;
	public static final int X86_INS_VCVTUSI2SS = 250;
	public static final int X86_INS_VUCOMISD = 251;
	public static final int X86_INS_VUCOMISS = 252;
	public static final int X86_INS_JAE = 253;
	public static final int X86_INS_JA = 254;
	public static final int X86_INS_JBE = 255;
	public static final int X86_INS_JB = 256;
	public static final int X86_INS_JCXZ = 257;
	public static final int X86_INS_JECXZ = 258;
	public static final int X86_INS_JE = 259;
	public static final int X86_INS_JGE = 260;
	public static final int X86_INS_JG = 261;
	public static final int X86_INS_JLE = 262;
	public static final int X86_INS_JL = 263;
	public static final int X86_INS_JMP = 264;
	public static final int X86_INS_JNE = 265;
	public static final int X86_INS_JNO = 266;
	public static final int X86_INS_JNP = 267;
	public static final int X86_INS_JNS = 268;
	public static final int X86_INS_JO = 269;
	public static final int X86_INS_JP = 270;
	public static final int X86_INS_JRCXZ = 271;
	public static final int X86_INS_JS = 272;
	public static final int X86_INS_KANDB = 273;
	public static final int X86_INS_KANDD = 274;
	public static final int X86_INS_KANDNB = 275;
	public static final int X86_INS_KANDND = 276;
	public static final int X86_INS_KANDNQ = 277;
	public static final int X86_INS_KANDNW = 278;
	public static final int X86_INS_KANDQ = 279;
	public static final int X86_INS_KANDW = 280;
	public static final int X86_INS_KMOVB = 281;
	public static final int X86_INS_KMOVD = 282;
	public static final int X86_INS_KMOVQ = 283;
	public static final int X86_INS_KMOVW = 284;
	public static final int X86_INS_KNOTB = 285;
	public static final int X86_INS_KNOTD = 286;
	public static final int X86_INS_KNOTQ = 287;
	public static final int X86_INS_KNOTW = 288;
	public static final int X86_INS_KORB = 289;
	public static final int X86_INS_KORD = 290;
	public static final int X86_INS_KORQ = 291;
	public static final int X86_INS_KORTESTB = 292;
	public static final int X86_INS_KORTESTD = 293;
	public static final int X86_INS_KORTESTQ = 294;
	public static final int X86_INS_KORTESTW = 295;
	public static final int X86_INS_KORW = 296;
	public static final int X86_INS_KSHIFTLB = 297;
	public static final int X86_INS_KSHIFTLD = 298;
	public static final int X86_INS_KSHIFTLQ = 299;
	public static final int X86_INS_KSHIFTLW = 300;
	public static final int X86_INS_KSHIFTRB = 301;
	public static final int X86_INS_KSHIFTRD = 302;
	public static final int X86_INS_KSHIFTRQ = 303;
	public static final int X86_INS_KSHIFTRW = 304;
	public static final int X86_INS_KUNPCKBW = 305;
	public static final int X86_INS_KXNORB = 306;
	public static final int X86_INS_KXNORD = 307;
	public static final int X86_INS_KXNORQ = 308;
	public static final int X86_INS_KXNORW = 309;
	public static final int X86_INS_KXORB = 310;
	public static final int X86_INS_KXORD = 311;
	public static final int X86_INS_KXORQ = 312;
	public static final int X86_INS_KXORW = 313;
	public static final int X86_INS_LAHF = 314;
	public static final int X86_INS_LAR = 315;
	public static final int X86_INS_LDDQU = 316;
	public static final int X86_INS_LDMXCSR = 317;
	public static final int X86_INS_LDS = 318;
	public static final int X86_INS_FLDZ = 319;
	public static final int X86_INS_FLD1 = 320;
	public static final int X86_INS_FLD = 321;
	public static final int X86_INS_LEA = 322;
	public static final int X86_INS_LEAVE = 323;
	public static final int X86_INS_LES = 324;
	public static final int X86_INS_LFENCE = 325;
	public static final int X86_INS_LFS = 326;
	public static final int X86_INS_LGDT = 327;
	public static final int X86_INS_LGS = 328;
	public static final int X86_INS_LIDT = 329;
	public static final int X86_INS_LLDT = 330;
	public static final int X86_INS_LMSW = 331;
	public static final int X86_INS_OR = 332;
	public static final int X86_INS_SUB = 333;
	public static final int X86_INS_XOR = 334;
	public static final int X86_INS_LODSB = 335;
	public static final int X86_INS_LODSD = 336;
	public static final int X86_INS_LODSQ = 337;
	public static final int X86_INS_LODSW = 338;
	public static final int X86_INS_LOOP = 339;
	public static final int X86_INS_LOOPE = 340;
	public static final int X86_INS_LOOPNE = 341;
	public static final int X86_INS_RETF = 342;
	public static final int X86_INS_RETFQ = 343;
	public static final int X86_INS_LSL = 344;
	public static final int X86_INS_LSS = 345;
	public static final int X86_INS_LTR = 346;
	public static final int X86_INS_XADD = 347;
	public static final int X86_INS_LZCNT = 348;
	public static final int X86_INS_MASKMOVDQU = 349;
	public static final int X86_INS_MAXPD = 350;
	public static final int X86_INS_MAXPS = 351;
	public static final int X86_INS_MAXSD = 352;
	public static final int X86_INS_MAXSS = 353;
	public static final int X86_INS_MFENCE = 354;
	public static final int X86_INS_MINPD = 355;
	public static final int X86_INS_MINPS = 356;
	public static final int X86_INS_MINSD = 357;
	public static final int X86_INS_MINSS = 358;
	public static final int X86_INS_CVTPD2PI = 359;
	public static final int X86_INS_CVTPI2PD = 360;
	public static final int X86_INS_CVTPI2PS = 361;
	public static final int X86_INS_CVTPS2PI = 362;
	public static final int X86_INS_CVTTPD2PI = 363;
	public static final int X86_INS_CVTTPS2PI = 364;
	public static final int X86_INS_EMMS = 365;
	public static final int X86_INS_MASKMOVQ = 366;
	public static final int X86_INS_MOVD = 367;
	public static final int X86_INS_MOVDQ2Q = 368;
	public static final int X86_INS_MOVNTQ = 369;
	public static final int X86_INS_MOVQ2DQ = 370;
	public static final int X86_INS_MOVQ = 371;
	public static final int X86_INS_PABSB = 372;
	public static final int X86_INS_PABSD = 373;
	public static final int X86_INS_PABSW = 374;
	public static final int X86_INS_PACKSSDW = 375;
	public static final int X86_INS_PACKSSWB = 376;
	public static final int X86_INS_PACKUSWB = 377;
	public static final int X86_INS_PADDB = 378;
	public static final int X86_INS_PADDD = 379;
	public static final int X86_INS_PADDQ = 380;
	public static final int X86_INS_PADDSB = 381;
	public static final int X86_INS_PADDSW = 382;
	public static final int X86_INS_PADDUSB = 383;
	public static final int X86_INS_PADDUSW = 384;
	public static final int X86_INS_PADDW = 385;
	public static final int X86_INS_PALIGNR = 386;
	public static final int X86_INS_PANDN = 387;
	public static final int X86_INS_PAND = 388;
	public static final int X86_INS_PAVGB = 389;
	public static final int X86_INS_PAVGW = 390;
	public static final int X86_INS_PCMPEQB = 391;
	public static final int X86_INS_PCMPEQD = 392;
	public static final int X86_INS_PCMPEQW = 393;
	public static final int X86_INS_PCMPGTB = 394;
	public static final int X86_INS_PCMPGTD = 395;
	public static final int X86_INS_PCMPGTW = 396;
	public static final int X86_INS_PEXTRW = 397;
	public static final int X86_INS_PHADDSW = 398;
	public static final int X86_INS_PHADDW = 399;
	public static final int X86_INS_PHADDD = 400;
	public static final int X86_INS_PHSUBD = 401;
	public static final int X86_INS_PHSUBSW = 402;
	public static final int X86_INS_PHSUBW = 403;
	public static final int X86_INS_PINSRW = 404;
	public static final int X86_INS_PMADDUBSW = 405;
	public static final int X86_INS_PMADDWD = 406;
	public static final int X86_INS_PMAXSW = 407;
	public static final int X86_INS_PMAXUB = 408;
	public static final int X86_INS_PMINSW = 409;
	public static final int X86_INS_PMINUB = 410;
	public static final int X86_INS_PMOVMSKB = 411;
	public static final int X86_INS_PMULHRSW = 412;
	public static final int X86_INS_PMULHUW = 413;
	public static final int X86_INS_PMULHW = 414;
	public static final int X86_INS_PMULLW = 415;
	public static final int X86_INS_PMULUDQ = 416;
	public static final int X86_INS_POR = 417;
	public static final int X86_INS_PSADBW = 418;
	public static final int X86_INS_PSHUFB = 419;
	public static final int X86_INS_PSHUFW = 420;
	public static final int X86_INS_PSIGNB = 421;
	public static final int X86_INS_PSIGND = 422;
	public static final int X86_INS_PSIGNW = 423;
	public static final int X86_INS_PSLLD = 424;
	public static final int X86_INS_PSLLQ = 425;
	public static final int X86_INS_PSLLW = 426;
	public static final int X86_INS_PSRAD = 427;
	public static final int X86_INS_PSRAW = 428;
	public static final int X86_INS_PSRLD = 429;
	public static final int X86_INS_PSRLQ = 430;
	public static final int X86_INS_PSRLW = 431;
	public static final int X86_INS_PSUBB = 432;
	public static final int X86_INS_PSUBD = 433;
	public static final int X86_INS_PSUBQ = 434;
	public static final int X86_INS_PSUBSB = 435;
	public static final int X86_INS_PSUBSW = 436;
	public static final int X86_INS_PSUBUSB = 437;
	public static final int X86_INS_PSUBUSW = 438;
	public static final int X86_INS_PSUBW = 439;
	public static final int X86_INS_PUNPCKHBW = 440;
	public static final int X86_INS_PUNPCKHDQ = 441;
	public static final int X86_INS_PUNPCKHWD = 442;
	public static final int X86_INS_PUNPCKLBW = 443;
	public static final int X86_INS_PUNPCKLDQ = 444;
	public static final int X86_INS_PUNPCKLWD = 445;
	public static final int X86_INS_PXOR = 446;
	public static final int X86_INS_MONITOR = 447;
	public static final int X86_INS_MONTMUL = 448;
	public static final int X86_INS_MOV = 449;
	public static final int X86_INS_MOVABS = 450;
	public static final int X86_INS_MOVBE = 451;
	public static final int X86_INS_MOVDDUP = 452;
	public static final int X86_INS_MOVDQA = 453;
	public static final int X86_INS_MOVDQU = 454;
	public static final int X86_INS_MOVHLPS = 455;
	public static final int X86_INS_MOVHPD = 456;
	public static final int X86_INS_MOVHPS = 457;
	public static final int X86_INS_MOVLHPS = 458;
	public static final int X86_INS_MOVLPD = 459;
	public static final int X86_INS_MOVLPS = 460;
	public static final int X86_INS_MOVMSKPD = 461;
	public static final int X86_INS_MOVMSKPS = 462;
	public static final int X86_INS_MOVNTDQA = 463;
	public static final int X86_INS_MOVNTDQ = 464;
	public static final int X86_INS_MOVNTI = 465;
	public static final int X86_INS_MOVNTPD = 466;
	public static final int X86_INS_MOVNTPS = 467;
	public static final int X86_INS_MOVNTSD = 468;
	public static final int X86_INS_MOVNTSS = 469;
	public static final int X86_INS_MOVSB = 470;
	public static final int X86_INS_MOVSD = 471;
	public static final int X86_INS_MOVSHDUP = 472;
	public static final int X86_INS_MOVSLDUP = 473;
	public static final int X86_INS_MOVSQ = 474;
	public static final int X86_INS_MOVSS = 475;
	public static final int X86_INS_MOVSW = 476;
	public static final int X86_INS_MOVSX = 477;
	public static final int X86_INS_MOVSXD = 478;
	public static final int X86_INS_MOVUPD = 479;
	public static final int X86_INS_MOVUPS = 480;
	public static final int X86_INS_MOVZX = 481;
	public static final int X86_INS_MPSADBW = 482;
	public static final int X86_INS_MUL = 483;
	public static final int X86_INS_MULPD = 484;
	public static final int X86_INS_MULPS = 485;
	public static final int X86_INS_MULSD = 486;
	public static final int X86_INS_MULSS = 487;
	public static final int X86_INS_MULX = 488;
	public static final int X86_INS_FMUL = 489;
	public static final int X86_INS_FIMUL = 490;
	public static final int X86_INS_FMULP = 491;
	public static final int X86_INS_MWAIT = 492;
	public static final int X86_INS_NEG = 493;
	public static final int X86_INS_NOP = 494;
	public static final int X86_INS_NOT = 495;
	public static final int X86_INS_OUT = 496;
	public static final int X86_INS_OUTSB = 497;
	public static final int X86_INS_OUTSD = 498;
	public static final int X86_INS_OUTSW = 499;
	public static final int X86_INS_PACKUSDW = 500;
	public static final int X86_INS_PAUSE = 501;
	public static final int X86_INS_PAVGUSB = 502;
	public static final int X86_INS_PBLENDVB = 503;
	public static final int X86_INS_PBLENDW = 504;
	public static final int X86_INS_PCLMULQDQ = 505;
	public static final int X86_INS_PCMPEQQ = 506;
	public static final int X86_INS_PCMPESTRI = 507;
	public static final int X86_INS_PCMPESTRM = 508;
	public static final int X86_INS_PCMPGTQ = 509;
	public static final int X86_INS_PCMPISTRI = 510;
	public static final int X86_INS_PCMPISTRM = 511;
	public static final int X86_INS_PCOMMIT = 512;
	public static final int X86_INS_PDEP = 513;
	public static final int X86_INS_PEXT = 514;
	public static final int X86_INS_PEXTRB = 515;
	public static final int X86_INS_PEXTRD = 516;
	public static final int X86_INS_PEXTRQ = 517;
	public static final int X86_INS_PF2ID = 518;
	public static final int X86_INS_PF2IW = 519;
	public static final int X86_INS_PFACC = 520;
	public static final int X86_INS_PFADD = 521;
	public static final int X86_INS_PFCMPEQ = 522;
	public static final int X86_INS_PFCMPGE = 523;
	public static final int X86_INS_PFCMPGT = 524;
	public static final int X86_INS_PFMAX = 525;
	public static final int X86_INS_PFMIN = 526;
	public static final int X86_INS_PFMUL = 527;
	public static final int X86_INS_PFNACC = 528;
	public static final int X86_INS_PFPNACC = 529;
	public static final int X86_INS_PFRCPIT1 = 530;
	public static final int X86_INS_PFRCPIT2 = 531;
	public static final int X86_INS_PFRCP = 532;
	public static final int X86_INS_PFRSQIT1 = 533;
	public static final int X86_INS_PFRSQRT = 534;
	public static final int X86_INS_PFSUBR = 535;
	public static final int X86_INS_PFSUB = 536;
	public static final int X86_INS_PHMINPOSUW = 537;
	public static final int X86_INS_PI2FD = 538;
	public static final int X86_INS_PI2FW = 539;
	public static final int X86_INS_PINSRB = 540;
	public static final int X86_INS_PINSRD = 541;
	public static final int X86_INS_PINSRQ = 542;
	public static final int X86_INS_PMAXSB = 543;
	public static final int X86_INS_PMAXSD = 544;
	public static final int X86_INS_PMAXUD = 545;
	public static final int X86_INS_PMAXUW = 546;
	public static final int X86_INS_PMINSB = 547;
	public static final int X86_INS_PMINSD = 548;
	public static final int X86_INS_PMINUD = 549;
	public static final int X86_INS_PMINUW = 550;
	public static final int X86_INS_PMOVSXBD = 551;
	public static final int X86_INS_PMOVSXBQ = 552;
	public static final int X86_INS_PMOVSXBW = 553;
	public static final int X86_INS_PMOVSXDQ = 554;
	public static final int X86_INS_PMOVSXWD = 555;
	public static final int X86_INS_PMOVSXWQ = 556;
	public static final int X86_INS_PMOVZXBD = 557;
	public static final int X86_INS_PMOVZXBQ = 558;
	public static final int X86_INS_PMOVZXBW = 559;
	public static final int X86_INS_PMOVZXDQ = 560;
	public static final int X86_INS_PMOVZXWD = 561;
	public static final int X86_INS_PMOVZXWQ = 562;
	public static final int X86_INS_PMULDQ = 563;
	public static final int X86_INS_PMULHRW = 564;
	public static final int X86_INS_PMULLD = 565;
	public static final int X86_INS_POP = 566;
	public static final int X86_INS_POPAW = 567;
	public static final int X86_INS_POPAL = 568;
	public static final int X86_INS_POPCNT = 569;
	public static final int X86_INS_POPF = 570;
	public static final int X86_INS_POPFD = 571;
	public static final int X86_INS_POPFQ = 572;
	public static final int X86_INS_PREFETCH = 573;
	public static final int X86_INS_PREFETCHNTA = 574;
	public static final int X86_INS_PREFETCHT0 = 575;
	public static final int X86_INS_PREFETCHT1 = 576;
	public static final int X86_INS_PREFETCHT2 = 577;
	public static final int X86_INS_PREFETCHW = 578;
	public static final int X86_INS_PSHUFD = 579;
	public static final int X86_INS_PSHUFHW = 580;
	public static final int X86_INS_PSHUFLW = 581;
	public static final int X86_INS_PSLLDQ = 582;
	public static final int X86_INS_PSRLDQ = 583;
	public static final int X86_INS_PSWAPD = 584;
	public static final int X86_INS_PTEST = 585;
	public static final int X86_INS_PUNPCKHQDQ = 586;
	public static final int X86_INS_PUNPCKLQDQ = 587;
	public static final int X86_INS_PUSH = 588;
	public static final int X86_INS_PUSHAW = 589;
	public static final int X86_INS_PUSHAL = 590;
	public static final int X86_INS_PUSHF = 591;
	public static final int X86_INS_PUSHFD = 592;
	public static final int X86_INS_PUSHFQ = 593;
	public static final int X86_INS_RCL = 594;
	public static final int X86_INS_RCPPS = 595;
	public static final int X86_INS_RCPSS = 596;
	public static final int X86_INS_RCR = 597;
	public static final int X86_INS_RDFSBASE = 598;
	public static final int X86_INS_RDGSBASE = 599;
	public static final int X86_INS_RDMSR = 600;
	public static final int X86_INS_RDPMC = 601;
	public static final int X86_INS_RDRAND = 602;
	public static final int X86_INS_RDSEED = 603;
	public static final int X86_INS_RDTSC = 604;
	public static final int X86_INS_RDTSCP = 605;
	public static final int X86_INS_ROL = 606;
	public static final int X86_INS_ROR = 607;
	public static final int X86_INS_RORX = 608;
	public static final int X86_INS_ROUNDPD = 609;
	public static final int X86_INS_ROUNDPS = 610;
	public static final int X86_INS_ROUNDSD = 611;
	public static final int X86_INS_ROUNDSS = 612;
	public static final int X86_INS_RSM = 613;
	public static final int X86_INS_RSQRTPS = 614;
	public static final int X86_INS_RSQRTSS = 615;
	public static final int X86_INS_SAHF = 616;
	public static final int X86_INS_SAL = 617;
	public static final int X86_INS_SALC = 618;
	public static final int X86_INS_SAR = 619;
	public static final int X86_INS_SARX = 620;
	public static final int X86_INS_SBB = 621;
	public static final int X86_INS_SCASB = 622;
	public static final int X86_INS_SCASD = 623;
	public static final int X86_INS_SCASQ = 624;
	public static final int X86_INS_SCASW = 625;
	public static final int X86_INS_SETAE = 626;
	public static final int X86_INS_SETA = 627;
	public static final int X86_INS_SETBE = 628;
	public static final int X86_INS_SETB = 629;
	public static final int X86_INS_SETE = 630;
	public static final int X86_INS_SETGE = 631;
	public static final int X86_INS_SETG = 632;
	public static final int X86_INS_SETLE = 633;
	public static final int X86_INS_SETL = 634;
	public static final int X86_INS_SETNE = 635;
	public static final int X86_INS_SETNO = 636;
	public static final int X86_INS_SETNP = 637;
	public static final int X86_INS_SETNS = 638;
	public static final int X86_INS_SETO = 639;
	public static final int X86_INS_SETP = 640;
	public static final int X86_INS_SETS = 641;
	public static final int X86_INS_SFENCE = 642;
	public static final int X86_INS_SGDT = 643;
	public static final int X86_INS_SHA1MSG1 = 644;
	public static final int X86_INS_SHA1MSG2 = 645;
	public static final int X86_INS_SHA1NEXTE = 646;
	public static final int X86_INS_SHA1RNDS4 = 647;
	public static final int X86_INS_SHA256MSG1 = 648;
	public static final int X86_INS_SHA256MSG2 = 649;
	public static final int X86_INS_SHA256RNDS2 = 650;
	public static final int X86_INS_SHL = 651;
	public static final int X86_INS_SHLD = 652;
	public static final int X86_INS_SHLX = 653;
	public static final int X86_INS_SHR = 654;
	public static final int X86_INS_SHRD = 655;
	public static final int X86_INS_SHRX = 656;
	public static final int X86_INS_SHUFPD = 657;
	public static final int X86_INS_SHUFPS = 658;
	public static final int X86_INS_SIDT = 659;
	public static final int X86_INS_FSIN = 660;
	public static final int X86_INS_SKINIT = 661;
	public static final int X86_INS_SLDT = 662;
	public static final int X86_INS_SMSW = 663;
	public static final int X86_INS_SQRTPD = 664;
	public static final int X86_INS_SQRTPS = 665;
	public static final int X86_INS_SQRTSD = 666;
	public static final int X86_INS_SQRTSS = 667;
	public static final int X86_INS_FSQRT = 668;
	public static final int X86_INS_STAC = 669;
	public static final int X86_INS_STC = 670;
	public static final int X86_INS_STD = 671;
	public static final int X86_INS_STGI = 672;
	public static final int X86_INS_STI = 673;
	public static final int X86_INS_STMXCSR = 674;
	public static final int X86_INS_STOSB = 675;
	public static final int X86_INS_STOSD = 676;
	public static final int X86_INS_STOSQ = 677;
	public static final int X86_INS_STOSW = 678;
	public static final int X86_INS_STR = 679;
	public static final int X86_INS_FST = 680;
	public static final int X86_INS_FSTP = 681;
	public static final int X86_INS_FSTPNCE = 682;
	public static final int X86_INS_FXCH = 683;
	public static final int X86_INS_SUBPD = 684;
	public static final int X86_INS_SUBPS = 685;
	public static final int X86_INS_FSUBR = 686;
	public static final int X86_INS_FISUBR = 687;
	public static final int X86_INS_FSUBRP = 688;
	public static final int X86_INS_SUBSD = 689;
	public static final int X86_INS_SUBSS = 690;
	public static final int X86_INS_FSUB = 691;
	public static final int X86_INS_FISUB = 692;
	public static final int X86_INS_FSUBP = 693;
	public static final int X86_INS_SWAPGS = 694;
	public static final int X86_INS_SYSCALL = 695;
	public static final int X86_INS_SYSENTER = 696;
	public static final int X86_INS_SYSEXIT = 697;
	public static final int X86_INS_SYSRET = 698;
	public static final int X86_INS_T1MSKC = 699;
	public static final int X86_INS_TEST = 700;
	public static final int X86_INS_UD2 = 701;
	public static final int X86_INS_FTST = 702;
	public static final int X86_INS_TZCNT = 703;
	public static final int X86_INS_TZMSK = 704;
	public static final int X86_INS_FUCOMIP = 705;
	public static final int X86_INS_FUCOMI = 706;
	public static final int X86_INS_FUCOMPP = 707;
	public static final int X86_INS_FUCOMP = 708;
	public static final int X86_INS_FUCOM = 709;
	public static final int X86_INS_UD2B = 710;
	public static final int X86_INS_UNPCKHPD = 711;
	public static final int X86_INS_UNPCKHPS = 712;
	public static final int X86_INS_UNPCKLPD = 713;
	public static final int X86_INS_UNPCKLPS = 714;
	public static final int X86_INS_VADDPD = 715;
	public static final int X86_INS_VADDPS = 716;
	public static final int X86_INS_VADDSD = 717;
	public static final int X86_INS_VADDSS = 718;
	public static final int X86_INS_VADDSUBPD = 719;
	public static final int X86_INS_VADDSUBPS = 720;
	public static final int X86_INS_VAESDECLAST = 721;
	public static final int X86_INS_VAESDEC = 722;
	public static final int X86_INS_VAESENCLAST = 723;
	public static final int X86_INS_VAESENC = 724;
	public static final int X86_INS_VAESIMC = 725;
	public static final int X86_INS_VAESKEYGENASSIST = 726;
	public static final int X86_INS_VALIGND = 727;
	public static final int X86_INS_VALIGNQ = 728;
	public static final int X86_INS_VANDNPD = 729;
	public static final int X86_INS_VANDNPS = 730;
	public static final int X86_INS_VANDPD = 731;
	public static final int X86_INS_VANDPS = 732;
	public static final int X86_INS_VBLENDMPD = 733;
	public static final int X86_INS_VBLENDMPS = 734;
	public static final int X86_INS_VBLENDPD = 735;
	public static final int X86_INS_VBLENDPS = 736;
	public static final int X86_INS_VBLENDVPD = 737;
	public static final int X86_INS_VBLENDVPS = 738;
	public static final int X86_INS_VBROADCASTF128 = 739;
	public static final int X86_INS_VBROADCASTI32X4 = 740;
	public static final int X86_INS_VBROADCASTI64X4 = 741;
	public static final int X86_INS_VBROADCASTSD = 742;
	public static final int X86_INS_VBROADCASTSS = 743;
	public static final int X86_INS_VCOMPRESSPD = 744;
	public static final int X86_INS_VCOMPRESSPS = 745;
	public static final int X86_INS_VCVTDQ2PD = 746;
	public static final int X86_INS_VCVTDQ2PS = 747;
	public static final int X86_INS_VCVTPD2DQX = 748;
	public static final int X86_INS_VCVTPD2DQ = 749;
	public static final int X86_INS_VCVTPD2PSX = 750;
	public static final int X86_INS_VCVTPD2PS = 751;
	public static final int X86_INS_VCVTPD2UDQ = 752;
	public static final int X86_INS_VCVTPH2PS = 753;
	public static final int X86_INS_VCVTPS2DQ = 754;
	public static final int X86_INS_VCVTPS2PD = 755;
	public static final int X86_INS_VCVTPS2PH = 756;
	public static final int X86_INS_VCVTPS2UDQ = 757;
	public static final int X86_INS_VCVTSD2SI = 758;
	public static final int X86_INS_VCVTSD2USI = 759;
	public static final int X86_INS_VCVTSS2SI = 760;
	public static final int X86_INS_VCVTSS2USI = 761;
	public static final int X86_INS_VCVTTPD2DQX = 762;
	public static final int X86_INS_VCVTTPD2DQ = 763;
	public static final int X86_INS_VCVTTPD2UDQ = 764;
	public static final int X86_INS_VCVTTPS2DQ = 765;
	public static final int X86_INS_VCVTTPS2UDQ = 766;
	public static final int X86_INS_VCVTUDQ2PD = 767;
	public static final int X86_INS_VCVTUDQ2PS = 768;
	public static final int X86_INS_VDIVPD = 769;
	public static final int X86_INS_VDIVPS = 770;
	public static final int X86_INS_VDIVSD = 771;
	public static final int X86_INS_VDIVSS = 772;
	public static final int X86_INS_VDPPD = 773;
	public static final int X86_INS_VDPPS = 774;
	public static final int X86_INS_VERR = 775;
	public static final int X86_INS_VERW = 776;
	public static final int X86_INS_VEXP2PD = 777;
	public static final int X86_INS_VEXP2PS = 778;
	public static final int X86_INS_VEXPANDPD = 779;
	public static final int X86_INS_VEXPANDPS = 780;
	public static final int X86_INS_VEXTRACTF128 = 781;
	public static final int X86_INS_VEXTRACTF32X4 = 782;
	public static final int X86_INS_VEXTRACTF64X4 = 783;
	public static final int X86_INS_VEXTRACTI128 = 784;
	public static final int X86_INS_VEXTRACTI32X4 = 785;
	public static final int X86_INS_VEXTRACTI64X4 = 786;
	public static final int X86_INS_VEXTRACTPS = 787;
	public static final int X86_INS_VFMADD132PD = 788;
	public static final int X86_INS_VFMADD132PS = 789;
	public static final int X86_INS_VFMADDPD = 790;
	public static final int X86_INS_VFMADD213PD = 791;
	public static final int X86_INS_VFMADD231PD = 792;
	public static final int X86_INS_VFMADDPS = 793;
	public static final int X86_INS_VFMADD213PS = 794;
	public static final int X86_INS_VFMADD231PS = 795;
	public static final int X86_INS_VFMADDSD = 796;
	public static final int X86_INS_VFMADD213SD = 797;
	public static final int X86_INS_VFMADD132SD = 798;
	public static final int X86_INS_VFMADD231SD = 799;
	public static final int X86_INS_VFMADDSS = 800;
	public static final int X86_INS_VFMADD213SS = 801;
	public static final int X86_INS_VFMADD132SS = 802;
	public static final int X86_INS_VFMADD231SS = 803;
	public static final int X86_INS_VFMADDSUB132PD = 804;
	public static final int X86_INS_VFMADDSUB132PS = 805;
	public static final int X86_INS_VFMADDSUBPD = 806;
	public static final int X86_INS_VFMADDSUB213PD = 807;
	public static final int X86_INS_VFMADDSUB231PD = 808;
	public static final int X86_INS_VFMADDSUBPS = 809;
	public static final int X86_INS_VFMADDSUB213PS = 810;
	public static final int X86_INS_VFMADDSUB231PS = 811;
	public static final int X86_INS_VFMSUB132PD = 812;
	public static final int X86_INS_VFMSUB132PS = 813;
	public static final int X86_INS_VFMSUBADD132PD = 814;
	public static final int X86_INS_VFMSUBADD132PS = 815;
	public static final int X86_INS_VFMSUBADDPD = 816;
	public static final int X86_INS_VFMSUBADD213PD = 817;
	public static final int X86_INS_VFMSUBADD231PD = 818;
	public static final int X86_INS_VFMSUBADDPS = 819;
	public static final int X86_INS_VFMSUBADD213PS = 820;
	public static final int X86_INS_VFMSUBADD231PS = 821;
	public static final int X86_INS_VFMSUBPD = 822;
	public static final int X86_INS_VFMSUB213PD = 823;
	public static final int X86_INS_VFMSUB231PD = 824;
	public static final int X86_INS_VFMSUBPS = 825;
	public static final int X86_INS_VFMSUB213PS = 826;
	public static final int X86_INS_VFMSUB231PS = 827;
	public static final int X86_INS_VFMSUBSD = 828;
	public static final int X86_INS_VFMSUB213SD = 829;
	public static final int X86_INS_VFMSUB132SD = 830;
	public static final int X86_INS_VFMSUB231SD = 831;
	public static final int X86_INS_VFMSUBSS = 832;
	public static final int X86_INS_VFMSUB213SS = 833;
	public static final int X86_INS_VFMSUB132SS = 834;
	public static final int X86_INS_VFMSUB231SS = 835;
	public static final int X86_INS_VFNMADD132PD = 836;
	public static final int X86_INS_VFNMADD132PS = 837;
	public static final int X86_INS_VFNMADDPD = 838;
	public static final int X86_INS_VFNMADD213PD = 839;
	public static final int X86_INS_VFNMADD231PD = 840;
	public static final int X86_INS_VFNMADDPS = 841;
	public static final int X86_INS_VFNMADD213PS = 842;
	public static final int X86_INS_VFNMADD231PS = 843;
	public static final int X86_INS_VFNMADDSD = 844;
	public static final int X86_INS_VFNMADD213SD = 845;
	public static final int X86_INS_VFNMADD132SD = 846;
	public static final int X86_INS_VFNMADD231SD = 847;
	public static final int X86_INS_VFNMADDSS = 848;
	public static final int X86_INS_VFNMADD213SS = 849;
	public static final int X86_INS_VFNMADD132SS = 850;
	public static final int X86_INS_VFNMADD231SS = 851;
	public static final int X86_INS_VFNMSUB132PD = 852;
	public static final int X86_INS_VFNMSUB132PS = 853;
	public static final int X86_INS_VFNMSUBPD = 854;
	public static final int X86_INS_VFNMSUB213PD = 855;
	public static final int X86_INS_VFNMSUB231PD = 856;
	public static final int X86_INS_VFNMSUBPS = 857;
	public static final int X86_INS_VFNMSUB213PS = 858;
	public static final int X86_INS_VFNMSUB231PS = 859;
	public static final int X86_INS_VFNMSUBSD = 860;
	public static final int X86_INS_VFNMSUB213SD = 861;
	public static final int X86_INS_VFNMSUB132SD = 862;
	public static final int X86_INS_VFNMSUB231SD = 863;
	public static final int X86_INS_VFNMSUBSS = 864;
	public static final int X86_INS_VFNMSUB213SS = 865;
	public static final int X86_INS_VFNMSUB132SS = 866;
	public static final int X86_INS_VFNMSUB231SS = 867;
	public static final int X86_INS_VFRCZPD = 868;
	public static final int X86_INS_VFRCZPS = 869;
	public static final int X86_INS_VFRCZSD = 870;
	public static final int X86_INS_VFRCZSS = 871;
	public static final int X86_INS_VORPD = 872;
	public static final int X86_INS_VORPS = 873;
	public static final int X86_INS_VXORPD = 874;
	public static final int X86_INS_VXORPS = 875;
	public static final int X86_INS_VGATHERDPD = 876;
	public static final int X86_INS_VGATHERDPS = 877;
	public static final int X86_INS_VGATHERPF0DPD = 878;
	public static final int X86_INS_VGATHERPF0DPS = 879;
	public static final int X86_INS_VGATHERPF0QPD = 880;
	public static final int X86_INS_VGATHERPF0QPS = 881;
	public static final int X86_INS_VGATHERPF1DPD = 882;
	public static final int X86_INS_VGATHERPF1DPS = 883;
	public static final int X86_INS_VGATHERPF1QPD = 884;
	public static final int X86_INS_VGATHERPF1QPS = 885;
	public static final int X86_INS_VGATHERQPD = 886;
	public static final int X86_INS_VGATHERQPS = 887;
	public static final int X86_INS_VHADDPD = 888;
	public static final int X86_INS_VHADDPS = 889;
	public static final int X86_INS_VHSUBPD = 890;
	public static final int X86_INS_VHSUBPS = 891;
	public static final int X86_INS_VINSERTF128 = 892;
	public static final int X86_INS_VINSERTF32X4 = 893;
	public static final int X86_INS_VINSERTF32X8 = 894;
	public static final int X86_INS_VINSERTF64X2 = 895;
	public static final int X86_INS_VINSERTF64X4 = 896;
	public static final int X86_INS_VINSERTI128 = 897;
	public static final int X86_INS_VINSERTI32X4 = 898;
	public static final int X86_INS_VINSERTI32X8 = 899;
	public static final int X86_INS_VINSERTI64X2 = 900;
	public static final int X86_INS_VINSERTI64X4 = 901;
	public static final int X86_INS_VINSERTPS = 902;
	public static final int X86_INS_VLDDQU = 903;
	public static final int X86_INS_VLDMXCSR = 904;
	public static final int X86_INS_VMASKMOVDQU = 905;
	public static final int X86_INS_VMASKMOVPD = 906;
	public static final int X86_INS_VMASKMOVPS = 907;
	public static final int X86_INS_VMAXPD = 908;
	public static final int X86_INS_VMAXPS = 909;
	public static final int X86_INS_VMAXSD = 910;
	public static final int X86_INS_VMAXSS = 911;
	public static final int X86_INS_VMCALL = 912;
	public static final int X86_INS_VMCLEAR = 913;
	public static final int X86_INS_VMFUNC = 914;
	public static final int X86_INS_VMINPD = 915;
	public static final int X86_INS_VMINPS = 916;
	public static final int X86_INS_VMINSD = 917;
	public static final int X86_INS_VMINSS = 918;
	public static final int X86_INS_VMLAUNCH = 919;
	public static final int X86_INS_VMLOAD = 920;
	public static final int X86_INS_VMMCALL = 921;
	public static final int X86_INS_VMOVQ = 922;
	public static final int X86_INS_VMOVDDUP = 923;
	public static final int X86_INS_VMOVD = 924;
	public static final int X86_INS_VMOVDQA32 = 925;
	public static final int X86_INS_VMOVDQA64 = 926;
	public static final int X86_INS_VMOVDQA = 927;
	public static final int X86_INS_VMOVDQU16 = 928;
	public static final int X86_INS_VMOVDQU32 = 929;
	public static final int X86_INS_VMOVDQU64 = 930;
	public static final int X86_INS_VMOVDQU8 = 931;
	public static final int X86_INS_VMOVDQU = 932;
	public static final int X86_INS_VMOVHLPS = 933;
	public static final int X86_INS_VMOVHPD = 934;
	public static final int X86_INS_VMOVHPS = 935;
	public static final int X86_INS_VMOVLHPS = 936;
	public static final int X86_INS_VMOVLPD = 937;
	public static final int X86_INS_VMOVLPS = 938;
	public static final int X86_INS_VMOVMSKPD = 939;
	public static final int X86_INS_VMOVMSKPS = 940;
	public static final int X86_INS_VMOVNTDQA = 941;
	public static final int X86_INS_VMOVNTDQ = 942;
	public static final int X86_INS_VMOVNTPD = 943;
	public static final int X86_INS_VMOVNTPS = 944;
	public static final int X86_INS_VMOVSD = 945;
	public static final int X86_INS_VMOVSHDUP = 946;
	public static final int X86_INS_VMOVSLDUP = 947;
	public static final int X86_INS_VMOVSS = 948;
	public static final int X86_INS_VMOVUPD = 949;
	public static final int X86_INS_VMOVUPS = 950;
	public static final int X86_INS_VMPSADBW = 951;
	public static final int X86_INS_VMPTRLD = 952;
	public static final int X86_INS_VMPTRST = 953;
	public static final int X86_INS_VMREAD = 954;
	public static final int X86_INS_VMRESUME = 955;
	public static final int X86_INS_VMRUN = 956;
	public static final int X86_INS_VMSAVE = 957;
	public static final int X86_INS_VMULPD = 958;
	public static final int X86_INS_VMULPS = 959;
	public static final int X86_INS_VMULSD = 960;
	public static final int X86_INS_VMULSS = 961;
	public static final int X86_INS_VMWRITE = 962;
	public static final int X86_INS_VMXOFF = 963;
	public static final int X86_INS_VMXON = 964;
	public static final int X86_INS_VPABSB = 965;
	public static final int X86_INS_VPABSD = 966;
	public static final int X86_INS_VPABSQ = 967;
	public static final int X86_INS_VPABSW = 968;
	public static final int X86_INS_VPACKSSDW = 969;
	public static final int X86_INS_VPACKSSWB = 970;
	public static final int X86_INS_VPACKUSDW = 971;
	public static final int X86_INS_VPACKUSWB = 972;
	public static final int X86_INS_VPADDB = 973;
	public static final int X86_INS_VPADDD = 974;
	public static final int X86_INS_VPADDQ = 975;
	public static final int X86_INS_VPADDSB = 976;
	public static final int X86_INS_VPADDSW = 977;
	public static final int X86_INS_VPADDUSB = 978;
	public static final int X86_INS_VPADDUSW = 979;
	public static final int X86_INS_VPADDW = 980;
	public static final int X86_INS_VPALIGNR = 981;
	public static final int X86_INS_VPANDD = 982;
	public static final int X86_INS_VPANDND = 983;
	public static final int X86_INS_VPANDNQ = 984;
	public static final int X86_INS_VPANDN = 985;
	public static final int X86_INS_VPANDQ = 986;
	public static final int X86_INS_VPAND = 987;
	public static final int X86_INS_VPAVGB = 988;
	public static final int X86_INS_VPAVGW = 989;
	public static final int X86_INS_VPBLENDD = 990;
	public static final int X86_INS_VPBLENDMB = 991;
	public static final int X86_INS_VPBLENDMD = 992;
	public static final int X86_INS_VPBLENDMQ = 993;
	public static final int X86_INS_VPBLENDMW = 994;
	public static final int X86_INS_VPBLENDVB = 995;
	public static final int X86_INS_VPBLENDW = 996;
	public static final int X86_INS_VPBROADCASTB = 997;
	public static final int X86_INS_VPBROADCASTD = 998;
	public static final int X86_INS_VPBROADCASTMB2Q = 999;
	public static final int X86_INS_VPBROADCASTMW2D = 1000;
	public static final int X86_INS_VPBROADCASTQ = 1001;
	public static final int X86_INS_VPBROADCASTW = 1002;
	public static final int X86_INS_VPCLMULQDQ = 1003;
	public static final int X86_INS_VPCMOV = 1004;
	public static final int X86_INS_VPCMPB = 1005;
	public static final int X86_INS_VPCMPD = 1006;
	public static final int X86_INS_VPCMPEQB = 1007;
	public static final int X86_INS_VPCMPEQD = 1008;
	public static final int X86_INS_VPCMPEQQ = 1009;
	public static final int X86_INS_VPCMPEQW = 1010;
	public static final int X86_INS_VPCMPESTRI = 1011;
	public static final int X86_INS_VPCMPESTRM = 1012;
	public static final int X86_INS_VPCMPGTB = 1013;
	public static final int X86_INS_VPCMPGTD = 1014;
	public static final int X86_INS_VPCMPGTQ = 1015;
	public static final int X86_INS_VPCMPGTW = 1016;
	public static final int X86_INS_VPCMPISTRI = 1017;
	public static final int X86_INS_VPCMPISTRM = 1018;
	public static final int X86_INS_VPCMPQ = 1019;
	public static final int X86_INS_VPCMPUB = 1020;
	public static final int X86_INS_VPCMPUD = 1021;
	public static final int X86_INS_VPCMPUQ = 1022;
	public static final int X86_INS_VPCMPUW = 1023;
	public static final int X86_INS_VPCMPW = 1024;
	public static final int X86_INS_VPCOMB = 1025;
	public static final int X86_INS_VPCOMD = 1026;
	public static final int X86_INS_VPCOMPRESSD = 1027;
	public static final int X86_INS_VPCOMPRESSQ = 1028;
	public static final int X86_INS_VPCOMQ = 1029;
	public static final int X86_INS_VPCOMUB = 1030;
	public static final int X86_INS_VPCOMUD = 1031;
	public static final int X86_INS_VPCOMUQ = 1032;
	public static final int X86_INS_VPCOMUW = 1033;
	public static final int X86_INS_VPCOMW = 1034;
	public static final int X86_INS_VPCONFLICTD = 1035;
	public static final int X86_INS_VPCONFLICTQ = 1036;
	public static final int X86_INS_VPERM2F128 = 1037;
	public static final int X86_INS_VPERM2I128 = 1038;
	public static final int X86_INS_VPERMD = 1039;
	public static final int X86_INS_VPERMI2D = 1040;
	public static final int X86_INS_VPERMI2PD = 1041;
	public static final int X86_INS_VPERMI2PS = 1042;
	public static final int X86_INS_VPERMI2Q = 1043;
	public static final int X86_INS_VPERMIL2PD = 1044;
	public static final int X86_INS_VPERMIL2PS = 1045;
	public static final int X86_INS_VPERMILPD = 1046;
	public static final int X86_INS_VPERMILPS = 1047;
	public static final int X86_INS_VPERMPD = 1048;
	public static final int X86_INS_VPERMPS = 1049;
	public static final int X86_INS_VPERMQ = 1050;
	public static final int X86_INS_VPERMT2D = 1051;
	public static final int X86_INS_VPERMT2PD = 1052;
	public static final int X86_INS_VPERMT2PS = 1053;
	public static final int X86_INS_VPERMT2Q = 1054;
	public static final int X86_INS_VPEXPANDD = 1055;
	public static final int X86_INS_VPEXPANDQ = 1056;
	public static final int X86_INS_VPEXTRB = 1057;
	public static final int X86_INS_VPEXTRD = 1058;
	public static final int X86_INS_VPEXTRQ = 1059;
	public static final int X86_INS_VPEXTRW = 1060;
	public static final int X86_INS_VPGATHERDD = 1061;
	public static final int X86_INS_VPGATHERDQ = 1062;
	public static final int X86_INS_VPGATHERQD = 1063;
	public static final int X86_INS_VPGATHERQQ = 1064;
	public static final int X86_INS_VPHADDBD = 1065;
	public static final int X86_INS_VPHADDBQ = 1066;
	public static final int X86_INS_VPHADDBW = 1067;
	public static final int X86_INS_VPHADDDQ = 1068;
	public static final int X86_INS_VPHADDD = 1069;
	public static final int X86_INS_VPHADDSW = 1070;
	public static final int X86_INS_VPHADDUBD = 1071;
	public static final int X86_INS_VPHADDUBQ = 1072;
	public static final int X86_INS_VPHADDUBW = 1073;
	public static final int X86_INS_VPHADDUDQ = 1074;
	public static final int X86_INS_VPHADDUWD = 1075;
	public static final int X86_INS_VPHADDUWQ = 1076;
	public static final int X86_INS_VPHADDWD = 1077;
	public static final int X86_INS_VPHADDWQ = 1078;
	public static final int X86_INS_VPHADDW = 1079;
	public static final int X86_INS_VPHMINPOSUW = 1080;
	public static final int X86_INS_VPHSUBBW = 1081;
	public static final int X86_INS_VPHSUBDQ = 1082;
	public static final int X86_INS_VPHSUBD = 1083;
	public static final int X86_INS_VPHSUBSW = 1084;
	public static final int X86_INS_VPHSUBWD = 1085;
	public static final int X86_INS_VPHSUBW = 1086;
	public static final int X86_INS_VPINSRB = 1087;
	public static final int X86_INS_VPINSRD = 1088;
	public static final int X86_INS_VPINSRQ = 1089;
	public static final int X86_INS_VPINSRW = 1090;
	public static final int X86_INS_VPLZCNTD = 1091;
	public static final int X86_INS_VPLZCNTQ = 1092;
	public static final int X86_INS_VPMACSDD = 1093;
	public static final int X86_INS_VPMACSDQH = 1094;
	public static final int X86_INS_VPMACSDQL = 1095;
	public static final int X86_INS_VPMACSSDD = 1096;
	public static final int X86_INS_VPMACSSDQH = 1097;
	public static final int X86_INS_VPMACSSDQL = 1098;
	public static final int X86_INS_VPMACSSWD = 1099;
	public static final int X86_INS_VPMACSSWW = 1100;
	public static final int X86_INS_VPMACSWD = 1101;
	public static final int X86_INS_VPMACSWW = 1102;
	public static final int X86_INS_VPMADCSSWD = 1103;
	public static final int X86_INS_VPMADCSWD = 1104;
	public static final int X86_INS_VPMADDUBSW = 1105;
	public static final int X86_INS_VPMADDWD = 1106;
	public static final int X86_INS_VPMASKMOVD = 1107;
	public static final int X86_INS_VPMASKMOVQ = 1108;
	public static final int X86_INS_VPMAXSB = 1109;
	public static final int X86_INS_VPMAXSD = 1110;
	public static final int X86_INS_VPMAXSQ = 1111;
	public static final int X86_INS_VPMAXSW = 1112;
	public static final int X86_INS_VPMAXUB = 1113;
	public static final int X86_INS_VPMAXUD = 1114;
	public static final int X86_INS_VPMAXUQ = 1115;
	public static final int X86_INS_VPMAXUW = 1116;
	public static final int X86_INS_VPMINSB = 1117;
	public static final int X86_INS_VPMINSD = 1118;
	public static final int X86_INS_VPMINSQ = 1119;
	public static final int X86_INS_VPMINSW = 1120;
	public static final int X86_INS_VPMINUB = 1121;
	public static final int X86_INS_VPMINUD = 1122;
	public static final int X86_INS_VPMINUQ = 1123;
	public static final int X86_INS_VPMINUW = 1124;
	public static final int X86_INS_VPMOVDB = 1125;
	public static final int X86_INS_VPMOVDW = 1126;
	public static final int X86_INS_VPMOVM2B = 1127;
	public static final int X86_INS_VPMOVM2D = 1128;
	public static final int X86_INS_VPMOVM2Q = 1129;
	public static final int X86_INS_VPMOVM2W = 1130;
	public static final int X86_INS_VPMOVMSKB = 1131;
	public static final int X86_INS_VPMOVQB = 1132;
	public static final int X86_INS_VPMOVQD = 1133;
	public static final int X86_INS_VPMOVQW = 1134;
	public static final int X86_INS_VPMOVSDB = 1135;
	public static final int X86_INS_VPMOVSDW = 1136;
	public static final int X86_INS_VPMOVSQB = 1137;
	public static final int X86_INS_VPMOVSQD = 1138;
	public static final int X86_INS_VPMOVSQW = 1139;
	public static final int X86_INS_VPMOVSXBD = 1140;
	public static final int X86_INS_VPMOVSXBQ = 1141;
	public static final int X86_INS_VPMOVSXBW = 1142;
	public static final int X86_INS_VPMOVSXDQ = 1143;
	public static final int X86_INS_VPMOVSXWD = 1144;
	public static final int X86_INS_VPMOVSXWQ = 1145;
	public static final int X86_INS_VPMOVUSDB = 1146;
	public static final int X86_INS_VPMOVUSDW = 1147;
	public static final int X86_INS_VPMOVUSQB = 1148;
	public static final int X86_INS_VPMOVUSQD = 1149;
	public static final int X86_INS_VPMOVUSQW = 1150;
	public static final int X86_INS_VPMOVZXBD = 1151;
	public static final int X86_INS_VPMOVZXBQ = 1152;
	public static final int X86_INS_VPMOVZXBW = 1153;
	public static final int X86_INS_VPMOVZXDQ = 1154;
	public static final int X86_INS_VPMOVZXWD = 1155;
	public static final int X86_INS_VPMOVZXWQ = 1156;
	public static final int X86_INS_VPMULDQ = 1157;
	public static final int X86_INS_VPMULHRSW = 1158;
	public static final int X86_INS_VPMULHUW = 1159;
	public static final int X86_INS_VPMULHW = 1160;
	public static final int X86_INS_VPMULLD = 1161;
	public static final int X86_INS_VPMULLQ = 1162;
	public static final int X86_INS_VPMULLW = 1163;
	public static final int X86_INS_VPMULUDQ = 1164;
	public static final int X86_INS_VPORD = 1165;
	public static final int X86_INS_VPORQ = 1166;
	public static final int X86_INS_VPOR = 1167;
	public static final int X86_INS_VPPERM = 1168;
	public static final int X86_INS_VPROTB = 1169;
	public static final int X86_INS_VPROTD = 1170;
	public static final int X86_INS_VPROTQ = 1171;
	public static final int X86_INS_VPROTW = 1172;
	public static final int X86_INS_VPSADBW = 1173;
	public static final int X86_INS_VPSCATTERDD = 1174;
	public static final int X86_INS_VPSCATTERDQ = 1175;
	public static final int X86_INS_VPSCATTERQD = 1176;
	public static final int X86_INS_VPSCATTERQQ = 1177;
	public static final int X86_INS_VPSHAB = 1178;
	public static final int X86_INS_VPSHAD = 1179;
	public static final int X86_INS_VPSHAQ = 1180;
	public static final int X86_INS_VPSHAW = 1181;
	public static final int X86_INS_VPSHLB = 1182;
	public static final int X86_INS_VPSHLD = 1183;
	public static final int X86_INS_VPSHLQ = 1184;
	public static final int X86_INS_VPSHLW = 1185;
	public static final int X86_INS_VPSHUFB = 1186;
	public static final int X86_INS_VPSHUFD = 1187;
	public static final int X86_INS_VPSHUFHW = 1188;
	public static final int X86_INS_VPSHUFLW = 1189;
	public static final int X86_INS_VPSIGNB = 1190;
	public static final int X86_INS_VPSIGND = 1191;
	public static final int X86_INS_VPSIGNW = 1192;
	public static final int X86_INS_VPSLLDQ = 1193;
	public static final int X86_INS_VPSLLD = 1194;
	public static final int X86_INS_VPSLLQ = 1195;
	public static final int X86_INS_VPSLLVD = 1196;
	public static final int X86_INS_VPSLLVQ = 1197;
	public static final int X86_INS_VPSLLW = 1198;
	public static final int X86_INS_VPSRAD = 1199;
	public static final int X86_INS_VPSRAQ = 1200;
	public static final int X86_INS_VPSRAVD = 1201;
	public static final int X86_INS_VPSRAVQ = 1202;
	public static final int X86_INS_VPSRAW = 1203;
	public static final int X86_INS_VPSRLDQ = 1204;
	public static final int X86_INS_VPSRLD = 1205;
	public static final int X86_INS_VPSRLQ = 1206;
	public static final int X86_INS_VPSRLVD = 1207;
	public static final int X86_INS_VPSRLVQ = 1208;
	public static final int X86_INS_VPSRLW = 1209;
	public static final int X86_INS_VPSUBB = 1210;
	public static final int X86_INS_VPSUBD = 1211;
	public static final int X86_INS_VPSUBQ = 1212;
	public static final int X86_INS_VPSUBSB = 1213;
	public static final int X86_INS_VPSUBSW = 1214;
	public static final int X86_INS_VPSUBUSB = 1215;
	public static final int X86_INS_VPSUBUSW = 1216;
	public static final int X86_INS_VPSUBW = 1217;
	public static final int X86_INS_VPTESTMD = 1218;
	public static final int X86_INS_VPTESTMQ = 1219;
	public static final int X86_INS_VPTESTNMD = 1220;
	public static final int X86_INS_VPTESTNMQ = 1221;
	public static final int X86_INS_VPTEST = 1222;
	public static final int X86_INS_VPUNPCKHBW = 1223;
	public static final int X86_INS_VPUNPCKHDQ = 1224;
	public static final int X86_INS_VPUNPCKHQDQ = 1225;
	public static final int X86_INS_VPUNPCKHWD = 1226;
	public static final int X86_INS_VPUNPCKLBW = 1227;
	public static final int X86_INS_VPUNPCKLDQ = 1228;
	public static final int X86_INS_VPUNPCKLQDQ = 1229;
	public static final int X86_INS_VPUNPCKLWD = 1230;
	public static final int X86_INS_VPXORD = 1231;
	public static final int X86_INS_VPXORQ = 1232;
	public static final int X86_INS_VPXOR = 1233;
	public static final int X86_INS_VRCP14PD = 1234;
	public static final int X86_INS_VRCP14PS = 1235;
	public static final int X86_INS_VRCP14SD = 1236;
	public static final int X86_INS_VRCP14SS = 1237;
	public static final int X86_INS_VRCP28PD = 1238;
	public static final int X86_INS_VRCP28PS = 1239;
	public static final int X86_INS_VRCP28SD = 1240;
	public static final int X86_INS_VRCP28SS = 1241;
	public static final int X86_INS_VRCPPS = 1242;
	public static final int X86_INS_VRCPSS = 1243;
	public static final int X86_INS_VRNDSCALEPD = 1244;
	public static final int X86_INS_VRNDSCALEPS = 1245;
	public static final int X86_INS_VRNDSCALESD = 1246;
	public static final int X86_INS_VRNDSCALESS = 1247;
	public static final int X86_INS_VROUNDPD = 1248;
	public static final int X86_INS_VROUNDPS = 1249;
	public static final int X86_INS_VROUNDSD = 1250;
	public static final int X86_INS_VROUNDSS = 1251;
	public static final int X86_INS_VRSQRT14PD = 1252;
	public static final int X86_INS_VRSQRT14PS = 1253;
	public static final int X86_INS_VRSQRT14SD = 1254;
	public static final int X86_INS_VRSQRT14SS = 1255;
	public static final int X86_INS_VRSQRT28PD = 1256;
	public static final int X86_INS_VRSQRT28PS = 1257;
	public static final int X86_INS_VRSQRT28SD = 1258;
	public static final int X86_INS_VRSQRT28SS = 1259;
	public static final int X86_INS_VRSQRTPS = 1260;
	public static final int X86_INS_VRSQRTSS = 1261;
	public static final int X86_INS_VSCATTERDPD = 1262;
	public static final int X86_INS_VSCATTERDPS = 1263;
	public static final int X86_INS_VSCATTERPF0DPD = 1264;
	public static final int X86_INS_VSCATTERPF0DPS = 1265;
	public static final int X86_INS_VSCATTERPF0QPD = 1266;
	public static final int X86_INS_VSCATTERPF0QPS = 1267;
	public static final int X86_INS_VSCATTERPF1DPD = 1268;
	public static final int X86_INS_VSCATTERPF1DPS = 1269;
	public static final int X86_INS_VSCATTERPF1QPD = 1270;
	public static final int X86_INS_VSCATTERPF1QPS = 1271;
	public static final int X86_INS_VSCATTERQPD = 1272;
	public static final int X86_INS_VSCATTERQPS = 1273;
	public static final int X86_INS_VSHUFPD = 1274;
	public static final int X86_INS_VSHUFPS = 1275;
	public static final int X86_INS_VSQRTPD = 1276;
	public static final int X86_INS_VSQRTPS = 1277;
	public static final int X86_INS_VSQRTSD = 1278;
	public static final int X86_INS_VSQRTSS = 1279;
	public static final int X86_INS_VSTMXCSR = 1280;
	public static final int X86_INS_VSUBPD = 1281;
	public static final int X86_INS_VSUBPS = 1282;
	public static final int X86_INS_VSUBSD = 1283;
	public static final int X86_INS_VSUBSS = 1284;
	public static final int X86_INS_VTESTPD = 1285;
	public static final int X86_INS_VTESTPS = 1286;
	public static final int X86_INS_VUNPCKHPD = 1287;
	public static final int X86_INS_VUNPCKHPS = 1288;
	public static final int X86_INS_VUNPCKLPD = 1289;
	public static final int X86_INS_VUNPCKLPS = 1290;
	public static final int X86_INS_VZEROALL = 1291;
	public static final int X86_INS_VZEROUPPER = 1292;
	public static final int X86_INS_WAIT = 1293;
	public static final int X86_INS_WBINVD = 1294;
	public static final int X86_INS_WRFSBASE = 1295;
	public static final int X86_INS_WRGSBASE = 1296;
	public static final int X86_INS_WRMSR = 1297;
	public static final int X86_INS_XABORT = 1298;
	public static final int X86_INS_XACQUIRE = 1299;
	public static final int X86_INS_XBEGIN = 1300;
	public static final int X86_INS_XCHG = 1301;
	public static final int X86_INS_XCRYPTCBC = 1302;
	public static final int X86_INS_XCRYPTCFB = 1303;
	public static final int X86_INS_XCRYPTCTR = 1304;
	public static final int X86_INS_XCRYPTECB = 1305;
	public static final int X86_INS_XCRYPTOFB = 1306;
	public static final int X86_INS_XEND = 1307;
	public static final int X86_INS_XGETBV = 1308;
	public static final int X86_INS_XLATB = 1309;
	public static final int X86_INS_XRELEASE = 1310;
	public static final int X86_INS_XRSTOR = 1311;
	public static final int X86_INS_XRSTOR64 = 1312;
	public static final int X86_INS_XRSTORS = 1313;
	public static final int X86_INS_XRSTORS64 = 1314;
	public static final int X86_INS_XSAVE = 1315;
	public static final int X86_INS_XSAVE64 = 1316;
	public static final int X86_INS_XSAVEC = 1317;
	public static final int X86_INS_XSAVEC64 = 1318;
	public static final int X86_INS_XSAVEOPT = 1319;
	public static final int X86_INS_XSAVEOPT64 = 1320;
	public static final int X86_INS_XSAVES = 1321;
	public static final int X86_INS_XSAVES64 = 1322;
	public static final int X86_INS_XSETBV = 1323;
	public static final int X86_INS_XSHA1 = 1324;
	public static final int X86_INS_XSHA256 = 1325;
	public static final int X86_INS_XSTORE = 1326;
	public static final int X86_INS_XTEST = 1327;
	public static final int X86_INS_FDISI8087_NOP = 1328;
	public static final int X86_INS_FENI8087_NOP = 1329;
	public static final int X86_INS_CMPSS = 1330;
	public static final int X86_INS_CMPEQSS = 1331;
	public static final int X86_INS_CMPLTSS = 1332;
	public static final int X86_INS_CMPLESS = 1333;
	public static final int X86_INS_CMPUNORDSS = 1334;
	public static final int X86_INS_CMPNEQSS = 1335;
	public static final int X86_INS_CMPNLTSS = 1336;
	public static final int X86_INS_CMPNLESS = 1337;
	public static final int X86_INS_CMPORDSS = 1338;
	public static final int X86_INS_CMPSD = 1339;
	public static final int X86_INS_CMPEQSD = 1340;
	public static final int X86_INS_CMPLTSD = 1341;
	public static final int X86_INS_CMPLESD = 1342;
	public static final int X86_INS_CMPUNORDSD = 1343;
	public static final int X86_INS_CMPNEQSD = 1344;
	public static final int X86_INS_CMPNLTSD = 1345;
	public static final int X86_INS_CMPNLESD = 1346;
	public static final int X86_INS_CMPORDSD = 1347;
	public static final int X86_INS_CMPPS = 1348;
	public static final int X86_INS_CMPEQPS = 1349;
	public static final int X86_INS_CMPLTPS = 1350;
	public static final int X86_INS_CMPLEPS = 1351;
	public static final int X86_INS_CMPUNORDPS = 1352;
	public static final int X86_INS_CMPNEQPS = 1353;
	public static final int X86_INS_CMPNLTPS = 1354;
	public static final int X86_INS_CMPNLEPS = 1355;
	public static final int X86_INS_CMPORDPS = 1356;
	public static final int X86_INS_CMPPD = 1357;
	public static final int X86_INS_CMPEQPD = 1358;
	public static final int X86_INS_CMPLTPD = 1359;
	public static final int X86_INS_CMPLEPD = 1360;
	public static final int X86_INS_CMPUNORDPD = 1361;
	public static final int X86_INS_CMPNEQPD = 1362;
	public static final int X86_INS_CMPNLTPD = 1363;
	public static final int X86_INS_CMPNLEPD = 1364;
	public static final int X86_INS_CMPORDPD = 1365;
	public static final int X86_INS_VCMPSS = 1366;
	public static final int X86_INS_VCMPEQSS = 1367;
	public static final int X86_INS_VCMPLTSS = 1368;
	public static final int X86_INS_VCMPLESS = 1369;
	public static final int X86_INS_VCMPUNORDSS = 1370;
	public static final int X86_INS_VCMPNEQSS = 1371;
	public static final int X86_INS_VCMPNLTSS = 1372;
	public static final int X86_INS_VCMPNLESS = 1373;
	public static final int X86_INS_VCMPORDSS = 1374;
	public static final int X86_INS_VCMPEQ_UQSS = 1375;
	public static final int X86_INS_VCMPNGESS = 1376;
	public static final int X86_INS_VCMPNGTSS = 1377;
	public static final int X86_INS_VCMPFALSESS = 1378;
	public static final int X86_INS_VCMPNEQ_OQSS = 1379;
	public static final int X86_INS_VCMPGESS = 1380;
	public static final int X86_INS_VCMPGTSS = 1381;
	public static final int X86_INS_VCMPTRUESS = 1382;
	public static final int X86_INS_VCMPEQ_OSSS = 1383;
	public static final int X86_INS_VCMPLT_OQSS = 1384;
	public static final int X86_INS_VCMPLE_OQSS = 1385;
	public static final int X86_INS_VCMPUNORD_SSS = 1386;
	public static final int X86_INS_VCMPNEQ_USSS = 1387;
	public static final int X86_INS_VCMPNLT_UQSS = 1388;
	public static final int X86_INS_VCMPNLE_UQSS = 1389;
	public static final int X86_INS_VCMPORD_SSS = 1390;
	public static final int X86_INS_VCMPEQ_USSS = 1391;
	public static final int X86_INS_VCMPNGE_UQSS = 1392;
	public static final int X86_INS_VCMPNGT_UQSS = 1393;
	public static final int X86_INS_VCMPFALSE_OSSS = 1394;
	public static final int X86_INS_VCMPNEQ_OSSS = 1395;
	public static final int X86_INS_VCMPGE_OQSS = 1396;
	public static final int X86_INS_VCMPGT_OQSS = 1397;
	public static final int X86_INS_VCMPTRUE_USSS = 1398;
	public static final int X86_INS_VCMPSD = 1399;
	public static final int X86_INS_VCMPEQSD = 1400;
	public static final int X86_INS_VCMPLTSD = 1401;
	public static final int X86_INS_VCMPLESD = 1402;
	public static final int X86_INS_VCMPUNORDSD = 1403;
	public static final int X86_INS_VCMPNEQSD = 1404;
	public static final int X86_INS_VCMPNLTSD = 1405;
	public static final int X86_INS_VCMPNLESD = 1406;
	public static final int X86_INS_VCMPORDSD = 1407;
	public static final int X86_INS_VCMPEQ_UQSD = 1408;
	public static final int X86_INS_VCMPNGESD = 1409;
	public static final int X86_INS_VCMPNGTSD = 1410;
	public static final int X86_INS_VCMPFALSESD = 1411;
	public static final int X86_INS_VCMPNEQ_OQSD = 1412;
	public static final int X86_INS_VCMPGESD = 1413;
	public static final int X86_INS_VCMPGTSD = 1414;
	public static final int X86_INS_VCMPTRUESD = 1415;
	public static final int X86_INS_VCMPEQ_OSSD = 1416;
	public static final int X86_INS_VCMPLT_OQSD = 1417;
	public static final int X86_INS_VCMPLE_OQSD = 1418;
	public static final int X86_INS_VCMPUNORD_SSD = 1419;
	public static final int X86_INS_VCMPNEQ_USSD = 1420;
	public static final int X86_INS_VCMPNLT_UQSD = 1421;
	public static final int X86_INS_VCMPNLE_UQSD = 1422;
	public static final int X86_INS_VCMPORD_SSD = 1423;
	public static final int X86_INS_VCMPEQ_USSD = 1424;
	public static final int X86_INS_VCMPNGE_UQSD = 1425;
	public static final int X86_INS_VCMPNGT_UQSD = 1426;
	public static final int X86_INS_VCMPFALSE_OSSD = 1427;
	public static final int X86_INS_VCMPNEQ_OSSD = 1428;
	public static final int X86_INS_VCMPGE_OQSD = 1429;
	public static final int X86_INS_VCMPGT_OQSD = 1430;
	public static final int X86_INS_VCMPTRUE_USSD = 1431;
	public static final int X86_INS_VCMPPS = 1432;
	public static final int X86_INS_VCMPEQPS = 1433;
	public static final int X86_INS_VCMPLTPS = 1434;
	public static final int X86_INS_VCMPLEPS = 1435;
	public static final int X86_INS_VCMPUNORDPS = 1436;
	public static final int X86_INS_VCMPNEQPS = 1437;
	public static final int X86_INS_VCMPNLTPS = 1438;
	public static final int X86_INS_VCMPNLEPS = 1439;
	public static final int X86_INS_VCMPORDPS = 1440;
	public static final int X86_INS_VCMPEQ_UQPS = 1441;
	public static final int X86_INS_VCMPNGEPS = 1442;
	public static final int X86_INS_VCMPNGTPS = 1443;
	public static final int X86_INS_VCMPFALSEPS = 1444;
	public static final int X86_INS_VCMPNEQ_OQPS = 1445;
	public static final int X86_INS_VCMPGEPS = 1446;
	public static final int X86_INS_VCMPGTPS = 1447;
	public static final int X86_INS_VCMPTRUEPS = 1448;
	public static final int X86_INS_VCMPEQ_OSPS = 1449;
	public static final int X86_INS_VCMPLT_OQPS = 1450;
	public static final int X86_INS_VCMPLE_OQPS = 1451;
	public static final int X86_INS_VCMPUNORD_SPS = 1452;
	public static final int X86_INS_VCMPNEQ_USPS = 1453;
	public static final int X86_INS_VCMPNLT_UQPS = 1454;
	public static final int X86_INS_VCMPNLE_UQPS = 1455;
	public static final int X86_INS_VCMPORD_SPS = 1456;
	public static final int X86_INS_VCMPEQ_USPS = 1457;
	public static final int X86_INS_VCMPNGE_UQPS = 1458;
	public static final int X86_INS_VCMPNGT_UQPS = 1459;
	public static final int X86_INS_VCMPFALSE_OSPS = 1460;
	public static final int X86_INS_VCMPNEQ_OSPS = 1461;
	public static final int X86_INS_VCMPGE_OQPS = 1462;
	public static final int X86_INS_VCMPGT_OQPS = 1463;
	public static final int X86_INS_VCMPTRUE_USPS = 1464;
	public static final int X86_INS_VCMPPD = 1465;
	public static final int X86_INS_VCMPEQPD = 1466;
	public static final int X86_INS_VCMPLTPD = 1467;
	public static final int X86_INS_VCMPLEPD = 1468;
	public static final int X86_INS_VCMPUNORDPD = 1469;
	public static final int X86_INS_VCMPNEQPD = 1470;
	public static final int X86_INS_VCMPNLTPD = 1471;
	public static final int X86_INS_VCMPNLEPD = 1472;
	public static final int X86_INS_VCMPORDPD = 1473;
	public static final int X86_INS_VCMPEQ_UQPD = 1474;
	public static final int X86_INS_VCMPNGEPD = 1475;
	public static final int X86_INS_VCMPNGTPD = 1476;
	public static final int X86_INS_VCMPFALSEPD = 1477;
	public static final int X86_INS_VCMPNEQ_OQPD = 1478;
	public static final int X86_INS_VCMPGEPD = 1479;
	public static final int X86_INS_VCMPGTPD = 1480;
	public static final int X86_INS_VCMPTRUEPD = 1481;
	public static final int X86_INS_VCMPEQ_OSPD = 1482;
	public static final int X86_INS_VCMPLT_OQPD = 1483;
	public static final int X86_INS_VCMPLE_OQPD = 1484;
	public static final int X86_INS_VCMPUNORD_SPD = 1485;
	public static final int X86_INS_VCMPNEQ_USPD = 1486;
	public static final int X86_INS_VCMPNLT_UQPD = 1487;
	public static final int X86_INS_VCMPNLE_UQPD = 1488;
	public static final int X86_INS_VCMPORD_SPD = 1489;
	public static final int X86_INS_VCMPEQ_USPD = 1490;
	public static final int X86_INS_VCMPNGE_UQPD = 1491;
	public static final int X86_INS_VCMPNGT_UQPD = 1492;
	public static final int X86_INS_VCMPFALSE_OSPD = 1493;
	public static final int X86_INS_VCMPNEQ_OSPD = 1494;
	public static final int X86_INS_VCMPGE_OQPD = 1495;
	public static final int X86_INS_VCMPGT_OQPD = 1496;
	public static final int X86_INS_VCMPTRUE_USPD = 1497;
	public static final int X86_INS_UD0 = 1498;
	public static final int X86_INS_ENDBR32 = 1499;
	public static final int X86_INS_ENDBR64 = 1500;
	public static final int X86_INS_ENDING = 1501;

	// Group of X86 instructions

	public static final int X86_GRP_INVALID = 0;

	// Generic groups
	public static final int X86_GRP_JUMP = 1;
	public static final int X86_GRP_CALL = 2;
	public static final int X86_GRP_RET = 3;
	public static final int X86_GRP_INT = 4;
	public static final int X86_GRP_IRET = 5;
	public static final int X86_GRP_PRIVILEGE = 6;
	public static final int X86_GRP_BRANCH_RELATIVE = 7;

	// Architecture-specific groups
	public static final int X86_GRP_VM = 128;
	public static final int X86_GRP_3DNOW = 129;
	public static final int X86_GRP_AES = 130;
	public static final int X86_GRP_ADX = 131;
	public static final int X86_GRP_AVX = 132;
	public static final int X86_GRP_AVX2 = 133;
	public static final int X86_GRP_AVX512 = 134;
	public static final int X86_GRP_BMI = 135;
	public static final int X86_GRP_BMI2 = 136;
	public static final int X86_GRP_CMOV = 137;
	public static final int X86_GRP_F16C = 138;
	public static final int X86_GRP_FMA = 139;
	public static final int X86_GRP_FMA4 = 140;
	public static final int X86_GRP_FSGSBASE = 141;
	public static final int X86_GRP_HLE = 142;
	public static final int X86_GRP_MMX = 143;
	public static final int X86_GRP_MODE32 = 144;
	public static final int X86_GRP_MODE64 = 145;
	public static final int X86_GRP_RTM = 146;
	public static final int X86_GRP_SHA = 147;
	public static final int X86_GRP_SSE1 = 148;
	public static final int X86_GRP_SSE2 = 149;
	public static final int X86_GRP_SSE3 = 150;
	public static final int X86_GRP_SSE41 = 151;
	public static final int X86_GRP_SSE42 = 152;
	public static final int X86_GRP_SSE4A = 153;
	public static final int X86_GRP_SSSE3 = 154;
	public static final int X86_GRP_PCLMUL = 155;
	public static final int X86_GRP_XOP = 156;
	public static final int X86_GRP_CDI = 157;
	public static final int X86_GRP_ERI = 158;
	public static final int X86_GRP_TBM = 159;
	public static final int X86_GRP_16BITMODE = 160;
	public static final int X86_GRP_NOT64BITMODE = 161;
	public static final int X86_GRP_SGX = 162;
	public static final int X86_GRP_DQI = 163;
	public static final int X86_GRP_BWI = 164;
	public static final int X86_GRP_PFI = 165;
	public static final int X86_GRP_VLX = 166;
	public static final int X86_GRP_SMAP = 167;
	public static final int X86_GRP_NOVLX = 168;
	public static final int X86_GRP_FPU = 169;
	public static final int X86_GRP_ENDING = 170;
}