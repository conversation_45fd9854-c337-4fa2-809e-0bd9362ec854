/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Subtarget Enumeration Source Fragment                                       *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine, http://www.capstone-engine.org */
/* By <PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2013-2015 */


#ifdef GET_SUBTARGETINFO_ENUM
#undef GET_SUBTARGETINFO_ENUM

#define Mips_FeatureCnMips (1ULL << 0)
#define Mips_FeatureDSP (1ULL << 1)
#define Mips_FeatureDSPR2 (1ULL << 2)
#define Mips_FeatureFP64Bit (1ULL << 3)
#define Mips_FeatureFPXX (1ULL << 4)
#define Mips_FeatureGP64Bit (1ULL << 5)
#define Mips_FeatureMSA (1ULL << 6)
#define Mips_FeatureMicroMips (1ULL << 7)
#define Mips_FeatureMips1 (1ULL << 8)
#define Mips_FeatureMips2 (1ULL << 9)
#define Mips_FeatureMips3 (1ULL << 10)
#define Mips_FeatureMips3_32 (1ULL << 11)
#define Mips_FeatureMips3_32r2 (1ULL << 12)
#define Mips_FeatureMips4 (1ULL << 13)
#define Mips_FeatureMips4_32 (1ULL << 14)
#define Mips_FeatureMips4_32r2 (1ULL << 15)
#define Mips_FeatureMips5 (1ULL << 16)
#define Mips_FeatureMips5_32r2 (1ULL << 17)
#define Mips_FeatureMips16 (1ULL << 18)
#define Mips_FeatureMips32 (1ULL << 19)
#define Mips_FeatureMips32r2 (1ULL << 20)
#define Mips_FeatureMips32r3 (1ULL << 21)
#define Mips_FeatureMips32r5 (1ULL << 22)
#define Mips_FeatureMips32r6 (1ULL << 23)
#define Mips_FeatureMips64 (1ULL << 24)
#define Mips_FeatureMips64r2 (1ULL << 25)
#define Mips_FeatureMips64r3 (1ULL << 26)
#define Mips_FeatureMips64r5 (1ULL << 27)
#define Mips_FeatureMips64r6 (1ULL << 28)
#define Mips_FeatureNaN2008 (1ULL << 29)
#define Mips_FeatureNoABICalls (1ULL << 30)
#define Mips_FeatureNoOddSPReg (1ULL << 31)
#define Mips_FeatureSingleFloat (1ULL << 32)
#define Mips_FeatureVFPU (1ULL << 33)

#endif // GET_SUBTARGETINFO_ENUM

