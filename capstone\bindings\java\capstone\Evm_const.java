// For Capstone Engine. AUTO-GENERATED FILE, DO NOT EDIT
package capstone;

public class Evm_const {

	// EVM instruction

	public static final int EVM_INS_STOP = 0;
	public static final int EVM_INS_ADD = 1;
	public static final int EVM_INS_MUL = 2;
	public static final int EVM_INS_SUB = 3;
	public static final int EVM_INS_DIV = 4;
	public static final int EVM_INS_SDIV = 5;
	public static final int EVM_INS_MOD = 6;
	public static final int EVM_INS_SMOD = 7;
	public static final int EVM_INS_ADDMOD = 8;
	public static final int EVM_INS_MULMOD = 9;
	public static final int EVM_INS_EXP = 10;
	public static final int EVM_INS_SIGNEXTEND = 11;
	public static final int EVM_INS_LT = 16;
	public static final int EVM_INS_GT = 17;
	public static final int EVM_INS_SLT = 18;
	public static final int EVM_INS_SGT = 19;
	public static final int EVM_INS_EQ = 20;
	public static final int EVM_INS_ISZERO = 21;
	public static final int EVM_INS_AND = 22;
	public static final int EVM_INS_OR = 23;
	public static final int EVM_INS_XOR = 24;
	public static final int EVM_INS_NOT = 25;
	public static final int EVM_INS_BYTE = 26;
	public static final int EVM_INS_SHA3 = 32;
	public static final int EVM_INS_ADDRESS = 48;
	public static final int EVM_INS_BALANCE = 49;
	public static final int EVM_INS_ORIGIN = 50;
	public static final int EVM_INS_CALLER = 51;
	public static final int EVM_INS_CALLVALUE = 52;
	public static final int EVM_INS_CALLDATALOAD = 53;
	public static final int EVM_INS_CALLDATASIZE = 54;
	public static final int EVM_INS_CALLDATACOPY = 55;
	public static final int EVM_INS_CODESIZE = 56;
	public static final int EVM_INS_CODECOPY = 57;
	public static final int EVM_INS_GASPRICE = 58;
	public static final int EVM_INS_EXTCODESIZE = 59;
	public static final int EVM_INS_EXTCODECOPY = 60;
	public static final int EVM_INS_RETURNDATASIZE = 61;
	public static final int EVM_INS_RETURNDATACOPY = 62;
	public static final int EVM_INS_BLOCKHASH = 64;
	public static final int EVM_INS_COINBASE = 65;
	public static final int EVM_INS_TIMESTAMP = 66;
	public static final int EVM_INS_NUMBER = 67;
	public static final int EVM_INS_DIFFICULTY = 68;
	public static final int EVM_INS_GASLIMIT = 69;
	public static final int EVM_INS_POP = 80;
	public static final int EVM_INS_MLOAD = 81;
	public static final int EVM_INS_MSTORE = 82;
	public static final int EVM_INS_MSTORE8 = 83;
	public static final int EVM_INS_SLOAD = 84;
	public static final int EVM_INS_SSTORE = 85;
	public static final int EVM_INS_JUMP = 86;
	public static final int EVM_INS_JUMPI = 87;
	public static final int EVM_INS_PC = 88;
	public static final int EVM_INS_MSIZE = 89;
	public static final int EVM_INS_GAS = 90;
	public static final int EVM_INS_JUMPDEST = 91;
	public static final int EVM_INS_PUSH1 = 96;
	public static final int EVM_INS_PUSH2 = 97;
	public static final int EVM_INS_PUSH3 = 98;
	public static final int EVM_INS_PUSH4 = 99;
	public static final int EVM_INS_PUSH5 = 100;
	public static final int EVM_INS_PUSH6 = 101;
	public static final int EVM_INS_PUSH7 = 102;
	public static final int EVM_INS_PUSH8 = 103;
	public static final int EVM_INS_PUSH9 = 104;
	public static final int EVM_INS_PUSH10 = 105;
	public static final int EVM_INS_PUSH11 = 106;
	public static final int EVM_INS_PUSH12 = 107;
	public static final int EVM_INS_PUSH13 = 108;
	public static final int EVM_INS_PUSH14 = 109;
	public static final int EVM_INS_PUSH15 = 110;
	public static final int EVM_INS_PUSH16 = 111;
	public static final int EVM_INS_PUSH17 = 112;
	public static final int EVM_INS_PUSH18 = 113;
	public static final int EVM_INS_PUSH19 = 114;
	public static final int EVM_INS_PUSH20 = 115;
	public static final int EVM_INS_PUSH21 = 116;
	public static final int EVM_INS_PUSH22 = 117;
	public static final int EVM_INS_PUSH23 = 118;
	public static final int EVM_INS_PUSH24 = 119;
	public static final int EVM_INS_PUSH25 = 120;
	public static final int EVM_INS_PUSH26 = 121;
	public static final int EVM_INS_PUSH27 = 122;
	public static final int EVM_INS_PUSH28 = 123;
	public static final int EVM_INS_PUSH29 = 124;
	public static final int EVM_INS_PUSH30 = 125;
	public static final int EVM_INS_PUSH31 = 126;
	public static final int EVM_INS_PUSH32 = 127;
	public static final int EVM_INS_DUP1 = 128;
	public static final int EVM_INS_DUP2 = 129;
	public static final int EVM_INS_DUP3 = 130;
	public static final int EVM_INS_DUP4 = 131;
	public static final int EVM_INS_DUP5 = 132;
	public static final int EVM_INS_DUP6 = 133;
	public static final int EVM_INS_DUP7 = 134;
	public static final int EVM_INS_DUP8 = 135;
	public static final int EVM_INS_DUP9 = 136;
	public static final int EVM_INS_DUP10 = 137;
	public static final int EVM_INS_DUP11 = 138;
	public static final int EVM_INS_DUP12 = 139;
	public static final int EVM_INS_DUP13 = 140;
	public static final int EVM_INS_DUP14 = 141;
	public static final int EVM_INS_DUP15 = 142;
	public static final int EVM_INS_DUP16 = 143;
	public static final int EVM_INS_SWAP1 = 144;
	public static final int EVM_INS_SWAP2 = 145;
	public static final int EVM_INS_SWAP3 = 146;
	public static final int EVM_INS_SWAP4 = 147;
	public static final int EVM_INS_SWAP5 = 148;
	public static final int EVM_INS_SWAP6 = 149;
	public static final int EVM_INS_SWAP7 = 150;
	public static final int EVM_INS_SWAP8 = 151;
	public static final int EVM_INS_SWAP9 = 152;
	public static final int EVM_INS_SWAP10 = 153;
	public static final int EVM_INS_SWAP11 = 154;
	public static final int EVM_INS_SWAP12 = 155;
	public static final int EVM_INS_SWAP13 = 156;
	public static final int EVM_INS_SWAP14 = 157;
	public static final int EVM_INS_SWAP15 = 158;
	public static final int EVM_INS_SWAP16 = 159;
	public static final int EVM_INS_LOG0 = 160;
	public static final int EVM_INS_LOG1 = 161;
	public static final int EVM_INS_LOG2 = 162;
	public static final int EVM_INS_LOG3 = 163;
	public static final int EVM_INS_LOG4 = 164;
	public static final int EVM_INS_CREATE = 240;
	public static final int EVM_INS_CALL = 241;
	public static final int EVM_INS_CALLCODE = 242;
	public static final int EVM_INS_RETURN = 243;
	public static final int EVM_INS_DELEGATECALL = 244;
	public static final int EVM_INS_CALLBLACKBOX = 245;
	public static final int EVM_INS_STATICCALL = 250;
	public static final int EVM_INS_REVERT = 253;
	public static final int EVM_INS_SUICIDE = 255;
	public static final int EVM_INS_INVALID = 512;
	public static final int EVM_INS_ENDING = 513;

	// Group of EVM instructions

	public static final int EVM_GRP_INVALID = 0;
	public static final int EVM_GRP_JUMP = 1;
	public static final int EVM_GRP_MATH = 8;
	public static final int EVM_GRP_STACK_WRITE = 9;
	public static final int EVM_GRP_STACK_READ = 10;
	public static final int EVM_GRP_MEM_WRITE = 11;
	public static final int EVM_GRP_MEM_READ = 12;
	public static final int EVM_GRP_STORE_WRITE = 13;
	public static final int EVM_GRP_STORE_READ = 14;
	public static final int EVM_GRP_HALT = 15;
	public static final int EVM_GRP_ENDING = 16;
}