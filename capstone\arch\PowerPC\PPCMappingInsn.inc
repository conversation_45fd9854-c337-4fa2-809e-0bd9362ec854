// This is auto-gen data for Capstone engine (www.capstone-engine.org)
// By <PERSON><PERSON><PERSON> <<EMAIL>>

{
	PPC_ADD4, PPC_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADD4TLS, PPC_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADD4o, PPC_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADD8, PPC_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADD8TLS, PPC_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADD8TLS_, PPC_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADD8o, PPC_INS_ADD,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADDC, PPC_INS_ADDC,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CARRY, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADDC8, PPC_INS_ADDC,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CARRY, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADDC8o, PPC_INS_ADDC,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CARRY, PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADDCo, PPC_INS_ADDC,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CARRY, PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADDE, PPC_INS_ADDE,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CARRY, 0 }, { PPC_REG_CARRY, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADDE8, PPC_INS_ADDE,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CARRY, 0 }, { PPC_REG_CARRY, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADDE8o, PPC_INS_ADDE,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CARRY, 0 }, { PPC_REG_CARRY, PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADDEo, PPC_INS_ADDE,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CARRY, 0 }, { PPC_REG_CARRY, PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADDI, PPC_INS_ADDI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADDI8, PPC_INS_ADDI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADDIC, PPC_INS_ADDIC,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CARRY, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADDIC8, PPC_INS_ADDIC,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CARRY, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADDICo, PPC_INS_ADDIC,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CARRY, PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADDIS, PPC_INS_ADDIS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADDIS8, PPC_INS_ADDIS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADDME, PPC_INS_ADDME,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CARRY, 0 }, { PPC_REG_CARRY, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADDME8, PPC_INS_ADDME,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CARRY, 0 }, { PPC_REG_CARRY, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADDME8o, PPC_INS_ADDME,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CARRY, 0 }, { PPC_REG_CARRY, PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADDMEo, PPC_INS_ADDME,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CARRY, 0 }, { PPC_REG_CARRY, PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADDZE, PPC_INS_ADDZE,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CARRY, 0 }, { PPC_REG_CARRY, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADDZE8, PPC_INS_ADDZE,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CARRY, 0 }, { PPC_REG_CARRY, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADDZE8o, PPC_INS_ADDZE,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CARRY, 0 }, { PPC_REG_CARRY, PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ADDZEo, PPC_INS_ADDZE,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CARRY, 0 }, { PPC_REG_CARRY, PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_AND, PPC_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_AND8, PPC_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_AND8o, PPC_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ANDC, PPC_INS_ANDC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ANDC8, PPC_INS_ANDC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ANDC8o, PPC_INS_ANDC,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ANDCo, PPC_INS_ANDC,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ANDISo, PPC_INS_ANDIS,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ANDISo8, PPC_INS_ANDIS,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ANDIo, PPC_INS_ANDI,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ANDIo8, PPC_INS_ANDI,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ANDo, PPC_INS_AND,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ATTN, PPC_INS_ATTN,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_B, PPC_INS_B,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BA, PPC_INS_BA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BC, PPC_INS_BC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BCC, PPC_INS_B,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BCCA, PPC_INS_BA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BCCCTR, PPC_INS_BCTR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, 0 }, { 0 }, { 0 }, 1, 1
#endif
},
{
	PPC_BCCCTR8, PPC_INS_BCTR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR8, 0 }, { 0 }, { PPC_GRP_MODE64, 0 }, 1, 1
#endif
},
{
	PPC_BCCCTRL, PPC_INS_BCTRL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_RM, 0 }, { PPC_REG_LR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BCCCTRL8, PPC_INS_BCTRL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR8, PPC_REG_RM, 0 }, { PPC_REG_LR8, 0 }, { PPC_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	PPC_BCCL, PPC_INS_BL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_LR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BCCLA, PPC_INS_BLA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_LR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BCCLR, PPC_INS_BLR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_LR, PPC_REG_RM, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BCCLRL, PPC_INS_BLRL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_LR, PPC_REG_RM, 0 }, { PPC_REG_LR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BCCTR, PPC_INS_BCCTR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, 0 }, { 0 }, { 0 }, 1, 1
#endif
},
{
	PPC_BCCTR8, PPC_INS_BCCTR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR8, 0 }, { 0 }, { PPC_GRP_MODE64, 0 }, 1, 1
#endif
},
{
	PPC_BCCTR8n, PPC_INS_BCCTR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR8, 0 }, { 0 }, { PPC_GRP_MODE64, 0 }, 1, 1
#endif
},
{
	PPC_BCCTRL, PPC_INS_BCCTRL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_RM, 0 }, { PPC_REG_LR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BCCTRL8, PPC_INS_BCCTRL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR8, PPC_REG_RM, 0 }, { PPC_REG_LR8, 0 }, { PPC_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	PPC_BCCTRL8n, PPC_INS_BCCTRL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR8, PPC_REG_RM, 0 }, { PPC_REG_LR8, 0 }, { PPC_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	PPC_BCCTRLn, PPC_INS_BCCTRL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_RM, 0 }, { PPC_REG_LR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BCCTRn, PPC_INS_BCCTR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, 0 }, { 0 }, { 0 }, 1, 1
#endif
},
{
	PPC_BCL, PPC_INS_BCL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_LR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BCLR, PPC_INS_BCLR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_LR, PPC_REG_RM, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BCLRL, PPC_INS_BCLRL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_LR, PPC_REG_RM, 0 }, { PPC_REG_LR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BCLRLn, PPC_INS_BCLRL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_LR, PPC_REG_RM, 0 }, { PPC_REG_LR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BCLRn, PPC_INS_BCLR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BCLalways, PPC_INS_BCL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_LR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BCLn, PPC_INS_BCL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_LR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BCTR, PPC_INS_BCTR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, 0 }, { 0 }, { 0 }, 1, 1
#endif
},
{
	PPC_BCTR8, PPC_INS_BCTR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR8, 0 }, { 0 }, { PPC_GRP_MODE64, 0 }, 1, 1
#endif
},
{
	PPC_BCTRL, PPC_INS_BCTRL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_RM, 0 }, { PPC_REG_LR, 0 }, { PPC_GRP_MODE32, 0 }, 0, 0
#endif
},
{
	PPC_BCTRL8, PPC_INS_BCTRL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR8, PPC_REG_RM, 0 }, { PPC_REG_LR8, 0 }, { PPC_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	PPC_BCTRL8_LDinto_toc, PPC_INS_BCT,	// FIXME
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR8, PPC_REG_RM, 0 }, { PPC_REG_LR8, PPC_REG_X2, 0 }, { PPC_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	PPC_BCn, PPC_INS_BC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BDNZ, PPC_INS_BDNZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BDNZ8, PPC_INS_BDNZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR8, 0 }, { PPC_REG_CTR8, 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BDNZA, PPC_INS_BDNZA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BDNZAm, PPC_INS_BDNZA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BDNZAp, PPC_INS_BDNZA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BDNZL, PPC_INS_BDNZL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BDNZLA, PPC_INS_BDNZLA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BDNZLAm, PPC_INS_BDNZLA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BDNZLAp, PPC_INS_BDNZLA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BDNZLR, PPC_INS_BDNZLR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_LR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BDNZLR8, PPC_INS_BDNZLR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR8, PPC_REG_LR8, PPC_REG_RM, 0 }, { PPC_REG_CTR8, 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BDNZLRL, PPC_INS_BDNZLRL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_LR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BDNZLRLm, PPC_INS_BDNZLRL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_LR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BDNZLRLp, PPC_INS_BDNZLRL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_LR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BDNZLRm, PPC_INS_BDNZLR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_LR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BDNZLRp, PPC_INS_BDNZLR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_LR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BDNZLm, PPC_INS_BDNZL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BDNZLp, PPC_INS_BDNZL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BDNZm, PPC_INS_BDNZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BDNZp, PPC_INS_BDNZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BDZ, PPC_INS_BDZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BDZ8, PPC_INS_BDZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR8, 0 }, { PPC_REG_CTR8, 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BDZA, PPC_INS_BDZA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BDZAm, PPC_INS_BDZA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BDZAp, PPC_INS_BDZA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BDZL, PPC_INS_BDZL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BDZLA, PPC_INS_BDZLA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BDZLAm, PPC_INS_BDZLA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BDZLAp, PPC_INS_BDZLA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BDZLR, PPC_INS_BDZLR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_LR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BDZLR8, PPC_INS_BDZLR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR8, PPC_REG_LR8, PPC_REG_RM, 0 }, { PPC_REG_CTR8, 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BDZLRL, PPC_INS_BDZLRL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_LR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BDZLRLm, PPC_INS_BDZLRL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_LR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BDZLRLp, PPC_INS_BDZLRL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_LR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BDZLRm, PPC_INS_BDZLR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_LR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BDZLRp, PPC_INS_BDZLR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_LR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BDZLm, PPC_INS_BDZL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BDZLp, PPC_INS_BDZL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BDZm, PPC_INS_BDZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BDZp, PPC_INS_BDZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_BL, PPC_INS_BL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_LR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BL8, PPC_INS_BL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_LR8, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BL8_NOP, PPC_INS_BL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_LR8, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BL8_NOP_TLS, PPC_INS_BL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_LR8, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BL8_TLS, PPC_INS_BL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_LR8, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BL8_TLS_, PPC_INS_BL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_LR8, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BLA, PPC_INS_BLA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_LR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BLA8, PPC_INS_BLA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_LR8, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BLA8_NOP, PPC_INS_BLA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_LR8, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BLR, PPC_INS_BLR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_LR, PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_MODE32, 0 }, 0, 0
#endif
},
{
	PPC_BLR8, PPC_INS_BLR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_LR8, PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_MODE64, 0 }, 0, 0
#endif
},
{
	PPC_BLRL, PPC_INS_BLRL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_LR, PPC_REG_RM, 0 }, { PPC_REG_LR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BL_TLS, PPC_INS_BL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_LR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_BRINC, PPC_INS_BRINC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_CMPB, PPC_INS_CMPB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_CMPB8, PPC_INS_CMPB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_CMPD, PPC_INS_CMPD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_CMPDI, PPC_INS_CMPDI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_CMPLD, PPC_INS_CMPLD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_CMPLDI, PPC_INS_CMPLDI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_CMPLW, PPC_INS_CMPLW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_CMPLWI, PPC_INS_CMPLWI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_CMPW, PPC_INS_CMPW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_CMPWI, PPC_INS_CMPWI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_CNTLZD, PPC_INS_CNTLZD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_CNTLZDo, PPC_INS_CNTLZD,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_CNTLZW, PPC_INS_CNTLZW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_CNTLZW8, PPC_INS_CNTLZW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_CNTLZW8o, PPC_INS_CNTLZW,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_CNTLZWo, PPC_INS_CNTLZW,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_CR6SET, PPC_INS_CREQV,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR1EQ, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_CR6UNSET, PPC_INS_CRXOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR1EQ, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_CRAND, PPC_INS_CRAND,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_CRANDC, PPC_INS_CRANDC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_CREQV, PPC_INS_CREQV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_CRNAND, PPC_INS_CRNAND,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_CRNOR, PPC_INS_CRNOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_CROR, PPC_INS_CROR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_CRORC, PPC_INS_CRORC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_CRSET, PPC_INS_CREQV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_CRUNSET, PPC_INS_CRXOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_CRXOR, PPC_INS_CRXOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_DCBA, PPC_INS_DCBA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_DCBF, PPC_INS_DCBF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_DCBI, PPC_INS_DCBI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_DCBST, PPC_INS_DCBST,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_DCBT, PPC_INS_DCBT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_DCBTST, PPC_INS_DCBTST,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_DCBZ, PPC_INS_DCBZ,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_DCBZL, PPC_INS_DCBZL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_DCCCI, PPC_INS_DCCCI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_PPC4XX, 0 }, 0, 0
#endif
},
{
	PPC_DIVD, PPC_INS_DIVD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_DIVDU, PPC_INS_DIVDU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_DIVDUo, PPC_INS_DIVDU,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_DIVDo, PPC_INS_DIVD,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_DIVW, PPC_INS_DIVW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_DIVWU, PPC_INS_DIVWU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_DIVWUo, PPC_INS_DIVWU,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_DIVWo, PPC_INS_DIVW,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_DSS, PPC_INS_DSS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_DSSALL, PPC_INS_DSSALL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_DST, PPC_INS_DST,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_DST64, PPC_INS_DST,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_DSTST, PPC_INS_DSTST,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_DSTST64, PPC_INS_DSTST,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_DSTSTT, PPC_INS_DSTSTT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_DSTSTT64, PPC_INS_DSTSTT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_DSTT, PPC_INS_DSTT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_DSTT64, PPC_INS_DSTT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_EQV, PPC_INS_EQV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_EQV8, PPC_INS_EQV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_EQV8o, PPC_INS_EQV,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_EQVo, PPC_INS_EQV,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_EVABS, PPC_INS_EVABS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVADDIW, PPC_INS_EVADDIW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVADDSMIAAW, PPC_INS_EVADDSMIAAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVADDSSIAAW, PPC_INS_EVADDSSIAAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVADDUMIAAW, PPC_INS_EVADDUMIAAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVADDUSIAAW, PPC_INS_EVADDUSIAAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVADDW, PPC_INS_EVADDW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVAND, PPC_INS_EVAND,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVANDC, PPC_INS_EVANDC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVCMPEQ, PPC_INS_EVCMPEQ,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVCMPGTS, PPC_INS_EVCMPGTS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVCMPGTU, PPC_INS_EVCMPGTU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVCMPLTS, PPC_INS_EVCMPLTS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVCMPLTU, PPC_INS_EVCMPLTU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVCNTLSW, PPC_INS_EVCNTLSW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVCNTLZW, PPC_INS_EVCNTLZW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVDIVWS, PPC_INS_EVDIVWS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVDIVWU, PPC_INS_EVDIVWU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVEQV, PPC_INS_EVEQV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVEXTSB, PPC_INS_EVEXTSB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVEXTSH, PPC_INS_EVEXTSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVLDD, PPC_INS_EVLDD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVLDDX, PPC_INS_EVLDDX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVLDH, PPC_INS_EVLDH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVLDHX, PPC_INS_EVLDHX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVLDW, PPC_INS_EVLDW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVLDWX, PPC_INS_EVLDWX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVLHHESPLAT, PPC_INS_EVLHHESPLAT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVLHHESPLATX, PPC_INS_EVLHHESPLATX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVLHHOSSPLAT, PPC_INS_EVLHHOSSPLAT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVLHHOSSPLATX, PPC_INS_EVLHHOSSPLATX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVLHHOUSPLAT, PPC_INS_EVLHHOUSPLAT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVLHHOUSPLATX, PPC_INS_EVLHHOUSPLATX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVLWHE, PPC_INS_EVLWHE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVLWHEX, PPC_INS_EVLWHEX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVLWHOS, PPC_INS_EVLWHOS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVLWHOSX, PPC_INS_EVLWHOSX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVLWHOU, PPC_INS_EVLWHOU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVLWHOUX, PPC_INS_EVLWHOUX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVLWHSPLAT, PPC_INS_EVLWHSPLAT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVLWHSPLATX, PPC_INS_EVLWHSPLATX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVLWWSPLAT, PPC_INS_EVLWWSPLAT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVLWWSPLATX, PPC_INS_EVLWWSPLATX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMERGEHI, PPC_INS_EVMERGEHI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMERGEHILO, PPC_INS_EVMERGEHILO,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMERGELO, PPC_INS_EVMERGELO,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMERGELOHI, PPC_INS_EVMERGELOHI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHEGSMFAA, PPC_INS_EVMHEGSMFAA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHEGSMFAN, PPC_INS_EVMHEGSMFAN,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHEGSMIAA, PPC_INS_EVMHEGSMIAA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHEGSMIAN, PPC_INS_EVMHEGSMIAN,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHEGUMIAA, PPC_INS_EVMHEGUMIAA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHEGUMIAN, PPC_INS_EVMHEGUMIAN,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHESMF, PPC_INS_EVMHESMF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHESMFA, PPC_INS_EVMHESMFA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHESMFAAW, PPC_INS_EVMHESMFAAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHESMFANW, PPC_INS_EVMHESMFANW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHESMI, PPC_INS_EVMHESMI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHESMIA, PPC_INS_EVMHESMIA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHESMIAAW, PPC_INS_EVMHESMIAAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHESMIANW, PPC_INS_EVMHESMIANW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHESSF, PPC_INS_EVMHESSF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHESSFA, PPC_INS_EVMHESSFA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHESSFAAW, PPC_INS_EVMHESSFAAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHESSFANW, PPC_INS_EVMHESSFANW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHESSIAAW, PPC_INS_EVMHESSIAAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHESSIANW, PPC_INS_EVMHESSIANW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHEUMI, PPC_INS_EVMHEUMI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHEUMIA, PPC_INS_EVMHEUMIA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHEUMIAAW, PPC_INS_EVMHEUMIAAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHEUMIANW, PPC_INS_EVMHEUMIANW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHEUSIAAW, PPC_INS_EVMHEUSIAAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHEUSIANW, PPC_INS_EVMHEUSIANW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHOGSMFAA, PPC_INS_EVMHOGSMFAA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHOGSMFAN, PPC_INS_EVMHOGSMFAN,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHOGSMIAA, PPC_INS_EVMHOGSMIAA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHOGSMIAN, PPC_INS_EVMHOGSMIAN,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHOGUMIAA, PPC_INS_EVMHOGUMIAA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHOGUMIAN, PPC_INS_EVMHOGUMIAN,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHOSMF, PPC_INS_EVMHOSMF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHOSMFA, PPC_INS_EVMHOSMFA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHOSMFAAW, PPC_INS_EVMHOSMFAAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHOSMFANW, PPC_INS_EVMHOSMFANW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHOSMI, PPC_INS_EVMHOSMI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHOSMIA, PPC_INS_EVMHOSMIA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHOSMIAAW, PPC_INS_EVMHOSMIAAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHOSMIANW, PPC_INS_EVMHOSMIANW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHOSSF, PPC_INS_EVMHOSSF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHOSSFA, PPC_INS_EVMHOSSFA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHOSSFAAW, PPC_INS_EVMHOSSFAAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHOSSFANW, PPC_INS_EVMHOSSFANW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHOSSIAAW, PPC_INS_EVMHOSSIAAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHOSSIANW, PPC_INS_EVMHOSSIANW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHOUMI, PPC_INS_EVMHOUMI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHOUMIA, PPC_INS_EVMHOUMIA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHOUMIAAW, PPC_INS_EVMHOUMIAAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHOUMIANW, PPC_INS_EVMHOUMIANW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHOUSIAAW, PPC_INS_EVMHOUSIAAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMHOUSIANW, PPC_INS_EVMHOUSIANW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMRA, PPC_INS_EVMRA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWHSMF, PPC_INS_EVMWHSMF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWHSMFA, PPC_INS_EVMWHSMFA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWHSMI, PPC_INS_EVMWHSMI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWHSMIA, PPC_INS_EVMWHSMIA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWHSSF, PPC_INS_EVMWHSSF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWHSSFA, PPC_INS_EVMWHSSFA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWHUMI, PPC_INS_EVMWHUMI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWHUMIA, PPC_INS_EVMWHUMIA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWLSMIAAW, PPC_INS_EVMWLSMIAAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWLSMIANW, PPC_INS_EVMWLSMIANW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWLSSIAAW, PPC_INS_EVMWLSSIAAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWLSSIANW, PPC_INS_EVMWLSSIANW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWLUMI, PPC_INS_EVMWLUMI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWLUMIA, PPC_INS_EVMWLUMIA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWLUMIAAW, PPC_INS_EVMWLUMIAAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWLUMIANW, PPC_INS_EVMWLUMIANW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWLUSIAAW, PPC_INS_EVMWLUSIAAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWLUSIANW, PPC_INS_EVMWLUSIANW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWSMF, PPC_INS_EVMWSMF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWSMFA, PPC_INS_EVMWSMFA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWSMFAA, PPC_INS_EVMWSMFAA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWSMFAN, PPC_INS_EVMWSMFAN,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWSMI, PPC_INS_EVMWSMI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWSMIA, PPC_INS_EVMWSMIA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWSMIAA, PPC_INS_EVMWSMIAA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWSMIAN, PPC_INS_EVMWSMIAN,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWSSF, PPC_INS_EVMWSSF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWSSFA, PPC_INS_EVMWSSFA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWSSFAA, PPC_INS_EVMWSSFAA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWSSFAN, PPC_INS_EVMWSSFAN,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWUMI, PPC_INS_EVMWUMI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWUMIA, PPC_INS_EVMWUMIA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWUMIAA, PPC_INS_EVMWUMIAA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVMWUMIAN, PPC_INS_EVMWUMIAN,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVNAND, PPC_INS_EVNAND,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVNEG, PPC_INS_EVNEG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVNOR, PPC_INS_EVNOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVOR, PPC_INS_EVOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVORC, PPC_INS_EVORC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVRLW, PPC_INS_EVRLW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVRLWI, PPC_INS_EVRLWI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVRNDW, PPC_INS_EVRNDW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSLW, PPC_INS_EVSLW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSLWI, PPC_INS_EVSLWI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSPLATFI, PPC_INS_EVSPLATFI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSPLATI, PPC_INS_EVSPLATI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSRWIS, PPC_INS_EVSRWIS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSRWIU, PPC_INS_EVSRWIU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSRWS, PPC_INS_EVSRWS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSRWU, PPC_INS_EVSRWU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSTDD, PPC_INS_EVSTDD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSTDDX, PPC_INS_EVSTDDX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSTDH, PPC_INS_EVSTDH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSTDHX, PPC_INS_EVSTDHX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSTDW, PPC_INS_EVSTDW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSTDWX, PPC_INS_EVSTDWX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSTWHE, PPC_INS_EVSTWHE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSTWHEX, PPC_INS_EVSTWHEX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSTWHO, PPC_INS_EVSTWHO,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSTWHOX, PPC_INS_EVSTWHOX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSTWWE, PPC_INS_EVSTWWE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSTWWEX, PPC_INS_EVSTWWEX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSTWWO, PPC_INS_EVSTWWO,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSTWWOX, PPC_INS_EVSTWWOX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSUBFSMIAAW, PPC_INS_EVSUBFSMIAAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSUBFSSIAAW, PPC_INS_EVSUBFSSIAAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSUBFUMIAAW, PPC_INS_EVSUBFUMIAAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSUBFUSIAAW, PPC_INS_EVSUBFUSIAAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSUBFW, PPC_INS_EVSUBFW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVSUBIFW, PPC_INS_EVSUBIFW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EVXOR, PPC_INS_EVXOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_SPE, 0 }, 0, 0
#endif
},
{
	PPC_EXTSB, PPC_INS_EXTSB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_EXTSB8, PPC_INS_EXTSB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_EXTSB8_32_64, PPC_INS_EXTSB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_EXTSB8o, PPC_INS_EXTSB,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_EXTSBo, PPC_INS_EXTSB,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_EXTSH, PPC_INS_EXTSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_EXTSH8, PPC_INS_EXTSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_EXTSH8_32_64, PPC_INS_EXTSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_EXTSH8o, PPC_INS_EXTSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_EXTSHo, PPC_INS_EXTSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_EXTSW, PPC_INS_EXTSW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_EXTSW_32_64, PPC_INS_EXTSW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_EXTSW_32_64o, PPC_INS_EXTSW,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_EXTSWo, PPC_INS_EXTSW,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_EnforceIEIO, PPC_INS_EIEIO,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FABSD, PPC_INS_FABS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FABSDo, PPC_INS_FABS,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FABSS, PPC_INS_FABS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FABSSo, PPC_INS_FABS,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FADD, PPC_INS_FADD,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FADDS, PPC_INS_FADDS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FADDSo, PPC_INS_FADDS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FADDo, PPC_INS_FADD,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FCFID, PPC_INS_FCFID,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FCFIDS, PPC_INS_FCFIDS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FCFIDSo, PPC_INS_FCFIDS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FCFIDU, PPC_INS_FCFIDU,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FCFIDUS, PPC_INS_FCFIDUS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FCFIDUSo, PPC_INS_FCFIDUS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FCFIDUo, PPC_INS_FCFIDU,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FCFIDo, PPC_INS_FCFID,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FCMPUD, PPC_INS_FCMPU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FCMPUS, PPC_INS_FCMPU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FCPSGND, PPC_INS_FCPSGN,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FCPSGNDo, PPC_INS_FCPSGN,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FCPSGNS, PPC_INS_FCPSGN,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FCPSGNSo, PPC_INS_FCPSGN,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FCTID, PPC_INS_FCTID,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FCTIDUZ, PPC_INS_FCTIDUZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FCTIDUZo, PPC_INS_FCTIDUZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FCTIDZ, PPC_INS_FCTIDZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FCTIDZo, PPC_INS_FCTIDZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FCTIDo, PPC_INS_FCTID,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FCTIW, PPC_INS_FCTIW,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FCTIWUZ, PPC_INS_FCTIWUZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FCTIWUZo, PPC_INS_FCTIWUZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FCTIWZ, PPC_INS_FCTIWZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FCTIWZo, PPC_INS_FCTIWZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FCTIWo, PPC_INS_FCTIW,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FDIV, PPC_INS_FDIV,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FDIVS, PPC_INS_FDIVS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FDIVSo, PPC_INS_FDIVS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FDIVo, PPC_INS_FDIV,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FMADD, PPC_INS_FMADD,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FMADDS, PPC_INS_FMADDS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FMADDSo, PPC_INS_FMADDS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FMADDo, PPC_INS_FMADD,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FMR, PPC_INS_FMR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FMRo, PPC_INS_FMR,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FMSUB, PPC_INS_FMSUB,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FMSUBS, PPC_INS_FMSUBS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FMSUBSo, PPC_INS_FMSUBS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FMSUBo, PPC_INS_FMSUB,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FMUL, PPC_INS_FMUL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FMULS, PPC_INS_FMULS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FMULSo, PPC_INS_FMULS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FMULo, PPC_INS_FMUL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FNABSD, PPC_INS_FNABS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FNABSDo, PPC_INS_FNABS,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FNABSS, PPC_INS_FNABS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FNABSSo, PPC_INS_FNABS,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FNEGD, PPC_INS_FNEG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FNEGDo, PPC_INS_FNEG,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FNEGS, PPC_INS_FNEG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FNEGSo, PPC_INS_FNEG,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FNMADD, PPC_INS_FNMADD,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FNMADDS, PPC_INS_FNMADDS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FNMADDSo, PPC_INS_FNMADDS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FNMADDo, PPC_INS_FNMADD,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FNMSUB, PPC_INS_FNMSUB,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FNMSUBS, PPC_INS_FNMSUBS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FNMSUBSo, PPC_INS_FNMSUBS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FNMSUBo, PPC_INS_FNMSUB,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FRE, PPC_INS_FRE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FRES, PPC_INS_FRES,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FRESo, PPC_INS_FRES,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FREo, PPC_INS_FRE,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FRIMD, PPC_INS_FRIM,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FRIMDo, PPC_INS_FRIM,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FRIMS, PPC_INS_FRIM,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FRIMSo, PPC_INS_FRIM,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FRIND, PPC_INS_FRIN,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FRINDo, PPC_INS_FRIN,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FRINS, PPC_INS_FRIN,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FRINSo, PPC_INS_FRIN,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FRIPD, PPC_INS_FRIP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FRIPDo, PPC_INS_FRIP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FRIPS, PPC_INS_FRIP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FRIPSo, PPC_INS_FRIP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FRIZD, PPC_INS_FRIZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FRIZDo, PPC_INS_FRIZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FRIZS, PPC_INS_FRIZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FRIZSo, PPC_INS_FRIZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FRSP, PPC_INS_FRSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FRSPo, PPC_INS_FRSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FRSQRTE, PPC_INS_FRSQRTE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FRSQRTES, PPC_INS_FRSQRTES,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FRSQRTESo, PPC_INS_FRSQRTES,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FRSQRTEo, PPC_INS_FRSQRTE,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FSELD, PPC_INS_FSEL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FSELDo, PPC_INS_FSEL,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FSELS, PPC_INS_FSEL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FSELSo, PPC_INS_FSEL,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FSQRT, PPC_INS_FSQRT,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FSQRTS, PPC_INS_FSQRTS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FSQRTSo, PPC_INS_FSQRTS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FSQRTo, PPC_INS_FSQRT,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FSUB, PPC_INS_FSUB,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FSUBS, PPC_INS_FSUBS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FSUBSo, PPC_INS_FSUBS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_FSUBo, PPC_INS_FSUB,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ICBI, PPC_INS_ICBI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ICBT, PPC_INS_ICBT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ICBT, 0 }, 0, 0
#endif
},
{
	PPC_ICCCI, PPC_INS_ICCCI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_PPC4XX, 0 }, 0, 0
#endif
},
{
	PPC_ISEL, PPC_INS_ISEL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ISEL8, PPC_INS_ISEL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ISYNC, PPC_INS_ISYNC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LA, PPC_INS_LA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LBZ, PPC_INS_LBZ,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LBZ8, PPC_INS_LBZ,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LBZCIX, PPC_INS_LBZCIX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LBZU, PPC_INS_LBZU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LBZU8, PPC_INS_LBZU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LBZUX, PPC_INS_LBZUX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LBZUX8, PPC_INS_LBZUX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LBZX, PPC_INS_LBZX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LBZX8, PPC_INS_LBZX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LD, PPC_INS_LD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LDARX, PPC_INS_LDARX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LDBRX, PPC_INS_LDBRX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LDCIX, PPC_INS_LDCIX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LDU, PPC_INS_LDU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LDUX, PPC_INS_LDUX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LDX, PPC_INS_LDX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LFD, PPC_INS_LFD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LFDU, PPC_INS_LFDU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LFDUX, PPC_INS_LFDUX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LFDX, PPC_INS_LFDX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LFIWAX, PPC_INS_LFIWAX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LFIWZX, PPC_INS_LFIWZX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LFS, PPC_INS_LFS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LFSU, PPC_INS_LFSU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LFSUX, PPC_INS_LFSUX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LFSX, PPC_INS_LFSX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LHA, PPC_INS_LHA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LHA8, PPC_INS_LHA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LHAU, PPC_INS_LHAU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LHAU8, PPC_INS_LHAU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LHAUX, PPC_INS_LHAUX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LHAUX8, PPC_INS_LHAUX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LHAX, PPC_INS_LHAX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LHAX8, PPC_INS_LHAX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LHBRX, PPC_INS_LHBRX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LHBRX8, PPC_INS_LHBRX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LHZ, PPC_INS_LHZ,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LHZ8, PPC_INS_LHZ,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LHZCIX, PPC_INS_LHZCIX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LHZU, PPC_INS_LHZU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LHZU8, PPC_INS_LHZU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LHZUX, PPC_INS_LHZUX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LHZUX8, PPC_INS_LHZUX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LHZX, PPC_INS_LHZX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LHZX8, PPC_INS_LHZX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LI, PPC_INS_LI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LI8, PPC_INS_LI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LIS, PPC_INS_LIS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LIS8, PPC_INS_LIS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LMW, PPC_INS_LMW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LSWI, PPC_INS_LSWI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LVEBX, PPC_INS_LVEBX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_LVEHX, PPC_INS_LVEHX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_LVEWX, PPC_INS_LVEWX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_LVSL, PPC_INS_LVSL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_LVSR, PPC_INS_LVSR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_LVX, PPC_INS_LVX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_LVXL, PPC_INS_LVXL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_LWA, PPC_INS_LWA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LWARX, PPC_INS_LWARX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LWAUX, PPC_INS_LWAUX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LWAX, PPC_INS_LWAX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LWAX_32, PPC_INS_LWAX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LWA_32, PPC_INS_LWA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LWBRX, PPC_INS_LWBRX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LWBRX8, PPC_INS_LWBRX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LWZ, PPC_INS_LWZ,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LWZ8, PPC_INS_LWZ,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LWZCIX, PPC_INS_LWZCIX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LWZU, PPC_INS_LWZU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LWZU8, PPC_INS_LWZU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LWZUX, PPC_INS_LWZUX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LWZUX8, PPC_INS_LWZUX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LWZX, PPC_INS_LWZX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LWZX8, PPC_INS_LWZX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_LXSDX, PPC_INS_LXSDX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_LXVD2X, PPC_INS_LXVD2X,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_LXVDSX, PPC_INS_LXVDSX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_LXVW4X, PPC_INS_LXVW4X,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_MBAR, PPC_INS_MBAR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_BOOKE, 0 }, 0, 0
#endif
},
{
	PPC_MCRF, PPC_INS_MCRF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MCRFS, PPC_INS_MCRFS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MFCR, PPC_INS_MFCR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MFCR8, PPC_INS_MFCR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MFCTR, PPC_INS_MFCTR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MFCTR8, PPC_INS_MFCTR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR8, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MFDCR, PPC_INS_MFDCR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_PPC4XX, 0 }, 0, 0
#endif
},
{
	PPC_MFFS, PPC_INS_MFFS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MFFSo, PPC_INS_MFFS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR1, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MFLR, PPC_INS_MFLR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_LR, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MFLR8, PPC_INS_MFLR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_LR8, 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MFMSR, PPC_INS_MFMSR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MFOCRF, PPC_INS_MFOCRF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MFOCRF8, PPC_INS_MFOCRF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MFSPR, PPC_INS_MFSPR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MFSR, PPC_INS_MFSR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MFSRIN, PPC_INS_MFSRIN,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MFTB, PPC_INS_MFTB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MFTB8, PPC_INS_MFSPR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MFVRSAVE, PPC_INS_MFSPR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MFVRSAVEv, PPC_INS_MFSPR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MFVSCR, PPC_INS_MFVSCR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_MSYNC, PPC_INS_MSYNC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MTCRF, PPC_INS_MTCRF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MTCRF8, PPC_INS_MTCRF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MTCTR, PPC_INS_MTCTR,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MTCTR8, PPC_INS_MTCTR,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CTR8, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MTCTR8loop, PPC_INS_MTCTR,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CTR8, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MTCTRloop, PPC_INS_MTCTR,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MTDCR, PPC_INS_MTDCR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_PPC4XX, 0 }, 0, 0
#endif
},
{
	PPC_MTFSB0, PPC_INS_MTFSB0,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_RM, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MTFSB1, PPC_INS_MTFSB1,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_RM, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MTFSF, PPC_INS_MTFSF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MTFSFI, PPC_INS_MTFSFI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MTFSFIo, PPC_INS_MTFSFI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MTFSFb, PPC_INS_MTFSF,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_RM, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MTFSFo, PPC_INS_MTFSF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MTLR, PPC_INS_MTLR,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_LR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MTLR8, PPC_INS_MTLR,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_LR8, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MTMSR, PPC_INS_MTMSR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MTMSRD, PPC_INS_MTMSRD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MTOCRF, PPC_INS_MTOCRF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MTOCRF8, PPC_INS_MTOCRF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MTSPR, PPC_INS_MTSPR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MTSR, PPC_INS_MTSR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MTSRIN, PPC_INS_MTSRIN,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MTVRSAVE, PPC_INS_MTSPR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MTVRSAVEv, PPC_INS_MTSPR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MTVSCR, PPC_INS_MTVSCR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_MULHD, PPC_INS_MULHD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MULHDU, PPC_INS_MULHDU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MULHDUo, PPC_INS_MULHDU,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MULHDo, PPC_INS_MULHD,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MULHW, PPC_INS_MULHW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MULHWU, PPC_INS_MULHWU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MULHWUo, PPC_INS_MULHWU,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MULHWo, PPC_INS_MULHW,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MULLD, PPC_INS_MULLD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MULLDo, PPC_INS_MULLD,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MULLI, PPC_INS_MULLI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MULLI8, PPC_INS_MULLI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MULLW, PPC_INS_MULLW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_MULLWo, PPC_INS_MULLW,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_NAND, PPC_INS_NAND,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_NAND8, PPC_INS_NAND,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_NAND8o, PPC_INS_NAND,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_NANDo, PPC_INS_NAND,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_NEG, PPC_INS_NEG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_NEG8, PPC_INS_NEG,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_NEG8o, PPC_INS_NEG,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_NEGo, PPC_INS_NEG,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_NOP, PPC_INS_NOP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_NOP_GT_PWR6, PPC_INS_ORI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_NOP_GT_PWR7, PPC_INS_ORI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_NOR, PPC_INS_NOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_NOR8, PPC_INS_NOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_NOR8o, PPC_INS_NOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_NORo, PPC_INS_NOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_OR, PPC_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_OR8, PPC_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_OR8o, PPC_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ORC, PPC_INS_ORC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ORC8, PPC_INS_ORC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ORC8o, PPC_INS_ORC,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ORCo, PPC_INS_ORC,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ORI, PPC_INS_ORI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ORI8, PPC_INS_ORI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ORIS, PPC_INS_ORIS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ORIS8, PPC_INS_ORIS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_ORo, PPC_INS_OR,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_POPCNTD, PPC_INS_POPCNTD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_POPCNTW, PPC_INS_POPCNTW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_QVALIGNI, PPC_INS_QVALIGNI,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVALIGNIb, PPC_INS_QVALIGNI,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVALIGNIs, PPC_INS_QVALIGNI,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVESPLATI, PPC_INS_QVESPLATI,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVESPLATIb, PPC_INS_QVESPLATI,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVESPLATIs, PPC_INS_QVESPLATI,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFABS, PPC_INS_QVFABS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFABSs, PPC_INS_QVFABS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFADD, PPC_INS_QVFADD,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFADDS, PPC_INS_QVFADDS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFADDSs, PPC_INS_QVFADDS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFCFID, PPC_INS_QVFCFID,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFCFIDS, PPC_INS_QVFCFIDS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFCFIDU, PPC_INS_QVFCFIDU,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFCFIDUS, PPC_INS_QVFCFIDUS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFCFIDb, PPC_INS_QVFCFID,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFCMPEQ, PPC_INS_QVFCMPEQ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFCMPEQb, PPC_INS_QVFCMPEQ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFCMPEQbs, PPC_INS_QVFCMPEQ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFCMPGT, PPC_INS_QVFCMPGT,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFCMPGTb, PPC_INS_QVFCMPGT,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFCMPGTbs, PPC_INS_QVFCMPGT,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFCMPLT, PPC_INS_QVFCMPLT,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFCMPLTb, PPC_INS_QVFCMPLT,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFCMPLTbs, PPC_INS_QVFCMPLT,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFCPSGN, PPC_INS_QVFCPSGN,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFCPSGNs, PPC_INS_QVFCPSGN,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFCTID, PPC_INS_QVFCTID,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFCTIDU, PPC_INS_QVFCTIDU,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFCTIDUZ, PPC_INS_QVFCTIDUZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFCTIDZ, PPC_INS_QVFCTIDZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFCTIDb, PPC_INS_QVFCTID,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFCTIW, PPC_INS_QVFCTIW,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFCTIWU, PPC_INS_QVFCTIWU,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFCTIWUZ, PPC_INS_QVFCTIWUZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFCTIWZ, PPC_INS_QVFCTIWZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFLOGICAL, PPC_INS_QVFLOGICAL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFLOGICALb, PPC_INS_QVFLOGICAL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFLOGICALs, PPC_INS_QVFLOGICAL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFMADD, PPC_INS_QVFMADD,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFMADDS, PPC_INS_QVFMADDS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFMADDSs, PPC_INS_QVFMADDS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFMR, PPC_INS_QVFMR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFMRb, PPC_INS_QVFMR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFMRs, PPC_INS_QVFMR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFMSUB, PPC_INS_QVFMSUB,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFMSUBS, PPC_INS_QVFMSUBS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFMSUBSs, PPC_INS_QVFMSUBS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFMUL, PPC_INS_QVFMUL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFMULS, PPC_INS_QVFMULS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFMULSs, PPC_INS_QVFMULS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFNABS, PPC_INS_QVFNABS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFNABSs, PPC_INS_QVFNABS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFNEG, PPC_INS_QVFNEG,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFNEGs, PPC_INS_QVFNEG,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFNMADD, PPC_INS_QVFNMADD,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFNMADDS, PPC_INS_QVFNMADDS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFNMADDSs, PPC_INS_QVFNMADDS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFNMSUB, PPC_INS_QVFNMSUB,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFNMSUBS, PPC_INS_QVFNMSUBS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFNMSUBSs, PPC_INS_QVFNMSUBS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFPERM, PPC_INS_QVFPERM,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFPERMs, PPC_INS_QVFPERM,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFRE, PPC_INS_QVFRE,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFRES, PPC_INS_QVFRES,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFRESs, PPC_INS_QVFRES,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFRIM, PPC_INS_QVFRIM,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFRIMs, PPC_INS_QVFRIM,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFRIN, PPC_INS_QVFRIN,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFRINs, PPC_INS_QVFRIN,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFRIP, PPC_INS_QVFRIP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFRIPs, PPC_INS_QVFRIP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFRIZ, PPC_INS_QVFRIZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFRIZs, PPC_INS_QVFRIZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFRSP, PPC_INS_QVFRSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFRSPs, PPC_INS_QVFRSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFRSQRTE, PPC_INS_QVFRSQRTE,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFRSQRTES, PPC_INS_QVFRSQRTES,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFRSQRTESs, PPC_INS_QVFRSQRTES,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFSEL, PPC_INS_QVFSEL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFSELb, PPC_INS_QVFSEL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFSELbb, PPC_INS_QVFSEL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFSELbs, PPC_INS_QVFSEL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFSUB, PPC_INS_QVFSUB,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFSUBS, PPC_INS_QVFSUBS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFSUBSs, PPC_INS_QVFSUBS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFTSTNAN, PPC_INS_QVFTSTNAN,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFTSTNANb, PPC_INS_QVFTSTNAN,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFTSTNANbs, PPC_INS_QVFTSTNAN,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFXMADD, PPC_INS_QVFXMADD,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFXMADDS, PPC_INS_QVFXMADDS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFXMUL, PPC_INS_QVFXMUL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFXMULS, PPC_INS_QVFXMULS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFXXCPNMADD, PPC_INS_QVFXXCPNMADD,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFXXCPNMADDS, PPC_INS_QVFXXCPNMADDS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFXXMADD, PPC_INS_QVFXXMADD,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFXXMADDS, PPC_INS_QVFXXMADDS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFXXNPMADD, PPC_INS_QVFXXNPMADD,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVFXXNPMADDS, PPC_INS_QVFXXNPMADDS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVGPCI, PPC_INS_QVGPCI,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLFCDUX, PPC_INS_QVLFCDUX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLFCDUXA, PPC_INS_QVLFCDUXA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLFCDX, PPC_INS_QVLFCDX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLFCDXA, PPC_INS_QVLFCDXA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLFCSUX, PPC_INS_QVLFCSUX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLFCSUXA, PPC_INS_QVLFCSUXA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLFCSX, PPC_INS_QVLFCSX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLFCSXA, PPC_INS_QVLFCSXA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLFCSXs, PPC_INS_QVLFCSX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLFDUX, PPC_INS_QVLFDUX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLFDUXA, PPC_INS_QVLFDUXA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLFDX, PPC_INS_QVLFDX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLFDXA, PPC_INS_QVLFDXA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLFDXb, PPC_INS_QVLFDX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLFIWAX, PPC_INS_QVLFIWAX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLFIWAXA, PPC_INS_QVLFIWAXA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLFIWZX, PPC_INS_QVLFIWZX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLFIWZXA, PPC_INS_QVLFIWZXA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLFSUX, PPC_INS_QVLFSUX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLFSUXA, PPC_INS_QVLFSUXA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLFSX, PPC_INS_QVLFSX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLFSXA, PPC_INS_QVLFSXA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLFSXb, PPC_INS_QVLFSX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLFSXs, PPC_INS_QVLFSX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLPCLDX, PPC_INS_QVLPCLDX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLPCLSX, PPC_INS_QVLPCLSX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLPCLSXint, PPC_INS_QVLPCLSX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLPCRDX, PPC_INS_QVLPCRDX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVLPCRSX, PPC_INS_QVLPCRSX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFCDUX, PPC_INS_QVSTFCDUX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFCDUXA, PPC_INS_QVSTFCDUXA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFCDUXI, PPC_INS_QVSTFCDUXI,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFCDUXIA, PPC_INS_QVSTFCDUXIA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFCDX, PPC_INS_QVSTFCDX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFCDXA, PPC_INS_QVSTFCDXA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFCDXI, PPC_INS_QVSTFCDXI,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFCDXIA, PPC_INS_QVSTFCDXIA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFCSUX, PPC_INS_QVSTFCSUX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFCSUXA, PPC_INS_QVSTFCSUXA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFCSUXI, PPC_INS_QVSTFCSUXI,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFCSUXIA, PPC_INS_QVSTFCSUXIA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFCSX, PPC_INS_QVSTFCSX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFCSXA, PPC_INS_QVSTFCSXA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFCSXI, PPC_INS_QVSTFCSXI,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFCSXIA, PPC_INS_QVSTFCSXIA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFCSXs, PPC_INS_QVSTFCSX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFDUX, PPC_INS_QVSTFDUX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFDUXA, PPC_INS_QVSTFDUXA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFDUXI, PPC_INS_QVSTFDUXI,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFDUXIA, PPC_INS_QVSTFDUXIA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFDX, PPC_INS_QVSTFDX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFDXA, PPC_INS_QVSTFDXA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFDXI, PPC_INS_QVSTFDXI,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFDXIA, PPC_INS_QVSTFDXIA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFDXb, PPC_INS_QVSTFDX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFIWX, PPC_INS_QVSTFIWX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFIWXA, PPC_INS_QVSTFIWXA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFSUX, PPC_INS_QVSTFSUX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFSUXA, PPC_INS_QVSTFSUXA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFSUXI, PPC_INS_QVSTFSUXI,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFSUXIA, PPC_INS_QVSTFSUXIA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFSUXs, PPC_INS_QVSTFSUX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFSX, PPC_INS_QVSTFSX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFSXA, PPC_INS_QVSTFSXA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFSXI, PPC_INS_QVSTFSXI,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFSXIA, PPC_INS_QVSTFSXIA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_QVSTFSXs, PPC_INS_QVSTFSX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_QPX, 0 }, 0, 0
#endif
},
{
	PPC_RFCI, PPC_INS_RFCI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_BOOKE, 0 }, 0, 0
#endif
},
{
	PPC_RFDI, PPC_INS_RFDI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_E500, 0 }, 0, 0
#endif
},
{
	PPC_RFI, PPC_INS_RFI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_BOOKE, 0 }, 0, 0
#endif
},
{
	PPC_RFID, PPC_INS_RFID,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_RFMCI, PPC_INS_RFMCI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_E500, 0 }, 0, 0
#endif
},
{
	PPC_RLDCL, PPC_INS_RLDCL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_RLDCLo, PPC_INS_RLDCL,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_RLDCR, PPC_INS_RLDCR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_RLDCRo, PPC_INS_RLDCR,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_RLDIC, PPC_INS_RLDIC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_RLDICL, PPC_INS_RLDICL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_RLDICL_32_64, PPC_INS_RLDICL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_RLDICLo, PPC_INS_RLDICL,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_RLDICR, PPC_INS_RLDICR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_RLDICRo, PPC_INS_RLDICR,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_RLDICo, PPC_INS_RLDIC,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_RLDIMI, PPC_INS_RLDIMI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_RLDIMIo, PPC_INS_RLDIMI,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_RLWIMI, PPC_INS_RLWIMI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_RLWIMI8, PPC_INS_RLWIMI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_RLWIMI8o, PPC_INS_RLWIMI,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_RLWIMIo, PPC_INS_RLWIMI,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_RLWINM, PPC_INS_RLWINM,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_RLWINM8, PPC_INS_RLWINM,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_RLWINM8o, PPC_INS_RLWINM,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_RLWINMo, PPC_INS_RLWINM,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_RLWNM, PPC_INS_RLWNM,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_RLWNM8, PPC_INS_RLWNM,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_RLWNM8o, PPC_INS_RLWNM,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_RLWNMo, PPC_INS_RLWNM,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SC, PPC_INS_SC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SLBIA, PPC_INS_SLBIA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SLBIE, PPC_INS_SLBIE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SLBMFEE, PPC_INS_SLBMFEE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SLBMTE, PPC_INS_SLBMTE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SLD, PPC_INS_SLD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SLDo, PPC_INS_SLD,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SLW, PPC_INS_SLW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SLW8, PPC_INS_SLW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SLW8o, PPC_INS_SLW,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SLWo, PPC_INS_SLW,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SRAD, PPC_INS_SRAD,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CARRY, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SRADI, PPC_INS_SRADI,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CARRY, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SRADIo, PPC_INS_SRADI,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CARRY, PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SRADo, PPC_INS_SRAD,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CARRY, PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SRAW, PPC_INS_SRAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CARRY, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SRAWI, PPC_INS_SRAWI,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CARRY, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SRAWIo, PPC_INS_SRAWI,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CARRY, PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SRAWo, PPC_INS_SRAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CARRY, PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SRD, PPC_INS_SRD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SRDo, PPC_INS_SRD,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SRW, PPC_INS_SRW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SRW8, PPC_INS_SRW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SRW8o, PPC_INS_SRW,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SRWo, PPC_INS_SRW,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STB, PPC_INS_STB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STB8, PPC_INS_STB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STBCIX, PPC_INS_STBCIX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STBU, PPC_INS_STBU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STBU8, PPC_INS_STBU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STBUX, PPC_INS_STBUX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STBUX8, PPC_INS_STBUX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STBX, PPC_INS_STBX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STBX8, PPC_INS_STBX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STD, PPC_INS_STD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STDBRX, PPC_INS_STDBRX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STDCIX, PPC_INS_STDCIX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STDCX, PPC_INS_STDCX,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STDU, PPC_INS_STDU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STDUX, PPC_INS_STDUX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STDX, PPC_INS_STDX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STFD, PPC_INS_STFD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STFDU, PPC_INS_STFDU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STFDUX, PPC_INS_STFDUX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STFDX, PPC_INS_STFDX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STFIWX, PPC_INS_STFIWX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STFS, PPC_INS_STFS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STFSU, PPC_INS_STFSU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STFSUX, PPC_INS_STFSUX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STFSX, PPC_INS_STFSX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STH, PPC_INS_STH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STH8, PPC_INS_STH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STHBRX, PPC_INS_STHBRX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STHCIX, PPC_INS_STHCIX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STHU, PPC_INS_STHU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STHU8, PPC_INS_STHU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STHUX, PPC_INS_STHUX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STHUX8, PPC_INS_STHUX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STHX, PPC_INS_STHX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STHX8, PPC_INS_STHX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STMW, PPC_INS_STMW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STSWI, PPC_INS_STSWI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STVEBX, PPC_INS_STVEBX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_STVEHX, PPC_INS_STVEHX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_STVEWX, PPC_INS_STVEWX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_STVX, PPC_INS_STVX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_STVXL, PPC_INS_STVXL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_STW, PPC_INS_STW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STW8, PPC_INS_STW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STWBRX, PPC_INS_STWBRX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STWCIX, PPC_INS_STWCIX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STWCX, PPC_INS_STWCX,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STWU, PPC_INS_STWU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STWU8, PPC_INS_STWU,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STWUX, PPC_INS_STWUX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STWUX8, PPC_INS_STWUX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STWX, PPC_INS_STWX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STWX8, PPC_INS_STWX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_STXSDX, PPC_INS_STXSDX,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_STXVD2X, PPC_INS_STXVD2X,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_STXVW4X, PPC_INS_STXVW4X,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_SUBF, PPC_INS_SUBF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SUBF8, PPC_INS_SUBF,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SUBF8o, PPC_INS_SUBF,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SUBFC, PPC_INS_SUBFC,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CARRY, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SUBFC8, PPC_INS_SUBFC,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CARRY, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SUBFC8o, PPC_INS_SUBFC,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CARRY, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SUBFCo, PPC_INS_SUBFC,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CARRY, PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SUBFE, PPC_INS_SUBFE,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CARRY, 0 }, { PPC_REG_CARRY, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SUBFE8, PPC_INS_SUBFE,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CARRY, 0 }, { PPC_REG_CARRY, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SUBFE8o, PPC_INS_SUBFE,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CARRY, 0 }, { PPC_REG_CARRY, PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SUBFEo, PPC_INS_SUBFE,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CARRY, 0 }, { PPC_REG_CARRY, PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SUBFIC, PPC_INS_SUBFIC,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CARRY, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SUBFIC8, PPC_INS_SUBFIC,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CARRY, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SUBFME, PPC_INS_SUBFME,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CARRY, 0 }, { PPC_REG_CARRY, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SUBFME8, PPC_INS_SUBFME,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CARRY, 0 }, { PPC_REG_CARRY, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SUBFME8o, PPC_INS_SUBFME,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CARRY, 0 }, { PPC_REG_CARRY, PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SUBFMEo, PPC_INS_SUBFME,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CARRY, 0 }, { PPC_REG_CARRY, PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SUBFZE, PPC_INS_SUBFZE,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CARRY, 0 }, { PPC_REG_CARRY, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SUBFZE8, PPC_INS_SUBFZE,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CARRY, 0 }, { PPC_REG_CARRY, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SUBFZE8o, PPC_INS_SUBFZE,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CARRY, 0 }, { PPC_REG_CARRY, PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SUBFZEo, PPC_INS_SUBFZE,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CARRY, 0 }, { PPC_REG_CARRY, PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SUBFo, PPC_INS_SUBF,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_SYNC, PPC_INS_SYNC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_TAILB, PPC_INS_B,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_TAILB8, PPC_INS_B,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_TAILBA, PPC_INS_BA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_TAILBA8, PPC_INS_BA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { 0 }, 1, 0
#endif
},
{
	PPC_TAILBCTR, PPC_INS_BCTR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_MODE32, 0 }, 1, 1
#endif
},
{
	PPC_TAILBCTR8, PPC_INS_BCTR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR8, PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_MODE64, 0 }, 1, 1
#endif
},
{
	PPC_TD, PPC_INS_TD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_TDI, PPC_INS_TDI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_TLBIA, PPC_INS_TLBIA,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_TLBIE, PPC_INS_TLBIE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_TLBIEL, PPC_INS_TLBIEL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_TLBIVAX, PPC_INS_TLBIVAX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_BOOKE, 0 }, 0, 0
#endif
},
{
	PPC_TLBLD, PPC_INS_TLBLD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_PPC6XX, 0 }, 0, 0
#endif
},
{
	PPC_TLBLI, PPC_INS_TLBLI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_PPC6XX, 0 }, 0, 0
#endif
},
{
	PPC_TLBRE, PPC_INS_TLBRE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_BOOKE, 0 }, 0, 0
#endif
},
{
	PPC_TLBRE2, PPC_INS_TLBRE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_PPC4XX, 0 }, 0, 0
#endif
},
{
	PPC_TLBSX, PPC_INS_TLBSX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_BOOKE, 0 }, 0, 0
#endif
},
{
	PPC_TLBSX2, PPC_INS_TLBSX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_PPC4XX, 0 }, 0, 0
#endif
},
{
	PPC_TLBSX2D, PPC_INS_TLBSX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_PPC4XX, 0 }, 0, 0
#endif
},
{
	PPC_TLBSYNC, PPC_INS_TLBSYNC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_TLBWE, PPC_INS_TLBWE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_BOOKE, 0 }, 0, 0
#endif
},
{
	PPC_TLBWE2, PPC_INS_TLBWE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_PPC4XX, 0 }, 0, 0
#endif
},
{
	PPC_TRAP, PPC_INS_TRAP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_TW, PPC_INS_TW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_TWI, PPC_INS_TWI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_VADDCUW, PPC_INS_VADDCUW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VADDFP, PPC_INS_VADDFP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VADDSBS, PPC_INS_VADDSBS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VADDSHS, PPC_INS_VADDSHS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VADDSWS, PPC_INS_VADDSWS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VADDUBM, PPC_INS_VADDUBM,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VADDUBS, PPC_INS_VADDUBS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VADDUDM, PPC_INS_VADDUDM,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VADDUHM, PPC_INS_VADDUHM,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VADDUHS, PPC_INS_VADDUHS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VADDUWM, PPC_INS_VADDUWM,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VADDUWS, PPC_INS_VADDUWS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VAND, PPC_INS_VAND,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VANDC, PPC_INS_VANDC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VAVGSB, PPC_INS_VAVGSB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VAVGSH, PPC_INS_VAVGSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VAVGSW, PPC_INS_VAVGSW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VAVGUB, PPC_INS_VAVGUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VAVGUH, PPC_INS_VAVGUH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VAVGUW, PPC_INS_VAVGUW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCFSX, PPC_INS_VCFSX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCFSX_0, PPC_INS_VCFSX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCFUX, PPC_INS_VCFUX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCFUX_0, PPC_INS_VCFUX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCLZB, PPC_INS_VCLZB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCLZD, PPC_INS_VCLZD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCLZH, PPC_INS_VCLZH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCLZW, PPC_INS_VCLZW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPBFP, PPC_INS_VCMPBFP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPBFPo, PPC_INS_VCMPBFP,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR6, 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPEQFP, PPC_INS_VCMPEQFP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPEQFPo, PPC_INS_VCMPEQFP,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR6, 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPEQUB, PPC_INS_VCMPEQUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPEQUBo, PPC_INS_VCMPEQUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR6, 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPEQUD, PPC_INS_VCMPEQUD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPEQUDo, PPC_INS_VCMPEQUD,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR6, 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPEQUH, PPC_INS_VCMPEQUH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPEQUHo, PPC_INS_VCMPEQUH,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR6, 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPEQUW, PPC_INS_VCMPEQUW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPEQUWo, PPC_INS_VCMPEQUW,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR6, 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPGEFP, PPC_INS_VCMPGEFP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPGEFPo, PPC_INS_VCMPGEFP,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR6, 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPGTFP, PPC_INS_VCMPGTFP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPGTFPo, PPC_INS_VCMPGTFP,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR6, 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPGTSB, PPC_INS_VCMPGTSB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPGTSBo, PPC_INS_VCMPGTSB,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR6, 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPGTSD, PPC_INS_VCMPGTSD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPGTSDo, PPC_INS_VCMPGTSD,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR6, 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPGTSH, PPC_INS_VCMPGTSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPGTSHo, PPC_INS_VCMPGTSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR6, 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPGTSW, PPC_INS_VCMPGTSW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPGTSWo, PPC_INS_VCMPGTSW,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR6, 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPGTUB, PPC_INS_VCMPGTUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPGTUBo, PPC_INS_VCMPGTUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR6, 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPGTUD, PPC_INS_VCMPGTUD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPGTUDo, PPC_INS_VCMPGTUD,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR6, 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPGTUH, PPC_INS_VCMPGTUH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPGTUHo, PPC_INS_VCMPGTUH,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR6, 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPGTUW, PPC_INS_VCMPGTUW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCMPGTUWo, PPC_INS_VCMPGTUW,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR6, 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCTSXS, PPC_INS_VCTSXS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCTSXS_0, PPC_INS_VCTSXS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCTUXS, PPC_INS_VCTUXS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VCTUXS_0, PPC_INS_VCTUXS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VEQV, PPC_INS_VEQV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VEXPTEFP, PPC_INS_VEXPTEFP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VLOGEFP, PPC_INS_VLOGEFP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMADDFP, PPC_INS_VMADDFP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMAXFP, PPC_INS_VMAXFP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMAXSB, PPC_INS_VMAXSB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMAXSD, PPC_INS_VMAXSD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMAXSH, PPC_INS_VMAXSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMAXSW, PPC_INS_VMAXSW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMAXUB, PPC_INS_VMAXUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMAXUD, PPC_INS_VMAXUD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMAXUH, PPC_INS_VMAXUH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMAXUW, PPC_INS_VMAXUW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMHADDSHS, PPC_INS_VMHADDSHS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMHRADDSHS, PPC_INS_VMHRADDSHS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMIDUD, PPC_INS_VMINUD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMINFP, PPC_INS_VMINFP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMINSB, PPC_INS_VMINSB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMINSD, PPC_INS_VMINSD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMINSH, PPC_INS_VMINSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMINSW, PPC_INS_VMINSW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMINUB, PPC_INS_VMINUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMINUH, PPC_INS_VMINUH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMINUW, PPC_INS_VMINUW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMLADDUHM, PPC_INS_VMLADDUHM,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMRGHB, PPC_INS_VMRGHB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMRGHH, PPC_INS_VMRGHH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMRGHW, PPC_INS_VMRGHW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMRGLB, PPC_INS_VMRGLB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMRGLH, PPC_INS_VMRGLH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMRGLW, PPC_INS_VMRGLW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMSUMMBM, PPC_INS_VMSUMMBM,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMSUMSHM, PPC_INS_VMSUMSHM,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMSUMSHS, PPC_INS_VMSUMSHS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMSUMUBM, PPC_INS_VMSUMUBM,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMSUMUHM, PPC_INS_VMSUMUHM,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMSUMUHS, PPC_INS_VMSUMUHS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMULESB, PPC_INS_VMULESB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMULESH, PPC_INS_VMULESH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMULESW, PPC_INS_VMULESW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMULEUB, PPC_INS_VMULEUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMULEUH, PPC_INS_VMULEUH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMULEUW, PPC_INS_VMULEUW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMULOSB, PPC_INS_VMULOSB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMULOSH, PPC_INS_VMULOSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMULOSW, PPC_INS_VMULOSW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMULOUB, PPC_INS_VMULOUB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMULOUH, PPC_INS_VMULOUH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMULOUW, PPC_INS_VMULOUW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VMULUWM, PPC_INS_VMULUWM,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VNAND, PPC_INS_VNAND,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VNMSUBFP, PPC_INS_VNMSUBFP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VNOR, PPC_INS_VNOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VOR, PPC_INS_VOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VORC, PPC_INS_VORC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VPERM, PPC_INS_VPERM,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VPKPX, PPC_INS_VPKPX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VPKSHSS, PPC_INS_VPKSHSS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VPKSHUS, PPC_INS_VPKSHUS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VPKSWSS, PPC_INS_VPKSWSS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VPKSWUS, PPC_INS_VPKSWUS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VPKUHUM, PPC_INS_VPKUHUM,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VPKUHUS, PPC_INS_VPKUHUS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VPKUWUM, PPC_INS_VPKUWUM,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VPKUWUS, PPC_INS_VPKUWUS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VPOPCNTB, PPC_INS_VPOPCNTB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VPOPCNTD, PPC_INS_VPOPCNTD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VPOPCNTH, PPC_INS_VPOPCNTH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VPOPCNTW, PPC_INS_VPOPCNTW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VREFP, PPC_INS_VREFP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VRFIM, PPC_INS_VRFIM,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VRFIN, PPC_INS_VRFIN,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VRFIP, PPC_INS_VRFIP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VRFIZ, PPC_INS_VRFIZ,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VRLB, PPC_INS_VRLB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VRLD, PPC_INS_VRLD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VRLH, PPC_INS_VRLH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VRLW, PPC_INS_VRLW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VRSQRTEFP, PPC_INS_VRSQRTEFP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSEL, PPC_INS_VSEL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSL, PPC_INS_VSL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSLB, PPC_INS_VSLB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSLD, PPC_INS_VSLD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSLDOI, PPC_INS_VSLDOI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSLH, PPC_INS_VSLH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSLO, PPC_INS_VSLO,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSLW, PPC_INS_VSLW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSPLTB, PPC_INS_VSPLTB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSPLTH, PPC_INS_VSPLTH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSPLTISB, PPC_INS_VSPLTISB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSPLTISH, PPC_INS_VSPLTISH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSPLTISW, PPC_INS_VSPLTISW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSPLTW, PPC_INS_VSPLTW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSR, PPC_INS_VSR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSRAB, PPC_INS_VSRAB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSRAD, PPC_INS_VSRAD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSRAH, PPC_INS_VSRAH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSRAW, PPC_INS_VSRAW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSRB, PPC_INS_VSRB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSRD, PPC_INS_VSRD,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSRH, PPC_INS_VSRH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSRO, PPC_INS_VSRO,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSRW, PPC_INS_VSRW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSUBCUW, PPC_INS_VSUBCUW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSUBFP, PPC_INS_VSUBFP,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSUBSBS, PPC_INS_VSUBSBS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSUBSHS, PPC_INS_VSUBSHS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSUBSWS, PPC_INS_VSUBSWS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSUBUBM, PPC_INS_VSUBUBM,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSUBUBS, PPC_INS_VSUBUBS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSUBUDM, PPC_INS_VSUBUDM,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSUBUHM, PPC_INS_VSUBUHM,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSUBUHS, PPC_INS_VSUBUHS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSUBUWM, PPC_INS_VSUBUWM,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSUBUWS, PPC_INS_VSUBUWS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSUM2SWS, PPC_INS_VSUM2SWS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSUM4SBS, PPC_INS_VSUM4SBS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSUM4SHS, PPC_INS_VSUM4SHS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSUM4UBS, PPC_INS_VSUM4UBS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VSUMSWS, PPC_INS_VSUMSWS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VUPKHPX, PPC_INS_VUPKHPX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VUPKHSB, PPC_INS_VUPKHSB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VUPKHSH, PPC_INS_VUPKHSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VUPKLPX, PPC_INS_VUPKLPX,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VUPKLSB, PPC_INS_VUPKLSB,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VUPKLSH, PPC_INS_VUPKLSH,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_VXOR, PPC_INS_VXOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_V_SET0, PPC_INS_VXOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_V_SET0B, PPC_INS_VXOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_V_SET0H, PPC_INS_VXOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_V_SETALLONES, PPC_INS_VSPLTISW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_V_SETALLONESB, PPC_INS_VSPLTISW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_V_SETALLONESH, PPC_INS_VSPLTISW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_ALTIVEC, 0 }, 0, 0
#endif
},
{
	PPC_WAIT, PPC_INS_WAIT,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_WRTEE, PPC_INS_WRTEE,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_BOOKE, 0 }, 0, 0
#endif
},
{
	PPC_WRTEEI, PPC_INS_WRTEEI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_BOOKE, 0 }, 0, 0
#endif
},
{
	PPC_XOR, PPC_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_XOR8, PPC_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_XOR8o, PPC_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_XORI, PPC_INS_XORI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_XORI8, PPC_INS_XORI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_XORIS, PPC_INS_XORIS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_XORIS8, PPC_INS_XORIS,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_XORo, PPC_INS_XOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { PPC_REG_CR0, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_XSABSDP, PPC_INS_XSABSDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSADDDP, PPC_INS_XSADDDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSCMPODP, PPC_INS_XSCMPODP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSCMPUDP, PPC_INS_XSCMPUDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSCPSGNDP, PPC_INS_XSCPSGNDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSCVDPSP, PPC_INS_XSCVDPSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSCVDPSXDS, PPC_INS_XSCVDPSXDS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSCVDPSXWS, PPC_INS_XSCVDPSXWS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSCVDPUXDS, PPC_INS_XSCVDPUXDS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSCVDPUXWS, PPC_INS_XSCVDPUXWS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSCVSPDP, PPC_INS_XSCVSPDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSCVSXDDP, PPC_INS_XSCVSXDDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSCVUXDDP, PPC_INS_XSCVUXDDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSDIVDP, PPC_INS_XSDIVDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSMADDADP, PPC_INS_XSMADDADP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSMADDMDP, PPC_INS_XSMADDMDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSMAXDP, PPC_INS_XSMAXDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSMINDP, PPC_INS_XSMINDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSMSUBADP, PPC_INS_XSMSUBADP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSMSUBMDP, PPC_INS_XSMSUBMDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSMULDP, PPC_INS_XSMULDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSNABSDP, PPC_INS_XSNABSDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSNEGDP, PPC_INS_XSNEGDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSNMADDADP, PPC_INS_XSNMADDADP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSNMADDMDP, PPC_INS_XSNMADDMDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSNMSUBADP, PPC_INS_XSNMSUBADP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSNMSUBMDP, PPC_INS_XSNMSUBMDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSRDPI, PPC_INS_XSRDPI,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSRDPIC, PPC_INS_XSRDPIC,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSRDPIM, PPC_INS_XSRDPIM,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSRDPIP, PPC_INS_XSRDPIP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSRDPIZ, PPC_INS_XSRDPIZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSREDP, PPC_INS_XSREDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSRSQRTEDP, PPC_INS_XSRSQRTEDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSSQRTDP, PPC_INS_XSSQRTDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSSUBDP, PPC_INS_XSSUBDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSTDIVDP, PPC_INS_XSTDIVDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XSTSQRTDP, PPC_INS_XSTSQRTDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVABSDP, PPC_INS_XVABSDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVABSSP, PPC_INS_XVABSSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVADDDP, PPC_INS_XVADDDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVADDSP, PPC_INS_XVADDSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCMPEQDP, PPC_INS_XVCMPEQDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCMPEQDPo, PPC_INS_XVCMPEQDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR6, 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCMPEQSP, PPC_INS_XVCMPEQSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCMPEQSPo, PPC_INS_XVCMPEQSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR6, 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCMPGEDP, PPC_INS_XVCMPGEDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCMPGEDPo, PPC_INS_XVCMPGEDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR6, 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCMPGESP, PPC_INS_XVCMPGESP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCMPGESPo, PPC_INS_XVCMPGESP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR6, 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCMPGTDP, PPC_INS_XVCMPGTDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCMPGTDPo, PPC_INS_XVCMPGTDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR6, 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCMPGTSP, PPC_INS_XVCMPGTSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCMPGTSPo, PPC_INS_XVCMPGTSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { PPC_REG_CR6, 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCPSGNDP, PPC_INS_XVCPSGNDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCPSGNSP, PPC_INS_XVCPSGNSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCVDPSP, PPC_INS_XVCVDPSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCVDPSXDS, PPC_INS_XVCVDPSXDS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCVDPSXWS, PPC_INS_XVCVDPSXWS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCVDPUXDS, PPC_INS_XVCVDPUXDS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCVDPUXWS, PPC_INS_XVCVDPUXWS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCVSPDP, PPC_INS_XVCVSPDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCVSPSXDS, PPC_INS_XVCVSPSXDS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCVSPSXWS, PPC_INS_XVCVSPSXWS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCVSPUXDS, PPC_INS_XVCVSPUXDS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCVSPUXWS, PPC_INS_XVCVSPUXWS,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCVSXDDP, PPC_INS_XVCVSXDDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCVSXDSP, PPC_INS_XVCVSXDSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCVSXWDP, PPC_INS_XVCVSXWDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCVSXWSP, PPC_INS_XVCVSXWSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCVUXDDP, PPC_INS_XVCVUXDDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCVUXDSP, PPC_INS_XVCVUXDSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCVUXWDP, PPC_INS_XVCVUXWDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVCVUXWSP, PPC_INS_XVCVUXWSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVDIVDP, PPC_INS_XVDIVDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVDIVSP, PPC_INS_XVDIVSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVMADDADP, PPC_INS_XVMADDADP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVMADDASP, PPC_INS_XVMADDASP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVMADDMDP, PPC_INS_XVMADDMDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVMADDMSP, PPC_INS_XVMADDMSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVMAXDP, PPC_INS_XVMAXDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVMAXSP, PPC_INS_XVMAXSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVMINDP, PPC_INS_XVMINDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVMINSP, PPC_INS_XVMINSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVMSUBADP, PPC_INS_XVMSUBADP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVMSUBASP, PPC_INS_XVMSUBASP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVMSUBMDP, PPC_INS_XVMSUBMDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVMSUBMSP, PPC_INS_XVMSUBMSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVMULDP, PPC_INS_XVMULDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVMULSP, PPC_INS_XVMULSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVNABSDP, PPC_INS_XVNABSDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVNABSSP, PPC_INS_XVNABSSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVNEGDP, PPC_INS_XVNEGDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVNEGSP, PPC_INS_XVNEGSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVNMADDADP, PPC_INS_XVNMADDADP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVNMADDASP, PPC_INS_XVNMADDASP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVNMADDMDP, PPC_INS_XVNMADDMDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVNMADDMSP, PPC_INS_XVNMADDMSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVNMSUBADP, PPC_INS_XVNMSUBADP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVNMSUBASP, PPC_INS_XVNMSUBASP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVNMSUBMDP, PPC_INS_XVNMSUBMDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVNMSUBMSP, PPC_INS_XVNMSUBMSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVRDPI, PPC_INS_XVRDPI,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVRDPIC, PPC_INS_XVRDPIC,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVRDPIM, PPC_INS_XVRDPIM,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVRDPIP, PPC_INS_XVRDPIP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVRDPIZ, PPC_INS_XVRDPIZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVREDP, PPC_INS_XVREDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVRESP, PPC_INS_XVRESP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVRSPI, PPC_INS_XVRSPI,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVRSPIC, PPC_INS_XVRSPIC,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVRSPIM, PPC_INS_XVRSPIM,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVRSPIP, PPC_INS_XVRSPIP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVRSPIZ, PPC_INS_XVRSPIZ,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVRSQRTEDP, PPC_INS_XVRSQRTEDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVRSQRTESP, PPC_INS_XVRSQRTESP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVSQRTDP, PPC_INS_XVSQRTDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVSQRTSP, PPC_INS_XVSQRTSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVSUBDP, PPC_INS_XVSUBDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVSUBSP, PPC_INS_XVSUBSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVTDIVDP, PPC_INS_XVTDIVDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVTDIVSP, PPC_INS_XVTDIVSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVTSQRTDP, PPC_INS_XVTSQRTDP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XVTSQRTSP, PPC_INS_XVTSQRTSP,
#ifndef CAPSTONE_DIET
	{ PPC_REG_RM, 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XXLAND, PPC_INS_XXLAND,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XXLANDC, PPC_INS_XXLANDC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XXLEQV, PPC_INS_XXLEQV,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8VECTOR, 0 }, 0, 0
#endif
},
{
	PPC_XXLNAND, PPC_INS_XXLNAND,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8VECTOR, 0 }, 0, 0
#endif
},
{
	PPC_XXLNOR, PPC_INS_XXLNOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XXLOR, PPC_INS_XXLOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XXLORC, PPC_INS_XXLORC,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_P8VECTOR, 0 }, 0, 0
#endif
},
{
	PPC_XXLORf, PPC_INS_XXLOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XXLXOR, PPC_INS_XXLXOR,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XXMRGHW, PPC_INS_XXMRGHW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XXMRGLW, PPC_INS_XXMRGLW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XXPERMDI, PPC_INS_XXPERMDI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XXSEL, PPC_INS_XXSEL,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XXSLDWI, PPC_INS_XXSLDWI,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_XXSPLTW, PPC_INS_XXSPLTW,
#ifndef CAPSTONE_DIET
	{ 0 }, { 0 }, { PPC_GRP_VSX, 0 }, 0, 0
#endif
},
{
	PPC_gBC, PPC_INS_BC,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_gBCA, PPC_INS_BCA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_gBCCTR, PPC_INS_BCCTR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_LR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_gBCCTRL, PPC_INS_BCCTRL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_LR, PPC_REG_RM, 0 }, { PPC_REG_LR, PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_gBCL, PPC_INS_BCL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_RM, 0 }, { PPC_REG_LR, PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_gBCLA, PPC_INS_BCLA,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_RM, 0 }, { PPC_REG_LR, PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_gBCLR, PPC_INS_BCLR,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_LR, PPC_REG_RM, 0 }, { PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
{
	PPC_gBCLRL, PPC_INS_BCLRL,
#ifndef CAPSTONE_DIET
	{ PPC_REG_CTR, PPC_REG_LR, PPC_REG_RM, 0 }, { PPC_REG_LR, PPC_REG_CTR, 0 }, { 0 }, 0, 0
#endif
},
