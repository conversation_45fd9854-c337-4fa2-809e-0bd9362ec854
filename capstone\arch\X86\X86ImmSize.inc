{1, 1, X86_AAD8i8},
{1, 1, X86_AAM8i8},
{2, 2, X86_ADC16i16},
{2, 2, X86_ADC16mi},
{1, 2, X86_ADC16mi8},
{2, 2, X86_ADC16ri},
{1, 2, X86_ADC16ri8},
{4, 4, X86_ADC32i32},
{4, 4, X86_ADC32mi},
{1, 4, X86_ADC32mi8},
{4, 4, X86_ADC32ri},
{1, 4, X86_ADC32ri8},
{4, 8, X86_ADC64i32},
{4, 8, X86_ADC64mi32},
{1, 8, X86_ADC64mi8},
{4, 8, X86_ADC64ri32},
{1, 8, X86_ADC64ri8},
{1, 1, X86_ADC8i8},
{1, 1, X86_ADC8mi},
{1, 1, X86_ADC8mi8},
{1, 1, X86_ADC8ri},
{1, 1, X86_ADC8ri8},
{2, 2, X<PERSON>_ADD16i16},
{2, 2, X86_ADD16mi},
{1, 2, X86_ADD16mi8},
{2, 2, X86_ADD16ri},
{1, 2, X86_ADD16ri8},
{4, 4, X86_ADD32i32},
{4, 4, X86_ADD32mi},
{1, 4, X86_ADD32mi8},
{4, 4, X86_ADD32ri},
{1, 4, X86_ADD32ri8},
{4, 8, X86_ADD64i32},
{4, 8, X86_ADD64mi32},
{1, 8, X86_ADD64mi8},
{4, 8, X86_ADD64ri32},
{1, 8, X86_ADD64ri8},
{1, 1, X86_ADD8i8},
{1, 1, X86_ADD8mi},
{1, 1, X86_ADD8mi8},
{1, 1, X86_ADD8ri},
{1, 1, X86_ADD8ri8},
{2, 2, X86_AND16i16},
{2, 2, X86_AND16mi},
{1, 2, X86_AND16mi8},
{2, 2, X86_AND16ri},
{1, 2, X86_AND16ri8},
{4, 4, X86_AND32i32},
{4, 4, X86_AND32mi},
{1, 4, X86_AND32mi8},
{4, 4, X86_AND32ri},
{1, 4, X86_AND32ri8},
{4, 8, X86_AND64i32},
{4, 8, X86_AND64mi32},
{1, 8, X86_AND64mi8},
{4, 8, X86_AND64ri32},
{1, 8, X86_AND64ri8},
{1, 1, X86_AND8i8},
{1, 1, X86_AND8mi},
{1, 1, X86_AND8mi8},
{1, 1, X86_AND8ri},
{1, 1, X86_AND8ri8},
{1, 1, X86_BT16mi8},
{1, 1, X86_BT16ri8},
{1, 1, X86_BT32mi8},
{1, 1, X86_BT32ri8},
{1, 1, X86_BT64mi8},
{1, 1, X86_BT64ri8},
{1, 1, X86_BTC16mi8},
{1, 1, X86_BTC16ri8},
{1, 1, X86_BTC32mi8},
{1, 1, X86_BTC32ri8},
{1, 1, X86_BTC64mi8},
{1, 1, X86_BTC64ri8},
{1, 1, X86_BTR16mi8},
{1, 1, X86_BTR16ri8},
{1, 1, X86_BTR32mi8},
{1, 1, X86_BTR32ri8},
{1, 1, X86_BTR64mi8},
{1, 1, X86_BTR64ri8},
{1, 1, X86_BTS16mi8},
{1, 1, X86_BTS16ri8},
{1, 1, X86_BTS32mi8},
{1, 1, X86_BTS32ri8},
{1, 1, X86_BTS64mi8},
{1, 1, X86_BTS64ri8},
{2, 2, X86_CALLpcrel16},
{2, 2, X86_CMP16i16},
{2, 2, X86_CMP16mi},
{1, 2, X86_CMP16mi8},
{2, 2, X86_CMP16ri},
{1, 2, X86_CMP16ri8},
{4, 4, X86_CMP32i32},
{4, 4, X86_CMP32mi},
{1, 4, X86_CMP32mi8},
{4, 4, X86_CMP32ri},
{1, 4, X86_CMP32ri8},
{4, 8, X86_CMP64i32},
{4, 8, X86_CMP64mi32},
{1, 8, X86_CMP64mi8},
{4, 8, X86_CMP64ri32},
{1, 8, X86_CMP64ri8},
{1, 1, X86_CMP8i8},
{1, 1, X86_CMP8mi},
{1, 1, X86_CMP8mi8},
{1, 1, X86_CMP8ri},
{1, 1, X86_CMP8ri8},
{1, 2, X86_IMUL16rmi8},
{1, 2, X86_IMUL16rri8},
{1, 4, X86_IMUL32rmi8},
{1, 4, X86_IMUL32rri8},
{4, 8, X86_IMUL64rmi32},
{1, 8, X86_IMUL64rmi8},
{4, 8, X86_IMUL64rri32},
{1, 8, X86_IMUL64rri8},
{2, 2, X86_IN16ri},
{4, 4, X86_IN32ri},
{1, 1, X86_IN8ri},
{2, 2, X86_JMP_2},
{2, 2, X86_MOV16mi},
{2, 2, X86_MOV16ri},
{2, 2, X86_MOV16ri_alt},
{4, 4, X86_MOV32mi},
{4, 4, X86_MOV32ri},
{8, 8, X86_MOV32ri64},
{4, 4, X86_MOV32ri_alt},
{4, 8, X86_MOV64mi32},
{8, 8, X86_MOV64ri},
{4, 8, X86_MOV64ri32},
{1, 1, X86_MOV8mi},
{1, 1, X86_MOV8ri},
{1, 1, X86_MOV8ri_alt},
{2, 2, X86_OR16i16},
{2, 2, X86_OR16mi},
{1, 2, X86_OR16mi8},
{2, 2, X86_OR16ri},
{1, 2, X86_OR16ri8},
{4, 4, X86_OR32i32},
{4, 4, X86_OR32mi},
{1, 4, X86_OR32mi8},
{4, 4, X86_OR32ri},
{1, 4, X86_OR32ri8},
{4, 8, X86_OR64i32},
{4, 8, X86_OR64mi32},
{1, 8, X86_OR64mi8},
{4, 8, X86_OR64ri32},
{1, 8, X86_OR64ri8},
{1, 1, X86_OR8i8},
{1, 1, X86_OR8mi},
{1, 1, X86_OR8mi8},
{1, 1, X86_OR8ri},
{1, 1, X86_OR8ri8},
{1, 2, X86_PUSH16i8},
{1, 4, X86_PUSH32i8},
{2, 8, X86_PUSH64i16},
{4, 8, X86_PUSH64i32},
{1, 8, X86_PUSH64i8},
{2, 2, X86_PUSHi16},
{4, 4, X86_PUSHi32},
{1, 1, X86_RCL16mi},
{1, 1, X86_RCL16ri},
{1, 1, X86_RCL32mi},
{1, 1, X86_RCL32ri},
{1, 1, X86_RCL64mi},
{1, 1, X86_RCL64ri},
{1, 1, X86_RCL8mi},
{1, 1, X86_RCL8ri},
{1, 1, X86_RCR16mi},
{1, 1, X86_RCR16ri},
{1, 1, X86_RCR32mi},
{1, 1, X86_RCR32ri},
{1, 1, X86_RCR64mi},
{1, 1, X86_RCR64ri},
{1, 1, X86_RCR8mi},
{1, 1, X86_RCR8ri},
{4, 4, X86_RELEASE_ADD32mi},
{4, 8, X86_RELEASE_ADD64mi32},
{1, 1, X86_RELEASE_ADD8mi},
{4, 4, X86_RELEASE_AND32mi},
{4, 8, X86_RELEASE_AND64mi32},
{1, 1, X86_RELEASE_AND8mi},
{2, 2, X86_RELEASE_MOV16mi},
{4, 4, X86_RELEASE_MOV32mi},
{4, 8, X86_RELEASE_MOV64mi32},
{1, 1, X86_RELEASE_MOV8mi},
{4, 4, X86_RELEASE_OR32mi},
{4, 8, X86_RELEASE_OR64mi32},
{1, 1, X86_RELEASE_OR8mi},
{4, 4, X86_RELEASE_XOR32mi},
{4, 8, X86_RELEASE_XOR64mi32},
{1, 1, X86_RELEASE_XOR8mi},
{1, 1, X86_ROL16mi},
{1, 1, X86_ROL16ri},
{1, 1, X86_ROL32mi},
{1, 1, X86_ROL32ri},
{1, 1, X86_ROL64mi},
{1, 1, X86_ROL64ri},
{1, 1, X86_ROL8mi},
{1, 1, X86_ROL8ri},
{1, 1, X86_ROR16mi},
{1, 1, X86_ROR16ri},
{1, 1, X86_ROR32mi},
{1, 1, X86_ROR32ri},
{1, 1, X86_ROR64mi},
{1, 1, X86_ROR64ri},
{1, 1, X86_ROR8mi},
{1, 1, X86_ROR8ri},
{4, 4, X86_RORX32mi},
{4, 4, X86_RORX32ri},
{8, 8, X86_RORX64mi},
{8, 8, X86_RORX64ri},
{1, 1, X86_SAL16mi},
{1, 1, X86_SAL16ri},
{1, 1, X86_SAL32mi},
{1, 1, X86_SAL32ri},
{1, 1, X86_SAL64mi},
{1, 1, X86_SAL64ri},
{1, 1, X86_SAL8mi},
{1, 1, X86_SAL8ri},
{1, 1, X86_SAR16mi},
{1, 1, X86_SAR16ri},
{1, 1, X86_SAR32mi},
{1, 1, X86_SAR32ri},
{1, 1, X86_SAR64mi},
{1, 1, X86_SAR64ri},
{1, 1, X86_SAR8mi},
{1, 1, X86_SAR8ri},
{2, 2, X86_SBB16i16},
{2, 2, X86_SBB16mi},
{1, 2, X86_SBB16mi8},
{2, 2, X86_SBB16ri},
{1, 2, X86_SBB16ri8},
{4, 4, X86_SBB32i32},
{4, 4, X86_SBB32mi},
{1, 4, X86_SBB32mi8},
{4, 4, X86_SBB32ri},
{1, 4, X86_SBB32ri8},
{4, 8, X86_SBB64i32},
{4, 8, X86_SBB64mi32},
{1, 8, X86_SBB64mi8},
{4, 8, X86_SBB64ri32},
{1, 8, X86_SBB64ri8},
{1, 1, X86_SBB8i8},
{1, 1, X86_SBB8mi},
{1, 1, X86_SBB8mi8},
{1, 1, X86_SBB8ri},
{1, 1, X86_SBB8ri8},
{1, 1, X86_SHL16mi},
{1, 1, X86_SHL16ri},
{1, 1, X86_SHL32mi},
{1, 1, X86_SHL32ri},
{1, 1, X86_SHL64mi},
{1, 1, X86_SHL64ri},
{1, 1, X86_SHL8mi},
{1, 1, X86_SHL8ri},
{1, 1, X86_SHLD16mri8},
{1, 1, X86_SHLD16rri8},
{1, 1, X86_SHLD32mri8},
{1, 1, X86_SHLD32rri8},
{1, 1, X86_SHLD64mri8},
{1, 1, X86_SHLD64rri8},
{1, 1, X86_SHR16mi},
{1, 1, X86_SHR16ri},
{1, 1, X86_SHR32mi},
{1, 1, X86_SHR32ri},
{1, 1, X86_SHR64mi},
{1, 1, X86_SHR64ri},
{1, 1, X86_SHR8mi},
{1, 1, X86_SHR8ri},
{1, 1, X86_SHRD16mri8},
{1, 1, X86_SHRD16rri8},
{1, 1, X86_SHRD32mri8},
{1, 1, X86_SHRD32rri8},
{1, 1, X86_SHRD64mri8},
{1, 1, X86_SHRD64rri8},
{2, 2, X86_SUB16i16},
{2, 2, X86_SUB16mi},
{1, 2, X86_SUB16mi8},
{2, 2, X86_SUB16ri},
{1, 2, X86_SUB16ri8},
{4, 4, X86_SUB32i32},
{4, 4, X86_SUB32mi},
{1, 4, X86_SUB32mi8},
{4, 4, X86_SUB32ri},
{1, 4, X86_SUB32ri8},
{4, 8, X86_SUB64i32},
{4, 8, X86_SUB64mi32},
{1, 8, X86_SUB64mi8},
{4, 8, X86_SUB64ri32},
{1, 8, X86_SUB64ri8},
{1, 1, X86_SUB8i8},
{1, 1, X86_SUB8mi},
{1, 1, X86_SUB8mi8},
{1, 1, X86_SUB8ri},
{1, 1, X86_SUB8ri8},
{8, 8, X86_TCRETURNdi64},
{8, 8, X86_TCRETURNmi64},
{8, 8, X86_TCRETURNri64},
{2, 2, X86_TEST16i16},
{2, 2, X86_TEST16mi},
{2, 2, X86_TEST16mi_alt},
{2, 2, X86_TEST16ri},
{2, 2, X86_TEST16ri_alt},
{4, 4, X86_TEST32i32},
{4, 4, X86_TEST32mi},
{4, 4, X86_TEST32mi_alt},
{4, 4, X86_TEST32ri},
{4, 4, X86_TEST32ri_alt},
{4, 8, X86_TEST64i32},
{4, 8, X86_TEST64mi32},
{4, 4, X86_TEST64mi32_alt},
{4, 8, X86_TEST64ri32},
{4, 4, X86_TEST64ri32_alt},
{1, 1, X86_TEST8i8},
{1, 1, X86_TEST8mi},
{1, 1, X86_TEST8mi_alt},
{1, 1, X86_TEST8ri},
{1, 1, X86_TEST8ri_NOREX},
{1, 1, X86_TEST8ri_alt},
{2, 2, X86_XOR16i16},
{2, 2, X86_XOR16mi},
{1, 2, X86_XOR16mi8},
{2, 2, X86_XOR16ri},
{1, 2, X86_XOR16ri8},
{4, 4, X86_XOR32i32},
{4, 4, X86_XOR32mi},
{1, 4, X86_XOR32mi8},
{4, 4, X86_XOR32ri},
{1, 4, X86_XOR32ri8},
{4, 8, X86_XOR64i32},
{4, 8, X86_XOR64mi32},
{1, 8, X86_XOR64mi8},
{4, 8, X86_XOR64ri32},
{1, 8, X86_XOR64ri8},
{1, 1, X86_XOR8i8},
{1, 1, X86_XOR8mi},
{1, 1, X86_XOR8mi8},
{1, 1, X86_XOR8ri},
{1, 1, X86_XOR8ri8},
