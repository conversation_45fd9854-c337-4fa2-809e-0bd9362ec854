/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Assembly Writer Source Fragment                                             *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine, http://www.capstone-engine.org */
/* By <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2015 */

/// printInstruction - This method is automatically generated by tablegen
/// from the instruction set description.
static void printInstruction(MCInst *MI, SStream *O, MCRegisterInfo *MRI)
{
  static const uint32_t OpInfo[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    2743U,	// DBG_VALUE
    0U,	// REG_SEQUENCE
    0U,	// COPY
    2736U,	// BUNDLE
    2799U,	// LIFETIME_START
    2723U,	// LIFETIME_END
    0U,	// STACKMAP
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    0U,	// STATEPOINT
    0U,	// FRAME_ALLOC
    2814U,	// AAA
    4314U,	// AAD8i8
    4794U,	// AAM8i8
    3356U,	// AAS
    2478U,	// ACQUIRE_MOV16rm
    2478U,	// ACQUIRE_MOV32rm
    2478U,	// ACQUIRE_MOV64rm
    2478U,	// ACQUIRE_MOV8rm
    5571U,	// ADC16i16
    270504U,	// ADC16mi
    270504U,	// ADC16mi8
    270504U,	// ADC16mr
    4468904U,	// ADC16ri
    4468904U,	// ADC16ri8
    8663208U,	// ADC16rm
    4468904U,	// ADC16rr
    4460712U,	// ADC16rr_REV
    5707U,	// ADC32i32
    278696U,	// ADC32mi
    278696U,	// ADC32mi8
    278696U,	// ADC32mr
    4468904U,	// ADC32ri
    4468904U,	// ADC32ri8
    12857512U,	// ADC32rm
    4468904U,	// ADC32rr
    4460712U,	// ADC32rr_REV
    5855U,	// ADC64i32
    282792U,	// ADC64mi32
    282792U,	// ADC64mi8
    282792U,	// ADC64mr
    4468904U,	// ADC64ri32
    4468904U,	// ADC64ri8
    17051816U,	// ADC64rm
    4468904U,	// ADC64rr
    4460712U,	// ADC64rr_REV
    5469U,	// ADC8i8
    286888U,	// ADC8mi
    286888U,	// ADC8mi8
    286888U,	// ADC8mr
    4468904U,	// ADC8ri
    4468904U,	// ADC8ri8
    21246120U,	// ADC8rm
    4468904U,	// ADC8rr
    4460712U,	// ADC8rr_REV
    12850409U,	// ADCX32rm
    4461801U,	// ADCX32rr
    17044713U,	// ADCX64rm
    4461801U,	// ADCX64rr
    5580U,	// ADD16i16
    270568U,	// ADD16mi
    270568U,	// ADD16mi8
    270568U,	// ADD16mr
    4468968U,	// ADD16ri
    4468968U,	// ADD16ri8
    0U,	// ADD16ri8_DB
    0U,	// ADD16ri_DB
    8663272U,	// ADD16rm
    4468968U,	// ADD16rr
    0U,	// ADD16rr_DB
    4460776U,	// ADD16rr_REV
    5717U,	// ADD32i32
    278760U,	// ADD32mi
    278760U,	// ADD32mi8
    278760U,	// ADD32mr
    4468968U,	// ADD32ri
    4468968U,	// ADD32ri8
    0U,	// ADD32ri8_DB
    0U,	// ADD32ri_DB
    12857576U,	// ADD32rm
    4468968U,	// ADD32rr
    0U,	// ADD32rr_DB
    4460776U,	// ADD32rr_REV
    5865U,	// ADD64i32
    282856U,	// ADD64mi32
    282856U,	// ADD64mi8
    282856U,	// ADD64mr
    4468968U,	// ADD64ri32
    0U,	// ADD64ri32_DB
    4468968U,	// ADD64ri8
    0U,	// ADD64ri8_DB
    17051880U,	// ADD64rm
    4468968U,	// ADD64rr
    0U,	// ADD64rr_DB
    4460776U,	// ADD64rr_REV
    5478U,	// ADD8i8
    286952U,	// ADD8mi
    286952U,	// ADD8mi8
    286952U,	// ADD8mr
    4468968U,	// ADD8ri
    4468968U,	// ADD8ri8
    21246184U,	// ADD8rm
    4468968U,	// ADD8rr
    4460776U,	// ADD8rr_REV
    2753U,	// ADJCALLSTACKDOWN32
    2753U,	// ADJCALLSTACKDOWN64
    2771U,	// ADJCALLSTACKUP32
    2771U,	// ADJCALLSTACKUP64
    25433339U,	// ADOX32rm
    29627643U,	// ADOX32rr
    33821947U,	// ADOX64rm
    29627643U,	// ADOX64rr
    5589U,	// AND16i16
    270617U,	// AND16mi
    270617U,	// AND16mi8
    270617U,	// AND16mr
    4469017U,	// AND16ri
    4469017U,	// AND16ri8
    8663321U,	// AND16rm
    4469017U,	// AND16rr
    4460825U,	// AND16rr_REV
    5727U,	// AND32i32
    278809U,	// AND32mi
    278809U,	// AND32mi8
    278809U,	// AND32mr
    4469017U,	// AND32ri
    4469017U,	// AND32ri8
    12857625U,	// AND32rm
    4469017U,	// AND32rr
    4460825U,	// AND32rr_REV
    5875U,	// AND64i32
    282905U,	// AND64mi32
    282905U,	// AND64mi8
    282905U,	// AND64mr
    4469017U,	// AND64ri32
    4469017U,	// AND64ri8
    17051929U,	// AND64rm
    4469017U,	// AND64rr
    4460825U,	// AND64rr_REV
    5487U,	// AND8i8
    287001U,	// AND8mi
    287001U,	// AND8mi8
    287001U,	// AND8mr
    4469017U,	// AND8ri
    4469017U,	// AND8ri8
    21246233U,	// AND8rm
    4469017U,	// AND8rr
    4460825U,	// AND8rr_REV
    163844799U,	// ANDN32rm
    700715711U,	// ANDN32rr
    1237586623U,	// ANDN64rm
    700715711U,	// ANDN64rr
    271004U,	// ARPL16mr
    29627036U,	// ARPL16rr
    1770263499U,	// BEXTR32rm
    700715979U,	// BEXTR32rr
    1778652107U,	// BEXTR64rm
    700715979U,	// BEXTR64rr
    1770263499U,	// BEXTRI32mi
    700715979U,	// BEXTRI32ri
    1778652107U,	// BEXTRI64mi
    700715979U,	// BEXTRI64ri
    25432709U,	// BLCFILL32rm
    29627013U,	// BLCFILL32rr
    33821317U,	// BLCFILL64rm
    29627013U,	// BLCFILL64rr
    25432642U,	// BLCI32rm
    29626946U,	// BLCI32rr
    33821250U,	// BLCI64rm
    29626946U,	// BLCI64rr
    25432250U,	// BLCIC32rm
    29626554U,	// BLCIC32rr
    33820858U,	// BLCIC64rm
    29626554U,	// BLCIC64rr
    25432660U,	// BLCMSK32rm
    29626964U,	// BLCMSK32rr
    33821268U,	// BLCMSK64rm
    29626964U,	// BLCMSK64rr
    25433050U,	// BLCS32rm
    29627354U,	// BLCS32rr
    33821658U,	// BLCS64rm
    29627354U,	// BLCS64rr
    25432718U,	// BLSFILL32rm
    29627022U,	// BLSFILL32rr
    33821326U,	// BLSFILL64rm
    29627022U,	// BLSFILL64rr
    25432654U,	// BLSI32rm
    29626958U,	// BLSI32rr
    33821262U,	// BLSI64rm
    29626958U,	// BLSI64rr
    25432257U,	// BLSIC32rm
    29626561U,	// BLSIC32rr
    33820865U,	// BLSIC64rm
    29626561U,	// BLSIC64rr
    25432668U,	// BLSMSK32rm
    29626972U,	// BLSMSK32rr
    33821276U,	// BLSMSK64rm
    29626972U,	// BLSMSK64rr
    25433014U,	// BLSR32rm
    29627318U,	// BLSR32rr
    33821622U,	// BLSR64rm
    29627318U,	// BLSR64rr
    25432350U,	// BOUNDS16rm
    33820958U,	// BOUNDS32rm
    38015498U,	// BSF16rm
    29626890U,	// BSF16rr
    25432586U,	// BSF32rm
    29626890U,	// BSF32rr
    33821194U,	// BSF64rm
    29626890U,	// BSF64rr
    38015921U,	// BSR16rm
    29627313U,	// BSR16rr
    25433009U,	// BSR32rm
    29627313U,	// BSR32rr
    33821617U,	// BSR64rm
    29627313U,	// BSR64rr
    4849U,	// BSWAP32r
    4849U,	// BSWAP64r
    271412U,	// BT16mi8
    271412U,	// BT16mr
    29627444U,	// BT16ri8
    29627444U,	// BT16rr
    279604U,	// BT32mi8
    279604U,	// BT32mr
    29627444U,	// BT32ri8
    29627444U,	// BT32rr
    283700U,	// BT64mi8
    283700U,	// BT64mr
    29627444U,	// BT64ri8
    29627444U,	// BT64rr
    270549U,	// BTC16mi8
    270549U,	// BTC16mr
    29626581U,	// BTC16ri8
    29626581U,	// BTC16rr
    278741U,	// BTC32mi8
    278741U,	// BTC32mr
    29626581U,	// BTC32ri8
    29626581U,	// BTC32rr
    282837U,	// BTC64mi8
    282837U,	// BTC64mr
    29626581U,	// BTC64ri8
    29626581U,	// BTC64rr
    271292U,	// BTR16mi8
    271292U,	// BTR16mr
    29627324U,	// BTR16ri8
    29627324U,	// BTR16rr
    279484U,	// BTR32mi8
    279484U,	// BTR32mr
    29627324U,	// BTR32ri8
    29627324U,	// BTR32rr
    283580U,	// BTR64mi8
    283580U,	// BTR64mr
    29627324U,	// BTR64ri8
    29627324U,	// BTR64rr
    271394U,	// BTS16mi8
    271394U,	// BTS16mr
    29627426U,	// BTS16ri8
    29627426U,	// BTS16rr
    279586U,	// BTS32mi8
    279586U,	// BTS32mr
    29627426U,	// BTS32ri8
    29627426U,	// BTS32rr
    283682U,	// BTS64mi8
    283682U,	// BTS64mr
    29627426U,	// BTS64ri8
    29627426U,	// BTS64rr
    1770263112U,	// BZHI32rm
    700715592U,	// BZHI32rr
    1778651720U,	// BZHI64rm
    700715592U,	// BZHI64rr
    8831U,	// CALL16m
    4735U,	// CALL16r
    17023U,	// CALL32m
    4735U,	// CALL32r
    21119U,	// CALL64m
    29311U,	// CALL64pcrel32
    4735U,	// CALL64r
    29311U,	// CALLpcrel16
    29311U,	// CALLpcrel32
    3563U,	// CBW
    3276U,	// CDQ
    3030U,	// CDQE
    2883U,	// CLAC
    2915U,	// CLC
    2965U,	// CLD
    25739U,	// CLFLUSHOPT
    3121U,	// CLGI
    3131U,	// CLI
    3499U,	// CLTS
    24738U,	// CLWB
    2919U,	// CMC
    8654926U,	// CMOVA16rm
    4460622U,	// CMOVA16rr
    12849230U,	// CMOVA32rm
    4460622U,	// CMOVA32rr
    17043534U,	// CMOVA64rm
    4460622U,	// CMOVA64rr
    8655194U,	// CMOVAE16rm
    4460890U,	// CMOVAE16rr
    12849498U,	// CMOVAE32rm
    4460890U,	// CMOVAE32rr
    17043802U,	// CMOVAE64rm
    4460890U,	// CMOVAE64rr
    8655003U,	// CMOVB16rm
    4460699U,	// CMOVB16rr
    12849307U,	// CMOVB32rm
    4460699U,	// CMOVB32rr
    17043611U,	// CMOVB64rm
    4460699U,	// CMOVB64rr
    8655214U,	// CMOVBE16rm
    4460910U,	// CMOVBE16rr
    12849518U,	// CMOVBE32rm
    4460910U,	// CMOVBE32rr
    17043822U,	// CMOVBE64rm
    4460910U,	// CMOVBE64rr
    8655363U,	// CMOVE16rm
    4461059U,	// CMOVE16rr
    12849667U,	// CMOVE32rm
    4461059U,	// CMOVE32rr
    17043971U,	// CMOVE64rm
    4461059U,	// CMOVE64rr
    8655413U,	// CMOVG16rm
    4461109U,	// CMOVG16rr
    12849717U,	// CMOVG32rm
    4461109U,	// CMOVG32rr
    17044021U,	// CMOVG64rm
    4461109U,	// CMOVG64rr
    8655234U,	// CMOVGE16rm
    4460930U,	// CMOVGE16rr
    12849538U,	// CMOVGE32rm
    4460930U,	// CMOVGE32rr
    17043842U,	// CMOVGE64rm
    4460930U,	// CMOVGE64rr
    8655539U,	// CMOVL16rm
    4461235U,	// CMOVL16rr
    12849843U,	// CMOVL32rm
    4461235U,	// CMOVL32rr
    17044147U,	// CMOVL64rm
    4461235U,	// CMOVL64rr
    8655258U,	// CMOVLE16rm
    4460954U,	// CMOVLE16rr
    12849562U,	// CMOVLE32rm
    4460954U,	// CMOVLE32rr
    17043866U,	// CMOVLE64rm
    4460954U,	// CMOVLE64rr
    8655286U,	// CMOVNE16rm
    4460982U,	// CMOVNE16rr
    12849590U,	// CMOVNE32rm
    4460982U,	// CMOVNE32rr
    17043894U,	// CMOVNE64rm
    4460982U,	// CMOVNE64rr
    8655580U,	// CMOVNO16rm
    4461276U,	// CMOVNO16rr
    12849884U,	// CMOVNO32rm
    4461276U,	// CMOVNO32rr
    17044188U,	// CMOVNO64rm
    4461276U,	// CMOVNO64rr
    8655652U,	// CMOVNP16rm
    4461348U,	// CMOVNP16rr
    12849956U,	// CMOVNP32rm
    4461348U,	// CMOVNP32rr
    17044260U,	// CMOVNP64rm
    4461348U,	// CMOVNP64rr
    8655884U,	// CMOVNS16rm
    4461580U,	// CMOVNS16rr
    12850188U,	// CMOVNS32rm
    4461580U,	// CMOVNS32rr
    17044492U,	// CMOVNS64rm
    4461580U,	// CMOVNS64rr
    8655594U,	// CMOVO16rm
    4461290U,	// CMOVO16rr
    12849898U,	// CMOVO32rm
    4461290U,	// CMOVO32rr
    17044202U,	// CMOVO64rm
    4461290U,	// CMOVO64rr
    8655698U,	// CMOVP16rm
    4461394U,	// CMOVP16rr
    12850002U,	// CMOVP32rm
    4461394U,	// CMOVP32rr
    17044306U,	// CMOVP64rm
    4461394U,	// CMOVP64rr
    8655917U,	// CMOVS16rm
    4461613U,	// CMOVS16rr
    12850221U,	// CMOVS32rm
    4461613U,	// CMOVS32rr
    17044525U,	// CMOVS64rm
    4461613U,	// CMOVS64rr
    2187U,	// CMOV_FR32
    2374U,	// CMOV_FR64
    2394U,	// CMOV_GR16
    2207U,	// CMOV_GR32
    2414U,	// CMOV_GR8
    2166U,	// CMOV_RFP32
    2353U,	// CMOV_RFP64
    2081U,	// CMOV_RFP80
    2123U,	// CMOV_V16F32
    2227U,	// CMOV_V2F64
    2290U,	// CMOV_V2I64
    2102U,	// CMOV_V4F32
    2248U,	// CMOV_V4F64
    2311U,	// CMOV_V4I64
    2145U,	// CMOV_V8F32
    2269U,	// CMOV_V8F64
    2332U,	// CMOV_V8I64
    5616U,	// CMP16i16
    271106U,	// CMP16mi
    271106U,	// CMP16mi8
    271106U,	// CMP16mr
    29627138U,	// CMP16ri
    29627138U,	// CMP16ri8
    38015746U,	// CMP16rm
    29627138U,	// CMP16rr
    29627138U,	// CMP16rr_REV
    5781U,	// CMP32i32
    279298U,	// CMP32mi
    279298U,	// CMP32mi8
    279298U,	// CMP32mr
    29627138U,	// CMP32ri
    29627138U,	// CMP32ri8
    25432834U,	// CMP32rm
    29627138U,	// CMP32rr
    29627138U,	// CMP32rr_REV
    5896U,	// CMP64i32
    283394U,	// CMP64mi32
    283394U,	// CMP64mi8
    283394U,	// CMP64mr
    29627138U,	// CMP64ri32
    29627138U,	// CMP64ri8
    33821442U,	// CMP64rm
    29627138U,	// CMP64rr
    29627138U,	// CMP64rr_REV
    5504U,	// CMP8i8
    287490U,	// CMP8mi
    287490U,	// CMP8mi8
    287490U,	// CMP8mr
    29627138U,	// CMP8ri
    29627138U,	// CMP8ri8
    42210050U,	// CMP8rm
    29627138U,	// CMP8rr
    29627138U,	// CMP8rr_REV
    32898U,	// CMPSB
    37176U,	// CMPSL
    41831U,	// CMPSQ
    46299U,	// CMPSW
    49237U,	// CMPXCHG16B
    270874U,	// CMPXCHG16rm
    29626906U,	// CMPXCHG16rr
    279066U,	// CMPXCHG32rm
    29626906U,	// CMPXCHG32rr
    283162U,	// CMPXCHG64rm
    29626906U,	// CMPXCHG64rr
    20577U,	// CMPXCHG8B
    287258U,	// CMPXCHG8rm
    29626906U,	// CMPXCHG8rr
    2959U,	// CPUID
    3252U,	// CQO
    3006U,	// CWD
    3010U,	// CWDE
    2818U,	// DAA
    3360U,	// DAS
    2708U,	// DATA16_PREFIX
    8365U,	// DEC16m
    4269U,	// DEC16r
    4269U,	// DEC16r_alt
    16557U,	// DEC32m
    4269U,	// DEC32r
    4269U,	// DEC32r_alt
    20653U,	// DEC64m
    4269U,	// DEC64r
    24749U,	// DEC8m
    4269U,	// DEC8r
    9394U,	// DIV16m
    5298U,	// DIV16r
    17586U,	// DIV32m
    5298U,	// DIV32r
    21682U,	// DIV64m
    5298U,	// DIV64r
    25778U,	// DIV8m
    5298U,	// DIV8r
    6018U,	// EH_RETURN
    6018U,	// EH_RETURN64
    2531U,	// EH_SjLj_LongJmp32
    2621U,	// EH_SjLj_LongJmp64
    2550U,	// EH_SjLj_SetJmp32
    2640U,	// EH_SjLj_SetJmp64
    29506U,	// EH_SjLj_Setup
    29627277U,	// ENTER
    537214U,	// FARCALL16i
    53886U,	// FARCALL16m
    537214U,	// FARCALL32i
    53886U,	// FARCALL32m
    53886U,	// FARCALL64
    537362U,	// FARJMP16i
    54034U,	// FARJMP16m
    537362U,	// FARJMP32i
    54034U,	// FARJMP32m
    54034U,	// FARJMP64
    3214U,	// FSETPM
    2903U,	// GETSEC
    3532U,	// HLT
    9393U,	// IDIV16m
    5297U,	// IDIV16r
    17585U,	// IDIV32m
    5297U,	// IDIV32r
    21681U,	// IDIV64m
    5297U,	// IDIV64r
    25777U,	// IDIV8m
    5297U,	// IDIV8r
    8877U,	// IMUL16m
    4781U,	// IMUL16r
    8655533U,	// IMUL16rm
    1782846125U,	// IMUL16rmi
    1782846125U,	// IMUL16rmi8
    4461229U,	// IMUL16rr
    700715693U,	// IMUL16rri
    700715693U,	// IMUL16rri8
    17069U,	// IMUL32m
    4781U,	// IMUL32r
    12849837U,	// IMUL32rm
    1770263213U,	// IMUL32rmi
    1770263213U,	// IMUL32rmi8
    4461229U,	// IMUL32rr
    700715693U,	// IMUL32rri
    700715693U,	// IMUL32rri8
    21165U,	// IMUL64m
    4781U,	// IMUL64r
    17044141U,	// IMUL64rm
    1778651821U,	// IMUL64rmi32
    1778651821U,	// IMUL64rmi8
    4461229U,	// IMUL64rr
    700715693U,	// IMUL64rri32
    700715693U,	// IMUL64rri8
    25261U,	// IMUL8m
    4781U,	// IMUL8r
    5608U,	// IN16ri
    3729U,	// IN16rr
    5772U,	// IN32ri
    3739U,	// IN32rr
    5496U,	// IN8ri
    3719U,	// IN8rr
    8400U,	// INC16m
    4304U,	// INC16r
    4304U,	// INC16r_alt
    16592U,	// INC32m
    4304U,	// INC32r
    4304U,	// INC32r_alt
    20688U,	// INC64m
    4304U,	// INC64r
    24784U,	// INC8m
    4304U,	// INC8r
    843893U,	// INSB
    848171U,	// INSL
    853198U,	// INSW
    5231U,	// INT
    2526U,	// INT1
    2616U,	// INT3
    3256U,	// INTO
    3001U,	// INVD
    46404729U,	// INVEPT32
    46404729U,	// INVEPT64
    25127U,	// INVLPG
    3685U,	// INVLPGA32
    3702U,	// INVLPGA64
    46403829U,	// INVPCID32
    46403829U,	// INVPCID64
    46403838U,	// INVVPID32
    46403838U,	// INVVPID64
    3504U,	// IRET16
    2989U,	// IRET32
    3319U,	// IRET64
    2787U,	// Int_MemBarrier
    29006U,	// JAE_1
    29006U,	// JAE_2
    29006U,	// JAE_4
    28740U,	// JA_1
    28740U,	// JA_2
    28740U,	// JA_4
    29026U,	// JBE_1
    29026U,	// JBE_2
    29026U,	// JBE_4
    28785U,	// JB_1
    28785U,	// JB_2
    28785U,	// JB_4
    29992U,	// JCXZ
    29985U,	// JECXZ
    29066U,	// JE_1
    29066U,	// JE_2
    29066U,	// JE_4
    29046U,	// JGE_1
    29046U,	// JGE_2
    29046U,	// JGE_4
    29219U,	// JG_1
    29219U,	// JG_2
    29219U,	// JG_4
    29070U,	// JLE_1
    29070U,	// JLE_2
    29070U,	// JLE_4
    29306U,	// JL_1
    29306U,	// JL_2
    29306U,	// JL_4
    8973U,	// JMP16m
    4877U,	// JMP16r
    17165U,	// JMP32m
    4877U,	// JMP32r
    21261U,	// JMP64m
    4877U,	// JMP64r
    29453U,	// JMP_1
    29453U,	// JMP_2
    29453U,	// JMP_4
    29090U,	// JNE_1
    29090U,	// JNE_2
    29090U,	// JNE_4
    29392U,	// JNO_1
    29392U,	// JNO_2
    29392U,	// JNO_4
    29464U,	// JNP_1
    29464U,	// JNP_2
    29464U,	// JNP_4
    29696U,	// JNS_1
    29696U,	// JNS_2
    29696U,	// JNS_4
    29388U,	// JO_1
    29388U,	// JO_2
    29388U,	// JO_4
    29438U,	// JP_1
    29438U,	// JP_2
    29438U,	// JP_4
    29998U,	// JRCXZ
    29692U,	// JS_1
    29692U,	// JS_2
    29692U,	// JS_4
    3086U,	// LAHF
    38015870U,	// LAR16rm
    29627262U,	// LAR16rr
    38015870U,	// LAR32rm
    29627262U,	// LAR32rr
    38015870U,	// LAR64rm
    29627262U,	// LAR64rr
    270874U,	// LCMPXCHG16
    49237U,	// LCMPXCHG16B
    279066U,	// LCMPXCHG32
    283162U,	// LCMPXCHG64
    287258U,	// LCMPXCHG8
    20577U,	// LCMPXCHG8B
    50598880U,	// LDS16rm
    50598880U,	// LDS32rm
    54792255U,	// LEA16r
    54792255U,	// LEA32r
    54792255U,	// LEA64_32r
    54792255U,	// LEA64r
    3073U,	// LEAVE
    3073U,	// LEAVE64
    50598885U,	// LES16rm
    50598885U,	// LES32rm
    50598898U,	// LFS16rm
    50598898U,	// LFS32rm
    50598898U,	// LFS64rm
    54328U,	// LGDT16m
    54328U,	// LGDT32m
    54328U,	// LGDT64m
    50598903U,	// LGS16rm
    50598903U,	// LGS32rm
    50598903U,	// LGS64rm
    54340U,	// LIDT16m
    54340U,	// LIDT32m
    54340U,	// LIDT64m
    9296U,	// LLDT16m
    5200U,	// LLDT16r
    9410U,	// LMSW16m
    5314U,	// LMSW16r
    270568U,	// LOCK_ADD16mi
    270568U,	// LOCK_ADD16mi8
    270568U,	// LOCK_ADD16mr
    278760U,	// LOCK_ADD32mi
    278760U,	// LOCK_ADD32mi8
    278760U,	// LOCK_ADD32mr
    282856U,	// LOCK_ADD64mi32
    282856U,	// LOCK_ADD64mi8
    282856U,	// LOCK_ADD64mr
    286952U,	// LOCK_ADD8mi
    286952U,	// LOCK_ADD8mr
    270617U,	// LOCK_AND16mi
    270617U,	// LOCK_AND16mi8
    270617U,	// LOCK_AND16mr
    278809U,	// LOCK_AND32mi
    278809U,	// LOCK_AND32mi8
    278809U,	// LOCK_AND32mr
    282905U,	// LOCK_AND64mi32
    282905U,	// LOCK_AND64mi8
    282905U,	// LOCK_AND64mr
    287001U,	// LOCK_AND8mi
    287001U,	// LOCK_AND8mr
    8365U,	// LOCK_DEC16m
    16557U,	// LOCK_DEC32m
    20653U,	// LOCK_DEC64m
    24749U,	// LOCK_DEC8m
    8400U,	// LOCK_INC16m
    16592U,	// LOCK_INC32m
    20688U,	// LOCK_INC64m
    24784U,	// LOCK_INC8m
    271258U,	// LOCK_OR16mi
    271258U,	// LOCK_OR16mi8
    271258U,	// LOCK_OR16mr
    279450U,	// LOCK_OR32mi
    279450U,	// LOCK_OR32mi8
    279450U,	// LOCK_OR32mr
    283546U,	// LOCK_OR64mi32
    283546U,	// LOCK_OR64mi8
    283546U,	// LOCK_OR64mr
    287642U,	// LOCK_OR8mi
    287642U,	// LOCK_OR8mr
    3154U,	// LOCK_PREFIX
    270486U,	// LOCK_SUB16mi
    270486U,	// LOCK_SUB16mi8
    270486U,	// LOCK_SUB16mr
    278678U,	// LOCK_SUB32mi
    278678U,	// LOCK_SUB32mi8
    278678U,	// LOCK_SUB32mr
    282774U,	// LOCK_SUB64mi32
    282774U,	// LOCK_SUB64mi8
    282774U,	// LOCK_SUB64mr
    286870U,	// LOCK_SUB8mi
    286870U,	// LOCK_SUB8mr
    271270U,	// LOCK_XOR16mi
    271270U,	// LOCK_XOR16mi8
    271270U,	// LOCK_XOR16mr
    279462U,	// LOCK_XOR32mi
    279462U,	// LOCK_XOR32mi8
    279462U,	// LOCK_XOR32mr
    283558U,	// LOCK_XOR64mi32
    283558U,	// LOCK_XOR64mi8
    283558U,	// LOCK_XOR64mr
    287654U,	// LOCK_XOR8mi
    287654U,	// LOCK_XOR8mr
    70985U,	// LODSB
    75381U,	// LODSL
    79646U,	// LODSQ
    83500U,	// LODSW
    29489U,	// LOOP
    29118U,	// LOOPE
    29095U,	// LOOPNE
    4623U,	// LRETIL
    4953U,	// LRETIQ
    4623U,	// LRETIW
    3107U,	// LRETL
    3293U,	// LRETQ
    3107U,	// LRETW
    38015650U,	// LSL16rm
    29627042U,	// LSL16rr
    25432738U,	// LSL32rm
    29627042U,	// LSL32rr
    33821346U,	// LSL64rm
    29627042U,	// LSL64rr
    50598941U,	// LSS16rm
    50598941U,	// LSS32rm
    50598941U,	// LSS64rm
    9153U,	// LTRm
    5057U,	// LTRr
    86247U,	// LXADD16
    90343U,	// LXADD32
    94439U,	// LXADD64
    98535U,	// LXADD8
    38016097U,	// LZCNT16rm
    29627489U,	// LZCNT16rr
    25433185U,	// LZCNT32rm
    29627489U,	// LZCNT32rr
    33821793U,	// LZCNT64rm
    29627489U,	// LZCNT64rr
    3206U,	// MONTMUL
    0U,	// MORESTACK_RET
    0U,	// MORESTACK_RET_RESTORE_R10
    103960U,	// MOV16ao16
    103960U,	// MOV16ao32
    103938U,	// MOV16ao64
    271543U,	// MOV16mi
    271543U,	// MOV16mr
    271543U,	// MOV16ms
    1152183U,	// MOV16o16a
    1152183U,	// MOV16o32a
    1151954U,	// MOV16o64a
    29627575U,	// MOV16ri
    29627575U,	// MOV16ri_alt
    38016183U,	// MOV16rm
    29627575U,	// MOV16rr
    29627575U,	// MOV16rr_REV
    29627575U,	// MOV16rs
    38016183U,	// MOV16sm
    29627575U,	// MOV16sr
    108225U,	// MOV32ao16
    108225U,	// MOV32ao32
    108201U,	// MOV32ao64
    29627575U,	// MOV32cr
    29627575U,	// MOV32dr
    279735U,	// MOV32mi
    279735U,	// MOV32mr
    271543U,	// MOV32ms
    1418423U,	// MOV32o16a
    1418423U,	// MOV32o32a
    1418194U,	// MOV32o64a
    0U,	// MOV32r0
    29627575U,	// MOV32rc
    29627575U,	// MOV32rd
    29627575U,	// MOV32ri
    0U,	// MOV32ri64
    29627575U,	// MOV32ri_alt
    25433271U,	// MOV32rm
    29627575U,	// MOV32rr
    29627575U,	// MOV32rr_REV
    29627575U,	// MOV32rs
    38016183U,	// MOV32sm
    29627575U,	// MOV32sr
    112460U,	// MOV64ao32
    112436U,	// MOV64ao64
    29627575U,	// MOV64cr
    29627575U,	// MOV64dr
    283831U,	// MOV64mi32
    283831U,	// MOV64mr
    271543U,	// MOV64ms
    1684663U,	// MOV64o32a
    1684434U,	// MOV64o64a
    29627575U,	// MOV64rc
    29627575U,	// MOV64rd
    29627346U,	// MOV64ri
    29627575U,	// MOV64ri32
    33821879U,	// MOV64rm
    29627575U,	// MOV64rr
    29627575U,	// MOV64rr_REV
    29627575U,	// MOV64rs
    38016183U,	// MOV64sm
    29627575U,	// MOV64sr
    116136U,	// MOV8ao16
    116136U,	// MOV8ao32
    116114U,	// MOV8ao64
    287927U,	// MOV8mi
    287927U,	// MOV8mr
    287927U,	// MOV8mr_NOREX
    1950903U,	// MOV8o16a
    1950903U,	// MOV8o32a
    1950674U,	// MOV8o64a
    29627575U,	// MOV8ri
    29627575U,	// MOV8ri_alt
    42210487U,	// MOV8rm
    42210487U,	// MOV8rm_NOREX
    29627575U,	// MOV8rr
    29627575U,	// MOV8rr_NOREX
    29627575U,	// MOV8rr_REV
    270703U,	// MOVBE16mr
    38015343U,	// MOVBE16rm
    278895U,	// MOVBE32mr
    25432431U,	// MOVBE32rm
    282991U,	// MOVBE64mr
    33821039U,	// MOVBE64rm
    0U,	// MOVPC32r
    59039881U,	// MOVSB
    63238463U,	// MOVSL
    67490670U,	// MOVSQ
    71632098U,	// MOVSW
    42210579U,	// MOVSX16rm8
    29627667U,	// MOVSX16rr8
    42210579U,	// MOVSX32_NOREXrm8
    29627667U,	// MOVSX32_NOREXrr8
    38016275U,	// MOVSX32rm16
    42210579U,	// MOVSX32rm8
    29627667U,	// MOVSX32rr16
    29627667U,	// MOVSX32rr8
    29626694U,	// MOVSX64_NOREXrr32
    38016275U,	// MOVSX64rm16
    25432390U,	// MOVSX64rm32
    25432390U,	// MOVSX64rm32_alt
    42210579U,	// MOVSX64rm8
    29627667U,	// MOVSX64rr16
    29626694U,	// MOVSX64rr32
    29627667U,	// MOVSX64rr8
    42210586U,	// MOVZX16rm8
    29627674U,	// MOVZX16rr8
    42210586U,	// MOVZX32_NOREXrm8
    29627674U,	// MOVZX32_NOREXrr8
    38016282U,	// MOVZX32rm16
    42210586U,	// MOVZX32rm8
    29627674U,	// MOVZX32rr16
    29627674U,	// MOVZX32rr8
    38016282U,	// MOVZX64rm16_Q
    42210586U,	// MOVZX64rm8_Q
    29627674U,	// MOVZX64rr16_Q
    29627674U,	// MOVZX64rr8_Q
    8878U,	// MUL16m
    4782U,	// MUL16r
    17070U,	// MUL32m
    4782U,	// MUL32r
    21166U,	// MUL64m
    4782U,	// MUL64r
    25262U,	// MUL8m
    4782U,	// MUL8r
    163845365U,	// MULX32rm
    700716277U,	// MULX32rr
    1237587189U,	// MULX64rm
    700716277U,	// MULX64rr
    8725U,	// NEG16m
    4629U,	// NEG16r
    16917U,	// NEG32m
    4629U,	// NEG32r
    21013U,	// NEG64m
    4629U,	// NEG64r
    25109U,	// NEG8m
    4629U,	// NEG8r
    3272U,	// NOOP
    9004U,	// NOOP18_16m4
    9004U,	// NOOP18_16m5
    9004U,	// NOOP18_16m6
    9004U,	// NOOP18_16m7
    4908U,	// NOOP18_16r4
    4908U,	// NOOP18_16r5
    4908U,	// NOOP18_16r6
    4908U,	// NOOP18_16r7
    17196U,	// NOOP18_m4
    17196U,	// NOOP18_m5
    17196U,	// NOOP18_m6
    17196U,	// NOOP18_m7
    4908U,	// NOOP18_r4
    4908U,	// NOOP18_r5
    4908U,	// NOOP18_r6
    4908U,	// NOOP18_r7
    75772716U,	// NOOP19rr
    17196U,	// NOOPL
    17196U,	// NOOPL_19
    17196U,	// NOOPL_1a
    17196U,	// NOOPL_1b
    17196U,	// NOOPL_1c
    17196U,	// NOOPL_1d
    17196U,	// NOOPL_1e
    9004U,	// NOOPW
    9004U,	// NOOPW_19
    9004U,	// NOOPW_1a
    9004U,	// NOOPW_1b
    9004U,	// NOOPW_1c
    9004U,	// NOOPW_1d
    9004U,	// NOOPW_1e
    9332U,	// NOT16m
    5236U,	// NOT16r
    17524U,	// NOT32m
    5236U,	// NOT32r
    21620U,	// NOT64m
    5236U,	// NOT64r
    25716U,	// NOT8m
    5236U,	// NOT8r
    5626U,	// OR16i16
    271258U,	// OR16mi
    271258U,	// OR16mi8
    271258U,	// OR16mr
    4469658U,	// OR16ri
    4469658U,	// OR16ri8
    8663962U,	// OR16rm
    4469658U,	// OR16rr
    4461466U,	// OR16rr_REV
    5792U,	// OR32i32
    279450U,	// OR32mi
    279450U,	// OR32mi8
    279450U,	// OR32mr
    279450U,	// OR32mrLocked
    4469658U,	// OR32ri
    4469658U,	// OR32ri8
    12858266U,	// OR32rm
    4469658U,	// OR32rr
    4461466U,	// OR32rr_REV
    5931U,	// OR64i32
    283546U,	// OR64mi32
    283546U,	// OR64mi8
    283546U,	// OR64mr
    4469658U,	// OR64ri32
    4469658U,	// OR64ri8
    17052570U,	// OR64rm
    4469658U,	// OR64rr
    4461466U,	// OR64rr_REV
    5514U,	// OR8i8
    287642U,	// OR8mi
    287642U,	// OR8mi8
    287642U,	// OR8mr
    4469658U,	// OR8ri
    4469658U,	// OR8ri8
    21246874U,	// OR8rm
    4469658U,	// OR8rr
    4461466U,	// OR8rr_REV
    1053862U,	// OUT16ir
    3587U,	// OUT16rr
    1316006U,	// OUT32ir
    3641U,	// OUT32rr
    1840294U,	// OUT8ir
    3159U,	// OUT8rr
    71510U,	// OUTSB
    75617U,	// OUTSL
    83820U,	// OUTSW
    3516U,	// PCOMMIT
    163844856U,	// PDEP32rm
    700715768U,	// PDEP32rr
    1237586680U,	// PDEP64rm
    700715768U,	// PDEP64rr
    163845291U,	// PEXT32rm
    700716203U,	// PEXT32rr
    1237587115U,	// PEXT64rm
    700716203U,	// PEXT64rr
    4919U,	// POP16r
    9015U,	// POP16rmm
    4919U,	// POP16rmr
    4919U,	// POP32r
    17207U,	// POP32rmm
    4919U,	// POP32rmr
    4919U,	// POP64r
    21303U,	// POP64rmm
    4919U,	// POP64rmr
    3557U,	// POPA16
    3177U,	// POPA32
    3380U,	// POPDS16
    3380U,	// POPDS32
    3395U,	// POPES16
    3395U,	// POPES32
    3102U,	// POPF16
    2953U,	// POPF32
    3287U,	// POPF64
    3410U,	// POPFS16
    3410U,	// POPFS32
    3410U,	// POPFS64
    3425U,	// POPGS16
    3425U,	// POPGS32
    3425U,	// POPGS64
    3492U,	// POPSS16
    3492U,	// POPSS32
    4668U,	// PUSH16i8
    4668U,	// PUSH16r
    8764U,	// PUSH16rmm
    4668U,	// PUSH16rmr
    4668U,	// PUSH32i8
    4668U,	// PUSH32r
    16956U,	// PUSH32rmm
    4668U,	// PUSH32rmr
    4668U,	// PUSH64i16
    4668U,	// PUSH64i32
    4668U,	// PUSH64i8
    4668U,	// PUSH64r
    21052U,	// PUSH64rmm
    4668U,	// PUSH64rmr
    3550U,	// PUSHA16
    3170U,	// PUSHA32
    3364U,	// PUSHCS16
    3364U,	// PUSHCS32
    3372U,	// PUSHDS16
    3372U,	// PUSHDS32
    3387U,	// PUSHES16
    3387U,	// PUSHES32
    3096U,	// PUSHF16
    2946U,	// PUSHF32
    3280U,	// PUSHF64
    3402U,	// PUSHFS16
    3402U,	// PUSHFS32
    3402U,	// PUSHFS64
    3417U,	// PUSHGS16
    3417U,	// PUSHGS32
    3417U,	// PUSHGS64
    3484U,	// PUSHSS16
    3484U,	// PUSHSS32
    4668U,	// PUSHi16
    4668U,	// PUSHi32
    2105968U,	// RCL16m1
    2368112U,	// RCL16mCL
    270960U,	// RCL16mi
    2101872U,	// RCL16r1
    2364016U,	// RCL16rCL
    4461168U,	// RCL16ri
    2114160U,	// RCL32m1
    2376304U,	// RCL32mCL
    279152U,	// RCL32mi
    2101872U,	// RCL32r1
    2364016U,	// RCL32rCL
    4461168U,	// RCL32ri
    2118256U,	// RCL64m1
    2380400U,	// RCL64mCL
    283248U,	// RCL64mi
    2101872U,	// RCL64r1
    2364016U,	// RCL64rCL
    4461168U,	// RCL64ri
    2122352U,	// RCL8m1
    2384496U,	// RCL8mCL
    287344U,	// RCL8mi
    2101872U,	// RCL8r1
    2364016U,	// RCL8rCL
    4461168U,	// RCL8ri
    2106248U,	// RCR16m1
    2368392U,	// RCR16mCL
    271240U,	// RCR16mi
    2102152U,	// RCR16r1
    2364296U,	// RCR16rCL
    4461448U,	// RCR16ri
    2114440U,	// RCR32m1
    2376584U,	// RCR32mCL
    279432U,	// RCR32mi
    2102152U,	// RCR32r1
    2364296U,	// RCR32rCL
    4461448U,	// RCR32ri
    2118536U,	// RCR64m1
    2380680U,	// RCR64mCL
    283528U,	// RCR64mi
    2102152U,	// RCR64r1
    2364296U,	// RCR64rCL
    4461448U,	// RCR64ri
    2122632U,	// RCR8m1
    2384776U,	// RCR8mCL
    287624U,	// RCR8mi
    2102152U,	// RCR8r1
    2364296U,	// RCR8rCL
    4461448U,	// RCR8ri
    4549U,	// RDFSBASE
    4549U,	// RDFSBASE64
    4569U,	// RDGSBASE
    4569U,	// RDGSBASE64
    3334U,	// RDMSR
    2923U,	// RDPMC
    4374U,	// RDRAND16r
    4374U,	// RDRAND32r
    4374U,	// RDRAND64r
    4333U,	// RDSEED16r
    4333U,	// RDSEED32r
    4333U,	// RDSEED64r
    2936U,	// RDTSC
    3261U,	// RDTSCP
    2433U,	// RELEASE_ADD32mi
    2433U,	// RELEASE_ADD64mi32
    2433U,	// RELEASE_ADD8mi
    2433U,	// RELEASE_AND32mi
    2433U,	// RELEASE_AND64mi32
    2433U,	// RELEASE_AND8mi
    2456U,	// RELEASE_DEC16m
    2456U,	// RELEASE_DEC32m
    2456U,	// RELEASE_DEC64m
    2456U,	// RELEASE_DEC8m
    2456U,	// RELEASE_INC16m
    2456U,	// RELEASE_INC32m
    2456U,	// RELEASE_INC64m
    2456U,	// RELEASE_INC8m
    2059U,	// RELEASE_MOV16mi
    2499U,	// RELEASE_MOV16mr
    2059U,	// RELEASE_MOV32mi
    2499U,	// RELEASE_MOV32mr
    2059U,	// RELEASE_MOV64mi32
    2499U,	// RELEASE_MOV64mr
    2059U,	// RELEASE_MOV8mi
    2499U,	// RELEASE_MOV8mr
    2433U,	// RELEASE_OR32mi
    2433U,	// RELEASE_OR64mi32
    2433U,	// RELEASE_OR8mi
    2433U,	// RELEASE_XOR32mi
    2433U,	// RELEASE_XOR64mi32
    2433U,	// RELEASE_XOR8mi
    3024U,	// REPNE_PREFIX
    2867U,	// REP_MOVSB_32
    2867U,	// REP_MOVSB_64
    2979U,	// REP_MOVSD_32
    2979U,	// REP_MOVSD_64
    3309U,	// REP_MOVSQ_64
    3577U,	// REP_MOVSW_32
    3577U,	// REP_MOVSW_64
    3268U,	// REP_PREFIX
    2857U,	// REP_STOSB_32
    2857U,	// REP_STOSB_64
    2969U,	// REP_STOSD_32
    2969U,	// REP_STOSD_64
    3299U,	// REP_STOSQ_64
    3567U,	// REP_STOSW_32
    3567U,	// REP_STOSW_64
    5212U,	// RETIL
    5212U,	// RETIQ
    5212U,	// RETIW
    3505U,	// RETL
    3505U,	// RETQ
    3505U,	// RETW
    2702U,	// REX64_PREFIX
    2106007U,	// ROL16m1
    2368151U,	// ROL16mCL
    270999U,	// ROL16mi
    2101911U,	// ROL16r1
    2364055U,	// ROL16rCL
    4461207U,	// ROL16ri
    2114199U,	// ROL32m1
    2376343U,	// ROL32mCL
    279191U,	// ROL32mi
    2101911U,	// ROL32r1
    2364055U,	// ROL32rCL
    4461207U,	// ROL32ri
    2118295U,	// ROL64m1
    2380439U,	// ROL64mCL
    283287U,	// ROL64mi
    2101911U,	// ROL64r1
    2364055U,	// ROL64rCL
    4461207U,	// ROL64ri
    2122391U,	// ROL8m1
    2384535U,	// ROL8mCL
    287383U,	// ROL8mi
    2101911U,	// ROL8r1
    2364055U,	// ROL8rCL
    4461207U,	// ROL8ri
    2106265U,	// ROR16m1
    2368409U,	// ROR16mCL
    271257U,	// ROR16mi
    2102169U,	// ROR16r1
    2364313U,	// ROR16rCL
    4461465U,	// ROR16ri
    2114457U,	// ROR32m1
    2376601U,	// ROR32mCL
    279449U,	// ROR32mi
    2102169U,	// ROR32r1
    2364313U,	// ROR32rCL
    4461465U,	// ROR32ri
    2118553U,	// ROR64m1
    2380697U,	// ROR64mCL
    283545U,	// ROR64mi
    2102169U,	// ROR64r1
    2364313U,	// ROR64rCL
    4461465U,	// ROR64ri
    2122649U,	// ROR8m1
    2384793U,	// ROR8mCL
    287641U,	// ROR8mi
    2102169U,	// ROR8r1
    2364313U,	// ROR8rCL
    4461465U,	// ROR8ri
    1770263821U,	// RORX32mi
    700716301U,	// RORX32ri
    1778652429U,	// RORX64mi
    700716301U,	// RORX64ri
    3221U,	// RSM
    3091U,	// SAHF
    2105963U,	// SAL16m1
    2368107U,	// SAL16mCL
    270955U,	// SAL16mi
    2101867U,	// SAL16r1
    2364011U,	// SAL16rCL
    4461163U,	// SAL16ri
    2114155U,	// SAL32m1
    2376299U,	// SAL32mCL
    279147U,	// SAL32mi
    2101867U,	// SAL32r1
    2364011U,	// SAL32rCL
    4461163U,	// SAL32ri
    2118251U,	// SAL64m1
    2380395U,	// SAL64mCL
    283243U,	// SAL64mi
    2101867U,	// SAL64r1
    2364011U,	// SAL64rCL
    4461163U,	// SAL64ri
    2122347U,	// SAL8m1
    2384491U,	// SAL8mCL
    287339U,	// SAL8mi
    2101867U,	// SAL8r1
    2364011U,	// SAL8rCL
    4461163U,	// SAL8ri
    2910U,	// SALC
    2106243U,	// SAR16m1
    2368387U,	// SAR16mCL
    271235U,	// SAR16mi
    2102147U,	// SAR16r1
    2364291U,	// SAR16rCL
    4461443U,	// SAR16ri
    2114435U,	// SAR32m1
    2376579U,	// SAR32mCL
    279427U,	// SAR32mi
    2102147U,	// SAR32r1
    2364291U,	// SAR32rCL
    4461443U,	// SAR32ri
    2118531U,	// SAR64m1
    2380675U,	// SAR64mCL
    283523U,	// SAR64mi
    2102147U,	// SAR64r1
    2364291U,	// SAR64rCL
    4461443U,	// SAR64ri
    2122627U,	// SAR8m1
    2384771U,	// SAR8mCL
    287619U,	// SAR8mi
    2102147U,	// SAR8r1
    2364291U,	// SAR8rCL
    4461443U,	// SAR8ri
    1770263809U,	// SARX32rm
    700716289U,	// SARX32rr
    1778652417U,	// SARX64rm
    700716289U,	// SARX64rr
    5553U,	// SBB16i16
    270444U,	// SBB16mi
    270444U,	// SBB16mi8
    270444U,	// SBB16mr
    4468844U,	// SBB16ri
    4468844U,	// SBB16ri8
    8663148U,	// SBB16rm
    4468844U,	// SBB16rr
    4460652U,	// SBB16rr_REV
    5687U,	// SBB32i32
    278636U,	// SBB32mi
    278636U,	// SBB32mi8
    278636U,	// SBB32mr
    4468844U,	// SBB32ri
    4468844U,	// SBB32ri8
    12857452U,	// SBB32rm
    4468844U,	// SBB32rr
    4460652U,	// SBB32rr_REV
    5835U,	// SBB64i32
    282732U,	// SBB64mi32
    282732U,	// SBB64mi8
    282732U,	// SBB64mr
    4468844U,	// SBB64ri32
    4468844U,	// SBB64ri8
    17051756U,	// SBB64rm
    4468844U,	// SBB64rr
    4460652U,	// SBB64rr_REV
    5429U,	// SBB8i8
    286828U,	// SBB8mi
    286828U,	// SBB8mi8
    286828U,	// SBB8mr
    4468844U,	// SBB8ri
    4468844U,	// SBB8ri8
    21246060U,	// SBB8rm
    4468844U,	// SBB8rr
    4460652U,	// SBB8rr_REV
    58686U,	// SCASB
    63081U,	// SCASL
    120594U,	// SCASQ
    67105U,	// SCASW
    3439U,	// SEG_ALLOCA_32
    3439U,	// SEG_ALLOCA_64
    3056U,	// SEH_EndPrologue
    3042U,	// SEH_Epilogue
    6096U,	// SEH_PushFrame
    6141U,	// SEH_PushReg
    29628399U,	// SEH_SaveReg
    29628313U,	// SEH_SaveXMM
    29628384U,	// SEH_SetFrame
    6079U,	// SEH_StackAlloc
    24915U,	// SETAEm
    4435U,	// SETAEr
    24648U,	// SETAm
    4168U,	// SETAr
    24935U,	// SETBEm
    4455U,	// SETBEr
    0U,	// SETB_C16r
    0U,	// SETB_C32r
    0U,	// SETB_C64r
    0U,	// SETB_C8r
    24720U,	// SETBm
    4240U,	// SETBr
    25069U,	// SETEm
    4589U,	// SETEr
    24955U,	// SETGEm
    4475U,	// SETGEr
    25135U,	// SETGm
    4655U,	// SETGr
    24979U,	// SETLEm
    4499U,	// SETLEr
    25255U,	// SETLm
    4775U,	// SETLr
    25007U,	// SETNEm
    4527U,	// SETNEr
    25301U,	// SETNOm
    4821U,	// SETNOr
    25373U,	// SETNPm
    4893U,	// SETNPr
    25605U,	// SETNSm
    5125U,	// SETNSr
    25316U,	// SETOm
    4836U,	// SETOr
    25404U,	// SETPm
    4924U,	// SETPr
    25639U,	// SETSm
    5159U,	// SETSr
    54334U,	// SGDT16m
    54334U,	// SGDT32m
    54334U,	// SGDT64m
    2105973U,	// SHL16m1
    2368117U,	// SHL16mCL
    270965U,	// SHL16mi
    2101877U,	// SHL16r1
    2364021U,	// SHL16rCL
    4461173U,	// SHL16ri
    2114165U,	// SHL32m1
    2376309U,	// SHL32mCL
    279157U,	// SHL32mi
    2101877U,	// SHL32r1
    2364021U,	// SHL32rCL
    4461173U,	// SHL32ri
    2118261U,	// SHL64m1
    2380405U,	// SHL64mCL
    283253U,	// SHL64mi
    2101877U,	// SHL64r1
    2364021U,	// SHL64rCL
    4461173U,	// SHL64ri
    2122357U,	// SHL8m1
    2384501U,	// SHL8mCL
    287349U,	// SHL8mi
    2101877U,	// SHL8r1
    2364021U,	// SHL8rCL
    4461173U,	// SHL8ri
    268706055U,	// SHLD16mrCL
    1745101063U,	// SHLD16mri8
    272896263U,	// SHLD16rrCL
    2286162183U,	// SHLD16rri8
    268714247U,	// SHLD32mrCL
    1745109255U,	// SHLD32mri8
    272896263U,	// SHLD32rrCL
    2286162183U,	// SHLD32rri8
    268718343U,	// SHLD64mrCL
    1745113351U,	// SHLD64mri8
    272896263U,	// SHLD64rrCL
    2286162183U,	// SHLD64rri8
    1770263791U,	// SHLX32rm
    700716271U,	// SHLX32rr
    1778652399U,	// SHLX64rm
    700716271U,	// SHLX64rr
    2106260U,	// SHR16m1
    2368404U,	// SHR16mCL
    271252U,	// SHR16mi
    2102164U,	// SHR16r1
    2364308U,	// SHR16rCL
    4461460U,	// SHR16ri
    2114452U,	// SHR32m1
    2376596U,	// SHR32mCL
    279444U,	// SHR32mi
    2102164U,	// SHR32r1
    2364308U,	// SHR32rCL
    4461460U,	// SHR32ri
    2118548U,	// SHR64m1
    2380692U,	// SHR64mCL
    283540U,	// SHR64mi
    2102164U,	// SHR64r1
    2364308U,	// SHR64rCL
    4461460U,	// SHR64ri
    2122644U,	// SHR8m1
    2384788U,	// SHR8mCL
    287636U,	// SHR8mi
    2102164U,	// SHR8r1
    2364308U,	// SHR8rCL
    4461460U,	// SHR8ri
    268706085U,	// SHRD16mrCL
    1745101093U,	// SHRD16mri8
    272896293U,	// SHRD16rrCL
    2286162213U,	// SHRD16rri8
    268714277U,	// SHRD32mrCL
    1745109285U,	// SHRD32mri8
    272896293U,	// SHRD32rrCL
    2286162213U,	// SHRD32rri8
    268718373U,	// SHRD64mrCL
    1745113381U,	// SHRD64mri8
    272896293U,	// SHRD64rrCL
    2286162213U,	// SHRD64rri8
    1770263815U,	// SHRX32rm
    700716295U,	// SHRX32rr
    1778652423U,	// SHRX64rm
    700716295U,	// SHRX64rr
    54346U,	// SIDT16m
    54346U,	// SIDT32m
    54346U,	// SIDT64m
    3630U,	// SKINIT
    9302U,	// SLDT16m
    5206U,	// SLDT16r
    5206U,	// SLDT32r
    9302U,	// SLDT64m
    5206U,	// SLDT64r
    9416U,	// SMSW16m
    5320U,	// SMSW16r
    5320U,	// SMSW32r
    5320U,	// SMSW64r
    2888U,	// STAC
    2942U,	// STC
    2995U,	// STD
    3126U,	// STGI
    3135U,	// STI
    1892475U,	// STOSB
    1372465U,	// STOSL
    1692512U,	// STOSQ
    1115348U,	// STOSW
    5062U,	// STR16r
    5062U,	// STR32r
    5062U,	// STR64r
    9158U,	// STRm
    5562U,	// SUB16i16
    270486U,	// SUB16mi
    270486U,	// SUB16mi8
    270486U,	// SUB16mr
    4468886U,	// SUB16ri
    4468886U,	// SUB16ri8
    8663190U,	// SUB16rm
    4468886U,	// SUB16rr
    4460694U,	// SUB16rr_REV
    5697U,	// SUB32i32
    278678U,	// SUB32mi
    278678U,	// SUB32mi8
    278678U,	// SUB32mr
    4468886U,	// SUB32ri
    4468886U,	// SUB32ri8
    12857494U,	// SUB32rm
    4468886U,	// SUB32rr
    4460694U,	// SUB32rr_REV
    5845U,	// SUB64i32
    282774U,	// SUB64mi32
    282774U,	// SUB64mi8
    282774U,	// SUB64mr
    4468886U,	// SUB64ri32
    4468886U,	// SUB64ri8
    17051798U,	// SUB64rm
    4468886U,	// SUB64rr
    4460694U,	// SUB64rr_REV
    5460U,	// SUB8i8
    286870U,	// SUB8mi
    286870U,	// SUB8mi8
    286870U,	// SUB8mr
    4468886U,	// SUB8ri
    4468886U,	// SUB8ri8
    21246102U,	// SUB8rm
    4468886U,	// SUB8rr
    4460694U,	// SUB8rr_REV
    3432U,	// SWAPGS
    3198U,	// SYSCALL
    3325U,	// SYSENTER
    3524U,	// SYSEXIT
    3524U,	// SYSEXIT64
    3509U,	// SYSRET
    3509U,	// SYSRET64
    25432264U,	// T1MSKC32rm
    29626568U,	// T1MSKC32rr
    33820872U,	// T1MSKC64rm
    29626568U,	// T1MSKC64rr
    29453U,	// TAILJMPd
    29453U,	// TAILJMPd64
    29447U,	// TAILJMPd64_REX
    17165U,	// TAILJMPm
    21261U,	// TAILJMPm64
    21255U,	// TAILJMPm64_REX
    0U,	// TAILJMPr
    4877U,	// TAILJMPr64
    4871U,	// TAILJMPr64_REX
    0U,	// TCRETURNdi
    0U,	// TCRETURNdi64
    0U,	// TCRETURNmi
    0U,	// TCRETURNmi64
    0U,	// TCRETURNri
    0U,	// TCRETURNri64
    5646U,	// TEST16i16
    271511U,	// TEST16mi
    271511U,	// TEST16mi_alt
    29627543U,	// TEST16ri
    29627543U,	// TEST16ri_alt
    124055U,	// TEST16rm
    29627543U,	// TEST16rr
    5814U,	// TEST32i32
    279703U,	// TEST32mi
    279703U,	// TEST32mi_alt
    29627543U,	// TEST32ri
    29627543U,	// TEST32ri_alt
    128151U,	// TEST32rm
    29627543U,	// TEST32rr
    5953U,	// TEST64i32
    283799U,	// TEST64mi32
    283799U,	// TEST64mi32_alt
    29627543U,	// TEST64ri32
    29627543U,	// TEST64ri32_alt
    132247U,	// TEST64rm
    29627543U,	// TEST64rr
    5534U,	// TEST8i8
    287895U,	// TEST8mi
    287895U,	// TEST8mi_alt
    29627543U,	// TEST8ri
    0U,	// TEST8ri_NOREX
    29627543U,	// TEST8ri_alt
    136343U,	// TEST8rm
    29627543U,	// TEST8rr
    2568U,	// TLSCall_32
    2658U,	// TLSCall_64
    2581U,	// TLS_addr32
    2671U,	// TLS_addr64
    2594U,	// TLS_base_addr32
    2684U,	// TLS_base_addr64
    2612U,	// TRAP
    38016104U,	// TZCNT16rm
    29627496U,	// TZCNT16rr
    25433192U,	// TZCNT32rm
    29627496U,	// TZCNT32rr
    33821800U,	// TZCNT64rm
    29627496U,	// TZCNT64rr
    25432676U,	// TZMSK32rm
    29626980U,	// TZMSK32rr
    33821284U,	// TZMSK64rm
    29626980U,	// TZMSK64rr
    2822U,	// UD2B
    1787041655U,	// VAARG_64
    700716967U,	// VASTART_SAVE_XMM_REGS
    9131U,	// VERRm
    5035U,	// VERRr
    9404U,	// VERWm
    5308U,	// VERWr
    3191U,	// VMCALL
    21365U,	// VMCLEARm
    2929U,	// VMFUNC
    3112U,	// VMLAUNCH
    3598U,	// VMLOAD32
    3653U,	// VMLOAD64
    3183U,	// VMMCALL
    20749U,	// VMPTRLDm
    21661U,	// VMPTRSTm
    278751U,	// VMREAD32rm
    29626591U,	// VMREAD32rr
    282847U,	// VMREAD64rm
    29626591U,	// VMREAD64rr
    3015U,	// VMRESUME
    3620U,	// VMRUN32
    3675U,	// VMRUN64
    3609U,	// VMSAVE32
    3664U,	// VMSAVE64
    25432563U,	// VMWRITE32rm
    29626867U,	// VMWRITE32rr
    33821171U,	// VMWRITE64rm
    29626867U,	// VMWRITE64rr
    3079U,	// VMXOFF
    21189U,	// VMXON
    2999U,	// WBINVD
    3225U,	// WIN_ALLOCA
    3139U,	// WIN_FTOL_32
    3139U,	// WIN_FTOL_64
    4559U,	// WRFSBASE
    4559U,	// WRFSBASE64
    4579U,	// WRGSBASE
    4579U,	// WRGSBASE64
    3340U,	// WRMSR
    270567U,	// XADD16rm
    29626599U,	// XADD16rr
    278759U,	// XADD32rm
    29626599U,	// XADD32rr
    282855U,	// XADD64rm
    29626599U,	// XADD64rr
    286951U,	// XADD8rm
    29626599U,	// XADD8rr
    5598U,	// XCHG16ar
    86557U,	// XCHG16rm
    139805U,	// XCHG16rr
    5761U,	// XCHG32ar
    5761U,	// XCHG32ar64
    90653U,	// XCHG32rm
    139805U,	// XCHG32rr
    5885U,	// XCHG64ar
    94749U,	// XCHG64rm
    139805U,	// XCHG64rr
    98845U,	// XCHG8rm
    139805U,	// XCHG8rr
    2893U,	// XCRYPTCBC
    2837U,	// XCRYPTCFB
    3346U,	// XCRYPTCTR
    2827U,	// XCRYPTECB
    2847U,	// XCRYPTOFB
    3536U,	// XGETBV
    2877U,	// XLAT
    5625U,	// XOR16i16
    271270U,	// XOR16mi
    271270U,	// XOR16mi8
    271270U,	// XOR16mr
    4469670U,	// XOR16ri
    4469670U,	// XOR16ri8
    8663974U,	// XOR16rm
    4469670U,	// XOR16rr
    4461478U,	// XOR16rr_REV
    5791U,	// XOR32i32
    279462U,	// XOR32mi
    279462U,	// XOR32mi8
    279462U,	// XOR32mr
    4469670U,	// XOR32ri
    4469670U,	// XOR32ri8
    12858278U,	// XOR32rm
    4469670U,	// XOR32rr
    4461478U,	// XOR32rr_REV
    5930U,	// XOR64i32
    283558U,	// XOR64mi32
    283558U,	// XOR64mi8
    283558U,	// XOR64mr
    4469670U,	// XOR64ri32
    4469670U,	// XOR64ri8
    17052582U,	// XOR64rm
    4469670U,	// XOR64rr
    4461478U,	// XOR64rr_REV
    5513U,	// XOR8i8
    287654U,	// XOR8mi
    287654U,	// XOR8mi8
    287654U,	// XOR8mr
    4469670U,	// XOR8ri
    4469670U,	// XOR8ri8
    21246886U,	// XOR8rm
    4469670U,	// XOR8rr
    4461478U,	// XOR8rr_REV
    54174U,	// XRSTOR
    53268U,	// XRSTOR64
    54292U,	// XRSTORS
    53288U,	// XRSTORS64
    53756U,	// XSAVE
    53259U,	// XSAVE64
    53426U,	// XSAVEC
    53249U,	// XSAVEC64
    54401U,	// XSAVEOPT
    53299U,	// XSAVEOPT64
    54250U,	// XSAVES
    53278U,	// XSAVES64
    3543U,	// XSETBV
    2520U,	// XSHA1
    2715U,	// XSHA256
    3035U,	// XSTORE
    0U
  };

  static const uint8_t OpInfo2[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    0U,	// DBG_VALUE
    0U,	// REG_SEQUENCE
    0U,	// COPY
    0U,	// BUNDLE
    0U,	// LIFETIME_START
    0U,	// LIFETIME_END
    0U,	// STACKMAP
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    0U,	// STATEPOINT
    0U,	// FRAME_ALLOC
    0U,	// AAA
    0U,	// AAD8i8
    0U,	// AAM8i8
    0U,	// AAS
    0U,	// ACQUIRE_MOV16rm
    0U,	// ACQUIRE_MOV32rm
    0U,	// ACQUIRE_MOV64rm
    0U,	// ACQUIRE_MOV8rm
    0U,	// ADC16i16
    0U,	// ADC16mi
    0U,	// ADC16mi8
    0U,	// ADC16mr
    0U,	// ADC16ri
    0U,	// ADC16ri8
    0U,	// ADC16rm
    0U,	// ADC16rr
    0U,	// ADC16rr_REV
    0U,	// ADC32i32
    0U,	// ADC32mi
    0U,	// ADC32mi8
    0U,	// ADC32mr
    0U,	// ADC32ri
    0U,	// ADC32ri8
    0U,	// ADC32rm
    0U,	// ADC32rr
    0U,	// ADC32rr_REV
    0U,	// ADC64i32
    0U,	// ADC64mi32
    0U,	// ADC64mi8
    0U,	// ADC64mr
    0U,	// ADC64ri32
    0U,	// ADC64ri8
    0U,	// ADC64rm
    0U,	// ADC64rr
    0U,	// ADC64rr_REV
    0U,	// ADC8i8
    0U,	// ADC8mi
    0U,	// ADC8mi8
    0U,	// ADC8mr
    0U,	// ADC8ri
    0U,	// ADC8ri8
    0U,	// ADC8rm
    0U,	// ADC8rr
    0U,	// ADC8rr_REV
    0U,	// ADCX32rm
    0U,	// ADCX32rr
    0U,	// ADCX64rm
    0U,	// ADCX64rr
    0U,	// ADD16i16
    0U,	// ADD16mi
    0U,	// ADD16mi8
    0U,	// ADD16mr
    0U,	// ADD16ri
    0U,	// ADD16ri8
    0U,	// ADD16ri8_DB
    0U,	// ADD16ri_DB
    0U,	// ADD16rm
    0U,	// ADD16rr
    0U,	// ADD16rr_DB
    0U,	// ADD16rr_REV
    0U,	// ADD32i32
    0U,	// ADD32mi
    0U,	// ADD32mi8
    0U,	// ADD32mr
    0U,	// ADD32ri
    0U,	// ADD32ri8
    0U,	// ADD32ri8_DB
    0U,	// ADD32ri_DB
    0U,	// ADD32rm
    0U,	// ADD32rr
    0U,	// ADD32rr_DB
    0U,	// ADD32rr_REV
    0U,	// ADD64i32
    0U,	// ADD64mi32
    0U,	// ADD64mi8
    0U,	// ADD64mr
    0U,	// ADD64ri32
    0U,	// ADD64ri32_DB
    0U,	// ADD64ri8
    0U,	// ADD64ri8_DB
    0U,	// ADD64rm
    0U,	// ADD64rr
    0U,	// ADD64rr_DB
    0U,	// ADD64rr_REV
    0U,	// ADD8i8
    0U,	// ADD8mi
    0U,	// ADD8mi8
    0U,	// ADD8mr
    0U,	// ADD8ri
    0U,	// ADD8ri8
    0U,	// ADD8rm
    0U,	// ADD8rr
    0U,	// ADD8rr_REV
    0U,	// ADJCALLSTACKDOWN32
    0U,	// ADJCALLSTACKDOWN64
    0U,	// ADJCALLSTACKUP32
    0U,	// ADJCALLSTACKUP64
    0U,	// ADOX32rm
    0U,	// ADOX32rr
    0U,	// ADOX64rm
    0U,	// ADOX64rr
    0U,	// AND16i16
    0U,	// AND16mi
    0U,	// AND16mi8
    0U,	// AND16mr
    0U,	// AND16ri
    0U,	// AND16ri8
    0U,	// AND16rm
    0U,	// AND16rr
    0U,	// AND16rr_REV
    0U,	// AND32i32
    0U,	// AND32mi
    0U,	// AND32mi8
    0U,	// AND32mr
    0U,	// AND32ri
    0U,	// AND32ri8
    0U,	// AND32rm
    0U,	// AND32rr
    0U,	// AND32rr_REV
    0U,	// AND64i32
    0U,	// AND64mi32
    0U,	// AND64mi8
    0U,	// AND64mr
    0U,	// AND64ri32
    0U,	// AND64ri8
    0U,	// AND64rm
    0U,	// AND64rr
    0U,	// AND64rr_REV
    0U,	// AND8i8
    0U,	// AND8mi
    0U,	// AND8mi8
    0U,	// AND8mr
    0U,	// AND8ri
    0U,	// AND8ri8
    0U,	// AND8rm
    0U,	// AND8rr
    0U,	// AND8rr_REV
    0U,	// ANDN32rm
    0U,	// ANDN32rr
    0U,	// ANDN64rm
    0U,	// ANDN64rr
    0U,	// ARPL16mr
    0U,	// ARPL16rr
    0U,	// BEXTR32rm
    0U,	// BEXTR32rr
    0U,	// BEXTR64rm
    0U,	// BEXTR64rr
    0U,	// BEXTRI32mi
    0U,	// BEXTRI32ri
    0U,	// BEXTRI64mi
    0U,	// BEXTRI64ri
    0U,	// BLCFILL32rm
    0U,	// BLCFILL32rr
    0U,	// BLCFILL64rm
    0U,	// BLCFILL64rr
    0U,	// BLCI32rm
    0U,	// BLCI32rr
    0U,	// BLCI64rm
    0U,	// BLCI64rr
    0U,	// BLCIC32rm
    0U,	// BLCIC32rr
    0U,	// BLCIC64rm
    0U,	// BLCIC64rr
    0U,	// BLCMSK32rm
    0U,	// BLCMSK32rr
    0U,	// BLCMSK64rm
    0U,	// BLCMSK64rr
    0U,	// BLCS32rm
    0U,	// BLCS32rr
    0U,	// BLCS64rm
    0U,	// BLCS64rr
    0U,	// BLSFILL32rm
    0U,	// BLSFILL32rr
    0U,	// BLSFILL64rm
    0U,	// BLSFILL64rr
    0U,	// BLSI32rm
    0U,	// BLSI32rr
    0U,	// BLSI64rm
    0U,	// BLSI64rr
    0U,	// BLSIC32rm
    0U,	// BLSIC32rr
    0U,	// BLSIC64rm
    0U,	// BLSIC64rr
    0U,	// BLSMSK32rm
    0U,	// BLSMSK32rr
    0U,	// BLSMSK64rm
    0U,	// BLSMSK64rr
    0U,	// BLSR32rm
    0U,	// BLSR32rr
    0U,	// BLSR64rm
    0U,	// BLSR64rr
    0U,	// BOUNDS16rm
    0U,	// BOUNDS32rm
    0U,	// BSF16rm
    0U,	// BSF16rr
    0U,	// BSF32rm
    0U,	// BSF32rr
    0U,	// BSF64rm
    0U,	// BSF64rr
    0U,	// BSR16rm
    0U,	// BSR16rr
    0U,	// BSR32rm
    0U,	// BSR32rr
    0U,	// BSR64rm
    0U,	// BSR64rr
    0U,	// BSWAP32r
    0U,	// BSWAP64r
    0U,	// BT16mi8
    0U,	// BT16mr
    0U,	// BT16ri8
    0U,	// BT16rr
    0U,	// BT32mi8
    0U,	// BT32mr
    0U,	// BT32ri8
    0U,	// BT32rr
    0U,	// BT64mi8
    0U,	// BT64mr
    0U,	// BT64ri8
    0U,	// BT64rr
    0U,	// BTC16mi8
    0U,	// BTC16mr
    0U,	// BTC16ri8
    0U,	// BTC16rr
    0U,	// BTC32mi8
    0U,	// BTC32mr
    0U,	// BTC32ri8
    0U,	// BTC32rr
    0U,	// BTC64mi8
    0U,	// BTC64mr
    0U,	// BTC64ri8
    0U,	// BTC64rr
    0U,	// BTR16mi8
    0U,	// BTR16mr
    0U,	// BTR16ri8
    0U,	// BTR16rr
    0U,	// BTR32mi8
    0U,	// BTR32mr
    0U,	// BTR32ri8
    0U,	// BTR32rr
    0U,	// BTR64mi8
    0U,	// BTR64mr
    0U,	// BTR64ri8
    0U,	// BTR64rr
    0U,	// BTS16mi8
    0U,	// BTS16mr
    0U,	// BTS16ri8
    0U,	// BTS16rr
    0U,	// BTS32mi8
    0U,	// BTS32mr
    0U,	// BTS32ri8
    0U,	// BTS32rr
    0U,	// BTS64mi8
    0U,	// BTS64mr
    0U,	// BTS64ri8
    0U,	// BTS64rr
    0U,	// BZHI32rm
    0U,	// BZHI32rr
    0U,	// BZHI64rm
    0U,	// BZHI64rr
    0U,	// CALL16m
    0U,	// CALL16r
    0U,	// CALL32m
    0U,	// CALL32r
    0U,	// CALL64m
    0U,	// CALL64pcrel32
    0U,	// CALL64r
    0U,	// CALLpcrel16
    0U,	// CALLpcrel32
    0U,	// CBW
    0U,	// CDQ
    0U,	// CDQE
    0U,	// CLAC
    0U,	// CLC
    0U,	// CLD
    0U,	// CLFLUSHOPT
    0U,	// CLGI
    0U,	// CLI
    0U,	// CLTS
    0U,	// CLWB
    0U,	// CMC
    0U,	// CMOVA16rm
    0U,	// CMOVA16rr
    0U,	// CMOVA32rm
    0U,	// CMOVA32rr
    0U,	// CMOVA64rm
    0U,	// CMOVA64rr
    0U,	// CMOVAE16rm
    0U,	// CMOVAE16rr
    0U,	// CMOVAE32rm
    0U,	// CMOVAE32rr
    0U,	// CMOVAE64rm
    0U,	// CMOVAE64rr
    0U,	// CMOVB16rm
    0U,	// CMOVB16rr
    0U,	// CMOVB32rm
    0U,	// CMOVB32rr
    0U,	// CMOVB64rm
    0U,	// CMOVB64rr
    0U,	// CMOVBE16rm
    0U,	// CMOVBE16rr
    0U,	// CMOVBE32rm
    0U,	// CMOVBE32rr
    0U,	// CMOVBE64rm
    0U,	// CMOVBE64rr
    0U,	// CMOVE16rm
    0U,	// CMOVE16rr
    0U,	// CMOVE32rm
    0U,	// CMOVE32rr
    0U,	// CMOVE64rm
    0U,	// CMOVE64rr
    0U,	// CMOVG16rm
    0U,	// CMOVG16rr
    0U,	// CMOVG32rm
    0U,	// CMOVG32rr
    0U,	// CMOVG64rm
    0U,	// CMOVG64rr
    0U,	// CMOVGE16rm
    0U,	// CMOVGE16rr
    0U,	// CMOVGE32rm
    0U,	// CMOVGE32rr
    0U,	// CMOVGE64rm
    0U,	// CMOVGE64rr
    0U,	// CMOVL16rm
    0U,	// CMOVL16rr
    0U,	// CMOVL32rm
    0U,	// CMOVL32rr
    0U,	// CMOVL64rm
    0U,	// CMOVL64rr
    0U,	// CMOVLE16rm
    0U,	// CMOVLE16rr
    0U,	// CMOVLE32rm
    0U,	// CMOVLE32rr
    0U,	// CMOVLE64rm
    0U,	// CMOVLE64rr
    0U,	// CMOVNE16rm
    0U,	// CMOVNE16rr
    0U,	// CMOVNE32rm
    0U,	// CMOVNE32rr
    0U,	// CMOVNE64rm
    0U,	// CMOVNE64rr
    0U,	// CMOVNO16rm
    0U,	// CMOVNO16rr
    0U,	// CMOVNO32rm
    0U,	// CMOVNO32rr
    0U,	// CMOVNO64rm
    0U,	// CMOVNO64rr
    0U,	// CMOVNP16rm
    0U,	// CMOVNP16rr
    0U,	// CMOVNP32rm
    0U,	// CMOVNP32rr
    0U,	// CMOVNP64rm
    0U,	// CMOVNP64rr
    0U,	// CMOVNS16rm
    0U,	// CMOVNS16rr
    0U,	// CMOVNS32rm
    0U,	// CMOVNS32rr
    0U,	// CMOVNS64rm
    0U,	// CMOVNS64rr
    0U,	// CMOVO16rm
    0U,	// CMOVO16rr
    0U,	// CMOVO32rm
    0U,	// CMOVO32rr
    0U,	// CMOVO64rm
    0U,	// CMOVO64rr
    0U,	// CMOVP16rm
    0U,	// CMOVP16rr
    0U,	// CMOVP32rm
    0U,	// CMOVP32rr
    0U,	// CMOVP64rm
    0U,	// CMOVP64rr
    0U,	// CMOVS16rm
    0U,	// CMOVS16rr
    0U,	// CMOVS32rm
    0U,	// CMOVS32rr
    0U,	// CMOVS64rm
    0U,	// CMOVS64rr
    0U,	// CMOV_FR32
    0U,	// CMOV_FR64
    0U,	// CMOV_GR16
    0U,	// CMOV_GR32
    0U,	// CMOV_GR8
    0U,	// CMOV_RFP32
    0U,	// CMOV_RFP64
    0U,	// CMOV_RFP80
    0U,	// CMOV_V16F32
    0U,	// CMOV_V2F64
    0U,	// CMOV_V2I64
    0U,	// CMOV_V4F32
    0U,	// CMOV_V4F64
    0U,	// CMOV_V4I64
    0U,	// CMOV_V8F32
    0U,	// CMOV_V8F64
    0U,	// CMOV_V8I64
    0U,	// CMP16i16
    0U,	// CMP16mi
    0U,	// CMP16mi8
    0U,	// CMP16mr
    0U,	// CMP16ri
    0U,	// CMP16ri8
    0U,	// CMP16rm
    0U,	// CMP16rr
    0U,	// CMP16rr_REV
    0U,	// CMP32i32
    0U,	// CMP32mi
    0U,	// CMP32mi8
    0U,	// CMP32mr
    0U,	// CMP32ri
    0U,	// CMP32ri8
    0U,	// CMP32rm
    0U,	// CMP32rr
    0U,	// CMP32rr_REV
    0U,	// CMP64i32
    0U,	// CMP64mi32
    0U,	// CMP64mi8
    0U,	// CMP64mr
    0U,	// CMP64ri32
    0U,	// CMP64ri8
    0U,	// CMP64rm
    0U,	// CMP64rr
    0U,	// CMP64rr_REV
    0U,	// CMP8i8
    0U,	// CMP8mi
    0U,	// CMP8mi8
    0U,	// CMP8mr
    0U,	// CMP8ri
    0U,	// CMP8ri8
    0U,	// CMP8rm
    0U,	// CMP8rr
    0U,	// CMP8rr_REV
    0U,	// CMPSB
    0U,	// CMPSL
    0U,	// CMPSQ
    0U,	// CMPSW
    0U,	// CMPXCHG16B
    0U,	// CMPXCHG16rm
    0U,	// CMPXCHG16rr
    0U,	// CMPXCHG32rm
    0U,	// CMPXCHG32rr
    0U,	// CMPXCHG64rm
    0U,	// CMPXCHG64rr
    0U,	// CMPXCHG8B
    0U,	// CMPXCHG8rm
    0U,	// CMPXCHG8rr
    0U,	// CPUID
    0U,	// CQO
    0U,	// CWD
    0U,	// CWDE
    0U,	// DAA
    0U,	// DAS
    0U,	// DATA16_PREFIX
    0U,	// DEC16m
    0U,	// DEC16r
    0U,	// DEC16r_alt
    0U,	// DEC32m
    0U,	// DEC32r
    0U,	// DEC32r_alt
    0U,	// DEC64m
    0U,	// DEC64r
    0U,	// DEC8m
    0U,	// DEC8r
    0U,	// DIV16m
    0U,	// DIV16r
    0U,	// DIV32m
    0U,	// DIV32r
    0U,	// DIV64m
    0U,	// DIV64r
    0U,	// DIV8m
    0U,	// DIV8r
    0U,	// EH_RETURN
    0U,	// EH_RETURN64
    0U,	// EH_SjLj_LongJmp32
    0U,	// EH_SjLj_LongJmp64
    0U,	// EH_SjLj_SetJmp32
    0U,	// EH_SjLj_SetJmp64
    0U,	// EH_SjLj_Setup
    0U,	// ENTER
    0U,	// FARCALL16i
    0U,	// FARCALL16m
    0U,	// FARCALL32i
    0U,	// FARCALL32m
    0U,	// FARCALL64
    0U,	// FARJMP16i
    0U,	// FARJMP16m
    0U,	// FARJMP32i
    0U,	// FARJMP32m
    0U,	// FARJMP64
    0U,	// FSETPM
    0U,	// GETSEC
    0U,	// HLT
    0U,	// IDIV16m
    0U,	// IDIV16r
    0U,	// IDIV32m
    0U,	// IDIV32r
    0U,	// IDIV64m
    0U,	// IDIV64r
    0U,	// IDIV8m
    0U,	// IDIV8r
    0U,	// IMUL16m
    0U,	// IMUL16r
    0U,	// IMUL16rm
    0U,	// IMUL16rmi
    0U,	// IMUL16rmi8
    0U,	// IMUL16rr
    0U,	// IMUL16rri
    0U,	// IMUL16rri8
    0U,	// IMUL32m
    0U,	// IMUL32r
    0U,	// IMUL32rm
    0U,	// IMUL32rmi
    0U,	// IMUL32rmi8
    0U,	// IMUL32rr
    0U,	// IMUL32rri
    0U,	// IMUL32rri8
    0U,	// IMUL64m
    0U,	// IMUL64r
    0U,	// IMUL64rm
    0U,	// IMUL64rmi32
    0U,	// IMUL64rmi8
    0U,	// IMUL64rr
    0U,	// IMUL64rri32
    0U,	// IMUL64rri8
    0U,	// IMUL8m
    0U,	// IMUL8r
    0U,	// IN16ri
    0U,	// IN16rr
    0U,	// IN32ri
    0U,	// IN32rr
    0U,	// IN8ri
    0U,	// IN8rr
    0U,	// INC16m
    0U,	// INC16r
    0U,	// INC16r_alt
    0U,	// INC32m
    0U,	// INC32r
    0U,	// INC32r_alt
    0U,	// INC64m
    0U,	// INC64r
    0U,	// INC8m
    0U,	// INC8r
    0U,	// INSB
    0U,	// INSL
    0U,	// INSW
    0U,	// INT
    0U,	// INT1
    0U,	// INT3
    0U,	// INTO
    0U,	// INVD
    0U,	// INVEPT32
    0U,	// INVEPT64
    0U,	// INVLPG
    0U,	// INVLPGA32
    0U,	// INVLPGA64
    0U,	// INVPCID32
    0U,	// INVPCID64
    0U,	// INVVPID32
    0U,	// INVVPID64
    0U,	// IRET16
    0U,	// IRET32
    0U,	// IRET64
    0U,	// Int_MemBarrier
    0U,	// JAE_1
    0U,	// JAE_2
    0U,	// JAE_4
    0U,	// JA_1
    0U,	// JA_2
    0U,	// JA_4
    0U,	// JBE_1
    0U,	// JBE_2
    0U,	// JBE_4
    0U,	// JB_1
    0U,	// JB_2
    0U,	// JB_4
    0U,	// JCXZ
    0U,	// JECXZ
    0U,	// JE_1
    0U,	// JE_2
    0U,	// JE_4
    0U,	// JGE_1
    0U,	// JGE_2
    0U,	// JGE_4
    0U,	// JG_1
    0U,	// JG_2
    0U,	// JG_4
    0U,	// JLE_1
    0U,	// JLE_2
    0U,	// JLE_4
    0U,	// JL_1
    0U,	// JL_2
    0U,	// JL_4
    0U,	// JMP16m
    0U,	// JMP16r
    0U,	// JMP32m
    0U,	// JMP32r
    0U,	// JMP64m
    0U,	// JMP64r
    0U,	// JMP_1
    0U,	// JMP_2
    0U,	// JMP_4
    0U,	// JNE_1
    0U,	// JNE_2
    0U,	// JNE_4
    0U,	// JNO_1
    0U,	// JNO_2
    0U,	// JNO_4
    0U,	// JNP_1
    0U,	// JNP_2
    0U,	// JNP_4
    0U,	// JNS_1
    0U,	// JNS_2
    0U,	// JNS_4
    0U,	// JO_1
    0U,	// JO_2
    0U,	// JO_4
    0U,	// JP_1
    0U,	// JP_2
    0U,	// JP_4
    0U,	// JRCXZ
    0U,	// JS_1
    0U,	// JS_2
    0U,	// JS_4
    0U,	// LAHF
    0U,	// LAR16rm
    0U,	// LAR16rr
    0U,	// LAR32rm
    0U,	// LAR32rr
    0U,	// LAR64rm
    0U,	// LAR64rr
    0U,	// LCMPXCHG16
    0U,	// LCMPXCHG16B
    0U,	// LCMPXCHG32
    0U,	// LCMPXCHG64
    0U,	// LCMPXCHG8
    0U,	// LCMPXCHG8B
    0U,	// LDS16rm
    0U,	// LDS32rm
    0U,	// LEA16r
    0U,	// LEA32r
    0U,	// LEA64_32r
    0U,	// LEA64r
    0U,	// LEAVE
    0U,	// LEAVE64
    0U,	// LES16rm
    0U,	// LES32rm
    0U,	// LFS16rm
    0U,	// LFS32rm
    0U,	// LFS64rm
    0U,	// LGDT16m
    0U,	// LGDT32m
    0U,	// LGDT64m
    0U,	// LGS16rm
    0U,	// LGS32rm
    0U,	// LGS64rm
    0U,	// LIDT16m
    0U,	// LIDT32m
    0U,	// LIDT64m
    0U,	// LLDT16m
    0U,	// LLDT16r
    0U,	// LMSW16m
    0U,	// LMSW16r
    0U,	// LOCK_ADD16mi
    0U,	// LOCK_ADD16mi8
    0U,	// LOCK_ADD16mr
    0U,	// LOCK_ADD32mi
    0U,	// LOCK_ADD32mi8
    0U,	// LOCK_ADD32mr
    0U,	// LOCK_ADD64mi32
    0U,	// LOCK_ADD64mi8
    0U,	// LOCK_ADD64mr
    0U,	// LOCK_ADD8mi
    0U,	// LOCK_ADD8mr
    0U,	// LOCK_AND16mi
    0U,	// LOCK_AND16mi8
    0U,	// LOCK_AND16mr
    0U,	// LOCK_AND32mi
    0U,	// LOCK_AND32mi8
    0U,	// LOCK_AND32mr
    0U,	// LOCK_AND64mi32
    0U,	// LOCK_AND64mi8
    0U,	// LOCK_AND64mr
    0U,	// LOCK_AND8mi
    0U,	// LOCK_AND8mr
    0U,	// LOCK_DEC16m
    0U,	// LOCK_DEC32m
    0U,	// LOCK_DEC64m
    0U,	// LOCK_DEC8m
    0U,	// LOCK_INC16m
    0U,	// LOCK_INC32m
    0U,	// LOCK_INC64m
    0U,	// LOCK_INC8m
    0U,	// LOCK_OR16mi
    0U,	// LOCK_OR16mi8
    0U,	// LOCK_OR16mr
    0U,	// LOCK_OR32mi
    0U,	// LOCK_OR32mi8
    0U,	// LOCK_OR32mr
    0U,	// LOCK_OR64mi32
    0U,	// LOCK_OR64mi8
    0U,	// LOCK_OR64mr
    0U,	// LOCK_OR8mi
    0U,	// LOCK_OR8mr
    0U,	// LOCK_PREFIX
    0U,	// LOCK_SUB16mi
    0U,	// LOCK_SUB16mi8
    0U,	// LOCK_SUB16mr
    0U,	// LOCK_SUB32mi
    0U,	// LOCK_SUB32mi8
    0U,	// LOCK_SUB32mr
    0U,	// LOCK_SUB64mi32
    0U,	// LOCK_SUB64mi8
    0U,	// LOCK_SUB64mr
    0U,	// LOCK_SUB8mi
    0U,	// LOCK_SUB8mr
    0U,	// LOCK_XOR16mi
    0U,	// LOCK_XOR16mi8
    0U,	// LOCK_XOR16mr
    0U,	// LOCK_XOR32mi
    0U,	// LOCK_XOR32mi8
    0U,	// LOCK_XOR32mr
    0U,	// LOCK_XOR64mi32
    0U,	// LOCK_XOR64mi8
    0U,	// LOCK_XOR64mr
    0U,	// LOCK_XOR8mi
    0U,	// LOCK_XOR8mr
    0U,	// LODSB
    0U,	// LODSL
    0U,	// LODSQ
    0U,	// LODSW
    0U,	// LOOP
    0U,	// LOOPE
    0U,	// LOOPNE
    0U,	// LRETIL
    0U,	// LRETIQ
    0U,	// LRETIW
    0U,	// LRETL
    0U,	// LRETQ
    0U,	// LRETW
    0U,	// LSL16rm
    0U,	// LSL16rr
    0U,	// LSL32rm
    0U,	// LSL32rr
    0U,	// LSL64rm
    0U,	// LSL64rr
    0U,	// LSS16rm
    0U,	// LSS32rm
    0U,	// LSS64rm
    0U,	// LTRm
    0U,	// LTRr
    0U,	// LXADD16
    0U,	// LXADD32
    0U,	// LXADD64
    0U,	// LXADD8
    0U,	// LZCNT16rm
    0U,	// LZCNT16rr
    0U,	// LZCNT32rm
    0U,	// LZCNT32rr
    0U,	// LZCNT64rm
    0U,	// LZCNT64rr
    0U,	// MONTMUL
    0U,	// MORESTACK_RET
    0U,	// MORESTACK_RET_RESTORE_R10
    0U,	// MOV16ao16
    0U,	// MOV16ao32
    0U,	// MOV16ao64
    0U,	// MOV16mi
    0U,	// MOV16mr
    0U,	// MOV16ms
    0U,	// MOV16o16a
    0U,	// MOV16o32a
    0U,	// MOV16o64a
    0U,	// MOV16ri
    0U,	// MOV16ri_alt
    0U,	// MOV16rm
    0U,	// MOV16rr
    0U,	// MOV16rr_REV
    0U,	// MOV16rs
    0U,	// MOV16sm
    0U,	// MOV16sr
    0U,	// MOV32ao16
    0U,	// MOV32ao32
    0U,	// MOV32ao64
    0U,	// MOV32cr
    0U,	// MOV32dr
    0U,	// MOV32mi
    0U,	// MOV32mr
    0U,	// MOV32ms
    0U,	// MOV32o16a
    0U,	// MOV32o32a
    0U,	// MOV32o64a
    0U,	// MOV32r0
    0U,	// MOV32rc
    0U,	// MOV32rd
    0U,	// MOV32ri
    0U,	// MOV32ri64
    0U,	// MOV32ri_alt
    0U,	// MOV32rm
    0U,	// MOV32rr
    0U,	// MOV32rr_REV
    0U,	// MOV32rs
    0U,	// MOV32sm
    0U,	// MOV32sr
    0U,	// MOV64ao32
    0U,	// MOV64ao64
    0U,	// MOV64cr
    0U,	// MOV64dr
    0U,	// MOV64mi32
    0U,	// MOV64mr
    0U,	// MOV64ms
    0U,	// MOV64o32a
    0U,	// MOV64o64a
    0U,	// MOV64rc
    0U,	// MOV64rd
    0U,	// MOV64ri
    0U,	// MOV64ri32
    0U,	// MOV64rm
    0U,	// MOV64rr
    0U,	// MOV64rr_REV
    0U,	// MOV64rs
    0U,	// MOV64sm
    0U,	// MOV64sr
    0U,	// MOV8ao16
    0U,	// MOV8ao32
    0U,	// MOV8ao64
    0U,	// MOV8mi
    0U,	// MOV8mr
    0U,	// MOV8mr_NOREX
    0U,	// MOV8o16a
    0U,	// MOV8o32a
    0U,	// MOV8o64a
    0U,	// MOV8ri
    0U,	// MOV8ri_alt
    0U,	// MOV8rm
    0U,	// MOV8rm_NOREX
    0U,	// MOV8rr
    0U,	// MOV8rr_NOREX
    0U,	// MOV8rr_REV
    0U,	// MOVBE16mr
    0U,	// MOVBE16rm
    0U,	// MOVBE32mr
    0U,	// MOVBE32rm
    0U,	// MOVBE64mr
    0U,	// MOVBE64rm
    0U,	// MOVPC32r
    0U,	// MOVSB
    0U,	// MOVSL
    0U,	// MOVSQ
    0U,	// MOVSW
    0U,	// MOVSX16rm8
    0U,	// MOVSX16rr8
    0U,	// MOVSX32_NOREXrm8
    0U,	// MOVSX32_NOREXrr8
    0U,	// MOVSX32rm16
    0U,	// MOVSX32rm8
    0U,	// MOVSX32rr16
    0U,	// MOVSX32rr8
    0U,	// MOVSX64_NOREXrr32
    0U,	// MOVSX64rm16
    0U,	// MOVSX64rm32
    0U,	// MOVSX64rm32_alt
    0U,	// MOVSX64rm8
    0U,	// MOVSX64rr16
    0U,	// MOVSX64rr32
    0U,	// MOVSX64rr8
    0U,	// MOVZX16rm8
    0U,	// MOVZX16rr8
    0U,	// MOVZX32_NOREXrm8
    0U,	// MOVZX32_NOREXrr8
    0U,	// MOVZX32rm16
    0U,	// MOVZX32rm8
    0U,	// MOVZX32rr16
    0U,	// MOVZX32rr8
    0U,	// MOVZX64rm16_Q
    0U,	// MOVZX64rm8_Q
    0U,	// MOVZX64rr16_Q
    0U,	// MOVZX64rr8_Q
    0U,	// MUL16m
    0U,	// MUL16r
    0U,	// MUL32m
    0U,	// MUL32r
    0U,	// MUL64m
    0U,	// MUL64r
    0U,	// MUL8m
    0U,	// MUL8r
    0U,	// MULX32rm
    0U,	// MULX32rr
    0U,	// MULX64rm
    0U,	// MULX64rr
    0U,	// NEG16m
    0U,	// NEG16r
    0U,	// NEG32m
    0U,	// NEG32r
    0U,	// NEG64m
    0U,	// NEG64r
    0U,	// NEG8m
    0U,	// NEG8r
    0U,	// NOOP
    0U,	// NOOP18_16m4
    0U,	// NOOP18_16m5
    0U,	// NOOP18_16m6
    0U,	// NOOP18_16m7
    0U,	// NOOP18_16r4
    0U,	// NOOP18_16r5
    0U,	// NOOP18_16r6
    0U,	// NOOP18_16r7
    0U,	// NOOP18_m4
    0U,	// NOOP18_m5
    0U,	// NOOP18_m6
    0U,	// NOOP18_m7
    0U,	// NOOP18_r4
    0U,	// NOOP18_r5
    0U,	// NOOP18_r6
    0U,	// NOOP18_r7
    0U,	// NOOP19rr
    0U,	// NOOPL
    0U,	// NOOPL_19
    0U,	// NOOPL_1a
    0U,	// NOOPL_1b
    0U,	// NOOPL_1c
    0U,	// NOOPL_1d
    0U,	// NOOPL_1e
    0U,	// NOOPW
    0U,	// NOOPW_19
    0U,	// NOOPW_1a
    0U,	// NOOPW_1b
    0U,	// NOOPW_1c
    0U,	// NOOPW_1d
    0U,	// NOOPW_1e
    0U,	// NOT16m
    0U,	// NOT16r
    0U,	// NOT32m
    0U,	// NOT32r
    0U,	// NOT64m
    0U,	// NOT64r
    0U,	// NOT8m
    0U,	// NOT8r
    0U,	// OR16i16
    0U,	// OR16mi
    0U,	// OR16mi8
    0U,	// OR16mr
    0U,	// OR16ri
    0U,	// OR16ri8
    0U,	// OR16rm
    0U,	// OR16rr
    0U,	// OR16rr_REV
    0U,	// OR32i32
    0U,	// OR32mi
    0U,	// OR32mi8
    0U,	// OR32mr
    0U,	// OR32mrLocked
    0U,	// OR32ri
    0U,	// OR32ri8
    0U,	// OR32rm
    0U,	// OR32rr
    0U,	// OR32rr_REV
    0U,	// OR64i32
    0U,	// OR64mi32
    0U,	// OR64mi8
    0U,	// OR64mr
    0U,	// OR64ri32
    0U,	// OR64ri8
    0U,	// OR64rm
    0U,	// OR64rr
    0U,	// OR64rr_REV
    0U,	// OR8i8
    0U,	// OR8mi
    0U,	// OR8mi8
    0U,	// OR8mr
    0U,	// OR8ri
    0U,	// OR8ri8
    0U,	// OR8rm
    0U,	// OR8rr
    0U,	// OR8rr_REV
    0U,	// OUT16ir
    0U,	// OUT16rr
    0U,	// OUT32ir
    0U,	// OUT32rr
    0U,	// OUT8ir
    0U,	// OUT8rr
    0U,	// OUTSB
    0U,	// OUTSL
    0U,	// OUTSW
    0U,	// PCOMMIT
    0U,	// PDEP32rm
    0U,	// PDEP32rr
    0U,	// PDEP64rm
    0U,	// PDEP64rr
    0U,	// PEXT32rm
    0U,	// PEXT32rr
    0U,	// PEXT64rm
    0U,	// PEXT64rr
    0U,	// POP16r
    0U,	// POP16rmm
    0U,	// POP16rmr
    0U,	// POP32r
    0U,	// POP32rmm
    0U,	// POP32rmr
    0U,	// POP64r
    0U,	// POP64rmm
    0U,	// POP64rmr
    0U,	// POPA16
    0U,	// POPA32
    0U,	// POPDS16
    0U,	// POPDS32
    0U,	// POPES16
    0U,	// POPES32
    0U,	// POPF16
    0U,	// POPF32
    0U,	// POPF64
    0U,	// POPFS16
    0U,	// POPFS32
    0U,	// POPFS64
    0U,	// POPGS16
    0U,	// POPGS32
    0U,	// POPGS64
    0U,	// POPSS16
    0U,	// POPSS32
    0U,	// PUSH16i8
    0U,	// PUSH16r
    0U,	// PUSH16rmm
    0U,	// PUSH16rmr
    0U,	// PUSH32i8
    0U,	// PUSH32r
    0U,	// PUSH32rmm
    0U,	// PUSH32rmr
    0U,	// PUSH64i16
    0U,	// PUSH64i32
    0U,	// PUSH64i8
    0U,	// PUSH64r
    0U,	// PUSH64rmm
    0U,	// PUSH64rmr
    0U,	// PUSHA16
    0U,	// PUSHA32
    0U,	// PUSHCS16
    0U,	// PUSHCS32
    0U,	// PUSHDS16
    0U,	// PUSHDS32
    0U,	// PUSHES16
    0U,	// PUSHES32
    0U,	// PUSHF16
    0U,	// PUSHF32
    0U,	// PUSHF64
    0U,	// PUSHFS16
    0U,	// PUSHFS32
    0U,	// PUSHFS64
    0U,	// PUSHGS16
    0U,	// PUSHGS32
    0U,	// PUSHGS64
    0U,	// PUSHSS16
    0U,	// PUSHSS32
    0U,	// PUSHi16
    0U,	// PUSHi32
    0U,	// RCL16m1
    0U,	// RCL16mCL
    0U,	// RCL16mi
    0U,	// RCL16r1
    0U,	// RCL16rCL
    0U,	// RCL16ri
    0U,	// RCL32m1
    0U,	// RCL32mCL
    0U,	// RCL32mi
    0U,	// RCL32r1
    0U,	// RCL32rCL
    0U,	// RCL32ri
    0U,	// RCL64m1
    0U,	// RCL64mCL
    0U,	// RCL64mi
    0U,	// RCL64r1
    0U,	// RCL64rCL
    0U,	// RCL64ri
    0U,	// RCL8m1
    0U,	// RCL8mCL
    0U,	// RCL8mi
    0U,	// RCL8r1
    0U,	// RCL8rCL
    0U,	// RCL8ri
    0U,	// RCR16m1
    0U,	// RCR16mCL
    0U,	// RCR16mi
    0U,	// RCR16r1
    0U,	// RCR16rCL
    0U,	// RCR16ri
    0U,	// RCR32m1
    0U,	// RCR32mCL
    0U,	// RCR32mi
    0U,	// RCR32r1
    0U,	// RCR32rCL
    0U,	// RCR32ri
    0U,	// RCR64m1
    0U,	// RCR64mCL
    0U,	// RCR64mi
    0U,	// RCR64r1
    0U,	// RCR64rCL
    0U,	// RCR64ri
    0U,	// RCR8m1
    0U,	// RCR8mCL
    0U,	// RCR8mi
    0U,	// RCR8r1
    0U,	// RCR8rCL
    0U,	// RCR8ri
    0U,	// RDFSBASE
    0U,	// RDFSBASE64
    0U,	// RDGSBASE
    0U,	// RDGSBASE64
    0U,	// RDMSR
    0U,	// RDPMC
    0U,	// RDRAND16r
    0U,	// RDRAND32r
    0U,	// RDRAND64r
    0U,	// RDSEED16r
    0U,	// RDSEED32r
    0U,	// RDSEED64r
    0U,	// RDTSC
    0U,	// RDTSCP
    0U,	// RELEASE_ADD32mi
    0U,	// RELEASE_ADD64mi32
    0U,	// RELEASE_ADD8mi
    0U,	// RELEASE_AND32mi
    0U,	// RELEASE_AND64mi32
    0U,	// RELEASE_AND8mi
    0U,	// RELEASE_DEC16m
    0U,	// RELEASE_DEC32m
    0U,	// RELEASE_DEC64m
    0U,	// RELEASE_DEC8m
    0U,	// RELEASE_INC16m
    0U,	// RELEASE_INC32m
    0U,	// RELEASE_INC64m
    0U,	// RELEASE_INC8m
    0U,	// RELEASE_MOV16mi
    0U,	// RELEASE_MOV16mr
    0U,	// RELEASE_MOV32mi
    0U,	// RELEASE_MOV32mr
    0U,	// RELEASE_MOV64mi32
    0U,	// RELEASE_MOV64mr
    0U,	// RELEASE_MOV8mi
    0U,	// RELEASE_MOV8mr
    0U,	// RELEASE_OR32mi
    0U,	// RELEASE_OR64mi32
    0U,	// RELEASE_OR8mi
    0U,	// RELEASE_XOR32mi
    0U,	// RELEASE_XOR64mi32
    0U,	// RELEASE_XOR8mi
    0U,	// REPNE_PREFIX
    0U,	// REP_MOVSB_32
    0U,	// REP_MOVSB_64
    0U,	// REP_MOVSD_32
    0U,	// REP_MOVSD_64
    0U,	// REP_MOVSQ_64
    0U,	// REP_MOVSW_32
    0U,	// REP_MOVSW_64
    0U,	// REP_PREFIX
    0U,	// REP_STOSB_32
    0U,	// REP_STOSB_64
    0U,	// REP_STOSD_32
    0U,	// REP_STOSD_64
    0U,	// REP_STOSQ_64
    0U,	// REP_STOSW_32
    0U,	// REP_STOSW_64
    0U,	// RETIL
    0U,	// RETIQ
    0U,	// RETIW
    0U,	// RETL
    0U,	// RETQ
    0U,	// RETW
    0U,	// REX64_PREFIX
    0U,	// ROL16m1
    0U,	// ROL16mCL
    0U,	// ROL16mi
    0U,	// ROL16r1
    0U,	// ROL16rCL
    0U,	// ROL16ri
    0U,	// ROL32m1
    0U,	// ROL32mCL
    0U,	// ROL32mi
    0U,	// ROL32r1
    0U,	// ROL32rCL
    0U,	// ROL32ri
    0U,	// ROL64m1
    0U,	// ROL64mCL
    0U,	// ROL64mi
    0U,	// ROL64r1
    0U,	// ROL64rCL
    0U,	// ROL64ri
    0U,	// ROL8m1
    0U,	// ROL8mCL
    0U,	// ROL8mi
    0U,	// ROL8r1
    0U,	// ROL8rCL
    0U,	// ROL8ri
    0U,	// ROR16m1
    0U,	// ROR16mCL
    0U,	// ROR16mi
    0U,	// ROR16r1
    0U,	// ROR16rCL
    0U,	// ROR16ri
    0U,	// ROR32m1
    0U,	// ROR32mCL
    0U,	// ROR32mi
    0U,	// ROR32r1
    0U,	// ROR32rCL
    0U,	// ROR32ri
    0U,	// ROR64m1
    0U,	// ROR64mCL
    0U,	// ROR64mi
    0U,	// ROR64r1
    0U,	// ROR64rCL
    0U,	// ROR64ri
    0U,	// ROR8m1
    0U,	// ROR8mCL
    0U,	// ROR8mi
    0U,	// ROR8r1
    0U,	// ROR8rCL
    0U,	// ROR8ri
    0U,	// RORX32mi
    0U,	// RORX32ri
    0U,	// RORX64mi
    0U,	// RORX64ri
    0U,	// RSM
    0U,	// SAHF
    0U,	// SAL16m1
    0U,	// SAL16mCL
    0U,	// SAL16mi
    0U,	// SAL16r1
    0U,	// SAL16rCL
    0U,	// SAL16ri
    0U,	// SAL32m1
    0U,	// SAL32mCL
    0U,	// SAL32mi
    0U,	// SAL32r1
    0U,	// SAL32rCL
    0U,	// SAL32ri
    0U,	// SAL64m1
    0U,	// SAL64mCL
    0U,	// SAL64mi
    0U,	// SAL64r1
    0U,	// SAL64rCL
    0U,	// SAL64ri
    0U,	// SAL8m1
    0U,	// SAL8mCL
    0U,	// SAL8mi
    0U,	// SAL8r1
    0U,	// SAL8rCL
    0U,	// SAL8ri
    0U,	// SALC
    0U,	// SAR16m1
    0U,	// SAR16mCL
    0U,	// SAR16mi
    0U,	// SAR16r1
    0U,	// SAR16rCL
    0U,	// SAR16ri
    0U,	// SAR32m1
    0U,	// SAR32mCL
    0U,	// SAR32mi
    0U,	// SAR32r1
    0U,	// SAR32rCL
    0U,	// SAR32ri
    0U,	// SAR64m1
    0U,	// SAR64mCL
    0U,	// SAR64mi
    0U,	// SAR64r1
    0U,	// SAR64rCL
    0U,	// SAR64ri
    0U,	// SAR8m1
    0U,	// SAR8mCL
    0U,	// SAR8mi
    0U,	// SAR8r1
    0U,	// SAR8rCL
    0U,	// SAR8ri
    0U,	// SARX32rm
    0U,	// SARX32rr
    0U,	// SARX64rm
    0U,	// SARX64rr
    0U,	// SBB16i16
    0U,	// SBB16mi
    0U,	// SBB16mi8
    0U,	// SBB16mr
    0U,	// SBB16ri
    0U,	// SBB16ri8
    0U,	// SBB16rm
    0U,	// SBB16rr
    0U,	// SBB16rr_REV
    0U,	// SBB32i32
    0U,	// SBB32mi
    0U,	// SBB32mi8
    0U,	// SBB32mr
    0U,	// SBB32ri
    0U,	// SBB32ri8
    0U,	// SBB32rm
    0U,	// SBB32rr
    0U,	// SBB32rr_REV
    0U,	// SBB64i32
    0U,	// SBB64mi32
    0U,	// SBB64mi8
    0U,	// SBB64mr
    0U,	// SBB64ri32
    0U,	// SBB64ri8
    0U,	// SBB64rm
    0U,	// SBB64rr
    0U,	// SBB64rr_REV
    0U,	// SBB8i8
    0U,	// SBB8mi
    0U,	// SBB8mi8
    0U,	// SBB8mr
    0U,	// SBB8ri
    0U,	// SBB8ri8
    0U,	// SBB8rm
    0U,	// SBB8rr
    0U,	// SBB8rr_REV
    0U,	// SCASB
    0U,	// SCASL
    0U,	// SCASQ
    0U,	// SCASW
    0U,	// SEG_ALLOCA_32
    0U,	// SEG_ALLOCA_64
    0U,	// SEH_EndPrologue
    0U,	// SEH_Epilogue
    0U,	// SEH_PushFrame
    0U,	// SEH_PushReg
    0U,	// SEH_SaveReg
    0U,	// SEH_SaveXMM
    0U,	// SEH_SetFrame
    0U,	// SEH_StackAlloc
    0U,	// SETAEm
    0U,	// SETAEr
    0U,	// SETAm
    0U,	// SETAr
    0U,	// SETBEm
    0U,	// SETBEr
    0U,	// SETB_C16r
    0U,	// SETB_C32r
    0U,	// SETB_C64r
    0U,	// SETB_C8r
    0U,	// SETBm
    0U,	// SETBr
    0U,	// SETEm
    0U,	// SETEr
    0U,	// SETGEm
    0U,	// SETGEr
    0U,	// SETGm
    0U,	// SETGr
    0U,	// SETLEm
    0U,	// SETLEr
    0U,	// SETLm
    0U,	// SETLr
    0U,	// SETNEm
    0U,	// SETNEr
    0U,	// SETNOm
    0U,	// SETNOr
    0U,	// SETNPm
    0U,	// SETNPr
    0U,	// SETNSm
    0U,	// SETNSr
    0U,	// SETOm
    0U,	// SETOr
    0U,	// SETPm
    0U,	// SETPr
    0U,	// SETSm
    0U,	// SETSr
    0U,	// SGDT16m
    0U,	// SGDT32m
    0U,	// SGDT64m
    0U,	// SHL16m1
    0U,	// SHL16mCL
    0U,	// SHL16mi
    0U,	// SHL16r1
    0U,	// SHL16rCL
    0U,	// SHL16ri
    0U,	// SHL32m1
    0U,	// SHL32mCL
    0U,	// SHL32mi
    0U,	// SHL32r1
    0U,	// SHL32rCL
    0U,	// SHL32ri
    0U,	// SHL64m1
    0U,	// SHL64mCL
    0U,	// SHL64mi
    0U,	// SHL64r1
    0U,	// SHL64rCL
    0U,	// SHL64ri
    0U,	// SHL8m1
    0U,	// SHL8mCL
    0U,	// SHL8mi
    0U,	// SHL8r1
    0U,	// SHL8rCL
    0U,	// SHL8ri
    0U,	// SHLD16mrCL
    0U,	// SHLD16mri8
    0U,	// SHLD16rrCL
    0U,	// SHLD16rri8
    0U,	// SHLD32mrCL
    0U,	// SHLD32mri8
    0U,	// SHLD32rrCL
    0U,	// SHLD32rri8
    0U,	// SHLD64mrCL
    0U,	// SHLD64mri8
    0U,	// SHLD64rrCL
    0U,	// SHLD64rri8
    0U,	// SHLX32rm
    0U,	// SHLX32rr
    0U,	// SHLX64rm
    0U,	// SHLX64rr
    0U,	// SHR16m1
    0U,	// SHR16mCL
    0U,	// SHR16mi
    0U,	// SHR16r1
    0U,	// SHR16rCL
    0U,	// SHR16ri
    0U,	// SHR32m1
    0U,	// SHR32mCL
    0U,	// SHR32mi
    0U,	// SHR32r1
    0U,	// SHR32rCL
    0U,	// SHR32ri
    0U,	// SHR64m1
    0U,	// SHR64mCL
    0U,	// SHR64mi
    0U,	// SHR64r1
    0U,	// SHR64rCL
    0U,	// SHR64ri
    0U,	// SHR8m1
    0U,	// SHR8mCL
    0U,	// SHR8mi
    0U,	// SHR8r1
    0U,	// SHR8rCL
    0U,	// SHR8ri
    0U,	// SHRD16mrCL
    0U,	// SHRD16mri8
    0U,	// SHRD16rrCL
    0U,	// SHRD16rri8
    0U,	// SHRD32mrCL
    0U,	// SHRD32mri8
    0U,	// SHRD32rrCL
    0U,	// SHRD32rri8
    0U,	// SHRD64mrCL
    0U,	// SHRD64mri8
    0U,	// SHRD64rrCL
    0U,	// SHRD64rri8
    0U,	// SHRX32rm
    0U,	// SHRX32rr
    0U,	// SHRX64rm
    0U,	// SHRX64rr
    0U,	// SIDT16m
    0U,	// SIDT32m
    0U,	// SIDT64m
    0U,	// SKINIT
    0U,	// SLDT16m
    0U,	// SLDT16r
    0U,	// SLDT32r
    0U,	// SLDT64m
    0U,	// SLDT64r
    0U,	// SMSW16m
    0U,	// SMSW16r
    0U,	// SMSW32r
    0U,	// SMSW64r
    0U,	// STAC
    0U,	// STC
    0U,	// STD
    0U,	// STGI
    0U,	// STI
    0U,	// STOSB
    0U,	// STOSL
    0U,	// STOSQ
    0U,	// STOSW
    0U,	// STR16r
    0U,	// STR32r
    0U,	// STR64r
    0U,	// STRm
    0U,	// SUB16i16
    0U,	// SUB16mi
    0U,	// SUB16mi8
    0U,	// SUB16mr
    0U,	// SUB16ri
    0U,	// SUB16ri8
    0U,	// SUB16rm
    0U,	// SUB16rr
    0U,	// SUB16rr_REV
    0U,	// SUB32i32
    0U,	// SUB32mi
    0U,	// SUB32mi8
    0U,	// SUB32mr
    0U,	// SUB32ri
    0U,	// SUB32ri8
    0U,	// SUB32rm
    0U,	// SUB32rr
    0U,	// SUB32rr_REV
    0U,	// SUB64i32
    0U,	// SUB64mi32
    0U,	// SUB64mi8
    0U,	// SUB64mr
    0U,	// SUB64ri32
    0U,	// SUB64ri8
    0U,	// SUB64rm
    0U,	// SUB64rr
    0U,	// SUB64rr_REV
    0U,	// SUB8i8
    0U,	// SUB8mi
    0U,	// SUB8mi8
    0U,	// SUB8mr
    0U,	// SUB8ri
    0U,	// SUB8ri8
    0U,	// SUB8rm
    0U,	// SUB8rr
    0U,	// SUB8rr_REV
    0U,	// SWAPGS
    0U,	// SYSCALL
    0U,	// SYSENTER
    0U,	// SYSEXIT
    0U,	// SYSEXIT64
    0U,	// SYSRET
    0U,	// SYSRET64
    0U,	// T1MSKC32rm
    0U,	// T1MSKC32rr
    0U,	// T1MSKC64rm
    0U,	// T1MSKC64rr
    0U,	// TAILJMPd
    0U,	// TAILJMPd64
    0U,	// TAILJMPd64_REX
    0U,	// TAILJMPm
    0U,	// TAILJMPm64
    0U,	// TAILJMPm64_REX
    0U,	// TAILJMPr
    0U,	// TAILJMPr64
    0U,	// TAILJMPr64_REX
    0U,	// TCRETURNdi
    0U,	// TCRETURNdi64
    0U,	// TCRETURNmi
    0U,	// TCRETURNmi64
    0U,	// TCRETURNri
    0U,	// TCRETURNri64
    0U,	// TEST16i16
    0U,	// TEST16mi
    0U,	// TEST16mi_alt
    0U,	// TEST16ri
    0U,	// TEST16ri_alt
    0U,	// TEST16rm
    0U,	// TEST16rr
    0U,	// TEST32i32
    0U,	// TEST32mi
    0U,	// TEST32mi_alt
    0U,	// TEST32ri
    0U,	// TEST32ri_alt
    0U,	// TEST32rm
    0U,	// TEST32rr
    0U,	// TEST64i32
    0U,	// TEST64mi32
    0U,	// TEST64mi32_alt
    0U,	// TEST64ri32
    0U,	// TEST64ri32_alt
    0U,	// TEST64rm
    0U,	// TEST64rr
    0U,	// TEST8i8
    0U,	// TEST8mi
    0U,	// TEST8mi_alt
    0U,	// TEST8ri
    0U,	// TEST8ri_NOREX
    0U,	// TEST8ri_alt
    0U,	// TEST8rm
    0U,	// TEST8rr
    0U,	// TLSCall_32
    0U,	// TLSCall_64
    0U,	// TLS_addr32
    0U,	// TLS_addr64
    0U,	// TLS_base_addr32
    0U,	// TLS_base_addr64
    0U,	// TRAP
    0U,	// TZCNT16rm
    0U,	// TZCNT16rr
    0U,	// TZCNT32rm
    0U,	// TZCNT32rr
    0U,	// TZCNT64rm
    0U,	// TZCNT64rr
    0U,	// TZMSK32rm
    0U,	// TZMSK32rr
    0U,	// TZMSK64rm
    0U,	// TZMSK64rr
    0U,	// UD2B
    1U,	// VAARG_64
    0U,	// VASTART_SAVE_XMM_REGS
    0U,	// VERRm
    0U,	// VERRr
    0U,	// VERWm
    0U,	// VERWr
    0U,	// VMCALL
    0U,	// VMCLEARm
    0U,	// VMFUNC
    0U,	// VMLAUNCH
    0U,	// VMLOAD32
    0U,	// VMLOAD64
    0U,	// VMMCALL
    0U,	// VMPTRLDm
    0U,	// VMPTRSTm
    0U,	// VMREAD32rm
    0U,	// VMREAD32rr
    0U,	// VMREAD64rm
    0U,	// VMREAD64rr
    0U,	// VMRESUME
    0U,	// VMRUN32
    0U,	// VMRUN64
    0U,	// VMSAVE32
    0U,	// VMSAVE64
    0U,	// VMWRITE32rm
    0U,	// VMWRITE32rr
    0U,	// VMWRITE64rm
    0U,	// VMWRITE64rr
    0U,	// VMXOFF
    0U,	// VMXON
    0U,	// WBINVD
    0U,	// WIN_ALLOCA
    0U,	// WIN_FTOL_32
    0U,	// WIN_FTOL_64
    0U,	// WRFSBASE
    0U,	// WRFSBASE64
    0U,	// WRGSBASE
    0U,	// WRGSBASE64
    0U,	// WRMSR
    0U,	// XADD16rm
    0U,	// XADD16rr
    0U,	// XADD32rm
    0U,	// XADD32rr
    0U,	// XADD64rm
    0U,	// XADD64rr
    0U,	// XADD8rm
    0U,	// XADD8rr
    0U,	// XCHG16ar
    0U,	// XCHG16rm
    0U,	// XCHG16rr
    0U,	// XCHG32ar
    0U,	// XCHG32ar64
    0U,	// XCHG32rm
    0U,	// XCHG32rr
    0U,	// XCHG64ar
    0U,	// XCHG64rm
    0U,	// XCHG64rr
    0U,	// XCHG8rm
    0U,	// XCHG8rr
    0U,	// XCRYPTCBC
    0U,	// XCRYPTCFB
    0U,	// XCRYPTCTR
    0U,	// XCRYPTECB
    0U,	// XCRYPTOFB
    0U,	// XGETBV
    0U,	// XLAT
    0U,	// XOR16i16
    0U,	// XOR16mi
    0U,	// XOR16mi8
    0U,	// XOR16mr
    0U,	// XOR16ri
    0U,	// XOR16ri8
    0U,	// XOR16rm
    0U,	// XOR16rr
    0U,	// XOR16rr_REV
    0U,	// XOR32i32
    0U,	// XOR32mi
    0U,	// XOR32mi8
    0U,	// XOR32mr
    0U,	// XOR32ri
    0U,	// XOR32ri8
    0U,	// XOR32rm
    0U,	// XOR32rr
    0U,	// XOR32rr_REV
    0U,	// XOR64i32
    0U,	// XOR64mi32
    0U,	// XOR64mi8
    0U,	// XOR64mr
    0U,	// XOR64ri32
    0U,	// XOR64ri8
    0U,	// XOR64rm
    0U,	// XOR64rr
    0U,	// XOR64rr_REV
    0U,	// XOR8i8
    0U,	// XOR8mi
    0U,	// XOR8mi8
    0U,	// XOR8mr
    0U,	// XOR8ri
    0U,	// XOR8ri8
    0U,	// XOR8rm
    0U,	// XOR8rr
    0U,	// XOR8rr_REV
    0U,	// XRSTOR
    0U,	// XRSTOR64
    0U,	// XRSTORS
    0U,	// XRSTORS64
    0U,	// XSAVE
    0U,	// XSAVE64
    0U,	// XSAVEC
    0U,	// XSAVEC64
    0U,	// XSAVEOPT
    0U,	// XSAVEOPT64
    0U,	// XSAVES
    0U,	// XSAVES64
    0U,	// XSETBV
    0U,	// XSHA1
    0U,	// XSHA256
    0U,	// XSTORE
    0U
  };

#ifndef CAPSTONE_DIET
  static char AsmStrs[] = {
  /* 0 */ 'x', 's', 'a', 'v', 'e', 'c', '6', '4', 9, 0,
  /* 10 */ 'x', 's', 'a', 'v', 'e', '6', '4', 9, 0,
  /* 19 */ 'x', 'r', 's', 't', 'o', 'r', '6', '4', 9, 0,
  /* 29 */ 'x', 's', 'a', 'v', 'e', 's', '6', '4', 9, 0,
  /* 39 */ 'x', 'r', 's', 't', 'o', 'r', 's', '6', '4', 9, 0,
  /* 50 */ 'x', 's', 'a', 'v', 'e', 'o', 'p', 't', '6', '4', 9, 0,
  /* 62 */ 'l', 'e', 'a', 9, 0,
  /* 67 */ 'j', 'a', 9, 0,
  /* 71 */ 's', 'e', 't', 'a', 9, 0,
  /* 77 */ 'c', 'm', 'o', 'v', 'a', 9, 0,
  /* 84 */ 'c', 'm', 'p', 'x', 'c', 'h', 'g', '1', '6', 'b', 9, 0,
  /* 96 */ 'c', 'm', 'p', 'x', 'c', 'h', 'g', '8', 'b', 9, 0,
  /* 107 */ 's', 'b', 'b', 9, 0,
  /* 112 */ 'j', 'b', 9, 0,
  /* 116 */ 'i', 'n', 's', 'b', 9, 0,
  /* 122 */ 's', 't', 'o', 's', 'b', 9, 0,
  /* 129 */ 'c', 'm', 'p', 's', 'b', 9, 0,
  /* 136 */ 'm', 'o', 'v', 's', 'b', 9, 0,
  /* 143 */ 's', 'e', 't', 'b', 9, 0,
  /* 149 */ 's', 'u', 'b', 9, 0,
  /* 154 */ 'c', 'm', 'o', 'v', 'b', 9, 0,
  /* 161 */ 'c', 'l', 'w', 'b', 9, 0,
  /* 167 */ 'a', 'd', 'c', 9, 0,
  /* 172 */ 'd', 'e', 'c', 9, 0,
  /* 177 */ 'x', 's', 'a', 'v', 'e', 'c', 9, 0,
  /* 185 */ 'b', 'l', 'c', 'i', 'c', 9, 0,
  /* 192 */ 'b', 'l', 's', 'i', 'c', 9, 0,
  /* 199 */ 't', '1', 'm', 's', 'k', 'c', 9, 0,
  /* 207 */ 'i', 'n', 'c', 9, 0,
  /* 212 */ 'b', 't', 'c', 9, 0,
  /* 217 */ 'a', 'a', 'd', 9, 0,
  /* 222 */ 'v', 'm', 'r', 'e', 'a', 'd', 9, 0,
  /* 230 */ 'x', 'a', 'd', 'd', 9, 0,
  /* 236 */ 'r', 'd', 's', 'e', 'e', 'd', 9, 0,
  /* 244 */ 'i', 'n', 'v', 'p', 'c', 'i', 'd', 9, 0,
  /* 253 */ 'i', 'n', 'v', 'v', 'p', 'i', 'd', 9, 0,
  /* 262 */ 's', 'h', 'l', 'd', 9, 0,
  /* 268 */ 'v', 'm', 'p', 't', 'r', 'l', 'd', 9, 0,
  /* 277 */ 'r', 'd', 'r', 'a', 'n', 'd', 9, 0,
  /* 285 */ 'b', 'o', 'u', 'n', 'd', 9, 0,
  /* 292 */ 's', 'h', 'r', 'd', 9, 0,
  /* 298 */ 'i', 'n', 's', 'd', 9, 0,
  /* 304 */ 's', 't', 'o', 's', 'd', 9, 0,
  /* 311 */ 'c', 'm', 'p', 's', 'd', 9, 0,
  /* 318 */ 'm', 'o', 'v', 's', 'd', 9, 0,
  /* 325 */ 'm', 'o', 'v', 's', 'x', 'd', 9, 0,
  /* 333 */ 'j', 'a', 'e', 9, 0,
  /* 338 */ 's', 'e', 't', 'a', 'e', 9, 0,
  /* 345 */ 'c', 'm', 'o', 'v', 'a', 'e', 9, 0,
  /* 353 */ 'j', 'b', 'e', 9, 0,
  /* 358 */ 's', 'e', 't', 'b', 'e', 9, 0,
  /* 365 */ 'c', 'm', 'o', 'v', 'b', 'e', 9, 0,
  /* 373 */ 'j', 'g', 'e', 9, 0,
  /* 378 */ 's', 'e', 't', 'g', 'e', 9, 0,
  /* 385 */ 'c', 'm', 'o', 'v', 'g', 'e', 9, 0,
  /* 393 */ 'j', 'e', 9, 0,
  /* 397 */ 'j', 'l', 'e', 9, 0,
  /* 402 */ 's', 'e', 't', 'l', 'e', 9, 0,
  /* 409 */ 'c', 'm', 'o', 'v', 'l', 'e', 9, 0,
  /* 417 */ 'j', 'n', 'e', 9, 0,
  /* 422 */ 'l', 'o', 'o', 'p', 'n', 'e', 9, 0,
  /* 430 */ 's', 'e', 't', 'n', 'e', 9, 0,
  /* 437 */ 'c', 'm', 'o', 'v', 'n', 'e', 9, 0,
  /* 445 */ 'l', 'o', 'o', 'p', 'e', 9, 0,
  /* 452 */ 'r', 'd', 'f', 's', 'b', 'a', 's', 'e', 9, 0,
  /* 462 */ 'w', 'r', 'f', 's', 'b', 'a', 's', 'e', 9, 0,
  /* 472 */ 'r', 'd', 'g', 's', 'b', 'a', 's', 'e', 9, 0,
  /* 482 */ 'w', 'r', 'g', 's', 'b', 'a', 's', 'e', 9, 0,
  /* 492 */ 's', 'e', 't', 'e', 9, 0,
  /* 498 */ 'v', 'm', 'w', 'r', 'i', 't', 'e', 9, 0,
  /* 507 */ 'x', 's', 'a', 'v', 'e', 9, 0,
  /* 514 */ 'c', 'm', 'o', 'v', 'e', 9, 0,
  /* 521 */ 'b', 's', 'f', 9, 0,
  /* 526 */ 'r', 'e', 't', 'f', 9, 0,
  /* 532 */ 'n', 'e', 'g', 9, 0,
  /* 537 */ 'c', 'm', 'p', 'x', 'c', 'h', 'g', 9, 0,
  /* 546 */ 'j', 'g', 9, 0,
  /* 550 */ 'i', 'n', 'v', 'l', 'p', 'g', 9, 0,
  /* 558 */ 's', 'e', 't', 'g', 9, 0,
  /* 564 */ 'c', 'm', 'o', 'v', 'g', 9, 0,
  /* 571 */ 'p', 'u', 's', 'h', 9, 0,
  /* 577 */ 'b', 'l', 'c', 'i', 9, 0,
  /* 583 */ 'b', 'z', 'h', 'i', 9, 0,
  /* 589 */ 'b', 'l', 's', 'i', 9, 0,
  /* 595 */ 'b', 'l', 'c', 'm', 's', 'k', 9, 0,
  /* 603 */ 'b', 'l', 's', 'm', 's', 'k', 9, 0,
  /* 611 */ 't', 'z', 'm', 's', 'k', 9, 0,
  /* 618 */ 's', 'a', 'l', 9, 0,
  /* 623 */ 'r', 'c', 'l', 9, 0,
  /* 628 */ 's', 'h', 'l', 9, 0,
  /* 633 */ 'j', 'l', 9, 0,
  /* 637 */ 'l', 'c', 'a', 'l', 'l', 9, 0,
  /* 644 */ 'b', 'l', 'c', 'f', 'i', 'l', 'l', 9, 0,
  /* 653 */ 'b', 'l', 's', 'f', 'i', 'l', 'l', 9, 0,
  /* 662 */ 'r', 'o', 'l', 9, 0,
  /* 667 */ 'a', 'r', 'p', 'l', 9, 0,
  /* 673 */ 'l', 's', 'l', 9, 0,
  /* 678 */ 's', 'e', 't', 'l', 9, 0,
  /* 684 */ 'i', 'm', 'u', 'l', 9, 0,
  /* 690 */ 'c', 'm', 'o', 'v', 'l', 9, 0,
  /* 697 */ 'a', 'a', 'm', 9, 0,
  /* 702 */ 'a', 'n', 'd', 'n', 9, 0,
  /* 708 */ 'v', 'm', 'x', 'o', 'n', 9, 0,
  /* 715 */ 'j', 'o', 9, 0,
  /* 719 */ 'j', 'n', 'o', 9, 0,
  /* 724 */ 's', 'e', 't', 'n', 'o', 9, 0,
  /* 731 */ 'c', 'm', 'o', 'v', 'n', 'o', 9, 0,
  /* 739 */ 's', 'e', 't', 'o', 9, 0,
  /* 745 */ 'c', 'm', 'o', 'v', 'o', 9, 0,
  /* 752 */ 'b', 's', 'w', 'a', 'p', 9, 0,
  /* 759 */ 'p', 'd', 'e', 'p', 9, 0,
  /* 765 */ 'j', 'p', 9, 0,
  /* 769 */ 'c', 'm', 'p', 9, 0,
  /* 774 */ 'r', 'e', 'x', '6', '4', 32, 'j', 'm', 'p', 9, 0,
  /* 785 */ 'l', 'j', 'm', 'p', 9, 0,
  /* 791 */ 'j', 'n', 'p', 9, 0,
  /* 796 */ 's', 'e', 't', 'n', 'p', 9, 0,
  /* 803 */ 'c', 'm', 'o', 'v', 'n', 'p', 9, 0,
  /* 811 */ 'n', 'o', 'p', 9, 0,
  /* 816 */ 'l', 'o', 'o', 'p', 9, 0,
  /* 822 */ 'p', 'o', 'p', 9, 0,
  /* 827 */ 's', 'e', 't', 'p', 9, 0,
  /* 833 */ '#', 'E', 'H', '_', 'S', 'j', 'L', 'j', '_', 'S', 'e', 't', 'u', 'p', 9, 0,
  /* 849 */ 'c', 'm', 'o', 'v', 'p', 9, 0,
  /* 856 */ 'r', 'e', 't', 'f', 'q', 9, 0,
  /* 863 */ 's', 't', 'o', 's', 'q', 9, 0,
  /* 870 */ 'c', 'm', 'p', 's', 'q', 9, 0,
  /* 877 */ 'm', 'o', 'v', 's', 'q', 9, 0,
  /* 884 */ 'v', 'm', 'c', 'l', 'e', 'a', 'r', 9, 0,
  /* 893 */ 'l', 'a', 'r', 9, 0,
  /* 898 */ 's', 'a', 'r', 9, 0,
  /* 903 */ 'r', 'c', 'r', 9, 0,
  /* 908 */ 'e', 'n', 't', 'e', 'r', 9, 0,
  /* 915 */ 's', 'h', 'r', 9, 0,
  /* 920 */ 'r', 'o', 'r', 9, 0,
  /* 925 */ 'x', 'r', 's', 't', 'o', 'r', 9, 0,
  /* 933 */ 'x', 'o', 'r', 9, 0,
  /* 938 */ 'v', 'e', 'r', 'r', 9, 0,
  /* 944 */ 'b', 's', 'r', 9, 0,
  /* 949 */ 'b', 'l', 's', 'r', 9, 0,
  /* 955 */ 'b', 't', 'r', 9, 0,
  /* 960 */ 'l', 't', 'r', 9, 0,
  /* 965 */ 's', 't', 'r', 9, 0,
  /* 970 */ 'b', 'e', 'x', 't', 'r', 9, 0,
  /* 977 */ 'm', 'o', 'v', 'a', 'b', 's', 9, 0,
  /* 985 */ 'b', 'l', 'c', 's', 9, 0,
  /* 991 */ 'l', 'd', 's', 9, 0,
  /* 996 */ 'l', 'e', 's', 9, 0,
  /* 1001 */ 'x', 's', 'a', 'v', 'e', 's', 9, 0,
  /* 1009 */ 'l', 'f', 's', 9, 0,
  /* 1014 */ 'l', 'g', 's', 9, 0,
  /* 1019 */ 'j', 's', 9, 0,
  /* 1023 */ 'j', 'n', 's', 9, 0,
  /* 1028 */ 's', 'e', 't', 'n', 's', 9, 0,
  /* 1035 */ 'c', 'm', 'o', 'v', 'n', 's', 9, 0,
  /* 1043 */ 'x', 'r', 's', 't', 'o', 'r', 's', 9, 0,
  /* 1052 */ 'l', 's', 's', 9, 0,
  /* 1057 */ 'b', 't', 's', 9, 0,
  /* 1062 */ 's', 'e', 't', 's', 9, 0,
  /* 1068 */ 'c', 'm', 'o', 'v', 's', 9, 0,
  /* 1075 */ 'b', 't', 9, 0,
  /* 1079 */ 'l', 'g', 'd', 't', 9, 0,
  /* 1085 */ 's', 'g', 'd', 't', 9, 0,
  /* 1091 */ 'l', 'i', 'd', 't', 9, 0,
  /* 1097 */ 's', 'i', 'd', 't', 9, 0,
  /* 1103 */ 'l', 'l', 'd', 't', 9, 0,
  /* 1109 */ 's', 'l', 'd', 't', 9, 0,
  /* 1115 */ 'r', 'e', 't', 9, 0,
  /* 1120 */ 'l', 'z', 'c', 'n', 't', 9, 0,
  /* 1127 */ 't', 'z', 'c', 'n', 't', 9, 0,
  /* 1134 */ 'i', 'n', 't', 9, 0,
  /* 1139 */ 'n', 'o', 't', 9, 0,
  /* 1144 */ 'i', 'n', 'v', 'e', 'p', 't', 9, 0,
  /* 1152 */ 'x', 's', 'a', 'v', 'e', 'o', 'p', 't', 9, 0,
  /* 1162 */ 'c', 'l', 'f', 'l', 'u', 's', 'h', 'o', 'p', 't', 9, 0,
  /* 1174 */ 't', 'e', 's', 't', 9, 0,
  /* 1180 */ 'v', 'm', 'p', 't', 'r', 's', 't', 9, 0,
  /* 1189 */ 'o', 'u', 't', 9, 0,
  /* 1194 */ 'p', 'e', 'x', 't', 9, 0,
  /* 1200 */ 'i', 'd', 'i', 'v', 9, 0,
  /* 1206 */ 'm', 'o', 'v', 9, 0,
  /* 1211 */ 'v', 'e', 'r', 'w', 9, 0,
  /* 1217 */ 'l', 'm', 's', 'w', 9, 0,
  /* 1223 */ 's', 'm', 's', 'w', 9, 0,
  /* 1229 */ 'i', 'n', 's', 'w', 9, 0,
  /* 1235 */ 's', 't', 'o', 's', 'w', 9, 0,
  /* 1242 */ 'c', 'm', 'p', 's', 'w', 9, 0,
  /* 1249 */ 'm', 'o', 'v', 's', 'w', 9, 0,
  /* 1256 */ 'a', 'd', 'c', 'x', 9, 0,
  /* 1262 */ 's', 'h', 'l', 'x', 9, 0,
  /* 1268 */ 'm', 'u', 'l', 'x', 9, 0,
  /* 1274 */ 'a', 'd', 'o', 'x', 9, 0,
  /* 1280 */ 's', 'a', 'r', 'x', 9, 0,
  /* 1286 */ 's', 'h', 'r', 'x', 9, 0,
  /* 1292 */ 'r', 'o', 'r', 'x', 9, 0,
  /* 1298 */ 'm', 'o', 'v', 's', 'x', 9, 0,
  /* 1305 */ 'm', 'o', 'v', 'z', 'x', 9, 0,
  /* 1312 */ 'j', 'e', 'c', 'x', 'z', 9, 0,
  /* 1319 */ 'j', 'c', 'x', 'z', 9, 0,
  /* 1325 */ 'j', 'r', 'c', 'x', 'z', 9, 0,
  /* 1332 */ 's', 'b', 'b', 9, 'a', 'l', ',', 32, 0,
  /* 1341 */ 's', 'c', 'a', 's', 'b', 9, 'a', 'l', ',', 32, 0,
  /* 1352 */ 'l', 'o', 'd', 's', 'b', 9, 'a', 'l', ',', 32, 0,
  /* 1363 */ 's', 'u', 'b', 9, 'a', 'l', ',', 32, 0,
  /* 1372 */ 'a', 'd', 'c', 9, 'a', 'l', ',', 32, 0,
  /* 1381 */ 'a', 'd', 'd', 9, 'a', 'l', ',', 32, 0,
  /* 1390 */ 'a', 'n', 'd', 9, 'a', 'l', ',', 32, 0,
  /* 1399 */ 'i', 'n', 9, 'a', 'l', ',', 32, 0,
  /* 1407 */ 'c', 'm', 'p', 9, 'a', 'l', ',', 32, 0,
  /* 1416 */ 'x', 'o', 'r', 9, 'a', 'l', ',', 32, 0,
  /* 1425 */ 'm', 'o', 'v', 'a', 'b', 's', 9, 'a', 'l', ',', 32, 0,
  /* 1437 */ 't', 'e', 's', 't', 9, 'a', 'l', ',', 32, 0,
  /* 1447 */ 'm', 'o', 'v', 9, 'a', 'l', ',', 32, 0,
  /* 1456 */ 's', 'b', 'b', 9, 'a', 'x', ',', 32, 0,
  /* 1465 */ 's', 'u', 'b', 9, 'a', 'x', ',', 32, 0,
  /* 1474 */ 'a', 'd', 'c', 9, 'a', 'x', ',', 32, 0,
  /* 1483 */ 'a', 'd', 'd', 9, 'a', 'x', ',', 32, 0,
  /* 1492 */ 'a', 'n', 'd', 9, 'a', 'x', ',', 32, 0,
  /* 1501 */ 'x', 'c', 'h', 'g', 9, 'a', 'x', ',', 32, 0,
  /* 1511 */ 'i', 'n', 9, 'a', 'x', ',', 32, 0,
  /* 1519 */ 'c', 'm', 'p', 9, 'a', 'x', ',', 32, 0,
  /* 1528 */ 'x', 'o', 'r', 9, 'a', 'x', ',', 32, 0,
  /* 1537 */ 'm', 'o', 'v', 'a', 'b', 's', 9, 'a', 'x', ',', 32, 0,
  /* 1549 */ 't', 'e', 's', 't', 9, 'a', 'x', ',', 32, 0,
  /* 1559 */ 'm', 'o', 'v', 9, 'a', 'x', ',', 32, 0,
  /* 1568 */ 's', 'c', 'a', 's', 'w', 9, 'a', 'x', ',', 32, 0,
  /* 1579 */ 'l', 'o', 'd', 's', 'w', 9, 'a', 'x', ',', 32, 0,
  /* 1590 */ 's', 'b', 'b', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1600 */ 's', 'u', 'b', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1610 */ 'a', 'd', 'c', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1620 */ 'a', 'd', 'd', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1630 */ 'a', 'n', 'd', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1640 */ 's', 'c', 'a', 's', 'd', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1652 */ 'l', 'o', 'd', 's', 'd', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1664 */ 'x', 'c', 'h', 'g', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1675 */ 'i', 'n', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1684 */ 'c', 'm', 'p', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1694 */ 'x', 'o', 'r', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1704 */ 'm', 'o', 'v', 'a', 'b', 's', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1717 */ 't', 'e', 's', 't', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1728 */ 'm', 'o', 'v', 9, 'e', 'a', 'x', ',', 32, 0,
  /* 1738 */ 's', 'b', 'b', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 1748 */ 's', 'u', 'b', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 1758 */ 'a', 'd', 'c', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 1768 */ 'a', 'd', 'd', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 1778 */ 'a', 'n', 'd', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 1788 */ 'x', 'c', 'h', 'g', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 1799 */ 'c', 'm', 'p', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 1809 */ 's', 'c', 'a', 's', 'q', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 1821 */ 'l', 'o', 'd', 's', 'q', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 1833 */ 'x', 'o', 'r', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 1843 */ 'm', 'o', 'v', 'a', 'b', 's', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 1856 */ 't', 'e', 's', 't', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 1867 */ 'm', 'o', 'v', 9, 'r', 'a', 'x', ',', 32, 0,
  /* 1877 */ 'o', 'u', 't', 's', 'b', 9, 'd', 'x', ',', 32, 0,
  /* 1888 */ 'o', 'u', 't', 's', 'd', 9, 'd', 'x', ',', 32, 0,
  /* 1899 */ 'o', 'u', 't', 's', 'w', 9, 'd', 'x', ',', 32, 0,
  /* 1910 */ '#', 'V', 'A', 'A', 'R', 'G', '_', '6', '4', 32, 0,
  /* 1921 */ 'r', 'e', 't', 9, '#', 'e', 'h', '_', 'r', 'e', 't', 'u', 'r', 'n', ',', 32, 'a', 'd', 'd', 'r', ':', 32, 0,
  /* 1944 */ '#', 'S', 'E', 'H', '_', 'S', 'a', 'v', 'e', 'X', 'M', 'M', 32, 0,
  /* 1958 */ '#', 'V', 'A', 'S', 'T', 'A', 'R', 'T', '_', 'S', 'A', 'V', 'E', '_', 'X', 'M', 'M', '_', 'R', 'E', 'G', 'S', 32, 0,
  /* 1982 */ '#', 'S', 'E', 'H', '_', 'S', 't', 'a', 'c', 'k', 'A', 'l', 'l', 'o', 'c', 32, 0,
  /* 1999 */ '#', 'S', 'E', 'H', '_', 'P', 'u', 's', 'h', 'F', 'r', 'a', 'm', 'e', 32, 0,
  /* 2015 */ '#', 'S', 'E', 'H', '_', 'S', 'e', 't', 'F', 'r', 'a', 'm', 'e', 32, 0,
  /* 2030 */ '#', 'S', 'E', 'H', '_', 'S', 'a', 'v', 'e', 'R', 'e', 'g', 32, 0,
  /* 2044 */ '#', 'S', 'E', 'H', '_', 'P', 'u', 's', 'h', 'R', 'e', 'g', 32, 0,
  /* 2058 */ '#', 'R', 'E', 'L', 'E', 'A', 'S', 'E', '_', 'M', 'O', 'V', 32, 'P', 'S', 'E', 'U', 'D', 'O', 32, '!', 0,
  /* 2080 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'R', 'F', 'P', '8', '0', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2101 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'V', '4', 'F', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2122 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'V', '1', '6', 'F', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2144 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'V', '8', 'F', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2165 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'R', 'F', 'P', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2186 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'F', 'R', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2206 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'G', 'R', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2226 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'V', '2', 'F', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2247 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'V', '4', 'F', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2268 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'V', '8', 'F', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2289 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'V', '2', 'I', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2310 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'V', '4', 'I', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2331 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'V', '8', 'I', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2352 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'R', 'F', 'P', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2373 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'F', 'R', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2393 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'G', 'R', '1', '6', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2413 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'G', 'R', '8', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2432 */ '#', 'R', 'E', 'L', 'E', 'A', 'S', 'E', '_', 'B', 'I', 'N', 'O', 'P', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2455 */ '#', 'R', 'E', 'L', 'E', 'A', 'S', 'E', '_', 'U', 'N', 'O', 'P', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2477 */ '#', 'A', 'C', 'Q', 'U', 'I', 'R', 'E', '_', 'M', 'O', 'V', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2498 */ '#', 'R', 'E', 'L', 'E', 'A', 'S', 'E', '_', 'M', 'O', 'V', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 2519 */ 'x', 's', 'h', 'a', '1', 0,
  /* 2525 */ 'i', 'n', 't', '1', 0,
  /* 2530 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'L', 'O', 'N', 'G', 'J', 'M', 'P', '3', '2', 0,
  /* 2549 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'S', 'E', 'T', 'J', 'M', 'P', '3', '2', 0,
  /* 2567 */ '#', 32, 'T', 'L', 'S', 'C', 'a', 'l', 'l', '_', '3', '2', 0,
  /* 2580 */ '#', 32, 'T', 'L', 'S', '_', 'a', 'd', 'd', 'r', '3', '2', 0,
  /* 2593 */ '#', 32, 'T', 'L', 'S', '_', 'b', 'a', 's', 'e', '_', 'a', 'd', 'd', 'r', '3', '2', 0,
  /* 2611 */ 'u', 'd', '2', 0,
  /* 2615 */ 'i', 'n', 't', '3', 0,
  /* 2620 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'L', 'O', 'N', 'G', 'J', 'M', 'P', '6', '4', 0,
  /* 2639 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'S', 'E', 'T', 'J', 'M', 'P', '6', '4', 0,
  /* 2657 */ '#', 32, 'T', 'L', 'S', 'C', 'a', 'l', 'l', '_', '6', '4', 0,
  /* 2670 */ '#', 32, 'T', 'L', 'S', '_', 'a', 'd', 'd', 'r', '6', '4', 0,
  /* 2683 */ '#', 32, 'T', 'L', 'S', '_', 'b', 'a', 's', 'e', '_', 'a', 'd', 'd', 'r', '6', '4', 0,
  /* 2701 */ 'r', 'e', 'x', '6', '4', 0,
  /* 2707 */ 'd', 'a', 't', 'a', '1', '6', 0,
  /* 2714 */ 'x', 's', 'h', 'a', '2', '5', '6', 0,
  /* 2722 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'E', 'N', 'D', 0,
  /* 2735 */ 'B', 'U', 'N', 'D', 'L', 'E', 0,
  /* 2742 */ 'D', 'B', 'G', '_', 'V', 'A', 'L', 'U', 'E', 0,
  /* 2752 */ '#', 'A', 'D', 'J', 'C', 'A', 'L', 'L', 'S', 'T', 'A', 'C', 'K', 'D', 'O', 'W', 'N', 0,
  /* 2770 */ '#', 'A', 'D', 'J', 'C', 'A', 'L', 'L', 'S', 'T', 'A', 'C', 'K', 'U', 'P', 0,
  /* 2786 */ '#', 'M', 'E', 'M', 'B', 'A', 'R', 'R', 'I', 'E', 'R', 0,
  /* 2798 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'S', 'T', 'A', 'R', 'T', 0,
  /* 2813 */ 'a', 'a', 'a', 0,
  /* 2817 */ 'd', 'a', 'a', 0,
  /* 2821 */ 'u', 'd', '2', 'b', 0,
  /* 2826 */ 'x', 'c', 'r', 'y', 'p', 't', 'e', 'c', 'b', 0,
  /* 2836 */ 'x', 'c', 'r', 'y', 'p', 't', 'c', 'f', 'b', 0,
  /* 2846 */ 'x', 'c', 'r', 'y', 'p', 't', 'o', 'f', 'b', 0,
  /* 2856 */ 'r', 'e', 'p', 32, 's', 't', 'o', 's', 'b', 0,
  /* 2866 */ 'r', 'e', 'p', 32, 'm', 'o', 'v', 's', 'b', 0,
  /* 2876 */ 'x', 'l', 'a', 't', 'b', 0,
  /* 2882 */ 'c', 'l', 'a', 'c', 0,
  /* 2887 */ 's', 't', 'a', 'c', 0,
  /* 2892 */ 'x', 'c', 'r', 'y', 'p', 't', 'c', 'b', 'c', 0,
  /* 2902 */ 'g', 'e', 't', 's', 'e', 'c', 0,
  /* 2909 */ 's', 'a', 'l', 'c', 0,
  /* 2914 */ 'c', 'l', 'c', 0,
  /* 2918 */ 'c', 'm', 'c', 0,
  /* 2922 */ 'r', 'd', 'p', 'm', 'c', 0,
  /* 2928 */ 'v', 'm', 'f', 'u', 'n', 'c', 0,
  /* 2935 */ 'r', 'd', 't', 's', 'c', 0,
  /* 2941 */ 's', 't', 'c', 0,
  /* 2945 */ 'p', 'u', 's', 'h', 'f', 'd', 0,
  /* 2952 */ 'p', 'o', 'p', 'f', 'd', 0,
  /* 2958 */ 'c', 'p', 'u', 'i', 'd', 0,
  /* 2964 */ 'c', 'l', 'd', 0,
  /* 2968 */ 'r', 'e', 'p', 32, 's', 't', 'o', 's', 'd', 0,
  /* 2978 */ 'r', 'e', 'p', 32, 'm', 'o', 'v', 's', 'd', 0,
  /* 2988 */ 'i', 'r', 'e', 't', 'd', 0,
  /* 2994 */ 's', 't', 'd', 0,
  /* 2998 */ 'w', 'b', 'i', 'n', 'v', 'd', 0,
  /* 3005 */ 'c', 'w', 'd', 0,
  /* 3009 */ 'c', 'w', 'd', 'e', 0,
  /* 3014 */ 'v', 'm', 'r', 'e', 's', 'u', 'm', 'e', 0,
  /* 3023 */ 'r', 'e', 'p', 'n', 'e', 0,
  /* 3029 */ 'c', 'd', 'q', 'e', 0,
  /* 3034 */ 'x', 's', 't', 'o', 'r', 'e', 0,
  /* 3041 */ '#', 'S', 'E', 'H', '_', 'E', 'p', 'i', 'l', 'o', 'g', 'u', 'e', 0,
  /* 3055 */ '#', 'S', 'E', 'H', '_', 'E', 'n', 'd', 'P', 'r', 'o', 'l', 'o', 'g', 'u', 'e', 0,
  /* 3072 */ 'l', 'e', 'a', 'v', 'e', 0,
  /* 3078 */ 'v', 'm', 'x', 'o', 'f', 'f', 0,
  /* 3085 */ 'l', 'a', 'h', 'f', 0,
  /* 3090 */ 's', 'a', 'h', 'f', 0,
  /* 3095 */ 'p', 'u', 's', 'h', 'f', 0,
  /* 3101 */ 'p', 'o', 'p', 'f', 0,
  /* 3106 */ 'r', 'e', 't', 'f', 0,
  /* 3111 */ 'v', 'm', 'l', 'a', 'u', 'n', 'c', 'h', 0,
  /* 3120 */ 'c', 'l', 'g', 'i', 0,
  /* 3125 */ 's', 't', 'g', 'i', 0,
  /* 3130 */ 'c', 'l', 'i', 0,
  /* 3134 */ 's', 't', 'i', 0,
  /* 3138 */ '#', 32, 'w', 'i', 'n', '3', '2', 32, 'f', 'p', 't', 'o', 'u', 'i', 0,
  /* 3153 */ 'l', 'o', 'c', 'k', 0,
  /* 3158 */ 'o', 'u', 't', 9, 'd', 'x', ',', 32, 'a', 'l', 0,
  /* 3169 */ 'p', 'u', 's', 'h', 'a', 'l', 0,
  /* 3176 */ 'p', 'o', 'p', 'a', 'l', 0,
  /* 3182 */ 'v', 'm', 'm', 'c', 'a', 'l', 'l', 0,
  /* 3190 */ 'v', 'm', 'c', 'a', 'l', 'l', 0,
  /* 3197 */ 's', 'y', 's', 'c', 'a', 'l', 'l', 0,
  /* 3205 */ 'm', 'o', 'n', 't', 'm', 'u', 'l', 0,
  /* 3213 */ 'f', 's', 'e', 't', 'p', 'm', 0,
  /* 3220 */ 'r', 's', 'm', 0,
  /* 3224 */ '#', 32, 'd', 'y', 'n', 'a', 'm', 'i', 'c', 32, 's', 't', 'a', 'c', 'k', 32, 'a', 'l', 'l', 'o', 'c', 'a', 't', 'i', 'o', 'n', 0,
  /* 3251 */ 'c', 'q', 'o', 0,
  /* 3255 */ 'i', 'n', 't', 'o', 0,
  /* 3260 */ 'r', 'd', 't', 's', 'c', 'p', 0,
  /* 3267 */ 'r', 'e', 'p', 0,
  /* 3271 */ 'n', 'o', 'p', 0,
  /* 3275 */ 'c', 'd', 'q', 0,
  /* 3279 */ 'p', 'u', 's', 'h', 'f', 'q', 0,
  /* 3286 */ 'p', 'o', 'p', 'f', 'q', 0,
  /* 3292 */ 'r', 'e', 't', 'f', 'q', 0,
  /* 3298 */ 'r', 'e', 'p', 32, 's', 't', 'o', 's', 'q', 0,
  /* 3308 */ 'r', 'e', 'p', 32, 'm', 'o', 'v', 's', 'q', 0,
  /* 3318 */ 'i', 'r', 'e', 't', 'q', 0,
  /* 3324 */ 's', 'y', 's', 'e', 'n', 't', 'e', 'r', 0,
  /* 3333 */ 'r', 'd', 'm', 's', 'r', 0,
  /* 3339 */ 'w', 'r', 'm', 's', 'r', 0,
  /* 3345 */ 'x', 'c', 'r', 'y', 'p', 't', 'c', 't', 'r', 0,
  /* 3355 */ 'a', 'a', 's', 0,
  /* 3359 */ 'd', 'a', 's', 0,
  /* 3363 */ 'p', 'u', 's', 'h', 9, 'c', 's', 0,
  /* 3371 */ 'p', 'u', 's', 'h', 9, 'd', 's', 0,
  /* 3379 */ 'p', 'o', 'p', 9, 'd', 's', 0,
  /* 3386 */ 'p', 'u', 's', 'h', 9, 'e', 's', 0,
  /* 3394 */ 'p', 'o', 'p', 9, 'e', 's', 0,
  /* 3401 */ 'p', 'u', 's', 'h', 9, 'f', 's', 0,
  /* 3409 */ 'p', 'o', 'p', 9, 'f', 's', 0,
  /* 3416 */ 'p', 'u', 's', 'h', 9, 'g', 's', 0,
  /* 3424 */ 'p', 'o', 'p', 9, 'g', 's', 0,
  /* 3431 */ 's', 'w', 'a', 'p', 'g', 's', 0,
  /* 3438 */ '#', 32, 'v', 'a', 'r', 'i', 'a', 'b', 'l', 'e', 32, 's', 'i', 'z', 'e', 'd', 32, 'a', 'l', 'l', 'o', 'c', 'a', 32, 'f', 'o', 'r', 32, 's', 'e', 'g', 'm', 'e', 'n', 't', 'e', 'd', 32, 's', 't', 'a', 'c', 'k', 's', 0,
  /* 3483 */ 'p', 'u', 's', 'h', 9, 's', 's', 0,
  /* 3491 */ 'p', 'o', 'p', 9, 's', 's', 0,
  /* 3498 */ 'c', 'l', 't', 's', 0,
  /* 3503 */ 'i', 'r', 'e', 't', 0,
  /* 3508 */ 's', 'y', 's', 'r', 'e', 't', 0,
  /* 3515 */ 'p', 'c', 'o', 'm', 'm', 'i', 't', 0,
  /* 3523 */ 's', 'y', 's', 'e', 'x', 'i', 't', 0,
  /* 3531 */ 'h', 'l', 't', 0,
  /* 3535 */ 'x', 'g', 'e', 't', 'b', 'v', 0,
  /* 3542 */ 'x', 's', 'e', 't', 'b', 'v', 0,
  /* 3549 */ 'p', 'u', 's', 'h', 'a', 'w', 0,
  /* 3556 */ 'p', 'o', 'p', 'a', 'w', 0,
  /* 3562 */ 'c', 'b', 'w', 0,
  /* 3566 */ 'r', 'e', 'p', 32, 's', 't', 'o', 's', 'w', 0,
  /* 3576 */ 'r', 'e', 'p', 32, 'm', 'o', 'v', 's', 'w', 0,
  /* 3586 */ 'o', 'u', 't', 9, 'd', 'x', ',', 32, 'a', 'x', 0,
  /* 3597 */ 'v', 'm', 'l', 'o', 'a', 'd', 9, 'e', 'a', 'x', 0,
  /* 3608 */ 'v', 'm', 's', 'a', 'v', 'e', 9, 'e', 'a', 'x', 0,
  /* 3619 */ 'v', 'm', 'r', 'u', 'n', 9, 'e', 'a', 'x', 0,
  /* 3629 */ 's', 'k', 'i', 'n', 'i', 't', 9, 'e', 'a', 'x', 0,
  /* 3640 */ 'o', 'u', 't', 9, 'd', 'x', ',', 32, 'e', 'a', 'x', 0,
  /* 3652 */ 'v', 'm', 'l', 'o', 'a', 'd', 9, 'r', 'a', 'x', 0,
  /* 3663 */ 'v', 'm', 's', 'a', 'v', 'e', 9, 'r', 'a', 'x', 0,
  /* 3674 */ 'v', 'm', 'r', 'u', 'n', 9, 'r', 'a', 'x', 0,
  /* 3684 */ 'i', 'n', 'v', 'l', 'p', 'g', 'a', 9, 'e', 'a', 'x', ',', 32, 'e', 'c', 'x', 0,
  /* 3701 */ 'i', 'n', 'v', 'l', 'p', 'g', 'a', 9, 'r', 'a', 'x', ',', 32, 'e', 'c', 'x', 0,
  /* 3718 */ 'i', 'n', 9, 'a', 'l', ',', 32, 'd', 'x', 0,
  /* 3728 */ 'i', 'n', 9, 'a', 'x', ',', 32, 'd', 'x', 0,
  /* 3738 */ 'i', 'n', 9, 'e', 'a', 'x', ',', 32, 'd', 'x', 0,
  };
#endif

  // Emit the opcode for the instruction.
  unsigned int opcode = MCInst_getOpcode(MI);
  uint64_t Bits1 = OpInfo[opcode];
  uint64_t Bits2 = OpInfo2[opcode];
  uint64_t Bits = (Bits2 << 32) | Bits1;
  // assert(Bits != 0 && "Cannot print this instruction.");
#ifndef CAPSTONE_DIET
  SStream_concat0(O, AsmStrs+(Bits & 4095)-1);
#endif


  // Fragment 0 encoded into 6 bits for 35 unique commands.
  //printf("Frag-0: %"PRIu64"\n", (Bits >> 12) & 63);
  switch ((Bits >> 12) & 63) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // DBG_VALUE, BUNDLE, LIFETIME_START, LIFETIME_END, AAA, AAS, ACQUIRE_MOV...
    return;
    break;
  case 1:
    // AAD8i8, AAM8i8, ADC16i16, ADC16rr_REV, ADC32i32, ADC32rr_REV, ADC64i32...
    printOperand(MI, 0, O); 
    break;
  case 2:
    // ADC16mi, ADC16mi8, ADC16mr, ADD16mi, ADD16mi8, ADD16mr, AND16mi, AND16...
    printi16mem(MI, 0, O); 
    break;
  case 3:
    // ADC16ri, ADC16ri8, ADC16rm, ADC16rr, ADC32ri, ADC32ri8, ADC32rm, ADC32...
    printOperand(MI, 1, O); 
    break;
  case 4:
    // ADC32mi, ADC32mi8, ADC32mr, ADD32mi, ADD32mi8, ADD32mr, AND32mi, AND32...
    printi32mem(MI, 0, O); 
    break;
  case 5:
    // ADC64mi32, ADC64mi8, ADC64mr, ADD64mi32, ADD64mi8, ADD64mr, AND64mi32,...
    printi64mem(MI, 0, O); 
    break;
  case 6:
    // ADC8mi, ADC8mi8, ADC8mr, ADD8mi, ADD8mi8, ADD8mr, AND8mi, AND8mi8, AND...
    printi8mem(MI, 0, O); 
    break;
  case 7:
    // CALL64pcrel32, CALLpcrel16, CALLpcrel32, EH_SjLj_Setup, JAE_1, JAE_2, ...
    printPCRelImm(MI, 0, O); 
    return;
    break;
  case 8:
    // CMPSB
    printSrcIdx8(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printDstIdx8(MI, 0, O); 
    return;
    break;
  case 9:
    // CMPSL
    printSrcIdx32(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printDstIdx32(MI, 0, O); 
    return;
    break;
  case 10:
    // CMPSQ
    printSrcIdx64(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printDstIdx64(MI, 0, O); 
    return;
    break;
  case 11:
    // CMPSW
    printSrcIdx16(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printDstIdx16(MI, 0, O); 
    return;
    break;
  case 12:
    // CMPXCHG16B, LCMPXCHG16B
    printi128mem(MI, 0, O); 
    return;
    break;
  case 13:
    // FARCALL16m, FARCALL32m, FARCALL64, FARJMP16m, FARJMP32m, FARJMP64, LGD...
    printopaquemem(MI, 0, O); 
    return;
    break;
  case 14:
    // INSB, MOVSB, SCASB, STOSB
    printDstIdx8(MI, 0, O); 
    break;
  case 15:
    // INSL, MOVSL, SCASL, STOSL
    printDstIdx32(MI, 0, O); 
    break;
  case 16:
    // INSW, MOVSW, SCASW, STOSW
    printDstIdx16(MI, 0, O); 
    break;
  case 17:
    // LODSB, OUTSB
    printSrcIdx8(MI, 0, O); 
    return;
    break;
  case 18:
    // LODSL, OUTSL
    printSrcIdx32(MI, 0, O); 
    return;
    break;
  case 19:
    // LODSQ
    printSrcIdx64(MI, 0, O); 
    return;
    break;
  case 20:
    // LODSW, OUTSW
    printSrcIdx16(MI, 0, O); 
    return;
    break;
  case 21:
    // LXADD16, XCHG16rm
    printi16mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 22:
    // LXADD32, XCHG32rm
    printi32mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 23:
    // LXADD64, XCHG64rm
    printi64mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 24:
    // LXADD8, XCHG8rm
    printi8mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 25:
    // MOV16ao16, MOV16ao32, MOV16ao64, MOV16o16a, MOV16o32a, MOV16o64a
    printMemOffs16(MI, 0, O); 
    break;
  case 26:
    // MOV32ao16, MOV32ao32, MOV32ao64, MOV32o16a, MOV32o32a, MOV32o64a
    printMemOffs32(MI, 0, O); 
    break;
  case 27:
    // MOV64ao32, MOV64ao64, MOV64o32a, MOV64o64a
    printMemOffs64(MI, 0, O); 
    break;
  case 28:
    // MOV8ao16, MOV8ao32, MOV8ao64, MOV8o16a, MOV8o32a, MOV8o64a
    printMemOffs8(MI, 0, O); 
    break;
  case 29:
    // MOVSQ, SCASQ, STOSQ
    printDstIdx64(MI, 0, O); 
    break;
  case 30:
    // TEST16rm
    printi16mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 31:
    // TEST32rm
    printi32mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 32:
    // TEST64rm
    printi64mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 33:
    // TEST8rm
    printi8mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 34:
    // XCHG16rr, XCHG32rr, XCHG64rr, XCHG8rr
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  }


  // Fragment 1 encoded into 4 bits for 10 unique commands.
  //printf("Frag-1: %"PRIu64"\n", (Bits >> 18) & 15);
  switch ((Bits >> 18) & 15) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // AAD8i8, AAM8i8, ADC16i16, ADC32i32, ADC64i32, ADC8i8, ADD16i16, ADD32i...
    return;
    break;
  case 1:
    // ADC16mi, ADC16mi8, ADC16mr, ADC16ri, ADC16ri8, ADC16rm, ADC16rr, ADC16...
    SStream_concat0(O, ", "); 
    break;
  case 2:
    // FARCALL16i, FARCALL32i, FARJMP16i, FARJMP32i
    SStream_concat0(O, ":"); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 3:
    // INSB, INSL, INSW
    SStream_concat0(O, ", dx"); 
    op_addReg(MI, X86_REG_DX);
    return;
    break;
  case 4:
    // MOV16o16a, MOV16o32a, MOV16o64a, OUT16ir, STOSW
    SStream_concat0(O, ", ax"); 
    op_addReg(MI, X86_REG_AX);
    return;
    break;
  case 5:
    // MOV32o16a, MOV32o32a, MOV32o64a, OUT32ir, STOSL
    SStream_concat0(O, ", eax"); 
    op_addReg(MI, X86_REG_EAX);
    return;
    break;
  case 6:
    // MOV64o32a, MOV64o64a, STOSQ
    SStream_concat0(O, ", rax"); 
    op_addReg(MI, X86_REG_RAX);
    return;
    break;
  case 7:
    // MOV8o16a, MOV8o32a, MOV8o64a, OUT8ir, STOSB
    SStream_concat0(O, ", al"); 
    op_addReg(MI, X86_REG_AL);
    return;
    break;
  case 8:
    // RCL16m1, RCL16r1, RCL32m1, RCL32r1, RCL64m1, RCL64r1, RCL8m1, RCL8r1, ...
    SStream_concat0(O, ", 1"); 
    op_addImm(MI, 1);
    return;
    break;
  case 9:
    // RCL16mCL, RCL16rCL, RCL32mCL, RCL32rCL, RCL64mCL, RCL64rCL, RCL8mCL, R...
    SStream_concat0(O, ", cl"); 
    op_addReg(MI, X86_REG_CL);
    return;
    break;
  }


  // Fragment 2 encoded into 5 bits for 19 unique commands.
  //printf("Frag-2: %"PRIu64"\n", (Bits >> 22) & 31);
  switch ((Bits >> 22) & 31) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // ADC16mi, ADC16mi8, ADC16mr, ADC32mi, ADC32mi8, ADC32mr, ADC64mi32, ADC...
    printOperand(MI, 5, O); 
    break;
  case 1:
    // ADC16ri, ADC16ri8, ADC16rr, ADC16rr_REV, ADC32ri, ADC32ri8, ADC32rr, A...
    printOperand(MI, 2, O); 
    break;
  case 2:
    // ADC16rm, ADD16rm, AND16rm, CMOVA16rm, CMOVAE16rm, CMOVB16rm, CMOVBE16r...
    printi16mem(MI, 2, O); 
    return;
    break;
  case 3:
    // ADC32rm, ADCX32rm, ADD32rm, AND32rm, CMOVA32rm, CMOVAE32rm, CMOVB32rm,...
    printi32mem(MI, 2, O); 
    return;
    break;
  case 4:
    // ADC64rm, ADCX64rm, ADD64rm, AND64rm, CMOVA64rm, CMOVAE64rm, CMOVB64rm,...
    printi64mem(MI, 2, O); 
    return;
    break;
  case 5:
    // ADC8rm, ADD8rm, AND8rm, OR8rm, SBB8rm, SUB8rm, XOR8rm
    printi8mem(MI, 2, O); 
    return;
    break;
  case 6:
    // ADOX32rm, BEXTR32rm, BEXTRI32mi, BLCFILL32rm, BLCI32rm, BLCIC32rm, BLC...
    printi32mem(MI, 1, O); 
    break;
  case 7:
    // ADOX32rr, ADOX64rr, ANDN32rm, ANDN32rr, ANDN64rm, ANDN64rr, ARPL16rr, ...
    printOperand(MI, 1, O); 
    break;
  case 8:
    // ADOX64rm, BEXTR64rm, BEXTRI64mi, BLCFILL64rm, BLCI64rm, BLCIC64rm, BLC...
    printi64mem(MI, 1, O); 
    break;
  case 9:
    // BSF16rm, BSR16rm, CMP16rm, IMUL16rmi, IMUL16rmi8, LAR16rm, LAR32rm, LA...
    printi16mem(MI, 1, O); 
    break;
  case 10:
    // CMP8rm, MOV8rm, MOV8rm_NOREX, MOVSX16rm8, MOVSX32_NOREXrm8, MOVSX32rm8...
    printi8mem(MI, 1, O); 
    break;
  case 11:
    // INVEPT32, INVEPT64, INVPCID32, INVPCID64, INVVPID32, INVVPID64
    printi128mem(MI, 1, O); 
    return;
    break;
  case 12:
    // LDS16rm, LDS32rm, LES16rm, LES32rm, LFS16rm, LFS32rm, LFS64rm, LGS16rm...
    printopaquemem(MI, 1, O); 
    return;
    break;
  case 13:
    // LEA16r, LEA32r, LEA64_32r, LEA64r
    printanymem(MI, 1, O); 
    return;
    break;
  case 14:
    // MOVSB
    printSrcIdx8(MI, 1, O); 
    return;
    break;
  case 15:
    // MOVSL
    printSrcIdx32(MI, 1, O); 
    return;
    break;
  case 16:
    // MOVSQ
    printSrcIdx64(MI, 1, O); 
    return;
    break;
  case 17:
    // MOVSW
    printSrcIdx16(MI, 1, O); 
    return;
    break;
  case 18:
    // NOOP19rr
    printOperand(MI, 0, O); 
    return;
    break;
  }


  // Fragment 3 encoded into 2 bits for 3 unique commands.
  //printf("Frag-3: %"PRIu64"\n", (Bits >> 27) & 3);
  switch ((Bits >> 27) & 3) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // ADC16mi, ADC16mi8, ADC16mr, ADC16ri, ADC16ri8, ADC16rr, ADC16rr_REV, A...
    return;
    break;
  case 1:
    // ANDN32rm, ANDN32rr, ANDN64rm, ANDN64rr, BEXTR32rm, BEXTR32rr, BEXTR64r...
    SStream_concat0(O, ", "); 
    break;
  case 2:
    // SHLD16mrCL, SHLD16rrCL, SHLD32mrCL, SHLD32rrCL, SHLD64mrCL, SHLD64rrCL...
    SStream_concat0(O, ", cl"); 
    op_addReg(MI, X86_REG_CL);
    return;
    break;
  }


  // Fragment 4 encoded into 3 bits for 5 unique commands.
  //printf("Frag-4: %"PRIu64"\n", (Bits >> 29) & 7);
  switch ((Bits >> 29) & 7) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // ANDN32rm, MULX32rm, PDEP32rm, PEXT32rm
    printi32mem(MI, 2, O); 
    return;
    break;
  case 1:
    // ANDN32rr, ANDN64rr, BEXTR32rr, BEXTR64rr, BEXTRI32ri, BEXTRI64ri, BZHI...
    printOperand(MI, 2, O); 
    return;
    break;
  case 2:
    // ANDN64rm, MULX64rm, PDEP64rm, PEXT64rm
    printi64mem(MI, 2, O); 
    return;
    break;
  case 3:
    // BEXTR32rm, BEXTR64rm, BEXTRI32mi, BEXTRI64mi, BZHI32rm, BZHI64rm, IMUL...
    printOperand(MI, 6, O); 
    break;
  case 4:
    // SHLD16rri8, SHLD32rri8, SHLD64rri8, SHRD16rri8, SHRD32rri8, SHRD64rri8
    printOperand(MI, 3, O); 
    return;
    break;
  }


  // Fragment 5 encoded into 1 bits for 2 unique commands.
  //printf("Frag-5: %"PRIu64"\n", (Bits >> 32) & 1);
  if ((Bits >> 32) & 1) {
    // VAARG_64
    SStream_concat0(O, ", "); 
    printOperand(MI, 7, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 8, O); 
    return;
  } else {
    // BEXTR32rm, BEXTR64rm, BEXTRI32mi, BEXTRI64mi, BZHI32rm, BZHI64rm, IMUL...
    return;
  }
}


/// getRegisterName - This method is automatically generated by tblgen
/// from the register set description.  This returns the assembler name
/// for the specified register.
static const char *getRegisterName(unsigned RegNo)
{
  // assert(RegNo && RegNo < 242 && "Invalid register number!");

#ifndef CAPSTONE_DIET
  static const char AsmStrs[] = {
  /* 0 */ 's', 't', '(', '0', ')', 0,
  /* 6 */ 's', 't', '(', '1', ')', 0,
  /* 12 */ 's', 't', '(', '2', ')', 0,
  /* 18 */ 's', 't', '(', '3', ')', 0,
  /* 24 */ 's', 't', '(', '4', ')', 0,
  /* 30 */ 's', 't', '(', '5', ')', 0,
  /* 36 */ 's', 't', '(', '6', ')', 0,
  /* 42 */ 's', 't', '(', '7', ')', 0,
  /* 48 */ 'x', 'm', 'm', '1', '0', 0,
  /* 54 */ 'y', 'm', 'm', '1', '0', 0,
  /* 60 */ 'z', 'm', 'm', '1', '0', 0,
  /* 66 */ 'c', 'r', '1', '0', 0,
  /* 71 */ 'd', 'r', '1', '0', 0,
  /* 76 */ 'x', 'm', 'm', '2', '0', 0,
  /* 82 */ 'y', 'm', 'm', '2', '0', 0,
  /* 88 */ 'z', 'm', 'm', '2', '0', 0,
  /* 94 */ 'x', 'm', 'm', '3', '0', 0,
  /* 100 */ 'y', 'm', 'm', '3', '0', 0,
  /* 106 */ 'z', 'm', 'm', '3', '0', 0,
  /* 112 */ 'k', '0', 0,
  /* 115 */ 'x', 'm', 'm', '0', 0,
  /* 120 */ 'y', 'm', 'm', '0', 0,
  /* 125 */ 'z', 'm', 'm', '0', 0,
  /* 130 */ 'f', 'p', '0', 0,
  /* 134 */ 'c', 'r', '0', 0,
  /* 138 */ 'd', 'r', '0', 0,
  /* 142 */ 'x', 'm', 'm', '1', '1', 0,
  /* 148 */ 'y', 'm', 'm', '1', '1', 0,
  /* 154 */ 'z', 'm', 'm', '1', '1', 0,
  /* 160 */ 'c', 'r', '1', '1', 0,
  /* 165 */ 'd', 'r', '1', '1', 0,
  /* 170 */ 'x', 'm', 'm', '2', '1', 0,
  /* 176 */ 'y', 'm', 'm', '2', '1', 0,
  /* 182 */ 'z', 'm', 'm', '2', '1', 0,
  /* 188 */ 'x', 'm', 'm', '3', '1', 0,
  /* 194 */ 'y', 'm', 'm', '3', '1', 0,
  /* 200 */ 'z', 'm', 'm', '3', '1', 0,
  /* 206 */ 'k', '1', 0,
  /* 209 */ 'x', 'm', 'm', '1', 0,
  /* 214 */ 'y', 'm', 'm', '1', 0,
  /* 219 */ 'z', 'm', 'm', '1', 0,
  /* 224 */ 'f', 'p', '1', 0,
  /* 228 */ 'c', 'r', '1', 0,
  /* 232 */ 'd', 'r', '1', 0,
  /* 236 */ 'x', 'm', 'm', '1', '2', 0,
  /* 242 */ 'y', 'm', 'm', '1', '2', 0,
  /* 248 */ 'z', 'm', 'm', '1', '2', 0,
  /* 254 */ 'c', 'r', '1', '2', 0,
  /* 259 */ 'd', 'r', '1', '2', 0,
  /* 264 */ 'x', 'm', 'm', '2', '2', 0,
  /* 270 */ 'y', 'm', 'm', '2', '2', 0,
  /* 276 */ 'z', 'm', 'm', '2', '2', 0,
  /* 282 */ 'k', '2', 0,
  /* 285 */ 'x', 'm', 'm', '2', 0,
  /* 290 */ 'y', 'm', 'm', '2', 0,
  /* 295 */ 'z', 'm', 'm', '2', 0,
  /* 300 */ 'f', 'p', '2', 0,
  /* 304 */ 'c', 'r', '2', 0,
  /* 308 */ 'd', 'r', '2', 0,
  /* 312 */ 'x', 'm', 'm', '1', '3', 0,
  /* 318 */ 'y', 'm', 'm', '1', '3', 0,
  /* 324 */ 'z', 'm', 'm', '1', '3', 0,
  /* 330 */ 'c', 'r', '1', '3', 0,
  /* 335 */ 'd', 'r', '1', '3', 0,
  /* 340 */ 'x', 'm', 'm', '2', '3', 0,
  /* 346 */ 'y', 'm', 'm', '2', '3', 0,
  /* 352 */ 'z', 'm', 'm', '2', '3', 0,
  /* 358 */ 'k', '3', 0,
  /* 361 */ 'x', 'm', 'm', '3', 0,
  /* 366 */ 'y', 'm', 'm', '3', 0,
  /* 371 */ 'z', 'm', 'm', '3', 0,
  /* 376 */ 'f', 'p', '3', 0,
  /* 380 */ 'c', 'r', '3', 0,
  /* 384 */ 'd', 'r', '3', 0,
  /* 388 */ 'x', 'm', 'm', '1', '4', 0,
  /* 394 */ 'y', 'm', 'm', '1', '4', 0,
  /* 400 */ 'z', 'm', 'm', '1', '4', 0,
  /* 406 */ 'c', 'r', '1', '4', 0,
  /* 411 */ 'd', 'r', '1', '4', 0,
  /* 416 */ 'x', 'm', 'm', '2', '4', 0,
  /* 422 */ 'y', 'm', 'm', '2', '4', 0,
  /* 428 */ 'z', 'm', 'm', '2', '4', 0,
  /* 434 */ 'k', '4', 0,
  /* 437 */ 'x', 'm', 'm', '4', 0,
  /* 442 */ 'y', 'm', 'm', '4', 0,
  /* 447 */ 'z', 'm', 'm', '4', 0,
  /* 452 */ 'f', 'p', '4', 0,
  /* 456 */ 'c', 'r', '4', 0,
  /* 460 */ 'd', 'r', '4', 0,
  /* 464 */ 'x', 'm', 'm', '1', '5', 0,
  /* 470 */ 'y', 'm', 'm', '1', '5', 0,
  /* 476 */ 'z', 'm', 'm', '1', '5', 0,
  /* 482 */ 'c', 'r', '1', '5', 0,
  /* 487 */ 'd', 'r', '1', '5', 0,
  /* 492 */ 'x', 'm', 'm', '2', '5', 0,
  /* 498 */ 'y', 'm', 'm', '2', '5', 0,
  /* 504 */ 'z', 'm', 'm', '2', '5', 0,
  /* 510 */ 'k', '5', 0,
  /* 513 */ 'x', 'm', 'm', '5', 0,
  /* 518 */ 'y', 'm', 'm', '5', 0,
  /* 523 */ 'z', 'm', 'm', '5', 0,
  /* 528 */ 'f', 'p', '5', 0,
  /* 532 */ 'c', 'r', '5', 0,
  /* 536 */ 'd', 'r', '5', 0,
  /* 540 */ 'x', 'm', 'm', '1', '6', 0,
  /* 546 */ 'y', 'm', 'm', '1', '6', 0,
  /* 552 */ 'z', 'm', 'm', '1', '6', 0,
  /* 558 */ 'x', 'm', 'm', '2', '6', 0,
  /* 564 */ 'y', 'm', 'm', '2', '6', 0,
  /* 570 */ 'z', 'm', 'm', '2', '6', 0,
  /* 576 */ 'k', '6', 0,
  /* 579 */ 'x', 'm', 'm', '6', 0,
  /* 584 */ 'y', 'm', 'm', '6', 0,
  /* 589 */ 'z', 'm', 'm', '6', 0,
  /* 594 */ 'f', 'p', '6', 0,
  /* 598 */ 'c', 'r', '6', 0,
  /* 602 */ 'd', 'r', '6', 0,
  /* 606 */ 'x', 'm', 'm', '1', '7', 0,
  /* 612 */ 'y', 'm', 'm', '1', '7', 0,
  /* 618 */ 'z', 'm', 'm', '1', '7', 0,
  /* 624 */ 'x', 'm', 'm', '2', '7', 0,
  /* 630 */ 'y', 'm', 'm', '2', '7', 0,
  /* 636 */ 'z', 'm', 'm', '2', '7', 0,
  /* 642 */ 'k', '7', 0,
  /* 645 */ 'x', 'm', 'm', '7', 0,
  /* 650 */ 'y', 'm', 'm', '7', 0,
  /* 655 */ 'z', 'm', 'm', '7', 0,
  /* 660 */ 'f', 'p', '7', 0,
  /* 664 */ 'c', 'r', '7', 0,
  /* 668 */ 'd', 'r', '7', 0,
  /* 672 */ 'x', 'm', 'm', '1', '8', 0,
  /* 678 */ 'y', 'm', 'm', '1', '8', 0,
  /* 684 */ 'z', 'm', 'm', '1', '8', 0,
  /* 690 */ 'x', 'm', 'm', '2', '8', 0,
  /* 696 */ 'y', 'm', 'm', '2', '8', 0,
  /* 702 */ 'z', 'm', 'm', '2', '8', 0,
  /* 708 */ 'x', 'm', 'm', '8', 0,
  /* 713 */ 'y', 'm', 'm', '8', 0,
  /* 718 */ 'z', 'm', 'm', '8', 0,
  /* 723 */ 'c', 'r', '8', 0,
  /* 727 */ 'd', 'r', '8', 0,
  /* 731 */ 'x', 'm', 'm', '1', '9', 0,
  /* 737 */ 'y', 'm', 'm', '1', '9', 0,
  /* 743 */ 'z', 'm', 'm', '1', '9', 0,
  /* 749 */ 'x', 'm', 'm', '2', '9', 0,
  /* 755 */ 'y', 'm', 'm', '2', '9', 0,
  /* 761 */ 'z', 'm', 'm', '2', '9', 0,
  /* 767 */ 'x', 'm', 'm', '9', 0,
  /* 772 */ 'y', 'm', 'm', '9', 0,
  /* 777 */ 'z', 'm', 'm', '9', 0,
  /* 782 */ 'c', 'r', '9', 0,
  /* 786 */ 'd', 'r', '9', 0,
  /* 790 */ 'r', '1', '0', 'b', 0,
  /* 795 */ 'r', '1', '1', 'b', 0,
  /* 800 */ 'r', '1', '2', 'b', 0,
  /* 805 */ 'r', '1', '3', 'b', 0,
  /* 810 */ 'r', '1', '4', 'b', 0,
  /* 815 */ 'r', '1', '5', 'b', 0,
  /* 820 */ 'r', '8', 'b', 0,
  /* 824 */ 'r', '9', 'b', 0,
  /* 828 */ 'r', '1', '0', 'd', 0,
  /* 833 */ 'r', '1', '1', 'd', 0,
  /* 838 */ 'r', '1', '2', 'd', 0,
  /* 843 */ 'r', '1', '3', 'd', 0,
  /* 848 */ 'r', '1', '4', 'd', 0,
  /* 853 */ 'r', '1', '5', 'd', 0,
  /* 858 */ 'r', '8', 'd', 0,
  /* 862 */ 'r', '9', 'd', 0,
  /* 866 */ 'a', 'h', 0,
  /* 869 */ 'b', 'h', 0,
  /* 872 */ 'c', 'h', 0,
  /* 875 */ 'd', 'h', 0,
  /* 878 */ 'e', 'd', 'i', 0,
  /* 882 */ 'r', 'd', 'i', 0,
  /* 886 */ 'e', 's', 'i', 0,
  /* 890 */ 'r', 's', 'i', 0,
  /* 894 */ 'a', 'l', 0,
  /* 897 */ 'b', 'l', 0,
  /* 900 */ 'c', 'l', 0,
  /* 903 */ 'd', 'l', 0,
  /* 906 */ 'd', 'i', 'l', 0,
  /* 910 */ 's', 'i', 'l', 0,
  /* 914 */ 'b', 'p', 'l', 0,
  /* 918 */ 's', 'p', 'l', 0,
  /* 922 */ 'e', 'b', 'p', 0,
  /* 926 */ 'r', 'b', 'p', 0,
  /* 930 */ 'e', 'i', 'p', 0,
  /* 934 */ 'r', 'i', 'p', 0,
  /* 938 */ 'e', 's', 'p', 0,
  /* 942 */ 'r', 's', 'p', 0,
  /* 946 */ 'c', 's', 0,
  /* 949 */ 'd', 's', 0,
  /* 952 */ 'e', 's', 0,
  /* 955 */ 'f', 's', 0,
  /* 958 */ 'f', 'l', 'a', 'g', 's', 0,
  /* 964 */ 's', 's', 0,
  /* 967 */ 'r', '1', '0', 'w', 0,
  /* 972 */ 'r', '1', '1', 'w', 0,
  /* 977 */ 'r', '1', '2', 'w', 0,
  /* 982 */ 'r', '1', '3', 'w', 0,
  /* 987 */ 'r', '1', '4', 'w', 0,
  /* 992 */ 'r', '1', '5', 'w', 0,
  /* 997 */ 'r', '8', 'w', 0,
  /* 1001 */ 'r', '9', 'w', 0,
  /* 1005 */ 'f', 'p', 's', 'w', 0,
  /* 1010 */ 'e', 'a', 'x', 0,
  /* 1014 */ 'r', 'a', 'x', 0,
  /* 1018 */ 'e', 'b', 'x', 0,
  /* 1022 */ 'r', 'b', 'x', 0,
  /* 1026 */ 'e', 'c', 'x', 0,
  /* 1030 */ 'r', 'c', 'x', 0,
  /* 1034 */ 'e', 'd', 'x', 0,
  /* 1038 */ 'r', 'd', 'x', 0,
  /* 1042 */ 'e', 'i', 'z', 0,
  /* 1046 */ 'r', 'i', 'z', 0,
  };

  static const uint16_t RegAsmOffset[] = {
    866, 894, 1011, 869, 897, 923, 914, 1019, 872, 900, 946, 1027, 875, 879, 
    906, 903, 949, 1035, 1010, 922, 1018, 1026, 878, 1034, 958, 930, 1042, 952, 
    886, 938, 1005, 955, 961, 931, 1014, 926, 1022, 1030, 882, 1038, 934, 1046, 
    890, 942, 887, 910, 939, 918, 964, 134, 228, 304, 380, 456, 532, 598, 
    664, 723, 782, 66, 160, 254, 330, 406, 482, 138, 232, 308, 384, 460, 
    536, 602, 668, 727, 786, 71, 165, 259, 335, 411, 487, 130, 224, 300, 
    376, 452, 528, 594, 660, 112, 206, 282, 358, 434, 510, 576, 642, 116, 
    210, 286, 362, 438, 514, 580, 646, 724, 783, 67, 161, 255, 331, 407, 
    483, 0, 6, 12, 18, 24, 30, 36, 42, 115, 209, 285, 361, 437, 
    513, 579, 645, 708, 767, 48, 142, 236, 312, 388, 464, 540, 606, 672, 
    731, 76, 170, 264, 340, 416, 492, 558, 624, 690, 749, 94, 188, 120, 
    214, 290, 366, 442, 518, 584, 650, 713, 772, 54, 148, 242, 318, 394, 
    470, 546, 612, 678, 737, 82, 176, 270, 346, 422, 498, 564, 630, 696, 
    755, 100, 194, 125, 219, 295, 371, 447, 523, 589, 655, 718, 777, 60, 
    154, 248, 324, 400, 476, 552, 618, 684, 743, 88, 182, 276, 352, 428, 
    504, 570, 636, 702, 761, 106, 200, 820, 824, 790, 795, 800, 805, 810, 
    815, 858, 862, 828, 833, 838, 843, 848, 853, 997, 1001, 967, 972, 977, 
    982, 987, 992, 
  };

  //int i;
  //for (i = 0; i < sizeof(RegAsmOffset)/2; i++)
  //     printf("%s = %u\n", AsmStrs+RegAsmOffset[i], i + 1);
  //printf("*************************\n");
  return AsmStrs+RegAsmOffset[RegNo-1];
#else
  return NULL;
#endif
}

#ifdef PRINT_ALIAS_INSTR
#undef PRINT_ALIAS_INSTR

#ifndef CAPSTONE_DIET

static void printCustomAliasOperand(MCInst *MI, unsigned OpIdx,
  unsigned PrintMethodIdx, SStream *OS)
{
}

static char *printAliasInstr(MCInst *MI, SStream *OS, void *info)
{
  #define GETREGCLASS_CONTAIN(_class, _reg) MCRegisterClass_contains(MCRegisterInfo_getRegClass(MRI, _class), MCOperand_getReg(MCInst_getOperand(MI, _reg)))
  const char *AsmString;
  char *tmp, *AsmMnem, *AsmOps, *c;
  int OpIdx, PrintMethodIdx;
  switch (MCInst_getOpcode(MI)) {
  default: return NULL;
  case X86_AAD8i8:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 10) {
      // (AAD8i8 10)
      AsmString = "aad";
      break;
    }
    return NULL;
  case X86_AAM8i8:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 10) {
      // (AAM8i8 10)
      AsmString = "aam";
      break;
    }
    return NULL;
  case X86_XSTORE:
    if (MCInst_getNumOperands(MI) == 0) {
      // (XSTORE)
      AsmString = "xstorerng";
      break;
    }
    return NULL;
  }

  tmp = cs_strdup(AsmString);
  AsmMnem = tmp;
  for(AsmOps = tmp; *AsmOps; AsmOps++) {
    if (*AsmOps == ' ' || *AsmOps == '\t') {
      *AsmOps = '\0';
      AsmOps++;
      break;
    }
  }
  SStream_concat0(OS, AsmMnem);
  if (*AsmOps) {
    SStream_concat0(OS, "\t");
    for (c = AsmOps; *c; c++) {
      if (*c == '$') {
        c += 1;
        if (*c == (char)0xff) {
          c += 1;
          OpIdx = *c - 1;
          c += 1;
          PrintMethodIdx = *c - 1;
          printCustomAliasOperand(MI, OpIdx, PrintMethodIdx, OS);
        } else
          printOperand(MI, *c - 1, OS);
      } else {
        SStream_concat(OS, "%c", *c);
      }
    }
  }
  return tmp;
}

#endif

#endif // PRINT_ALIAS_INSTR
