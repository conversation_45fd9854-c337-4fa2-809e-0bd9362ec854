// This is auto-gen data for Capstone disassembly engine (www.capstone-engine.org)
// By <PERSON><PERSON><PERSON> <<EMAIL>>

{	/* X86_AAA, X86_INS_AAA: aaa */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ 0 }
},
{	/* X86_AAD8i8, X86_INS_AAD: aad	$src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_AAM8i8, X86_INS_AAM: aam	$src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_AAS, X86_INS_AAS: aas */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ 0 }
},
{	/* X86_ADC16i16, X86_INS_ADC: adc{w}	ax, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADC16mi, X86_INS_ADC: adc{w}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADC16mi8, X86_INS_ADC: adc{w}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADC16mr, X86_INS_ADC: adc{w}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADC16ri, X86_INS_ADC: adc{w}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADC16ri8, X86_INS_ADC: adc{w}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADC16rm, X86_INS_ADC: adc{w}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADC16rr, X86_INS_ADC: adc{w}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADC16rr_REV, X86_INS_ADC: adc{w}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADC32i32, X86_INS_ADC: adc{l}	eax, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADC32mi, X86_INS_ADC: adc{l}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADC32mi8, X86_INS_ADC: adc{l}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADC32mr, X86_INS_ADC: adc{l}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADC32ri, X86_INS_ADC: adc{l}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADC32ri8, X86_INS_ADC: adc{l}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADC32rm, X86_INS_ADC: adc{l}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADC32rr, X86_INS_ADC: adc{l}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADC32rr_REV, X86_INS_ADC: adc{l}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADC64i32, X86_INS_ADC: adc{q}	rax, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADC64mi32, X86_INS_ADC: adc{q}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADC64mi8, X86_INS_ADC: adc{q}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADC64mr, X86_INS_ADC: adc{q}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADC64ri32, X86_INS_ADC: adc{q}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADC64ri8, X86_INS_ADC: adc{q}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADC64rm, X86_INS_ADC: adc{q}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADC64rr, X86_INS_ADC: adc{q}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADC64rr_REV, X86_INS_ADC: adc{q}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADC8i8, X86_INS_ADC: adc{b}	al, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADC8mi, X86_INS_ADC: adc{b}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADC8mi8, X86_INS_ADC: adc{b}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADC8mr, X86_INS_ADC: adc{b}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADC8ri, X86_INS_ADC: adc{b}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADC8ri8, X86_INS_ADC: adc{b}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADC8rm, X86_INS_ADC: adc{b}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADC8rr, X86_INS_ADC: adc{b}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADC8rr_REV, X86_INS_ADC: adc{b}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADCX32rm, X86_INS_ADCX: adcx{l}	$dst, $src */
	X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADCX32rr, X86_INS_ADCX: adcx{l}	$dst, $src */
	X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADCX64rm, X86_INS_ADCX: adcx{q}	$dst, $src */
	X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADCX64rr, X86_INS_ADCX: adcx{q}	$dst, $src */
	X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADD16i16, X86_INS_ADD: add{w}	ax, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADD16mi, X86_INS_ADD: add{w}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADD16mi8, X86_INS_ADD: add{w}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADD16mr, X86_INS_ADD: add{w}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADD16ri, X86_INS_ADD: add{w}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADD16ri8, X86_INS_ADD: add{w}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADD16rm, X86_INS_ADD: add{w}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADD16rr, X86_INS_ADD: add{w}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADD16rr_REV, X86_INS_ADD: add{w}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADD32i32, X86_INS_ADD: add{l}	eax, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADD32mi, X86_INS_ADD: add{l}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADD32mi8, X86_INS_ADD: add{l}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADD32mr, X86_INS_ADD: add{l}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADD32ri, X86_INS_ADD: add{l}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADD32ri8, X86_INS_ADD: add{l}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADD32rm, X86_INS_ADD: add{l}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADD32rr, X86_INS_ADD: add{l}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADD32rr_REV, X86_INS_ADD: add{l}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADD64i32, X86_INS_ADD: add{q}	rax, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADD64mi32, X86_INS_ADD: add{q}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADD64mi8, X86_INS_ADD: add{q}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADD64mr, X86_INS_ADD: add{q}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADD64ri32, X86_INS_ADD: add{q}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADD64ri8, X86_INS_ADD: add{q}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADD64rm, X86_INS_ADD: add{q}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADD64rr, X86_INS_ADD: add{q}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADD64rr_REV, X86_INS_ADD: add{q}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADD8i8, X86_INS_ADD: add{b}	al, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADD8mi, X86_INS_ADD: add{b}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADD8mi8, X86_INS_ADD: add{b}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADD8mr, X86_INS_ADD: add{b}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADD8ri, X86_INS_ADD: add{b}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADD8ri8, X86_INS_ADD: add{b}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ADD8rm, X86_INS_ADD: add{b}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADD8rr, X86_INS_ADD: add{b}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADD8rr_REV, X86_INS_ADD: add{b}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADOX32rm, X86_INS_ADOX: adox{l}	$dst, $src */
	X86_EFLAGS_MODIFY_OF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADOX32rr, X86_INS_ADOX: adox{l}	$dst, $src */
	X86_EFLAGS_MODIFY_OF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADOX64rm, X86_INS_ADOX: adox{q}	$dst, $src */
	X86_EFLAGS_MODIFY_OF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ADOX64rr, X86_INS_ADOX: adox{q}	$dst, $src */
	X86_EFLAGS_MODIFY_OF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_AND16i16, X86_INS_AND: and{w}	ax, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_AND16mi, X86_INS_AND: and{w}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_AND16mi8, X86_INS_AND: and{w}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_AND16mr, X86_INS_AND: and{w}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_AND16ri, X86_INS_AND: and{w}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_AND16ri8, X86_INS_AND: and{w}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_AND16rm, X86_INS_AND: and{w}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_AND16rr, X86_INS_AND: and{w}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_AND16rr_REV, X86_INS_AND: and{w}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_AND32i32, X86_INS_AND: and{l}	eax, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_AND32mi, X86_INS_AND: and{l}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_AND32mi8, X86_INS_AND: and{l}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_AND32mr, X86_INS_AND: and{l}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_AND32ri, X86_INS_AND: and{l}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_AND32ri8, X86_INS_AND: and{l}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_AND32rm, X86_INS_AND: and{l}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_AND32rr, X86_INS_AND: and{l}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_AND32rr_REV, X86_INS_AND: and{l}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_AND64i32, X86_INS_AND: and{q}	rax, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_AND64mi32, X86_INS_AND: and{q}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_AND64mi8, X86_INS_AND: and{q}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_AND64mr, X86_INS_AND: and{q}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_AND64ri32, X86_INS_AND: and{q}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_AND64ri8, X86_INS_AND: and{q}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_AND64rm, X86_INS_AND: and{q}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_AND64rr, X86_INS_AND: and{q}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_AND64rr_REV, X86_INS_AND: and{q}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_AND8i8, X86_INS_AND: and{b}	al, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_AND8mi, X86_INS_AND: and{b}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_AND8mi8, X86_INS_AND: and{b}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_AND8mr, X86_INS_AND: and{b}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_AND8ri, X86_INS_AND: and{b}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_AND8ri8, X86_INS_AND: and{b}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_AND8rm, X86_INS_AND: and{b}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_AND8rr, X86_INS_AND: and{b}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_AND8rr_REV, X86_INS_AND: and{b}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ANDN32rm, X86_INS_ANDN: andn{l}	$dst, $src1, $src2 */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_RESET_OF | X86_EFLAGS_RESET_CF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_ANDN32rr, X86_INS_ANDN: andn{l}	$dst, $src1, $src2 */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_RESET_OF | X86_EFLAGS_RESET_CF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_ANDN64rm, X86_INS_ANDN: andn{q}	$dst, $src1, $src2 */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_RESET_OF | X86_EFLAGS_RESET_CF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_ANDN64rr, X86_INS_ANDN: andn{q}	$dst, $src1, $src2 */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_RESET_OF | X86_EFLAGS_RESET_CF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_ARPL16mr, X86_INS_ARPL: arpl	$dst, $src */
	X86_EFLAGS_MODIFY_ZF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_ARPL16rr, X86_INS_ARPL: arpl	$dst, $src */
	X86_EFLAGS_MODIFY_ZF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BEXTR32rm, X86_INS_BEXTR: bextr{l}	$dst, $src1, $src2 */
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_RESET_CF | X86_EFLAGS_RESET_OF | X86_EFLAGS_RESET_TF | X86_EFLAGS_RESET_IF | X86_EFLAGS_RESET_DF | X86_EFLAGS_RESET_NT | X86_EFLAGS_RESET_RF,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_BEXTR32rr, X86_INS_BEXTR: bextr{l}	$dst, $src1, $src2 */
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_RESET_CF | X86_EFLAGS_RESET_OF | X86_EFLAGS_RESET_TF | X86_EFLAGS_RESET_IF | X86_EFLAGS_RESET_DF | X86_EFLAGS_RESET_NT | X86_EFLAGS_RESET_RF,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_BEXTR64rm, X86_INS_BEXTR: bextr{q}	$dst, $src1, $src2 */
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_RESET_CF | X86_EFLAGS_RESET_OF | X86_EFLAGS_RESET_TF | X86_EFLAGS_RESET_IF | X86_EFLAGS_RESET_DF | X86_EFLAGS_RESET_NT | X86_EFLAGS_RESET_RF,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_BEXTR64rr, X86_INS_BEXTR: bextr{q}	$dst, $src1, $src2 */
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_RESET_CF | X86_EFLAGS_RESET_OF | X86_EFLAGS_RESET_TF | X86_EFLAGS_RESET_IF | X86_EFLAGS_RESET_DF | X86_EFLAGS_RESET_NT | X86_EFLAGS_RESET_RF,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_BEXTRI32mi, X86_INS_BEXTR: bextr	$dst, $src1, $cntl */
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_RESET_CF | X86_EFLAGS_RESET_OF | X86_EFLAGS_RESET_TF | X86_EFLAGS_RESET_IF | X86_EFLAGS_RESET_DF | X86_EFLAGS_RESET_NT | X86_EFLAGS_RESET_RF,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_BEXTRI32ri, X86_INS_BEXTR: bextr	$dst, $src1, $cntl */
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_RESET_CF | X86_EFLAGS_RESET_OF | X86_EFLAGS_RESET_TF | X86_EFLAGS_RESET_IF | X86_EFLAGS_RESET_DF | X86_EFLAGS_RESET_NT | X86_EFLAGS_RESET_RF,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_BEXTRI64mi, X86_INS_BEXTR: bextr	$dst, $src1, $cntl */
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_RESET_CF | X86_EFLAGS_RESET_OF | X86_EFLAGS_RESET_TF | X86_EFLAGS_RESET_IF | X86_EFLAGS_RESET_DF | X86_EFLAGS_RESET_NT | X86_EFLAGS_RESET_RF,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_BEXTRI64ri, X86_INS_BEXTR: bextr	$dst, $src1, $cntl */
	0,
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_RESET_CF | X86_EFLAGS_RESET_OF | X86_EFLAGS_RESET_TF | X86_EFLAGS_RESET_IF | X86_EFLAGS_RESET_DF | X86_EFLAGS_RESET_NT | X86_EFLAGS_RESET_RF,
},
{	/* X86_BLCFILL32rm, X86_INS_BLCFILL: blcfill	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLCFILL32rr, X86_INS_BLCFILL: blcfill	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLCFILL64rm, X86_INS_BLCFILL: blcfill	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLCFILL64rr, X86_INS_BLCFILL: blcfill	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLCI32rm, X86_INS_BLCI: blci	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLCI32rr, X86_INS_BLCI: blci	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLCI64rm, X86_INS_BLCI: blci	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLCI64rr, X86_INS_BLCI: blci	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLCIC32rm, X86_INS_BLCIC: blcic	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLCIC32rr, X86_INS_BLCIC: blcic	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLCIC64rm, X86_INS_BLCIC: blcic	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLCIC64rr, X86_INS_BLCIC: blcic	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLCMSK32rm, X86_INS_BLCMSK: blcmsk	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLCMSK32rr, X86_INS_BLCMSK: blcmsk	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLCMSK64rm, X86_INS_BLCMSK: blcmsk	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLCMSK64rr, X86_INS_BLCMSK: blcmsk	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLCS32rm, X86_INS_BLCS: blcs	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLCS32rr, X86_INS_BLCS: blcs	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLCS64rm, X86_INS_BLCS: blcs	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLCS64rr, X86_INS_BLCS: blcs	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLSFILL32rm, X86_INS_BLSFILL: blsfill	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLSFILL32rr, X86_INS_BLSFILL: blsfill	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLSFILL64rm, X86_INS_BLSFILL: blsfill	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLSFILL64rr, X86_INS_BLSFILL: blsfill	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLSI32rm, X86_INS_BLSI: blsi{l}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_RESET_OF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLSI32rr, X86_INS_BLSI: blsi{l}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_RESET_OF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLSI64rm, X86_INS_BLSI: blsi{q}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_RESET_OF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLSI64rr, X86_INS_BLSI: blsi{q}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_RESET_OF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLSIC32rm, X86_INS_BLSIC: blsic	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLSIC32rr, X86_INS_BLSIC: blsic	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLSIC64rm, X86_INS_BLSIC: blsic	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLSIC64rr, X86_INS_BLSIC: blsic	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLSMSK32rm, X86_INS_BLSMSK: blsmsk{l}	$dst, $src */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_RESET_ZF | X86_EFLAGS_RESET_OF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLSMSK32rr, X86_INS_BLSMSK: blsmsk{l}	$dst, $src */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_RESET_ZF | X86_EFLAGS_RESET_OF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLSMSK64rm, X86_INS_BLSMSK: blsmsk{q}	$dst, $src */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_RESET_ZF | X86_EFLAGS_RESET_OF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLSMSK64rr, X86_INS_BLSMSK: blsmsk{q}	$dst, $src */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_RESET_ZF | X86_EFLAGS_RESET_OF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLSR32rm, X86_INS_BLSR: blsr{l}	$dst, $src */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_RESET_ZF | X86_EFLAGS_RESET_OF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLSR32rr, X86_INS_BLSR: blsr{l}	$dst, $src */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_RESET_ZF | X86_EFLAGS_RESET_OF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLSR64rm, X86_INS_BLSR: blsr{q}	$dst, $src */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_RESET_ZF | X86_EFLAGS_RESET_OF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BLSR64rr, X86_INS_BLSR: blsr{q}	$dst, $src */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_RESET_ZF | X86_EFLAGS_RESET_OF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BOUNDS16rm, X86_INS_BOUND: bound	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BOUNDS32rm, X86_INS_BOUND: bound	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BSF16rm, X86_INS_BSF: bsf{w}	$dst, $src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BSF16rr, X86_INS_BSF: bsf{w}	$dst, $src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BSF32rm, X86_INS_BSF: bsf{l}	$dst, $src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BSF32rr, X86_INS_BSF: bsf{l}	$dst, $src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BSF64rm, X86_INS_BSF: bsf{q}	$dst, $src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BSF64rr, X86_INS_BSF: bsf{q}	$dst, $src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BSR16rm, X86_INS_BSR: bsr{w}	$dst, $src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BSR16rr, X86_INS_BSR: bsr{w}	$dst, $src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BSR32rm, X86_INS_BSR: bsr{l}	$dst, $src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BSR32rr, X86_INS_BSR: bsr{l}	$dst, $src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BSR64rm, X86_INS_BSR: bsr{q}	$dst, $src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BSR64rr, X86_INS_BSR: bsr{q}	$dst, $src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BSWAP32r, X86_INS_BSWAP: bswap{l}	$dst */
	0,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_BSWAP64r, X86_INS_BSWAP: bswap{q}	$dst */
	0,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_BT16mi8, X86_INS_BT: bt{w}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_BT16mr, X86_INS_BT: bt{w}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_BT16ri8, X86_INS_BT: bt{w}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_BT16rr, X86_INS_BT: bt{w}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_BT32mi8, X86_INS_BT: bt{l}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_BT32mr, X86_INS_BT: bt{l}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_BT32ri8, X86_INS_BT: bt{l}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_BT32rr, X86_INS_BT: bt{l}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_BT64mi8, X86_INS_BT: bt{q}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_BT64mr, X86_INS_BT: bt{q}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_BT64ri8, X86_INS_BT: bt{q}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_BT64rr, X86_INS_BT: bt{q}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_BTC16mi8, X86_INS_BTC: btc{w}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_BTC16mr, X86_INS_BTC: btc{w}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BTC16ri8, X86_INS_BTC: btc{w}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_BTC16rr, X86_INS_BTC: btc{w}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BTC32mi8, X86_INS_BTC: btc{l}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_BTC32mr, X86_INS_BTC: btc{l}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BTC32ri8, X86_INS_BTC: btc{l}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_BTC32rr, X86_INS_BTC: btc{l}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BTC64mi8, X86_INS_BTC: btc{q}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_BTC64mr, X86_INS_BTC: btc{q}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BTC64ri8, X86_INS_BTC: btc{q}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_BTC64rr, X86_INS_BTC: btc{q}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BTR16mi8, X86_INS_BTR: btr{w}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_BTR16mr, X86_INS_BTR: btr{w}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BTR16ri8, X86_INS_BTR: btr{w}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_BTR16rr, X86_INS_BTR: btr{w}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BTR32mi8, X86_INS_BTR: btr{l}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_BTR32mr, X86_INS_BTR: btr{l}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BTR32ri8, X86_INS_BTR: btr{l}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_BTR32rr, X86_INS_BTR: btr{l}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BTR64mi8, X86_INS_BTR: btr{q}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_BTR64mr, X86_INS_BTR: btr{q}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BTR64ri8, X86_INS_BTR: btr{q}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_BTR64rr, X86_INS_BTR: btr{q}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BTS16mi8, X86_INS_BTS: bts{w}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_BTS16mr, X86_INS_BTS: bts{w}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BTS16ri8, X86_INS_BTS: bts{w}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_BTS16rr, X86_INS_BTS: bts{w}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BTS32mi8, X86_INS_BTS: bts{l}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_BTS32mr, X86_INS_BTS: bts{l}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BTS32ri8, X86_INS_BTS: bts{l}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_BTS32rr, X86_INS_BTS: bts{l}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BTS64mi8, X86_INS_BTS: bts{q}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_BTS64mr, X86_INS_BTS: bts{q}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BTS64ri8, X86_INS_BTS: bts{q}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_BTS64rr, X86_INS_BTS: bts{q}	$src1, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_BZHI32rm, X86_INS_BZHI: bzhi{l}	$dst, $src1, $src2 */
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_RESET_OF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_BZHI32rr, X86_INS_BZHI: bzhi{l}	$dst, $src1, $src2 */
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_RESET_OF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_BZHI64rm, X86_INS_BZHI: bzhi{q}	$dst, $src1, $src2 */
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_RESET_OF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_BZHI64rr, X86_INS_BZHI: bzhi{q}	$dst, $src1, $src2 */
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_RESET_OF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_CALL16m, X86_INS_CALL: call{w}	{*}$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_CALL16r, X86_INS_CALL: call{w}	{*}$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_CALL32m, X86_INS_CALL: call{l}	{*}$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_CALL32r, X86_INS_CALL: call{l}	{*}$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_CALL64m, X86_INS_CALL: call{q}	{*}$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_CALL64pcrel32, X86_INS_CALL: call{q}	$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_CALL64r, X86_INS_CALL: call{q}	{*}$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_CALLpcrel16, X86_INS_CALL: call{w}	$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_CALLpcrel32, X86_INS_CALL: call{l}	$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_CBW, X86_INS_CBW: cbw */
	0,
	{ 0 }
},
{	/* X86_CDQ, X86_INS_CDQ: cdq */
	0,
	{ 0 }
},
{	/* X86_CDQE, X86_INS_CDQE: cdqe */
	0,
	{ 0 }
},
{	/* X86_CLAC, X86_INS_CLAC: clac */
	X86_EFLAGS_RESET_AC,
	{ 0 }
},
{	/* X86_CLC, X86_INS_CLC: clc */
	X86_EFLAGS_RESET_CF,
	{ 0 }
},
{	/* X86_CLD, X86_INS_CLD: cld */
	X86_EFLAGS_RESET_DF,
	{ 0 }
},
{	/* X86_CLFLUSHOPT, X86_INS_CLFLUSHOPT: clflushopt	$src */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_CLGI, X86_INS_CLGI: clgi */
	0,
	{ 0 }
},
{	/* X86_CLI, X86_INS_CLI: cli */
	X86_EFLAGS_RESET_IF,
	{ 0 }
},
{	/* X86_CLTS, X86_INS_CLTS: clts */
	0,
	{ 0 }
},
{	/* X86_CLWB, X86_INS_CLWB: clwb	$src */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_CMC, X86_INS_CMC: cmc */
	X86_EFLAGS_MODIFY_CF,
	{ 0 }
},
{	/* X86_CMOVA16rm, X86_INS_CMOVA: cmova{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVA16rr, X86_INS_CMOVA: cmova{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVA32rm, X86_INS_CMOVA: cmova{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVA32rr, X86_INS_CMOVA: cmova{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVA64rm, X86_INS_CMOVA: cmova{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVA64rr, X86_INS_CMOVA: cmova{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVAE16rm, X86_INS_CMOVAE: cmovae{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVAE16rr, X86_INS_CMOVAE: cmovae{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVAE32rm, X86_INS_CMOVAE: cmovae{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVAE32rr, X86_INS_CMOVAE: cmovae{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVAE64rm, X86_INS_CMOVAE: cmovae{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVAE64rr, X86_INS_CMOVAE: cmovae{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVB16rm, X86_INS_CMOVB: cmovb{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVB16rr, X86_INS_CMOVB: cmovb{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVB32rm, X86_INS_CMOVB: cmovb{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVB32rr, X86_INS_CMOVB: cmovb{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVB64rm, X86_INS_CMOVB: cmovb{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVB64rr, X86_INS_CMOVB: cmovb{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVBE16rm, X86_INS_CMOVBE: cmovbe{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVBE16rr, X86_INS_CMOVBE: cmovbe{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVBE32rm, X86_INS_CMOVBE: cmovbe{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVBE32rr, X86_INS_CMOVBE: cmovbe{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVBE64rm, X86_INS_CMOVBE: cmovbe{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVBE64rr, X86_INS_CMOVBE: cmovbe{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVE16rm, X86_INS_CMOVE: cmove{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVE16rr, X86_INS_CMOVE: cmove{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVE32rm, X86_INS_CMOVE: cmove{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVE32rr, X86_INS_CMOVE: cmove{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVE64rm, X86_INS_CMOVE: cmove{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVE64rr, X86_INS_CMOVE: cmove{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVG16rm, X86_INS_CMOVG: cmovg{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVG16rr, X86_INS_CMOVG: cmovg{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVG32rm, X86_INS_CMOVG: cmovg{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVG32rr, X86_INS_CMOVG: cmovg{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVG64rm, X86_INS_CMOVG: cmovg{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVG64rr, X86_INS_CMOVG: cmovg{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVGE16rm, X86_INS_CMOVGE: cmovge{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVGE16rr, X86_INS_CMOVGE: cmovge{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVGE32rm, X86_INS_CMOVGE: cmovge{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVGE32rr, X86_INS_CMOVGE: cmovge{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVGE64rm, X86_INS_CMOVGE: cmovge{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVGE64rr, X86_INS_CMOVGE: cmovge{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVL16rm, X86_INS_CMOVL: cmovl{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVL16rr, X86_INS_CMOVL: cmovl{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVL32rm, X86_INS_CMOVL: cmovl{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVL32rr, X86_INS_CMOVL: cmovl{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVL64rm, X86_INS_CMOVL: cmovl{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVL64rr, X86_INS_CMOVL: cmovl{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVLE16rm, X86_INS_CMOVLE: cmovle{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVLE16rr, X86_INS_CMOVLE: cmovle{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVLE32rm, X86_INS_CMOVLE: cmovle{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVLE32rr, X86_INS_CMOVLE: cmovle{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVLE64rm, X86_INS_CMOVLE: cmovle{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVLE64rr, X86_INS_CMOVLE: cmovle{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVNE16rm, X86_INS_CMOVNE: cmovne{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVNE16rr, X86_INS_CMOVNE: cmovne{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVNE32rm, X86_INS_CMOVNE: cmovne{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVNE32rr, X86_INS_CMOVNE: cmovne{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVNE64rm, X86_INS_CMOVNE: cmovne{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVNE64rr, X86_INS_CMOVNE: cmovne{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVNO16rm, X86_INS_CMOVNO: cmovno{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVNO16rr, X86_INS_CMOVNO: cmovno{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVNO32rm, X86_INS_CMOVNO: cmovno{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVNO32rr, X86_INS_CMOVNO: cmovno{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVNO64rm, X86_INS_CMOVNO: cmovno{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVNO64rr, X86_INS_CMOVNO: cmovno{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVNP16rm, X86_INS_CMOVNP: cmovnp{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVNP16rr, X86_INS_CMOVNP: cmovnp{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVNP32rm, X86_INS_CMOVNP: cmovnp{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVNP32rr, X86_INS_CMOVNP: cmovnp{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVNP64rm, X86_INS_CMOVNP: cmovnp{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVNP64rr, X86_INS_CMOVNP: cmovnp{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVNS16rm, X86_INS_CMOVNS: cmovns{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVNS16rr, X86_INS_CMOVNS: cmovns{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVNS32rm, X86_INS_CMOVNS: cmovns{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVNS32rr, X86_INS_CMOVNS: cmovns{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVNS64rm, X86_INS_CMOVNS: cmovns{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVNS64rr, X86_INS_CMOVNS: cmovns{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVO16rm, X86_INS_CMOVO: cmovo{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVO16rr, X86_INS_CMOVO: cmovo{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVO32rm, X86_INS_CMOVO: cmovo{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVO32rr, X86_INS_CMOVO: cmovo{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVO64rm, X86_INS_CMOVO: cmovo{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVO64rr, X86_INS_CMOVO: cmovo{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVP16rm, X86_INS_CMOVP: cmovp{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVP16rr, X86_INS_CMOVP: cmovp{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVP32rm, X86_INS_CMOVP: cmovp{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVP32rr, X86_INS_CMOVP: cmovp{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVP64rm, X86_INS_CMOVP: cmovp{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVP64rr, X86_INS_CMOVP: cmovp{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVS16rm, X86_INS_CMOVS: cmovs{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVS16rr, X86_INS_CMOVS: cmovs{w}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVS32rm, X86_INS_CMOVS: cmovs{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVS32rr, X86_INS_CMOVS: cmovs{l}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVS64rm, X86_INS_CMOVS: cmovs{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMOVS64rr, X86_INS_CMOVS: cmovs{q}	$dst, $src2 */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_PF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMP16i16, X86_INS_CMP: cmp{w}	ax, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_CMP16mi, X86_INS_CMP: cmp{w}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_CMP16mi8, X86_INS_CMP: cmp{w}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_CMP16mr, X86_INS_CMP: cmp{w}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_CMP16ri, X86_INS_CMP: cmp{w}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_CMP16ri8, X86_INS_CMP: cmp{w}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_CMP16rm, X86_INS_CMP: cmp{w}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_CMP16rr, X86_INS_CMP: cmp{w}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_CMP16rr_REV, X86_INS_CMP: cmp{w}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_CMP32i32, X86_INS_CMP: cmp{l}	eax, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_CMP32mi, X86_INS_CMP: cmp{l}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_CMP32mi8, X86_INS_CMP: cmp{l}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_CMP32mr, X86_INS_CMP: cmp{l}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_CMP32ri, X86_INS_CMP: cmp{l}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_CMP32ri8, X86_INS_CMP: cmp{l}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_CMP32rm, X86_INS_CMP: cmp{l}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_CMP32rr, X86_INS_CMP: cmp{l}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_CMP32rr_REV, X86_INS_CMP: cmp{l}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_CMP64i32, X86_INS_CMP: cmp{q}	rax, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_CMP64mi32, X86_INS_CMP: cmp{q}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_CMP64mi8, X86_INS_CMP: cmp{q}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_CMP64mr, X86_INS_CMP: cmp{q}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_CMP64ri32, X86_INS_CMP: cmp{q}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_CMP64ri8, X86_INS_CMP: cmp{q}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_CMP64rm, X86_INS_CMP: cmp{q}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_CMP64rr, X86_INS_CMP: cmp{q}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_CMP64rr_REV, X86_INS_CMP: cmp{q}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_CMP8i8, X86_INS_CMP: cmp{b}	al, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_CMP8mi, X86_INS_CMP: cmp{b}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_CMP8mi8, X86_INS_CMP: cmp{b}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_CMP8mr, X86_INS_CMP: cmp{b}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_CMP8ri, X86_INS_CMP: cmp{b}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_CMP8ri8, X86_INS_CMP: cmp{b}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_CMP8rm, X86_INS_CMP: cmp{b}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_CMP8rr, X86_INS_CMP: cmp{b}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_CMP8rr_REV, X86_INS_CMP: cmp{b}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_CMPSB, X86_INS_CMPSB: cmpsb	$src, $dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_CMPSL, X86_INS_CMPSD: cmps{l|d}	{$dst, $src|$src, $dst} */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_IGNORE, CS_AC_IGNORE, 0 }
},
{	/* X86_CMPSQ, X86_INS_CMPSQ: cmpsq	$src, $dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_CMPSW, X86_INS_CMPSW: cmpsw	$src, $dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_CMPXCHG16B, X86_INS_CMPXCHG16B: cmpxchg16b	$dst */
	X86_EFLAGS_MODIFY_ZF,
	{ CS_AC_READ, 0 }
},
{	/* X86_CMPXCHG16rm, X86_INS_CMPXCHG: cmpxchg{w}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_CMPXCHG16rr, X86_INS_CMPXCHG: cmpxchg{w}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMPXCHG32rm, X86_INS_CMPXCHG: cmpxchg{l}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_CMPXCHG32rr, X86_INS_CMPXCHG: cmpxchg{l}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMPXCHG64rm, X86_INS_CMPXCHG: cmpxchg{q}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_CMPXCHG64rr, X86_INS_CMPXCHG: cmpxchg{q}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CMPXCHG8B, X86_INS_CMPXCHG8B: cmpxchg8b	$dst */
	X86_EFLAGS_MODIFY_ZF,
	{ CS_AC_READ, 0 }
},
{	/* X86_CMPXCHG8rm, X86_INS_CMPXCHG: cmpxchg{b}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_CMPXCHG8rr, X86_INS_CMPXCHG: cmpxchg{b}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_CPUID, X86_INS_CPUID: cpuid */
	0,
	{ 0 }
},
{	/* X86_CQO, X86_INS_CQO: cqo */
	0,
	{ 0 }
},
{	/* X86_CWD, X86_INS_CWD: cwd */
	0,
	{ 0 }
},
{	/* X86_CWDE, X86_INS_CWDE: cwde */
	0,
	{ 0 }
},
{	/* X86_DAA, X86_INS_DAA: daa */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ 0 }
},
{	/* X86_DAS, X86_INS_DAS: das */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ 0 }
},
{	/* X86_DATA16_PREFIX, X86_INS_DATA16: data16 */
	0,
	{ 0 }
},
{	/* X86_DEC16m, X86_INS_DEC: dec{w}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_DEC16r, X86_INS_DEC: dec{w}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_DEC16r_alt, X86_INS_DEC: dec{w}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_DEC32m, X86_INS_DEC: dec{l}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_DEC32r, X86_INS_DEC: dec{l}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_DEC32r_alt, X86_INS_DEC: dec{l}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_DEC64m, X86_INS_DEC: dec{q}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_DEC64r, X86_INS_DEC: dec{q}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_DEC8m, X86_INS_DEC: dec{b}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_DEC8r, X86_INS_DEC: dec{b}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_DIV16m, X86_INS_DIV: div{w}	$src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_DIV16r, X86_INS_DIV: div{w}	$src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_DIV32m, X86_INS_DIV: div{l}	$src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_DIV32r, X86_INS_DIV: div{l}	$src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_DIV64m, X86_INS_DIV: div{q}	$src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_DIV64r, X86_INS_DIV: div{q}	$src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_DIV8m, X86_INS_DIV: div{b}	$src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_DIV8r, X86_INS_DIV: div{b}	$src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_ENTER, X86_INS_ENTER: enter	$len, $lvl */
	0,
	{ CS_AC_IGNORE, CS_AC_IGNORE, 0 }
},
{	/* X86_FARCALL16i, X86_INS_LCALL: lcall{w}	$seg : $off */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_FARCALL16m, X86_INS_LCALL: lcall{w}	{*}$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_FARCALL32i, X86_INS_LCALL: lcall{l}	$seg : $off */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_FARCALL32m, X86_INS_LCALL: lcall{l}	{*}$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_FARCALL64, X86_INS_LCALL: lcall{q}	{*}$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_FARJMP16i, X86_INS_LJMP: ljmp{w}	$seg : $off */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_FARJMP16m, X86_INS_LJMP: ljmp{w}	{*}$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_FARJMP32i, X86_INS_LJMP: ljmp{l}	$seg : $off */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_FARJMP32m, X86_INS_LJMP: ljmp{l}	{*}$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_FARJMP64, X86_INS_LJMP: ljmp{q}	{*}$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_FSETPM, X86_INS_FSETPM: fsetpm */
	0,
	{ 0 }
},
{	/* X86_GETSEC, X86_INS_GETSEC: getsec */
	0,
	{ 0 }
},
{	/* X86_HLT, X86_INS_HLT: hlt */
	0,
	{ 0 }
},
{	/* X86_IDIV16m, X86_INS_IDIV: idiv{w}	$src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_IDIV16r, X86_INS_IDIV: idiv{w}	$src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_IDIV32m, X86_INS_IDIV: idiv{l}	$src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_IDIV32r, X86_INS_IDIV: idiv{l}	$src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_IDIV64m, X86_INS_IDIV: idiv{q}	$src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_IDIV64r, X86_INS_IDIV: idiv{q}	$src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_IDIV8m, X86_INS_IDIV: idiv{b}	$src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_IDIV8r, X86_INS_IDIV: idiv{b}	$src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_IMUL16m, X86_INS_IMUL: imul{w}	$src */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_READ, 0 }
},
{	/* X86_IMUL16r, X86_INS_IMUL: imul{w}	$src */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_READ, 0 }
},
{	/* X86_IMUL16rm, X86_INS_IMUL: imul{w}	$dst, $src2 */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_IMUL16rmi, X86_INS_IMUL: imul{w}	$dst, $src1, $src2 */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_IMUL16rmi8, X86_INS_IMUL: imul{w}	$dst, $src1, $src2 */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_IMUL16rr, X86_INS_IMUL: imul{w}	$dst, $src2 */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_IMUL16rri, X86_INS_IMUL: imul{w}	$dst, $src1, $src2 */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_IMUL16rri8, X86_INS_IMUL: imul{w}	$dst, $src1, $src2 */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_IMUL32m, X86_INS_IMUL: imul{l}	$src */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_READ, 0 }
},
{	/* X86_IMUL32r, X86_INS_IMUL: imul{l}	$src */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_READ, 0 }
},
{	/* X86_IMUL32rm, X86_INS_IMUL: imul{l}	$dst, $src2 */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_IMUL32rmi, X86_INS_IMUL: imul{l}	$dst, $src1, $src2 */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_IMUL32rmi8, X86_INS_IMUL: imul{l}	$dst, $src1, $src2 */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_IMUL32rr, X86_INS_IMUL: imul{l}	$dst, $src2 */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_IMUL32rri, X86_INS_IMUL: imul{l}	$dst, $src1, $src2 */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_IMUL32rri8, X86_INS_IMUL: imul{l}	$dst, $src1, $src2 */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_IMUL64m, X86_INS_IMUL: imul{q}	$src */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_READ, 0 }
},
{	/* X86_IMUL64r, X86_INS_IMUL: imul{q}	$src */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_READ, 0 }
},
{	/* X86_IMUL64rm, X86_INS_IMUL: imul{q}	$dst, $src2 */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_IMUL64rmi32, X86_INS_IMUL: imul{q}	$dst, $src1, $src2 */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_IMUL64rmi8, X86_INS_IMUL: imul{q}	$dst, $src1, $src2 */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_IMUL64rr, X86_INS_IMUL: imul{q}	$dst, $src2 */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_IMUL64rri32, X86_INS_IMUL: imul{q}	$dst, $src1, $src2 */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_IMUL64rri8, X86_INS_IMUL: imul{q}	$dst, $src1, $src2 */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_IMUL8m, X86_INS_IMUL: imul{b}	$src */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_READ, 0 }
},
{	/* X86_IMUL8r, X86_INS_IMUL: imul{b}	$src */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF,
	{ CS_AC_READ, 0 }
},
{	/* X86_IN16ri, X86_INS_IN: in{w}	ax, $port */
	0,
	{ CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_IN16rr, X86_INS_IN: in{w}	ax, dx */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_IN32ri, X86_INS_IN: in{l}	eax, $port */
	0,
	{ CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_IN32rr, X86_INS_IN: in{l}	eax, dx */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_IN8ri, X86_INS_IN: in{b}	al, $port */
	0,
	{ CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_IN8rr, X86_INS_IN: in{b}	al, dx */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_INC16m, X86_INS_INC: inc{w}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_INC16r, X86_INS_INC: inc{w}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_INC16r_alt, X86_INS_INC: inc{w}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_INC32m, X86_INS_INC: inc{l}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_INC32r, X86_INS_INC: inc{l}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_INC32r_alt, X86_INS_INC: inc{l}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_INC64m, X86_INS_INC: inc{q}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_INC64r, X86_INS_INC: inc{q}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_INC8m, X86_INS_INC: inc{b}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_INC8r, X86_INS_INC: inc{b}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_INSB, X86_INS_INSB: insb	$dst, dx */
	0,
	{ CS_AC_IGNORE, CS_AC_READ, 0 }
},
{	/* X86_INSL, X86_INS_INSD: ins{l|d}	{%dx, $dst|$dst, dx} */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_INSW, X86_INS_INSW: insw	$dst, dx */
	0,
	{ CS_AC_IGNORE, CS_AC_READ, 0 }
},
{	/* X86_INT, X86_INS_INT: int	$trap */
	X86_EFLAGS_MODIFY_IF | X86_EFLAGS_MODIFY_TF | X86_EFLAGS_MODIFY_NT | X86_EFLAGS_MODIFY_RF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_INT1, X86_INS_INT1: int1 */
	X86_EFLAGS_MODIFY_IF | X86_EFLAGS_MODIFY_TF | X86_EFLAGS_MODIFY_NT | X86_EFLAGS_MODIFY_RF,
	{ 0 }
},
{	/* X86_INT3, X86_INS_INT3: int3 */
	X86_EFLAGS_MODIFY_IF | X86_EFLAGS_MODIFY_TF | X86_EFLAGS_MODIFY_NT | X86_EFLAGS_MODIFY_RF,
	{ 0 }
},
{	/* X86_INTO, X86_INS_INTO: into */
	X86_EFLAGS_MODIFY_IF | X86_EFLAGS_MODIFY_TF | X86_EFLAGS_MODIFY_NT | X86_EFLAGS_MODIFY_RF,
	{ 0 }
},
{	/* X86_INVD, X86_INS_INVD: invd */
	0,
	{ 0 }
},
{	/* X86_INVEPT32, X86_INS_INVEPT: invept	$src1, $src2 */
	0,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_INVEPT64, X86_INS_INVEPT: invept	$src1, $src2 */
	0,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_INVLPG, X86_INS_INVLPG: invlpg	$addr */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_INVLPGA32, X86_INS_INVLPGA: invlpga	eax, ecx */
	0,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_INVLPGA64, X86_INS_INVLPGA: invlpga	rax, ecx */
	0,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_INVPCID32, X86_INS_INVPCID: invpcid	$src1, $src2 */
	0,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_INVPCID64, X86_INS_INVPCID: invpcid	$src1, $src2 */
	0,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_INVVPID32, X86_INS_INVVPID: invvpid	$src1, $src2 */
	0,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_INVVPID64, X86_INS_INVVPID: invvpid	$src1, $src2 */
	0,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_IRET16, X86_INS_IRET: iret{w} */
	X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_TF | X86_EFLAGS_MODIFY_IF | X86_EFLAGS_MODIFY_DF | X86_EFLAGS_MODIFY_NT | X86_EFLAGS_MODIFY_RF,
	{ 0 }
},
{	/* X86_IRET32, X86_INS_IRETD: iretd */
	X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_TF | X86_EFLAGS_MODIFY_IF | X86_EFLAGS_MODIFY_DF | X86_EFLAGS_MODIFY_NT | X86_EFLAGS_MODIFY_RF,
	{ 0 }
},
{	/* X86_IRET64, X86_INS_IRETQ: iretq */
	X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_TF | X86_EFLAGS_MODIFY_IF | X86_EFLAGS_MODIFY_DF | X86_EFLAGS_MODIFY_NT | X86_EFLAGS_MODIFY_RF,
	{ 0 }
},
{	/* X86_JAE_1, X86_INS_JAE: jae	$dst */
	X86_EFLAGS_TEST_CF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JAE_2, X86_INS_JAE: jae	$dst */
	X86_EFLAGS_TEST_CF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JAE_4, X86_INS_JAE: jae	$dst */
	X86_EFLAGS_TEST_CF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JA_1, X86_INS_JA: ja	$dst */
	X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_CF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JA_2, X86_INS_JA: ja	$dst */
	X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_CF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JA_4, X86_INS_JA: ja	$dst */
	X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_CF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JBE_1, X86_INS_JBE: jbe	$dst */
	X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_CF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JBE_2, X86_INS_JBE: jbe	$dst */
	X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_CF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JBE_4, X86_INS_JBE: jbe	$dst */
	X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_CF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JB_1, X86_INS_JB: jb	$dst */
	X86_EFLAGS_TEST_CF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JB_2, X86_INS_JB: jb	$dst */
	X86_EFLAGS_TEST_CF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JB_4, X86_INS_JB: jb	$dst */
	X86_EFLAGS_TEST_CF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JCXZ, X86_INS_JCXZ: jcxz	$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JECXZ, X86_INS_JECXZ: jecxz	$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JE_1, X86_INS_JE: je	$dst */
	X86_EFLAGS_TEST_ZF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JE_2, X86_INS_JE: je	$dst */
	X86_EFLAGS_TEST_ZF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JE_4, X86_INS_JE: je	$dst */
	X86_EFLAGS_TEST_ZF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JGE_1, X86_INS_JGE: jge	$dst */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JGE_2, X86_INS_JGE: jge	$dst */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JGE_4, X86_INS_JGE: jge	$dst */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JG_1, X86_INS_JG: jg	$dst */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JG_2, X86_INS_JG: jg	$dst */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JG_4, X86_INS_JG: jg	$dst */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JLE_1, X86_INS_JLE: jle	$dst */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JLE_2, X86_INS_JLE: jle	$dst */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JLE_4, X86_INS_JLE: jle	$dst */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JL_1, X86_INS_JL: jl	$dst */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JL_2, X86_INS_JL: jl	$dst */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JL_4, X86_INS_JL: jl	$dst */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JMP16m, X86_INS_JMP: jmp{w}	{*}$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JMP16r, X86_INS_JMP: jmp{w}	{*}$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JMP32m, X86_INS_JMP: jmp{l}	{*}$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JMP32r, X86_INS_JMP: jmp{l}	{*}$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JMP64m, X86_INS_JMP: jmp{q}	{*}$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JMP64r, X86_INS_JMP: jmp{q}	{*}$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JMP_1, X86_INS_JMP: jmp	$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JMP_2, X86_INS_JMP: jmp	$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JMP_4, X86_INS_JMP: jmp	$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JNE_1, X86_INS_JNE: jne	$dst */
	X86_EFLAGS_TEST_ZF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JNE_2, X86_INS_JNE: jne	$dst */
	X86_EFLAGS_TEST_ZF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JNE_4, X86_INS_JNE: jne	$dst */
	X86_EFLAGS_TEST_ZF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JNO_1, X86_INS_JNO: jno	$dst */
	X86_EFLAGS_TEST_OF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JNO_2, X86_INS_JNO: jno	$dst */
	X86_EFLAGS_TEST_OF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JNO_4, X86_INS_JNO: jno	$dst */
	X86_EFLAGS_TEST_OF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JNP_1, X86_INS_JNP: jnp	$dst */
	X86_EFLAGS_TEST_PF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JNP_2, X86_INS_JNP: jnp	$dst */
	X86_EFLAGS_TEST_PF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JNP_4, X86_INS_JNP: jnp	$dst */
	X86_EFLAGS_TEST_PF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JNS_1, X86_INS_JNS: jns	$dst */
	X86_EFLAGS_TEST_SF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JNS_2, X86_INS_JNS: jns	$dst */
	X86_EFLAGS_TEST_SF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JNS_4, X86_INS_JNS: jns	$dst */
	X86_EFLAGS_TEST_SF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JO_1, X86_INS_JO: jo	$dst */
	X86_EFLAGS_TEST_OF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JO_2, X86_INS_JO: jo	$dst */
	X86_EFLAGS_TEST_OF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JO_4, X86_INS_JO: jo	$dst */
	X86_EFLAGS_TEST_OF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JP_1, X86_INS_JP: jp	$dst */
	X86_EFLAGS_TEST_PF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JP_2, X86_INS_JP: jp	$dst */
	X86_EFLAGS_TEST_PF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JP_4, X86_INS_JP: jp	$dst */
	X86_EFLAGS_TEST_PF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JRCXZ, X86_INS_JRCXZ: jrcxz	$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JS_1, X86_INS_JS: js	$dst */
	X86_EFLAGS_TEST_SF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JS_2, X86_INS_JS: js	$dst */
	X86_EFLAGS_TEST_SF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_JS_4, X86_INS_JS: js	$dst */
	X86_EFLAGS_TEST_SF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_LAHF, X86_INS_LAHF: lahf */
	0,
	{ 0 }
},
{	/* X86_LAR16rm, X86_INS_LAR: lar{w}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LAR16rr, X86_INS_LAR: lar{w}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LAR32rm, X86_INS_LAR: lar{l}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LAR32rr, X86_INS_LAR: lar{l}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LAR64rm, X86_INS_LAR: lar{q}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LAR64rr, X86_INS_LAR: lar{q}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LCMPXCHG16, X86_INS_CMPXCHG: cmpxchg{w}	$ptr, $swap */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_LCMPXCHG16B, X86_INS_CMPXCHG16B: cmpxchg16b	$ptr */
	X86_EFLAGS_MODIFY_ZF,
	{ CS_AC_READ, 0 }
},
{	/* X86_LCMPXCHG32, X86_INS_CMPXCHG: cmpxchg{l}	$ptr, $swap */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_LCMPXCHG64, X86_INS_CMPXCHG: cmpxchg{q}	$ptr, $swap */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_LCMPXCHG8, X86_INS_CMPXCHG: cmpxchg{b}	$ptr, $swap */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_LCMPXCHG8B, X86_INS_CMPXCHG8B: cmpxchg8b	$ptr */
	X86_EFLAGS_MODIFY_ZF,
	{ CS_AC_READ, 0 }
},
{	/* X86_LDS16rm, X86_INS_LDS: lds{w}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LDS32rm, X86_INS_LDS: lds{l}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LEA16r, X86_INS_LEA: lea{w}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_LEA32r, X86_INS_LEA: lea{l}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_LEA64_32r, X86_INS_LEA: lea{l}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_LEA64r, X86_INS_LEA: lea{q}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_LEAVE, X86_INS_LEAVE: leave */
	0,
	{ 0 }
},
{	/* X86_LEAVE64, X86_INS_LEAVE: leave */
	0,
	{ 0 }
},
{	/* X86_LES16rm, X86_INS_LES: les{w}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LES32rm, X86_INS_LES: les{l}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LFS16rm, X86_INS_LFS: lfs{w}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LFS32rm, X86_INS_LFS: lfs{l}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LFS64rm, X86_INS_LFS: lfs{q}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LGDT16m, X86_INS_LGDT: lgdt{w}	$src */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_LGDT32m, X86_INS_LGDT: lgdt{l}	$src */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_LGDT64m, X86_INS_LGDT: lgdt{q}	$src */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_LGS16rm, X86_INS_LGS: lgs{w}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LGS32rm, X86_INS_LGS: lgs{l}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LGS64rm, X86_INS_LGS: lgs{q}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LIDT16m, X86_INS_LIDT: lidt{w}	$src */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_LIDT32m, X86_INS_LIDT: lidt{l}	$src */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_LIDT64m, X86_INS_LIDT: lidt{q}	$src */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_LLDT16m, X86_INS_LLDT: lldt{w}	$src */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_LLDT16r, X86_INS_LLDT: lldt{w}	$src */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_LMSW16m, X86_INS_LMSW: lmsw{w}	$src */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_LMSW16r, X86_INS_LMSW: lmsw{w}	$src */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_LOCK_ADD16mi, X86_INS_ADD: add{w}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_ADD16mi8, X86_INS_ADD: add{w}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_ADD16mr, X86_INS_ADD: add{w}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_LOCK_ADD32mi, X86_INS_ADD: add{l}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_ADD32mi8, X86_INS_ADD: add{l}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_ADD32mr, X86_INS_ADD: add{l}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_LOCK_ADD64mi32, X86_INS_ADD: add{q}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_ADD64mi8, X86_INS_ADD: add{q}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_ADD64mr, X86_INS_ADD: add{q}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_LOCK_ADD8mi, X86_INS_ADD: add{b}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_ADD8mr, X86_INS_ADD: add{b}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_LOCK_AND16mi, X86_INS_AND: and{w}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_AND16mi8, X86_INS_AND: and{w}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_AND16mr, X86_INS_AND: and{w}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_LOCK_AND32mi, X86_INS_AND: and{l}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_AND32mi8, X86_INS_AND: and{l}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_AND32mr, X86_INS_AND: and{l}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_LOCK_AND64mi32, X86_INS_AND: and{q}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_AND64mi8, X86_INS_AND: and{q}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_AND64mr, X86_INS_AND: and{q}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_LOCK_AND8mi, X86_INS_AND: and{b}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_AND8mr, X86_INS_AND: and{b}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_LOCK_DEC16m, X86_INS_DEC: dec{w}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ, 0 }
},
{	/* X86_LOCK_DEC32m, X86_INS_DEC: dec{l}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ, 0 }
},
{	/* X86_LOCK_DEC64m, X86_INS_DEC: dec{q}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ, 0 }
},
{	/* X86_LOCK_DEC8m, X86_INS_DEC: dec{b}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ, 0 }
},
{	/* X86_LOCK_INC16m, X86_INS_INC: inc{w}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ, 0 }
},
{	/* X86_LOCK_INC32m, X86_INS_INC: inc{l}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ, 0 }
},
{	/* X86_LOCK_INC64m, X86_INS_INC: inc{q}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ, 0 }
},
{	/* X86_LOCK_INC8m, X86_INS_INC: inc{b}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF,
	{ CS_AC_READ, 0 }
},
{	/* X86_LOCK_OR16mi, X86_INS_OR: or{w}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_OR16mi8, X86_INS_OR: or{w}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_OR16mr, X86_INS_OR: or{w}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_LOCK_OR32mi, X86_INS_OR: or{l}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_OR32mi8, X86_INS_OR: or{l}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_OR32mr, X86_INS_OR: or{l}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_LOCK_OR64mi32, X86_INS_OR: or{q}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_OR64mi8, X86_INS_OR: or{q}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_OR64mr, X86_INS_OR: or{q}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_LOCK_OR8mi, X86_INS_OR: or{b}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_OR8mr, X86_INS_OR: or{b}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_LOCK_SUB16mi, X86_INS_SUB: sub{w}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_SUB16mi8, X86_INS_SUB: sub{w}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_SUB16mr, X86_INS_SUB: sub{w}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_LOCK_SUB32mi, X86_INS_SUB: sub{l}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_SUB32mi8, X86_INS_SUB: sub{l}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_SUB32mr, X86_INS_SUB: sub{l}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_LOCK_SUB64mi32, X86_INS_SUB: sub{q}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_SUB64mi8, X86_INS_SUB: sub{q}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_SUB64mr, X86_INS_SUB: sub{q}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_LOCK_SUB8mi, X86_INS_SUB: sub{b}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_SUB8mr, X86_INS_SUB: sub{b}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_LOCK_XOR16mi, X86_INS_XOR: xor{w}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_XOR16mi8, X86_INS_XOR: xor{w}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_XOR16mr, X86_INS_XOR: xor{w}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_LOCK_XOR32mi, X86_INS_XOR: xor{l}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_XOR32mi8, X86_INS_XOR: xor{l}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_XOR32mr, X86_INS_XOR: xor{l}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_LOCK_XOR64mi32, X86_INS_XOR: xor{q}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_XOR64mi8, X86_INS_XOR: xor{q}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_XOR64mr, X86_INS_XOR: xor{q}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_LOCK_XOR8mi, X86_INS_XOR: xor{b}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_LOCK_XOR8mr, X86_INS_XOR: xor{b}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_LODSB, X86_INS_LODSB: lodsb	al, $src */
	X86_EFLAGS_TEST_DF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LODSL, X86_INS_LODSD: lods{l|d}	{$src, %eax|eax, $src} */
	X86_EFLAGS_TEST_DF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LODSQ, X86_INS_LODSQ: lodsq	rax, $src */
	X86_EFLAGS_TEST_DF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LODSW, X86_INS_LODSW: lodsw	ax, $src */
	X86_EFLAGS_TEST_DF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LOOP, X86_INS_LOOP: loop	$dst */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_LOOPE, X86_INS_LOOPE: loope	$dst */
	X86_EFLAGS_TEST_ZF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_LOOPNE, X86_INS_LOOPNE: loopne	$dst */
	X86_EFLAGS_TEST_ZF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_LRETIL, X86_INS_RETF: {l}retf	$amt */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_LRETIQ, X86_INS_RETFQ: {l}retfq	$amt */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_LRETIW, X86_INS_RETF: {l}retf	$amt */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_LRETL, X86_INS_RETF: {l}retf */
	0,
	{ 0 }
},
{	/* X86_LRETQ, X86_INS_RETFQ: {l}retfq */
	0,
	{ 0 }
},
{	/* X86_LRETW, X86_INS_RETF: {l}retf */
	0,
	{ 0 }
},
{	/* X86_LSL16rm, X86_INS_LSL: lsl{w}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LSL16rr, X86_INS_LSL: lsl{w}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LSL32rm, X86_INS_LSL: lsl{l}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LSL32rr, X86_INS_LSL: lsl{l}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LSL64rm, X86_INS_LSL: lsl{q}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LSL64rr, X86_INS_LSL: lsl{q}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LSS16rm, X86_INS_LSS: lss{w}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LSS32rm, X86_INS_LSS: lss{l}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LSS64rm, X86_INS_LSS: lss{q}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LTRm, X86_INS_LTR: ltr{w}	$src */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_LTRr, X86_INS_LTR: ltr{w}	$src */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_LXADD16, X86_INS_XADD: xadd{w}	$ptr, $val */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_LXADD32, X86_INS_XADD: xadd{l}	$ptr, $val */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_LXADD64, X86_INS_XADD: xadd{q}	$ptr, $val */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_LXADD8, X86_INS_XADD: xadd{b}	$ptr, $val */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_LZCNT16rm, X86_INS_LZCNT: lzcnt{w}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_AF,	
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LZCNT16rr, X86_INS_LZCNT: lzcnt{w}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_AF,	
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LZCNT32rm, X86_INS_LZCNT: lzcnt{l}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_AF,	
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LZCNT32rr, X86_INS_LZCNT: lzcnt{l}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_AF,	
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LZCNT64rm, X86_INS_LZCNT: lzcnt{q}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_AF,	
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_LZCNT64rr, X86_INS_LZCNT: lzcnt{q}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_AF,	
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MONTMUL, X86_INS_MONTMUL: montmul */
	0,
	{ 0 }
},
{	/* X86_MOV16ao16, X86_INS_MOV: mov{w}	ax, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV16ao32, X86_INS_MOV: mov{w}	ax, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV16ao64, X86_INS_MOVABS: movabs{w}	ax, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV16mi, X86_INS_MOV: mov{w}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_MOV16mr, X86_INS_MOV: mov{w}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV16ms, X86_INS_MOV: mov{w}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV16o16a, X86_INS_MOV: mov{w}	$dst, ax */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV16o32a, X86_INS_MOV: mov{w}	$dst, ax */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV16o64a, X86_INS_MOVABS: movabs{w}	$dst, ax */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV16ri, X86_INS_MOV: mov{w}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_MOV16ri_alt, X86_INS_MOV: mov{w}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_MOV16rm, X86_INS_MOV: mov{w}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV16rr, X86_INS_MOV: mov{w}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV16rr_REV, X86_INS_MOV: mov{w}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV16rs, X86_INS_MOV: mov{w}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV16sm, X86_INS_MOV: mov{w}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV16sr, X86_INS_MOV: mov{w}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV32ao16, X86_INS_MOV: mov{l}	eax, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV32ao32, X86_INS_MOV: mov{l}	eax, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV32ao64, X86_INS_MOVABS: movabs{l}	eax, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV32cr, X86_INS_MOV: mov{l}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV32dr, X86_INS_MOV: mov{l}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV32mi, X86_INS_MOV: mov{l}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_MOV32mr, X86_INS_MOV: mov{l}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV32ms, X86_INS_MOV: mov{l}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV32o16a, X86_INS_MOV: mov{l}	$dst, eax */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV32o32a, X86_INS_MOV: mov{l}	$dst, eax */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV32o64a, X86_INS_MOVABS: movabs{l}	$dst, eax */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV32rc, X86_INS_MOV: mov{l}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV32rd, X86_INS_MOV: mov{l}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV32ri, X86_INS_MOV: mov{l}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_MOV32ri_alt, X86_INS_MOV: mov{l}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_MOV32rm, X86_INS_MOV: mov{l}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV32rr, X86_INS_MOV: mov{l}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV32rr_REV, X86_INS_MOV: mov{l}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV32rs, X86_INS_MOV: mov{l}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV32sm, X86_INS_MOV: mov{l}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV32sr, X86_INS_MOV: mov{l}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV64ao32, X86_INS_MOV: mov{q}	rax, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV64ao64, X86_INS_MOVABS: movabs{q}	rax, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV64cr, X86_INS_MOV: mov{q}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV64dr, X86_INS_MOV: mov{q}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV64mi32, X86_INS_MOV: mov{q}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_MOV64mr, X86_INS_MOV: mov{q}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV64ms, X86_INS_MOV: mov{q}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV64o32a, X86_INS_MOV: mov{q}	$dst, rax */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV64o64a, X86_INS_MOVABS: movabs{q}	$dst, rax */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV64rc, X86_INS_MOV: mov{q}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV64rd, X86_INS_MOV: mov{q}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV64ri, X86_INS_MOVABS: movabs{q}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_MOV64ri32, X86_INS_MOV: mov{q}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_MOV64rm, X86_INS_MOV: mov{q}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV64rr, X86_INS_MOV: mov{q}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV64rr_REV, X86_INS_MOV: mov{q}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV64rs, X86_INS_MOV: mov{q}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV64sm, X86_INS_MOV: mov{q}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV64sr, X86_INS_MOV: mov{q}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV8ao16, X86_INS_MOV: mov{b}	al, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV8ao32, X86_INS_MOV: mov{b}	al, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV8ao64, X86_INS_MOVABS: movabs{b}	al, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV8mi, X86_INS_MOV: mov{b}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_MOV8mr, X86_INS_MOV: mov{b}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV8mr_NOREX, X86_INS_MOV: mov{b}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV8o16a, X86_INS_MOV: mov{b}	$dst, al */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV8o32a, X86_INS_MOV: mov{b}	$dst, al */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV8o64a, X86_INS_MOVABS: movabs{b}	$dst, al */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV8ri, X86_INS_MOV: mov{b}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_MOV8ri_alt, X86_INS_MOV: mov{b}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_MOV8rm, X86_INS_MOV: mov{b}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV8rm_NOREX, X86_INS_MOV: mov{b}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV8rr, X86_INS_MOV: mov{b}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV8rr_NOREX, X86_INS_MOV: mov{b}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOV8rr_REV, X86_INS_MOV: mov{b}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVBE16mr, X86_INS_MOVBE: movbe{w}	$dst, $src */
	0,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_MOVBE16rm, X86_INS_MOVBE: movbe{w}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVBE32mr, X86_INS_MOVBE: movbe{l}	$dst, $src */
	0,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_MOVBE32rm, X86_INS_MOVBE: movbe{l}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVBE64mr, X86_INS_MOVBE: movbe{q}	$dst, $src */
	0,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_MOVBE64rm, X86_INS_MOVBE: movbe{q}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVSB, X86_INS_MOVSB: movsb	$dst, $src */
	X86_EFLAGS_TEST_DF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVSL, X86_INS_MOVSD: movs{l|d}	{$src, $dst|$dst, $src} */
	X86_EFLAGS_TEST_DF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVSQ, X86_INS_MOVSQ: movsq	$dst, $src */
	X86_EFLAGS_TEST_DF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVSW, X86_INS_MOVSW: movsw	$dst, $src */
	X86_EFLAGS_TEST_DF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVSX16rm8, X86_INS_MOVSX: movs{bw|x}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVSX16rr8, X86_INS_MOVSX: movs{bw|x}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVSX32_NOREXrm8, X86_INS_MOVSX: movs{bl|x}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVSX32_NOREXrr8, X86_INS_MOVSX: movs{bl|x}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVSX32rm16, X86_INS_MOVSX: movs{wl|x}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVSX32rm8, X86_INS_MOVSX: movs{bl|x}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVSX32rr16, X86_INS_MOVSX: movs{wl|x}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVSX32rr8, X86_INS_MOVSX: movs{bl|x}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVSX64_NOREXrr32, X86_INS_MOVSXD: movs{lq|xd}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVSX64rm16, X86_INS_MOVSX: movs{wq|x}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVSX64rm32, X86_INS_MOVSXD: movs{lq|xd}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVSX64rm32_alt, X86_INS_MOVSXD: movs{lq|xd}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVSX64rm8, X86_INS_MOVSX: movs{bq|x}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVSX64rr16, X86_INS_MOVSX: movs{wq|x}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVSX64rr32, X86_INS_MOVSXD: movs{lq|xd}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVSX64rr8, X86_INS_MOVSX: movs{bq|x}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVZX16rm8, X86_INS_MOVZX: movz{bw|x}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVZX16rr8, X86_INS_MOVZX: movz{bw|x}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVZX32_NOREXrm8, X86_INS_MOVZX: movz{bl|x}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVZX32_NOREXrr8, X86_INS_MOVZX: movz{bl|x}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVZX32rm16, X86_INS_MOVZX: movz{wl|x}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVZX32rm8, X86_INS_MOVZX: movz{bl|x}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVZX32rr16, X86_INS_MOVZX: movz{wl|x}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVZX32rr8, X86_INS_MOVZX: movz{bl|x}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVZX64rm16_Q, X86_INS_MOVZX: movz{wq|x}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVZX64rm8_Q, X86_INS_MOVZX: movz{bq|x}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVZX64rr16_Q, X86_INS_MOVZX: movz{wq|x}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MOVZX64rr8_Q, X86_INS_MOVZX: movz{bq|x}	{$src, $dst|$dst, $src} */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MUL16m, X86_INS_MUL: mul{w}	$src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_MUL16r, X86_INS_MUL: mul{w}	$src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_MUL32m, X86_INS_MUL: mul{l}	$src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_MUL32r, X86_INS_MUL: mul{l}	$src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_MUL64m, X86_INS_MUL: mul{q}	$src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_MUL64r, X86_INS_MUL: mul{q}	$src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_MUL8m, X86_INS_MUL: mul{b}	$src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_MUL8r, X86_INS_MUL: mul{b}	$src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_MULX32rm, X86_INS_MULX: mulx{l}	$dst1, $dst2, $src */
	0,
	{ CS_AC_WRITE, CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MULX32rr, X86_INS_MULX: mulx{l}	$dst1, $dst2, $src */
	0,
	{ CS_AC_WRITE, CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MULX64rm, X86_INS_MULX: mulx{q}	$dst1, $dst2, $src */
	0,
	{ CS_AC_WRITE, CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_MULX64rr, X86_INS_MULX: mulx{q}	$dst1, $dst2, $src */
	0,
	{ CS_AC_WRITE, CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_NEG16m, X86_INS_NEG: neg{w}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_NEG16r, X86_INS_NEG: neg{w}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_NEG32m, X86_INS_NEG: neg{l}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_NEG32r, X86_INS_NEG: neg{l}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_NEG64m, X86_INS_NEG: neg{q}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_NEG64r, X86_INS_NEG: neg{q}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_NEG8m, X86_INS_NEG: neg{b}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_NEG8r, X86_INS_NEG: neg{b}	$dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_NOOP, X86_INS_NOP: nop */
	0,
	{ 0 }
},
{	/* X86_NOOP18_16m4, X86_INS_NOP: nop{w}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOP18_16m5, X86_INS_NOP: nop{w}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOP18_16m6, X86_INS_NOP: nop{w}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOP18_16m7, X86_INS_NOP: nop{w}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOP18_16r4, X86_INS_NOP: nop{w}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOP18_16r5, X86_INS_NOP: nop{w}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOP18_16r6, X86_INS_NOP: nop{w}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOP18_16r7, X86_INS_NOP: nop{w}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOP18_m4, X86_INS_NOP: nop{l}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOP18_m5, X86_INS_NOP: nop{l}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOP18_m6, X86_INS_NOP: nop{l}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOP18_m7, X86_INS_NOP: nop{l}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOP18_r4, X86_INS_NOP: nop{l}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOP18_r5, X86_INS_NOP: nop{l}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOP18_r6, X86_INS_NOP: nop{l}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOP18_r7, X86_INS_NOP: nop{l}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOP19rr, X86_INS_NOP: nop	$src, $val */
	0,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_NOOPL, X86_INS_NOP: nop{l}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOPL_19, X86_INS_NOP: nop{l}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOPL_1a, X86_INS_NOP: nop{l}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOPL_1b, X86_INS_NOP: nop{l}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOPL_1c, X86_INS_NOP: nop{l}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOPL_1d, X86_INS_NOP: nop{l}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOPL_1e, X86_INS_NOP: nop{l}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOPW, X86_INS_NOP: nop{w}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOPW_19, X86_INS_NOP: nop{w}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOPW_1a, X86_INS_NOP: nop{w}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOPW_1b, X86_INS_NOP: nop{w}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOPW_1c, X86_INS_NOP: nop{w}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOPW_1d, X86_INS_NOP: nop{w}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOOPW_1e, X86_INS_NOP: nop{w}	$zero */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_NOT16m, X86_INS_NOT: not{w}	$dst */
	0,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_NOT16r, X86_INS_NOT: not{w}	$dst */
	0,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_NOT32m, X86_INS_NOT: not{l}	$dst */
	0,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_NOT32r, X86_INS_NOT: not{l}	$dst */
	0,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_NOT64m, X86_INS_NOT: not{q}	$dst */
	0,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_NOT64r, X86_INS_NOT: not{q}	$dst */
	0,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_NOT8m, X86_INS_NOT: not{b}	$dst */
	0,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_NOT8r, X86_INS_NOT: not{b}	$dst */
	0,
	{ CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_OR16i16, X86_INS_OR: or{w}	ax, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_OR16mi, X86_INS_OR: or{w}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_OR16mi8, X86_INS_OR: or{w}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_OR16mr, X86_INS_OR: or{w}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_OR16ri, X86_INS_OR: or{w}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_OR16ri8, X86_INS_OR: or{w}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_OR16rm, X86_INS_OR: or{w}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_OR16rr, X86_INS_OR: or{w}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_OR16rr_REV, X86_INS_OR: or{w}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_OR32i32, X86_INS_OR: or{l}	eax, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_OR32mi, X86_INS_OR: or{l}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_OR32mi8, X86_INS_OR: or{l}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_OR32mr, X86_INS_OR: or{l}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_OR32mrLocked, X86_INS_OR: or{l}	$dst, $zero */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_OR32ri, X86_INS_OR: or{l}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_OR32ri8, X86_INS_OR: or{l}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_OR32rm, X86_INS_OR: or{l}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_OR32rr, X86_INS_OR: or{l}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_OR32rr_REV, X86_INS_OR: or{l}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_OR64i32, X86_INS_OR: or{q}	rax, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_OR64mi32, X86_INS_OR: or{q}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_OR64mi8, X86_INS_OR: or{q}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_OR64mr, X86_INS_OR: or{q}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_OR64ri32, X86_INS_OR: or{q}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_OR64ri8, X86_INS_OR: or{q}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_OR64rm, X86_INS_OR: or{q}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_OR64rr, X86_INS_OR: or{q}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_OR64rr_REV, X86_INS_OR: or{q}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_OR8i8, X86_INS_OR: or{b}	al, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_OR8mi, X86_INS_OR: or{b}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_OR8mi8, X86_INS_OR: or{b}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_OR8mr, X86_INS_OR: or{b}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_OR8ri, X86_INS_OR: or{b}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_OR8ri8, X86_INS_OR: or{b}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_OR8rm, X86_INS_OR: or{b}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_OR8rr, X86_INS_OR: or{b}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_OR8rr_REV, X86_INS_OR: or{b}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_OUT16ir, X86_INS_OUT: out{w}	$port, ax */
	0,
	{ CS_AC_IGNORE, CS_AC_READ, 0 }
},
{	/* X86_OUT16rr, X86_INS_OUT: out{w}	dx, ax */
	0,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_OUT32ir, X86_INS_OUT: out{l}	$port, eax */
	0,
	{ CS_AC_IGNORE, CS_AC_READ, 0 }
},
{	/* X86_OUT32rr, X86_INS_OUT: out{l}	dx, eax */
	0,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_OUT8ir, X86_INS_OUT: out{b}	$port, al */
	0,
	{ CS_AC_IGNORE, CS_AC_READ, 0 }
},
{	/* X86_OUT8rr, X86_INS_OUT: out{b}	dx, al */
	0,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_OUTSB, X86_INS_OUTSB: outsb	dx, $src */
	X86_EFLAGS_TEST_DF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_OUTSL, X86_INS_OUTSD: outs{l|d}	{$src, %dx|dx, $src} */
	X86_EFLAGS_TEST_DF,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_OUTSW, X86_INS_OUTSW: outsw	dx, $src */
	X86_EFLAGS_TEST_DF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_PCOMMIT, X86_INS_PCOMMIT: pcommit */
	0,
	{ 0 }
},
{	/* X86_PDEP32rm, X86_INS_PDEP: pdep{l}	$dst, $src1, $src2 */
	0,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_PDEP32rr, X86_INS_PDEP: pdep{l}	$dst, $src1, $src2 */
	0,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_PDEP64rm, X86_INS_PDEP: pdep{q}	$dst, $src1, $src2 */
	0,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_PDEP64rr, X86_INS_PDEP: pdep{q}	$dst, $src1, $src2 */
	0,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_PEXT32rm, X86_INS_PEXT: pext{l}	$dst, $src1, $src2 */
	0,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_PEXT32rr, X86_INS_PEXT: pext{l}	$dst, $src1, $src2 */
	0,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_PEXT64rm, X86_INS_PEXT: pext{q}	$dst, $src1, $src2 */
	0,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_PEXT64rr, X86_INS_PEXT: pext{q}	$dst, $src1, $src2 */
	0,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_POP16r, X86_INS_POP: pop{w}	$reg */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_POP16rmm, X86_INS_POP: pop{w}	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_POP16rmr, X86_INS_POP: pop{w}	$reg */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_POP32r, X86_INS_POP: pop{l}	$reg */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_POP32rmm, X86_INS_POP: pop{l}	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_POP32rmr, X86_INS_POP: pop{l}	$reg */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_POP64r, X86_INS_POP: pop{q}	$reg */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_POP64rmm, X86_INS_POP: pop{q}	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_POP64rmr, X86_INS_POP: pop{q}	$reg */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_POPA16, X86_INS_POPAW: popaw */
	0,
	{ 0 }
},
{	/* X86_POPA32, X86_INS_POPAL: popal */
	0,
	{ 0 }
},
{	/* X86_POPDS16, X86_INS_POP: pop{w}	ds */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_POPDS32, X86_INS_POP: pop{l}	ds */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_POPES16, X86_INS_POP: pop{w}	es */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_POPES32, X86_INS_POP: pop{l}	es */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_POPF16, X86_INS_POPF: popf{w} */
	X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_TF | X86_EFLAGS_MODIFY_IF | X86_EFLAGS_MODIFY_DF | X86_EFLAGS_MODIFY_NT | X86_EFLAGS_MODIFY_RF,
	{ 0 }
},
{	/* X86_POPF32, X86_INS_POPFD: popfd */
	X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_TF | X86_EFLAGS_MODIFY_IF | X86_EFLAGS_MODIFY_DF | X86_EFLAGS_MODIFY_NT | X86_EFLAGS_MODIFY_RF,
	{ 0 }
},
{	/* X86_POPF64, X86_INS_POPFQ: popfq */
	X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_TF | X86_EFLAGS_MODIFY_IF | X86_EFLAGS_MODIFY_DF | X86_EFLAGS_MODIFY_NT | X86_EFLAGS_MODIFY_RF,
	{ 0 }
},
{	/* X86_POPFS16, X86_INS_POP: pop{w}	fs */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_POPFS32, X86_INS_POP: pop{l}	fs */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_POPFS64, X86_INS_POP: pop{q}	fs */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_POPGS16, X86_INS_POP: pop{w}	gs */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_POPGS32, X86_INS_POP: pop{l}	gs */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_POPGS64, X86_INS_POP: pop{q}	gs */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_POPSS16, X86_INS_POP: pop{w}	ss */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_POPSS32, X86_INS_POP: pop{l}	ss */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_PUSH16i8, X86_INS_PUSH: push{w}	$imm */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_PUSH16r, X86_INS_PUSH: push{w}	$reg */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_PUSH16rmm, X86_INS_PUSH: push{w}	$src */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_PUSH16rmr, X86_INS_PUSH: push{w}	$reg */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_PUSH32i8, X86_INS_PUSH: push{l}	$imm */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_PUSH32r, X86_INS_PUSH: push{l}	$reg */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_PUSH32rmm, X86_INS_PUSH: push{l}	$src */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_PUSH32rmr, X86_INS_PUSH: push{l}	$reg */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_PUSH64i16, X86_INS_PUSH: push{w}	$imm */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_PUSH64i32, X86_INS_PUSH: push{q}	$imm */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_PUSH64i8, X86_INS_PUSH: push{q}	$imm */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_PUSH64r, X86_INS_PUSH: push{q}	$reg */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_PUSH64rmm, X86_INS_PUSH: push{q}	$src */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_PUSH64rmr, X86_INS_PUSH: push{q}	$reg */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_PUSHA16, X86_INS_PUSHAW: pushaw */
	0,
	{ 0 }
},
{	/* X86_PUSHA32, X86_INS_PUSHAL: pushal */
	0,
	{ 0 }
},
{	/* X86_PUSHCS16, X86_INS_PUSH: push{w}	cs */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_PUSHCS32, X86_INS_PUSH: push{l}	cs */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_PUSHDS16, X86_INS_PUSH: push{w}	ds */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_PUSHDS32, X86_INS_PUSH: push{l}	ds */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_PUSHES16, X86_INS_PUSH: push{w}	es */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_PUSHES32, X86_INS_PUSH: push{l}	es */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_PUSHF16, X86_INS_PUSHF: pushf{w} */
	0,
	{ 0 }
},
{	/* X86_PUSHF32, X86_INS_PUSHFD: pushfd */
	0,
	{ 0 }
},
{	/* X86_PUSHF64, X86_INS_PUSHFQ: pushfq */
	0,
	{ 0 }
},
{	/* X86_PUSHFS16, X86_INS_PUSH: push{w}	fs */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_PUSHFS32, X86_INS_PUSH: push{l}	fs */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_PUSHFS64, X86_INS_PUSH: push{q}	fs */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_PUSHGS16, X86_INS_PUSH: push{w}	gs */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_PUSHGS32, X86_INS_PUSH: push{l}	gs */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_PUSHGS64, X86_INS_PUSH: push{q}	gs */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_PUSHSS16, X86_INS_PUSH: push{w}	ss */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_PUSHSS32, X86_INS_PUSH: push{l}	ss */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_PUSHi16, X86_INS_PUSH: push{w}	$imm */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_PUSHi32, X86_INS_PUSH: push{l}	$imm */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_RCL16m1, X86_INS_RCL: rcl{w}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_RCL16mCL, X86_INS_RCL: rcl{w}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_RCL16mi, X86_INS_RCL: rcl{w}	$dst, $cnt */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_RCL16r1, X86_INS_RCL: rcl{w}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_RCL16rCL, X86_INS_RCL: rcl{w}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_RCL16ri, X86_INS_RCL: rcl{w}	$dst, $cnt */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_RCL32m1, X86_INS_RCL: rcl{l}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_RCL32mCL, X86_INS_RCL: rcl{l}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_RCL32mi, X86_INS_RCL: rcl{l}	$dst, $cnt */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_RCL32r1, X86_INS_RCL: rcl{l}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_RCL32rCL, X86_INS_RCL: rcl{l}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_RCL32ri, X86_INS_RCL: rcl{l}	$dst, $cnt */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_RCL64m1, X86_INS_RCL: rcl{q}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_RCL64mCL, X86_INS_RCL: rcl{q}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_RCL64mi, X86_INS_RCL: rcl{q}	$dst, $cnt */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_RCL64r1, X86_INS_RCL: rcl{q}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_RCL64rCL, X86_INS_RCL: rcl{q}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_RCL64ri, X86_INS_RCL: rcl{q}	$dst, $cnt */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_RCL8m1, X86_INS_RCL: rcl{b}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_RCL8mCL, X86_INS_RCL: rcl{b}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_RCL8mi, X86_INS_RCL: rcl{b}	$dst, $cnt */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_RCL8r1, X86_INS_RCL: rcl{b}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_RCL8rCL, X86_INS_RCL: rcl{b}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_RCL8ri, X86_INS_RCL: rcl{b}	$dst, $cnt */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_RCR16m1, X86_INS_RCR: rcr{w}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_RCR16mCL, X86_INS_RCR: rcr{w}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_RCR16mi, X86_INS_RCR: rcr{w}	$dst, $cnt */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_RCR16r1, X86_INS_RCR: rcr{w}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_RCR16rCL, X86_INS_RCR: rcr{w}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_RCR16ri, X86_INS_RCR: rcr{w}	$dst, $cnt */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_RCR32m1, X86_INS_RCR: rcr{l}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_RCR32mCL, X86_INS_RCR: rcr{l}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_RCR32mi, X86_INS_RCR: rcr{l}	$dst, $cnt */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_RCR32r1, X86_INS_RCR: rcr{l}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_RCR32rCL, X86_INS_RCR: rcr{l}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_RCR32ri, X86_INS_RCR: rcr{l}	$dst, $cnt */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_RCR64m1, X86_INS_RCR: rcr{q}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_RCR64mCL, X86_INS_RCR: rcr{q}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_RCR64mi, X86_INS_RCR: rcr{q}	$dst, $cnt */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_RCR64r1, X86_INS_RCR: rcr{q}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_RCR64rCL, X86_INS_RCR: rcr{q}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_RCR64ri, X86_INS_RCR: rcr{q}	$dst, $cnt */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_RCR8m1, X86_INS_RCR: rcr{b}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_RCR8mCL, X86_INS_RCR: rcr{b}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_RCR8mi, X86_INS_RCR: rcr{b}	$dst, $cnt */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_RCR8r1, X86_INS_RCR: rcr{b}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_RCR8rCL, X86_INS_RCR: rcr{b}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_RCR8ri, X86_INS_RCR: rcr{b}	$dst, $cnt */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_RDFSBASE, X86_INS_RDFSBASE: rdfsbase{l}	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_RDFSBASE64, X86_INS_RDFSBASE: rdfsbase{q}	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_RDGSBASE, X86_INS_RDGSBASE: rdgsbase{l}	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_RDGSBASE64, X86_INS_RDGSBASE: rdgsbase{q}	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_RDMSR, X86_INS_RDMSR: rdmsr */
	0,
	{ 0 }
},
{	/* X86_RDPMC, X86_INS_RDPMC: rdpmc */
	0,
	{ 0 }
},
{	/* X86_RDRAND16r, X86_INS_RDRAND: rdrand{w}	$dst */
	X86_EFLAGS_MODIFY_CF | X86_EFLAGS_RESET_OF | X86_EFLAGS_RESET_SF | X86_EFLAGS_RESET_ZF | X86_EFLAGS_RESET_AF | X86_EFLAGS_RESET_PF,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_RDRAND32r, X86_INS_RDRAND: rdrand{l}	$dst */
	X86_EFLAGS_MODIFY_CF | X86_EFLAGS_RESET_OF | X86_EFLAGS_RESET_SF | X86_EFLAGS_RESET_ZF | X86_EFLAGS_RESET_AF | X86_EFLAGS_RESET_PF,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_RDRAND64r, X86_INS_RDRAND: rdrand{q}	$dst */
	X86_EFLAGS_MODIFY_CF | X86_EFLAGS_RESET_OF | X86_EFLAGS_RESET_SF | X86_EFLAGS_RESET_ZF | X86_EFLAGS_RESET_AF | X86_EFLAGS_RESET_PF,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_RDSEED16r, X86_INS_RDSEED: rdseed{w}	$dst */
	X86_EFLAGS_MODIFY_CF | X86_EFLAGS_RESET_OF | X86_EFLAGS_RESET_SF | X86_EFLAGS_RESET_ZF | X86_EFLAGS_RESET_AF | X86_EFLAGS_RESET_PF,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_RDSEED32r, X86_INS_RDSEED: rdseed{l}	$dst */
	X86_EFLAGS_MODIFY_CF | X86_EFLAGS_RESET_OF | X86_EFLAGS_RESET_SF | X86_EFLAGS_RESET_ZF | X86_EFLAGS_RESET_AF | X86_EFLAGS_RESET_PF,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_RDSEED64r, X86_INS_RDSEED: rdseed{q}	$dst */
	X86_EFLAGS_MODIFY_CF | X86_EFLAGS_RESET_OF | X86_EFLAGS_RESET_SF | X86_EFLAGS_RESET_ZF | X86_EFLAGS_RESET_AF | X86_EFLAGS_RESET_PF,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_RDTSC, X86_INS_RDTSC: rdtsc */
	0,
	{ 0 }
},
{	/* X86_RDTSCP, X86_INS_RDTSCP: rdtscp */
	0,
	{ 0 }
},
{	/* X86_RETIL, X86_INS_RET: ret{l}	$amt */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_RETIQ, X86_INS_RET: ret{q}	$amt */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_RETIW, X86_INS_RET: ret{w}	$amt */
	0,
	{ CS_AC_IGNORE, 0 }
},
{	/* X86_RETL, X86_INS_RET: ret{l} */
	0,
	{ 0 }
},
{	/* X86_RETQ, X86_INS_RET: ret{q} */
	0,
	{ 0 }
},
{	/* X86_RETW, X86_INS_RET: ret{w} */
	0,
	{ 0 }
},
{	/* X86_ROL16m1, X86_INS_ROL: rol{w}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_ROL16mCL, X86_INS_ROL: rol{w}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_ROL16mi, X86_INS_ROL: rol{w}	$dst, $src1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_ROL16r1, X86_INS_ROL: rol{w}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ROL16rCL, X86_INS_ROL: rol{w}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ROL16ri, X86_INS_ROL: rol{w}	$dst, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ROL32m1, X86_INS_ROL: rol{l}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_ROL32mCL, X86_INS_ROL: rol{l}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_ROL32mi, X86_INS_ROL: rol{l}	$dst, $src1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_ROL32r1, X86_INS_ROL: rol{l}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ROL32rCL, X86_INS_ROL: rol{l}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ROL32ri, X86_INS_ROL: rol{l}	$dst, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ROL64m1, X86_INS_ROL: rol{q}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_ROL64mCL, X86_INS_ROL: rol{q}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_ROL64mi, X86_INS_ROL: rol{q}	$dst, $src1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_ROL64r1, X86_INS_ROL: rol{q}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ROL64rCL, X86_INS_ROL: rol{q}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ROL64ri, X86_INS_ROL: rol{q}	$dst, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ROL8m1, X86_INS_ROL: rol{b}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_ROL8mCL, X86_INS_ROL: rol{b}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_ROL8mi, X86_INS_ROL: rol{b}	$dst, $src1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_ROL8r1, X86_INS_ROL: rol{b}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ROL8rCL, X86_INS_ROL: rol{b}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ROL8ri, X86_INS_ROL: rol{b}	$dst, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ROR16m1, X86_INS_ROR: ror{w}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_ROR16mCL, X86_INS_ROR: ror{w}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_ROR16mi, X86_INS_ROR: ror{w}	$dst, $src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_ROR16r1, X86_INS_ROR: ror{w}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ROR16rCL, X86_INS_ROR: ror{w}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ROR16ri, X86_INS_ROR: ror{w}	$dst, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ROR32m1, X86_INS_ROR: ror{l}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_ROR32mCL, X86_INS_ROR: ror{l}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_ROR32mi, X86_INS_ROR: ror{l}	$dst, $src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_ROR32r1, X86_INS_ROR: ror{l}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ROR32rCL, X86_INS_ROR: ror{l}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ROR32ri, X86_INS_ROR: ror{l}	$dst, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ROR64m1, X86_INS_ROR: ror{q}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_ROR64mCL, X86_INS_ROR: ror{q}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_ROR64mi, X86_INS_ROR: ror{q}	$dst, $src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_ROR64r1, X86_INS_ROR: ror{q}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ROR64rCL, X86_INS_ROR: ror{q}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ROR64ri, X86_INS_ROR: ror{q}	$dst, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ROR8m1, X86_INS_ROR: ror{b}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_ROR8mCL, X86_INS_ROR: ror{b}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_ROR8mi, X86_INS_ROR: ror{b}	$dst, $src */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_ROR8r1, X86_INS_ROR: ror{b}	$dst, 1 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_ROR8rCL, X86_INS_ROR: ror{b}	$dst, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_ROR8ri, X86_INS_ROR: ror{b}	$dst, $src2 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_RORX32mi, X86_INS_RORX: rorx{l}	$dst, $src1, $src2 */
	0,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_RORX32ri, X86_INS_RORX: rorx{l}	$dst, $src1, $src2 */
	0,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_RORX64mi, X86_INS_RORX: rorx{q}	$dst, $src1, $src2 */
	0,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_RORX64ri, X86_INS_RORX: rorx{q}	$dst, $src1, $src2 */
	0,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_RSM, X86_INS_RSM: rsm */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_TF | X86_EFLAGS_MODIFY_IF | X86_EFLAGS_MODIFY_DF | X86_EFLAGS_MODIFY_NT | X86_EFLAGS_MODIFY_RF,
	{ 0 }
},
{	/* X86_SAHF, X86_INS_SAHF: sahf */
	X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ 0 }
},
{	/* X86_SAL16m1, X86_INS_SAL: sal{w}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAL16mCL, X86_INS_SAL: sal{w}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SAL16mi, X86_INS_SAL: sal{w}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAL16r1, X86_INS_SAL: sal{w}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAL16rCL, X86_INS_SAL: sal{w}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SAL16ri, X86_INS_SAL: sal{w}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAL32m1, X86_INS_SAL: sal{l}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAL32mCL, X86_INS_SAL: sal{l}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SAL32mi, X86_INS_SAL: sal{l}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAL32r1, X86_INS_SAL: sal{l}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAL32rCL, X86_INS_SAL: sal{l}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SAL32ri, X86_INS_SAL: sal{l}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAL64m1, X86_INS_SAL: sal{q}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAL64mCL, X86_INS_SAL: sal{q}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SAL64mi, X86_INS_SAL: sal{q}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAL64r1, X86_INS_SAL: sal{q}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAL64rCL, X86_INS_SAL: sal{q}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SAL64ri, X86_INS_SAL: sal{q}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAL8m1, X86_INS_SAL: sal{b}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAL8mCL, X86_INS_SAL: sal{b}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SAL8mi, X86_INS_SAL: sal{b}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAL8r1, X86_INS_SAL: sal{b}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAL8rCL, X86_INS_SAL: sal{b}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SAL8ri, X86_INS_SAL: sal{b}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SALC, X86_INS_SALC: salc */
	0,
	{ 0 }
},
{	/* X86_SAR16m1, X86_INS_SAR: sar{w}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAR16mCL, X86_INS_SAR: sar{w}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SAR16mi, X86_INS_SAR: sar{w}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAR16r1, X86_INS_SAR: sar{w}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAR16rCL, X86_INS_SAR: sar{w}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SAR16ri, X86_INS_SAR: sar{w}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAR32m1, X86_INS_SAR: sar{l}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAR32mCL, X86_INS_SAR: sar{l}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SAR32mi, X86_INS_SAR: sar{l}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAR32r1, X86_INS_SAR: sar{l}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAR32rCL, X86_INS_SAR: sar{l}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SAR32ri, X86_INS_SAR: sar{l}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAR64m1, X86_INS_SAR: sar{q}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAR64mCL, X86_INS_SAR: sar{q}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SAR64mi, X86_INS_SAR: sar{q}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAR64r1, X86_INS_SAR: sar{q}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAR64rCL, X86_INS_SAR: sar{q}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SAR64ri, X86_INS_SAR: sar{q}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAR8m1, X86_INS_SAR: sar{b}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAR8mCL, X86_INS_SAR: sar{b}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SAR8mi, X86_INS_SAR: sar{b}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAR8r1, X86_INS_SAR: sar{b}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SAR8rCL, X86_INS_SAR: sar{b}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SAR8ri, X86_INS_SAR: sar{b}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SARX32rm, X86_INS_SARX: sarx{l}	$dst, $src1, $src2 */
	0,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SARX32rr, X86_INS_SARX: sarx{l}	$dst, $src1, $src2 */
	0,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SARX64rm, X86_INS_SARX: sarx{q}	$dst, $src1, $src2 */
	0,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SARX64rr, X86_INS_SARX: sarx{q}	$dst, $src1, $src2 */
	0,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SBB16i16, X86_INS_SBB: sbb{w}	ax, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SBB16mi, X86_INS_SBB: sbb{w}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SBB16mi8, X86_INS_SBB: sbb{w}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SBB16mr, X86_INS_SBB: sbb{w}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SBB16ri, X86_INS_SBB: sbb{w}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SBB16ri8, X86_INS_SBB: sbb{w}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SBB16rm, X86_INS_SBB: sbb{w}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SBB16rr, X86_INS_SBB: sbb{w}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SBB16rr_REV, X86_INS_SBB: sbb{w}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SBB32i32, X86_INS_SBB: sbb{l}	eax, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SBB32mi, X86_INS_SBB: sbb{l}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SBB32mi8, X86_INS_SBB: sbb{l}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SBB32mr, X86_INS_SBB: sbb{l}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SBB32ri, X86_INS_SBB: sbb{l}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SBB32ri8, X86_INS_SBB: sbb{l}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SBB32rm, X86_INS_SBB: sbb{l}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SBB32rr, X86_INS_SBB: sbb{l}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SBB32rr_REV, X86_INS_SBB: sbb{l}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SBB64i32, X86_INS_SBB: sbb{q}	rax, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SBB64mi32, X86_INS_SBB: sbb{q}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SBB64mi8, X86_INS_SBB: sbb{q}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SBB64mr, X86_INS_SBB: sbb{q}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SBB64ri32, X86_INS_SBB: sbb{q}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SBB64ri8, X86_INS_SBB: sbb{q}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SBB64rm, X86_INS_SBB: sbb{q}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SBB64rr, X86_INS_SBB: sbb{q}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SBB64rr_REV, X86_INS_SBB: sbb{q}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SBB8i8, X86_INS_SBB: sbb{b}	al, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SBB8mi, X86_INS_SBB: sbb{b}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SBB8mi8, X86_INS_SBB: sbb{b}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SBB8mr, X86_INS_SBB: sbb{b}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SBB8ri, X86_INS_SBB: sbb{b}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SBB8ri8, X86_INS_SBB: sbb{b}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SBB8rm, X86_INS_SBB: sbb{b}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SBB8rr, X86_INS_SBB: sbb{b}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SBB8rr_REV, X86_INS_SBB: sbb{b}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SCASB, X86_INS_SCASB: scasb	al, $dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SCASL, X86_INS_SCASD: scas{l|d}	{$dst, %eax|eax, $dst} */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SCASQ, X86_INS_SCASQ: scasq	rax, $dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SCASW, X86_INS_SCASW: scasw	ax, $dst */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SETAEm, X86_INS_SETAE: setae	$dst */
	X86_EFLAGS_TEST_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_SETAEr, X86_INS_SETAE: setae	$dst */
	X86_EFLAGS_TEST_CF,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SETAm, X86_INS_SETA: seta	$dst */
	X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_SETAr, X86_INS_SETA: seta	$dst */
	X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_CF,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SETBEm, X86_INS_SETBE: setbe	$dst */
	X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_SETBEr, X86_INS_SETBE: setbe	$dst */
	X86_EFLAGS_TEST_ZF | X86_EFLAGS_TEST_CF,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SETBm, X86_INS_SETB: setb	$dst */
	X86_EFLAGS_TEST_CF,
	{ CS_AC_READ, 0 }
},
{	/* X86_SETBr, X86_INS_SETB: setb	$dst */
	X86_EFLAGS_TEST_CF,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SETEm, X86_INS_SETE: sete	$dst */
	X86_EFLAGS_TEST_ZF,
	{ CS_AC_READ, 0 }
},
{	/* X86_SETEr, X86_INS_SETE: sete	$dst */
	X86_EFLAGS_TEST_ZF,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SETGEm, X86_INS_SETGE: setge	$dst */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF,
	{ CS_AC_READ, 0 }
},
{	/* X86_SETGEr, X86_INS_SETGE: setge	$dst */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SETGm, X86_INS_SETG: setg	$dst */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF,
	{ CS_AC_READ, 0 }
},
{	/* X86_SETGr, X86_INS_SETG: setg	$dst */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SETLEm, X86_INS_SETLE: setle	$dst */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF,
	{ CS_AC_READ, 0 }
},
{	/* X86_SETLEr, X86_INS_SETLE: setle	$dst */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF | X86_EFLAGS_TEST_ZF,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SETLm, X86_INS_SETL: setl	$dst */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF,
	{ CS_AC_READ, 0 }
},
{	/* X86_SETLr, X86_INS_SETL: setl	$dst */
	X86_EFLAGS_TEST_OF | X86_EFLAGS_TEST_SF,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SETNEm, X86_INS_SETNE: setne	$dst */
	X86_EFLAGS_TEST_ZF,
	{ CS_AC_READ, 0 }
},
{	/* X86_SETNEr, X86_INS_SETNE: setne	$dst */
	X86_EFLAGS_TEST_ZF,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SETNOm, X86_INS_SETNO: setno	$dst */
	X86_EFLAGS_TEST_OF,
	{ CS_AC_READ, 0 }
},
{	/* X86_SETNOr, X86_INS_SETNO: setno	$dst */
	X86_EFLAGS_TEST_OF,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SETNPm, X86_INS_SETNP: setnp	$dst */
	X86_EFLAGS_TEST_PF,
	{ CS_AC_READ, 0 }
},
{	/* X86_SETNPr, X86_INS_SETNP: setnp	$dst */
	X86_EFLAGS_TEST_PF,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SETNSm, X86_INS_SETNS: setns	$dst */
	X86_EFLAGS_TEST_SF,
	{ CS_AC_READ, 0 }
},
{	/* X86_SETNSr, X86_INS_SETNS: setns	$dst */
	X86_EFLAGS_TEST_SF,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SETOm, X86_INS_SETO: seto	$dst */
	X86_EFLAGS_TEST_OF,
	{ CS_AC_READ, 0 }
},
{	/* X86_SETOr, X86_INS_SETO: seto	$dst */
	X86_EFLAGS_TEST_OF,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SETPm, X86_INS_SETP: setp	$dst */
	X86_EFLAGS_TEST_PF,
	{ CS_AC_READ, 0 }
},
{	/* X86_SETPr, X86_INS_SETP: setp	$dst */
	X86_EFLAGS_TEST_PF,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SETSm, X86_INS_SETS: sets	$dst */
	X86_EFLAGS_TEST_SF,
	{ CS_AC_READ, 0 }
},
{	/* X86_SETSr, X86_INS_SETS: sets	$dst */
	X86_EFLAGS_TEST_SF,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SGDT16m, X86_INS_SGDT: sgdt{w}	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SGDT32m, X86_INS_SGDT: sgdt{l}	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SGDT64m, X86_INS_SGDT: sgdt{q}	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SHL16m1, X86_INS_SHL: shl{w}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHL16mCL, X86_INS_SHL: shl{w}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SHL16mi, X86_INS_SHL: shl{w}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHL16r1, X86_INS_SHL: shl{w}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHL16rCL, X86_INS_SHL: shl{w}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SHL16ri, X86_INS_SHL: shl{w}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHL32m1, X86_INS_SHL: shl{l}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHL32mCL, X86_INS_SHL: shl{l}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SHL32mi, X86_INS_SHL: shl{l}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHL32r1, X86_INS_SHL: shl{l}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHL32rCL, X86_INS_SHL: shl{l}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SHL32ri, X86_INS_SHL: shl{l}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHL64m1, X86_INS_SHL: shl{q}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHL64mCL, X86_INS_SHL: shl{q}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SHL64mi, X86_INS_SHL: shl{q}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHL64r1, X86_INS_SHL: shl{q}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHL64rCL, X86_INS_SHL: shl{q}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SHL64ri, X86_INS_SHL: shl{q}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHL8m1, X86_INS_SHL: shl{b}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHL8mCL, X86_INS_SHL: shl{b}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SHL8mi, X86_INS_SHL: shl{b}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHL8r1, X86_INS_SHL: shl{b}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHL8rCL, X86_INS_SHL: shl{b}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SHL8ri, X86_INS_SHL: shl{b}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHLD16mrCL, X86_INS_SHLD: shld{w}	$dst, $src2, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SHLD16mri8, X86_INS_SHLD: shld{w}	$dst, $src2, $src3 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_SHLD16rrCL, X86_INS_SHLD: shld{w}	$dst, $src2, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SHLD16rri8, X86_INS_SHLD: shld{w}	$dst, $src2, $src3 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_SHLD32mrCL, X86_INS_SHLD: shld{l}	$dst, $src2, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SHLD32mri8, X86_INS_SHLD: shld{l}	$dst, $src2, $src3 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_SHLD32rrCL, X86_INS_SHLD: shld{l}	$dst, $src2, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SHLD32rri8, X86_INS_SHLD: shld{l}	$dst, $src2, $src3 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_SHLD64mrCL, X86_INS_SHLD: shld{q}	$dst, $src2, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SHLD64mri8, X86_INS_SHLD: shld{q}	$dst, $src2, $src3 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_SHLD64rrCL, X86_INS_SHLD: shld{q}	$dst, $src2, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SHLD64rri8, X86_INS_SHLD: shld{q}	$dst, $src2, $src3 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_SHLX32rm, X86_INS_SHLX: shlx{l}	$dst, $src1, $src2 */
	0,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SHLX32rr, X86_INS_SHLX: shlx{l}	$dst, $src1, $src2 */
	0,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SHLX64rm, X86_INS_SHLX: shlx{q}	$dst, $src1, $src2 */
	0,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SHLX64rr, X86_INS_SHLX: shlx{q}	$dst, $src1, $src2 */
	0,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SHR16m1, X86_INS_SHR: shr{w}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHR16mCL, X86_INS_SHR: shr{w}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SHR16mi, X86_INS_SHR: shr{w}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHR16r1, X86_INS_SHR: shr{w}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHR16rCL, X86_INS_SHR: shr{w}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SHR16ri, X86_INS_SHR: shr{w}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHR32m1, X86_INS_SHR: shr{l}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHR32mCL, X86_INS_SHR: shr{l}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SHR32mi, X86_INS_SHR: shr{l}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHR32r1, X86_INS_SHR: shr{l}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHR32rCL, X86_INS_SHR: shr{l}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SHR32ri, X86_INS_SHR: shr{l}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHR64m1, X86_INS_SHR: shr{q}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHR64mCL, X86_INS_SHR: shr{q}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SHR64mi, X86_INS_SHR: shr{q}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHR64r1, X86_INS_SHR: shr{q}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHR64rCL, X86_INS_SHR: shr{q}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SHR64ri, X86_INS_SHR: shr{q}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHR8m1, X86_INS_SHR: shr{b}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHR8mCL, X86_INS_SHR: shr{b}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SHR8mi, X86_INS_SHR: shr{b}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHR8r1, X86_INS_SHR: shr{b}	$dst, 1 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHR8rCL, X86_INS_SHR: shr{b}	$dst, cl */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SHR8ri, X86_INS_SHR: shr{b}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SHRD16mrCL, X86_INS_SHRD: shrd{w}	$dst, $src2, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SHRD16mri8, X86_INS_SHRD: shrd{w}	$dst, $src2, $src3 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_SHRD16rrCL, X86_INS_SHRD: shrd{w}	$dst, $src2, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SHRD16rri8, X86_INS_SHRD: shrd{w}	$dst, $src2, $src3 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_SHRD32mrCL, X86_INS_SHRD: shrd{l}	$dst, $src2, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SHRD32mri8, X86_INS_SHRD: shrd{l}	$dst, $src2, $src3 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_SHRD32rrCL, X86_INS_SHRD: shrd{l}	$dst, $src2, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SHRD32rri8, X86_INS_SHRD: shrd{l}	$dst, $src2, $src3 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_SHRD64mrCL, X86_INS_SHRD: shrd{q}	$dst, $src2, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SHRD64mri8, X86_INS_SHRD: shrd{q}	$dst, $src2, $src3 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_SHRD64rrCL, X86_INS_SHRD: shrd{q}	$dst, $src2, cl */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SHRD64rri8, X86_INS_SHRD: shrd{q}	$dst, $src2, $src3 */
	X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_SHRX32rm, X86_INS_SHRX: shrx{l}	$dst, $src1, $src2 */
	0,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SHRX32rr, X86_INS_SHRX: shrx{l}	$dst, $src1, $src2 */
	0,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SHRX64rm, X86_INS_SHRX: shrx{q}	$dst, $src1, $src2 */
	0,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SHRX64rr, X86_INS_SHRX: shrx{q}	$dst, $src1, $src2 */
	0,
	{ CS_AC_WRITE, CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_SIDT16m, X86_INS_SIDT: sidt{w}	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SIDT32m, X86_INS_SIDT: sidt{l}	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SIDT64m, X86_INS_SIDT: sidt{q}	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SKINIT, X86_INS_SKINIT: skinit	eax */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_SLDT16m, X86_INS_SLDT: sldt{w}	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SLDT16r, X86_INS_SLDT: sldt{w}	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SLDT32r, X86_INS_SLDT: sldt{l}	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SLDT64m, X86_INS_SLDT: sldt{q}	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SLDT64r, X86_INS_SLDT: sldt{q}	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SMSW16m, X86_INS_SMSW: smsw{w}	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SMSW16r, X86_INS_SMSW: smsw{w}	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SMSW32r, X86_INS_SMSW: smsw{l}	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SMSW64r, X86_INS_SMSW: smsw{q}	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_STAC, X86_INS_STAC: stac */
	0,
	{ 0 }
},
{	/* X86_STC, X86_INS_STC: stc */
	X86_EFLAGS_SET_CF,
	{ 0 }
},
{	/* X86_STD, X86_INS_STD: std */
	X86_EFLAGS_SET_DF,
	{ 0 }
},
{	/* X86_STGI, X86_INS_STGI: stgi */
	0,
	{ 0 }
},
{	/* X86_STI, X86_INS_STI: sti */
	X86_EFLAGS_SET_IF,
	{ 0 }
},
{	/* X86_STOSB, X86_INS_STOSB: stosb	$dst, al */
	X86_EFLAGS_TEST_DF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_STOSL, X86_INS_STOSD: stos{l|d}	{%eax, $dst|$dst, eax} */
	X86_EFLAGS_TEST_DF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_STOSQ, X86_INS_STOSQ: stosq	$dst, rax */
	X86_EFLAGS_TEST_DF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_STOSW, X86_INS_STOSW: stosw	$dst, ax */
	X86_EFLAGS_TEST_DF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_STR16r, X86_INS_STR: str{w}	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_STR32r, X86_INS_STR: str{l}	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_STR64r, X86_INS_STR: str{q}	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_STRm, X86_INS_STR: str{w}	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_SUB16i16, X86_INS_SUB: sub{w}	ax, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SUB16mi, X86_INS_SUB: sub{w}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SUB16mi8, X86_INS_SUB: sub{w}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SUB16mr, X86_INS_SUB: sub{w}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SUB16ri, X86_INS_SUB: sub{w}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SUB16ri8, X86_INS_SUB: sub{w}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SUB16rm, X86_INS_SUB: sub{w}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SUB16rr, X86_INS_SUB: sub{w}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SUB16rr_REV, X86_INS_SUB: sub{w}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SUB32i32, X86_INS_SUB: sub{l}	eax, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SUB32mi, X86_INS_SUB: sub{l}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SUB32mi8, X86_INS_SUB: sub{l}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SUB32mr, X86_INS_SUB: sub{l}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SUB32ri, X86_INS_SUB: sub{l}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SUB32ri8, X86_INS_SUB: sub{l}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SUB32rm, X86_INS_SUB: sub{l}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SUB32rr, X86_INS_SUB: sub{l}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SUB32rr_REV, X86_INS_SUB: sub{l}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SUB64i32, X86_INS_SUB: sub{q}	rax, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SUB64mi32, X86_INS_SUB: sub{q}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SUB64mi8, X86_INS_SUB: sub{q}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SUB64mr, X86_INS_SUB: sub{q}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SUB64ri32, X86_INS_SUB: sub{q}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SUB64ri8, X86_INS_SUB: sub{q}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SUB64rm, X86_INS_SUB: sub{q}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SUB64rr, X86_INS_SUB: sub{q}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SUB64rr_REV, X86_INS_SUB: sub{q}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SUB8i8, X86_INS_SUB: sub{b}	al, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SUB8mi, X86_INS_SUB: sub{b}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SUB8mi8, X86_INS_SUB: sub{b}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SUB8mr, X86_INS_SUB: sub{b}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SUB8ri, X86_INS_SUB: sub{b}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SUB8ri8, X86_INS_SUB: sub{b}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_SUB8rm, X86_INS_SUB: sub{b}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SUB8rr, X86_INS_SUB: sub{b}	$src1, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SUB8rr_REV, X86_INS_SUB: sub{b}	$dst, $src2 */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_SWAPGS, X86_INS_SWAPGS: swapgs */
	0,
	{ 0 }
},
{	/* X86_SYSCALL, X86_INS_SYSCALL: syscall */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_TF | X86_EFLAGS_MODIFY_IF | X86_EFLAGS_MODIFY_DF | X86_EFLAGS_MODIFY_NT | X86_EFLAGS_MODIFY_RF,
	{ 0 }
},
{	/* X86_SYSENTER, X86_INS_SYSENTER: sysenter */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_TF | X86_EFLAGS_MODIFY_IF | X86_EFLAGS_MODIFY_DF | X86_EFLAGS_MODIFY_NT | X86_EFLAGS_MODIFY_RF,
	{ 0 }
},
{	/* X86_SYSEXIT, X86_INS_SYSEXIT: sysexit{l} */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_TF | X86_EFLAGS_MODIFY_IF | X86_EFLAGS_MODIFY_DF | X86_EFLAGS_MODIFY_NT | X86_EFLAGS_MODIFY_RF,
	{ 0 }
},
{	/* X86_SYSEXIT64, X86_INS_SYSEXIT: sysexit{q} */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_TF | X86_EFLAGS_MODIFY_IF | X86_EFLAGS_MODIFY_DF | X86_EFLAGS_MODIFY_NT | X86_EFLAGS_MODIFY_RF,
	{ 0 }
},
{	/* X86_SYSRET, X86_INS_SYSRET: sysret{l} */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_TF | X86_EFLAGS_MODIFY_IF | X86_EFLAGS_MODIFY_DF | X86_EFLAGS_MODIFY_NT | X86_EFLAGS_MODIFY_RF,
	{ 0 }
},
{	/* X86_SYSRET64, X86_INS_SYSRET: sysret{q} */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_MODIFY_TF | X86_EFLAGS_MODIFY_IF | X86_EFLAGS_MODIFY_DF | X86_EFLAGS_MODIFY_NT | X86_EFLAGS_MODIFY_RF,
	{ 0 }
},
{	/* X86_T1MSKC32rm, X86_INS_T1MSKC: t1mskc	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_T1MSKC32rr, X86_INS_T1MSKC: t1mskc	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_T1MSKC64rm, X86_INS_T1MSKC: t1mskc	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_T1MSKC64rr, X86_INS_T1MSKC: t1mskc	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_TEST16i16, X86_INS_TEST: test{w}	ax, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_TEST16mi, X86_INS_TEST: test{w}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_TEST16mi_alt, X86_INS_TEST: test{w}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_TEST16ri, X86_INS_TEST: test{w}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_TEST16ri_alt, X86_INS_TEST: test{w}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_TEST16rm, X86_INS_TEST: test{w}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_TEST16rr, X86_INS_TEST: test{w}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_TEST32i32, X86_INS_TEST: test{l}	eax, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_TEST32mi, X86_INS_TEST: test{l}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_TEST32mi_alt, X86_INS_TEST: test{l}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_TEST32ri, X86_INS_TEST: test{l}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_TEST32ri_alt, X86_INS_TEST: test{l}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_TEST32rm, X86_INS_TEST: test{l}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_TEST32rr, X86_INS_TEST: test{l}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_TEST64i32, X86_INS_TEST: test{q}	rax, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_TEST64mi32, X86_INS_TEST: test{q}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_TEST64mi32_alt, X86_INS_TEST: test{q}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_TEST64ri32, X86_INS_TEST: test{q}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_TEST64ri32_alt, X86_INS_TEST: test{q}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_TEST64rm, X86_INS_TEST: test{q}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_TEST64rr, X86_INS_TEST: test{q}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_TEST8i8, X86_INS_TEST: test{b}	al, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_TEST8mi, X86_INS_TEST: test{b}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_TEST8mi_alt, X86_INS_TEST: test{b}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_TEST8ri, X86_INS_TEST: test{b}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_TEST8ri_alt, X86_INS_TEST: test{b}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_IGNORE, 0 }
},
{	/* X86_TEST8rm, X86_INS_TEST: test{b}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_TEST8rr, X86_INS_TEST: test{b}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ, CS_AC_READ, 0 }
},
{	/* X86_TRAP, X86_INS_UD2: ud2 */
	0,
	{ 0 }
},
{	/* X86_TZCNT16rm, X86_INS_TZCNT: tzcnt{w}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_AF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_TZCNT16rr, X86_INS_TZCNT: tzcnt{w}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_AF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_TZCNT32rm, X86_INS_TZCNT: tzcnt{l}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_AF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_TZCNT32rr, X86_INS_TZCNT: tzcnt{l}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_AF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_TZCNT64rm, X86_INS_TZCNT: tzcnt{q}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_AF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_TZCNT64rr, X86_INS_TZCNT: tzcnt{q}	$dst, $src */
	X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_CF | X86_EFLAGS_UNDEFINED_OF | X86_EFLAGS_UNDEFINED_SF | X86_EFLAGS_UNDEFINED_PF | X86_EFLAGS_UNDEFINED_AF,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_TZMSK32rm, X86_INS_TZMSK: tzmsk	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_TZMSK32rr, X86_INS_TZMSK: tzmsk	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_TZMSK64rm, X86_INS_TZMSK: tzmsk	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_TZMSK64rr, X86_INS_TZMSK: tzmsk	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_UD2B, X86_INS_UD2B: ud2b */
	0,
	{ 0 }
},
{	/* X86_VERRm, X86_INS_VERR: verr	$seg */
	X86_EFLAGS_MODIFY_ZF,
	{ CS_AC_READ, 0 }
},
{	/* X86_VERRr, X86_INS_VERR: verr	$seg */
	X86_EFLAGS_MODIFY_ZF,
	{ CS_AC_READ, 0 }
},
{	/* X86_VERWm, X86_INS_VERW: verw	$seg */
	X86_EFLAGS_MODIFY_ZF,
	{ CS_AC_READ, 0 }
},
{	/* X86_VERWr, X86_INS_VERW: verw	$seg */
	X86_EFLAGS_MODIFY_ZF,
	{ CS_AC_READ, 0 }
},
{	/* X86_VMCALL, X86_INS_VMCALL: vmcall */
	0,
	{ 0 }
},
{	/* X86_VMCLEARm, X86_INS_VMCLEAR: vmclear	$vmcs */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_VMFUNC, X86_INS_VMFUNC: vmfunc */
	0,
	{ 0 }
},
{	/* X86_VMLAUNCH, X86_INS_VMLAUNCH: vmlaunch */
	0,
	{ 0 }
},
{	/* X86_VMLOAD32, X86_INS_VMLOAD: vmload	eax */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_VMLOAD64, X86_INS_VMLOAD: vmload	rax */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_VMMCALL, X86_INS_VMMCALL: vmmcall */
	0,
	{ 0 }
},
{	/* X86_VMPTRLDm, X86_INS_VMPTRLD: vmptrld	$vmcs */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_VMPTRSTm, X86_INS_VMPTRST: vmptrst	$vmcs */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_VMREAD32rm, X86_INS_VMREAD: vmread{l}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_VMREAD32rr, X86_INS_VMREAD: vmread{l}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_VMREAD64rm, X86_INS_VMREAD: vmread{q}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_VMREAD64rr, X86_INS_VMREAD: vmread{q}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_VMRESUME, X86_INS_VMRESUME: vmresume */
	0,
	{ 0 }
},
{	/* X86_VMRUN32, X86_INS_VMRUN: vmrun	eax */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_VMRUN64, X86_INS_VMRUN: vmrun	rax */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_VMSAVE32, X86_INS_VMSAVE: vmsave	eax */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_VMSAVE64, X86_INS_VMSAVE: vmsave	rax */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_VMWRITE32rm, X86_INS_VMWRITE: vmwrite{l}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_VMWRITE32rr, X86_INS_VMWRITE: vmwrite{l}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_VMWRITE64rm, X86_INS_VMWRITE: vmwrite{q}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_VMWRITE64rr, X86_INS_VMWRITE: vmwrite{q}	$dst, $src */
	0,
	{ CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_VMXOFF, X86_INS_VMXOFF: vmxoff */
	0,
	{ 0 }
},
{	/* X86_VMXON, X86_INS_VMXON: vmxon	$vmxon */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_WBINVD, X86_INS_WBINVD: wbinvd */
	0,
	{ 0 }
},
{	/* X86_WRFSBASE, X86_INS_WRFSBASE: wrfsbase{l}	$src */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_WRFSBASE64, X86_INS_WRFSBASE: wrfsbase{q}	$src */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_WRGSBASE, X86_INS_WRGSBASE: wrgsbase{l}	$src */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_WRGSBASE64, X86_INS_WRGSBASE: wrgsbase{q}	$src */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_WRMSR, X86_INS_WRMSR: wrmsr */
	0,
	{ 0 }
},
{	/* X86_XADD16rm, X86_INS_XADD: xadd{w}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_XADD16rr, X86_INS_XADD: xadd{w}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_XADD32rm, X86_INS_XADD: xadd{l}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_XADD32rr, X86_INS_XADD: xadd{l}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_XADD64rm, X86_INS_XADD: xadd{q}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_XADD64rr, X86_INS_XADD: xadd{q}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_XADD8rm, X86_INS_XADD: xadd{b}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_XADD8rr, X86_INS_XADD: xadd{b}	$dst, $src */
	X86_EFLAGS_MODIFY_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_MODIFY_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_MODIFY_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_XCHG16ar, X86_INS_XCHG: xchg{w}	ax, $src */
	0,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_XCHG16rm, X86_INS_XCHG: xchg{w}	$ptr, $val */
	0,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_XCHG16rr, X86_INS_XCHG: xchg{w}	$src, $val */
	0,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_XCHG32ar, X86_INS_XCHG: xchg{l}	eax, $src */
	0,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_XCHG32ar64, X86_INS_XCHG: xchg{l}	eax, $src */
	0,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_XCHG32rm, X86_INS_XCHG: xchg{l}	$ptr, $val */
	0,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_XCHG32rr, X86_INS_XCHG: xchg{l}	$src, $val */
	0,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_XCHG64ar, X86_INS_XCHG: xchg{q}	rax, $src */
	0,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_XCHG64rm, X86_INS_XCHG: xchg{q}	$ptr, $val */
	0,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_XCHG64rr, X86_INS_XCHG: xchg{q}	$src, $val */
	0,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_XCHG8rm, X86_INS_XCHG: xchg{b}	$ptr, $val */
	0,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_XCHG8rr, X86_INS_XCHG: xchg{b}	$src, $val */
	0,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ | CS_AC_WRITE, 0 }
},
{	/* X86_XCRYPTCBC, X86_INS_XCRYPTCBC: xcryptcbc */
	0,
	{ 0 }
},
{	/* X86_XCRYPTCFB, X86_INS_XCRYPTCFB: xcryptcfb */
	0,
	{ 0 }
},
{	/* X86_XCRYPTCTR, X86_INS_XCRYPTCTR: xcryptctr */
	0,
	{ 0 }
},
{	/* X86_XCRYPTECB, X86_INS_XCRYPTECB: xcryptecb */
	0,
	{ 0 }
},
{	/* X86_XCRYPTOFB, X86_INS_XCRYPTOFB: xcryptofb */
	0,
	{ 0 }
},
{	/* X86_XGETBV, X86_INS_XGETBV: xgetbv */
	0,
	{ 0 }
},
{	/* X86_XLAT, X86_INS_XLATB: xlatb */
	0,
	{ 0 }
},
{	/* X86_XOR16i16, X86_INS_XOR: xor{w}	ax, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_XOR16mi, X86_INS_XOR: xor{w}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_XOR16mi8, X86_INS_XOR: xor{w}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_XOR16mr, X86_INS_XOR: xor{w}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_XOR16ri, X86_INS_XOR: xor{w}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_XOR16ri8, X86_INS_XOR: xor{w}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_XOR16rm, X86_INS_XOR: xor{w}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_XOR16rr, X86_INS_XOR: xor{w}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_XOR16rr_REV, X86_INS_XOR: xor{w}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_XOR32i32, X86_INS_XOR: xor{l}	eax, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_XOR32mi, X86_INS_XOR: xor{l}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_XOR32mi8, X86_INS_XOR: xor{l}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_XOR32mr, X86_INS_XOR: xor{l}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_XOR32ri, X86_INS_XOR: xor{l}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_XOR32ri8, X86_INS_XOR: xor{l}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_XOR32rm, X86_INS_XOR: xor{l}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_XOR32rr, X86_INS_XOR: xor{l}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_XOR32rr_REV, X86_INS_XOR: xor{l}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_XOR64i32, X86_INS_XOR: xor{q}	rax, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_XOR64mi32, X86_INS_XOR: xor{q}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_XOR64mi8, X86_INS_XOR: xor{q}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_XOR64mr, X86_INS_XOR: xor{q}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_XOR64ri32, X86_INS_XOR: xor{q}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_XOR64ri8, X86_INS_XOR: xor{q}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_XOR64rm, X86_INS_XOR: xor{q}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_XOR64rr, X86_INS_XOR: xor{q}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_XOR64rr_REV, X86_INS_XOR: xor{q}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_XOR8i8, X86_INS_XOR: xor{b}	al, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_XOR8mi, X86_INS_XOR: xor{b}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_XOR8mi8, X86_INS_XOR: xor{b}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_XOR8mr, X86_INS_XOR: xor{b}	$dst, $src */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_XOR8ri, X86_INS_XOR: xor{b}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_XOR8ri8, X86_INS_XOR: xor{b}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_IGNORE, 0 }
},
{	/* X86_XOR8rm, X86_INS_XOR: xor{b}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_XOR8rr, X86_INS_XOR: xor{b}	$src1, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_XOR8rr_REV, X86_INS_XOR: xor{b}	$dst, $src2 */
	X86_EFLAGS_RESET_OF | X86_EFLAGS_MODIFY_SF | X86_EFLAGS_MODIFY_ZF | X86_EFLAGS_UNDEFINED_AF | X86_EFLAGS_MODIFY_PF | X86_EFLAGS_RESET_CF,
	{ CS_AC_READ | CS_AC_WRITE, CS_AC_READ, 0 }
},
{	/* X86_XRSTOR, X86_INS_XRSTOR: xrstor	$dst */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_XRSTOR64, X86_INS_XRSTOR64: xrstor64	$dst */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_XRSTORS, X86_INS_XRSTORS: xrstors	$dst */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_XRSTORS64, X86_INS_XRSTORS64: xrstors64	$dst */
	0,
	{ CS_AC_READ, 0 }
},
{	/* X86_XSAVE, X86_INS_XSAVE: xsave	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_XSAVE64, X86_INS_XSAVE64: xsave64	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_XSAVEC, X86_INS_XSAVEC: xsavec	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_XSAVEC64, X86_INS_XSAVEC64: xsavec64	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_XSAVEOPT, X86_INS_XSAVEOPT: xsaveopt	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_XSAVEOPT64, X86_INS_XSAVEOPT64: xsaveopt64	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_XSAVES, X86_INS_XSAVES: xsaves	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_XSAVES64, X86_INS_XSAVES64: xsaves64	$dst */
	0,
	{ CS_AC_WRITE, 0 }
},
{	/* X86_XSETBV, X86_INS_XSETBV: xsetbv */
	0,
	{ 0 }
},
{	/* X86_XSHA1, X86_INS_XSHA1: xsha1 */
	0,
	{ 0 }
},
{	/* X86_XSHA256, X86_INS_XSHA256: xsha256 */
	0,
	{ 0 }
},
{	/* X86_XSTORE, X86_INS_XSTORE: xstore */
	0,
	{ 0 }
},
