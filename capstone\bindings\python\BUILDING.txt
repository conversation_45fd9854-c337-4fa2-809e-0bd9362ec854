0. This documentation explains how to install the Python bindings for Capstone
   from source. If you want to install it from a PyPi package (recommended if
   you are on Windows), see README.txt.

1. To install Capstone and the Python bindings on *nix, run the command below:

		$ sudo make install

   To install Capstone for Python 3, run the command below:
   (Note: this requires python3 installed in your machine)

		$ sudo make install3

   To control the install destination, set the DESTDIR environment variable.

2. For better Python performance, install cython-based binding with:

		$ sudo make install_cython

	Note that this requires Cython installed first. To install Cython, see
	below.
	
3. To install Cython, you have to ensure that the header files
   and the static library for Python are installed beforehand.

	E.g. on Ubuntu, do:

		$ sudo apt-get install python-dev

	Depending on if you already have pip or easy_install installed, install
	Cython with either:

		$ sudo pip install cython
	or:
		$ sudo easy_install cython

	NOTE: Depending on your distribution you might also be able to
	      install the required Cython version using your repository.

	E.g. on Ubuntu, do:
	
		$ sudo apt-get install cython

	However, our cython-based binding requires Cython version 0.19 or newer,
	but sometimes distributions only provide older version. Make sure to
	verify the current installed version before going into section 2 above.
	
	E.g, on Ubuntu, you can verify the current Cython version with:

		$ apt-cache policy cython

	Which should at least print version 0.19

4. This directory contains some test code to show how to use the Capstone API.

- test_basic.py
  This code shows the most simple form of API where we only want to get basic
  information out of disassembled instruction, such as address, mnemonic and
  operand string.

- test_lite.py
  Similarly to test_basic.py, but this code shows how to use disasm_lite(), a lighter
  method to disassemble binary. Unlike disasm() API (used by test_basic.py), which returns
  CsInsn objects, this API just returns tuples of (address, size, mnemonic, op_str).

  The main reason for using this API is better performance: disasm_lite() is at least
  20% faster than disasm(). Memory usage is also less. So if you just need basic
  information out of disassembler, use disasm_lite() instead of disasm().

- test_detail.py:
  This code shows how to access to architecture-neutral information in disassembled
  instructions, such as implicit registers read/written, or groups of instructions
  that this instruction belong to.

- test_<arch>.py
  These code show how to access architecture-specific information for each
  architecture.
