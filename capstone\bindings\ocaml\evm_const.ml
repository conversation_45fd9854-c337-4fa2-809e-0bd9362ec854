(* For Capstone Engine. AUTO-GENERATED FILE, DO NOT EDIT [evm_const.ml] *)

(* EVM instruction *)

let _EVM_INS_STOP = 0;;
let _EVM_INS_ADD = 1;;
let _EVM_INS_MUL = 2;;
let _EVM_INS_SUB = 3;;
let _EVM_INS_DIV = 4;;
let _EVM_INS_SDIV = 5;;
let _EVM_INS_MOD = 6;;
let _EVM_INS_SMOD = 7;;
let _EVM_INS_ADDMOD = 8;;
let _EVM_INS_MULMOD = 9;;
let _EVM_INS_EXP = 10;;
let _EVM_INS_SIGNEXTEND = 11;;
let _EVM_INS_LT = 16;;
let _EVM_INS_GT = 17;;
let _EVM_INS_SLT = 18;;
let _EVM_INS_SGT = 19;;
let _EVM_INS_EQ = 20;;
let _EVM_INS_ISZERO = 21;;
let _EVM_INS_AND = 22;;
let _EVM_INS_OR = 23;;
let _EVM_INS_XOR = 24;;
let _EVM_INS_NOT = 25;;
let _EVM_INS_BYTE = 26;;
let _EVM_INS_SHA3 = 32;;
let _EVM_INS_ADDRESS = 48;;
let _EVM_INS_BALANCE = 49;;
let _EVM_INS_ORIGIN = 50;;
let _EVM_INS_CALLER = 51;;
let _EVM_INS_CALLVALUE = 52;;
let _EVM_INS_CALLDATALOAD = 53;;
let _EVM_INS_CALLDATASIZE = 54;;
let _EVM_INS_CALLDATACOPY = 55;;
let _EVM_INS_CODESIZE = 56;;
let _EVM_INS_CODECOPY = 57;;
let _EVM_INS_GASPRICE = 58;;
let _EVM_INS_EXTCODESIZE = 59;;
let _EVM_INS_EXTCODECOPY = 60;;
let _EVM_INS_RETURNDATASIZE = 61;;
let _EVM_INS_RETURNDATACOPY = 62;;
let _EVM_INS_BLOCKHASH = 64;;
let _EVM_INS_COINBASE = 65;;
let _EVM_INS_TIMESTAMP = 66;;
let _EVM_INS_NUMBER = 67;;
let _EVM_INS_DIFFICULTY = 68;;
let _EVM_INS_GASLIMIT = 69;;
let _EVM_INS_POP = 80;;
let _EVM_INS_MLOAD = 81;;
let _EVM_INS_MSTORE = 82;;
let _EVM_INS_MSTORE8 = 83;;
let _EVM_INS_SLOAD = 84;;
let _EVM_INS_SSTORE = 85;;
let _EVM_INS_JUMP = 86;;
let _EVM_INS_JUMPI = 87;;
let _EVM_INS_PC = 88;;
let _EVM_INS_MSIZE = 89;;
let _EVM_INS_GAS = 90;;
let _EVM_INS_JUMPDEST = 91;;
let _EVM_INS_PUSH1 = 96;;
let _EVM_INS_PUSH2 = 97;;
let _EVM_INS_PUSH3 = 98;;
let _EVM_INS_PUSH4 = 99;;
let _EVM_INS_PUSH5 = 100;;
let _EVM_INS_PUSH6 = 101;;
let _EVM_INS_PUSH7 = 102;;
let _EVM_INS_PUSH8 = 103;;
let _EVM_INS_PUSH9 = 104;;
let _EVM_INS_PUSH10 = 105;;
let _EVM_INS_PUSH11 = 106;;
let _EVM_INS_PUSH12 = 107;;
let _EVM_INS_PUSH13 = 108;;
let _EVM_INS_PUSH14 = 109;;
let _EVM_INS_PUSH15 = 110;;
let _EVM_INS_PUSH16 = 111;;
let _EVM_INS_PUSH17 = 112;;
let _EVM_INS_PUSH18 = 113;;
let _EVM_INS_PUSH19 = 114;;
let _EVM_INS_PUSH20 = 115;;
let _EVM_INS_PUSH21 = 116;;
let _EVM_INS_PUSH22 = 117;;
let _EVM_INS_PUSH23 = 118;;
let _EVM_INS_PUSH24 = 119;;
let _EVM_INS_PUSH25 = 120;;
let _EVM_INS_PUSH26 = 121;;
let _EVM_INS_PUSH27 = 122;;
let _EVM_INS_PUSH28 = 123;;
let _EVM_INS_PUSH29 = 124;;
let _EVM_INS_PUSH30 = 125;;
let _EVM_INS_PUSH31 = 126;;
let _EVM_INS_PUSH32 = 127;;
let _EVM_INS_DUP1 = 128;;
let _EVM_INS_DUP2 = 129;;
let _EVM_INS_DUP3 = 130;;
let _EVM_INS_DUP4 = 131;;
let _EVM_INS_DUP5 = 132;;
let _EVM_INS_DUP6 = 133;;
let _EVM_INS_DUP7 = 134;;
let _EVM_INS_DUP8 = 135;;
let _EVM_INS_DUP9 = 136;;
let _EVM_INS_DUP10 = 137;;
let _EVM_INS_DUP11 = 138;;
let _EVM_INS_DUP12 = 139;;
let _EVM_INS_DUP13 = 140;;
let _EVM_INS_DUP14 = 141;;
let _EVM_INS_DUP15 = 142;;
let _EVM_INS_DUP16 = 143;;
let _EVM_INS_SWAP1 = 144;;
let _EVM_INS_SWAP2 = 145;;
let _EVM_INS_SWAP3 = 146;;
let _EVM_INS_SWAP4 = 147;;
let _EVM_INS_SWAP5 = 148;;
let _EVM_INS_SWAP6 = 149;;
let _EVM_INS_SWAP7 = 150;;
let _EVM_INS_SWAP8 = 151;;
let _EVM_INS_SWAP9 = 152;;
let _EVM_INS_SWAP10 = 153;;
let _EVM_INS_SWAP11 = 154;;
let _EVM_INS_SWAP12 = 155;;
let _EVM_INS_SWAP13 = 156;;
let _EVM_INS_SWAP14 = 157;;
let _EVM_INS_SWAP15 = 158;;
let _EVM_INS_SWAP16 = 159;;
let _EVM_INS_LOG0 = 160;;
let _EVM_INS_LOG1 = 161;;
let _EVM_INS_LOG2 = 162;;
let _EVM_INS_LOG3 = 163;;
let _EVM_INS_LOG4 = 164;;
let _EVM_INS_CREATE = 240;;
let _EVM_INS_CALL = 241;;
let _EVM_INS_CALLCODE = 242;;
let _EVM_INS_RETURN = 243;;
let _EVM_INS_DELEGATECALL = 244;;
let _EVM_INS_CALLBLACKBOX = 245;;
let _EVM_INS_STATICCALL = 250;;
let _EVM_INS_REVERT = 253;;
let _EVM_INS_SUICIDE = 255;;
let _EVM_INS_INVALID = 512;;
let _EVM_INS_ENDING = 513;;

(* Group of EVM instructions *)

let _EVM_GRP_INVALID = 0;;
let _EVM_GRP_JUMP = 1;;
let _EVM_GRP_MATH = 8;;
let _EVM_GRP_STACK_WRITE = 9;;
let _EVM_GRP_STACK_READ = 10;;
let _EVM_GRP_MEM_WRITE = 11;;
let _EVM_GRP_MEM_READ = 12;;
let _EVM_GRP_STORE_WRITE = 13;;
let _EVM_GRP_STORE_READ = 14;;
let _EVM_GRP_HALT = 15;;
let _EVM_GRP_ENDING = 16;;
