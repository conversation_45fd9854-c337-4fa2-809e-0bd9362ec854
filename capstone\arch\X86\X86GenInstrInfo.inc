/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Target Instruction Enum Values                                              *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine, http://www.capstone-engine.org */
/* By <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2015 */


#ifdef GET_INSTRINFO_ENUM
#undef GET_INSTRINFO_ENUM

enum {
    X86_PHI	= 0,
    X86_INLINEASM	= 1,
    X86_CFI_INSTRUCTION	= 2,
    X86_EH_LABEL	= 3,
    X86_GC_LABEL	= 4,
    X86_KILL	= 5,
    X86_EXTRACT_SUBREG	= 6,
    X86_INSERT_SUBREG	= 7,
    X86_IMPLICIT_DEF	= 8,
    X86_SUBREG_TO_REG	= 9,
    X86_COPY_TO_REGCLASS	= 10,
    X86_DBG_VALUE	= 11,
    X86_REG_SEQUENCE	= 12,
    X86_COPY	= 13,
    X86_BUNDLE	= 14,
    X86_LIFETIME_START	= 15,
    X86_LIFETIME_END	= 16,
    X86_STACKMAP	= 17,
    X86_PATCHPOINT	= 18,
    X86_LOAD_STACK_GUARD	= 19,
    X86_STATEPOINT	= 20,
    X86_FRAME_ALLOC	= 21,
    X86_AAA	= 22,
    X86_AAD8i8	= 23,
    X86_AAM8i8	= 24,
    X86_AAS	= 25,
    X86_ABS_F	= 26,
    X86_ABS_Fp32	= 27,
    X86_ABS_Fp64	= 28,
    X86_ABS_Fp80	= 29,
    X86_ACQUIRE_MOV16rm	= 30,
    X86_ACQUIRE_MOV32rm	= 31,
    X86_ACQUIRE_MOV64rm	= 32,
    X86_ACQUIRE_MOV8rm	= 33,
    X86_ADC16i16	= 34,
    X86_ADC16mi	= 35,
    X86_ADC16mi8	= 36,
    X86_ADC16mr	= 37,
    X86_ADC16ri	= 38,
    X86_ADC16ri8	= 39,
    X86_ADC16rm	= 40,
    X86_ADC16rr	= 41,
    X86_ADC16rr_REV	= 42,
    X86_ADC32i32	= 43,
    X86_ADC32mi	= 44,
    X86_ADC32mi8	= 45,
    X86_ADC32mr	= 46,
    X86_ADC32ri	= 47,
    X86_ADC32ri8	= 48,
    X86_ADC32rm	= 49,
    X86_ADC32rr	= 50,
    X86_ADC32rr_REV	= 51,
    X86_ADC64i32	= 52,
    X86_ADC64mi32	= 53,
    X86_ADC64mi8	= 54,
    X86_ADC64mr	= 55,
    X86_ADC64ri32	= 56,
    X86_ADC64ri8	= 57,
    X86_ADC64rm	= 58,
    X86_ADC64rr	= 59,
    X86_ADC64rr_REV	= 60,
    X86_ADC8i8	= 61,
    X86_ADC8mi	= 62,
    X86_ADC8mi8	= 63,
    X86_ADC8mr	= 64,
    X86_ADC8ri	= 65,
    X86_ADC8ri8	= 66,
    X86_ADC8rm	= 67,
    X86_ADC8rr	= 68,
    X86_ADC8rr_REV	= 69,
    X86_ADCX32rm	= 70,
    X86_ADCX32rr	= 71,
    X86_ADCX64rm	= 72,
    X86_ADCX64rr	= 73,
    X86_ADD16i16	= 74,
    X86_ADD16mi	= 75,
    X86_ADD16mi8	= 76,
    X86_ADD16mr	= 77,
    X86_ADD16ri	= 78,
    X86_ADD16ri8	= 79,
    X86_ADD16ri8_DB	= 80,
    X86_ADD16ri_DB	= 81,
    X86_ADD16rm	= 82,
    X86_ADD16rr	= 83,
    X86_ADD16rr_DB	= 84,
    X86_ADD16rr_REV	= 85,
    X86_ADD32i32	= 86,
    X86_ADD32mi	= 87,
    X86_ADD32mi8	= 88,
    X86_ADD32mr	= 89,
    X86_ADD32ri	= 90,
    X86_ADD32ri8	= 91,
    X86_ADD32ri8_DB	= 92,
    X86_ADD32ri_DB	= 93,
    X86_ADD32rm	= 94,
    X86_ADD32rr	= 95,
    X86_ADD32rr_DB	= 96,
    X86_ADD32rr_REV	= 97,
    X86_ADD64i32	= 98,
    X86_ADD64mi32	= 99,
    X86_ADD64mi8	= 100,
    X86_ADD64mr	= 101,
    X86_ADD64ri32	= 102,
    X86_ADD64ri32_DB	= 103,
    X86_ADD64ri8	= 104,
    X86_ADD64ri8_DB	= 105,
    X86_ADD64rm	= 106,
    X86_ADD64rr	= 107,
    X86_ADD64rr_DB	= 108,
    X86_ADD64rr_REV	= 109,
    X86_ADD8i8	= 110,
    X86_ADD8mi	= 111,
    X86_ADD8mi8	= 112,
    X86_ADD8mr	= 113,
    X86_ADD8ri	= 114,
    X86_ADD8ri8	= 115,
    X86_ADD8rm	= 116,
    X86_ADD8rr	= 117,
    X86_ADD8rr_REV	= 118,
    X86_ADDPDrm	= 119,
    X86_ADDPDrr	= 120,
    X86_ADDPSrm	= 121,
    X86_ADDPSrr	= 122,
    X86_ADDSDrm	= 123,
    X86_ADDSDrm_Int	= 124,
    X86_ADDSDrr	= 125,
    X86_ADDSDrr_Int	= 126,
    X86_ADDSSrm	= 127,
    X86_ADDSSrm_Int	= 128,
    X86_ADDSSrr	= 129,
    X86_ADDSSrr_Int	= 130,
    X86_ADDSUBPDrm	= 131,
    X86_ADDSUBPDrr	= 132,
    X86_ADDSUBPSrm	= 133,
    X86_ADDSUBPSrr	= 134,
    X86_ADD_F32m	= 135,
    X86_ADD_F64m	= 136,
    X86_ADD_FI16m	= 137,
    X86_ADD_FI32m	= 138,
    X86_ADD_FPrST0	= 139,
    X86_ADD_FST0r	= 140,
    X86_ADD_Fp32	= 141,
    X86_ADD_Fp32m	= 142,
    X86_ADD_Fp64	= 143,
    X86_ADD_Fp64m	= 144,
    X86_ADD_Fp64m32	= 145,
    X86_ADD_Fp80	= 146,
    X86_ADD_Fp80m32	= 147,
    X86_ADD_Fp80m64	= 148,
    X86_ADD_FpI16m32	= 149,
    X86_ADD_FpI16m64	= 150,
    X86_ADD_FpI16m80	= 151,
    X86_ADD_FpI32m32	= 152,
    X86_ADD_FpI32m64	= 153,
    X86_ADD_FpI32m80	= 154,
    X86_ADD_FrST0	= 155,
    X86_ADJCALLSTACKDOWN32	= 156,
    X86_ADJCALLSTACKDOWN64	= 157,
    X86_ADJCALLSTACKUP32	= 158,
    X86_ADJCALLSTACKUP64	= 159,
    X86_ADOX32rm	= 160,
    X86_ADOX32rr	= 161,
    X86_ADOX64rm	= 162,
    X86_ADOX64rr	= 163,
    X86_AESDECLASTrm	= 164,
    X86_AESDECLASTrr	= 165,
    X86_AESDECrm	= 166,
    X86_AESDECrr	= 167,
    X86_AESENCLASTrm	= 168,
    X86_AESENCLASTrr	= 169,
    X86_AESENCrm	= 170,
    X86_AESENCrr	= 171,
    X86_AESIMCrm	= 172,
    X86_AESIMCrr	= 173,
    X86_AESKEYGENASSIST128rm	= 174,
    X86_AESKEYGENASSIST128rr	= 175,
    X86_AND16i16	= 176,
    X86_AND16mi	= 177,
    X86_AND16mi8	= 178,
    X86_AND16mr	= 179,
    X86_AND16ri	= 180,
    X86_AND16ri8	= 181,
    X86_AND16rm	= 182,
    X86_AND16rr	= 183,
    X86_AND16rr_REV	= 184,
    X86_AND32i32	= 185,
    X86_AND32mi	= 186,
    X86_AND32mi8	= 187,
    X86_AND32mr	= 188,
    X86_AND32ri	= 189,
    X86_AND32ri8	= 190,
    X86_AND32rm	= 191,
    X86_AND32rr	= 192,
    X86_AND32rr_REV	= 193,
    X86_AND64i32	= 194,
    X86_AND64mi32	= 195,
    X86_AND64mi8	= 196,
    X86_AND64mr	= 197,
    X86_AND64ri32	= 198,
    X86_AND64ri8	= 199,
    X86_AND64rm	= 200,
    X86_AND64rr	= 201,
    X86_AND64rr_REV	= 202,
    X86_AND8i8	= 203,
    X86_AND8mi	= 204,
    X86_AND8mi8	= 205,
    X86_AND8mr	= 206,
    X86_AND8ri	= 207,
    X86_AND8ri8	= 208,
    X86_AND8rm	= 209,
    X86_AND8rr	= 210,
    X86_AND8rr_REV	= 211,
    X86_ANDN32rm	= 212,
    X86_ANDN32rr	= 213,
    X86_ANDN64rm	= 214,
    X86_ANDN64rr	= 215,
    X86_ANDNPDrm	= 216,
    X86_ANDNPDrr	= 217,
    X86_ANDNPSrm	= 218,
    X86_ANDNPSrr	= 219,
    X86_ANDPDrm	= 220,
    X86_ANDPDrr	= 221,
    X86_ANDPSrm	= 222,
    X86_ANDPSrr	= 223,
    X86_ARPL16mr	= 224,
    X86_ARPL16rr	= 225,
    X86_AVX2_SETALLONES	= 226,
    X86_AVX512_512_SET0	= 227,
    X86_AVX_SET0	= 228,
    X86_BEXTR32rm	= 229,
    X86_BEXTR32rr	= 230,
    X86_BEXTR64rm	= 231,
    X86_BEXTR64rr	= 232,
    X86_BEXTRI32mi	= 233,
    X86_BEXTRI32ri	= 234,
    X86_BEXTRI64mi	= 235,
    X86_BEXTRI64ri	= 236,
    X86_BLCFILL32rm	= 237,
    X86_BLCFILL32rr	= 238,
    X86_BLCFILL64rm	= 239,
    X86_BLCFILL64rr	= 240,
    X86_BLCI32rm	= 241,
    X86_BLCI32rr	= 242,
    X86_BLCI64rm	= 243,
    X86_BLCI64rr	= 244,
    X86_BLCIC32rm	= 245,
    X86_BLCIC32rr	= 246,
    X86_BLCIC64rm	= 247,
    X86_BLCIC64rr	= 248,
    X86_BLCMSK32rm	= 249,
    X86_BLCMSK32rr	= 250,
    X86_BLCMSK64rm	= 251,
    X86_BLCMSK64rr	= 252,
    X86_BLCS32rm	= 253,
    X86_BLCS32rr	= 254,
    X86_BLCS64rm	= 255,
    X86_BLCS64rr	= 256,
    X86_BLENDPDrmi	= 257,
    X86_BLENDPDrri	= 258,
    X86_BLENDPSrmi	= 259,
    X86_BLENDPSrri	= 260,
    X86_BLENDVPDrm0	= 261,
    X86_BLENDVPDrr0	= 262,
    X86_BLENDVPSrm0	= 263,
    X86_BLENDVPSrr0	= 264,
    X86_BLSFILL32rm	= 265,
    X86_BLSFILL32rr	= 266,
    X86_BLSFILL64rm	= 267,
    X86_BLSFILL64rr	= 268,
    X86_BLSI32rm	= 269,
    X86_BLSI32rr	= 270,
    X86_BLSI64rm	= 271,
    X86_BLSI64rr	= 272,
    X86_BLSIC32rm	= 273,
    X86_BLSIC32rr	= 274,
    X86_BLSIC64rm	= 275,
    X86_BLSIC64rr	= 276,
    X86_BLSMSK32rm	= 277,
    X86_BLSMSK32rr	= 278,
    X86_BLSMSK64rm	= 279,
    X86_BLSMSK64rr	= 280,
    X86_BLSR32rm	= 281,
    X86_BLSR32rr	= 282,
    X86_BLSR64rm	= 283,
    X86_BLSR64rr	= 284,
    X86_BOUNDS16rm	= 285,
    X86_BOUNDS32rm	= 286,
    X86_BSF16rm	= 287,
    X86_BSF16rr	= 288,
    X86_BSF32rm	= 289,
    X86_BSF32rr	= 290,
    X86_BSF64rm	= 291,
    X86_BSF64rr	= 292,
    X86_BSR16rm	= 293,
    X86_BSR16rr	= 294,
    X86_BSR32rm	= 295,
    X86_BSR32rr	= 296,
    X86_BSR64rm	= 297,
    X86_BSR64rr	= 298,
    X86_BSWAP32r	= 299,
    X86_BSWAP64r	= 300,
    X86_BT16mi8	= 301,
    X86_BT16mr	= 302,
    X86_BT16ri8	= 303,
    X86_BT16rr	= 304,
    X86_BT32mi8	= 305,
    X86_BT32mr	= 306,
    X86_BT32ri8	= 307,
    X86_BT32rr	= 308,
    X86_BT64mi8	= 309,
    X86_BT64mr	= 310,
    X86_BT64ri8	= 311,
    X86_BT64rr	= 312,
    X86_BTC16mi8	= 313,
    X86_BTC16mr	= 314,
    X86_BTC16ri8	= 315,
    X86_BTC16rr	= 316,
    X86_BTC32mi8	= 317,
    X86_BTC32mr	= 318,
    X86_BTC32ri8	= 319,
    X86_BTC32rr	= 320,
    X86_BTC64mi8	= 321,
    X86_BTC64mr	= 322,
    X86_BTC64ri8	= 323,
    X86_BTC64rr	= 324,
    X86_BTR16mi8	= 325,
    X86_BTR16mr	= 326,
    X86_BTR16ri8	= 327,
    X86_BTR16rr	= 328,
    X86_BTR32mi8	= 329,
    X86_BTR32mr	= 330,
    X86_BTR32ri8	= 331,
    X86_BTR32rr	= 332,
    X86_BTR64mi8	= 333,
    X86_BTR64mr	= 334,
    X86_BTR64ri8	= 335,
    X86_BTR64rr	= 336,
    X86_BTS16mi8	= 337,
    X86_BTS16mr	= 338,
    X86_BTS16ri8	= 339,
    X86_BTS16rr	= 340,
    X86_BTS32mi8	= 341,
    X86_BTS32mr	= 342,
    X86_BTS32ri8	= 343,
    X86_BTS32rr	= 344,
    X86_BTS64mi8	= 345,
    X86_BTS64mr	= 346,
    X86_BTS64ri8	= 347,
    X86_BTS64rr	= 348,
    X86_BZHI32rm	= 349,
    X86_BZHI32rr	= 350,
    X86_BZHI64rm	= 351,
    X86_BZHI64rr	= 352,
    X86_CALL16m	= 353,
    X86_CALL16r	= 354,
    X86_CALL32m	= 355,
    X86_CALL32r	= 356,
    X86_CALL64m	= 357,
    X86_CALL64pcrel32	= 358,
    X86_CALL64r	= 359,
    X86_CALLpcrel16	= 360,
    X86_CALLpcrel32	= 361,
    X86_CBW	= 362,
    X86_CDQ	= 363,
    X86_CDQE	= 364,
    X86_CHS_F	= 365,
    X86_CHS_Fp32	= 366,
    X86_CHS_Fp64	= 367,
    X86_CHS_Fp80	= 368,
    X86_CLAC	= 369,
    X86_CLC	= 370,
    X86_CLD	= 371,
    X86_CLFLUSH	= 372,
    X86_CLFLUSHOPT	= 373,
    X86_CLGI	= 374,
    X86_CLI	= 375,
    X86_CLTS	= 376,
    X86_CLWB	= 377,
    X86_CMC	= 378,
    X86_CMOVA16rm	= 379,
    X86_CMOVA16rr	= 380,
    X86_CMOVA32rm	= 381,
    X86_CMOVA32rr	= 382,
    X86_CMOVA64rm	= 383,
    X86_CMOVA64rr	= 384,
    X86_CMOVAE16rm	= 385,
    X86_CMOVAE16rr	= 386,
    X86_CMOVAE32rm	= 387,
    X86_CMOVAE32rr	= 388,
    X86_CMOVAE64rm	= 389,
    X86_CMOVAE64rr	= 390,
    X86_CMOVB16rm	= 391,
    X86_CMOVB16rr	= 392,
    X86_CMOVB32rm	= 393,
    X86_CMOVB32rr	= 394,
    X86_CMOVB64rm	= 395,
    X86_CMOVB64rr	= 396,
    X86_CMOVBE16rm	= 397,
    X86_CMOVBE16rr	= 398,
    X86_CMOVBE32rm	= 399,
    X86_CMOVBE32rr	= 400,
    X86_CMOVBE64rm	= 401,
    X86_CMOVBE64rr	= 402,
    X86_CMOVBE_F	= 403,
    X86_CMOVBE_Fp32	= 404,
    X86_CMOVBE_Fp64	= 405,
    X86_CMOVBE_Fp80	= 406,
    X86_CMOVB_F	= 407,
    X86_CMOVB_Fp32	= 408,
    X86_CMOVB_Fp64	= 409,
    X86_CMOVB_Fp80	= 410,
    X86_CMOVE16rm	= 411,
    X86_CMOVE16rr	= 412,
    X86_CMOVE32rm	= 413,
    X86_CMOVE32rr	= 414,
    X86_CMOVE64rm	= 415,
    X86_CMOVE64rr	= 416,
    X86_CMOVE_F	= 417,
    X86_CMOVE_Fp32	= 418,
    X86_CMOVE_Fp64	= 419,
    X86_CMOVE_Fp80	= 420,
    X86_CMOVG16rm	= 421,
    X86_CMOVG16rr	= 422,
    X86_CMOVG32rm	= 423,
    X86_CMOVG32rr	= 424,
    X86_CMOVG64rm	= 425,
    X86_CMOVG64rr	= 426,
    X86_CMOVGE16rm	= 427,
    X86_CMOVGE16rr	= 428,
    X86_CMOVGE32rm	= 429,
    X86_CMOVGE32rr	= 430,
    X86_CMOVGE64rm	= 431,
    X86_CMOVGE64rr	= 432,
    X86_CMOVL16rm	= 433,
    X86_CMOVL16rr	= 434,
    X86_CMOVL32rm	= 435,
    X86_CMOVL32rr	= 436,
    X86_CMOVL64rm	= 437,
    X86_CMOVL64rr	= 438,
    X86_CMOVLE16rm	= 439,
    X86_CMOVLE16rr	= 440,
    X86_CMOVLE32rm	= 441,
    X86_CMOVLE32rr	= 442,
    X86_CMOVLE64rm	= 443,
    X86_CMOVLE64rr	= 444,
    X86_CMOVNBE_F	= 445,
    X86_CMOVNBE_Fp32	= 446,
    X86_CMOVNBE_Fp64	= 447,
    X86_CMOVNBE_Fp80	= 448,
    X86_CMOVNB_F	= 449,
    X86_CMOVNB_Fp32	= 450,
    X86_CMOVNB_Fp64	= 451,
    X86_CMOVNB_Fp80	= 452,
    X86_CMOVNE16rm	= 453,
    X86_CMOVNE16rr	= 454,
    X86_CMOVNE32rm	= 455,
    X86_CMOVNE32rr	= 456,
    X86_CMOVNE64rm	= 457,
    X86_CMOVNE64rr	= 458,
    X86_CMOVNE_F	= 459,
    X86_CMOVNE_Fp32	= 460,
    X86_CMOVNE_Fp64	= 461,
    X86_CMOVNE_Fp80	= 462,
    X86_CMOVNO16rm	= 463,
    X86_CMOVNO16rr	= 464,
    X86_CMOVNO32rm	= 465,
    X86_CMOVNO32rr	= 466,
    X86_CMOVNO64rm	= 467,
    X86_CMOVNO64rr	= 468,
    X86_CMOVNP16rm	= 469,
    X86_CMOVNP16rr	= 470,
    X86_CMOVNP32rm	= 471,
    X86_CMOVNP32rr	= 472,
    X86_CMOVNP64rm	= 473,
    X86_CMOVNP64rr	= 474,
    X86_CMOVNP_F	= 475,
    X86_CMOVNP_Fp32	= 476,
    X86_CMOVNP_Fp64	= 477,
    X86_CMOVNP_Fp80	= 478,
    X86_CMOVNS16rm	= 479,
    X86_CMOVNS16rr	= 480,
    X86_CMOVNS32rm	= 481,
    X86_CMOVNS32rr	= 482,
    X86_CMOVNS64rm	= 483,
    X86_CMOVNS64rr	= 484,
    X86_CMOVO16rm	= 485,
    X86_CMOVO16rr	= 486,
    X86_CMOVO32rm	= 487,
    X86_CMOVO32rr	= 488,
    X86_CMOVO64rm	= 489,
    X86_CMOVO64rr	= 490,
    X86_CMOVP16rm	= 491,
    X86_CMOVP16rr	= 492,
    X86_CMOVP32rm	= 493,
    X86_CMOVP32rr	= 494,
    X86_CMOVP64rm	= 495,
    X86_CMOVP64rr	= 496,
    X86_CMOVP_F	= 497,
    X86_CMOVP_Fp32	= 498,
    X86_CMOVP_Fp64	= 499,
    X86_CMOVP_Fp80	= 500,
    X86_CMOVS16rm	= 501,
    X86_CMOVS16rr	= 502,
    X86_CMOVS32rm	= 503,
    X86_CMOVS32rr	= 504,
    X86_CMOVS64rm	= 505,
    X86_CMOVS64rr	= 506,
    X86_CMOV_FR32	= 507,
    X86_CMOV_FR64	= 508,
    X86_CMOV_GR16	= 509,
    X86_CMOV_GR32	= 510,
    X86_CMOV_GR8	= 511,
    X86_CMOV_RFP32	= 512,
    X86_CMOV_RFP64	= 513,
    X86_CMOV_RFP80	= 514,
    X86_CMOV_V16F32	= 515,
    X86_CMOV_V2F64	= 516,
    X86_CMOV_V2I64	= 517,
    X86_CMOV_V4F32	= 518,
    X86_CMOV_V4F64	= 519,
    X86_CMOV_V4I64	= 520,
    X86_CMOV_V8F32	= 521,
    X86_CMOV_V8F64	= 522,
    X86_CMOV_V8I64	= 523,
    X86_CMP16i16	= 524,
    X86_CMP16mi	= 525,
    X86_CMP16mi8	= 526,
    X86_CMP16mr	= 527,
    X86_CMP16ri	= 528,
    X86_CMP16ri8	= 529,
    X86_CMP16rm	= 530,
    X86_CMP16rr	= 531,
    X86_CMP16rr_REV	= 532,
    X86_CMP32i32	= 533,
    X86_CMP32mi	= 534,
    X86_CMP32mi8	= 535,
    X86_CMP32mr	= 536,
    X86_CMP32ri	= 537,
    X86_CMP32ri8	= 538,
    X86_CMP32rm	= 539,
    X86_CMP32rr	= 540,
    X86_CMP32rr_REV	= 541,
    X86_CMP64i32	= 542,
    X86_CMP64mi32	= 543,
    X86_CMP64mi8	= 544,
    X86_CMP64mr	= 545,
    X86_CMP64ri32	= 546,
    X86_CMP64ri8	= 547,
    X86_CMP64rm	= 548,
    X86_CMP64rr	= 549,
    X86_CMP64rr_REV	= 550,
    X86_CMP8i8	= 551,
    X86_CMP8mi	= 552,
    X86_CMP8mi8	= 553,
    X86_CMP8mr	= 554,
    X86_CMP8ri	= 555,
    X86_CMP8ri8	= 556,
    X86_CMP8rm	= 557,
    X86_CMP8rr	= 558,
    X86_CMP8rr_REV	= 559,
    X86_CMPPDrmi	= 560,
    X86_CMPPDrmi_alt	= 561,
    X86_CMPPDrri	= 562,
    X86_CMPPDrri_alt	= 563,
    X86_CMPPSrmi	= 564,
    X86_CMPPSrmi_alt	= 565,
    X86_CMPPSrri	= 566,
    X86_CMPPSrri_alt	= 567,
    X86_CMPSB	= 568,
    X86_CMPSDrm	= 569,
    X86_CMPSDrm_alt	= 570,
    X86_CMPSDrr	= 571,
    X86_CMPSDrr_alt	= 572,
    X86_CMPSL	= 573,
    X86_CMPSQ	= 574,
    X86_CMPSSrm	= 575,
    X86_CMPSSrm_alt	= 576,
    X86_CMPSSrr	= 577,
    X86_CMPSSrr_alt	= 578,
    X86_CMPSW	= 579,
    X86_CMPXCHG16B	= 580,
    X86_CMPXCHG16rm	= 581,
    X86_CMPXCHG16rr	= 582,
    X86_CMPXCHG32rm	= 583,
    X86_CMPXCHG32rr	= 584,
    X86_CMPXCHG64rm	= 585,
    X86_CMPXCHG64rr	= 586,
    X86_CMPXCHG8B	= 587,
    X86_CMPXCHG8rm	= 588,
    X86_CMPXCHG8rr	= 589,
    X86_COMISDrm	= 590,
    X86_COMISDrr	= 591,
    X86_COMISSrm	= 592,
    X86_COMISSrr	= 593,
    X86_COMP_FST0r	= 594,
    X86_COM_FIPr	= 595,
    X86_COM_FIr	= 596,
    X86_COM_FST0r	= 597,
    X86_COS_F	= 598,
    X86_COS_Fp32	= 599,
    X86_COS_Fp64	= 600,
    X86_COS_Fp80	= 601,
    X86_CPUID	= 602,
    X86_CQO	= 603,
    X86_CRC32r32m16	= 604,
    X86_CRC32r32m32	= 605,
    X86_CRC32r32m8	= 606,
    X86_CRC32r32r16	= 607,
    X86_CRC32r32r32	= 608,
    X86_CRC32r32r8	= 609,
    X86_CRC32r64m64	= 610,
    X86_CRC32r64m8	= 611,
    X86_CRC32r64r64	= 612,
    X86_CRC32r64r8	= 613,
    X86_CVTDQ2PDrm	= 614,
    X86_CVTDQ2PDrr	= 615,
    X86_CVTDQ2PSrm	= 616,
    X86_CVTDQ2PSrr	= 617,
    X86_CVTPD2DQrm	= 618,
    X86_CVTPD2DQrr	= 619,
    X86_CVTPD2PSrm	= 620,
    X86_CVTPD2PSrr	= 621,
    X86_CVTPS2DQrm	= 622,
    X86_CVTPS2DQrr	= 623,
    X86_CVTPS2PDrm	= 624,
    X86_CVTPS2PDrr	= 625,
    X86_CVTSD2SI64rm	= 626,
    X86_CVTSD2SI64rr	= 627,
    X86_CVTSD2SIrm	= 628,
    X86_CVTSD2SIrr	= 629,
    X86_CVTSD2SSrm	= 630,
    X86_CVTSD2SSrr	= 631,
    X86_CVTSI2SD64rm	= 632,
    X86_CVTSI2SD64rr	= 633,
    X86_CVTSI2SDrm	= 634,
    X86_CVTSI2SDrr	= 635,
    X86_CVTSI2SS64rm	= 636,
    X86_CVTSI2SS64rr	= 637,
    X86_CVTSI2SSrm	= 638,
    X86_CVTSI2SSrr	= 639,
    X86_CVTSS2SDrm	= 640,
    X86_CVTSS2SDrr	= 641,
    X86_CVTSS2SI64rm	= 642,
    X86_CVTSS2SI64rr	= 643,
    X86_CVTSS2SIrm	= 644,
    X86_CVTSS2SIrr	= 645,
    X86_CVTTPD2DQrm	= 646,
    X86_CVTTPD2DQrr	= 647,
    X86_CVTTPS2DQrm	= 648,
    X86_CVTTPS2DQrr	= 649,
    X86_CVTTSD2SI64rm	= 650,
    X86_CVTTSD2SI64rr	= 651,
    X86_CVTTSD2SIrm	= 652,
    X86_CVTTSD2SIrr	= 653,
    X86_CVTTSS2SI64rm	= 654,
    X86_CVTTSS2SI64rr	= 655,
    X86_CVTTSS2SIrm	= 656,
    X86_CVTTSS2SIrr	= 657,
    X86_CWD	= 658,
    X86_CWDE	= 659,
    X86_DAA	= 660,
    X86_DAS	= 661,
    X86_DATA16_PREFIX	= 662,
    X86_DEC16m	= 663,
    X86_DEC16r	= 664,
    X86_DEC16r_alt	= 665,
    X86_DEC32m	= 666,
    X86_DEC32r	= 667,
    X86_DEC32r_alt	= 668,
    X86_DEC64m	= 669,
    X86_DEC64r	= 670,
    X86_DEC8m	= 671,
    X86_DEC8r	= 672,
    X86_DIV16m	= 673,
    X86_DIV16r	= 674,
    X86_DIV32m	= 675,
    X86_DIV32r	= 676,
    X86_DIV64m	= 677,
    X86_DIV64r	= 678,
    X86_DIV8m	= 679,
    X86_DIV8r	= 680,
    X86_DIVPDrm	= 681,
    X86_DIVPDrr	= 682,
    X86_DIVPSrm	= 683,
    X86_DIVPSrr	= 684,
    X86_DIVR_F32m	= 685,
    X86_DIVR_F64m	= 686,
    X86_DIVR_FI16m	= 687,
    X86_DIVR_FI32m	= 688,
    X86_DIVR_FPrST0	= 689,
    X86_DIVR_FST0r	= 690,
    X86_DIVR_Fp32m	= 691,
    X86_DIVR_Fp64m	= 692,
    X86_DIVR_Fp64m32	= 693,
    X86_DIVR_Fp80m32	= 694,
    X86_DIVR_Fp80m64	= 695,
    X86_DIVR_FpI16m32	= 696,
    X86_DIVR_FpI16m64	= 697,
    X86_DIVR_FpI16m80	= 698,
    X86_DIVR_FpI32m32	= 699,
    X86_DIVR_FpI32m64	= 700,
    X86_DIVR_FpI32m80	= 701,
    X86_DIVR_FrST0	= 702,
    X86_DIVSDrm	= 703,
    X86_DIVSDrm_Int	= 704,
    X86_DIVSDrr	= 705,
    X86_DIVSDrr_Int	= 706,
    X86_DIVSSrm	= 707,
    X86_DIVSSrm_Int	= 708,
    X86_DIVSSrr	= 709,
    X86_DIVSSrr_Int	= 710,
    X86_DIV_F32m	= 711,
    X86_DIV_F64m	= 712,
    X86_DIV_FI16m	= 713,
    X86_DIV_FI32m	= 714,
    X86_DIV_FPrST0	= 715,
    X86_DIV_FST0r	= 716,
    X86_DIV_Fp32	= 717,
    X86_DIV_Fp32m	= 718,
    X86_DIV_Fp64	= 719,
    X86_DIV_Fp64m	= 720,
    X86_DIV_Fp64m32	= 721,
    X86_DIV_Fp80	= 722,
    X86_DIV_Fp80m32	= 723,
    X86_DIV_Fp80m64	= 724,
    X86_DIV_FpI16m32	= 725,
    X86_DIV_FpI16m64	= 726,
    X86_DIV_FpI16m80	= 727,
    X86_DIV_FpI32m32	= 728,
    X86_DIV_FpI32m64	= 729,
    X86_DIV_FpI32m80	= 730,
    X86_DIV_FrST0	= 731,
    X86_DPPDrmi	= 732,
    X86_DPPDrri	= 733,
    X86_DPPSrmi	= 734,
    X86_DPPSrri	= 735,
    X86_EH_RETURN	= 736,
    X86_EH_RETURN64	= 737,
    X86_EH_SjLj_LongJmp32	= 738,
    X86_EH_SjLj_LongJmp64	= 739,
    X86_EH_SjLj_SetJmp32	= 740,
    X86_EH_SjLj_SetJmp64	= 741,
    X86_EH_SjLj_Setup	= 742,
    X86_ENCLS	= 743,
    X86_ENCLU	= 744,
    X86_ENTER	= 745,
    X86_EXTRACTPSmr	= 746,
    X86_EXTRACTPSrr	= 747,
    X86_EXTRQ	= 748,
    X86_EXTRQI	= 749,
    X86_F2XM1	= 750,
    X86_FARCALL16i	= 751,
    X86_FARCALL16m	= 752,
    X86_FARCALL32i	= 753,
    X86_FARCALL32m	= 754,
    X86_FARCALL64	= 755,
    X86_FARJMP16i	= 756,
    X86_FARJMP16m	= 757,
    X86_FARJMP32i	= 758,
    X86_FARJMP32m	= 759,
    X86_FARJMP64	= 760,
    X86_FBLDm	= 761,
    X86_FBSTPm	= 762,
    X86_FCOM32m	= 763,
    X86_FCOM64m	= 764,
    X86_FCOMP32m	= 765,
    X86_FCOMP64m	= 766,
    X86_FCOMPP	= 767,
    X86_FDECSTP	= 768,
    X86_FEMMS	= 769,
    X86_FFREE	= 770,
    X86_FICOM16m	= 771,
    X86_FICOM32m	= 772,
    X86_FICOMP16m	= 773,
    X86_FICOMP32m	= 774,
    X86_FINCSTP	= 775,
    X86_FLDCW16m	= 776,
    X86_FLDENVm	= 777,
    X86_FLDL2E	= 778,
    X86_FLDL2T	= 779,
    X86_FLDLG2	= 780,
    X86_FLDLN2	= 781,
    X86_FLDPI	= 782,
    X86_FNCLEX	= 783,
    X86_FNINIT	= 784,
    X86_FNOP	= 785,
    X86_FNSTCW16m	= 786,
    X86_FNSTSW16r	= 787,
    X86_FNSTSWm	= 788,
    X86_FP32_TO_INT16_IN_MEM	= 789,
    X86_FP32_TO_INT32_IN_MEM	= 790,
    X86_FP32_TO_INT64_IN_MEM	= 791,
    X86_FP64_TO_INT16_IN_MEM	= 792,
    X86_FP64_TO_INT32_IN_MEM	= 793,
    X86_FP64_TO_INT64_IN_MEM	= 794,
    X86_FP80_TO_INT16_IN_MEM	= 795,
    X86_FP80_TO_INT32_IN_MEM	= 796,
    X86_FP80_TO_INT64_IN_MEM	= 797,
    X86_FPATAN	= 798,
    X86_FPREM	= 799,
    X86_FPREM1	= 800,
    X86_FPTAN	= 801,
    X86_FP_FFREEP	= 802,
    X86_FRNDINT	= 803,
    X86_FRSTORm	= 804,
    X86_FSAVEm	= 805,
    X86_FSCALE	= 806,
    X86_FSETPM	= 807,
    X86_FSINCOS	= 808,
    X86_FSTENVm	= 809,
    X86_FXAM	= 810,
    X86_FXRSTOR	= 811,
    X86_FXRSTOR64	= 812,
    X86_FXSAVE	= 813,
    X86_FXSAVE64	= 814,
    X86_FXTRACT	= 815,
    X86_FYL2X	= 816,
    X86_FYL2XP1	= 817,
    X86_FsANDNPDrm	= 818,
    X86_FsANDNPDrr	= 819,
    X86_FsANDNPSrm	= 820,
    X86_FsANDNPSrr	= 821,
    X86_FsANDPDrm	= 822,
    X86_FsANDPDrr	= 823,
    X86_FsANDPSrm	= 824,
    X86_FsANDPSrr	= 825,
    X86_FsFLD0SD	= 826,
    X86_FsFLD0SS	= 827,
    X86_FsMOVAPDrm	= 828,
    X86_FsMOVAPSrm	= 829,
    X86_FsORPDrm	= 830,
    X86_FsORPDrr	= 831,
    X86_FsORPSrm	= 832,
    X86_FsORPSrr	= 833,
    X86_FsVMOVAPDrm	= 834,
    X86_FsVMOVAPSrm	= 835,
    X86_FsXORPDrm	= 836,
    X86_FsXORPDrr	= 837,
    X86_FsXORPSrm	= 838,
    X86_FsXORPSrr	= 839,
    X86_FvANDNPDrm	= 840,
    X86_FvANDNPDrr	= 841,
    X86_FvANDNPSrm	= 842,
    X86_FvANDNPSrr	= 843,
    X86_FvANDPDrm	= 844,
    X86_FvANDPDrr	= 845,
    X86_FvANDPSrm	= 846,
    X86_FvANDPSrr	= 847,
    X86_FvORPDrm	= 848,
    X86_FvORPDrr	= 849,
    X86_FvORPSrm	= 850,
    X86_FvORPSrr	= 851,
    X86_FvXORPDrm	= 852,
    X86_FvXORPDrr	= 853,
    X86_FvXORPSrm	= 854,
    X86_FvXORPSrr	= 855,
    X86_GETSEC	= 856,
    X86_HADDPDrm	= 857,
    X86_HADDPDrr	= 858,
    X86_HADDPSrm	= 859,
    X86_HADDPSrr	= 860,
    X86_HLT	= 861,
    X86_HSUBPDrm	= 862,
    X86_HSUBPDrr	= 863,
    X86_HSUBPSrm	= 864,
    X86_HSUBPSrr	= 865,
    X86_IDIV16m	= 866,
    X86_IDIV16r	= 867,
    X86_IDIV32m	= 868,
    X86_IDIV32r	= 869,
    X86_IDIV64m	= 870,
    X86_IDIV64r	= 871,
    X86_IDIV8m	= 872,
    X86_IDIV8r	= 873,
    X86_ILD_F16m	= 874,
    X86_ILD_F32m	= 875,
    X86_ILD_F64m	= 876,
    X86_ILD_Fp16m32	= 877,
    X86_ILD_Fp16m64	= 878,
    X86_ILD_Fp16m80	= 879,
    X86_ILD_Fp32m32	= 880,
    X86_ILD_Fp32m64	= 881,
    X86_ILD_Fp32m80	= 882,
    X86_ILD_Fp64m32	= 883,
    X86_ILD_Fp64m64	= 884,
    X86_ILD_Fp64m80	= 885,
    X86_IMUL16m	= 886,
    X86_IMUL16r	= 887,
    X86_IMUL16rm	= 888,
    X86_IMUL16rmi	= 889,
    X86_IMUL16rmi8	= 890,
    X86_IMUL16rr	= 891,
    X86_IMUL16rri	= 892,
    X86_IMUL16rri8	= 893,
    X86_IMUL32m	= 894,
    X86_IMUL32r	= 895,
    X86_IMUL32rm	= 896,
    X86_IMUL32rmi	= 897,
    X86_IMUL32rmi8	= 898,
    X86_IMUL32rr	= 899,
    X86_IMUL32rri	= 900,
    X86_IMUL32rri8	= 901,
    X86_IMUL64m	= 902,
    X86_IMUL64r	= 903,
    X86_IMUL64rm	= 904,
    X86_IMUL64rmi32	= 905,
    X86_IMUL64rmi8	= 906,
    X86_IMUL64rr	= 907,
    X86_IMUL64rri32	= 908,
    X86_IMUL64rri8	= 909,
    X86_IMUL8m	= 910,
    X86_IMUL8r	= 911,
    X86_IN16ri	= 912,
    X86_IN16rr	= 913,
    X86_IN32ri	= 914,
    X86_IN32rr	= 915,
    X86_IN8ri	= 916,
    X86_IN8rr	= 917,
    X86_INC16m	= 918,
    X86_INC16r	= 919,
    X86_INC16r_alt	= 920,
    X86_INC32m	= 921,
    X86_INC32r	= 922,
    X86_INC32r_alt	= 923,
    X86_INC64m	= 924,
    X86_INC64r	= 925,
    X86_INC8m	= 926,
    X86_INC8r	= 927,
    X86_INSB	= 928,
    X86_INSERTPSrm	= 929,
    X86_INSERTPSrr	= 930,
    X86_INSERTQ	= 931,
    X86_INSERTQI	= 932,
    X86_INSL	= 933,
    X86_INSW	= 934,
    X86_INT	= 935,
    X86_INT1	= 936,
    X86_INT3	= 937,
    X86_INTO	= 938,
    X86_INVD	= 939,
    X86_INVEPT32	= 940,
    X86_INVEPT64	= 941,
    X86_INVLPG	= 942,
    X86_INVLPGA32	= 943,
    X86_INVLPGA64	= 944,
    X86_INVPCID32	= 945,
    X86_INVPCID64	= 946,
    X86_INVVPID32	= 947,
    X86_INVVPID64	= 948,
    X86_IRET16	= 949,
    X86_IRET32	= 950,
    X86_IRET64	= 951,
    X86_ISTT_FP16m	= 952,
    X86_ISTT_FP32m	= 953,
    X86_ISTT_FP64m	= 954,
    X86_ISTT_Fp16m32	= 955,
    X86_ISTT_Fp16m64	= 956,
    X86_ISTT_Fp16m80	= 957,
    X86_ISTT_Fp32m32	= 958,
    X86_ISTT_Fp32m64	= 959,
    X86_ISTT_Fp32m80	= 960,
    X86_ISTT_Fp64m32	= 961,
    X86_ISTT_Fp64m64	= 962,
    X86_ISTT_Fp64m80	= 963,
    X86_IST_F16m	= 964,
    X86_IST_F32m	= 965,
    X86_IST_FP16m	= 966,
    X86_IST_FP32m	= 967,
    X86_IST_FP64m	= 968,
    X86_IST_Fp16m32	= 969,
    X86_IST_Fp16m64	= 970,
    X86_IST_Fp16m80	= 971,
    X86_IST_Fp32m32	= 972,
    X86_IST_Fp32m64	= 973,
    X86_IST_Fp32m80	= 974,
    X86_IST_Fp64m32	= 975,
    X86_IST_Fp64m64	= 976,
    X86_IST_Fp64m80	= 977,
    X86_Int_CMPSDrm	= 978,
    X86_Int_CMPSDrr	= 979,
    X86_Int_CMPSSrm	= 980,
    X86_Int_CMPSSrr	= 981,
    X86_Int_COMISDrm	= 982,
    X86_Int_COMISDrr	= 983,
    X86_Int_COMISSrm	= 984,
    X86_Int_COMISSrr	= 985,
    X86_Int_CVTSD2SSrm	= 986,
    X86_Int_CVTSD2SSrr	= 987,
    X86_Int_CVTSI2SD64rm	= 988,
    X86_Int_CVTSI2SD64rr	= 989,
    X86_Int_CVTSI2SDrm	= 990,
    X86_Int_CVTSI2SDrr	= 991,
    X86_Int_CVTSI2SS64rm	= 992,
    X86_Int_CVTSI2SS64rr	= 993,
    X86_Int_CVTSI2SSrm	= 994,
    X86_Int_CVTSI2SSrr	= 995,
    X86_Int_CVTSS2SDrm	= 996,
    X86_Int_CVTSS2SDrr	= 997,
    X86_Int_CVTTSD2SI64rm	= 998,
    X86_Int_CVTTSD2SI64rr	= 999,
    X86_Int_CVTTSD2SIrm	= 1000,
    X86_Int_CVTTSD2SIrr	= 1001,
    X86_Int_CVTTSS2SI64rm	= 1002,
    X86_Int_CVTTSS2SI64rr	= 1003,
    X86_Int_CVTTSS2SIrm	= 1004,
    X86_Int_CVTTSS2SIrr	= 1005,
    X86_Int_MemBarrier	= 1006,
    X86_Int_UCOMISDrm	= 1007,
    X86_Int_UCOMISDrr	= 1008,
    X86_Int_UCOMISSrm	= 1009,
    X86_Int_UCOMISSrr	= 1010,
    X86_Int_VCMPSDrm	= 1011,
    X86_Int_VCMPSDrr	= 1012,
    X86_Int_VCMPSSrm	= 1013,
    X86_Int_VCMPSSrr	= 1014,
    X86_Int_VCOMISDZrm	= 1015,
    X86_Int_VCOMISDZrr	= 1016,
    X86_Int_VCOMISDrm	= 1017,
    X86_Int_VCOMISDrr	= 1018,
    X86_Int_VCOMISSZrm	= 1019,
    X86_Int_VCOMISSZrr	= 1020,
    X86_Int_VCOMISSrm	= 1021,
    X86_Int_VCOMISSrr	= 1022,
    X86_Int_VCVTSD2SSrm	= 1023,
    X86_Int_VCVTSD2SSrr	= 1024,
    X86_Int_VCVTSI2SD64Zrm	= 1025,
    X86_Int_VCVTSI2SD64Zrr	= 1026,
    X86_Int_VCVTSI2SD64rm	= 1027,
    X86_Int_VCVTSI2SD64rr	= 1028,
    X86_Int_VCVTSI2SDZrm	= 1029,
    X86_Int_VCVTSI2SDZrr	= 1030,
    X86_Int_VCVTSI2SDrm	= 1031,
    X86_Int_VCVTSI2SDrr	= 1032,
    X86_Int_VCVTSI2SS64Zrm	= 1033,
    X86_Int_VCVTSI2SS64Zrr	= 1034,
    X86_Int_VCVTSI2SS64rm	= 1035,
    X86_Int_VCVTSI2SS64rr	= 1036,
    X86_Int_VCVTSI2SSZrm	= 1037,
    X86_Int_VCVTSI2SSZrr	= 1038,
    X86_Int_VCVTSI2SSrm	= 1039,
    X86_Int_VCVTSI2SSrr	= 1040,
    X86_Int_VCVTSS2SDrm	= 1041,
    X86_Int_VCVTSS2SDrr	= 1042,
    X86_Int_VCVTTSD2SI64Zrm	= 1043,
    X86_Int_VCVTTSD2SI64Zrr	= 1044,
    X86_Int_VCVTTSD2SI64rm	= 1045,
    X86_Int_VCVTTSD2SI64rr	= 1046,
    X86_Int_VCVTTSD2SIZrm	= 1047,
    X86_Int_VCVTTSD2SIZrr	= 1048,
    X86_Int_VCVTTSD2SIrm	= 1049,
    X86_Int_VCVTTSD2SIrr	= 1050,
    X86_Int_VCVTTSD2USI64Zrm	= 1051,
    X86_Int_VCVTTSD2USI64Zrr	= 1052,
    X86_Int_VCVTTSD2USIZrm	= 1053,
    X86_Int_VCVTTSD2USIZrr	= 1054,
    X86_Int_VCVTTSS2SI64Zrm	= 1055,
    X86_Int_VCVTTSS2SI64Zrr	= 1056,
    X86_Int_VCVTTSS2SI64rm	= 1057,
    X86_Int_VCVTTSS2SI64rr	= 1058,
    X86_Int_VCVTTSS2SIZrm	= 1059,
    X86_Int_VCVTTSS2SIZrr	= 1060,
    X86_Int_VCVTTSS2SIrm	= 1061,
    X86_Int_VCVTTSS2SIrr	= 1062,
    X86_Int_VCVTTSS2USI64Zrm	= 1063,
    X86_Int_VCVTTSS2USI64Zrr	= 1064,
    X86_Int_VCVTTSS2USIZrm	= 1065,
    X86_Int_VCVTTSS2USIZrr	= 1066,
    X86_Int_VCVTUSI2SD64Zrm	= 1067,
    X86_Int_VCVTUSI2SD64Zrr	= 1068,
    X86_Int_VCVTUSI2SDZrm	= 1069,
    X86_Int_VCVTUSI2SDZrr	= 1070,
    X86_Int_VCVTUSI2SS64Zrm	= 1071,
    X86_Int_VCVTUSI2SS64Zrr	= 1072,
    X86_Int_VCVTUSI2SSZrm	= 1073,
    X86_Int_VCVTUSI2SSZrr	= 1074,
    X86_Int_VUCOMISDZrm	= 1075,
    X86_Int_VUCOMISDZrr	= 1076,
    X86_Int_VUCOMISDrm	= 1077,
    X86_Int_VUCOMISDrr	= 1078,
    X86_Int_VUCOMISSZrm	= 1079,
    X86_Int_VUCOMISSZrr	= 1080,
    X86_Int_VUCOMISSrm	= 1081,
    X86_Int_VUCOMISSrr	= 1082,
    X86_JAE_1	= 1083,
    X86_JAE_2	= 1084,
    X86_JAE_4	= 1085,
    X86_JA_1	= 1086,
    X86_JA_2	= 1087,
    X86_JA_4	= 1088,
    X86_JBE_1	= 1089,
    X86_JBE_2	= 1090,
    X86_JBE_4	= 1091,
    X86_JB_1	= 1092,
    X86_JB_2	= 1093,
    X86_JB_4	= 1094,
    X86_JCXZ	= 1095,
    X86_JECXZ	= 1096,
    X86_JE_1	= 1097,
    X86_JE_2	= 1098,
    X86_JE_4	= 1099,
    X86_JGE_1	= 1100,
    X86_JGE_2	= 1101,
    X86_JGE_4	= 1102,
    X86_JG_1	= 1103,
    X86_JG_2	= 1104,
    X86_JG_4	= 1105,
    X86_JLE_1	= 1106,
    X86_JLE_2	= 1107,
    X86_JLE_4	= 1108,
    X86_JL_1	= 1109,
    X86_JL_2	= 1110,
    X86_JL_4	= 1111,
    X86_JMP16m	= 1112,
    X86_JMP16r	= 1113,
    X86_JMP32m	= 1114,
    X86_JMP32r	= 1115,
    X86_JMP64m	= 1116,
    X86_JMP64r	= 1117,
    X86_JMP_1	= 1118,
    X86_JMP_2	= 1119,
    X86_JMP_4	= 1120,
    X86_JNE_1	= 1121,
    X86_JNE_2	= 1122,
    X86_JNE_4	= 1123,
    X86_JNO_1	= 1124,
    X86_JNO_2	= 1125,
    X86_JNO_4	= 1126,
    X86_JNP_1	= 1127,
    X86_JNP_2	= 1128,
    X86_JNP_4	= 1129,
    X86_JNS_1	= 1130,
    X86_JNS_2	= 1131,
    X86_JNS_4	= 1132,
    X86_JO_1	= 1133,
    X86_JO_2	= 1134,
    X86_JO_4	= 1135,
    X86_JP_1	= 1136,
    X86_JP_2	= 1137,
    X86_JP_4	= 1138,
    X86_JRCXZ	= 1139,
    X86_JS_1	= 1140,
    X86_JS_2	= 1141,
    X86_JS_4	= 1142,
    X86_KANDBrr	= 1143,
    X86_KANDDrr	= 1144,
    X86_KANDNBrr	= 1145,
    X86_KANDNDrr	= 1146,
    X86_KANDNQrr	= 1147,
    X86_KANDNWrr	= 1148,
    X86_KANDQrr	= 1149,
    X86_KANDWrr	= 1150,
    X86_KMOVBkk	= 1151,
    X86_KMOVBkm	= 1152,
    X86_KMOVBkr	= 1153,
    X86_KMOVBmk	= 1154,
    X86_KMOVBrk	= 1155,
    X86_KMOVDkk	= 1156,
    X86_KMOVDkm	= 1157,
    X86_KMOVDkr	= 1158,
    X86_KMOVDmk	= 1159,
    X86_KMOVDrk	= 1160,
    X86_KMOVQkk	= 1161,
    X86_KMOVQkm	= 1162,
    X86_KMOVQkr	= 1163,
    X86_KMOVQmk	= 1164,
    X86_KMOVQrk	= 1165,
    X86_KMOVWkk	= 1166,
    X86_KMOVWkm	= 1167,
    X86_KMOVWkr	= 1168,
    X86_KMOVWmk	= 1169,
    X86_KMOVWrk	= 1170,
    X86_KNOTBrr	= 1171,
    X86_KNOTDrr	= 1172,
    X86_KNOTQrr	= 1173,
    X86_KNOTWrr	= 1174,
    X86_KORBrr	= 1175,
    X86_KORDrr	= 1176,
    X86_KORQrr	= 1177,
    X86_KORTESTBrr	= 1178,
    X86_KORTESTDrr	= 1179,
    X86_KORTESTQrr	= 1180,
    X86_KORTESTWrr	= 1181,
    X86_KORWrr	= 1182,
    X86_KSET0B	= 1183,
    X86_KSET0W	= 1184,
    X86_KSET1B	= 1185,
    X86_KSET1W	= 1186,
    X86_KSHIFTLBri	= 1187,
    X86_KSHIFTLDri	= 1188,
    X86_KSHIFTLQri	= 1189,
    X86_KSHIFTLWri	= 1190,
    X86_KSHIFTRBri	= 1191,
    X86_KSHIFTRDri	= 1192,
    X86_KSHIFTRQri	= 1193,
    X86_KSHIFTRWri	= 1194,
    X86_KUNPCKBWrr	= 1195,
    X86_KXNORBrr	= 1196,
    X86_KXNORDrr	= 1197,
    X86_KXNORQrr	= 1198,
    X86_KXNORWrr	= 1199,
    X86_KXORBrr	= 1200,
    X86_KXORDrr	= 1201,
    X86_KXORQrr	= 1202,
    X86_KXORWrr	= 1203,
    X86_LAHF	= 1204,
    X86_LAR16rm	= 1205,
    X86_LAR16rr	= 1206,
    X86_LAR32rm	= 1207,
    X86_LAR32rr	= 1208,
    X86_LAR64rm	= 1209,
    X86_LAR64rr	= 1210,
    X86_LCMPXCHG16	= 1211,
    X86_LCMPXCHG16B	= 1212,
    X86_LCMPXCHG32	= 1213,
    X86_LCMPXCHG64	= 1214,
    X86_LCMPXCHG8	= 1215,
    X86_LCMPXCHG8B	= 1216,
    X86_LDDQUrm	= 1217,
    X86_LDMXCSR	= 1218,
    X86_LDS16rm	= 1219,
    X86_LDS32rm	= 1220,
    X86_LD_F0	= 1221,
    X86_LD_F1	= 1222,
    X86_LD_F32m	= 1223,
    X86_LD_F64m	= 1224,
    X86_LD_F80m	= 1225,
    X86_LD_Fp032	= 1226,
    X86_LD_Fp064	= 1227,
    X86_LD_Fp080	= 1228,
    X86_LD_Fp132	= 1229,
    X86_LD_Fp164	= 1230,
    X86_LD_Fp180	= 1231,
    X86_LD_Fp32m	= 1232,
    X86_LD_Fp32m64	= 1233,
    X86_LD_Fp32m80	= 1234,
    X86_LD_Fp64m	= 1235,
    X86_LD_Fp64m80	= 1236,
    X86_LD_Fp80m	= 1237,
    X86_LD_Frr	= 1238,
    X86_LEA16r	= 1239,
    X86_LEA32r	= 1240,
    X86_LEA64_32r	= 1241,
    X86_LEA64r	= 1242,
    X86_LEAVE	= 1243,
    X86_LEAVE64	= 1244,
    X86_LES16rm	= 1245,
    X86_LES32rm	= 1246,
    X86_LFENCE	= 1247,
    X86_LFS16rm	= 1248,
    X86_LFS32rm	= 1249,
    X86_LFS64rm	= 1250,
    X86_LGDT16m	= 1251,
    X86_LGDT32m	= 1252,
    X86_LGDT64m	= 1253,
    X86_LGS16rm	= 1254,
    X86_LGS32rm	= 1255,
    X86_LGS64rm	= 1256,
    X86_LIDT16m	= 1257,
    X86_LIDT32m	= 1258,
    X86_LIDT64m	= 1259,
    X86_LLDT16m	= 1260,
    X86_LLDT16r	= 1261,
    X86_LMSW16m	= 1262,
    X86_LMSW16r	= 1263,
    X86_LOCK_ADD16mi	= 1264,
    X86_LOCK_ADD16mi8	= 1265,
    X86_LOCK_ADD16mr	= 1266,
    X86_LOCK_ADD32mi	= 1267,
    X86_LOCK_ADD32mi8	= 1268,
    X86_LOCK_ADD32mr	= 1269,
    X86_LOCK_ADD64mi32	= 1270,
    X86_LOCK_ADD64mi8	= 1271,
    X86_LOCK_ADD64mr	= 1272,
    X86_LOCK_ADD8mi	= 1273,
    X86_LOCK_ADD8mr	= 1274,
    X86_LOCK_AND16mi	= 1275,
    X86_LOCK_AND16mi8	= 1276,
    X86_LOCK_AND16mr	= 1277,
    X86_LOCK_AND32mi	= 1278,
    X86_LOCK_AND32mi8	= 1279,
    X86_LOCK_AND32mr	= 1280,
    X86_LOCK_AND64mi32	= 1281,
    X86_LOCK_AND64mi8	= 1282,
    X86_LOCK_AND64mr	= 1283,
    X86_LOCK_AND8mi	= 1284,
    X86_LOCK_AND8mr	= 1285,
    X86_LOCK_DEC16m	= 1286,
    X86_LOCK_DEC32m	= 1287,
    X86_LOCK_DEC64m	= 1288,
    X86_LOCK_DEC8m	= 1289,
    X86_LOCK_INC16m	= 1290,
    X86_LOCK_INC32m	= 1291,
    X86_LOCK_INC64m	= 1292,
    X86_LOCK_INC8m	= 1293,
    X86_LOCK_OR16mi	= 1294,
    X86_LOCK_OR16mi8	= 1295,
    X86_LOCK_OR16mr	= 1296,
    X86_LOCK_OR32mi	= 1297,
    X86_LOCK_OR32mi8	= 1298,
    X86_LOCK_OR32mr	= 1299,
    X86_LOCK_OR64mi32	= 1300,
    X86_LOCK_OR64mi8	= 1301,
    X86_LOCK_OR64mr	= 1302,
    X86_LOCK_OR8mi	= 1303,
    X86_LOCK_OR8mr	= 1304,
    X86_LOCK_PREFIX	= 1305,
    X86_LOCK_SUB16mi	= 1306,
    X86_LOCK_SUB16mi8	= 1307,
    X86_LOCK_SUB16mr	= 1308,
    X86_LOCK_SUB32mi	= 1309,
    X86_LOCK_SUB32mi8	= 1310,
    X86_LOCK_SUB32mr	= 1311,
    X86_LOCK_SUB64mi32	= 1312,
    X86_LOCK_SUB64mi8	= 1313,
    X86_LOCK_SUB64mr	= 1314,
    X86_LOCK_SUB8mi	= 1315,
    X86_LOCK_SUB8mr	= 1316,
    X86_LOCK_XOR16mi	= 1317,
    X86_LOCK_XOR16mi8	= 1318,
    X86_LOCK_XOR16mr	= 1319,
    X86_LOCK_XOR32mi	= 1320,
    X86_LOCK_XOR32mi8	= 1321,
    X86_LOCK_XOR32mr	= 1322,
    X86_LOCK_XOR64mi32	= 1323,
    X86_LOCK_XOR64mi8	= 1324,
    X86_LOCK_XOR64mr	= 1325,
    X86_LOCK_XOR8mi	= 1326,
    X86_LOCK_XOR8mr	= 1327,
    X86_LODSB	= 1328,
    X86_LODSL	= 1329,
    X86_LODSQ	= 1330,
    X86_LODSW	= 1331,
    X86_LOOP	= 1332,
    X86_LOOPE	= 1333,
    X86_LOOPNE	= 1334,
    X86_LRETIL	= 1335,
    X86_LRETIQ	= 1336,
    X86_LRETIW	= 1337,
    X86_LRETL	= 1338,
    X86_LRETQ	= 1339,
    X86_LRETW	= 1340,
    X86_LSL16rm	= 1341,
    X86_LSL16rr	= 1342,
    X86_LSL32rm	= 1343,
    X86_LSL32rr	= 1344,
    X86_LSL64rm	= 1345,
    X86_LSL64rr	= 1346,
    X86_LSS16rm	= 1347,
    X86_LSS32rm	= 1348,
    X86_LSS64rm	= 1349,
    X86_LTRm	= 1350,
    X86_LTRr	= 1351,
    X86_LXADD16	= 1352,
    X86_LXADD32	= 1353,
    X86_LXADD64	= 1354,
    X86_LXADD8	= 1355,
    X86_LZCNT16rm	= 1356,
    X86_LZCNT16rr	= 1357,
    X86_LZCNT32rm	= 1358,
    X86_LZCNT32rr	= 1359,
    X86_LZCNT64rm	= 1360,
    X86_LZCNT64rr	= 1361,
    X86_MASKMOVDQU	= 1362,
    X86_MASKMOVDQU64	= 1363,
    X86_MAXCPDrm	= 1364,
    X86_MAXCPDrr	= 1365,
    X86_MAXCPSrm	= 1366,
    X86_MAXCPSrr	= 1367,
    X86_MAXCSDrm	= 1368,
    X86_MAXCSDrr	= 1369,
    X86_MAXCSSrm	= 1370,
    X86_MAXCSSrr	= 1371,
    X86_MAXPDrm	= 1372,
    X86_MAXPDrr	= 1373,
    X86_MAXPSrm	= 1374,
    X86_MAXPSrr	= 1375,
    X86_MAXSDrm	= 1376,
    X86_MAXSDrm_Int	= 1377,
    X86_MAXSDrr	= 1378,
    X86_MAXSDrr_Int	= 1379,
    X86_MAXSSrm	= 1380,
    X86_MAXSSrm_Int	= 1381,
    X86_MAXSSrr	= 1382,
    X86_MAXSSrr_Int	= 1383,
    X86_MFENCE	= 1384,
    X86_MINCPDrm	= 1385,
    X86_MINCPDrr	= 1386,
    X86_MINCPSrm	= 1387,
    X86_MINCPSrr	= 1388,
    X86_MINCSDrm	= 1389,
    X86_MINCSDrr	= 1390,
    X86_MINCSSrm	= 1391,
    X86_MINCSSrr	= 1392,
    X86_MINPDrm	= 1393,
    X86_MINPDrr	= 1394,
    X86_MINPSrm	= 1395,
    X86_MINPSrr	= 1396,
    X86_MINSDrm	= 1397,
    X86_MINSDrm_Int	= 1398,
    X86_MINSDrr	= 1399,
    X86_MINSDrr_Int	= 1400,
    X86_MINSSrm	= 1401,
    X86_MINSSrm_Int	= 1402,
    X86_MINSSrr	= 1403,
    X86_MINSSrr_Int	= 1404,
    X86_MMX_CVTPD2PIirm	= 1405,
    X86_MMX_CVTPD2PIirr	= 1406,
    X86_MMX_CVTPI2PDirm	= 1407,
    X86_MMX_CVTPI2PDirr	= 1408,
    X86_MMX_CVTPI2PSirm	= 1409,
    X86_MMX_CVTPI2PSirr	= 1410,
    X86_MMX_CVTPS2PIirm	= 1411,
    X86_MMX_CVTPS2PIirr	= 1412,
    X86_MMX_CVTTPD2PIirm	= 1413,
    X86_MMX_CVTTPD2PIirr	= 1414,
    X86_MMX_CVTTPS2PIirm	= 1415,
    X86_MMX_CVTTPS2PIirr	= 1416,
    X86_MMX_EMMS	= 1417,
    X86_MMX_MASKMOVQ	= 1418,
    X86_MMX_MASKMOVQ64	= 1419,
    X86_MMX_MOVD64from64rm	= 1420,
    X86_MMX_MOVD64from64rr	= 1421,
    X86_MMX_MOVD64grr	= 1422,
    X86_MMX_MOVD64mr	= 1423,
    X86_MMX_MOVD64rm	= 1424,
    X86_MMX_MOVD64rr	= 1425,
    X86_MMX_MOVD64to64rm	= 1426,
    X86_MMX_MOVD64to64rr	= 1427,
    X86_MMX_MOVDQ2Qrr	= 1428,
    X86_MMX_MOVFR642Qrr	= 1429,
    X86_MMX_MOVNTQmr	= 1430,
    X86_MMX_MOVQ2DQrr	= 1431,
    X86_MMX_MOVQ2FR64rr	= 1432,
    X86_MMX_MOVQ64mr	= 1433,
    X86_MMX_MOVQ64rm	= 1434,
    X86_MMX_MOVQ64rr	= 1435,
    X86_MMX_MOVQ64rr_REV	= 1436,
    X86_MMX_PABSBrm64	= 1437,
    X86_MMX_PABSBrr64	= 1438,
    X86_MMX_PABSDrm64	= 1439,
    X86_MMX_PABSDrr64	= 1440,
    X86_MMX_PABSWrm64	= 1441,
    X86_MMX_PABSWrr64	= 1442,
    X86_MMX_PACKSSDWirm	= 1443,
    X86_MMX_PACKSSDWirr	= 1444,
    X86_MMX_PACKSSWBirm	= 1445,
    X86_MMX_PACKSSWBirr	= 1446,
    X86_MMX_PACKUSWBirm	= 1447,
    X86_MMX_PACKUSWBirr	= 1448,
    X86_MMX_PADDBirm	= 1449,
    X86_MMX_PADDBirr	= 1450,
    X86_MMX_PADDDirm	= 1451,
    X86_MMX_PADDDirr	= 1452,
    X86_MMX_PADDQirm	= 1453,
    X86_MMX_PADDQirr	= 1454,
    X86_MMX_PADDSBirm	= 1455,
    X86_MMX_PADDSBirr	= 1456,
    X86_MMX_PADDSWirm	= 1457,
    X86_MMX_PADDSWirr	= 1458,
    X86_MMX_PADDUSBirm	= 1459,
    X86_MMX_PADDUSBirr	= 1460,
    X86_MMX_PADDUSWirm	= 1461,
    X86_MMX_PADDUSWirr	= 1462,
    X86_MMX_PADDWirm	= 1463,
    X86_MMX_PADDWirr	= 1464,
    X86_MMX_PALIGNR64irm	= 1465,
    X86_MMX_PALIGNR64irr	= 1466,
    X86_MMX_PANDNirm	= 1467,
    X86_MMX_PANDNirr	= 1468,
    X86_MMX_PANDirm	= 1469,
    X86_MMX_PANDirr	= 1470,
    X86_MMX_PAVGBirm	= 1471,
    X86_MMX_PAVGBirr	= 1472,
    X86_MMX_PAVGWirm	= 1473,
    X86_MMX_PAVGWirr	= 1474,
    X86_MMX_PCMPEQBirm	= 1475,
    X86_MMX_PCMPEQBirr	= 1476,
    X86_MMX_PCMPEQDirm	= 1477,
    X86_MMX_PCMPEQDirr	= 1478,
    X86_MMX_PCMPEQWirm	= 1479,
    X86_MMX_PCMPEQWirr	= 1480,
    X86_MMX_PCMPGTBirm	= 1481,
    X86_MMX_PCMPGTBirr	= 1482,
    X86_MMX_PCMPGTDirm	= 1483,
    X86_MMX_PCMPGTDirr	= 1484,
    X86_MMX_PCMPGTWirm	= 1485,
    X86_MMX_PCMPGTWirr	= 1486,
    X86_MMX_PEXTRWirri	= 1487,
    X86_MMX_PHADDSWrm64	= 1488,
    X86_MMX_PHADDSWrr64	= 1489,
    X86_MMX_PHADDWrm64	= 1490,
    X86_MMX_PHADDWrr64	= 1491,
    X86_MMX_PHADDrm64	= 1492,
    X86_MMX_PHADDrr64	= 1493,
    X86_MMX_PHSUBDrm64	= 1494,
    X86_MMX_PHSUBDrr64	= 1495,
    X86_MMX_PHSUBSWrm64	= 1496,
    X86_MMX_PHSUBSWrr64	= 1497,
    X86_MMX_PHSUBWrm64	= 1498,
    X86_MMX_PHSUBWrr64	= 1499,
    X86_MMX_PINSRWirmi	= 1500,
    X86_MMX_PINSRWirri	= 1501,
    X86_MMX_PMADDUBSWrm64	= 1502,
    X86_MMX_PMADDUBSWrr64	= 1503,
    X86_MMX_PMADDWDirm	= 1504,
    X86_MMX_PMADDWDirr	= 1505,
    X86_MMX_PMAXSWirm	= 1506,
    X86_MMX_PMAXSWirr	= 1507,
    X86_MMX_PMAXUBirm	= 1508,
    X86_MMX_PMAXUBirr	= 1509,
    X86_MMX_PMINSWirm	= 1510,
    X86_MMX_PMINSWirr	= 1511,
    X86_MMX_PMINUBirm	= 1512,
    X86_MMX_PMINUBirr	= 1513,
    X86_MMX_PMOVMSKBrr	= 1514,
    X86_MMX_PMULHRSWrm64	= 1515,
    X86_MMX_PMULHRSWrr64	= 1516,
    X86_MMX_PMULHUWirm	= 1517,
    X86_MMX_PMULHUWirr	= 1518,
    X86_MMX_PMULHWirm	= 1519,
    X86_MMX_PMULHWirr	= 1520,
    X86_MMX_PMULLWirm	= 1521,
    X86_MMX_PMULLWirr	= 1522,
    X86_MMX_PMULUDQirm	= 1523,
    X86_MMX_PMULUDQirr	= 1524,
    X86_MMX_PORirm	= 1525,
    X86_MMX_PORirr	= 1526,
    X86_MMX_PSADBWirm	= 1527,
    X86_MMX_PSADBWirr	= 1528,
    X86_MMX_PSHUFBrm64	= 1529,
    X86_MMX_PSHUFBrr64	= 1530,
    X86_MMX_PSHUFWmi	= 1531,
    X86_MMX_PSHUFWri	= 1532,
    X86_MMX_PSIGNBrm64	= 1533,
    X86_MMX_PSIGNBrr64	= 1534,
    X86_MMX_PSIGNDrm64	= 1535,
    X86_MMX_PSIGNDrr64	= 1536,
    X86_MMX_PSIGNWrm64	= 1537,
    X86_MMX_PSIGNWrr64	= 1538,
    X86_MMX_PSLLDri	= 1539,
    X86_MMX_PSLLDrm	= 1540,
    X86_MMX_PSLLDrr	= 1541,
    X86_MMX_PSLLQri	= 1542,
    X86_MMX_PSLLQrm	= 1543,
    X86_MMX_PSLLQrr	= 1544,
    X86_MMX_PSLLWri	= 1545,
    X86_MMX_PSLLWrm	= 1546,
    X86_MMX_PSLLWrr	= 1547,
    X86_MMX_PSRADri	= 1548,
    X86_MMX_PSRADrm	= 1549,
    X86_MMX_PSRADrr	= 1550,
    X86_MMX_PSRAWri	= 1551,
    X86_MMX_PSRAWrm	= 1552,
    X86_MMX_PSRAWrr	= 1553,
    X86_MMX_PSRLDri	= 1554,
    X86_MMX_PSRLDrm	= 1555,
    X86_MMX_PSRLDrr	= 1556,
    X86_MMX_PSRLQri	= 1557,
    X86_MMX_PSRLQrm	= 1558,
    X86_MMX_PSRLQrr	= 1559,
    X86_MMX_PSRLWri	= 1560,
    X86_MMX_PSRLWrm	= 1561,
    X86_MMX_PSRLWrr	= 1562,
    X86_MMX_PSUBBirm	= 1563,
    X86_MMX_PSUBBirr	= 1564,
    X86_MMX_PSUBDirm	= 1565,
    X86_MMX_PSUBDirr	= 1566,
    X86_MMX_PSUBQirm	= 1567,
    X86_MMX_PSUBQirr	= 1568,
    X86_MMX_PSUBSBirm	= 1569,
    X86_MMX_PSUBSBirr	= 1570,
    X86_MMX_PSUBSWirm	= 1571,
    X86_MMX_PSUBSWirr	= 1572,
    X86_MMX_PSUBUSBirm	= 1573,
    X86_MMX_PSUBUSBirr	= 1574,
    X86_MMX_PSUBUSWirm	= 1575,
    X86_MMX_PSUBUSWirr	= 1576,
    X86_MMX_PSUBWirm	= 1577,
    X86_MMX_PSUBWirr	= 1578,
    X86_MMX_PUNPCKHBWirm	= 1579,
    X86_MMX_PUNPCKHBWirr	= 1580,
    X86_MMX_PUNPCKHDQirm	= 1581,
    X86_MMX_PUNPCKHDQirr	= 1582,
    X86_MMX_PUNPCKHWDirm	= 1583,
    X86_MMX_PUNPCKHWDirr	= 1584,
    X86_MMX_PUNPCKLBWirm	= 1585,
    X86_MMX_PUNPCKLBWirr	= 1586,
    X86_MMX_PUNPCKLDQirm	= 1587,
    X86_MMX_PUNPCKLDQirr	= 1588,
    X86_MMX_PUNPCKLWDirm	= 1589,
    X86_MMX_PUNPCKLWDirr	= 1590,
    X86_MMX_PXORirm	= 1591,
    X86_MMX_PXORirr	= 1592,
    X86_MONITOR	= 1593,
    X86_MONITORrrr	= 1594,
    X86_MONTMUL	= 1595,
    X86_MORESTACK_RET	= 1596,
    X86_MORESTACK_RET_RESTORE_R10	= 1597,
    X86_MOV16ao16	= 1598,
    X86_MOV16ao32	= 1599,
    X86_MOV16ao64	= 1600,
    X86_MOV16mi	= 1601,
    X86_MOV16mr	= 1602,
    X86_MOV16ms	= 1603,
    X86_MOV16o16a	= 1604,
    X86_MOV16o32a	= 1605,
    X86_MOV16o64a	= 1606,
    X86_MOV16ri	= 1607,
    X86_MOV16ri_alt	= 1608,
    X86_MOV16rm	= 1609,
    X86_MOV16rr	= 1610,
    X86_MOV16rr_REV	= 1611,
    X86_MOV16rs	= 1612,
    X86_MOV16sm	= 1613,
    X86_MOV16sr	= 1614,
    X86_MOV32ao16	= 1615,
    X86_MOV32ao32	= 1616,
    X86_MOV32ao64	= 1617,
    X86_MOV32cr	= 1618,
    X86_MOV32dr	= 1619,
    X86_MOV32mi	= 1620,
    X86_MOV32mr	= 1621,
    X86_MOV32ms	= 1622,
    X86_MOV32o16a	= 1623,
    X86_MOV32o32a	= 1624,
    X86_MOV32o64a	= 1625,
    X86_MOV32r0	= 1626,
    X86_MOV32rc	= 1627,
    X86_MOV32rd	= 1628,
    X86_MOV32ri	= 1629,
    X86_MOV32ri64	= 1630,
    X86_MOV32ri_alt	= 1631,
    X86_MOV32rm	= 1632,
    X86_MOV32rr	= 1633,
    X86_MOV32rr_REV	= 1634,
    X86_MOV32rs	= 1635,
    X86_MOV32sm	= 1636,
    X86_MOV32sr	= 1637,
    X86_MOV64ao32	= 1638,
    X86_MOV64ao64	= 1639,
    X86_MOV64cr	= 1640,
    X86_MOV64dr	= 1641,
    X86_MOV64mi32	= 1642,
    X86_MOV64mr	= 1643,
    X86_MOV64ms	= 1644,
    X86_MOV64o32a	= 1645,
    X86_MOV64o64a	= 1646,
    X86_MOV64rc	= 1647,
    X86_MOV64rd	= 1648,
    X86_MOV64ri	= 1649,
    X86_MOV64ri32	= 1650,
    X86_MOV64rm	= 1651,
    X86_MOV64rr	= 1652,
    X86_MOV64rr_REV	= 1653,
    X86_MOV64rs	= 1654,
    X86_MOV64sm	= 1655,
    X86_MOV64sr	= 1656,
    X86_MOV64toPQIrm	= 1657,
    X86_MOV64toPQIrr	= 1658,
    X86_MOV64toSDrm	= 1659,
    X86_MOV64toSDrr	= 1660,
    X86_MOV8ao16	= 1661,
    X86_MOV8ao32	= 1662,
    X86_MOV8ao64	= 1663,
    X86_MOV8mi	= 1664,
    X86_MOV8mr	= 1665,
    X86_MOV8mr_NOREX	= 1666,
    X86_MOV8o16a	= 1667,
    X86_MOV8o32a	= 1668,
    X86_MOV8o64a	= 1669,
    X86_MOV8ri	= 1670,
    X86_MOV8ri_alt	= 1671,
    X86_MOV8rm	= 1672,
    X86_MOV8rm_NOREX	= 1673,
    X86_MOV8rr	= 1674,
    X86_MOV8rr_NOREX	= 1675,
    X86_MOV8rr_REV	= 1676,
    X86_MOVAPDmr	= 1677,
    X86_MOVAPDrm	= 1678,
    X86_MOVAPDrr	= 1679,
    X86_MOVAPDrr_REV	= 1680,
    X86_MOVAPSmr	= 1681,
    X86_MOVAPSrm	= 1682,
    X86_MOVAPSrr	= 1683,
    X86_MOVAPSrr_REV	= 1684,
    X86_MOVBE16mr	= 1685,
    X86_MOVBE16rm	= 1686,
    X86_MOVBE32mr	= 1687,
    X86_MOVBE32rm	= 1688,
    X86_MOVBE64mr	= 1689,
    X86_MOVBE64rm	= 1690,
    X86_MOVDDUPrm	= 1691,
    X86_MOVDDUPrr	= 1692,
    X86_MOVDI2PDIrm	= 1693,
    X86_MOVDI2PDIrr	= 1694,
    X86_MOVDI2SSrm	= 1695,
    X86_MOVDI2SSrr	= 1696,
    X86_MOVDQAmr	= 1697,
    X86_MOVDQArm	= 1698,
    X86_MOVDQArr	= 1699,
    X86_MOVDQArr_REV	= 1700,
    X86_MOVDQUmr	= 1701,
    X86_MOVDQUrm	= 1702,
    X86_MOVDQUrr	= 1703,
    X86_MOVDQUrr_REV	= 1704,
    X86_MOVHLPSrr	= 1705,
    X86_MOVHPDmr	= 1706,
    X86_MOVHPDrm	= 1707,
    X86_MOVHPSmr	= 1708,
    X86_MOVHPSrm	= 1709,
    X86_MOVLHPSrr	= 1710,
    X86_MOVLPDmr	= 1711,
    X86_MOVLPDrm	= 1712,
    X86_MOVLPSmr	= 1713,
    X86_MOVLPSrm	= 1714,
    X86_MOVMSKPDrr	= 1715,
    X86_MOVMSKPSrr	= 1716,
    X86_MOVNTDQArm	= 1717,
    X86_MOVNTDQmr	= 1718,
    X86_MOVNTI_64mr	= 1719,
    X86_MOVNTImr	= 1720,
    X86_MOVNTPDmr	= 1721,
    X86_MOVNTPSmr	= 1722,
    X86_MOVNTSD	= 1723,
    X86_MOVNTSS	= 1724,
    X86_MOVPC32r	= 1725,
    X86_MOVPDI2DImr	= 1726,
    X86_MOVPDI2DIrr	= 1727,
    X86_MOVPQI2QImr	= 1728,
    X86_MOVPQI2QIrr	= 1729,
    X86_MOVPQIto64rm	= 1730,
    X86_MOVPQIto64rr	= 1731,
    X86_MOVQI2PQIrm	= 1732,
    X86_MOVSB	= 1733,
    X86_MOVSDmr	= 1734,
    X86_MOVSDrm	= 1735,
    X86_MOVSDrr	= 1736,
    X86_MOVSDrr_REV	= 1737,
    X86_MOVSDto64mr	= 1738,
    X86_MOVSDto64rr	= 1739,
    X86_MOVSHDUPrm	= 1740,
    X86_MOVSHDUPrr	= 1741,
    X86_MOVSL	= 1742,
    X86_MOVSLDUPrm	= 1743,
    X86_MOVSLDUPrr	= 1744,
    X86_MOVSQ	= 1745,
    X86_MOVSS2DImr	= 1746,
    X86_MOVSS2DIrr	= 1747,
    X86_MOVSSmr	= 1748,
    X86_MOVSSrm	= 1749,
    X86_MOVSSrr	= 1750,
    X86_MOVSSrr_REV	= 1751,
    X86_MOVSW	= 1752,
    X86_MOVSX16rm8	= 1753,
    X86_MOVSX16rr8	= 1754,
    X86_MOVSX32_NOREXrm8	= 1755,
    X86_MOVSX32_NOREXrr8	= 1756,
    X86_MOVSX32rm16	= 1757,
    X86_MOVSX32rm8	= 1758,
    X86_MOVSX32rr16	= 1759,
    X86_MOVSX32rr8	= 1760,
    X86_MOVSX64_NOREXrr32	= 1761,
    X86_MOVSX64rm16	= 1762,
    X86_MOVSX64rm32	= 1763,
    X86_MOVSX64rm32_alt	= 1764,
    X86_MOVSX64rm8	= 1765,
    X86_MOVSX64rr16	= 1766,
    X86_MOVSX64rr32	= 1767,
    X86_MOVSX64rr8	= 1768,
    X86_MOVUPDmr	= 1769,
    X86_MOVUPDrm	= 1770,
    X86_MOVUPDrr	= 1771,
    X86_MOVUPDrr_REV	= 1772,
    X86_MOVUPSmr	= 1773,
    X86_MOVUPSrm	= 1774,
    X86_MOVUPSrr	= 1775,
    X86_MOVUPSrr_REV	= 1776,
    X86_MOVZPQILo2PQIrm	= 1777,
    X86_MOVZPQILo2PQIrr	= 1778,
    X86_MOVZQI2PQIrm	= 1779,
    X86_MOVZQI2PQIrr	= 1780,
    X86_MOVZX16rm8	= 1781,
    X86_MOVZX16rr8	= 1782,
    X86_MOVZX32_NOREXrm8	= 1783,
    X86_MOVZX32_NOREXrr8	= 1784,
    X86_MOVZX32rm16	= 1785,
    X86_MOVZX32rm8	= 1786,
    X86_MOVZX32rr16	= 1787,
    X86_MOVZX32rr8	= 1788,
    X86_MOVZX64rm16_Q	= 1789,
    X86_MOVZX64rm8_Q	= 1790,
    X86_MOVZX64rr16_Q	= 1791,
    X86_MOVZX64rr8_Q	= 1792,
    X86_MPSADBWrmi	= 1793,
    X86_MPSADBWrri	= 1794,
    X86_MUL16m	= 1795,
    X86_MUL16r	= 1796,
    X86_MUL32m	= 1797,
    X86_MUL32r	= 1798,
    X86_MUL64m	= 1799,
    X86_MUL64r	= 1800,
    X86_MUL8m	= 1801,
    X86_MUL8r	= 1802,
    X86_MULPDrm	= 1803,
    X86_MULPDrr	= 1804,
    X86_MULPSrm	= 1805,
    X86_MULPSrr	= 1806,
    X86_MULSDrm	= 1807,
    X86_MULSDrm_Int	= 1808,
    X86_MULSDrr	= 1809,
    X86_MULSDrr_Int	= 1810,
    X86_MULSSrm	= 1811,
    X86_MULSSrm_Int	= 1812,
    X86_MULSSrr	= 1813,
    X86_MULSSrr_Int	= 1814,
    X86_MULX32rm	= 1815,
    X86_MULX32rr	= 1816,
    X86_MULX64rm	= 1817,
    X86_MULX64rr	= 1818,
    X86_MUL_F32m	= 1819,
    X86_MUL_F64m	= 1820,
    X86_MUL_FI16m	= 1821,
    X86_MUL_FI32m	= 1822,
    X86_MUL_FPrST0	= 1823,
    X86_MUL_FST0r	= 1824,
    X86_MUL_Fp32	= 1825,
    X86_MUL_Fp32m	= 1826,
    X86_MUL_Fp64	= 1827,
    X86_MUL_Fp64m	= 1828,
    X86_MUL_Fp64m32	= 1829,
    X86_MUL_Fp80	= 1830,
    X86_MUL_Fp80m32	= 1831,
    X86_MUL_Fp80m64	= 1832,
    X86_MUL_FpI16m32	= 1833,
    X86_MUL_FpI16m64	= 1834,
    X86_MUL_FpI16m80	= 1835,
    X86_MUL_FpI32m32	= 1836,
    X86_MUL_FpI32m64	= 1837,
    X86_MUL_FpI32m80	= 1838,
    X86_MUL_FrST0	= 1839,
    X86_MWAITrr	= 1840,
    X86_NEG16m	= 1841,
    X86_NEG16r	= 1842,
    X86_NEG32m	= 1843,
    X86_NEG32r	= 1844,
    X86_NEG64m	= 1845,
    X86_NEG64r	= 1846,
    X86_NEG8m	= 1847,
    X86_NEG8r	= 1848,
    X86_NOOP	= 1849,
    X86_NOOP18_16m4	= 1850,
    X86_NOOP18_16m5	= 1851,
    X86_NOOP18_16m6	= 1852,
    X86_NOOP18_16m7	= 1853,
    X86_NOOP18_16r4	= 1854,
    X86_NOOP18_16r5	= 1855,
    X86_NOOP18_16r6	= 1856,
    X86_NOOP18_16r7	= 1857,
    X86_NOOP18_m4	= 1858,
    X86_NOOP18_m5	= 1859,
    X86_NOOP18_m6	= 1860,
    X86_NOOP18_m7	= 1861,
    X86_NOOP18_r4	= 1862,
    X86_NOOP18_r5	= 1863,
    X86_NOOP18_r6	= 1864,
    X86_NOOP18_r7	= 1865,
    X86_NOOP19rr	= 1866,
    X86_NOOPL	= 1867,
    X86_NOOPL_19	= 1868,
    X86_NOOPL_1a	= 1869,
    X86_NOOPL_1b	= 1870,
    X86_NOOPL_1c	= 1871,
    X86_NOOPL_1d	= 1872,
    X86_NOOPL_1e	= 1873,
    X86_NOOPW	= 1874,
    X86_NOOPW_19	= 1875,
    X86_NOOPW_1a	= 1876,
    X86_NOOPW_1b	= 1877,
    X86_NOOPW_1c	= 1878,
    X86_NOOPW_1d	= 1879,
    X86_NOOPW_1e	= 1880,
    X86_NOT16m	= 1881,
    X86_NOT16r	= 1882,
    X86_NOT32m	= 1883,
    X86_NOT32r	= 1884,
    X86_NOT64m	= 1885,
    X86_NOT64r	= 1886,
    X86_NOT8m	= 1887,
    X86_NOT8r	= 1888,
    X86_OR16i16	= 1889,
    X86_OR16mi	= 1890,
    X86_OR16mi8	= 1891,
    X86_OR16mr	= 1892,
    X86_OR16ri	= 1893,
    X86_OR16ri8	= 1894,
    X86_OR16rm	= 1895,
    X86_OR16rr	= 1896,
    X86_OR16rr_REV	= 1897,
    X86_OR32i32	= 1898,
    X86_OR32mi	= 1899,
    X86_OR32mi8	= 1900,
    X86_OR32mr	= 1901,
    X86_OR32mrLocked	= 1902,
    X86_OR32ri	= 1903,
    X86_OR32ri8	= 1904,
    X86_OR32rm	= 1905,
    X86_OR32rr	= 1906,
    X86_OR32rr_REV	= 1907,
    X86_OR64i32	= 1908,
    X86_OR64mi32	= 1909,
    X86_OR64mi8	= 1910,
    X86_OR64mr	= 1911,
    X86_OR64ri32	= 1912,
    X86_OR64ri8	= 1913,
    X86_OR64rm	= 1914,
    X86_OR64rr	= 1915,
    X86_OR64rr_REV	= 1916,
    X86_OR8i8	= 1917,
    X86_OR8mi	= 1918,
    X86_OR8mi8	= 1919,
    X86_OR8mr	= 1920,
    X86_OR8ri	= 1921,
    X86_OR8ri8	= 1922,
    X86_OR8rm	= 1923,
    X86_OR8rr	= 1924,
    X86_OR8rr_REV	= 1925,
    X86_ORPDrm	= 1926,
    X86_ORPDrr	= 1927,
    X86_ORPSrm	= 1928,
    X86_ORPSrr	= 1929,
    X86_OUT16ir	= 1930,
    X86_OUT16rr	= 1931,
    X86_OUT32ir	= 1932,
    X86_OUT32rr	= 1933,
    X86_OUT8ir	= 1934,
    X86_OUT8rr	= 1935,
    X86_OUTSB	= 1936,
    X86_OUTSL	= 1937,
    X86_OUTSW	= 1938,
    X86_PABSBrm128	= 1939,
    X86_PABSBrr128	= 1940,
    X86_PABSDrm128	= 1941,
    X86_PABSDrr128	= 1942,
    X86_PABSWrm128	= 1943,
    X86_PABSWrr128	= 1944,
    X86_PACKSSDWrm	= 1945,
    X86_PACKSSDWrr	= 1946,
    X86_PACKSSWBrm	= 1947,
    X86_PACKSSWBrr	= 1948,
    X86_PACKUSDWrm	= 1949,
    X86_PACKUSDWrr	= 1950,
    X86_PACKUSWBrm	= 1951,
    X86_PACKUSWBrr	= 1952,
    X86_PADDBrm	= 1953,
    X86_PADDBrr	= 1954,
    X86_PADDDrm	= 1955,
    X86_PADDDrr	= 1956,
    X86_PADDQrm	= 1957,
    X86_PADDQrr	= 1958,
    X86_PADDSBrm	= 1959,
    X86_PADDSBrr	= 1960,
    X86_PADDSWrm	= 1961,
    X86_PADDSWrr	= 1962,
    X86_PADDUSBrm	= 1963,
    X86_PADDUSBrr	= 1964,
    X86_PADDUSWrm	= 1965,
    X86_PADDUSWrr	= 1966,
    X86_PADDWrm	= 1967,
    X86_PADDWrr	= 1968,
    X86_PALIGNR128rm	= 1969,
    X86_PALIGNR128rr	= 1970,
    X86_PANDNrm	= 1971,
    X86_PANDNrr	= 1972,
    X86_PANDrm	= 1973,
    X86_PANDrr	= 1974,
    X86_PAUSE	= 1975,
    X86_PAVGBrm	= 1976,
    X86_PAVGBrr	= 1977,
    X86_PAVGUSBrm	= 1978,
    X86_PAVGUSBrr	= 1979,
    X86_PAVGWrm	= 1980,
    X86_PAVGWrr	= 1981,
    X86_PBLENDVBrm0	= 1982,
    X86_PBLENDVBrr0	= 1983,
    X86_PBLENDWrmi	= 1984,
    X86_PBLENDWrri	= 1985,
    X86_PCLMULQDQrm	= 1986,
    X86_PCLMULQDQrr	= 1987,
    X86_PCMPEQBrm	= 1988,
    X86_PCMPEQBrr	= 1989,
    X86_PCMPEQDrm	= 1990,
    X86_PCMPEQDrr	= 1991,
    X86_PCMPEQQrm	= 1992,
    X86_PCMPEQQrr	= 1993,
    X86_PCMPEQWrm	= 1994,
    X86_PCMPEQWrr	= 1995,
    X86_PCMPESTRIMEM	= 1996,
    X86_PCMPESTRIREG	= 1997,
    X86_PCMPESTRIrm	= 1998,
    X86_PCMPESTRIrr	= 1999,
    X86_PCMPESTRM128MEM	= 2000,
    X86_PCMPESTRM128REG	= 2001,
    X86_PCMPESTRM128rm	= 2002,
    X86_PCMPESTRM128rr	= 2003,
    X86_PCMPGTBrm	= 2004,
    X86_PCMPGTBrr	= 2005,
    X86_PCMPGTDrm	= 2006,
    X86_PCMPGTDrr	= 2007,
    X86_PCMPGTQrm	= 2008,
    X86_PCMPGTQrr	= 2009,
    X86_PCMPGTWrm	= 2010,
    X86_PCMPGTWrr	= 2011,
    X86_PCMPISTRIMEM	= 2012,
    X86_PCMPISTRIREG	= 2013,
    X86_PCMPISTRIrm	= 2014,
    X86_PCMPISTRIrr	= 2015,
    X86_PCMPISTRM128MEM	= 2016,
    X86_PCMPISTRM128REG	= 2017,
    X86_PCMPISTRM128rm	= 2018,
    X86_PCMPISTRM128rr	= 2019,
    X86_PCOMMIT	= 2020,
    X86_PDEP32rm	= 2021,
    X86_PDEP32rr	= 2022,
    X86_PDEP64rm	= 2023,
    X86_PDEP64rr	= 2024,
    X86_PEXT32rm	= 2025,
    X86_PEXT32rr	= 2026,
    X86_PEXT64rm	= 2027,
    X86_PEXT64rr	= 2028,
    X86_PEXTRBmr	= 2029,
    X86_PEXTRBrr	= 2030,
    X86_PEXTRDmr	= 2031,
    X86_PEXTRDrr	= 2032,
    X86_PEXTRQmr	= 2033,
    X86_PEXTRQrr	= 2034,
    X86_PEXTRWmr	= 2035,
    X86_PEXTRWri	= 2036,
    X86_PEXTRWrr_REV	= 2037,
    X86_PF2IDrm	= 2038,
    X86_PF2IDrr	= 2039,
    X86_PF2IWrm	= 2040,
    X86_PF2IWrr	= 2041,
    X86_PFACCrm	= 2042,
    X86_PFACCrr	= 2043,
    X86_PFADDrm	= 2044,
    X86_PFADDrr	= 2045,
    X86_PFCMPEQrm	= 2046,
    X86_PFCMPEQrr	= 2047,
    X86_PFCMPGErm	= 2048,
    X86_PFCMPGErr	= 2049,
    X86_PFCMPGTrm	= 2050,
    X86_PFCMPGTrr	= 2051,
    X86_PFMAXrm	= 2052,
    X86_PFMAXrr	= 2053,
    X86_PFMINrm	= 2054,
    X86_PFMINrr	= 2055,
    X86_PFMULrm	= 2056,
    X86_PFMULrr	= 2057,
    X86_PFNACCrm	= 2058,
    X86_PFNACCrr	= 2059,
    X86_PFPNACCrm	= 2060,
    X86_PFPNACCrr	= 2061,
    X86_PFRCPIT1rm	= 2062,
    X86_PFRCPIT1rr	= 2063,
    X86_PFRCPIT2rm	= 2064,
    X86_PFRCPIT2rr	= 2065,
    X86_PFRCPrm	= 2066,
    X86_PFRCPrr	= 2067,
    X86_PFRSQIT1rm	= 2068,
    X86_PFRSQIT1rr	= 2069,
    X86_PFRSQRTrm	= 2070,
    X86_PFRSQRTrr	= 2071,
    X86_PFSUBRrm	= 2072,
    X86_PFSUBRrr	= 2073,
    X86_PFSUBrm	= 2074,
    X86_PFSUBrr	= 2075,
    X86_PHADDDrm	= 2076,
    X86_PHADDDrr	= 2077,
    X86_PHADDSWrm128	= 2078,
    X86_PHADDSWrr128	= 2079,
    X86_PHADDWrm	= 2080,
    X86_PHADDWrr	= 2081,
    X86_PHMINPOSUWrm128	= 2082,
    X86_PHMINPOSUWrr128	= 2083,
    X86_PHSUBDrm	= 2084,
    X86_PHSUBDrr	= 2085,
    X86_PHSUBSWrm128	= 2086,
    X86_PHSUBSWrr128	= 2087,
    X86_PHSUBWrm	= 2088,
    X86_PHSUBWrr	= 2089,
    X86_PI2FDrm	= 2090,
    X86_PI2FDrr	= 2091,
    X86_PI2FWrm	= 2092,
    X86_PI2FWrr	= 2093,
    X86_PINSRBrm	= 2094,
    X86_PINSRBrr	= 2095,
    X86_PINSRDrm	= 2096,
    X86_PINSRDrr	= 2097,
    X86_PINSRQrm	= 2098,
    X86_PINSRQrr	= 2099,
    X86_PINSRWrmi	= 2100,
    X86_PINSRWrri	= 2101,
    X86_PMADDUBSWrm128	= 2102,
    X86_PMADDUBSWrr128	= 2103,
    X86_PMADDWDrm	= 2104,
    X86_PMADDWDrr	= 2105,
    X86_PMAXSBrm	= 2106,
    X86_PMAXSBrr	= 2107,
    X86_PMAXSDrm	= 2108,
    X86_PMAXSDrr	= 2109,
    X86_PMAXSWrm	= 2110,
    X86_PMAXSWrr	= 2111,
    X86_PMAXUBrm	= 2112,
    X86_PMAXUBrr	= 2113,
    X86_PMAXUDrm	= 2114,
    X86_PMAXUDrr	= 2115,
    X86_PMAXUWrm	= 2116,
    X86_PMAXUWrr	= 2117,
    X86_PMINSBrm	= 2118,
    X86_PMINSBrr	= 2119,
    X86_PMINSDrm	= 2120,
    X86_PMINSDrr	= 2121,
    X86_PMINSWrm	= 2122,
    X86_PMINSWrr	= 2123,
    X86_PMINUBrm	= 2124,
    X86_PMINUBrr	= 2125,
    X86_PMINUDrm	= 2126,
    X86_PMINUDrr	= 2127,
    X86_PMINUWrm	= 2128,
    X86_PMINUWrr	= 2129,
    X86_PMOVMSKBrr	= 2130,
    X86_PMOVSXBDrm	= 2131,
    X86_PMOVSXBDrr	= 2132,
    X86_PMOVSXBQrm	= 2133,
    X86_PMOVSXBQrr	= 2134,
    X86_PMOVSXBWrm	= 2135,
    X86_PMOVSXBWrr	= 2136,
    X86_PMOVSXDQrm	= 2137,
    X86_PMOVSXDQrr	= 2138,
    X86_PMOVSXWDrm	= 2139,
    X86_PMOVSXWDrr	= 2140,
    X86_PMOVSXWQrm	= 2141,
    X86_PMOVSXWQrr	= 2142,
    X86_PMOVZXBDrm	= 2143,
    X86_PMOVZXBDrr	= 2144,
    X86_PMOVZXBQrm	= 2145,
    X86_PMOVZXBQrr	= 2146,
    X86_PMOVZXBWrm	= 2147,
    X86_PMOVZXBWrr	= 2148,
    X86_PMOVZXDQrm	= 2149,
    X86_PMOVZXDQrr	= 2150,
    X86_PMOVZXWDrm	= 2151,
    X86_PMOVZXWDrr	= 2152,
    X86_PMOVZXWQrm	= 2153,
    X86_PMOVZXWQrr	= 2154,
    X86_PMULDQrm	= 2155,
    X86_PMULDQrr	= 2156,
    X86_PMULHRSWrm128	= 2157,
    X86_PMULHRSWrr128	= 2158,
    X86_PMULHRWrm	= 2159,
    X86_PMULHRWrr	= 2160,
    X86_PMULHUWrm	= 2161,
    X86_PMULHUWrr	= 2162,
    X86_PMULHWrm	= 2163,
    X86_PMULHWrr	= 2164,
    X86_PMULLDrm	= 2165,
    X86_PMULLDrr	= 2166,
    X86_PMULLWrm	= 2167,
    X86_PMULLWrr	= 2168,
    X86_PMULUDQrm	= 2169,
    X86_PMULUDQrr	= 2170,
    X86_POP16r	= 2171,
    X86_POP16rmm	= 2172,
    X86_POP16rmr	= 2173,
    X86_POP32r	= 2174,
    X86_POP32rmm	= 2175,
    X86_POP32rmr	= 2176,
    X86_POP64r	= 2177,
    X86_POP64rmm	= 2178,
    X86_POP64rmr	= 2179,
    X86_POPA16	= 2180,
    X86_POPA32	= 2181,
    X86_POPCNT16rm	= 2182,
    X86_POPCNT16rr	= 2183,
    X86_POPCNT32rm	= 2184,
    X86_POPCNT32rr	= 2185,
    X86_POPCNT64rm	= 2186,
    X86_POPCNT64rr	= 2187,
    X86_POPDS16	= 2188,
    X86_POPDS32	= 2189,
    X86_POPES16	= 2190,
    X86_POPES32	= 2191,
    X86_POPF16	= 2192,
    X86_POPF32	= 2193,
    X86_POPF64	= 2194,
    X86_POPFS16	= 2195,
    X86_POPFS32	= 2196,
    X86_POPFS64	= 2197,
    X86_POPGS16	= 2198,
    X86_POPGS32	= 2199,
    X86_POPGS64	= 2200,
    X86_POPSS16	= 2201,
    X86_POPSS32	= 2202,
    X86_PORrm	= 2203,
    X86_PORrr	= 2204,
    X86_PREFETCH	= 2205,
    X86_PREFETCHNTA	= 2206,
    X86_PREFETCHT0	= 2207,
    X86_PREFETCHT1	= 2208,
    X86_PREFETCHT2	= 2209,
    X86_PREFETCHW	= 2210,
    X86_PSADBWrm	= 2211,
    X86_PSADBWrr	= 2212,
    X86_PSHUFBrm	= 2213,
    X86_PSHUFBrr	= 2214,
    X86_PSHUFDmi	= 2215,
    X86_PSHUFDri	= 2216,
    X86_PSHUFHWmi	= 2217,
    X86_PSHUFHWri	= 2218,
    X86_PSHUFLWmi	= 2219,
    X86_PSHUFLWri	= 2220,
    X86_PSIGNBrm	= 2221,
    X86_PSIGNBrr	= 2222,
    X86_PSIGNDrm	= 2223,
    X86_PSIGNDrr	= 2224,
    X86_PSIGNWrm	= 2225,
    X86_PSIGNWrr	= 2226,
    X86_PSLLDQri	= 2227,
    X86_PSLLDri	= 2228,
    X86_PSLLDrm	= 2229,
    X86_PSLLDrr	= 2230,
    X86_PSLLQri	= 2231,
    X86_PSLLQrm	= 2232,
    X86_PSLLQrr	= 2233,
    X86_PSLLWri	= 2234,
    X86_PSLLWrm	= 2235,
    X86_PSLLWrr	= 2236,
    X86_PSRADri	= 2237,
    X86_PSRADrm	= 2238,
    X86_PSRADrr	= 2239,
    X86_PSRAWri	= 2240,
    X86_PSRAWrm	= 2241,
    X86_PSRAWrr	= 2242,
    X86_PSRLDQri	= 2243,
    X86_PSRLDri	= 2244,
    X86_PSRLDrm	= 2245,
    X86_PSRLDrr	= 2246,
    X86_PSRLQri	= 2247,
    X86_PSRLQrm	= 2248,
    X86_PSRLQrr	= 2249,
    X86_PSRLWri	= 2250,
    X86_PSRLWrm	= 2251,
    X86_PSRLWrr	= 2252,
    X86_PSUBBrm	= 2253,
    X86_PSUBBrr	= 2254,
    X86_PSUBDrm	= 2255,
    X86_PSUBDrr	= 2256,
    X86_PSUBQrm	= 2257,
    X86_PSUBQrr	= 2258,
    X86_PSUBSBrm	= 2259,
    X86_PSUBSBrr	= 2260,
    X86_PSUBSWrm	= 2261,
    X86_PSUBSWrr	= 2262,
    X86_PSUBUSBrm	= 2263,
    X86_PSUBUSBrr	= 2264,
    X86_PSUBUSWrm	= 2265,
    X86_PSUBUSWrr	= 2266,
    X86_PSUBWrm	= 2267,
    X86_PSUBWrr	= 2268,
    X86_PSWAPDrm	= 2269,
    X86_PSWAPDrr	= 2270,
    X86_PTESTrm	= 2271,
    X86_PTESTrr	= 2272,
    X86_PUNPCKHBWrm	= 2273,
    X86_PUNPCKHBWrr	= 2274,
    X86_PUNPCKHDQrm	= 2275,
    X86_PUNPCKHDQrr	= 2276,
    X86_PUNPCKHQDQrm	= 2277,
    X86_PUNPCKHQDQrr	= 2278,
    X86_PUNPCKHWDrm	= 2279,
    X86_PUNPCKHWDrr	= 2280,
    X86_PUNPCKLBWrm	= 2281,
    X86_PUNPCKLBWrr	= 2282,
    X86_PUNPCKLDQrm	= 2283,
    X86_PUNPCKLDQrr	= 2284,
    X86_PUNPCKLQDQrm	= 2285,
    X86_PUNPCKLQDQrr	= 2286,
    X86_PUNPCKLWDrm	= 2287,
    X86_PUNPCKLWDrr	= 2288,
    X86_PUSH16i8	= 2289,
    X86_PUSH16r	= 2290,
    X86_PUSH16rmm	= 2291,
    X86_PUSH16rmr	= 2292,
    X86_PUSH32i8	= 2293,
    X86_PUSH32r	= 2294,
    X86_PUSH32rmm	= 2295,
    X86_PUSH32rmr	= 2296,
    X86_PUSH64i16	= 2297,
    X86_PUSH64i32	= 2298,
    X86_PUSH64i8	= 2299,
    X86_PUSH64r	= 2300,
    X86_PUSH64rmm	= 2301,
    X86_PUSH64rmr	= 2302,
    X86_PUSHA16	= 2303,
    X86_PUSHA32	= 2304,
    X86_PUSHCS16	= 2305,
    X86_PUSHCS32	= 2306,
    X86_PUSHDS16	= 2307,
    X86_PUSHDS32	= 2308,
    X86_PUSHES16	= 2309,
    X86_PUSHES32	= 2310,
    X86_PUSHF16	= 2311,
    X86_PUSHF32	= 2312,
    X86_PUSHF64	= 2313,
    X86_PUSHFS16	= 2314,
    X86_PUSHFS32	= 2315,
    X86_PUSHFS64	= 2316,
    X86_PUSHGS16	= 2317,
    X86_PUSHGS32	= 2318,
    X86_PUSHGS64	= 2319,
    X86_PUSHSS16	= 2320,
    X86_PUSHSS32	= 2321,
    X86_PUSHi16	= 2322,
    X86_PUSHi32	= 2323,
    X86_PXORrm	= 2324,
    X86_PXORrr	= 2325,
    X86_RCL16m1	= 2326,
    X86_RCL16mCL	= 2327,
    X86_RCL16mi	= 2328,
    X86_RCL16r1	= 2329,
    X86_RCL16rCL	= 2330,
    X86_RCL16ri	= 2331,
    X86_RCL32m1	= 2332,
    X86_RCL32mCL	= 2333,
    X86_RCL32mi	= 2334,
    X86_RCL32r1	= 2335,
    X86_RCL32rCL	= 2336,
    X86_RCL32ri	= 2337,
    X86_RCL64m1	= 2338,
    X86_RCL64mCL	= 2339,
    X86_RCL64mi	= 2340,
    X86_RCL64r1	= 2341,
    X86_RCL64rCL	= 2342,
    X86_RCL64ri	= 2343,
    X86_RCL8m1	= 2344,
    X86_RCL8mCL	= 2345,
    X86_RCL8mi	= 2346,
    X86_RCL8r1	= 2347,
    X86_RCL8rCL	= 2348,
    X86_RCL8ri	= 2349,
    X86_RCPPSm	= 2350,
    X86_RCPPSm_Int	= 2351,
    X86_RCPPSr	= 2352,
    X86_RCPPSr_Int	= 2353,
    X86_RCPSSm	= 2354,
    X86_RCPSSm_Int	= 2355,
    X86_RCPSSr	= 2356,
    X86_RCPSSr_Int	= 2357,
    X86_RCR16m1	= 2358,
    X86_RCR16mCL	= 2359,
    X86_RCR16mi	= 2360,
    X86_RCR16r1	= 2361,
    X86_RCR16rCL	= 2362,
    X86_RCR16ri	= 2363,
    X86_RCR32m1	= 2364,
    X86_RCR32mCL	= 2365,
    X86_RCR32mi	= 2366,
    X86_RCR32r1	= 2367,
    X86_RCR32rCL	= 2368,
    X86_RCR32ri	= 2369,
    X86_RCR64m1	= 2370,
    X86_RCR64mCL	= 2371,
    X86_RCR64mi	= 2372,
    X86_RCR64r1	= 2373,
    X86_RCR64rCL	= 2374,
    X86_RCR64ri	= 2375,
    X86_RCR8m1	= 2376,
    X86_RCR8mCL	= 2377,
    X86_RCR8mi	= 2378,
    X86_RCR8r1	= 2379,
    X86_RCR8rCL	= 2380,
    X86_RCR8ri	= 2381,
    X86_RDFSBASE	= 2382,
    X86_RDFSBASE64	= 2383,
    X86_RDGSBASE	= 2384,
    X86_RDGSBASE64	= 2385,
    X86_RDMSR	= 2386,
    X86_RDPMC	= 2387,
    X86_RDRAND16r	= 2388,
    X86_RDRAND32r	= 2389,
    X86_RDRAND64r	= 2390,
    X86_RDSEED16r	= 2391,
    X86_RDSEED32r	= 2392,
    X86_RDSEED64r	= 2393,
    X86_RDTSC	= 2394,
    X86_RDTSCP	= 2395,
    X86_RELEASE_ADD32mi	= 2396,
    X86_RELEASE_ADD64mi32	= 2397,
    X86_RELEASE_ADD8mi	= 2398,
    X86_RELEASE_AND32mi	= 2399,
    X86_RELEASE_AND64mi32	= 2400,
    X86_RELEASE_AND8mi	= 2401,
    X86_RELEASE_DEC16m	= 2402,
    X86_RELEASE_DEC32m	= 2403,
    X86_RELEASE_DEC64m	= 2404,
    X86_RELEASE_DEC8m	= 2405,
    X86_RELEASE_INC16m	= 2406,
    X86_RELEASE_INC32m	= 2407,
    X86_RELEASE_INC64m	= 2408,
    X86_RELEASE_INC8m	= 2409,
    X86_RELEASE_MOV16mi	= 2410,
    X86_RELEASE_MOV16mr	= 2411,
    X86_RELEASE_MOV32mi	= 2412,
    X86_RELEASE_MOV32mr	= 2413,
    X86_RELEASE_MOV64mi32	= 2414,
    X86_RELEASE_MOV64mr	= 2415,
    X86_RELEASE_MOV8mi	= 2416,
    X86_RELEASE_MOV8mr	= 2417,
    X86_RELEASE_OR32mi	= 2418,
    X86_RELEASE_OR64mi32	= 2419,
    X86_RELEASE_OR8mi	= 2420,
    X86_RELEASE_XOR32mi	= 2421,
    X86_RELEASE_XOR64mi32	= 2422,
    X86_RELEASE_XOR8mi	= 2423,
    X86_REPNE_PREFIX	= 2424,
    X86_REP_MOVSB_32	= 2425,
    X86_REP_MOVSB_64	= 2426,
    X86_REP_MOVSD_32	= 2427,
    X86_REP_MOVSD_64	= 2428,
    X86_REP_MOVSQ_64	= 2429,
    X86_REP_MOVSW_32	= 2430,
    X86_REP_MOVSW_64	= 2431,
    X86_REP_PREFIX	= 2432,
    X86_REP_STOSB_32	= 2433,
    X86_REP_STOSB_64	= 2434,
    X86_REP_STOSD_32	= 2435,
    X86_REP_STOSD_64	= 2436,
    X86_REP_STOSQ_64	= 2437,
    X86_REP_STOSW_32	= 2438,
    X86_REP_STOSW_64	= 2439,
    X86_RETIL	= 2440,
    X86_RETIQ	= 2441,
    X86_RETIW	= 2442,
    X86_RETL	= 2443,
    X86_RETQ	= 2444,
    X86_RETW	= 2445,
    X86_REX64_PREFIX	= 2446,
    X86_ROL16m1	= 2447,
    X86_ROL16mCL	= 2448,
    X86_ROL16mi	= 2449,
    X86_ROL16r1	= 2450,
    X86_ROL16rCL	= 2451,
    X86_ROL16ri	= 2452,
    X86_ROL32m1	= 2453,
    X86_ROL32mCL	= 2454,
    X86_ROL32mi	= 2455,
    X86_ROL32r1	= 2456,
    X86_ROL32rCL	= 2457,
    X86_ROL32ri	= 2458,
    X86_ROL64m1	= 2459,
    X86_ROL64mCL	= 2460,
    X86_ROL64mi	= 2461,
    X86_ROL64r1	= 2462,
    X86_ROL64rCL	= 2463,
    X86_ROL64ri	= 2464,
    X86_ROL8m1	= 2465,
    X86_ROL8mCL	= 2466,
    X86_ROL8mi	= 2467,
    X86_ROL8r1	= 2468,
    X86_ROL8rCL	= 2469,
    X86_ROL8ri	= 2470,
    X86_ROR16m1	= 2471,
    X86_ROR16mCL	= 2472,
    X86_ROR16mi	= 2473,
    X86_ROR16r1	= 2474,
    X86_ROR16rCL	= 2475,
    X86_ROR16ri	= 2476,
    X86_ROR32m1	= 2477,
    X86_ROR32mCL	= 2478,
    X86_ROR32mi	= 2479,
    X86_ROR32r1	= 2480,
    X86_ROR32rCL	= 2481,
    X86_ROR32ri	= 2482,
    X86_ROR64m1	= 2483,
    X86_ROR64mCL	= 2484,
    X86_ROR64mi	= 2485,
    X86_ROR64r1	= 2486,
    X86_ROR64rCL	= 2487,
    X86_ROR64ri	= 2488,
    X86_ROR8m1	= 2489,
    X86_ROR8mCL	= 2490,
    X86_ROR8mi	= 2491,
    X86_ROR8r1	= 2492,
    X86_ROR8rCL	= 2493,
    X86_ROR8ri	= 2494,
    X86_RORX32mi	= 2495,
    X86_RORX32ri	= 2496,
    X86_RORX64mi	= 2497,
    X86_RORX64ri	= 2498,
    X86_ROUNDPDm	= 2499,
    X86_ROUNDPDr	= 2500,
    X86_ROUNDPSm	= 2501,
    X86_ROUNDPSr	= 2502,
    X86_ROUNDSDm	= 2503,
    X86_ROUNDSDr	= 2504,
    X86_ROUNDSDr_Int	= 2505,
    X86_ROUNDSSm	= 2506,
    X86_ROUNDSSr	= 2507,
    X86_ROUNDSSr_Int	= 2508,
    X86_RSM	= 2509,
    X86_RSQRTPSm	= 2510,
    X86_RSQRTPSm_Int	= 2511,
    X86_RSQRTPSr	= 2512,
    X86_RSQRTPSr_Int	= 2513,
    X86_RSQRTSSm	= 2514,
    X86_RSQRTSSm_Int	= 2515,
    X86_RSQRTSSr	= 2516,
    X86_RSQRTSSr_Int	= 2517,
    X86_SAHF	= 2518,
    X86_SAL16m1	= 2519,
    X86_SAL16mCL	= 2520,
    X86_SAL16mi	= 2521,
    X86_SAL16r1	= 2522,
    X86_SAL16rCL	= 2523,
    X86_SAL16ri	= 2524,
    X86_SAL32m1	= 2525,
    X86_SAL32mCL	= 2526,
    X86_SAL32mi	= 2527,
    X86_SAL32r1	= 2528,
    X86_SAL32rCL	= 2529,
    X86_SAL32ri	= 2530,
    X86_SAL64m1	= 2531,
    X86_SAL64mCL	= 2532,
    X86_SAL64mi	= 2533,
    X86_SAL64r1	= 2534,
    X86_SAL64rCL	= 2535,
    X86_SAL64ri	= 2536,
    X86_SAL8m1	= 2537,
    X86_SAL8mCL	= 2538,
    X86_SAL8mi	= 2539,
    X86_SAL8r1	= 2540,
    X86_SAL8rCL	= 2541,
    X86_SAL8ri	= 2542,
    X86_SALC	= 2543,
    X86_SAR16m1	= 2544,
    X86_SAR16mCL	= 2545,
    X86_SAR16mi	= 2546,
    X86_SAR16r1	= 2547,
    X86_SAR16rCL	= 2548,
    X86_SAR16ri	= 2549,
    X86_SAR32m1	= 2550,
    X86_SAR32mCL	= 2551,
    X86_SAR32mi	= 2552,
    X86_SAR32r1	= 2553,
    X86_SAR32rCL	= 2554,
    X86_SAR32ri	= 2555,
    X86_SAR64m1	= 2556,
    X86_SAR64mCL	= 2557,
    X86_SAR64mi	= 2558,
    X86_SAR64r1	= 2559,
    X86_SAR64rCL	= 2560,
    X86_SAR64ri	= 2561,
    X86_SAR8m1	= 2562,
    X86_SAR8mCL	= 2563,
    X86_SAR8mi	= 2564,
    X86_SAR8r1	= 2565,
    X86_SAR8rCL	= 2566,
    X86_SAR8ri	= 2567,
    X86_SARX32rm	= 2568,
    X86_SARX32rr	= 2569,
    X86_SARX64rm	= 2570,
    X86_SARX64rr	= 2571,
    X86_SBB16i16	= 2572,
    X86_SBB16mi	= 2573,
    X86_SBB16mi8	= 2574,
    X86_SBB16mr	= 2575,
    X86_SBB16ri	= 2576,
    X86_SBB16ri8	= 2577,
    X86_SBB16rm	= 2578,
    X86_SBB16rr	= 2579,
    X86_SBB16rr_REV	= 2580,
    X86_SBB32i32	= 2581,
    X86_SBB32mi	= 2582,
    X86_SBB32mi8	= 2583,
    X86_SBB32mr	= 2584,
    X86_SBB32ri	= 2585,
    X86_SBB32ri8	= 2586,
    X86_SBB32rm	= 2587,
    X86_SBB32rr	= 2588,
    X86_SBB32rr_REV	= 2589,
    X86_SBB64i32	= 2590,
    X86_SBB64mi32	= 2591,
    X86_SBB64mi8	= 2592,
    X86_SBB64mr	= 2593,
    X86_SBB64ri32	= 2594,
    X86_SBB64ri8	= 2595,
    X86_SBB64rm	= 2596,
    X86_SBB64rr	= 2597,
    X86_SBB64rr_REV	= 2598,
    X86_SBB8i8	= 2599,
    X86_SBB8mi	= 2600,
    X86_SBB8mi8	= 2601,
    X86_SBB8mr	= 2602,
    X86_SBB8ri	= 2603,
    X86_SBB8ri8	= 2604,
    X86_SBB8rm	= 2605,
    X86_SBB8rr	= 2606,
    X86_SBB8rr_REV	= 2607,
    X86_SCASB	= 2608,
    X86_SCASL	= 2609,
    X86_SCASQ	= 2610,
    X86_SCASW	= 2611,
    X86_SEG_ALLOCA_32	= 2612,
    X86_SEG_ALLOCA_64	= 2613,
    X86_SEH_EndPrologue	= 2614,
    X86_SEH_Epilogue	= 2615,
    X86_SEH_PushFrame	= 2616,
    X86_SEH_PushReg	= 2617,
    X86_SEH_SaveReg	= 2618,
    X86_SEH_SaveXMM	= 2619,
    X86_SEH_SetFrame	= 2620,
    X86_SEH_StackAlloc	= 2621,
    X86_SETAEm	= 2622,
    X86_SETAEr	= 2623,
    X86_SETAm	= 2624,
    X86_SETAr	= 2625,
    X86_SETBEm	= 2626,
    X86_SETBEr	= 2627,
    X86_SETB_C16r	= 2628,
    X86_SETB_C32r	= 2629,
    X86_SETB_C64r	= 2630,
    X86_SETB_C8r	= 2631,
    X86_SETBm	= 2632,
    X86_SETBr	= 2633,
    X86_SETEm	= 2634,
    X86_SETEr	= 2635,
    X86_SETGEm	= 2636,
    X86_SETGEr	= 2637,
    X86_SETGm	= 2638,
    X86_SETGr	= 2639,
    X86_SETLEm	= 2640,
    X86_SETLEr	= 2641,
    X86_SETLm	= 2642,
    X86_SETLr	= 2643,
    X86_SETNEm	= 2644,
    X86_SETNEr	= 2645,
    X86_SETNOm	= 2646,
    X86_SETNOr	= 2647,
    X86_SETNPm	= 2648,
    X86_SETNPr	= 2649,
    X86_SETNSm	= 2650,
    X86_SETNSr	= 2651,
    X86_SETOm	= 2652,
    X86_SETOr	= 2653,
    X86_SETPm	= 2654,
    X86_SETPr	= 2655,
    X86_SETSm	= 2656,
    X86_SETSr	= 2657,
    X86_SFENCE	= 2658,
    X86_SGDT16m	= 2659,
    X86_SGDT32m	= 2660,
    X86_SGDT64m	= 2661,
    X86_SHA1MSG1rm	= 2662,
    X86_SHA1MSG1rr	= 2663,
    X86_SHA1MSG2rm	= 2664,
    X86_SHA1MSG2rr	= 2665,
    X86_SHA1NEXTErm	= 2666,
    X86_SHA1NEXTErr	= 2667,
    X86_SHA1RNDS4rmi	= 2668,
    X86_SHA1RNDS4rri	= 2669,
    X86_SHA256MSG1rm	= 2670,
    X86_SHA256MSG1rr	= 2671,
    X86_SHA256MSG2rm	= 2672,
    X86_SHA256MSG2rr	= 2673,
    X86_SHA256RNDS2rm	= 2674,
    X86_SHA256RNDS2rr	= 2675,
    X86_SHL16m1	= 2676,
    X86_SHL16mCL	= 2677,
    X86_SHL16mi	= 2678,
    X86_SHL16r1	= 2679,
    X86_SHL16rCL	= 2680,
    X86_SHL16ri	= 2681,
    X86_SHL32m1	= 2682,
    X86_SHL32mCL	= 2683,
    X86_SHL32mi	= 2684,
    X86_SHL32r1	= 2685,
    X86_SHL32rCL	= 2686,
    X86_SHL32ri	= 2687,
    X86_SHL64m1	= 2688,
    X86_SHL64mCL	= 2689,
    X86_SHL64mi	= 2690,
    X86_SHL64r1	= 2691,
    X86_SHL64rCL	= 2692,
    X86_SHL64ri	= 2693,
    X86_SHL8m1	= 2694,
    X86_SHL8mCL	= 2695,
    X86_SHL8mi	= 2696,
    X86_SHL8r1	= 2697,
    X86_SHL8rCL	= 2698,
    X86_SHL8ri	= 2699,
    X86_SHLD16mrCL	= 2700,
    X86_SHLD16mri8	= 2701,
    X86_SHLD16rrCL	= 2702,
    X86_SHLD16rri8	= 2703,
    X86_SHLD32mrCL	= 2704,
    X86_SHLD32mri8	= 2705,
    X86_SHLD32rrCL	= 2706,
    X86_SHLD32rri8	= 2707,
    X86_SHLD64mrCL	= 2708,
    X86_SHLD64mri8	= 2709,
    X86_SHLD64rrCL	= 2710,
    X86_SHLD64rri8	= 2711,
    X86_SHLX32rm	= 2712,
    X86_SHLX32rr	= 2713,
    X86_SHLX64rm	= 2714,
    X86_SHLX64rr	= 2715,
    X86_SHR16m1	= 2716,
    X86_SHR16mCL	= 2717,
    X86_SHR16mi	= 2718,
    X86_SHR16r1	= 2719,
    X86_SHR16rCL	= 2720,
    X86_SHR16ri	= 2721,
    X86_SHR32m1	= 2722,
    X86_SHR32mCL	= 2723,
    X86_SHR32mi	= 2724,
    X86_SHR32r1	= 2725,
    X86_SHR32rCL	= 2726,
    X86_SHR32ri	= 2727,
    X86_SHR64m1	= 2728,
    X86_SHR64mCL	= 2729,
    X86_SHR64mi	= 2730,
    X86_SHR64r1	= 2731,
    X86_SHR64rCL	= 2732,
    X86_SHR64ri	= 2733,
    X86_SHR8m1	= 2734,
    X86_SHR8mCL	= 2735,
    X86_SHR8mi	= 2736,
    X86_SHR8r1	= 2737,
    X86_SHR8rCL	= 2738,
    X86_SHR8ri	= 2739,
    X86_SHRD16mrCL	= 2740,
    X86_SHRD16mri8	= 2741,
    X86_SHRD16rrCL	= 2742,
    X86_SHRD16rri8	= 2743,
    X86_SHRD32mrCL	= 2744,
    X86_SHRD32mri8	= 2745,
    X86_SHRD32rrCL	= 2746,
    X86_SHRD32rri8	= 2747,
    X86_SHRD64mrCL	= 2748,
    X86_SHRD64mri8	= 2749,
    X86_SHRD64rrCL	= 2750,
    X86_SHRD64rri8	= 2751,
    X86_SHRX32rm	= 2752,
    X86_SHRX32rr	= 2753,
    X86_SHRX64rm	= 2754,
    X86_SHRX64rr	= 2755,
    X86_SHUFPDrmi	= 2756,
    X86_SHUFPDrri	= 2757,
    X86_SHUFPSrmi	= 2758,
    X86_SHUFPSrri	= 2759,
    X86_SIDT16m	= 2760,
    X86_SIDT32m	= 2761,
    X86_SIDT64m	= 2762,
    X86_SIN_F	= 2763,
    X86_SIN_Fp32	= 2764,
    X86_SIN_Fp64	= 2765,
    X86_SIN_Fp80	= 2766,
    X86_SKINIT	= 2767,
    X86_SLDT16m	= 2768,
    X86_SLDT16r	= 2769,
    X86_SLDT32r	= 2770,
    X86_SLDT64m	= 2771,
    X86_SLDT64r	= 2772,
    X86_SMSW16m	= 2773,
    X86_SMSW16r	= 2774,
    X86_SMSW32r	= 2775,
    X86_SMSW64r	= 2776,
    X86_SQRTPDm	= 2777,
    X86_SQRTPDr	= 2778,
    X86_SQRTPSm	= 2779,
    X86_SQRTPSr	= 2780,
    X86_SQRTSDm	= 2781,
    X86_SQRTSDm_Int	= 2782,
    X86_SQRTSDr	= 2783,
    X86_SQRTSDr_Int	= 2784,
    X86_SQRTSSm	= 2785,
    X86_SQRTSSm_Int	= 2786,
    X86_SQRTSSr	= 2787,
    X86_SQRTSSr_Int	= 2788,
    X86_SQRT_F	= 2789,
    X86_SQRT_Fp32	= 2790,
    X86_SQRT_Fp64	= 2791,
    X86_SQRT_Fp80	= 2792,
    X86_STAC	= 2793,
    X86_STC	= 2794,
    X86_STD	= 2795,
    X86_STGI	= 2796,
    X86_STI	= 2797,
    X86_STMXCSR	= 2798,
    X86_STOSB	= 2799,
    X86_STOSL	= 2800,
    X86_STOSQ	= 2801,
    X86_STOSW	= 2802,
    X86_STR16r	= 2803,
    X86_STR32r	= 2804,
    X86_STR64r	= 2805,
    X86_STRm	= 2806,
    X86_ST_F32m	= 2807,
    X86_ST_F64m	= 2808,
    X86_ST_FCOMPST0r	= 2809,
    X86_ST_FCOMPST0r_alt	= 2810,
    X86_ST_FCOMST0r	= 2811,
    X86_ST_FP32m	= 2812,
    X86_ST_FP64m	= 2813,
    X86_ST_FP80m	= 2814,
    X86_ST_FPNCEST0r	= 2815,
    X86_ST_FPST0r	= 2816,
    X86_ST_FPST0r_alt	= 2817,
    X86_ST_FPrr	= 2818,
    X86_ST_FXCHST0r	= 2819,
    X86_ST_FXCHST0r_alt	= 2820,
    X86_ST_Fp32m	= 2821,
    X86_ST_Fp64m	= 2822,
    X86_ST_Fp64m32	= 2823,
    X86_ST_Fp80m32	= 2824,
    X86_ST_Fp80m64	= 2825,
    X86_ST_FpP32m	= 2826,
    X86_ST_FpP64m	= 2827,
    X86_ST_FpP64m32	= 2828,
    X86_ST_FpP80m	= 2829,
    X86_ST_FpP80m32	= 2830,
    X86_ST_FpP80m64	= 2831,
    X86_ST_Frr	= 2832,
    X86_SUB16i16	= 2833,
    X86_SUB16mi	= 2834,
    X86_SUB16mi8	= 2835,
    X86_SUB16mr	= 2836,
    X86_SUB16ri	= 2837,
    X86_SUB16ri8	= 2838,
    X86_SUB16rm	= 2839,
    X86_SUB16rr	= 2840,
    X86_SUB16rr_REV	= 2841,
    X86_SUB32i32	= 2842,
    X86_SUB32mi	= 2843,
    X86_SUB32mi8	= 2844,
    X86_SUB32mr	= 2845,
    X86_SUB32ri	= 2846,
    X86_SUB32ri8	= 2847,
    X86_SUB32rm	= 2848,
    X86_SUB32rr	= 2849,
    X86_SUB32rr_REV	= 2850,
    X86_SUB64i32	= 2851,
    X86_SUB64mi32	= 2852,
    X86_SUB64mi8	= 2853,
    X86_SUB64mr	= 2854,
    X86_SUB64ri32	= 2855,
    X86_SUB64ri8	= 2856,
    X86_SUB64rm	= 2857,
    X86_SUB64rr	= 2858,
    X86_SUB64rr_REV	= 2859,
    X86_SUB8i8	= 2860,
    X86_SUB8mi	= 2861,
    X86_SUB8mi8	= 2862,
    X86_SUB8mr	= 2863,
    X86_SUB8ri	= 2864,
    X86_SUB8ri8	= 2865,
    X86_SUB8rm	= 2866,
    X86_SUB8rr	= 2867,
    X86_SUB8rr_REV	= 2868,
    X86_SUBPDrm	= 2869,
    X86_SUBPDrr	= 2870,
    X86_SUBPSrm	= 2871,
    X86_SUBPSrr	= 2872,
    X86_SUBR_F32m	= 2873,
    X86_SUBR_F64m	= 2874,
    X86_SUBR_FI16m	= 2875,
    X86_SUBR_FI32m	= 2876,
    X86_SUBR_FPrST0	= 2877,
    X86_SUBR_FST0r	= 2878,
    X86_SUBR_Fp32m	= 2879,
    X86_SUBR_Fp64m	= 2880,
    X86_SUBR_Fp64m32	= 2881,
    X86_SUBR_Fp80m32	= 2882,
    X86_SUBR_Fp80m64	= 2883,
    X86_SUBR_FpI16m32	= 2884,
    X86_SUBR_FpI16m64	= 2885,
    X86_SUBR_FpI16m80	= 2886,
    X86_SUBR_FpI32m32	= 2887,
    X86_SUBR_FpI32m64	= 2888,
    X86_SUBR_FpI32m80	= 2889,
    X86_SUBR_FrST0	= 2890,
    X86_SUBSDrm	= 2891,
    X86_SUBSDrm_Int	= 2892,
    X86_SUBSDrr	= 2893,
    X86_SUBSDrr_Int	= 2894,
    X86_SUBSSrm	= 2895,
    X86_SUBSSrm_Int	= 2896,
    X86_SUBSSrr	= 2897,
    X86_SUBSSrr_Int	= 2898,
    X86_SUB_F32m	= 2899,
    X86_SUB_F64m	= 2900,
    X86_SUB_FI16m	= 2901,
    X86_SUB_FI32m	= 2902,
    X86_SUB_FPrST0	= 2903,
    X86_SUB_FST0r	= 2904,
    X86_SUB_Fp32	= 2905,
    X86_SUB_Fp32m	= 2906,
    X86_SUB_Fp64	= 2907,
    X86_SUB_Fp64m	= 2908,
    X86_SUB_Fp64m32	= 2909,
    X86_SUB_Fp80	= 2910,
    X86_SUB_Fp80m32	= 2911,
    X86_SUB_Fp80m64	= 2912,
    X86_SUB_FpI16m32	= 2913,
    X86_SUB_FpI16m64	= 2914,
    X86_SUB_FpI16m80	= 2915,
    X86_SUB_FpI32m32	= 2916,
    X86_SUB_FpI32m64	= 2917,
    X86_SUB_FpI32m80	= 2918,
    X86_SUB_FrST0	= 2919,
    X86_SWAPGS	= 2920,
    X86_SYSCALL	= 2921,
    X86_SYSENTER	= 2922,
    X86_SYSEXIT	= 2923,
    X86_SYSEXIT64	= 2924,
    X86_SYSRET	= 2925,
    X86_SYSRET64	= 2926,
    X86_T1MSKC32rm	= 2927,
    X86_T1MSKC32rr	= 2928,
    X86_T1MSKC64rm	= 2929,
    X86_T1MSKC64rr	= 2930,
    X86_TAILJMPd	= 2931,
    X86_TAILJMPd64	= 2932,
    X86_TAILJMPd64_REX	= 2933,
    X86_TAILJMPm	= 2934,
    X86_TAILJMPm64	= 2935,
    X86_TAILJMPm64_REX	= 2936,
    X86_TAILJMPr	= 2937,
    X86_TAILJMPr64	= 2938,
    X86_TAILJMPr64_REX	= 2939,
    X86_TCRETURNdi	= 2940,
    X86_TCRETURNdi64	= 2941,
    X86_TCRETURNmi	= 2942,
    X86_TCRETURNmi64	= 2943,
    X86_TCRETURNri	= 2944,
    X86_TCRETURNri64	= 2945,
    X86_TEST16i16	= 2946,
    X86_TEST16mi	= 2947,
    X86_TEST16mi_alt	= 2948,
    X86_TEST16ri	= 2949,
    X86_TEST16ri_alt	= 2950,
    X86_TEST16rm	= 2951,
    X86_TEST16rr	= 2952,
    X86_TEST32i32	= 2953,
    X86_TEST32mi	= 2954,
    X86_TEST32mi_alt	= 2955,
    X86_TEST32ri	= 2956,
    X86_TEST32ri_alt	= 2957,
    X86_TEST32rm	= 2958,
    X86_TEST32rr	= 2959,
    X86_TEST64i32	= 2960,
    X86_TEST64mi32	= 2961,
    X86_TEST64mi32_alt	= 2962,
    X86_TEST64ri32	= 2963,
    X86_TEST64ri32_alt	= 2964,
    X86_TEST64rm	= 2965,
    X86_TEST64rr	= 2966,
    X86_TEST8i8	= 2967,
    X86_TEST8mi	= 2968,
    X86_TEST8mi_alt	= 2969,
    X86_TEST8ri	= 2970,
    X86_TEST8ri_NOREX	= 2971,
    X86_TEST8ri_alt	= 2972,
    X86_TEST8rm	= 2973,
    X86_TEST8rr	= 2974,
    X86_TLSCall_32	= 2975,
    X86_TLSCall_64	= 2976,
    X86_TLS_addr32	= 2977,
    X86_TLS_addr64	= 2978,
    X86_TLS_base_addr32	= 2979,
    X86_TLS_base_addr64	= 2980,
    X86_TRAP	= 2981,
    X86_TST_F	= 2982,
    X86_TST_Fp32	= 2983,
    X86_TST_Fp64	= 2984,
    X86_TST_Fp80	= 2985,
    X86_TZCNT16rm	= 2986,
    X86_TZCNT16rr	= 2987,
    X86_TZCNT32rm	= 2988,
    X86_TZCNT32rr	= 2989,
    X86_TZCNT64rm	= 2990,
    X86_TZCNT64rr	= 2991,
    X86_TZMSK32rm	= 2992,
    X86_TZMSK32rr	= 2993,
    X86_TZMSK64rm	= 2994,
    X86_TZMSK64rr	= 2995,
    X86_UCOMISDrm	= 2996,
    X86_UCOMISDrr	= 2997,
    X86_UCOMISSrm	= 2998,
    X86_UCOMISSrr	= 2999,
    X86_UCOM_FIPr	= 3000,
    X86_UCOM_FIr	= 3001,
    X86_UCOM_FPPr	= 3002,
    X86_UCOM_FPr	= 3003,
    X86_UCOM_FpIr32	= 3004,
    X86_UCOM_FpIr64	= 3005,
    X86_UCOM_FpIr80	= 3006,
    X86_UCOM_Fpr32	= 3007,
    X86_UCOM_Fpr64	= 3008,
    X86_UCOM_Fpr80	= 3009,
    X86_UCOM_Fr	= 3010,
    X86_UD2B	= 3011,
    X86_UNPCKHPDrm	= 3012,
    X86_UNPCKHPDrr	= 3013,
    X86_UNPCKHPSrm	= 3014,
    X86_UNPCKHPSrr	= 3015,
    X86_UNPCKLPDrm	= 3016,
    X86_UNPCKLPDrr	= 3017,
    X86_UNPCKLPSrm	= 3018,
    X86_UNPCKLPSrr	= 3019,
    X86_VAARG_64	= 3020,
    X86_VADDPDYrm	= 3021,
    X86_VADDPDYrr	= 3022,
    X86_VADDPDZ128rm	= 3023,
    X86_VADDPDZ128rmb	= 3024,
    X86_VADDPDZ128rmbk	= 3025,
    X86_VADDPDZ128rmbkz	= 3026,
    X86_VADDPDZ128rmk	= 3027,
    X86_VADDPDZ128rmkz	= 3028,
    X86_VADDPDZ128rr	= 3029,
    X86_VADDPDZ128rrk	= 3030,
    X86_VADDPDZ128rrkz	= 3031,
    X86_VADDPDZ256rm	= 3032,
    X86_VADDPDZ256rmb	= 3033,
    X86_VADDPDZ256rmbk	= 3034,
    X86_VADDPDZ256rmbkz	= 3035,
    X86_VADDPDZ256rmk	= 3036,
    X86_VADDPDZ256rmkz	= 3037,
    X86_VADDPDZ256rr	= 3038,
    X86_VADDPDZ256rrk	= 3039,
    X86_VADDPDZ256rrkz	= 3040,
    X86_VADDPDZrb	= 3041,
    X86_VADDPDZrbk	= 3042,
    X86_VADDPDZrbkz	= 3043,
    X86_VADDPDZrm	= 3044,
    X86_VADDPDZrmb	= 3045,
    X86_VADDPDZrmbk	= 3046,
    X86_VADDPDZrmbkz	= 3047,
    X86_VADDPDZrmk	= 3048,
    X86_VADDPDZrmkz	= 3049,
    X86_VADDPDZrr	= 3050,
    X86_VADDPDZrrk	= 3051,
    X86_VADDPDZrrkz	= 3052,
    X86_VADDPDrm	= 3053,
    X86_VADDPDrr	= 3054,
    X86_VADDPSYrm	= 3055,
    X86_VADDPSYrr	= 3056,
    X86_VADDPSZ128rm	= 3057,
    X86_VADDPSZ128rmb	= 3058,
    X86_VADDPSZ128rmbk	= 3059,
    X86_VADDPSZ128rmbkz	= 3060,
    X86_VADDPSZ128rmk	= 3061,
    X86_VADDPSZ128rmkz	= 3062,
    X86_VADDPSZ128rr	= 3063,
    X86_VADDPSZ128rrk	= 3064,
    X86_VADDPSZ128rrkz	= 3065,
    X86_VADDPSZ256rm	= 3066,
    X86_VADDPSZ256rmb	= 3067,
    X86_VADDPSZ256rmbk	= 3068,
    X86_VADDPSZ256rmbkz	= 3069,
    X86_VADDPSZ256rmk	= 3070,
    X86_VADDPSZ256rmkz	= 3071,
    X86_VADDPSZ256rr	= 3072,
    X86_VADDPSZ256rrk	= 3073,
    X86_VADDPSZ256rrkz	= 3074,
    X86_VADDPSZrb	= 3075,
    X86_VADDPSZrbk	= 3076,
    X86_VADDPSZrbkz	= 3077,
    X86_VADDPSZrm	= 3078,
    X86_VADDPSZrmb	= 3079,
    X86_VADDPSZrmbk	= 3080,
    X86_VADDPSZrmbkz	= 3081,
    X86_VADDPSZrmk	= 3082,
    X86_VADDPSZrmkz	= 3083,
    X86_VADDPSZrr	= 3084,
    X86_VADDPSZrrk	= 3085,
    X86_VADDPSZrrkz	= 3086,
    X86_VADDPSrm	= 3087,
    X86_VADDPSrr	= 3088,
    X86_VADDSDZrm	= 3089,
    X86_VADDSDZrm_Int	= 3090,
    X86_VADDSDZrm_Intk	= 3091,
    X86_VADDSDZrm_Intkz	= 3092,
    X86_VADDSDZrr	= 3093,
    X86_VADDSDZrr_Int	= 3094,
    X86_VADDSDZrr_Intk	= 3095,
    X86_VADDSDZrr_Intkz	= 3096,
    X86_VADDSDZrrb	= 3097,
    X86_VADDSDZrrbk	= 3098,
    X86_VADDSDZrrbkz	= 3099,
    X86_VADDSDrm	= 3100,
    X86_VADDSDrm_Int	= 3101,
    X86_VADDSDrr	= 3102,
    X86_VADDSDrr_Int	= 3103,
    X86_VADDSSZrm	= 3104,
    X86_VADDSSZrm_Int	= 3105,
    X86_VADDSSZrm_Intk	= 3106,
    X86_VADDSSZrm_Intkz	= 3107,
    X86_VADDSSZrr	= 3108,
    X86_VADDSSZrr_Int	= 3109,
    X86_VADDSSZrr_Intk	= 3110,
    X86_VADDSSZrr_Intkz	= 3111,
    X86_VADDSSZrrb	= 3112,
    X86_VADDSSZrrbk	= 3113,
    X86_VADDSSZrrbkz	= 3114,
    X86_VADDSSrm	= 3115,
    X86_VADDSSrm_Int	= 3116,
    X86_VADDSSrr	= 3117,
    X86_VADDSSrr_Int	= 3118,
    X86_VADDSUBPDYrm	= 3119,
    X86_VADDSUBPDYrr	= 3120,
    X86_VADDSUBPDrm	= 3121,
    X86_VADDSUBPDrr	= 3122,
    X86_VADDSUBPSYrm	= 3123,
    X86_VADDSUBPSYrr	= 3124,
    X86_VADDSUBPSrm	= 3125,
    X86_VADDSUBPSrr	= 3126,
    X86_VAESDECLASTrm	= 3127,
    X86_VAESDECLASTrr	= 3128,
    X86_VAESDECrm	= 3129,
    X86_VAESDECrr	= 3130,
    X86_VAESENCLASTrm	= 3131,
    X86_VAESENCLASTrr	= 3132,
    X86_VAESENCrm	= 3133,
    X86_VAESENCrr	= 3134,
    X86_VAESIMCrm	= 3135,
    X86_VAESIMCrr	= 3136,
    X86_VAESKEYGENASSIST128rm	= 3137,
    X86_VAESKEYGENASSIST128rr	= 3138,
    X86_VALIGNDrmi	= 3139,
    X86_VALIGNDrri	= 3140,
    X86_VALIGNDrrik	= 3141,
    X86_VALIGNDrrikz	= 3142,
    X86_VALIGNQrmi	= 3143,
    X86_VALIGNQrri	= 3144,
    X86_VALIGNQrrik	= 3145,
    X86_VALIGNQrrikz	= 3146,
    X86_VANDNPDYrm	= 3147,
    X86_VANDNPDYrr	= 3148,
    X86_VANDNPDrm	= 3149,
    X86_VANDNPDrr	= 3150,
    X86_VANDNPSYrm	= 3151,
    X86_VANDNPSYrr	= 3152,
    X86_VANDNPSrm	= 3153,
    X86_VANDNPSrr	= 3154,
    X86_VANDPDYrm	= 3155,
    X86_VANDPDYrr	= 3156,
    X86_VANDPDrm	= 3157,
    X86_VANDPDrr	= 3158,
    X86_VANDPSYrm	= 3159,
    X86_VANDPSYrr	= 3160,
    X86_VANDPSrm	= 3161,
    X86_VANDPSrr	= 3162,
    X86_VASTART_SAVE_XMM_REGS	= 3163,
    X86_VBLENDMPDZ128rm	= 3164,
    X86_VBLENDMPDZ128rmb	= 3165,
    X86_VBLENDMPDZ128rmbk	= 3166,
    X86_VBLENDMPDZ128rmk	= 3167,
    X86_VBLENDMPDZ128rmkz	= 3168,
    X86_VBLENDMPDZ128rr	= 3169,
    X86_VBLENDMPDZ128rrk	= 3170,
    X86_VBLENDMPDZ128rrkz	= 3171,
    X86_VBLENDMPDZ256rm	= 3172,
    X86_VBLENDMPDZ256rmb	= 3173,
    X86_VBLENDMPDZ256rmbk	= 3174,
    X86_VBLENDMPDZ256rmk	= 3175,
    X86_VBLENDMPDZ256rmkz	= 3176,
    X86_VBLENDMPDZ256rr	= 3177,
    X86_VBLENDMPDZ256rrk	= 3178,
    X86_VBLENDMPDZ256rrkz	= 3179,
    X86_VBLENDMPDZrm	= 3180,
    X86_VBLENDMPDZrmb	= 3181,
    X86_VBLENDMPDZrmbk	= 3182,
    X86_VBLENDMPDZrmk	= 3183,
    X86_VBLENDMPDZrmkz	= 3184,
    X86_VBLENDMPDZrr	= 3185,
    X86_VBLENDMPDZrrk	= 3186,
    X86_VBLENDMPDZrrkz	= 3187,
    X86_VBLENDMPSZ128rm	= 3188,
    X86_VBLENDMPSZ128rmb	= 3189,
    X86_VBLENDMPSZ128rmbk	= 3190,
    X86_VBLENDMPSZ128rmk	= 3191,
    X86_VBLENDMPSZ128rmkz	= 3192,
    X86_VBLENDMPSZ128rr	= 3193,
    X86_VBLENDMPSZ128rrk	= 3194,
    X86_VBLENDMPSZ128rrkz	= 3195,
    X86_VBLENDMPSZ256rm	= 3196,
    X86_VBLENDMPSZ256rmb	= 3197,
    X86_VBLENDMPSZ256rmbk	= 3198,
    X86_VBLENDMPSZ256rmk	= 3199,
    X86_VBLENDMPSZ256rmkz	= 3200,
    X86_VBLENDMPSZ256rr	= 3201,
    X86_VBLENDMPSZ256rrk	= 3202,
    X86_VBLENDMPSZ256rrkz	= 3203,
    X86_VBLENDMPSZrm	= 3204,
    X86_VBLENDMPSZrmb	= 3205,
    X86_VBLENDMPSZrmbk	= 3206,
    X86_VBLENDMPSZrmk	= 3207,
    X86_VBLENDMPSZrmkz	= 3208,
    X86_VBLENDMPSZrr	= 3209,
    X86_VBLENDMPSZrrk	= 3210,
    X86_VBLENDMPSZrrkz	= 3211,
    X86_VBLENDPDYrmi	= 3212,
    X86_VBLENDPDYrri	= 3213,
    X86_VBLENDPDrmi	= 3214,
    X86_VBLENDPDrri	= 3215,
    X86_VBLENDPSYrmi	= 3216,
    X86_VBLENDPSYrri	= 3217,
    X86_VBLENDPSrmi	= 3218,
    X86_VBLENDPSrri	= 3219,
    X86_VBLENDVPDYrm	= 3220,
    X86_VBLENDVPDYrr	= 3221,
    X86_VBLENDVPDrm	= 3222,
    X86_VBLENDVPDrr	= 3223,
    X86_VBLENDVPSYrm	= 3224,
    X86_VBLENDVPSYrr	= 3225,
    X86_VBLENDVPSrm	= 3226,
    X86_VBLENDVPSrr	= 3227,
    X86_VBROADCASTF128	= 3228,
    X86_VBROADCASTI32X4krm	= 3229,
    X86_VBROADCASTI32X4rm	= 3230,
    X86_VBROADCASTI64X4krm	= 3231,
    X86_VBROADCASTI64X4rm	= 3232,
    X86_VBROADCASTSDYrm	= 3233,
    X86_VBROADCASTSDYrr	= 3234,
    X86_VBROADCASTSDZ256m	= 3235,
    X86_VBROADCASTSDZ256mk	= 3236,
    X86_VBROADCASTSDZ256mkz	= 3237,
    X86_VBROADCASTSDZ256r	= 3238,
    X86_VBROADCASTSDZ256rk	= 3239,
    X86_VBROADCASTSDZ256rkz	= 3240,
    X86_VBROADCASTSDZm	= 3241,
    X86_VBROADCASTSDZmk	= 3242,
    X86_VBROADCASTSDZmkz	= 3243,
    X86_VBROADCASTSDZr	= 3244,
    X86_VBROADCASTSDZrk	= 3245,
    X86_VBROADCASTSDZrkz	= 3246,
    X86_VBROADCASTSSYrm	= 3247,
    X86_VBROADCASTSSYrr	= 3248,
    X86_VBROADCASTSSZ128m	= 3249,
    X86_VBROADCASTSSZ128mk	= 3250,
    X86_VBROADCASTSSZ128mkz	= 3251,
    X86_VBROADCASTSSZ128r	= 3252,
    X86_VBROADCASTSSZ128rk	= 3253,
    X86_VBROADCASTSSZ128rkz	= 3254,
    X86_VBROADCASTSSZ256m	= 3255,
    X86_VBROADCASTSSZ256mk	= 3256,
    X86_VBROADCASTSSZ256mkz	= 3257,
    X86_VBROADCASTSSZ256r	= 3258,
    X86_VBROADCASTSSZ256rk	= 3259,
    X86_VBROADCASTSSZ256rkz	= 3260,
    X86_VBROADCASTSSZm	= 3261,
    X86_VBROADCASTSSZmk	= 3262,
    X86_VBROADCASTSSZmkz	= 3263,
    X86_VBROADCASTSSZr	= 3264,
    X86_VBROADCASTSSZrk	= 3265,
    X86_VBROADCASTSSZrkz	= 3266,
    X86_VBROADCASTSSrm	= 3267,
    X86_VBROADCASTSSrr	= 3268,
    X86_VCMPPDYrmi	= 3269,
    X86_VCMPPDYrmi_alt	= 3270,
    X86_VCMPPDYrri	= 3271,
    X86_VCMPPDYrri_alt	= 3272,
    X86_VCMPPDZrmi	= 3273,
    X86_VCMPPDZrmi_alt	= 3274,
    X86_VCMPPDZrri	= 3275,
    X86_VCMPPDZrri_alt	= 3276,
    X86_VCMPPDZrrib	= 3277,
    X86_VCMPPDZrrib_alt	= 3278,
    X86_VCMPPDrmi	= 3279,
    X86_VCMPPDrmi_alt	= 3280,
    X86_VCMPPDrri	= 3281,
    X86_VCMPPDrri_alt	= 3282,
    X86_VCMPPSYrmi	= 3283,
    X86_VCMPPSYrmi_alt	= 3284,
    X86_VCMPPSYrri	= 3285,
    X86_VCMPPSYrri_alt	= 3286,
    X86_VCMPPSZrmi	= 3287,
    X86_VCMPPSZrmi_alt	= 3288,
    X86_VCMPPSZrri	= 3289,
    X86_VCMPPSZrri_alt	= 3290,
    X86_VCMPPSZrrib	= 3291,
    X86_VCMPPSZrrib_alt	= 3292,
    X86_VCMPPSrmi	= 3293,
    X86_VCMPPSrmi_alt	= 3294,
    X86_VCMPPSrri	= 3295,
    X86_VCMPPSrri_alt	= 3296,
    X86_VCMPSDZrm	= 3297,
    X86_VCMPSDZrmi_alt	= 3298,
    X86_VCMPSDZrr	= 3299,
    X86_VCMPSDZrri_alt	= 3300,
    X86_VCMPSDrm	= 3301,
    X86_VCMPSDrm_alt	= 3302,
    X86_VCMPSDrr	= 3303,
    X86_VCMPSDrr_alt	= 3304,
    X86_VCMPSSZrm	= 3305,
    X86_VCMPSSZrmi_alt	= 3306,
    X86_VCMPSSZrr	= 3307,
    X86_VCMPSSZrri_alt	= 3308,
    X86_VCMPSSrm	= 3309,
    X86_VCMPSSrm_alt	= 3310,
    X86_VCMPSSrr	= 3311,
    X86_VCMPSSrr_alt	= 3312,
    X86_VCOMISDZrm	= 3313,
    X86_VCOMISDZrr	= 3314,
    X86_VCOMISDrm	= 3315,
    X86_VCOMISDrr	= 3316,
    X86_VCOMISSZrm	= 3317,
    X86_VCOMISSZrr	= 3318,
    X86_VCOMISSrm	= 3319,
    X86_VCOMISSrr	= 3320,
    X86_VCOMPRESSPDZ128mrk	= 3321,
    X86_VCOMPRESSPDZ128rrk	= 3322,
    X86_VCOMPRESSPDZ128rrkz	= 3323,
    X86_VCOMPRESSPDZ256mrk	= 3324,
    X86_VCOMPRESSPDZ256rrk	= 3325,
    X86_VCOMPRESSPDZ256rrkz	= 3326,
    X86_VCOMPRESSPDZmrk	= 3327,
    X86_VCOMPRESSPDZrrk	= 3328,
    X86_VCOMPRESSPDZrrkz	= 3329,
    X86_VCOMPRESSPSZ128mrk	= 3330,
    X86_VCOMPRESSPSZ128rrk	= 3331,
    X86_VCOMPRESSPSZ128rrkz	= 3332,
    X86_VCOMPRESSPSZ256mrk	= 3333,
    X86_VCOMPRESSPSZ256rrk	= 3334,
    X86_VCOMPRESSPSZ256rrkz	= 3335,
    X86_VCOMPRESSPSZmrk	= 3336,
    X86_VCOMPRESSPSZrrk	= 3337,
    X86_VCOMPRESSPSZrrkz	= 3338,
    X86_VCVTDQ2PDYrm	= 3339,
    X86_VCVTDQ2PDYrr	= 3340,
    X86_VCVTDQ2PDZrm	= 3341,
    X86_VCVTDQ2PDZrr	= 3342,
    X86_VCVTDQ2PDrm	= 3343,
    X86_VCVTDQ2PDrr	= 3344,
    X86_VCVTDQ2PSYrm	= 3345,
    X86_VCVTDQ2PSYrr	= 3346,
    X86_VCVTDQ2PSZrm	= 3347,
    X86_VCVTDQ2PSZrr	= 3348,
    X86_VCVTDQ2PSZrrb	= 3349,
    X86_VCVTDQ2PSrm	= 3350,
    X86_VCVTDQ2PSrr	= 3351,
    X86_VCVTPD2DQXrm	= 3352,
    X86_VCVTPD2DQYrm	= 3353,
    X86_VCVTPD2DQYrr	= 3354,
    X86_VCVTPD2DQZrm	= 3355,
    X86_VCVTPD2DQZrr	= 3356,
    X86_VCVTPD2DQZrrb	= 3357,
    X86_VCVTPD2DQrr	= 3358,
    X86_VCVTPD2PSXrm	= 3359,
    X86_VCVTPD2PSYrm	= 3360,
    X86_VCVTPD2PSYrr	= 3361,
    X86_VCVTPD2PSZrm	= 3362,
    X86_VCVTPD2PSZrr	= 3363,
    X86_VCVTPD2PSZrrb	= 3364,
    X86_VCVTPD2PSrr	= 3365,
    X86_VCVTPD2UDQZrm	= 3366,
    X86_VCVTPD2UDQZrr	= 3367,
    X86_VCVTPD2UDQZrrb	= 3368,
    X86_VCVTPH2PSYrm	= 3369,
    X86_VCVTPH2PSYrr	= 3370,
    X86_VCVTPH2PSZrm	= 3371,
    X86_VCVTPH2PSZrr	= 3372,
    X86_VCVTPH2PSrm	= 3373,
    X86_VCVTPH2PSrr	= 3374,
    X86_VCVTPS2DQYrm	= 3375,
    X86_VCVTPS2DQYrr	= 3376,
    X86_VCVTPS2DQZrm	= 3377,
    X86_VCVTPS2DQZrr	= 3378,
    X86_VCVTPS2DQZrrb	= 3379,
    X86_VCVTPS2DQrm	= 3380,
    X86_VCVTPS2DQrr	= 3381,
    X86_VCVTPS2PDYrm	= 3382,
    X86_VCVTPS2PDYrr	= 3383,
    X86_VCVTPS2PDZrm	= 3384,
    X86_VCVTPS2PDZrr	= 3385,
    X86_VCVTPS2PDrm	= 3386,
    X86_VCVTPS2PDrr	= 3387,
    X86_VCVTPS2PHYmr	= 3388,
    X86_VCVTPS2PHYrr	= 3389,
    X86_VCVTPS2PHZmr	= 3390,
    X86_VCVTPS2PHZrr	= 3391,
    X86_VCVTPS2PHmr	= 3392,
    X86_VCVTPS2PHrr	= 3393,
    X86_VCVTPS2UDQZrm	= 3394,
    X86_VCVTPS2UDQZrr	= 3395,
    X86_VCVTPS2UDQZrrb	= 3396,
    X86_VCVTSD2SI64Zrm	= 3397,
    X86_VCVTSD2SI64Zrr	= 3398,
    X86_VCVTSD2SI64rm	= 3399,
    X86_VCVTSD2SI64rr	= 3400,
    X86_VCVTSD2SIZrm	= 3401,
    X86_VCVTSD2SIZrr	= 3402,
    X86_VCVTSD2SIrm	= 3403,
    X86_VCVTSD2SIrr	= 3404,
    X86_VCVTSD2SSZrm	= 3405,
    X86_VCVTSD2SSZrr	= 3406,
    X86_VCVTSD2SSrm	= 3407,
    X86_VCVTSD2SSrr	= 3408,
    X86_VCVTSD2USI64Zrm	= 3409,
    X86_VCVTSD2USI64Zrr	= 3410,
    X86_VCVTSD2USIZrm	= 3411,
    X86_VCVTSD2USIZrr	= 3412,
    X86_VCVTSI2SD64rm	= 3413,
    X86_VCVTSI2SD64rr	= 3414,
    X86_VCVTSI2SDZrm	= 3415,
    X86_VCVTSI2SDZrr	= 3416,
    X86_VCVTSI2SDrm	= 3417,
    X86_VCVTSI2SDrr	= 3418,
    X86_VCVTSI2SS64rm	= 3419,
    X86_VCVTSI2SS64rr	= 3420,
    X86_VCVTSI2SSZrm	= 3421,
    X86_VCVTSI2SSZrr	= 3422,
    X86_VCVTSI2SSrm	= 3423,
    X86_VCVTSI2SSrr	= 3424,
    X86_VCVTSI642SDZrm	= 3425,
    X86_VCVTSI642SDZrr	= 3426,
    X86_VCVTSI642SSZrm	= 3427,
    X86_VCVTSI642SSZrr	= 3428,
    X86_VCVTSS2SDZrm	= 3429,
    X86_VCVTSS2SDZrr	= 3430,
    X86_VCVTSS2SDrm	= 3431,
    X86_VCVTSS2SDrr	= 3432,
    X86_VCVTSS2SI64Zrm	= 3433,
    X86_VCVTSS2SI64Zrr	= 3434,
    X86_VCVTSS2SI64rm	= 3435,
    X86_VCVTSS2SI64rr	= 3436,
    X86_VCVTSS2SIZrm	= 3437,
    X86_VCVTSS2SIZrr	= 3438,
    X86_VCVTSS2SIrm	= 3439,
    X86_VCVTSS2SIrr	= 3440,
    X86_VCVTSS2USI64Zrm	= 3441,
    X86_VCVTSS2USI64Zrr	= 3442,
    X86_VCVTSS2USIZrm	= 3443,
    X86_VCVTSS2USIZrr	= 3444,
    X86_VCVTTPD2DQXrm	= 3445,
    X86_VCVTTPD2DQYrm	= 3446,
    X86_VCVTTPD2DQYrr	= 3447,
    X86_VCVTTPD2DQZrm	= 3448,
    X86_VCVTTPD2DQZrr	= 3449,
    X86_VCVTTPD2DQrr	= 3450,
    X86_VCVTTPD2UDQZrm	= 3451,
    X86_VCVTTPD2UDQZrr	= 3452,
    X86_VCVTTPS2DQYrm	= 3453,
    X86_VCVTTPS2DQYrr	= 3454,
    X86_VCVTTPS2DQZrm	= 3455,
    X86_VCVTTPS2DQZrr	= 3456,
    X86_VCVTTPS2DQrm	= 3457,
    X86_VCVTTPS2DQrr	= 3458,
    X86_VCVTTPS2UDQZrm	= 3459,
    X86_VCVTTPS2UDQZrr	= 3460,
    X86_VCVTTSD2SI64Zrm	= 3461,
    X86_VCVTTSD2SI64Zrr	= 3462,
    X86_VCVTTSD2SI64rm	= 3463,
    X86_VCVTTSD2SI64rr	= 3464,
    X86_VCVTTSD2SIZrm	= 3465,
    X86_VCVTTSD2SIZrr	= 3466,
    X86_VCVTTSD2SIrm	= 3467,
    X86_VCVTTSD2SIrr	= 3468,
    X86_VCVTTSD2USI64Zrm	= 3469,
    X86_VCVTTSD2USI64Zrr	= 3470,
    X86_VCVTTSD2USIZrm	= 3471,
    X86_VCVTTSD2USIZrr	= 3472,
    X86_VCVTTSS2SI64Zrm	= 3473,
    X86_VCVTTSS2SI64Zrr	= 3474,
    X86_VCVTTSS2SI64rm	= 3475,
    X86_VCVTTSS2SI64rr	= 3476,
    X86_VCVTTSS2SIZrm	= 3477,
    X86_VCVTTSS2SIZrr	= 3478,
    X86_VCVTTSS2SIrm	= 3479,
    X86_VCVTTSS2SIrr	= 3480,
    X86_VCVTTSS2USI64Zrm	= 3481,
    X86_VCVTTSS2USI64Zrr	= 3482,
    X86_VCVTTSS2USIZrm	= 3483,
    X86_VCVTTSS2USIZrr	= 3484,
    X86_VCVTUDQ2PDZrm	= 3485,
    X86_VCVTUDQ2PDZrr	= 3486,
    X86_VCVTUDQ2PSZrm	= 3487,
    X86_VCVTUDQ2PSZrr	= 3488,
    X86_VCVTUDQ2PSZrrb	= 3489,
    X86_VCVTUSI2SDZrm	= 3490,
    X86_VCVTUSI2SDZrr	= 3491,
    X86_VCVTUSI2SSZrm	= 3492,
    X86_VCVTUSI2SSZrr	= 3493,
    X86_VCVTUSI642SDZrm	= 3494,
    X86_VCVTUSI642SDZrr	= 3495,
    X86_VCVTUSI642SSZrm	= 3496,
    X86_VCVTUSI642SSZrr	= 3497,
    X86_VDIVPDYrm	= 3498,
    X86_VDIVPDYrr	= 3499,
    X86_VDIVPDZ128rm	= 3500,
    X86_VDIVPDZ128rmb	= 3501,
    X86_VDIVPDZ128rmbk	= 3502,
    X86_VDIVPDZ128rmbkz	= 3503,
    X86_VDIVPDZ128rmk	= 3504,
    X86_VDIVPDZ128rmkz	= 3505,
    X86_VDIVPDZ128rr	= 3506,
    X86_VDIVPDZ128rrk	= 3507,
    X86_VDIVPDZ128rrkz	= 3508,
    X86_VDIVPDZ256rm	= 3509,
    X86_VDIVPDZ256rmb	= 3510,
    X86_VDIVPDZ256rmbk	= 3511,
    X86_VDIVPDZ256rmbkz	= 3512,
    X86_VDIVPDZ256rmk	= 3513,
    X86_VDIVPDZ256rmkz	= 3514,
    X86_VDIVPDZ256rr	= 3515,
    X86_VDIVPDZ256rrk	= 3516,
    X86_VDIVPDZ256rrkz	= 3517,
    X86_VDIVPDZrb	= 3518,
    X86_VDIVPDZrbk	= 3519,
    X86_VDIVPDZrbkz	= 3520,
    X86_VDIVPDZrm	= 3521,
    X86_VDIVPDZrmb	= 3522,
    X86_VDIVPDZrmbk	= 3523,
    X86_VDIVPDZrmbkz	= 3524,
    X86_VDIVPDZrmk	= 3525,
    X86_VDIVPDZrmkz	= 3526,
    X86_VDIVPDZrr	= 3527,
    X86_VDIVPDZrrk	= 3528,
    X86_VDIVPDZrrkz	= 3529,
    X86_VDIVPDrm	= 3530,
    X86_VDIVPDrr	= 3531,
    X86_VDIVPSYrm	= 3532,
    X86_VDIVPSYrr	= 3533,
    X86_VDIVPSZ128rm	= 3534,
    X86_VDIVPSZ128rmb	= 3535,
    X86_VDIVPSZ128rmbk	= 3536,
    X86_VDIVPSZ128rmbkz	= 3537,
    X86_VDIVPSZ128rmk	= 3538,
    X86_VDIVPSZ128rmkz	= 3539,
    X86_VDIVPSZ128rr	= 3540,
    X86_VDIVPSZ128rrk	= 3541,
    X86_VDIVPSZ128rrkz	= 3542,
    X86_VDIVPSZ256rm	= 3543,
    X86_VDIVPSZ256rmb	= 3544,
    X86_VDIVPSZ256rmbk	= 3545,
    X86_VDIVPSZ256rmbkz	= 3546,
    X86_VDIVPSZ256rmk	= 3547,
    X86_VDIVPSZ256rmkz	= 3548,
    X86_VDIVPSZ256rr	= 3549,
    X86_VDIVPSZ256rrk	= 3550,
    X86_VDIVPSZ256rrkz	= 3551,
    X86_VDIVPSZrb	= 3552,
    X86_VDIVPSZrbk	= 3553,
    X86_VDIVPSZrbkz	= 3554,
    X86_VDIVPSZrm	= 3555,
    X86_VDIVPSZrmb	= 3556,
    X86_VDIVPSZrmbk	= 3557,
    X86_VDIVPSZrmbkz	= 3558,
    X86_VDIVPSZrmk	= 3559,
    X86_VDIVPSZrmkz	= 3560,
    X86_VDIVPSZrr	= 3561,
    X86_VDIVPSZrrk	= 3562,
    X86_VDIVPSZrrkz	= 3563,
    X86_VDIVPSrm	= 3564,
    X86_VDIVPSrr	= 3565,
    X86_VDIVSDZrm	= 3566,
    X86_VDIVSDZrm_Int	= 3567,
    X86_VDIVSDZrm_Intk	= 3568,
    X86_VDIVSDZrm_Intkz	= 3569,
    X86_VDIVSDZrr	= 3570,
    X86_VDIVSDZrr_Int	= 3571,
    X86_VDIVSDZrr_Intk	= 3572,
    X86_VDIVSDZrr_Intkz	= 3573,
    X86_VDIVSDZrrb	= 3574,
    X86_VDIVSDZrrbk	= 3575,
    X86_VDIVSDZrrbkz	= 3576,
    X86_VDIVSDrm	= 3577,
    X86_VDIVSDrm_Int	= 3578,
    X86_VDIVSDrr	= 3579,
    X86_VDIVSDrr_Int	= 3580,
    X86_VDIVSSZrm	= 3581,
    X86_VDIVSSZrm_Int	= 3582,
    X86_VDIVSSZrm_Intk	= 3583,
    X86_VDIVSSZrm_Intkz	= 3584,
    X86_VDIVSSZrr	= 3585,
    X86_VDIVSSZrr_Int	= 3586,
    X86_VDIVSSZrr_Intk	= 3587,
    X86_VDIVSSZrr_Intkz	= 3588,
    X86_VDIVSSZrrb	= 3589,
    X86_VDIVSSZrrbk	= 3590,
    X86_VDIVSSZrrbkz	= 3591,
    X86_VDIVSSrm	= 3592,
    X86_VDIVSSrm_Int	= 3593,
    X86_VDIVSSrr	= 3594,
    X86_VDIVSSrr_Int	= 3595,
    X86_VDPPDrmi	= 3596,
    X86_VDPPDrri	= 3597,
    X86_VDPPSYrmi	= 3598,
    X86_VDPPSYrri	= 3599,
    X86_VDPPSrmi	= 3600,
    X86_VDPPSrri	= 3601,
    X86_VERRm	= 3602,
    X86_VERRr	= 3603,
    X86_VERWm	= 3604,
    X86_VERWr	= 3605,
    X86_VEXP2PDm	= 3606,
    X86_VEXP2PDmb	= 3607,
    X86_VEXP2PDmbk	= 3608,
    X86_VEXP2PDmbkz	= 3609,
    X86_VEXP2PDmk	= 3610,
    X86_VEXP2PDmkz	= 3611,
    X86_VEXP2PDr	= 3612,
    X86_VEXP2PDrb	= 3613,
    X86_VEXP2PDrbk	= 3614,
    X86_VEXP2PDrbkz	= 3615,
    X86_VEXP2PDrk	= 3616,
    X86_VEXP2PDrkz	= 3617,
    X86_VEXP2PSm	= 3618,
    X86_VEXP2PSmb	= 3619,
    X86_VEXP2PSmbk	= 3620,
    X86_VEXP2PSmbkz	= 3621,
    X86_VEXP2PSmk	= 3622,
    X86_VEXP2PSmkz	= 3623,
    X86_VEXP2PSr	= 3624,
    X86_VEXP2PSrb	= 3625,
    X86_VEXP2PSrbk	= 3626,
    X86_VEXP2PSrbkz	= 3627,
    X86_VEXP2PSrk	= 3628,
    X86_VEXP2PSrkz	= 3629,
    X86_VEXPANDPDZ128rmk	= 3630,
    X86_VEXPANDPDZ128rmkz	= 3631,
    X86_VEXPANDPDZ128rrk	= 3632,
    X86_VEXPANDPDZ128rrkz	= 3633,
    X86_VEXPANDPDZ256rmk	= 3634,
    X86_VEXPANDPDZ256rmkz	= 3635,
    X86_VEXPANDPDZ256rrk	= 3636,
    X86_VEXPANDPDZ256rrkz	= 3637,
    X86_VEXPANDPDZrmk	= 3638,
    X86_VEXPANDPDZrmkz	= 3639,
    X86_VEXPANDPDZrrk	= 3640,
    X86_VEXPANDPDZrrkz	= 3641,
    X86_VEXPANDPSZ128rmk	= 3642,
    X86_VEXPANDPSZ128rmkz	= 3643,
    X86_VEXPANDPSZ128rrk	= 3644,
    X86_VEXPANDPSZ128rrkz	= 3645,
    X86_VEXPANDPSZ256rmk	= 3646,
    X86_VEXPANDPSZ256rmkz	= 3647,
    X86_VEXPANDPSZ256rrk	= 3648,
    X86_VEXPANDPSZ256rrkz	= 3649,
    X86_VEXPANDPSZrmk	= 3650,
    X86_VEXPANDPSZrmkz	= 3651,
    X86_VEXPANDPSZrrk	= 3652,
    X86_VEXPANDPSZrrkz	= 3653,
    X86_VEXTRACTF128mr	= 3654,
    X86_VEXTRACTF128rr	= 3655,
    X86_VEXTRACTF32x4rm	= 3656,
    X86_VEXTRACTF32x4rr	= 3657,
    X86_VEXTRACTF32x4rrk	= 3658,
    X86_VEXTRACTF32x4rrkz	= 3659,
    X86_VEXTRACTF64x4rm	= 3660,
    X86_VEXTRACTF64x4rr	= 3661,
    X86_VEXTRACTF64x4rrk	= 3662,
    X86_VEXTRACTF64x4rrkz	= 3663,
    X86_VEXTRACTI128mr	= 3664,
    X86_VEXTRACTI128rr	= 3665,
    X86_VEXTRACTI32x4rm	= 3666,
    X86_VEXTRACTI32x4rr	= 3667,
    X86_VEXTRACTI32x4rrk	= 3668,
    X86_VEXTRACTI32x4rrkz	= 3669,
    X86_VEXTRACTI64x4rm	= 3670,
    X86_VEXTRACTI64x4rr	= 3671,
    X86_VEXTRACTI64x4rrk	= 3672,
    X86_VEXTRACTI64x4rrkz	= 3673,
    X86_VEXTRACTPSmr	= 3674,
    X86_VEXTRACTPSrr	= 3675,
    X86_VEXTRACTPSzmr	= 3676,
    X86_VEXTRACTPSzrr	= 3677,
    X86_VFMADD132PDZ128m	= 3678,
    X86_VFMADD132PDZ128mb	= 3679,
    X86_VFMADD132PDZ256m	= 3680,
    X86_VFMADD132PDZ256mb	= 3681,
    X86_VFMADD132PDZm	= 3682,
    X86_VFMADD132PDZmb	= 3683,
    X86_VFMADD132PSZ128m	= 3684,
    X86_VFMADD132PSZ128mb	= 3685,
    X86_VFMADD132PSZ256m	= 3686,
    X86_VFMADD132PSZ256mb	= 3687,
    X86_VFMADD132PSZm	= 3688,
    X86_VFMADD132PSZmb	= 3689,
    X86_VFMADDPD4mr	= 3690,
    X86_VFMADDPD4mrY	= 3691,
    X86_VFMADDPD4rm	= 3692,
    X86_VFMADDPD4rmY	= 3693,
    X86_VFMADDPD4rr	= 3694,
    X86_VFMADDPD4rrY	= 3695,
    X86_VFMADDPD4rrY_REV	= 3696,
    X86_VFMADDPD4rr_REV	= 3697,
    X86_VFMADDPDZ128v213rm	= 3698,
    X86_VFMADDPDZ128v213rmb	= 3699,
    X86_VFMADDPDZ128v213rmbk	= 3700,
    X86_VFMADDPDZ128v213rmbkz	= 3701,
    X86_VFMADDPDZ128v213rmk	= 3702,
    X86_VFMADDPDZ128v213rmkz	= 3703,
    X86_VFMADDPDZ128v213rr	= 3704,
    X86_VFMADDPDZ128v213rrk	= 3705,
    X86_VFMADDPDZ128v213rrkz	= 3706,
    X86_VFMADDPDZ128v231rm	= 3707,
    X86_VFMADDPDZ128v231rmb	= 3708,
    X86_VFMADDPDZ128v231rmbk	= 3709,
    X86_VFMADDPDZ128v231rmbkz	= 3710,
    X86_VFMADDPDZ128v231rmk	= 3711,
    X86_VFMADDPDZ128v231rmkz	= 3712,
    X86_VFMADDPDZ128v231rr	= 3713,
    X86_VFMADDPDZ128v231rrk	= 3714,
    X86_VFMADDPDZ128v231rrkz	= 3715,
    X86_VFMADDPDZ256v213rm	= 3716,
    X86_VFMADDPDZ256v213rmb	= 3717,
    X86_VFMADDPDZ256v213rmbk	= 3718,
    X86_VFMADDPDZ256v213rmbkz	= 3719,
    X86_VFMADDPDZ256v213rmk	= 3720,
    X86_VFMADDPDZ256v213rmkz	= 3721,
    X86_VFMADDPDZ256v213rr	= 3722,
    X86_VFMADDPDZ256v213rrk	= 3723,
    X86_VFMADDPDZ256v213rrkz	= 3724,
    X86_VFMADDPDZ256v231rm	= 3725,
    X86_VFMADDPDZ256v231rmb	= 3726,
    X86_VFMADDPDZ256v231rmbk	= 3727,
    X86_VFMADDPDZ256v231rmbkz	= 3728,
    X86_VFMADDPDZ256v231rmk	= 3729,
    X86_VFMADDPDZ256v231rmkz	= 3730,
    X86_VFMADDPDZ256v231rr	= 3731,
    X86_VFMADDPDZ256v231rrk	= 3732,
    X86_VFMADDPDZ256v231rrkz	= 3733,
    X86_VFMADDPDZv213rm	= 3734,
    X86_VFMADDPDZv213rmb	= 3735,
    X86_VFMADDPDZv213rmbk	= 3736,
    X86_VFMADDPDZv213rmbkz	= 3737,
    X86_VFMADDPDZv213rmk	= 3738,
    X86_VFMADDPDZv213rmkz	= 3739,
    X86_VFMADDPDZv213rr	= 3740,
    X86_VFMADDPDZv213rrb	= 3741,
    X86_VFMADDPDZv213rrbk	= 3742,
    X86_VFMADDPDZv213rrbkz	= 3743,
    X86_VFMADDPDZv213rrk	= 3744,
    X86_VFMADDPDZv213rrkz	= 3745,
    X86_VFMADDPDZv231rm	= 3746,
    X86_VFMADDPDZv231rmb	= 3747,
    X86_VFMADDPDZv231rmbk	= 3748,
    X86_VFMADDPDZv231rmbkz	= 3749,
    X86_VFMADDPDZv231rmk	= 3750,
    X86_VFMADDPDZv231rmkz	= 3751,
    X86_VFMADDPDZv231rr	= 3752,
    X86_VFMADDPDZv231rrk	= 3753,
    X86_VFMADDPDZv231rrkz	= 3754,
    X86_VFMADDPDr132m	= 3755,
    X86_VFMADDPDr132mY	= 3756,
    X86_VFMADDPDr132r	= 3757,
    X86_VFMADDPDr132rY	= 3758,
    X86_VFMADDPDr213m	= 3759,
    X86_VFMADDPDr213mY	= 3760,
    X86_VFMADDPDr213r	= 3761,
    X86_VFMADDPDr213rY	= 3762,
    X86_VFMADDPDr231m	= 3763,
    X86_VFMADDPDr231mY	= 3764,
    X86_VFMADDPDr231r	= 3765,
    X86_VFMADDPDr231rY	= 3766,
    X86_VFMADDPS4mr	= 3767,
    X86_VFMADDPS4mrY	= 3768,
    X86_VFMADDPS4rm	= 3769,
    X86_VFMADDPS4rmY	= 3770,
    X86_VFMADDPS4rr	= 3771,
    X86_VFMADDPS4rrY	= 3772,
    X86_VFMADDPS4rrY_REV	= 3773,
    X86_VFMADDPS4rr_REV	= 3774,
    X86_VFMADDPSZ128v213rm	= 3775,
    X86_VFMADDPSZ128v213rmb	= 3776,
    X86_VFMADDPSZ128v213rmbk	= 3777,
    X86_VFMADDPSZ128v213rmbkz	= 3778,
    X86_VFMADDPSZ128v213rmk	= 3779,
    X86_VFMADDPSZ128v213rmkz	= 3780,
    X86_VFMADDPSZ128v213rr	= 3781,
    X86_VFMADDPSZ128v213rrk	= 3782,
    X86_VFMADDPSZ128v213rrkz	= 3783,
    X86_VFMADDPSZ128v231rm	= 3784,
    X86_VFMADDPSZ128v231rmb	= 3785,
    X86_VFMADDPSZ128v231rmbk	= 3786,
    X86_VFMADDPSZ128v231rmbkz	= 3787,
    X86_VFMADDPSZ128v231rmk	= 3788,
    X86_VFMADDPSZ128v231rmkz	= 3789,
    X86_VFMADDPSZ128v231rr	= 3790,
    X86_VFMADDPSZ128v231rrk	= 3791,
    X86_VFMADDPSZ128v231rrkz	= 3792,
    X86_VFMADDPSZ256v213rm	= 3793,
    X86_VFMADDPSZ256v213rmb	= 3794,
    X86_VFMADDPSZ256v213rmbk	= 3795,
    X86_VFMADDPSZ256v213rmbkz	= 3796,
    X86_VFMADDPSZ256v213rmk	= 3797,
    X86_VFMADDPSZ256v213rmkz	= 3798,
    X86_VFMADDPSZ256v213rr	= 3799,
    X86_VFMADDPSZ256v213rrk	= 3800,
    X86_VFMADDPSZ256v213rrkz	= 3801,
    X86_VFMADDPSZ256v231rm	= 3802,
    X86_VFMADDPSZ256v231rmb	= 3803,
    X86_VFMADDPSZ256v231rmbk	= 3804,
    X86_VFMADDPSZ256v231rmbkz	= 3805,
    X86_VFMADDPSZ256v231rmk	= 3806,
    X86_VFMADDPSZ256v231rmkz	= 3807,
    X86_VFMADDPSZ256v231rr	= 3808,
    X86_VFMADDPSZ256v231rrk	= 3809,
    X86_VFMADDPSZ256v231rrkz	= 3810,
    X86_VFMADDPSZv213rm	= 3811,
    X86_VFMADDPSZv213rmb	= 3812,
    X86_VFMADDPSZv213rmbk	= 3813,
    X86_VFMADDPSZv213rmbkz	= 3814,
    X86_VFMADDPSZv213rmk	= 3815,
    X86_VFMADDPSZv213rmkz	= 3816,
    X86_VFMADDPSZv213rr	= 3817,
    X86_VFMADDPSZv213rrb	= 3818,
    X86_VFMADDPSZv213rrbk	= 3819,
    X86_VFMADDPSZv213rrbkz	= 3820,
    X86_VFMADDPSZv213rrk	= 3821,
    X86_VFMADDPSZv213rrkz	= 3822,
    X86_VFMADDPSZv231rm	= 3823,
    X86_VFMADDPSZv231rmb	= 3824,
    X86_VFMADDPSZv231rmbk	= 3825,
    X86_VFMADDPSZv231rmbkz	= 3826,
    X86_VFMADDPSZv231rmk	= 3827,
    X86_VFMADDPSZv231rmkz	= 3828,
    X86_VFMADDPSZv231rr	= 3829,
    X86_VFMADDPSZv231rrk	= 3830,
    X86_VFMADDPSZv231rrkz	= 3831,
    X86_VFMADDPSr132m	= 3832,
    X86_VFMADDPSr132mY	= 3833,
    X86_VFMADDPSr132r	= 3834,
    X86_VFMADDPSr132rY	= 3835,
    X86_VFMADDPSr213m	= 3836,
    X86_VFMADDPSr213mY	= 3837,
    X86_VFMADDPSr213r	= 3838,
    X86_VFMADDPSr213rY	= 3839,
    X86_VFMADDPSr231m	= 3840,
    X86_VFMADDPSr231mY	= 3841,
    X86_VFMADDPSr231r	= 3842,
    X86_VFMADDPSr231rY	= 3843,
    X86_VFMADDSD4mr	= 3844,
    X86_VFMADDSD4mr_Int	= 3845,
    X86_VFMADDSD4rm	= 3846,
    X86_VFMADDSD4rm_Int	= 3847,
    X86_VFMADDSD4rr	= 3848,
    X86_VFMADDSD4rr_Int	= 3849,
    X86_VFMADDSD4rr_REV	= 3850,
    X86_VFMADDSDZm	= 3851,
    X86_VFMADDSDZr	= 3852,
    X86_VFMADDSDr132m	= 3853,
    X86_VFMADDSDr132r	= 3854,
    X86_VFMADDSDr213m	= 3855,
    X86_VFMADDSDr213r	= 3856,
    X86_VFMADDSDr231m	= 3857,
    X86_VFMADDSDr231r	= 3858,
    X86_VFMADDSS4mr	= 3859,
    X86_VFMADDSS4mr_Int	= 3860,
    X86_VFMADDSS4rm	= 3861,
    X86_VFMADDSS4rm_Int	= 3862,
    X86_VFMADDSS4rr	= 3863,
    X86_VFMADDSS4rr_Int	= 3864,
    X86_VFMADDSS4rr_REV	= 3865,
    X86_VFMADDSSZm	= 3866,
    X86_VFMADDSSZr	= 3867,
    X86_VFMADDSSr132m	= 3868,
    X86_VFMADDSSr132r	= 3869,
    X86_VFMADDSSr213m	= 3870,
    X86_VFMADDSSr213r	= 3871,
    X86_VFMADDSSr231m	= 3872,
    X86_VFMADDSSr231r	= 3873,
    X86_VFMADDSUB132PDZ128m	= 3874,
    X86_VFMADDSUB132PDZ128mb	= 3875,
    X86_VFMADDSUB132PDZ256m	= 3876,
    X86_VFMADDSUB132PDZ256mb	= 3877,
    X86_VFMADDSUB132PDZm	= 3878,
    X86_VFMADDSUB132PDZmb	= 3879,
    X86_VFMADDSUB132PSZ128m	= 3880,
    X86_VFMADDSUB132PSZ128mb	= 3881,
    X86_VFMADDSUB132PSZ256m	= 3882,
    X86_VFMADDSUB132PSZ256mb	= 3883,
    X86_VFMADDSUB132PSZm	= 3884,
    X86_VFMADDSUB132PSZmb	= 3885,
    X86_VFMADDSUBPD4mr	= 3886,
    X86_VFMADDSUBPD4mrY	= 3887,
    X86_VFMADDSUBPD4rm	= 3888,
    X86_VFMADDSUBPD4rmY	= 3889,
    X86_VFMADDSUBPD4rr	= 3890,
    X86_VFMADDSUBPD4rrY	= 3891,
    X86_VFMADDSUBPD4rrY_REV	= 3892,
    X86_VFMADDSUBPD4rr_REV	= 3893,
    X86_VFMADDSUBPDZ128v213rm	= 3894,
    X86_VFMADDSUBPDZ128v213rmb	= 3895,
    X86_VFMADDSUBPDZ128v213rmbk	= 3896,
    X86_VFMADDSUBPDZ128v213rmbkz	= 3897,
    X86_VFMADDSUBPDZ128v213rmk	= 3898,
    X86_VFMADDSUBPDZ128v213rmkz	= 3899,
    X86_VFMADDSUBPDZ128v213rr	= 3900,
    X86_VFMADDSUBPDZ128v213rrk	= 3901,
    X86_VFMADDSUBPDZ128v213rrkz	= 3902,
    X86_VFMADDSUBPDZ128v231rm	= 3903,
    X86_VFMADDSUBPDZ128v231rmb	= 3904,
    X86_VFMADDSUBPDZ128v231rmbk	= 3905,
    X86_VFMADDSUBPDZ128v231rmbkz	= 3906,
    X86_VFMADDSUBPDZ128v231rmk	= 3907,
    X86_VFMADDSUBPDZ128v231rmkz	= 3908,
    X86_VFMADDSUBPDZ128v231rr	= 3909,
    X86_VFMADDSUBPDZ128v231rrk	= 3910,
    X86_VFMADDSUBPDZ128v231rrkz	= 3911,
    X86_VFMADDSUBPDZ256v213rm	= 3912,
    X86_VFMADDSUBPDZ256v213rmb	= 3913,
    X86_VFMADDSUBPDZ256v213rmbk	= 3914,
    X86_VFMADDSUBPDZ256v213rmbkz	= 3915,
    X86_VFMADDSUBPDZ256v213rmk	= 3916,
    X86_VFMADDSUBPDZ256v213rmkz	= 3917,
    X86_VFMADDSUBPDZ256v213rr	= 3918,
    X86_VFMADDSUBPDZ256v213rrk	= 3919,
    X86_VFMADDSUBPDZ256v213rrkz	= 3920,
    X86_VFMADDSUBPDZ256v231rm	= 3921,
    X86_VFMADDSUBPDZ256v231rmb	= 3922,
    X86_VFMADDSUBPDZ256v231rmbk	= 3923,
    X86_VFMADDSUBPDZ256v231rmbkz	= 3924,
    X86_VFMADDSUBPDZ256v231rmk	= 3925,
    X86_VFMADDSUBPDZ256v231rmkz	= 3926,
    X86_VFMADDSUBPDZ256v231rr	= 3927,
    X86_VFMADDSUBPDZ256v231rrk	= 3928,
    X86_VFMADDSUBPDZ256v231rrkz	= 3929,
    X86_VFMADDSUBPDZv213rm	= 3930,
    X86_VFMADDSUBPDZv213rmb	= 3931,
    X86_VFMADDSUBPDZv213rmbk	= 3932,
    X86_VFMADDSUBPDZv213rmbkz	= 3933,
    X86_VFMADDSUBPDZv213rmk	= 3934,
    X86_VFMADDSUBPDZv213rmkz	= 3935,
    X86_VFMADDSUBPDZv213rr	= 3936,
    X86_VFMADDSUBPDZv213rrb	= 3937,
    X86_VFMADDSUBPDZv213rrbk	= 3938,
    X86_VFMADDSUBPDZv213rrbkz	= 3939,
    X86_VFMADDSUBPDZv213rrk	= 3940,
    X86_VFMADDSUBPDZv213rrkz	= 3941,
    X86_VFMADDSUBPDZv231rm	= 3942,
    X86_VFMADDSUBPDZv231rmb	= 3943,
    X86_VFMADDSUBPDZv231rmbk	= 3944,
    X86_VFMADDSUBPDZv231rmbkz	= 3945,
    X86_VFMADDSUBPDZv231rmk	= 3946,
    X86_VFMADDSUBPDZv231rmkz	= 3947,
    X86_VFMADDSUBPDZv231rr	= 3948,
    X86_VFMADDSUBPDZv231rrk	= 3949,
    X86_VFMADDSUBPDZv231rrkz	= 3950,
    X86_VFMADDSUBPDr132m	= 3951,
    X86_VFMADDSUBPDr132mY	= 3952,
    X86_VFMADDSUBPDr132r	= 3953,
    X86_VFMADDSUBPDr132rY	= 3954,
    X86_VFMADDSUBPDr213m	= 3955,
    X86_VFMADDSUBPDr213mY	= 3956,
    X86_VFMADDSUBPDr213r	= 3957,
    X86_VFMADDSUBPDr213rY	= 3958,
    X86_VFMADDSUBPDr231m	= 3959,
    X86_VFMADDSUBPDr231mY	= 3960,
    X86_VFMADDSUBPDr231r	= 3961,
    X86_VFMADDSUBPDr231rY	= 3962,
    X86_VFMADDSUBPS4mr	= 3963,
    X86_VFMADDSUBPS4mrY	= 3964,
    X86_VFMADDSUBPS4rm	= 3965,
    X86_VFMADDSUBPS4rmY	= 3966,
    X86_VFMADDSUBPS4rr	= 3967,
    X86_VFMADDSUBPS4rrY	= 3968,
    X86_VFMADDSUBPS4rrY_REV	= 3969,
    X86_VFMADDSUBPS4rr_REV	= 3970,
    X86_VFMADDSUBPSZ128v213rm	= 3971,
    X86_VFMADDSUBPSZ128v213rmb	= 3972,
    X86_VFMADDSUBPSZ128v213rmbk	= 3973,
    X86_VFMADDSUBPSZ128v213rmbkz	= 3974,
    X86_VFMADDSUBPSZ128v213rmk	= 3975,
    X86_VFMADDSUBPSZ128v213rmkz	= 3976,
    X86_VFMADDSUBPSZ128v213rr	= 3977,
    X86_VFMADDSUBPSZ128v213rrk	= 3978,
    X86_VFMADDSUBPSZ128v213rrkz	= 3979,
    X86_VFMADDSUBPSZ128v231rm	= 3980,
    X86_VFMADDSUBPSZ128v231rmb	= 3981,
    X86_VFMADDSUBPSZ128v231rmbk	= 3982,
    X86_VFMADDSUBPSZ128v231rmbkz	= 3983,
    X86_VFMADDSUBPSZ128v231rmk	= 3984,
    X86_VFMADDSUBPSZ128v231rmkz	= 3985,
    X86_VFMADDSUBPSZ128v231rr	= 3986,
    X86_VFMADDSUBPSZ128v231rrk	= 3987,
    X86_VFMADDSUBPSZ128v231rrkz	= 3988,
    X86_VFMADDSUBPSZ256v213rm	= 3989,
    X86_VFMADDSUBPSZ256v213rmb	= 3990,
    X86_VFMADDSUBPSZ256v213rmbk	= 3991,
    X86_VFMADDSUBPSZ256v213rmbkz	= 3992,
    X86_VFMADDSUBPSZ256v213rmk	= 3993,
    X86_VFMADDSUBPSZ256v213rmkz	= 3994,
    X86_VFMADDSUBPSZ256v213rr	= 3995,
    X86_VFMADDSUBPSZ256v213rrk	= 3996,
    X86_VFMADDSUBPSZ256v213rrkz	= 3997,
    X86_VFMADDSUBPSZ256v231rm	= 3998,
    X86_VFMADDSUBPSZ256v231rmb	= 3999,
    X86_VFMADDSUBPSZ256v231rmbk	= 4000,
    X86_VFMADDSUBPSZ256v231rmbkz	= 4001,
    X86_VFMADDSUBPSZ256v231rmk	= 4002,
    X86_VFMADDSUBPSZ256v231rmkz	= 4003,
    X86_VFMADDSUBPSZ256v231rr	= 4004,
    X86_VFMADDSUBPSZ256v231rrk	= 4005,
    X86_VFMADDSUBPSZ256v231rrkz	= 4006,
    X86_VFMADDSUBPSZv213rm	= 4007,
    X86_VFMADDSUBPSZv213rmb	= 4008,
    X86_VFMADDSUBPSZv213rmbk	= 4009,
    X86_VFMADDSUBPSZv213rmbkz	= 4010,
    X86_VFMADDSUBPSZv213rmk	= 4011,
    X86_VFMADDSUBPSZv213rmkz	= 4012,
    X86_VFMADDSUBPSZv213rr	= 4013,
    X86_VFMADDSUBPSZv213rrb	= 4014,
    X86_VFMADDSUBPSZv213rrbk	= 4015,
    X86_VFMADDSUBPSZv213rrbkz	= 4016,
    X86_VFMADDSUBPSZv213rrk	= 4017,
    X86_VFMADDSUBPSZv213rrkz	= 4018,
    X86_VFMADDSUBPSZv231rm	= 4019,
    X86_VFMADDSUBPSZv231rmb	= 4020,
    X86_VFMADDSUBPSZv231rmbk	= 4021,
    X86_VFMADDSUBPSZv231rmbkz	= 4022,
    X86_VFMADDSUBPSZv231rmk	= 4023,
    X86_VFMADDSUBPSZv231rmkz	= 4024,
    X86_VFMADDSUBPSZv231rr	= 4025,
    X86_VFMADDSUBPSZv231rrk	= 4026,
    X86_VFMADDSUBPSZv231rrkz	= 4027,
    X86_VFMADDSUBPSr132m	= 4028,
    X86_VFMADDSUBPSr132mY	= 4029,
    X86_VFMADDSUBPSr132r	= 4030,
    X86_VFMADDSUBPSr132rY	= 4031,
    X86_VFMADDSUBPSr213m	= 4032,
    X86_VFMADDSUBPSr213mY	= 4033,
    X86_VFMADDSUBPSr213r	= 4034,
    X86_VFMADDSUBPSr213rY	= 4035,
    X86_VFMADDSUBPSr231m	= 4036,
    X86_VFMADDSUBPSr231mY	= 4037,
    X86_VFMADDSUBPSr231r	= 4038,
    X86_VFMADDSUBPSr231rY	= 4039,
    X86_VFMSUB132PDZ128m	= 4040,
    X86_VFMSUB132PDZ128mb	= 4041,
    X86_VFMSUB132PDZ256m	= 4042,
    X86_VFMSUB132PDZ256mb	= 4043,
    X86_VFMSUB132PDZm	= 4044,
    X86_VFMSUB132PDZmb	= 4045,
    X86_VFMSUB132PSZ128m	= 4046,
    X86_VFMSUB132PSZ128mb	= 4047,
    X86_VFMSUB132PSZ256m	= 4048,
    X86_VFMSUB132PSZ256mb	= 4049,
    X86_VFMSUB132PSZm	= 4050,
    X86_VFMSUB132PSZmb	= 4051,
    X86_VFMSUBADD132PDZ128m	= 4052,
    X86_VFMSUBADD132PDZ128mb	= 4053,
    X86_VFMSUBADD132PDZ256m	= 4054,
    X86_VFMSUBADD132PDZ256mb	= 4055,
    X86_VFMSUBADD132PDZm	= 4056,
    X86_VFMSUBADD132PDZmb	= 4057,
    X86_VFMSUBADD132PSZ128m	= 4058,
    X86_VFMSUBADD132PSZ128mb	= 4059,
    X86_VFMSUBADD132PSZ256m	= 4060,
    X86_VFMSUBADD132PSZ256mb	= 4061,
    X86_VFMSUBADD132PSZm	= 4062,
    X86_VFMSUBADD132PSZmb	= 4063,
    X86_VFMSUBADDPD4mr	= 4064,
    X86_VFMSUBADDPD4mrY	= 4065,
    X86_VFMSUBADDPD4rm	= 4066,
    X86_VFMSUBADDPD4rmY	= 4067,
    X86_VFMSUBADDPD4rr	= 4068,
    X86_VFMSUBADDPD4rrY	= 4069,
    X86_VFMSUBADDPD4rrY_REV	= 4070,
    X86_VFMSUBADDPD4rr_REV	= 4071,
    X86_VFMSUBADDPDZ128v213rm	= 4072,
    X86_VFMSUBADDPDZ128v213rmb	= 4073,
    X86_VFMSUBADDPDZ128v213rmbk	= 4074,
    X86_VFMSUBADDPDZ128v213rmbkz	= 4075,
    X86_VFMSUBADDPDZ128v213rmk	= 4076,
    X86_VFMSUBADDPDZ128v213rmkz	= 4077,
    X86_VFMSUBADDPDZ128v213rr	= 4078,
    X86_VFMSUBADDPDZ128v213rrk	= 4079,
    X86_VFMSUBADDPDZ128v213rrkz	= 4080,
    X86_VFMSUBADDPDZ128v231rm	= 4081,
    X86_VFMSUBADDPDZ128v231rmb	= 4082,
    X86_VFMSUBADDPDZ128v231rmbk	= 4083,
    X86_VFMSUBADDPDZ128v231rmbkz	= 4084,
    X86_VFMSUBADDPDZ128v231rmk	= 4085,
    X86_VFMSUBADDPDZ128v231rmkz	= 4086,
    X86_VFMSUBADDPDZ128v231rr	= 4087,
    X86_VFMSUBADDPDZ128v231rrk	= 4088,
    X86_VFMSUBADDPDZ128v231rrkz	= 4089,
    X86_VFMSUBADDPDZ256v213rm	= 4090,
    X86_VFMSUBADDPDZ256v213rmb	= 4091,
    X86_VFMSUBADDPDZ256v213rmbk	= 4092,
    X86_VFMSUBADDPDZ256v213rmbkz	= 4093,
    X86_VFMSUBADDPDZ256v213rmk	= 4094,
    X86_VFMSUBADDPDZ256v213rmkz	= 4095,
    X86_VFMSUBADDPDZ256v213rr	= 4096,
    X86_VFMSUBADDPDZ256v213rrk	= 4097,
    X86_VFMSUBADDPDZ256v213rrkz	= 4098,
    X86_VFMSUBADDPDZ256v231rm	= 4099,
    X86_VFMSUBADDPDZ256v231rmb	= 4100,
    X86_VFMSUBADDPDZ256v231rmbk	= 4101,
    X86_VFMSUBADDPDZ256v231rmbkz	= 4102,
    X86_VFMSUBADDPDZ256v231rmk	= 4103,
    X86_VFMSUBADDPDZ256v231rmkz	= 4104,
    X86_VFMSUBADDPDZ256v231rr	= 4105,
    X86_VFMSUBADDPDZ256v231rrk	= 4106,
    X86_VFMSUBADDPDZ256v231rrkz	= 4107,
    X86_VFMSUBADDPDZv213rm	= 4108,
    X86_VFMSUBADDPDZv213rmb	= 4109,
    X86_VFMSUBADDPDZv213rmbk	= 4110,
    X86_VFMSUBADDPDZv213rmbkz	= 4111,
    X86_VFMSUBADDPDZv213rmk	= 4112,
    X86_VFMSUBADDPDZv213rmkz	= 4113,
    X86_VFMSUBADDPDZv213rr	= 4114,
    X86_VFMSUBADDPDZv213rrb	= 4115,
    X86_VFMSUBADDPDZv213rrbk	= 4116,
    X86_VFMSUBADDPDZv213rrbkz	= 4117,
    X86_VFMSUBADDPDZv213rrk	= 4118,
    X86_VFMSUBADDPDZv213rrkz	= 4119,
    X86_VFMSUBADDPDZv231rm	= 4120,
    X86_VFMSUBADDPDZv231rmb	= 4121,
    X86_VFMSUBADDPDZv231rmbk	= 4122,
    X86_VFMSUBADDPDZv231rmbkz	= 4123,
    X86_VFMSUBADDPDZv231rmk	= 4124,
    X86_VFMSUBADDPDZv231rmkz	= 4125,
    X86_VFMSUBADDPDZv231rr	= 4126,
    X86_VFMSUBADDPDZv231rrk	= 4127,
    X86_VFMSUBADDPDZv231rrkz	= 4128,
    X86_VFMSUBADDPDr132m	= 4129,
    X86_VFMSUBADDPDr132mY	= 4130,
    X86_VFMSUBADDPDr132r	= 4131,
    X86_VFMSUBADDPDr132rY	= 4132,
    X86_VFMSUBADDPDr213m	= 4133,
    X86_VFMSUBADDPDr213mY	= 4134,
    X86_VFMSUBADDPDr213r	= 4135,
    X86_VFMSUBADDPDr213rY	= 4136,
    X86_VFMSUBADDPDr231m	= 4137,
    X86_VFMSUBADDPDr231mY	= 4138,
    X86_VFMSUBADDPDr231r	= 4139,
    X86_VFMSUBADDPDr231rY	= 4140,
    X86_VFMSUBADDPS4mr	= 4141,
    X86_VFMSUBADDPS4mrY	= 4142,
    X86_VFMSUBADDPS4rm	= 4143,
    X86_VFMSUBADDPS4rmY	= 4144,
    X86_VFMSUBADDPS4rr	= 4145,
    X86_VFMSUBADDPS4rrY	= 4146,
    X86_VFMSUBADDPS4rrY_REV	= 4147,
    X86_VFMSUBADDPS4rr_REV	= 4148,
    X86_VFMSUBADDPSZ128v213rm	= 4149,
    X86_VFMSUBADDPSZ128v213rmb	= 4150,
    X86_VFMSUBADDPSZ128v213rmbk	= 4151,
    X86_VFMSUBADDPSZ128v213rmbkz	= 4152,
    X86_VFMSUBADDPSZ128v213rmk	= 4153,
    X86_VFMSUBADDPSZ128v213rmkz	= 4154,
    X86_VFMSUBADDPSZ128v213rr	= 4155,
    X86_VFMSUBADDPSZ128v213rrk	= 4156,
    X86_VFMSUBADDPSZ128v213rrkz	= 4157,
    X86_VFMSUBADDPSZ128v231rm	= 4158,
    X86_VFMSUBADDPSZ128v231rmb	= 4159,
    X86_VFMSUBADDPSZ128v231rmbk	= 4160,
    X86_VFMSUBADDPSZ128v231rmbkz	= 4161,
    X86_VFMSUBADDPSZ128v231rmk	= 4162,
    X86_VFMSUBADDPSZ128v231rmkz	= 4163,
    X86_VFMSUBADDPSZ128v231rr	= 4164,
    X86_VFMSUBADDPSZ128v231rrk	= 4165,
    X86_VFMSUBADDPSZ128v231rrkz	= 4166,
    X86_VFMSUBADDPSZ256v213rm	= 4167,
    X86_VFMSUBADDPSZ256v213rmb	= 4168,
    X86_VFMSUBADDPSZ256v213rmbk	= 4169,
    X86_VFMSUBADDPSZ256v213rmbkz	= 4170,
    X86_VFMSUBADDPSZ256v213rmk	= 4171,
    X86_VFMSUBADDPSZ256v213rmkz	= 4172,
    X86_VFMSUBADDPSZ256v213rr	= 4173,
    X86_VFMSUBADDPSZ256v213rrk	= 4174,
    X86_VFMSUBADDPSZ256v213rrkz	= 4175,
    X86_VFMSUBADDPSZ256v231rm	= 4176,
    X86_VFMSUBADDPSZ256v231rmb	= 4177,
    X86_VFMSUBADDPSZ256v231rmbk	= 4178,
    X86_VFMSUBADDPSZ256v231rmbkz	= 4179,
    X86_VFMSUBADDPSZ256v231rmk	= 4180,
    X86_VFMSUBADDPSZ256v231rmkz	= 4181,
    X86_VFMSUBADDPSZ256v231rr	= 4182,
    X86_VFMSUBADDPSZ256v231rrk	= 4183,
    X86_VFMSUBADDPSZ256v231rrkz	= 4184,
    X86_VFMSUBADDPSZv213rm	= 4185,
    X86_VFMSUBADDPSZv213rmb	= 4186,
    X86_VFMSUBADDPSZv213rmbk	= 4187,
    X86_VFMSUBADDPSZv213rmbkz	= 4188,
    X86_VFMSUBADDPSZv213rmk	= 4189,
    X86_VFMSUBADDPSZv213rmkz	= 4190,
    X86_VFMSUBADDPSZv213rr	= 4191,
    X86_VFMSUBADDPSZv213rrb	= 4192,
    X86_VFMSUBADDPSZv213rrbk	= 4193,
    X86_VFMSUBADDPSZv213rrbkz	= 4194,
    X86_VFMSUBADDPSZv213rrk	= 4195,
    X86_VFMSUBADDPSZv213rrkz	= 4196,
    X86_VFMSUBADDPSZv231rm	= 4197,
    X86_VFMSUBADDPSZv231rmb	= 4198,
    X86_VFMSUBADDPSZv231rmbk	= 4199,
    X86_VFMSUBADDPSZv231rmbkz	= 4200,
    X86_VFMSUBADDPSZv231rmk	= 4201,
    X86_VFMSUBADDPSZv231rmkz	= 4202,
    X86_VFMSUBADDPSZv231rr	= 4203,
    X86_VFMSUBADDPSZv231rrk	= 4204,
    X86_VFMSUBADDPSZv231rrkz	= 4205,
    X86_VFMSUBADDPSr132m	= 4206,
    X86_VFMSUBADDPSr132mY	= 4207,
    X86_VFMSUBADDPSr132r	= 4208,
    X86_VFMSUBADDPSr132rY	= 4209,
    X86_VFMSUBADDPSr213m	= 4210,
    X86_VFMSUBADDPSr213mY	= 4211,
    X86_VFMSUBADDPSr213r	= 4212,
    X86_VFMSUBADDPSr213rY	= 4213,
    X86_VFMSUBADDPSr231m	= 4214,
    X86_VFMSUBADDPSr231mY	= 4215,
    X86_VFMSUBADDPSr231r	= 4216,
    X86_VFMSUBADDPSr231rY	= 4217,
    X86_VFMSUBPD4mr	= 4218,
    X86_VFMSUBPD4mrY	= 4219,
    X86_VFMSUBPD4rm	= 4220,
    X86_VFMSUBPD4rmY	= 4221,
    X86_VFMSUBPD4rr	= 4222,
    X86_VFMSUBPD4rrY	= 4223,
    X86_VFMSUBPD4rrY_REV	= 4224,
    X86_VFMSUBPD4rr_REV	= 4225,
    X86_VFMSUBPDZ128v213rm	= 4226,
    X86_VFMSUBPDZ128v213rmb	= 4227,
    X86_VFMSUBPDZ128v213rmbk	= 4228,
    X86_VFMSUBPDZ128v213rmbkz	= 4229,
    X86_VFMSUBPDZ128v213rmk	= 4230,
    X86_VFMSUBPDZ128v213rmkz	= 4231,
    X86_VFMSUBPDZ128v213rr	= 4232,
    X86_VFMSUBPDZ128v213rrk	= 4233,
    X86_VFMSUBPDZ128v213rrkz	= 4234,
    X86_VFMSUBPDZ128v231rm	= 4235,
    X86_VFMSUBPDZ128v231rmb	= 4236,
    X86_VFMSUBPDZ128v231rmbk	= 4237,
    X86_VFMSUBPDZ128v231rmbkz	= 4238,
    X86_VFMSUBPDZ128v231rmk	= 4239,
    X86_VFMSUBPDZ128v231rmkz	= 4240,
    X86_VFMSUBPDZ128v231rr	= 4241,
    X86_VFMSUBPDZ128v231rrk	= 4242,
    X86_VFMSUBPDZ128v231rrkz	= 4243,
    X86_VFMSUBPDZ256v213rm	= 4244,
    X86_VFMSUBPDZ256v213rmb	= 4245,
    X86_VFMSUBPDZ256v213rmbk	= 4246,
    X86_VFMSUBPDZ256v213rmbkz	= 4247,
    X86_VFMSUBPDZ256v213rmk	= 4248,
    X86_VFMSUBPDZ256v213rmkz	= 4249,
    X86_VFMSUBPDZ256v213rr	= 4250,
    X86_VFMSUBPDZ256v213rrk	= 4251,
    X86_VFMSUBPDZ256v213rrkz	= 4252,
    X86_VFMSUBPDZ256v231rm	= 4253,
    X86_VFMSUBPDZ256v231rmb	= 4254,
    X86_VFMSUBPDZ256v231rmbk	= 4255,
    X86_VFMSUBPDZ256v231rmbkz	= 4256,
    X86_VFMSUBPDZ256v231rmk	= 4257,
    X86_VFMSUBPDZ256v231rmkz	= 4258,
    X86_VFMSUBPDZ256v231rr	= 4259,
    X86_VFMSUBPDZ256v231rrk	= 4260,
    X86_VFMSUBPDZ256v231rrkz	= 4261,
    X86_VFMSUBPDZv213rm	= 4262,
    X86_VFMSUBPDZv213rmb	= 4263,
    X86_VFMSUBPDZv213rmbk	= 4264,
    X86_VFMSUBPDZv213rmbkz	= 4265,
    X86_VFMSUBPDZv213rmk	= 4266,
    X86_VFMSUBPDZv213rmkz	= 4267,
    X86_VFMSUBPDZv213rr	= 4268,
    X86_VFMSUBPDZv213rrb	= 4269,
    X86_VFMSUBPDZv213rrbk	= 4270,
    X86_VFMSUBPDZv213rrbkz	= 4271,
    X86_VFMSUBPDZv213rrk	= 4272,
    X86_VFMSUBPDZv213rrkz	= 4273,
    X86_VFMSUBPDZv231rm	= 4274,
    X86_VFMSUBPDZv231rmb	= 4275,
    X86_VFMSUBPDZv231rmbk	= 4276,
    X86_VFMSUBPDZv231rmbkz	= 4277,
    X86_VFMSUBPDZv231rmk	= 4278,
    X86_VFMSUBPDZv231rmkz	= 4279,
    X86_VFMSUBPDZv231rr	= 4280,
    X86_VFMSUBPDZv231rrk	= 4281,
    X86_VFMSUBPDZv231rrkz	= 4282,
    X86_VFMSUBPDr132m	= 4283,
    X86_VFMSUBPDr132mY	= 4284,
    X86_VFMSUBPDr132r	= 4285,
    X86_VFMSUBPDr132rY	= 4286,
    X86_VFMSUBPDr213m	= 4287,
    X86_VFMSUBPDr213mY	= 4288,
    X86_VFMSUBPDr213r	= 4289,
    X86_VFMSUBPDr213rY	= 4290,
    X86_VFMSUBPDr231m	= 4291,
    X86_VFMSUBPDr231mY	= 4292,
    X86_VFMSUBPDr231r	= 4293,
    X86_VFMSUBPDr231rY	= 4294,
    X86_VFMSUBPS4mr	= 4295,
    X86_VFMSUBPS4mrY	= 4296,
    X86_VFMSUBPS4rm	= 4297,
    X86_VFMSUBPS4rmY	= 4298,
    X86_VFMSUBPS4rr	= 4299,
    X86_VFMSUBPS4rrY	= 4300,
    X86_VFMSUBPS4rrY_REV	= 4301,
    X86_VFMSUBPS4rr_REV	= 4302,
    X86_VFMSUBPSZ128v213rm	= 4303,
    X86_VFMSUBPSZ128v213rmb	= 4304,
    X86_VFMSUBPSZ128v213rmbk	= 4305,
    X86_VFMSUBPSZ128v213rmbkz	= 4306,
    X86_VFMSUBPSZ128v213rmk	= 4307,
    X86_VFMSUBPSZ128v213rmkz	= 4308,
    X86_VFMSUBPSZ128v213rr	= 4309,
    X86_VFMSUBPSZ128v213rrk	= 4310,
    X86_VFMSUBPSZ128v213rrkz	= 4311,
    X86_VFMSUBPSZ128v231rm	= 4312,
    X86_VFMSUBPSZ128v231rmb	= 4313,
    X86_VFMSUBPSZ128v231rmbk	= 4314,
    X86_VFMSUBPSZ128v231rmbkz	= 4315,
    X86_VFMSUBPSZ128v231rmk	= 4316,
    X86_VFMSUBPSZ128v231rmkz	= 4317,
    X86_VFMSUBPSZ128v231rr	= 4318,
    X86_VFMSUBPSZ128v231rrk	= 4319,
    X86_VFMSUBPSZ128v231rrkz	= 4320,
    X86_VFMSUBPSZ256v213rm	= 4321,
    X86_VFMSUBPSZ256v213rmb	= 4322,
    X86_VFMSUBPSZ256v213rmbk	= 4323,
    X86_VFMSUBPSZ256v213rmbkz	= 4324,
    X86_VFMSUBPSZ256v213rmk	= 4325,
    X86_VFMSUBPSZ256v213rmkz	= 4326,
    X86_VFMSUBPSZ256v213rr	= 4327,
    X86_VFMSUBPSZ256v213rrk	= 4328,
    X86_VFMSUBPSZ256v213rrkz	= 4329,
    X86_VFMSUBPSZ256v231rm	= 4330,
    X86_VFMSUBPSZ256v231rmb	= 4331,
    X86_VFMSUBPSZ256v231rmbk	= 4332,
    X86_VFMSUBPSZ256v231rmbkz	= 4333,
    X86_VFMSUBPSZ256v231rmk	= 4334,
    X86_VFMSUBPSZ256v231rmkz	= 4335,
    X86_VFMSUBPSZ256v231rr	= 4336,
    X86_VFMSUBPSZ256v231rrk	= 4337,
    X86_VFMSUBPSZ256v231rrkz	= 4338,
    X86_VFMSUBPSZv213rm	= 4339,
    X86_VFMSUBPSZv213rmb	= 4340,
    X86_VFMSUBPSZv213rmbk	= 4341,
    X86_VFMSUBPSZv213rmbkz	= 4342,
    X86_VFMSUBPSZv213rmk	= 4343,
    X86_VFMSUBPSZv213rmkz	= 4344,
    X86_VFMSUBPSZv213rr	= 4345,
    X86_VFMSUBPSZv213rrb	= 4346,
    X86_VFMSUBPSZv213rrbk	= 4347,
    X86_VFMSUBPSZv213rrbkz	= 4348,
    X86_VFMSUBPSZv213rrk	= 4349,
    X86_VFMSUBPSZv213rrkz	= 4350,
    X86_VFMSUBPSZv231rm	= 4351,
    X86_VFMSUBPSZv231rmb	= 4352,
    X86_VFMSUBPSZv231rmbk	= 4353,
    X86_VFMSUBPSZv231rmbkz	= 4354,
    X86_VFMSUBPSZv231rmk	= 4355,
    X86_VFMSUBPSZv231rmkz	= 4356,
    X86_VFMSUBPSZv231rr	= 4357,
    X86_VFMSUBPSZv231rrk	= 4358,
    X86_VFMSUBPSZv231rrkz	= 4359,
    X86_VFMSUBPSr132m	= 4360,
    X86_VFMSUBPSr132mY	= 4361,
    X86_VFMSUBPSr132r	= 4362,
    X86_VFMSUBPSr132rY	= 4363,
    X86_VFMSUBPSr213m	= 4364,
    X86_VFMSUBPSr213mY	= 4365,
    X86_VFMSUBPSr213r	= 4366,
    X86_VFMSUBPSr213rY	= 4367,
    X86_VFMSUBPSr231m	= 4368,
    X86_VFMSUBPSr231mY	= 4369,
    X86_VFMSUBPSr231r	= 4370,
    X86_VFMSUBPSr231rY	= 4371,
    X86_VFMSUBSD4mr	= 4372,
    X86_VFMSUBSD4mr_Int	= 4373,
    X86_VFMSUBSD4rm	= 4374,
    X86_VFMSUBSD4rm_Int	= 4375,
    X86_VFMSUBSD4rr	= 4376,
    X86_VFMSUBSD4rr_Int	= 4377,
    X86_VFMSUBSD4rr_REV	= 4378,
    X86_VFMSUBSDZm	= 4379,
    X86_VFMSUBSDZr	= 4380,
    X86_VFMSUBSDr132m	= 4381,
    X86_VFMSUBSDr132r	= 4382,
    X86_VFMSUBSDr213m	= 4383,
    X86_VFMSUBSDr213r	= 4384,
    X86_VFMSUBSDr231m	= 4385,
    X86_VFMSUBSDr231r	= 4386,
    X86_VFMSUBSS4mr	= 4387,
    X86_VFMSUBSS4mr_Int	= 4388,
    X86_VFMSUBSS4rm	= 4389,
    X86_VFMSUBSS4rm_Int	= 4390,
    X86_VFMSUBSS4rr	= 4391,
    X86_VFMSUBSS4rr_Int	= 4392,
    X86_VFMSUBSS4rr_REV	= 4393,
    X86_VFMSUBSSZm	= 4394,
    X86_VFMSUBSSZr	= 4395,
    X86_VFMSUBSSr132m	= 4396,
    X86_VFMSUBSSr132r	= 4397,
    X86_VFMSUBSSr213m	= 4398,
    X86_VFMSUBSSr213r	= 4399,
    X86_VFMSUBSSr231m	= 4400,
    X86_VFMSUBSSr231r	= 4401,
    X86_VFNMADD132PDZ128m	= 4402,
    X86_VFNMADD132PDZ128mb	= 4403,
    X86_VFNMADD132PDZ256m	= 4404,
    X86_VFNMADD132PDZ256mb	= 4405,
    X86_VFNMADD132PDZm	= 4406,
    X86_VFNMADD132PDZmb	= 4407,
    X86_VFNMADD132PSZ128m	= 4408,
    X86_VFNMADD132PSZ128mb	= 4409,
    X86_VFNMADD132PSZ256m	= 4410,
    X86_VFNMADD132PSZ256mb	= 4411,
    X86_VFNMADD132PSZm	= 4412,
    X86_VFNMADD132PSZmb	= 4413,
    X86_VFNMADDPD4mr	= 4414,
    X86_VFNMADDPD4mrY	= 4415,
    X86_VFNMADDPD4rm	= 4416,
    X86_VFNMADDPD4rmY	= 4417,
    X86_VFNMADDPD4rr	= 4418,
    X86_VFNMADDPD4rrY	= 4419,
    X86_VFNMADDPD4rrY_REV	= 4420,
    X86_VFNMADDPD4rr_REV	= 4421,
    X86_VFNMADDPDZ128v213rm	= 4422,
    X86_VFNMADDPDZ128v213rmb	= 4423,
    X86_VFNMADDPDZ128v213rmbk	= 4424,
    X86_VFNMADDPDZ128v213rmbkz	= 4425,
    X86_VFNMADDPDZ128v213rmk	= 4426,
    X86_VFNMADDPDZ128v213rmkz	= 4427,
    X86_VFNMADDPDZ128v213rr	= 4428,
    X86_VFNMADDPDZ128v213rrk	= 4429,
    X86_VFNMADDPDZ128v213rrkz	= 4430,
    X86_VFNMADDPDZ128v231rm	= 4431,
    X86_VFNMADDPDZ128v231rmb	= 4432,
    X86_VFNMADDPDZ128v231rmbk	= 4433,
    X86_VFNMADDPDZ128v231rmbkz	= 4434,
    X86_VFNMADDPDZ128v231rmk	= 4435,
    X86_VFNMADDPDZ128v231rmkz	= 4436,
    X86_VFNMADDPDZ128v231rr	= 4437,
    X86_VFNMADDPDZ128v231rrk	= 4438,
    X86_VFNMADDPDZ128v231rrkz	= 4439,
    X86_VFNMADDPDZ256v213rm	= 4440,
    X86_VFNMADDPDZ256v213rmb	= 4441,
    X86_VFNMADDPDZ256v213rmbk	= 4442,
    X86_VFNMADDPDZ256v213rmbkz	= 4443,
    X86_VFNMADDPDZ256v213rmk	= 4444,
    X86_VFNMADDPDZ256v213rmkz	= 4445,
    X86_VFNMADDPDZ256v213rr	= 4446,
    X86_VFNMADDPDZ256v213rrk	= 4447,
    X86_VFNMADDPDZ256v213rrkz	= 4448,
    X86_VFNMADDPDZ256v231rm	= 4449,
    X86_VFNMADDPDZ256v231rmb	= 4450,
    X86_VFNMADDPDZ256v231rmbk	= 4451,
    X86_VFNMADDPDZ256v231rmbkz	= 4452,
    X86_VFNMADDPDZ256v231rmk	= 4453,
    X86_VFNMADDPDZ256v231rmkz	= 4454,
    X86_VFNMADDPDZ256v231rr	= 4455,
    X86_VFNMADDPDZ256v231rrk	= 4456,
    X86_VFNMADDPDZ256v231rrkz	= 4457,
    X86_VFNMADDPDZv213rm	= 4458,
    X86_VFNMADDPDZv213rmb	= 4459,
    X86_VFNMADDPDZv213rmbk	= 4460,
    X86_VFNMADDPDZv213rmbkz	= 4461,
    X86_VFNMADDPDZv213rmk	= 4462,
    X86_VFNMADDPDZv213rmkz	= 4463,
    X86_VFNMADDPDZv213rr	= 4464,
    X86_VFNMADDPDZv213rrb	= 4465,
    X86_VFNMADDPDZv213rrbk	= 4466,
    X86_VFNMADDPDZv213rrbkz	= 4467,
    X86_VFNMADDPDZv213rrk	= 4468,
    X86_VFNMADDPDZv213rrkz	= 4469,
    X86_VFNMADDPDZv231rm	= 4470,
    X86_VFNMADDPDZv231rmb	= 4471,
    X86_VFNMADDPDZv231rmbk	= 4472,
    X86_VFNMADDPDZv231rmbkz	= 4473,
    X86_VFNMADDPDZv231rmk	= 4474,
    X86_VFNMADDPDZv231rmkz	= 4475,
    X86_VFNMADDPDZv231rr	= 4476,
    X86_VFNMADDPDZv231rrk	= 4477,
    X86_VFNMADDPDZv231rrkz	= 4478,
    X86_VFNMADDPDr132m	= 4479,
    X86_VFNMADDPDr132mY	= 4480,
    X86_VFNMADDPDr132r	= 4481,
    X86_VFNMADDPDr132rY	= 4482,
    X86_VFNMADDPDr213m	= 4483,
    X86_VFNMADDPDr213mY	= 4484,
    X86_VFNMADDPDr213r	= 4485,
    X86_VFNMADDPDr213rY	= 4486,
    X86_VFNMADDPDr231m	= 4487,
    X86_VFNMADDPDr231mY	= 4488,
    X86_VFNMADDPDr231r	= 4489,
    X86_VFNMADDPDr231rY	= 4490,
    X86_VFNMADDPS4mr	= 4491,
    X86_VFNMADDPS4mrY	= 4492,
    X86_VFNMADDPS4rm	= 4493,
    X86_VFNMADDPS4rmY	= 4494,
    X86_VFNMADDPS4rr	= 4495,
    X86_VFNMADDPS4rrY	= 4496,
    X86_VFNMADDPS4rrY_REV	= 4497,
    X86_VFNMADDPS4rr_REV	= 4498,
    X86_VFNMADDPSZ128v213rm	= 4499,
    X86_VFNMADDPSZ128v213rmb	= 4500,
    X86_VFNMADDPSZ128v213rmbk	= 4501,
    X86_VFNMADDPSZ128v213rmbkz	= 4502,
    X86_VFNMADDPSZ128v213rmk	= 4503,
    X86_VFNMADDPSZ128v213rmkz	= 4504,
    X86_VFNMADDPSZ128v213rr	= 4505,
    X86_VFNMADDPSZ128v213rrk	= 4506,
    X86_VFNMADDPSZ128v213rrkz	= 4507,
    X86_VFNMADDPSZ128v231rm	= 4508,
    X86_VFNMADDPSZ128v231rmb	= 4509,
    X86_VFNMADDPSZ128v231rmbk	= 4510,
    X86_VFNMADDPSZ128v231rmbkz	= 4511,
    X86_VFNMADDPSZ128v231rmk	= 4512,
    X86_VFNMADDPSZ128v231rmkz	= 4513,
    X86_VFNMADDPSZ128v231rr	= 4514,
    X86_VFNMADDPSZ128v231rrk	= 4515,
    X86_VFNMADDPSZ128v231rrkz	= 4516,
    X86_VFNMADDPSZ256v213rm	= 4517,
    X86_VFNMADDPSZ256v213rmb	= 4518,
    X86_VFNMADDPSZ256v213rmbk	= 4519,
    X86_VFNMADDPSZ256v213rmbkz	= 4520,
    X86_VFNMADDPSZ256v213rmk	= 4521,
    X86_VFNMADDPSZ256v213rmkz	= 4522,
    X86_VFNMADDPSZ256v213rr	= 4523,
    X86_VFNMADDPSZ256v213rrk	= 4524,
    X86_VFNMADDPSZ256v213rrkz	= 4525,
    X86_VFNMADDPSZ256v231rm	= 4526,
    X86_VFNMADDPSZ256v231rmb	= 4527,
    X86_VFNMADDPSZ256v231rmbk	= 4528,
    X86_VFNMADDPSZ256v231rmbkz	= 4529,
    X86_VFNMADDPSZ256v231rmk	= 4530,
    X86_VFNMADDPSZ256v231rmkz	= 4531,
    X86_VFNMADDPSZ256v231rr	= 4532,
    X86_VFNMADDPSZ256v231rrk	= 4533,
    X86_VFNMADDPSZ256v231rrkz	= 4534,
    X86_VFNMADDPSZv213rm	= 4535,
    X86_VFNMADDPSZv213rmb	= 4536,
    X86_VFNMADDPSZv213rmbk	= 4537,
    X86_VFNMADDPSZv213rmbkz	= 4538,
    X86_VFNMADDPSZv213rmk	= 4539,
    X86_VFNMADDPSZv213rmkz	= 4540,
    X86_VFNMADDPSZv213rr	= 4541,
    X86_VFNMADDPSZv213rrb	= 4542,
    X86_VFNMADDPSZv213rrbk	= 4543,
    X86_VFNMADDPSZv213rrbkz	= 4544,
    X86_VFNMADDPSZv213rrk	= 4545,
    X86_VFNMADDPSZv213rrkz	= 4546,
    X86_VFNMADDPSZv231rm	= 4547,
    X86_VFNMADDPSZv231rmb	= 4548,
    X86_VFNMADDPSZv231rmbk	= 4549,
    X86_VFNMADDPSZv231rmbkz	= 4550,
    X86_VFNMADDPSZv231rmk	= 4551,
    X86_VFNMADDPSZv231rmkz	= 4552,
    X86_VFNMADDPSZv231rr	= 4553,
    X86_VFNMADDPSZv231rrk	= 4554,
    X86_VFNMADDPSZv231rrkz	= 4555,
    X86_VFNMADDPSr132m	= 4556,
    X86_VFNMADDPSr132mY	= 4557,
    X86_VFNMADDPSr132r	= 4558,
    X86_VFNMADDPSr132rY	= 4559,
    X86_VFNMADDPSr213m	= 4560,
    X86_VFNMADDPSr213mY	= 4561,
    X86_VFNMADDPSr213r	= 4562,
    X86_VFNMADDPSr213rY	= 4563,
    X86_VFNMADDPSr231m	= 4564,
    X86_VFNMADDPSr231mY	= 4565,
    X86_VFNMADDPSr231r	= 4566,
    X86_VFNMADDPSr231rY	= 4567,
    X86_VFNMADDSD4mr	= 4568,
    X86_VFNMADDSD4mr_Int	= 4569,
    X86_VFNMADDSD4rm	= 4570,
    X86_VFNMADDSD4rm_Int	= 4571,
    X86_VFNMADDSD4rr	= 4572,
    X86_VFNMADDSD4rr_Int	= 4573,
    X86_VFNMADDSD4rr_REV	= 4574,
    X86_VFNMADDSDZm	= 4575,
    X86_VFNMADDSDZr	= 4576,
    X86_VFNMADDSDr132m	= 4577,
    X86_VFNMADDSDr132r	= 4578,
    X86_VFNMADDSDr213m	= 4579,
    X86_VFNMADDSDr213r	= 4580,
    X86_VFNMADDSDr231m	= 4581,
    X86_VFNMADDSDr231r	= 4582,
    X86_VFNMADDSS4mr	= 4583,
    X86_VFNMADDSS4mr_Int	= 4584,
    X86_VFNMADDSS4rm	= 4585,
    X86_VFNMADDSS4rm_Int	= 4586,
    X86_VFNMADDSS4rr	= 4587,
    X86_VFNMADDSS4rr_Int	= 4588,
    X86_VFNMADDSS4rr_REV	= 4589,
    X86_VFNMADDSSZm	= 4590,
    X86_VFNMADDSSZr	= 4591,
    X86_VFNMADDSSr132m	= 4592,
    X86_VFNMADDSSr132r	= 4593,
    X86_VFNMADDSSr213m	= 4594,
    X86_VFNMADDSSr213r	= 4595,
    X86_VFNMADDSSr231m	= 4596,
    X86_VFNMADDSSr231r	= 4597,
    X86_VFNMSUB132PDZ128m	= 4598,
    X86_VFNMSUB132PDZ128mb	= 4599,
    X86_VFNMSUB132PDZ256m	= 4600,
    X86_VFNMSUB132PDZ256mb	= 4601,
    X86_VFNMSUB132PDZm	= 4602,
    X86_VFNMSUB132PDZmb	= 4603,
    X86_VFNMSUB132PSZ128m	= 4604,
    X86_VFNMSUB132PSZ128mb	= 4605,
    X86_VFNMSUB132PSZ256m	= 4606,
    X86_VFNMSUB132PSZ256mb	= 4607,
    X86_VFNMSUB132PSZm	= 4608,
    X86_VFNMSUB132PSZmb	= 4609,
    X86_VFNMSUBPD4mr	= 4610,
    X86_VFNMSUBPD4mrY	= 4611,
    X86_VFNMSUBPD4rm	= 4612,
    X86_VFNMSUBPD4rmY	= 4613,
    X86_VFNMSUBPD4rr	= 4614,
    X86_VFNMSUBPD4rrY	= 4615,
    X86_VFNMSUBPD4rrY_REV	= 4616,
    X86_VFNMSUBPD4rr_REV	= 4617,
    X86_VFNMSUBPDZ128v213rm	= 4618,
    X86_VFNMSUBPDZ128v213rmb	= 4619,
    X86_VFNMSUBPDZ128v213rmbk	= 4620,
    X86_VFNMSUBPDZ128v213rmbkz	= 4621,
    X86_VFNMSUBPDZ128v213rmk	= 4622,
    X86_VFNMSUBPDZ128v213rmkz	= 4623,
    X86_VFNMSUBPDZ128v213rr	= 4624,
    X86_VFNMSUBPDZ128v213rrk	= 4625,
    X86_VFNMSUBPDZ128v213rrkz	= 4626,
    X86_VFNMSUBPDZ128v231rm	= 4627,
    X86_VFNMSUBPDZ128v231rmb	= 4628,
    X86_VFNMSUBPDZ128v231rmbk	= 4629,
    X86_VFNMSUBPDZ128v231rmbkz	= 4630,
    X86_VFNMSUBPDZ128v231rmk	= 4631,
    X86_VFNMSUBPDZ128v231rmkz	= 4632,
    X86_VFNMSUBPDZ128v231rr	= 4633,
    X86_VFNMSUBPDZ128v231rrk	= 4634,
    X86_VFNMSUBPDZ128v231rrkz	= 4635,
    X86_VFNMSUBPDZ256v213rm	= 4636,
    X86_VFNMSUBPDZ256v213rmb	= 4637,
    X86_VFNMSUBPDZ256v213rmbk	= 4638,
    X86_VFNMSUBPDZ256v213rmbkz	= 4639,
    X86_VFNMSUBPDZ256v213rmk	= 4640,
    X86_VFNMSUBPDZ256v213rmkz	= 4641,
    X86_VFNMSUBPDZ256v213rr	= 4642,
    X86_VFNMSUBPDZ256v213rrk	= 4643,
    X86_VFNMSUBPDZ256v213rrkz	= 4644,
    X86_VFNMSUBPDZ256v231rm	= 4645,
    X86_VFNMSUBPDZ256v231rmb	= 4646,
    X86_VFNMSUBPDZ256v231rmbk	= 4647,
    X86_VFNMSUBPDZ256v231rmbkz	= 4648,
    X86_VFNMSUBPDZ256v231rmk	= 4649,
    X86_VFNMSUBPDZ256v231rmkz	= 4650,
    X86_VFNMSUBPDZ256v231rr	= 4651,
    X86_VFNMSUBPDZ256v231rrk	= 4652,
    X86_VFNMSUBPDZ256v231rrkz	= 4653,
    X86_VFNMSUBPDZv213rm	= 4654,
    X86_VFNMSUBPDZv213rmb	= 4655,
    X86_VFNMSUBPDZv213rmbk	= 4656,
    X86_VFNMSUBPDZv213rmbkz	= 4657,
    X86_VFNMSUBPDZv213rmk	= 4658,
    X86_VFNMSUBPDZv213rmkz	= 4659,
    X86_VFNMSUBPDZv213rr	= 4660,
    X86_VFNMSUBPDZv213rrb	= 4661,
    X86_VFNMSUBPDZv213rrbk	= 4662,
    X86_VFNMSUBPDZv213rrbkz	= 4663,
    X86_VFNMSUBPDZv213rrk	= 4664,
    X86_VFNMSUBPDZv213rrkz	= 4665,
    X86_VFNMSUBPDZv231rm	= 4666,
    X86_VFNMSUBPDZv231rmb	= 4667,
    X86_VFNMSUBPDZv231rmbk	= 4668,
    X86_VFNMSUBPDZv231rmbkz	= 4669,
    X86_VFNMSUBPDZv231rmk	= 4670,
    X86_VFNMSUBPDZv231rmkz	= 4671,
    X86_VFNMSUBPDZv231rr	= 4672,
    X86_VFNMSUBPDZv231rrk	= 4673,
    X86_VFNMSUBPDZv231rrkz	= 4674,
    X86_VFNMSUBPDr132m	= 4675,
    X86_VFNMSUBPDr132mY	= 4676,
    X86_VFNMSUBPDr132r	= 4677,
    X86_VFNMSUBPDr132rY	= 4678,
    X86_VFNMSUBPDr213m	= 4679,
    X86_VFNMSUBPDr213mY	= 4680,
    X86_VFNMSUBPDr213r	= 4681,
    X86_VFNMSUBPDr213rY	= 4682,
    X86_VFNMSUBPDr231m	= 4683,
    X86_VFNMSUBPDr231mY	= 4684,
    X86_VFNMSUBPDr231r	= 4685,
    X86_VFNMSUBPDr231rY	= 4686,
    X86_VFNMSUBPS4mr	= 4687,
    X86_VFNMSUBPS4mrY	= 4688,
    X86_VFNMSUBPS4rm	= 4689,
    X86_VFNMSUBPS4rmY	= 4690,
    X86_VFNMSUBPS4rr	= 4691,
    X86_VFNMSUBPS4rrY	= 4692,
    X86_VFNMSUBPS4rrY_REV	= 4693,
    X86_VFNMSUBPS4rr_REV	= 4694,
    X86_VFNMSUBPSZ128v213rm	= 4695,
    X86_VFNMSUBPSZ128v213rmb	= 4696,
    X86_VFNMSUBPSZ128v213rmbk	= 4697,
    X86_VFNMSUBPSZ128v213rmbkz	= 4698,
    X86_VFNMSUBPSZ128v213rmk	= 4699,
    X86_VFNMSUBPSZ128v213rmkz	= 4700,
    X86_VFNMSUBPSZ128v213rr	= 4701,
    X86_VFNMSUBPSZ128v213rrk	= 4702,
    X86_VFNMSUBPSZ128v213rrkz	= 4703,
    X86_VFNMSUBPSZ128v231rm	= 4704,
    X86_VFNMSUBPSZ128v231rmb	= 4705,
    X86_VFNMSUBPSZ128v231rmbk	= 4706,
    X86_VFNMSUBPSZ128v231rmbkz	= 4707,
    X86_VFNMSUBPSZ128v231rmk	= 4708,
    X86_VFNMSUBPSZ128v231rmkz	= 4709,
    X86_VFNMSUBPSZ128v231rr	= 4710,
    X86_VFNMSUBPSZ128v231rrk	= 4711,
    X86_VFNMSUBPSZ128v231rrkz	= 4712,
    X86_VFNMSUBPSZ256v213rm	= 4713,
    X86_VFNMSUBPSZ256v213rmb	= 4714,
    X86_VFNMSUBPSZ256v213rmbk	= 4715,
    X86_VFNMSUBPSZ256v213rmbkz	= 4716,
    X86_VFNMSUBPSZ256v213rmk	= 4717,
    X86_VFNMSUBPSZ256v213rmkz	= 4718,
    X86_VFNMSUBPSZ256v213rr	= 4719,
    X86_VFNMSUBPSZ256v213rrk	= 4720,
    X86_VFNMSUBPSZ256v213rrkz	= 4721,
    X86_VFNMSUBPSZ256v231rm	= 4722,
    X86_VFNMSUBPSZ256v231rmb	= 4723,
    X86_VFNMSUBPSZ256v231rmbk	= 4724,
    X86_VFNMSUBPSZ256v231rmbkz	= 4725,
    X86_VFNMSUBPSZ256v231rmk	= 4726,
    X86_VFNMSUBPSZ256v231rmkz	= 4727,
    X86_VFNMSUBPSZ256v231rr	= 4728,
    X86_VFNMSUBPSZ256v231rrk	= 4729,
    X86_VFNMSUBPSZ256v231rrkz	= 4730,
    X86_VFNMSUBPSZv213rm	= 4731,
    X86_VFNMSUBPSZv213rmb	= 4732,
    X86_VFNMSUBPSZv213rmbk	= 4733,
    X86_VFNMSUBPSZv213rmbkz	= 4734,
    X86_VFNMSUBPSZv213rmk	= 4735,
    X86_VFNMSUBPSZv213rmkz	= 4736,
    X86_VFNMSUBPSZv213rr	= 4737,
    X86_VFNMSUBPSZv213rrb	= 4738,
    X86_VFNMSUBPSZv213rrbk	= 4739,
    X86_VFNMSUBPSZv213rrbkz	= 4740,
    X86_VFNMSUBPSZv213rrk	= 4741,
    X86_VFNMSUBPSZv213rrkz	= 4742,
    X86_VFNMSUBPSZv231rm	= 4743,
    X86_VFNMSUBPSZv231rmb	= 4744,
    X86_VFNMSUBPSZv231rmbk	= 4745,
    X86_VFNMSUBPSZv231rmbkz	= 4746,
    X86_VFNMSUBPSZv231rmk	= 4747,
    X86_VFNMSUBPSZv231rmkz	= 4748,
    X86_VFNMSUBPSZv231rr	= 4749,
    X86_VFNMSUBPSZv231rrk	= 4750,
    X86_VFNMSUBPSZv231rrkz	= 4751,
    X86_VFNMSUBPSr132m	= 4752,
    X86_VFNMSUBPSr132mY	= 4753,
    X86_VFNMSUBPSr132r	= 4754,
    X86_VFNMSUBPSr132rY	= 4755,
    X86_VFNMSUBPSr213m	= 4756,
    X86_VFNMSUBPSr213mY	= 4757,
    X86_VFNMSUBPSr213r	= 4758,
    X86_VFNMSUBPSr213rY	= 4759,
    X86_VFNMSUBPSr231m	= 4760,
    X86_VFNMSUBPSr231mY	= 4761,
    X86_VFNMSUBPSr231r	= 4762,
    X86_VFNMSUBPSr231rY	= 4763,
    X86_VFNMSUBSD4mr	= 4764,
    X86_VFNMSUBSD4mr_Int	= 4765,
    X86_VFNMSUBSD4rm	= 4766,
    X86_VFNMSUBSD4rm_Int	= 4767,
    X86_VFNMSUBSD4rr	= 4768,
    X86_VFNMSUBSD4rr_Int	= 4769,
    X86_VFNMSUBSD4rr_REV	= 4770,
    X86_VFNMSUBSDZm	= 4771,
    X86_VFNMSUBSDZr	= 4772,
    X86_VFNMSUBSDr132m	= 4773,
    X86_VFNMSUBSDr132r	= 4774,
    X86_VFNMSUBSDr213m	= 4775,
    X86_VFNMSUBSDr213r	= 4776,
    X86_VFNMSUBSDr231m	= 4777,
    X86_VFNMSUBSDr231r	= 4778,
    X86_VFNMSUBSS4mr	= 4779,
    X86_VFNMSUBSS4mr_Int	= 4780,
    X86_VFNMSUBSS4rm	= 4781,
    X86_VFNMSUBSS4rm_Int	= 4782,
    X86_VFNMSUBSS4rr	= 4783,
    X86_VFNMSUBSS4rr_Int	= 4784,
    X86_VFNMSUBSS4rr_REV	= 4785,
    X86_VFNMSUBSSZm	= 4786,
    X86_VFNMSUBSSZr	= 4787,
    X86_VFNMSUBSSr132m	= 4788,
    X86_VFNMSUBSSr132r	= 4789,
    X86_VFNMSUBSSr213m	= 4790,
    X86_VFNMSUBSSr213r	= 4791,
    X86_VFNMSUBSSr231m	= 4792,
    X86_VFNMSUBSSr231r	= 4793,
    X86_VFRCZPDrm	= 4794,
    X86_VFRCZPDrmY	= 4795,
    X86_VFRCZPDrr	= 4796,
    X86_VFRCZPDrrY	= 4797,
    X86_VFRCZPSrm	= 4798,
    X86_VFRCZPSrmY	= 4799,
    X86_VFRCZPSrr	= 4800,
    X86_VFRCZPSrrY	= 4801,
    X86_VFRCZSDrm	= 4802,
    X86_VFRCZSDrr	= 4803,
    X86_VFRCZSSrm	= 4804,
    X86_VFRCZSSrr	= 4805,
    X86_VFsANDNPDrm	= 4806,
    X86_VFsANDNPDrr	= 4807,
    X86_VFsANDNPSrm	= 4808,
    X86_VFsANDNPSrr	= 4809,
    X86_VFsANDPDrm	= 4810,
    X86_VFsANDPDrr	= 4811,
    X86_VFsANDPSrm	= 4812,
    X86_VFsANDPSrr	= 4813,
    X86_VFsORPDrm	= 4814,
    X86_VFsORPDrr	= 4815,
    X86_VFsORPSrm	= 4816,
    X86_VFsORPSrr	= 4817,
    X86_VFsXORPDrm	= 4818,
    X86_VFsXORPDrr	= 4819,
    X86_VFsXORPSrm	= 4820,
    X86_VFsXORPSrr	= 4821,
    X86_VFvANDNPDrm	= 4822,
    X86_VFvANDNPDrr	= 4823,
    X86_VFvANDNPSrm	= 4824,
    X86_VFvANDNPSrr	= 4825,
    X86_VFvANDPDrm	= 4826,
    X86_VFvANDPDrr	= 4827,
    X86_VFvANDPSrm	= 4828,
    X86_VFvANDPSrr	= 4829,
    X86_VFvORPDrm	= 4830,
    X86_VFvORPDrr	= 4831,
    X86_VFvORPSrm	= 4832,
    X86_VFvORPSrr	= 4833,
    X86_VFvXORPDrm	= 4834,
    X86_VFvXORPDrr	= 4835,
    X86_VFvXORPSrm	= 4836,
    X86_VFvXORPSrr	= 4837,
    X86_VGATHERDPDYrm	= 4838,
    X86_VGATHERDPDZrm	= 4839,
    X86_VGATHERDPDrm	= 4840,
    X86_VGATHERDPSYrm	= 4841,
    X86_VGATHERDPSZrm	= 4842,
    X86_VGATHERDPSrm	= 4843,
    X86_VGATHERPF0DPDm	= 4844,
    X86_VGATHERPF0DPSm	= 4845,
    X86_VGATHERPF0QPDm	= 4846,
    X86_VGATHERPF0QPSm	= 4847,
    X86_VGATHERPF1DPDm	= 4848,
    X86_VGATHERPF1DPSm	= 4849,
    X86_VGATHERPF1QPDm	= 4850,
    X86_VGATHERPF1QPSm	= 4851,
    X86_VGATHERQPDYrm	= 4852,
    X86_VGATHERQPDZrm	= 4853,
    X86_VGATHERQPDrm	= 4854,
    X86_VGATHERQPSYrm	= 4855,
    X86_VGATHERQPSZrm	= 4856,
    X86_VGATHERQPSrm	= 4857,
    X86_VHADDPDYrm	= 4858,
    X86_VHADDPDYrr	= 4859,
    X86_VHADDPDrm	= 4860,
    X86_VHADDPDrr	= 4861,
    X86_VHADDPSYrm	= 4862,
    X86_VHADDPSYrr	= 4863,
    X86_VHADDPSrm	= 4864,
    X86_VHADDPSrr	= 4865,
    X86_VHSUBPDYrm	= 4866,
    X86_VHSUBPDYrr	= 4867,
    X86_VHSUBPDrm	= 4868,
    X86_VHSUBPDrr	= 4869,
    X86_VHSUBPSYrm	= 4870,
    X86_VHSUBPSYrr	= 4871,
    X86_VHSUBPSrm	= 4872,
    X86_VHSUBPSrr	= 4873,
    X86_VINSERTF128rm	= 4874,
    X86_VINSERTF128rr	= 4875,
    X86_VINSERTF32x4rm	= 4876,
    X86_VINSERTF32x4rr	= 4877,
    X86_VINSERTF32x8rm	= 4878,
    X86_VINSERTF32x8rr	= 4879,
    X86_VINSERTF64x2rm	= 4880,
    X86_VINSERTF64x2rr	= 4881,
    X86_VINSERTF64x4rm	= 4882,
    X86_VINSERTF64x4rr	= 4883,
    X86_VINSERTI128rm	= 4884,
    X86_VINSERTI128rr	= 4885,
    X86_VINSERTI32x4rm	= 4886,
    X86_VINSERTI32x4rr	= 4887,
    X86_VINSERTI32x8rm	= 4888,
    X86_VINSERTI32x8rr	= 4889,
    X86_VINSERTI64x2rm	= 4890,
    X86_VINSERTI64x2rr	= 4891,
    X86_VINSERTI64x4rm	= 4892,
    X86_VINSERTI64x4rr	= 4893,
    X86_VINSERTPSrm	= 4894,
    X86_VINSERTPSrr	= 4895,
    X86_VINSERTPSzrm	= 4896,
    X86_VINSERTPSzrr	= 4897,
    X86_VLDDQUYrm	= 4898,
    X86_VLDDQUrm	= 4899,
    X86_VLDMXCSR	= 4900,
    X86_VMASKMOVDQU	= 4901,
    X86_VMASKMOVDQU64	= 4902,
    X86_VMASKMOVPDYmr	= 4903,
    X86_VMASKMOVPDYrm	= 4904,
    X86_VMASKMOVPDmr	= 4905,
    X86_VMASKMOVPDrm	= 4906,
    X86_VMASKMOVPSYmr	= 4907,
    X86_VMASKMOVPSYrm	= 4908,
    X86_VMASKMOVPSmr	= 4909,
    X86_VMASKMOVPSrm	= 4910,
    X86_VMAXCPDYrm	= 4911,
    X86_VMAXCPDYrr	= 4912,
    X86_VMAXCPDrm	= 4913,
    X86_VMAXCPDrr	= 4914,
    X86_VMAXCPSYrm	= 4915,
    X86_VMAXCPSYrr	= 4916,
    X86_VMAXCPSrm	= 4917,
    X86_VMAXCPSrr	= 4918,
    X86_VMAXCSDrm	= 4919,
    X86_VMAXCSDrr	= 4920,
    X86_VMAXCSSrm	= 4921,
    X86_VMAXCSSrr	= 4922,
    X86_VMAXPDYrm	= 4923,
    X86_VMAXPDYrr	= 4924,
    X86_VMAXPDZ128rm	= 4925,
    X86_VMAXPDZ128rmb	= 4926,
    X86_VMAXPDZ128rmbk	= 4927,
    X86_VMAXPDZ128rmbkz	= 4928,
    X86_VMAXPDZ128rmk	= 4929,
    X86_VMAXPDZ128rmkz	= 4930,
    X86_VMAXPDZ128rr	= 4931,
    X86_VMAXPDZ128rrk	= 4932,
    X86_VMAXPDZ128rrkz	= 4933,
    X86_VMAXPDZ256rm	= 4934,
    X86_VMAXPDZ256rmb	= 4935,
    X86_VMAXPDZ256rmbk	= 4936,
    X86_VMAXPDZ256rmbkz	= 4937,
    X86_VMAXPDZ256rmk	= 4938,
    X86_VMAXPDZ256rmkz	= 4939,
    X86_VMAXPDZ256rr	= 4940,
    X86_VMAXPDZ256rrk	= 4941,
    X86_VMAXPDZ256rrkz	= 4942,
    X86_VMAXPDZrm	= 4943,
    X86_VMAXPDZrmb	= 4944,
    X86_VMAXPDZrmbk	= 4945,
    X86_VMAXPDZrmbkz	= 4946,
    X86_VMAXPDZrmk	= 4947,
    X86_VMAXPDZrmkz	= 4948,
    X86_VMAXPDZrr	= 4949,
    X86_VMAXPDZrrk	= 4950,
    X86_VMAXPDZrrkz	= 4951,
    X86_VMAXPDrm	= 4952,
    X86_VMAXPDrr	= 4953,
    X86_VMAXPSYrm	= 4954,
    X86_VMAXPSYrr	= 4955,
    X86_VMAXPSZ128rm	= 4956,
    X86_VMAXPSZ128rmb	= 4957,
    X86_VMAXPSZ128rmbk	= 4958,
    X86_VMAXPSZ128rmbkz	= 4959,
    X86_VMAXPSZ128rmk	= 4960,
    X86_VMAXPSZ128rmkz	= 4961,
    X86_VMAXPSZ128rr	= 4962,
    X86_VMAXPSZ128rrk	= 4963,
    X86_VMAXPSZ128rrkz	= 4964,
    X86_VMAXPSZ256rm	= 4965,
    X86_VMAXPSZ256rmb	= 4966,
    X86_VMAXPSZ256rmbk	= 4967,
    X86_VMAXPSZ256rmbkz	= 4968,
    X86_VMAXPSZ256rmk	= 4969,
    X86_VMAXPSZ256rmkz	= 4970,
    X86_VMAXPSZ256rr	= 4971,
    X86_VMAXPSZ256rrk	= 4972,
    X86_VMAXPSZ256rrkz	= 4973,
    X86_VMAXPSZrm	= 4974,
    X86_VMAXPSZrmb	= 4975,
    X86_VMAXPSZrmbk	= 4976,
    X86_VMAXPSZrmbkz	= 4977,
    X86_VMAXPSZrmk	= 4978,
    X86_VMAXPSZrmkz	= 4979,
    X86_VMAXPSZrr	= 4980,
    X86_VMAXPSZrrk	= 4981,
    X86_VMAXPSZrrkz	= 4982,
    X86_VMAXPSrm	= 4983,
    X86_VMAXPSrr	= 4984,
    X86_VMAXSDZrm	= 4985,
    X86_VMAXSDZrm_Int	= 4986,
    X86_VMAXSDZrm_Intk	= 4987,
    X86_VMAXSDZrm_Intkz	= 4988,
    X86_VMAXSDZrr	= 4989,
    X86_VMAXSDZrr_Int	= 4990,
    X86_VMAXSDZrr_Intk	= 4991,
    X86_VMAXSDZrr_Intkz	= 4992,
    X86_VMAXSDZrrb	= 4993,
    X86_VMAXSDZrrbk	= 4994,
    X86_VMAXSDZrrbkz	= 4995,
    X86_VMAXSDrm	= 4996,
    X86_VMAXSDrm_Int	= 4997,
    X86_VMAXSDrr	= 4998,
    X86_VMAXSDrr_Int	= 4999,
    X86_VMAXSSZrm	= 5000,
    X86_VMAXSSZrm_Int	= 5001,
    X86_VMAXSSZrm_Intk	= 5002,
    X86_VMAXSSZrm_Intkz	= 5003,
    X86_VMAXSSZrr	= 5004,
    X86_VMAXSSZrr_Int	= 5005,
    X86_VMAXSSZrr_Intk	= 5006,
    X86_VMAXSSZrr_Intkz	= 5007,
    X86_VMAXSSZrrb	= 5008,
    X86_VMAXSSZrrbk	= 5009,
    X86_VMAXSSZrrbkz	= 5010,
    X86_VMAXSSrm	= 5011,
    X86_VMAXSSrm_Int	= 5012,
    X86_VMAXSSrr	= 5013,
    X86_VMAXSSrr_Int	= 5014,
    X86_VMCALL	= 5015,
    X86_VMCLEARm	= 5016,
    X86_VMFUNC	= 5017,
    X86_VMINCPDYrm	= 5018,
    X86_VMINCPDYrr	= 5019,
    X86_VMINCPDrm	= 5020,
    X86_VMINCPDrr	= 5021,
    X86_VMINCPSYrm	= 5022,
    X86_VMINCPSYrr	= 5023,
    X86_VMINCPSrm	= 5024,
    X86_VMINCPSrr	= 5025,
    X86_VMINCSDrm	= 5026,
    X86_VMINCSDrr	= 5027,
    X86_VMINCSSrm	= 5028,
    X86_VMINCSSrr	= 5029,
    X86_VMINPDYrm	= 5030,
    X86_VMINPDYrr	= 5031,
    X86_VMINPDZ128rm	= 5032,
    X86_VMINPDZ128rmb	= 5033,
    X86_VMINPDZ128rmbk	= 5034,
    X86_VMINPDZ128rmbkz	= 5035,
    X86_VMINPDZ128rmk	= 5036,
    X86_VMINPDZ128rmkz	= 5037,
    X86_VMINPDZ128rr	= 5038,
    X86_VMINPDZ128rrk	= 5039,
    X86_VMINPDZ128rrkz	= 5040,
    X86_VMINPDZ256rm	= 5041,
    X86_VMINPDZ256rmb	= 5042,
    X86_VMINPDZ256rmbk	= 5043,
    X86_VMINPDZ256rmbkz	= 5044,
    X86_VMINPDZ256rmk	= 5045,
    X86_VMINPDZ256rmkz	= 5046,
    X86_VMINPDZ256rr	= 5047,
    X86_VMINPDZ256rrk	= 5048,
    X86_VMINPDZ256rrkz	= 5049,
    X86_VMINPDZrm	= 5050,
    X86_VMINPDZrmb	= 5051,
    X86_VMINPDZrmbk	= 5052,
    X86_VMINPDZrmbkz	= 5053,
    X86_VMINPDZrmk	= 5054,
    X86_VMINPDZrmkz	= 5055,
    X86_VMINPDZrr	= 5056,
    X86_VMINPDZrrk	= 5057,
    X86_VMINPDZrrkz	= 5058,
    X86_VMINPDrm	= 5059,
    X86_VMINPDrr	= 5060,
    X86_VMINPSYrm	= 5061,
    X86_VMINPSYrr	= 5062,
    X86_VMINPSZ128rm	= 5063,
    X86_VMINPSZ128rmb	= 5064,
    X86_VMINPSZ128rmbk	= 5065,
    X86_VMINPSZ128rmbkz	= 5066,
    X86_VMINPSZ128rmk	= 5067,
    X86_VMINPSZ128rmkz	= 5068,
    X86_VMINPSZ128rr	= 5069,
    X86_VMINPSZ128rrk	= 5070,
    X86_VMINPSZ128rrkz	= 5071,
    X86_VMINPSZ256rm	= 5072,
    X86_VMINPSZ256rmb	= 5073,
    X86_VMINPSZ256rmbk	= 5074,
    X86_VMINPSZ256rmbkz	= 5075,
    X86_VMINPSZ256rmk	= 5076,
    X86_VMINPSZ256rmkz	= 5077,
    X86_VMINPSZ256rr	= 5078,
    X86_VMINPSZ256rrk	= 5079,
    X86_VMINPSZ256rrkz	= 5080,
    X86_VMINPSZrm	= 5081,
    X86_VMINPSZrmb	= 5082,
    X86_VMINPSZrmbk	= 5083,
    X86_VMINPSZrmbkz	= 5084,
    X86_VMINPSZrmk	= 5085,
    X86_VMINPSZrmkz	= 5086,
    X86_VMINPSZrr	= 5087,
    X86_VMINPSZrrk	= 5088,
    X86_VMINPSZrrkz	= 5089,
    X86_VMINPSrm	= 5090,
    X86_VMINPSrr	= 5091,
    X86_VMINSDZrm	= 5092,
    X86_VMINSDZrm_Int	= 5093,
    X86_VMINSDZrm_Intk	= 5094,
    X86_VMINSDZrm_Intkz	= 5095,
    X86_VMINSDZrr	= 5096,
    X86_VMINSDZrr_Int	= 5097,
    X86_VMINSDZrr_Intk	= 5098,
    X86_VMINSDZrr_Intkz	= 5099,
    X86_VMINSDZrrb	= 5100,
    X86_VMINSDZrrbk	= 5101,
    X86_VMINSDZrrbkz	= 5102,
    X86_VMINSDrm	= 5103,
    X86_VMINSDrm_Int	= 5104,
    X86_VMINSDrr	= 5105,
    X86_VMINSDrr_Int	= 5106,
    X86_VMINSSZrm	= 5107,
    X86_VMINSSZrm_Int	= 5108,
    X86_VMINSSZrm_Intk	= 5109,
    X86_VMINSSZrm_Intkz	= 5110,
    X86_VMINSSZrr	= 5111,
    X86_VMINSSZrr_Int	= 5112,
    X86_VMINSSZrr_Intk	= 5113,
    X86_VMINSSZrr_Intkz	= 5114,
    X86_VMINSSZrrb	= 5115,
    X86_VMINSSZrrbk	= 5116,
    X86_VMINSSZrrbkz	= 5117,
    X86_VMINSSrm	= 5118,
    X86_VMINSSrm_Int	= 5119,
    X86_VMINSSrr	= 5120,
    X86_VMINSSrr_Int	= 5121,
    X86_VMLAUNCH	= 5122,
    X86_VMLOAD32	= 5123,
    X86_VMLOAD64	= 5124,
    X86_VMMCALL	= 5125,
    X86_VMOV64toPQIZrr	= 5126,
    X86_VMOV64toPQIrm	= 5127,
    X86_VMOV64toPQIrr	= 5128,
    X86_VMOV64toSDZrr	= 5129,
    X86_VMOV64toSDrm	= 5130,
    X86_VMOV64toSDrr	= 5131,
    X86_VMOVAPDYmr	= 5132,
    X86_VMOVAPDYrm	= 5133,
    X86_VMOVAPDYrr	= 5134,
    X86_VMOVAPDYrr_REV	= 5135,
    X86_VMOVAPDZ128mr	= 5136,
    X86_VMOVAPDZ128mrk	= 5137,
    X86_VMOVAPDZ128rm	= 5138,
    X86_VMOVAPDZ128rmk	= 5139,
    X86_VMOVAPDZ128rmkz	= 5140,
    X86_VMOVAPDZ128rr	= 5141,
    X86_VMOVAPDZ128rr_alt	= 5142,
    X86_VMOVAPDZ128rrk	= 5143,
    X86_VMOVAPDZ128rrk_alt	= 5144,
    X86_VMOVAPDZ128rrkz	= 5145,
    X86_VMOVAPDZ128rrkz_alt	= 5146,
    X86_VMOVAPDZ256mr	= 5147,
    X86_VMOVAPDZ256mrk	= 5148,
    X86_VMOVAPDZ256rm	= 5149,
    X86_VMOVAPDZ256rmk	= 5150,
    X86_VMOVAPDZ256rmkz	= 5151,
    X86_VMOVAPDZ256rr	= 5152,
    X86_VMOVAPDZ256rr_alt	= 5153,
    X86_VMOVAPDZ256rrk	= 5154,
    X86_VMOVAPDZ256rrk_alt	= 5155,
    X86_VMOVAPDZ256rrkz	= 5156,
    X86_VMOVAPDZ256rrkz_alt	= 5157,
    X86_VMOVAPDZmr	= 5158,
    X86_VMOVAPDZmrk	= 5159,
    X86_VMOVAPDZrm	= 5160,
    X86_VMOVAPDZrmk	= 5161,
    X86_VMOVAPDZrmkz	= 5162,
    X86_VMOVAPDZrr	= 5163,
    X86_VMOVAPDZrr_alt	= 5164,
    X86_VMOVAPDZrrk	= 5165,
    X86_VMOVAPDZrrk_alt	= 5166,
    X86_VMOVAPDZrrkz	= 5167,
    X86_VMOVAPDZrrkz_alt	= 5168,
    X86_VMOVAPDmr	= 5169,
    X86_VMOVAPDrm	= 5170,
    X86_VMOVAPDrr	= 5171,
    X86_VMOVAPDrr_REV	= 5172,
    X86_VMOVAPSYmr	= 5173,
    X86_VMOVAPSYrm	= 5174,
    X86_VMOVAPSYrr	= 5175,
    X86_VMOVAPSYrr_REV	= 5176,
    X86_VMOVAPSZ128mr	= 5177,
    X86_VMOVAPSZ128mrk	= 5178,
    X86_VMOVAPSZ128rm	= 5179,
    X86_VMOVAPSZ128rmk	= 5180,
    X86_VMOVAPSZ128rmkz	= 5181,
    X86_VMOVAPSZ128rr	= 5182,
    X86_VMOVAPSZ128rr_alt	= 5183,
    X86_VMOVAPSZ128rrk	= 5184,
    X86_VMOVAPSZ128rrk_alt	= 5185,
    X86_VMOVAPSZ128rrkz	= 5186,
    X86_VMOVAPSZ128rrkz_alt	= 5187,
    X86_VMOVAPSZ256mr	= 5188,
    X86_VMOVAPSZ256mrk	= 5189,
    X86_VMOVAPSZ256rm	= 5190,
    X86_VMOVAPSZ256rmk	= 5191,
    X86_VMOVAPSZ256rmkz	= 5192,
    X86_VMOVAPSZ256rr	= 5193,
    X86_VMOVAPSZ256rr_alt	= 5194,
    X86_VMOVAPSZ256rrk	= 5195,
    X86_VMOVAPSZ256rrk_alt	= 5196,
    X86_VMOVAPSZ256rrkz	= 5197,
    X86_VMOVAPSZ256rrkz_alt	= 5198,
    X86_VMOVAPSZmr	= 5199,
    X86_VMOVAPSZmrk	= 5200,
    X86_VMOVAPSZrm	= 5201,
    X86_VMOVAPSZrmk	= 5202,
    X86_VMOVAPSZrmkz	= 5203,
    X86_VMOVAPSZrr	= 5204,
    X86_VMOVAPSZrr_alt	= 5205,
    X86_VMOVAPSZrrk	= 5206,
    X86_VMOVAPSZrrk_alt	= 5207,
    X86_VMOVAPSZrrkz	= 5208,
    X86_VMOVAPSZrrkz_alt	= 5209,
    X86_VMOVAPSmr	= 5210,
    X86_VMOVAPSrm	= 5211,
    X86_VMOVAPSrr	= 5212,
    X86_VMOVAPSrr_REV	= 5213,
    X86_VMOVDDUPYrm	= 5214,
    X86_VMOVDDUPYrr	= 5215,
    X86_VMOVDDUPZrm	= 5216,
    X86_VMOVDDUPZrr	= 5217,
    X86_VMOVDDUPrm	= 5218,
    X86_VMOVDDUPrr	= 5219,
    X86_VMOVDI2PDIZrm	= 5220,
    X86_VMOVDI2PDIZrr	= 5221,
    X86_VMOVDI2PDIrm	= 5222,
    X86_VMOVDI2PDIrr	= 5223,
    X86_VMOVDI2SSZrm	= 5224,
    X86_VMOVDI2SSZrr	= 5225,
    X86_VMOVDI2SSrm	= 5226,
    X86_VMOVDI2SSrr	= 5227,
    X86_VMOVDQA32Z128mr	= 5228,
    X86_VMOVDQA32Z128mrk	= 5229,
    X86_VMOVDQA32Z128rm	= 5230,
    X86_VMOVDQA32Z128rmk	= 5231,
    X86_VMOVDQA32Z128rmkz	= 5232,
    X86_VMOVDQA32Z128rr	= 5233,
    X86_VMOVDQA32Z128rr_alt	= 5234,
    X86_VMOVDQA32Z128rrk	= 5235,
    X86_VMOVDQA32Z128rrk_alt	= 5236,
    X86_VMOVDQA32Z128rrkz	= 5237,
    X86_VMOVDQA32Z128rrkz_alt	= 5238,
    X86_VMOVDQA32Z256mr	= 5239,
    X86_VMOVDQA32Z256mrk	= 5240,
    X86_VMOVDQA32Z256rm	= 5241,
    X86_VMOVDQA32Z256rmk	= 5242,
    X86_VMOVDQA32Z256rmkz	= 5243,
    X86_VMOVDQA32Z256rr	= 5244,
    X86_VMOVDQA32Z256rr_alt	= 5245,
    X86_VMOVDQA32Z256rrk	= 5246,
    X86_VMOVDQA32Z256rrk_alt	= 5247,
    X86_VMOVDQA32Z256rrkz	= 5248,
    X86_VMOVDQA32Z256rrkz_alt	= 5249,
    X86_VMOVDQA32Zmr	= 5250,
    X86_VMOVDQA32Zmrk	= 5251,
    X86_VMOVDQA32Zrm	= 5252,
    X86_VMOVDQA32Zrmk	= 5253,
    X86_VMOVDQA32Zrmkz	= 5254,
    X86_VMOVDQA32Zrr	= 5255,
    X86_VMOVDQA32Zrr_alt	= 5256,
    X86_VMOVDQA32Zrrk	= 5257,
    X86_VMOVDQA32Zrrk_alt	= 5258,
    X86_VMOVDQA32Zrrkz	= 5259,
    X86_VMOVDQA32Zrrkz_alt	= 5260,
    X86_VMOVDQA64Z128mr	= 5261,
    X86_VMOVDQA64Z128mrk	= 5262,
    X86_VMOVDQA64Z128rm	= 5263,
    X86_VMOVDQA64Z128rmk	= 5264,
    X86_VMOVDQA64Z128rmkz	= 5265,
    X86_VMOVDQA64Z128rr	= 5266,
    X86_VMOVDQA64Z128rr_alt	= 5267,
    X86_VMOVDQA64Z128rrk	= 5268,
    X86_VMOVDQA64Z128rrk_alt	= 5269,
    X86_VMOVDQA64Z128rrkz	= 5270,
    X86_VMOVDQA64Z128rrkz_alt	= 5271,
    X86_VMOVDQA64Z256mr	= 5272,
    X86_VMOVDQA64Z256mrk	= 5273,
    X86_VMOVDQA64Z256rm	= 5274,
    X86_VMOVDQA64Z256rmk	= 5275,
    X86_VMOVDQA64Z256rmkz	= 5276,
    X86_VMOVDQA64Z256rr	= 5277,
    X86_VMOVDQA64Z256rr_alt	= 5278,
    X86_VMOVDQA64Z256rrk	= 5279,
    X86_VMOVDQA64Z256rrk_alt	= 5280,
    X86_VMOVDQA64Z256rrkz	= 5281,
    X86_VMOVDQA64Z256rrkz_alt	= 5282,
    X86_VMOVDQA64Zmr	= 5283,
    X86_VMOVDQA64Zmrk	= 5284,
    X86_VMOVDQA64Zrm	= 5285,
    X86_VMOVDQA64Zrmk	= 5286,
    X86_VMOVDQA64Zrmkz	= 5287,
    X86_VMOVDQA64Zrr	= 5288,
    X86_VMOVDQA64Zrr_alt	= 5289,
    X86_VMOVDQA64Zrrk	= 5290,
    X86_VMOVDQA64Zrrk_alt	= 5291,
    X86_VMOVDQA64Zrrkz	= 5292,
    X86_VMOVDQA64Zrrkz_alt	= 5293,
    X86_VMOVDQAYmr	= 5294,
    X86_VMOVDQAYrm	= 5295,
    X86_VMOVDQAYrr	= 5296,
    X86_VMOVDQAYrr_REV	= 5297,
    X86_VMOVDQAmr	= 5298,
    X86_VMOVDQArm	= 5299,
    X86_VMOVDQArr	= 5300,
    X86_VMOVDQArr_REV	= 5301,
    X86_VMOVDQU16Z128mr	= 5302,
    X86_VMOVDQU16Z128mrk	= 5303,
    X86_VMOVDQU16Z128rm	= 5304,
    X86_VMOVDQU16Z128rmk	= 5305,
    X86_VMOVDQU16Z128rmkz	= 5306,
    X86_VMOVDQU16Z128rr	= 5307,
    X86_VMOVDQU16Z128rr_alt	= 5308,
    X86_VMOVDQU16Z128rrk	= 5309,
    X86_VMOVDQU16Z128rrk_alt	= 5310,
    X86_VMOVDQU16Z128rrkz	= 5311,
    X86_VMOVDQU16Z128rrkz_alt	= 5312,
    X86_VMOVDQU16Z256mr	= 5313,
    X86_VMOVDQU16Z256mrk	= 5314,
    X86_VMOVDQU16Z256rm	= 5315,
    X86_VMOVDQU16Z256rmk	= 5316,
    X86_VMOVDQU16Z256rmkz	= 5317,
    X86_VMOVDQU16Z256rr	= 5318,
    X86_VMOVDQU16Z256rr_alt	= 5319,
    X86_VMOVDQU16Z256rrk	= 5320,
    X86_VMOVDQU16Z256rrk_alt	= 5321,
    X86_VMOVDQU16Z256rrkz	= 5322,
    X86_VMOVDQU16Z256rrkz_alt	= 5323,
    X86_VMOVDQU16Zmr	= 5324,
    X86_VMOVDQU16Zmrk	= 5325,
    X86_VMOVDQU16Zrm	= 5326,
    X86_VMOVDQU16Zrmk	= 5327,
    X86_VMOVDQU16Zrmkz	= 5328,
    X86_VMOVDQU16Zrr	= 5329,
    X86_VMOVDQU16Zrr_alt	= 5330,
    X86_VMOVDQU16Zrrk	= 5331,
    X86_VMOVDQU16Zrrk_alt	= 5332,
    X86_VMOVDQU16Zrrkz	= 5333,
    X86_VMOVDQU16Zrrkz_alt	= 5334,
    X86_VMOVDQU32Z128mr	= 5335,
    X86_VMOVDQU32Z128mrk	= 5336,
    X86_VMOVDQU32Z128rm	= 5337,
    X86_VMOVDQU32Z128rmk	= 5338,
    X86_VMOVDQU32Z128rmkz	= 5339,
    X86_VMOVDQU32Z128rr	= 5340,
    X86_VMOVDQU32Z128rr_alt	= 5341,
    X86_VMOVDQU32Z128rrk	= 5342,
    X86_VMOVDQU32Z128rrk_alt	= 5343,
    X86_VMOVDQU32Z128rrkz	= 5344,
    X86_VMOVDQU32Z128rrkz_alt	= 5345,
    X86_VMOVDQU32Z256mr	= 5346,
    X86_VMOVDQU32Z256mrk	= 5347,
    X86_VMOVDQU32Z256rm	= 5348,
    X86_VMOVDQU32Z256rmk	= 5349,
    X86_VMOVDQU32Z256rmkz	= 5350,
    X86_VMOVDQU32Z256rr	= 5351,
    X86_VMOVDQU32Z256rr_alt	= 5352,
    X86_VMOVDQU32Z256rrk	= 5353,
    X86_VMOVDQU32Z256rrk_alt	= 5354,
    X86_VMOVDQU32Z256rrkz	= 5355,
    X86_VMOVDQU32Z256rrkz_alt	= 5356,
    X86_VMOVDQU32Zmr	= 5357,
    X86_VMOVDQU32Zmrk	= 5358,
    X86_VMOVDQU32Zrm	= 5359,
    X86_VMOVDQU32Zrmk	= 5360,
    X86_VMOVDQU32Zrmkz	= 5361,
    X86_VMOVDQU32Zrr	= 5362,
    X86_VMOVDQU32Zrr_alt	= 5363,
    X86_VMOVDQU32Zrrk	= 5364,
    X86_VMOVDQU32Zrrk_alt	= 5365,
    X86_VMOVDQU32Zrrkz	= 5366,
    X86_VMOVDQU32Zrrkz_alt	= 5367,
    X86_VMOVDQU64Z128mr	= 5368,
    X86_VMOVDQU64Z128mrk	= 5369,
    X86_VMOVDQU64Z128rm	= 5370,
    X86_VMOVDQU64Z128rmk	= 5371,
    X86_VMOVDQU64Z128rmkz	= 5372,
    X86_VMOVDQU64Z128rr	= 5373,
    X86_VMOVDQU64Z128rr_alt	= 5374,
    X86_VMOVDQU64Z128rrk	= 5375,
    X86_VMOVDQU64Z128rrk_alt	= 5376,
    X86_VMOVDQU64Z128rrkz	= 5377,
    X86_VMOVDQU64Z128rrkz_alt	= 5378,
    X86_VMOVDQU64Z256mr	= 5379,
    X86_VMOVDQU64Z256mrk	= 5380,
    X86_VMOVDQU64Z256rm	= 5381,
    X86_VMOVDQU64Z256rmk	= 5382,
    X86_VMOVDQU64Z256rmkz	= 5383,
    X86_VMOVDQU64Z256rr	= 5384,
    X86_VMOVDQU64Z256rr_alt	= 5385,
    X86_VMOVDQU64Z256rrk	= 5386,
    X86_VMOVDQU64Z256rrk_alt	= 5387,
    X86_VMOVDQU64Z256rrkz	= 5388,
    X86_VMOVDQU64Z256rrkz_alt	= 5389,
    X86_VMOVDQU64Zmr	= 5390,
    X86_VMOVDQU64Zmrk	= 5391,
    X86_VMOVDQU64Zrm	= 5392,
    X86_VMOVDQU64Zrmk	= 5393,
    X86_VMOVDQU64Zrmkz	= 5394,
    X86_VMOVDQU64Zrr	= 5395,
    X86_VMOVDQU64Zrr_alt	= 5396,
    X86_VMOVDQU64Zrrk	= 5397,
    X86_VMOVDQU64Zrrk_alt	= 5398,
    X86_VMOVDQU64Zrrkz	= 5399,
    X86_VMOVDQU64Zrrkz_alt	= 5400,
    X86_VMOVDQU8Z128mr	= 5401,
    X86_VMOVDQU8Z128mrk	= 5402,
    X86_VMOVDQU8Z128rm	= 5403,
    X86_VMOVDQU8Z128rmk	= 5404,
    X86_VMOVDQU8Z128rmkz	= 5405,
    X86_VMOVDQU8Z128rr	= 5406,
    X86_VMOVDQU8Z128rr_alt	= 5407,
    X86_VMOVDQU8Z128rrk	= 5408,
    X86_VMOVDQU8Z128rrk_alt	= 5409,
    X86_VMOVDQU8Z128rrkz	= 5410,
    X86_VMOVDQU8Z128rrkz_alt	= 5411,
    X86_VMOVDQU8Z256mr	= 5412,
    X86_VMOVDQU8Z256mrk	= 5413,
    X86_VMOVDQU8Z256rm	= 5414,
    X86_VMOVDQU8Z256rmk	= 5415,
    X86_VMOVDQU8Z256rmkz	= 5416,
    X86_VMOVDQU8Z256rr	= 5417,
    X86_VMOVDQU8Z256rr_alt	= 5418,
    X86_VMOVDQU8Z256rrk	= 5419,
    X86_VMOVDQU8Z256rrk_alt	= 5420,
    X86_VMOVDQU8Z256rrkz	= 5421,
    X86_VMOVDQU8Z256rrkz_alt	= 5422,
    X86_VMOVDQU8Zmr	= 5423,
    X86_VMOVDQU8Zmrk	= 5424,
    X86_VMOVDQU8Zrm	= 5425,
    X86_VMOVDQU8Zrmk	= 5426,
    X86_VMOVDQU8Zrmkz	= 5427,
    X86_VMOVDQU8Zrr	= 5428,
    X86_VMOVDQU8Zrr_alt	= 5429,
    X86_VMOVDQU8Zrrk	= 5430,
    X86_VMOVDQU8Zrrk_alt	= 5431,
    X86_VMOVDQU8Zrrkz	= 5432,
    X86_VMOVDQU8Zrrkz_alt	= 5433,
    X86_VMOVDQUYmr	= 5434,
    X86_VMOVDQUYrm	= 5435,
    X86_VMOVDQUYrr	= 5436,
    X86_VMOVDQUYrr_REV	= 5437,
    X86_VMOVDQUmr	= 5438,
    X86_VMOVDQUrm	= 5439,
    X86_VMOVDQUrr	= 5440,
    X86_VMOVDQUrr_REV	= 5441,
    X86_VMOVHLPSZrr	= 5442,
    X86_VMOVHLPSrr	= 5443,
    X86_VMOVHPDmr	= 5444,
    X86_VMOVHPDrm	= 5445,
    X86_VMOVHPSmr	= 5446,
    X86_VMOVHPSrm	= 5447,
    X86_VMOVLHPSZrr	= 5448,
    X86_VMOVLHPSrr	= 5449,
    X86_VMOVLPDmr	= 5450,
    X86_VMOVLPDrm	= 5451,
    X86_VMOVLPSmr	= 5452,
    X86_VMOVLPSrm	= 5453,
    X86_VMOVMSKPDYrr	= 5454,
    X86_VMOVMSKPDrr	= 5455,
    X86_VMOVMSKPSYrr	= 5456,
    X86_VMOVMSKPSrr	= 5457,
    X86_VMOVNTDQAYrm	= 5458,
    X86_VMOVNTDQAZ128rm	= 5459,
    X86_VMOVNTDQAZ256rm	= 5460,
    X86_VMOVNTDQAZrm	= 5461,
    X86_VMOVNTDQArm	= 5462,
    X86_VMOVNTDQYmr	= 5463,
    X86_VMOVNTDQZ128mr	= 5464,
    X86_VMOVNTDQZ256mr	= 5465,
    X86_VMOVNTDQZmr	= 5466,
    X86_VMOVNTDQmr	= 5467,
    X86_VMOVNTPDYmr	= 5468,
    X86_VMOVNTPDZ128mr	= 5469,
    X86_VMOVNTPDZ256mr	= 5470,
    X86_VMOVNTPDZmr	= 5471,
    X86_VMOVNTPDmr	= 5472,
    X86_VMOVNTPSYmr	= 5473,
    X86_VMOVNTPSZ128mr	= 5474,
    X86_VMOVNTPSZ256mr	= 5475,
    X86_VMOVNTPSZmr	= 5476,
    X86_VMOVNTPSmr	= 5477,
    X86_VMOVPDI2DIZmr	= 5478,
    X86_VMOVPDI2DIZrr	= 5479,
    X86_VMOVPDI2DImr	= 5480,
    X86_VMOVPDI2DIrr	= 5481,
    X86_VMOVPQI2QImr	= 5482,
    X86_VMOVPQI2QIrr	= 5483,
    X86_VMOVPQIto64Zmr	= 5484,
    X86_VMOVPQIto64Zrr	= 5485,
    X86_VMOVPQIto64rm	= 5486,
    X86_VMOVPQIto64rr	= 5487,
    X86_VMOVQI2PQIZrm	= 5488,
    X86_VMOVQI2PQIrm	= 5489,
    X86_VMOVSDZmr	= 5490,
    X86_VMOVSDZmrk	= 5491,
    X86_VMOVSDZrm	= 5492,
    X86_VMOVSDZrr	= 5493,
    X86_VMOVSDZrr_REV	= 5494,
    X86_VMOVSDZrrk	= 5495,
    X86_VMOVSDmr	= 5496,
    X86_VMOVSDrm	= 5497,
    X86_VMOVSDrr	= 5498,
    X86_VMOVSDrr_REV	= 5499,
    X86_VMOVSDto64Zmr	= 5500,
    X86_VMOVSDto64Zrr	= 5501,
    X86_VMOVSDto64mr	= 5502,
    X86_VMOVSDto64rr	= 5503,
    X86_VMOVSHDUPYrm	= 5504,
    X86_VMOVSHDUPYrr	= 5505,
    X86_VMOVSHDUPZrm	= 5506,
    X86_VMOVSHDUPZrr	= 5507,
    X86_VMOVSHDUPrm	= 5508,
    X86_VMOVSHDUPrr	= 5509,
    X86_VMOVSLDUPYrm	= 5510,
    X86_VMOVSLDUPYrr	= 5511,
    X86_VMOVSLDUPZrm	= 5512,
    X86_VMOVSLDUPZrr	= 5513,
    X86_VMOVSLDUPrm	= 5514,
    X86_VMOVSLDUPrr	= 5515,
    X86_VMOVSS2DIZmr	= 5516,
    X86_VMOVSS2DIZrr	= 5517,
    X86_VMOVSS2DImr	= 5518,
    X86_VMOVSS2DIrr	= 5519,
    X86_VMOVSSZmr	= 5520,
    X86_VMOVSSZmrk	= 5521,
    X86_VMOVSSZrm	= 5522,
    X86_VMOVSSZrr	= 5523,
    X86_VMOVSSZrr_REV	= 5524,
    X86_VMOVSSZrrk	= 5525,
    X86_VMOVSSmr	= 5526,
    X86_VMOVSSrm	= 5527,
    X86_VMOVSSrr	= 5528,
    X86_VMOVSSrr_REV	= 5529,
    X86_VMOVUPDYmr	= 5530,
    X86_VMOVUPDYrm	= 5531,
    X86_VMOVUPDYrr	= 5532,
    X86_VMOVUPDYrr_REV	= 5533,
    X86_VMOVUPDZ128mr	= 5534,
    X86_VMOVUPDZ128mrk	= 5535,
    X86_VMOVUPDZ128rm	= 5536,
    X86_VMOVUPDZ128rmk	= 5537,
    X86_VMOVUPDZ128rmkz	= 5538,
    X86_VMOVUPDZ128rr	= 5539,
    X86_VMOVUPDZ128rr_alt	= 5540,
    X86_VMOVUPDZ128rrk	= 5541,
    X86_VMOVUPDZ128rrk_alt	= 5542,
    X86_VMOVUPDZ128rrkz	= 5543,
    X86_VMOVUPDZ128rrkz_alt	= 5544,
    X86_VMOVUPDZ256mr	= 5545,
    X86_VMOVUPDZ256mrk	= 5546,
    X86_VMOVUPDZ256rm	= 5547,
    X86_VMOVUPDZ256rmk	= 5548,
    X86_VMOVUPDZ256rmkz	= 5549,
    X86_VMOVUPDZ256rr	= 5550,
    X86_VMOVUPDZ256rr_alt	= 5551,
    X86_VMOVUPDZ256rrk	= 5552,
    X86_VMOVUPDZ256rrk_alt	= 5553,
    X86_VMOVUPDZ256rrkz	= 5554,
    X86_VMOVUPDZ256rrkz_alt	= 5555,
    X86_VMOVUPDZmr	= 5556,
    X86_VMOVUPDZmrk	= 5557,
    X86_VMOVUPDZrm	= 5558,
    X86_VMOVUPDZrmk	= 5559,
    X86_VMOVUPDZrmkz	= 5560,
    X86_VMOVUPDZrr	= 5561,
    X86_VMOVUPDZrr_alt	= 5562,
    X86_VMOVUPDZrrk	= 5563,
    X86_VMOVUPDZrrk_alt	= 5564,
    X86_VMOVUPDZrrkz	= 5565,
    X86_VMOVUPDZrrkz_alt	= 5566,
    X86_VMOVUPDmr	= 5567,
    X86_VMOVUPDrm	= 5568,
    X86_VMOVUPDrr	= 5569,
    X86_VMOVUPDrr_REV	= 5570,
    X86_VMOVUPSYmr	= 5571,
    X86_VMOVUPSYrm	= 5572,
    X86_VMOVUPSYrr	= 5573,
    X86_VMOVUPSYrr_REV	= 5574,
    X86_VMOVUPSZ128mr	= 5575,
    X86_VMOVUPSZ128mrk	= 5576,
    X86_VMOVUPSZ128rm	= 5577,
    X86_VMOVUPSZ128rmk	= 5578,
    X86_VMOVUPSZ128rmkz	= 5579,
    X86_VMOVUPSZ128rr	= 5580,
    X86_VMOVUPSZ128rr_alt	= 5581,
    X86_VMOVUPSZ128rrk	= 5582,
    X86_VMOVUPSZ128rrk_alt	= 5583,
    X86_VMOVUPSZ128rrkz	= 5584,
    X86_VMOVUPSZ128rrkz_alt	= 5585,
    X86_VMOVUPSZ256mr	= 5586,
    X86_VMOVUPSZ256mrk	= 5587,
    X86_VMOVUPSZ256rm	= 5588,
    X86_VMOVUPSZ256rmk	= 5589,
    X86_VMOVUPSZ256rmkz	= 5590,
    X86_VMOVUPSZ256rr	= 5591,
    X86_VMOVUPSZ256rr_alt	= 5592,
    X86_VMOVUPSZ256rrk	= 5593,
    X86_VMOVUPSZ256rrk_alt	= 5594,
    X86_VMOVUPSZ256rrkz	= 5595,
    X86_VMOVUPSZ256rrkz_alt	= 5596,
    X86_VMOVUPSZmr	= 5597,
    X86_VMOVUPSZmrk	= 5598,
    X86_VMOVUPSZrm	= 5599,
    X86_VMOVUPSZrmk	= 5600,
    X86_VMOVUPSZrmkz	= 5601,
    X86_VMOVUPSZrr	= 5602,
    X86_VMOVUPSZrr_alt	= 5603,
    X86_VMOVUPSZrrk	= 5604,
    X86_VMOVUPSZrrk_alt	= 5605,
    X86_VMOVUPSZrrkz	= 5606,
    X86_VMOVUPSZrrkz_alt	= 5607,
    X86_VMOVUPSmr	= 5608,
    X86_VMOVUPSrm	= 5609,
    X86_VMOVUPSrr	= 5610,
    X86_VMOVUPSrr_REV	= 5611,
    X86_VMOVZPQILo2PQIZrm	= 5612,
    X86_VMOVZPQILo2PQIZrr	= 5613,
    X86_VMOVZPQILo2PQIrm	= 5614,
    X86_VMOVZPQILo2PQIrr	= 5615,
    X86_VMOVZQI2PQIrm	= 5616,
    X86_VMOVZQI2PQIrr	= 5617,
    X86_VMPSADBWYrmi	= 5618,
    X86_VMPSADBWYrri	= 5619,
    X86_VMPSADBWrmi	= 5620,
    X86_VMPSADBWrri	= 5621,
    X86_VMPTRLDm	= 5622,
    X86_VMPTRSTm	= 5623,
    X86_VMREAD32rm	= 5624,
    X86_VMREAD32rr	= 5625,
    X86_VMREAD64rm	= 5626,
    X86_VMREAD64rr	= 5627,
    X86_VMRESUME	= 5628,
    X86_VMRUN32	= 5629,
    X86_VMRUN64	= 5630,
    X86_VMSAVE32	= 5631,
    X86_VMSAVE64	= 5632,
    X86_VMULPDYrm	= 5633,
    X86_VMULPDYrr	= 5634,
    X86_VMULPDZ128rm	= 5635,
    X86_VMULPDZ128rmb	= 5636,
    X86_VMULPDZ128rmbk	= 5637,
    X86_VMULPDZ128rmbkz	= 5638,
    X86_VMULPDZ128rmk	= 5639,
    X86_VMULPDZ128rmkz	= 5640,
    X86_VMULPDZ128rr	= 5641,
    X86_VMULPDZ128rrk	= 5642,
    X86_VMULPDZ128rrkz	= 5643,
    X86_VMULPDZ256rm	= 5644,
    X86_VMULPDZ256rmb	= 5645,
    X86_VMULPDZ256rmbk	= 5646,
    X86_VMULPDZ256rmbkz	= 5647,
    X86_VMULPDZ256rmk	= 5648,
    X86_VMULPDZ256rmkz	= 5649,
    X86_VMULPDZ256rr	= 5650,
    X86_VMULPDZ256rrk	= 5651,
    X86_VMULPDZ256rrkz	= 5652,
    X86_VMULPDZrb	= 5653,
    X86_VMULPDZrbk	= 5654,
    X86_VMULPDZrbkz	= 5655,
    X86_VMULPDZrm	= 5656,
    X86_VMULPDZrmb	= 5657,
    X86_VMULPDZrmbk	= 5658,
    X86_VMULPDZrmbkz	= 5659,
    X86_VMULPDZrmk	= 5660,
    X86_VMULPDZrmkz	= 5661,
    X86_VMULPDZrr	= 5662,
    X86_VMULPDZrrk	= 5663,
    X86_VMULPDZrrkz	= 5664,
    X86_VMULPDrm	= 5665,
    X86_VMULPDrr	= 5666,
    X86_VMULPSYrm	= 5667,
    X86_VMULPSYrr	= 5668,
    X86_VMULPSZ128rm	= 5669,
    X86_VMULPSZ128rmb	= 5670,
    X86_VMULPSZ128rmbk	= 5671,
    X86_VMULPSZ128rmbkz	= 5672,
    X86_VMULPSZ128rmk	= 5673,
    X86_VMULPSZ128rmkz	= 5674,
    X86_VMULPSZ128rr	= 5675,
    X86_VMULPSZ128rrk	= 5676,
    X86_VMULPSZ128rrkz	= 5677,
    X86_VMULPSZ256rm	= 5678,
    X86_VMULPSZ256rmb	= 5679,
    X86_VMULPSZ256rmbk	= 5680,
    X86_VMULPSZ256rmbkz	= 5681,
    X86_VMULPSZ256rmk	= 5682,
    X86_VMULPSZ256rmkz	= 5683,
    X86_VMULPSZ256rr	= 5684,
    X86_VMULPSZ256rrk	= 5685,
    X86_VMULPSZ256rrkz	= 5686,
    X86_VMULPSZrb	= 5687,
    X86_VMULPSZrbk	= 5688,
    X86_VMULPSZrbkz	= 5689,
    X86_VMULPSZrm	= 5690,
    X86_VMULPSZrmb	= 5691,
    X86_VMULPSZrmbk	= 5692,
    X86_VMULPSZrmbkz	= 5693,
    X86_VMULPSZrmk	= 5694,
    X86_VMULPSZrmkz	= 5695,
    X86_VMULPSZrr	= 5696,
    X86_VMULPSZrrk	= 5697,
    X86_VMULPSZrrkz	= 5698,
    X86_VMULPSrm	= 5699,
    X86_VMULPSrr	= 5700,
    X86_VMULSDZrm	= 5701,
    X86_VMULSDZrm_Int	= 5702,
    X86_VMULSDZrm_Intk	= 5703,
    X86_VMULSDZrm_Intkz	= 5704,
    X86_VMULSDZrr	= 5705,
    X86_VMULSDZrr_Int	= 5706,
    X86_VMULSDZrr_Intk	= 5707,
    X86_VMULSDZrr_Intkz	= 5708,
    X86_VMULSDZrrb	= 5709,
    X86_VMULSDZrrbk	= 5710,
    X86_VMULSDZrrbkz	= 5711,
    X86_VMULSDrm	= 5712,
    X86_VMULSDrm_Int	= 5713,
    X86_VMULSDrr	= 5714,
    X86_VMULSDrr_Int	= 5715,
    X86_VMULSSZrm	= 5716,
    X86_VMULSSZrm_Int	= 5717,
    X86_VMULSSZrm_Intk	= 5718,
    X86_VMULSSZrm_Intkz	= 5719,
    X86_VMULSSZrr	= 5720,
    X86_VMULSSZrr_Int	= 5721,
    X86_VMULSSZrr_Intk	= 5722,
    X86_VMULSSZrr_Intkz	= 5723,
    X86_VMULSSZrrb	= 5724,
    X86_VMULSSZrrbk	= 5725,
    X86_VMULSSZrrbkz	= 5726,
    X86_VMULSSrm	= 5727,
    X86_VMULSSrm_Int	= 5728,
    X86_VMULSSrr	= 5729,
    X86_VMULSSrr_Int	= 5730,
    X86_VMWRITE32rm	= 5731,
    X86_VMWRITE32rr	= 5732,
    X86_VMWRITE64rm	= 5733,
    X86_VMWRITE64rr	= 5734,
    X86_VMXOFF	= 5735,
    X86_VMXON	= 5736,
    X86_VORPDYrm	= 5737,
    X86_VORPDYrr	= 5738,
    X86_VORPDrm	= 5739,
    X86_VORPDrr	= 5740,
    X86_VORPSYrm	= 5741,
    X86_VORPSYrr	= 5742,
    X86_VORPSrm	= 5743,
    X86_VORPSrr	= 5744,
    X86_VPABSBrm128	= 5745,
    X86_VPABSBrm256	= 5746,
    X86_VPABSBrr128	= 5747,
    X86_VPABSBrr256	= 5748,
    X86_VPABSDZrm	= 5749,
    X86_VPABSDZrmb	= 5750,
    X86_VPABSDZrmbk	= 5751,
    X86_VPABSDZrmbkz	= 5752,
    X86_VPABSDZrmk	= 5753,
    X86_VPABSDZrmkz	= 5754,
    X86_VPABSDZrr	= 5755,
    X86_VPABSDZrrk	= 5756,
    X86_VPABSDZrrkz	= 5757,
    X86_VPABSDrm128	= 5758,
    X86_VPABSDrm256	= 5759,
    X86_VPABSDrr128	= 5760,
    X86_VPABSDrr256	= 5761,
    X86_VPABSQZrm	= 5762,
    X86_VPABSQZrmb	= 5763,
    X86_VPABSQZrmbk	= 5764,
    X86_VPABSQZrmbkz	= 5765,
    X86_VPABSQZrmk	= 5766,
    X86_VPABSQZrmkz	= 5767,
    X86_VPABSQZrr	= 5768,
    X86_VPABSQZrrk	= 5769,
    X86_VPABSQZrrkz	= 5770,
    X86_VPABSWrm128	= 5771,
    X86_VPABSWrm256	= 5772,
    X86_VPABSWrr128	= 5773,
    X86_VPABSWrr256	= 5774,
    X86_VPACKSSDWYrm	= 5775,
    X86_VPACKSSDWYrr	= 5776,
    X86_VPACKSSDWrm	= 5777,
    X86_VPACKSSDWrr	= 5778,
    X86_VPACKSSWBYrm	= 5779,
    X86_VPACKSSWBYrr	= 5780,
    X86_VPACKSSWBrm	= 5781,
    X86_VPACKSSWBrr	= 5782,
    X86_VPACKUSDWYrm	= 5783,
    X86_VPACKUSDWYrr	= 5784,
    X86_VPACKUSDWrm	= 5785,
    X86_VPACKUSDWrr	= 5786,
    X86_VPACKUSWBYrm	= 5787,
    X86_VPACKUSWBYrr	= 5788,
    X86_VPACKUSWBrm	= 5789,
    X86_VPACKUSWBrr	= 5790,
    X86_VPADDBYrm	= 5791,
    X86_VPADDBYrr	= 5792,
    X86_VPADDBZ128rm	= 5793,
    X86_VPADDBZ128rmk	= 5794,
    X86_VPADDBZ128rmkz	= 5795,
    X86_VPADDBZ128rr	= 5796,
    X86_VPADDBZ128rrk	= 5797,
    X86_VPADDBZ128rrkz	= 5798,
    X86_VPADDBZ256rm	= 5799,
    X86_VPADDBZ256rmk	= 5800,
    X86_VPADDBZ256rmkz	= 5801,
    X86_VPADDBZ256rr	= 5802,
    X86_VPADDBZ256rrk	= 5803,
    X86_VPADDBZ256rrkz	= 5804,
    X86_VPADDBZrm	= 5805,
    X86_VPADDBZrmk	= 5806,
    X86_VPADDBZrmkz	= 5807,
    X86_VPADDBZrr	= 5808,
    X86_VPADDBZrrk	= 5809,
    X86_VPADDBZrrkz	= 5810,
    X86_VPADDBrm	= 5811,
    X86_VPADDBrr	= 5812,
    X86_VPADDDYrm	= 5813,
    X86_VPADDDYrr	= 5814,
    X86_VPADDDZ128rm	= 5815,
    X86_VPADDDZ128rmb	= 5816,
    X86_VPADDDZ128rmbk	= 5817,
    X86_VPADDDZ128rmbkz	= 5818,
    X86_VPADDDZ128rmk	= 5819,
    X86_VPADDDZ128rmkz	= 5820,
    X86_VPADDDZ128rr	= 5821,
    X86_VPADDDZ128rrk	= 5822,
    X86_VPADDDZ128rrkz	= 5823,
    X86_VPADDDZ256rm	= 5824,
    X86_VPADDDZ256rmb	= 5825,
    X86_VPADDDZ256rmbk	= 5826,
    X86_VPADDDZ256rmbkz	= 5827,
    X86_VPADDDZ256rmk	= 5828,
    X86_VPADDDZ256rmkz	= 5829,
    X86_VPADDDZ256rr	= 5830,
    X86_VPADDDZ256rrk	= 5831,
    X86_VPADDDZ256rrkz	= 5832,
    X86_VPADDDZrm	= 5833,
    X86_VPADDDZrmb	= 5834,
    X86_VPADDDZrmbk	= 5835,
    X86_VPADDDZrmbkz	= 5836,
    X86_VPADDDZrmk	= 5837,
    X86_VPADDDZrmkz	= 5838,
    X86_VPADDDZrr	= 5839,
    X86_VPADDDZrrk	= 5840,
    X86_VPADDDZrrkz	= 5841,
    X86_VPADDDrm	= 5842,
    X86_VPADDDrr	= 5843,
    X86_VPADDQYrm	= 5844,
    X86_VPADDQYrr	= 5845,
    X86_VPADDQZ128rm	= 5846,
    X86_VPADDQZ128rmb	= 5847,
    X86_VPADDQZ128rmbk	= 5848,
    X86_VPADDQZ128rmbkz	= 5849,
    X86_VPADDQZ128rmk	= 5850,
    X86_VPADDQZ128rmkz	= 5851,
    X86_VPADDQZ128rr	= 5852,
    X86_VPADDQZ128rrk	= 5853,
    X86_VPADDQZ128rrkz	= 5854,
    X86_VPADDQZ256rm	= 5855,
    X86_VPADDQZ256rmb	= 5856,
    X86_VPADDQZ256rmbk	= 5857,
    X86_VPADDQZ256rmbkz	= 5858,
    X86_VPADDQZ256rmk	= 5859,
    X86_VPADDQZ256rmkz	= 5860,
    X86_VPADDQZ256rr	= 5861,
    X86_VPADDQZ256rrk	= 5862,
    X86_VPADDQZ256rrkz	= 5863,
    X86_VPADDQZrm	= 5864,
    X86_VPADDQZrmb	= 5865,
    X86_VPADDQZrmbk	= 5866,
    X86_VPADDQZrmbkz	= 5867,
    X86_VPADDQZrmk	= 5868,
    X86_VPADDQZrmkz	= 5869,
    X86_VPADDQZrr	= 5870,
    X86_VPADDQZrrk	= 5871,
    X86_VPADDQZrrkz	= 5872,
    X86_VPADDQrm	= 5873,
    X86_VPADDQrr	= 5874,
    X86_VPADDSBYrm	= 5875,
    X86_VPADDSBYrr	= 5876,
    X86_VPADDSBrm	= 5877,
    X86_VPADDSBrr	= 5878,
    X86_VPADDSWYrm	= 5879,
    X86_VPADDSWYrr	= 5880,
    X86_VPADDSWrm	= 5881,
    X86_VPADDSWrr	= 5882,
    X86_VPADDUSBYrm	= 5883,
    X86_VPADDUSBYrr	= 5884,
    X86_VPADDUSBrm	= 5885,
    X86_VPADDUSBrr	= 5886,
    X86_VPADDUSWYrm	= 5887,
    X86_VPADDUSWYrr	= 5888,
    X86_VPADDUSWrm	= 5889,
    X86_VPADDUSWrr	= 5890,
    X86_VPADDWYrm	= 5891,
    X86_VPADDWYrr	= 5892,
    X86_VPADDWZ128rm	= 5893,
    X86_VPADDWZ128rmk	= 5894,
    X86_VPADDWZ128rmkz	= 5895,
    X86_VPADDWZ128rr	= 5896,
    X86_VPADDWZ128rrk	= 5897,
    X86_VPADDWZ128rrkz	= 5898,
    X86_VPADDWZ256rm	= 5899,
    X86_VPADDWZ256rmk	= 5900,
    X86_VPADDWZ256rmkz	= 5901,
    X86_VPADDWZ256rr	= 5902,
    X86_VPADDWZ256rrk	= 5903,
    X86_VPADDWZ256rrkz	= 5904,
    X86_VPADDWZrm	= 5905,
    X86_VPADDWZrmk	= 5906,
    X86_VPADDWZrmkz	= 5907,
    X86_VPADDWZrr	= 5908,
    X86_VPADDWZrrk	= 5909,
    X86_VPADDWZrrkz	= 5910,
    X86_VPADDWrm	= 5911,
    X86_VPADDWrr	= 5912,
    X86_VPALIGNR128rm	= 5913,
    X86_VPALIGNR128rr	= 5914,
    X86_VPALIGNR256rm	= 5915,
    X86_VPALIGNR256rr	= 5916,
    X86_VPANDDZ128rm	= 5917,
    X86_VPANDDZ128rmb	= 5918,
    X86_VPANDDZ128rmbk	= 5919,
    X86_VPANDDZ128rmbkz	= 5920,
    X86_VPANDDZ128rmk	= 5921,
    X86_VPANDDZ128rmkz	= 5922,
    X86_VPANDDZ128rr	= 5923,
    X86_VPANDDZ128rrk	= 5924,
    X86_VPANDDZ128rrkz	= 5925,
    X86_VPANDDZ256rm	= 5926,
    X86_VPANDDZ256rmb	= 5927,
    X86_VPANDDZ256rmbk	= 5928,
    X86_VPANDDZ256rmbkz	= 5929,
    X86_VPANDDZ256rmk	= 5930,
    X86_VPANDDZ256rmkz	= 5931,
    X86_VPANDDZ256rr	= 5932,
    X86_VPANDDZ256rrk	= 5933,
    X86_VPANDDZ256rrkz	= 5934,
    X86_VPANDDZrm	= 5935,
    X86_VPANDDZrmb	= 5936,
    X86_VPANDDZrmbk	= 5937,
    X86_VPANDDZrmbkz	= 5938,
    X86_VPANDDZrmk	= 5939,
    X86_VPANDDZrmkz	= 5940,
    X86_VPANDDZrr	= 5941,
    X86_VPANDDZrrk	= 5942,
    X86_VPANDDZrrkz	= 5943,
    X86_VPANDNDZ128rm	= 5944,
    X86_VPANDNDZ128rmb	= 5945,
    X86_VPANDNDZ128rmbk	= 5946,
    X86_VPANDNDZ128rmbkz	= 5947,
    X86_VPANDNDZ128rmk	= 5948,
    X86_VPANDNDZ128rmkz	= 5949,
    X86_VPANDNDZ128rr	= 5950,
    X86_VPANDNDZ128rrk	= 5951,
    X86_VPANDNDZ128rrkz	= 5952,
    X86_VPANDNDZ256rm	= 5953,
    X86_VPANDNDZ256rmb	= 5954,
    X86_VPANDNDZ256rmbk	= 5955,
    X86_VPANDNDZ256rmbkz	= 5956,
    X86_VPANDNDZ256rmk	= 5957,
    X86_VPANDNDZ256rmkz	= 5958,
    X86_VPANDNDZ256rr	= 5959,
    X86_VPANDNDZ256rrk	= 5960,
    X86_VPANDNDZ256rrkz	= 5961,
    X86_VPANDNDZrm	= 5962,
    X86_VPANDNDZrmb	= 5963,
    X86_VPANDNDZrmbk	= 5964,
    X86_VPANDNDZrmbkz	= 5965,
    X86_VPANDNDZrmk	= 5966,
    X86_VPANDNDZrmkz	= 5967,
    X86_VPANDNDZrr	= 5968,
    X86_VPANDNDZrrk	= 5969,
    X86_VPANDNDZrrkz	= 5970,
    X86_VPANDNQZ128rm	= 5971,
    X86_VPANDNQZ128rmb	= 5972,
    X86_VPANDNQZ128rmbk	= 5973,
    X86_VPANDNQZ128rmbkz	= 5974,
    X86_VPANDNQZ128rmk	= 5975,
    X86_VPANDNQZ128rmkz	= 5976,
    X86_VPANDNQZ128rr	= 5977,
    X86_VPANDNQZ128rrk	= 5978,
    X86_VPANDNQZ128rrkz	= 5979,
    X86_VPANDNQZ256rm	= 5980,
    X86_VPANDNQZ256rmb	= 5981,
    X86_VPANDNQZ256rmbk	= 5982,
    X86_VPANDNQZ256rmbkz	= 5983,
    X86_VPANDNQZ256rmk	= 5984,
    X86_VPANDNQZ256rmkz	= 5985,
    X86_VPANDNQZ256rr	= 5986,
    X86_VPANDNQZ256rrk	= 5987,
    X86_VPANDNQZ256rrkz	= 5988,
    X86_VPANDNQZrm	= 5989,
    X86_VPANDNQZrmb	= 5990,
    X86_VPANDNQZrmbk	= 5991,
    X86_VPANDNQZrmbkz	= 5992,
    X86_VPANDNQZrmk	= 5993,
    X86_VPANDNQZrmkz	= 5994,
    X86_VPANDNQZrr	= 5995,
    X86_VPANDNQZrrk	= 5996,
    X86_VPANDNQZrrkz	= 5997,
    X86_VPANDNYrm	= 5998,
    X86_VPANDNYrr	= 5999,
    X86_VPANDNrm	= 6000,
    X86_VPANDNrr	= 6001,
    X86_VPANDQZ128rm	= 6002,
    X86_VPANDQZ128rmb	= 6003,
    X86_VPANDQZ128rmbk	= 6004,
    X86_VPANDQZ128rmbkz	= 6005,
    X86_VPANDQZ128rmk	= 6006,
    X86_VPANDQZ128rmkz	= 6007,
    X86_VPANDQZ128rr	= 6008,
    X86_VPANDQZ128rrk	= 6009,
    X86_VPANDQZ128rrkz	= 6010,
    X86_VPANDQZ256rm	= 6011,
    X86_VPANDQZ256rmb	= 6012,
    X86_VPANDQZ256rmbk	= 6013,
    X86_VPANDQZ256rmbkz	= 6014,
    X86_VPANDQZ256rmk	= 6015,
    X86_VPANDQZ256rmkz	= 6016,
    X86_VPANDQZ256rr	= 6017,
    X86_VPANDQZ256rrk	= 6018,
    X86_VPANDQZ256rrkz	= 6019,
    X86_VPANDQZrm	= 6020,
    X86_VPANDQZrmb	= 6021,
    X86_VPANDQZrmbk	= 6022,
    X86_VPANDQZrmbkz	= 6023,
    X86_VPANDQZrmk	= 6024,
    X86_VPANDQZrmkz	= 6025,
    X86_VPANDQZrr	= 6026,
    X86_VPANDQZrrk	= 6027,
    X86_VPANDQZrrkz	= 6028,
    X86_VPANDYrm	= 6029,
    X86_VPANDYrr	= 6030,
    X86_VPANDrm	= 6031,
    X86_VPANDrr	= 6032,
    X86_VPAVGBYrm	= 6033,
    X86_VPAVGBYrr	= 6034,
    X86_VPAVGBrm	= 6035,
    X86_VPAVGBrr	= 6036,
    X86_VPAVGWYrm	= 6037,
    X86_VPAVGWYrr	= 6038,
    X86_VPAVGWrm	= 6039,
    X86_VPAVGWrr	= 6040,
    X86_VPBLENDDYrmi	= 6041,
    X86_VPBLENDDYrri	= 6042,
    X86_VPBLENDDrmi	= 6043,
    X86_VPBLENDDrri	= 6044,
    X86_VPBLENDMBZ128rm	= 6045,
    X86_VPBLENDMBZ128rmk	= 6046,
    X86_VPBLENDMBZ128rmkz	= 6047,
    X86_VPBLENDMBZ128rr	= 6048,
    X86_VPBLENDMBZ128rrk	= 6049,
    X86_VPBLENDMBZ128rrkz	= 6050,
    X86_VPBLENDMBZ256rm	= 6051,
    X86_VPBLENDMBZ256rmk	= 6052,
    X86_VPBLENDMBZ256rmkz	= 6053,
    X86_VPBLENDMBZ256rr	= 6054,
    X86_VPBLENDMBZ256rrk	= 6055,
    X86_VPBLENDMBZ256rrkz	= 6056,
    X86_VPBLENDMBZrm	= 6057,
    X86_VPBLENDMBZrmk	= 6058,
    X86_VPBLENDMBZrmkz	= 6059,
    X86_VPBLENDMBZrr	= 6060,
    X86_VPBLENDMBZrrk	= 6061,
    X86_VPBLENDMBZrrkz	= 6062,
    X86_VPBLENDMDZ128rm	= 6063,
    X86_VPBLENDMDZ128rmb	= 6064,
    X86_VPBLENDMDZ128rmbk	= 6065,
    X86_VPBLENDMDZ128rmk	= 6066,
    X86_VPBLENDMDZ128rmkz	= 6067,
    X86_VPBLENDMDZ128rr	= 6068,
    X86_VPBLENDMDZ128rrk	= 6069,
    X86_VPBLENDMDZ128rrkz	= 6070,
    X86_VPBLENDMDZ256rm	= 6071,
    X86_VPBLENDMDZ256rmb	= 6072,
    X86_VPBLENDMDZ256rmbk	= 6073,
    X86_VPBLENDMDZ256rmk	= 6074,
    X86_VPBLENDMDZ256rmkz	= 6075,
    X86_VPBLENDMDZ256rr	= 6076,
    X86_VPBLENDMDZ256rrk	= 6077,
    X86_VPBLENDMDZ256rrkz	= 6078,
    X86_VPBLENDMDZrm	= 6079,
    X86_VPBLENDMDZrmb	= 6080,
    X86_VPBLENDMDZrmbk	= 6081,
    X86_VPBLENDMDZrmk	= 6082,
    X86_VPBLENDMDZrmkz	= 6083,
    X86_VPBLENDMDZrr	= 6084,
    X86_VPBLENDMDZrrk	= 6085,
    X86_VPBLENDMDZrrkz	= 6086,
    X86_VPBLENDMQZ128rm	= 6087,
    X86_VPBLENDMQZ128rmb	= 6088,
    X86_VPBLENDMQZ128rmbk	= 6089,
    X86_VPBLENDMQZ128rmk	= 6090,
    X86_VPBLENDMQZ128rmkz	= 6091,
    X86_VPBLENDMQZ128rr	= 6092,
    X86_VPBLENDMQZ128rrk	= 6093,
    X86_VPBLENDMQZ128rrkz	= 6094,
    X86_VPBLENDMQZ256rm	= 6095,
    X86_VPBLENDMQZ256rmb	= 6096,
    X86_VPBLENDMQZ256rmbk	= 6097,
    X86_VPBLENDMQZ256rmk	= 6098,
    X86_VPBLENDMQZ256rmkz	= 6099,
    X86_VPBLENDMQZ256rr	= 6100,
    X86_VPBLENDMQZ256rrk	= 6101,
    X86_VPBLENDMQZ256rrkz	= 6102,
    X86_VPBLENDMQZrm	= 6103,
    X86_VPBLENDMQZrmb	= 6104,
    X86_VPBLENDMQZrmbk	= 6105,
    X86_VPBLENDMQZrmk	= 6106,
    X86_VPBLENDMQZrmkz	= 6107,
    X86_VPBLENDMQZrr	= 6108,
    X86_VPBLENDMQZrrk	= 6109,
    X86_VPBLENDMQZrrkz	= 6110,
    X86_VPBLENDMWZ128rm	= 6111,
    X86_VPBLENDMWZ128rmk	= 6112,
    X86_VPBLENDMWZ128rmkz	= 6113,
    X86_VPBLENDMWZ128rr	= 6114,
    X86_VPBLENDMWZ128rrk	= 6115,
    X86_VPBLENDMWZ128rrkz	= 6116,
    X86_VPBLENDMWZ256rm	= 6117,
    X86_VPBLENDMWZ256rmk	= 6118,
    X86_VPBLENDMWZ256rmkz	= 6119,
    X86_VPBLENDMWZ256rr	= 6120,
    X86_VPBLENDMWZ256rrk	= 6121,
    X86_VPBLENDMWZ256rrkz	= 6122,
    X86_VPBLENDMWZrm	= 6123,
    X86_VPBLENDMWZrmk	= 6124,
    X86_VPBLENDMWZrmkz	= 6125,
    X86_VPBLENDMWZrr	= 6126,
    X86_VPBLENDMWZrrk	= 6127,
    X86_VPBLENDMWZrrkz	= 6128,
    X86_VPBLENDVBYrm	= 6129,
    X86_VPBLENDVBYrr	= 6130,
    X86_VPBLENDVBrm	= 6131,
    X86_VPBLENDVBrr	= 6132,
    X86_VPBLENDWYrmi	= 6133,
    X86_VPBLENDWYrri	= 6134,
    X86_VPBLENDWrmi	= 6135,
    X86_VPBLENDWrri	= 6136,
    X86_VPBROADCASTBYrm	= 6137,
    X86_VPBROADCASTBYrr	= 6138,
    X86_VPBROADCASTBrZ128r	= 6139,
    X86_VPBROADCASTBrZ128rk	= 6140,
    X86_VPBROADCASTBrZ128rkz	= 6141,
    X86_VPBROADCASTBrZ256r	= 6142,
    X86_VPBROADCASTBrZ256rk	= 6143,
    X86_VPBROADCASTBrZ256rkz	= 6144,
    X86_VPBROADCASTBrZr	= 6145,
    X86_VPBROADCASTBrZrk	= 6146,
    X86_VPBROADCASTBrZrkz	= 6147,
    X86_VPBROADCASTBrm	= 6148,
    X86_VPBROADCASTBrr	= 6149,
    X86_VPBROADCASTDYrm	= 6150,
    X86_VPBROADCASTDYrr	= 6151,
    X86_VPBROADCASTDZkrm	= 6152,
    X86_VPBROADCASTDZkrr	= 6153,
    X86_VPBROADCASTDZrm	= 6154,
    X86_VPBROADCASTDZrr	= 6155,
    X86_VPBROADCASTDrZ128r	= 6156,
    X86_VPBROADCASTDrZ128rk	= 6157,
    X86_VPBROADCASTDrZ128rkz	= 6158,
    X86_VPBROADCASTDrZ256r	= 6159,
    X86_VPBROADCASTDrZ256rk	= 6160,
    X86_VPBROADCASTDrZ256rkz	= 6161,
    X86_VPBROADCASTDrZr	= 6162,
    X86_VPBROADCASTDrZrk	= 6163,
    X86_VPBROADCASTDrZrkz	= 6164,
    X86_VPBROADCASTDrm	= 6165,
    X86_VPBROADCASTDrr	= 6166,
    X86_VPBROADCASTMB2QZ128rr	= 6167,
    X86_VPBROADCASTMB2QZ256rr	= 6168,
    X86_VPBROADCASTMB2QZrr	= 6169,
    X86_VPBROADCASTMW2DZ128rr	= 6170,
    X86_VPBROADCASTMW2DZ256rr	= 6171,
    X86_VPBROADCASTMW2DZrr	= 6172,
    X86_VPBROADCASTQYrm	= 6173,
    X86_VPBROADCASTQYrr	= 6174,
    X86_VPBROADCASTQZkrm	= 6175,
    X86_VPBROADCASTQZkrr	= 6176,
    X86_VPBROADCASTQZrm	= 6177,
    X86_VPBROADCASTQZrr	= 6178,
    X86_VPBROADCASTQrZ128r	= 6179,
    X86_VPBROADCASTQrZ128rk	= 6180,
    X86_VPBROADCASTQrZ128rkz	= 6181,
    X86_VPBROADCASTQrZ256r	= 6182,
    X86_VPBROADCASTQrZ256rk	= 6183,
    X86_VPBROADCASTQrZ256rkz	= 6184,
    X86_VPBROADCASTQrZr	= 6185,
    X86_VPBROADCASTQrZrk	= 6186,
    X86_VPBROADCASTQrZrkz	= 6187,
    X86_VPBROADCASTQrm	= 6188,
    X86_VPBROADCASTQrr	= 6189,
    X86_VPBROADCASTWYrm	= 6190,
    X86_VPBROADCASTWYrr	= 6191,
    X86_VPBROADCASTWrZ128r	= 6192,
    X86_VPBROADCASTWrZ128rk	= 6193,
    X86_VPBROADCASTWrZ128rkz	= 6194,
    X86_VPBROADCASTWrZ256r	= 6195,
    X86_VPBROADCASTWrZ256rk	= 6196,
    X86_VPBROADCASTWrZ256rkz	= 6197,
    X86_VPBROADCASTWrZr	= 6198,
    X86_VPBROADCASTWrZrk	= 6199,
    X86_VPBROADCASTWrZrkz	= 6200,
    X86_VPBROADCASTWrm	= 6201,
    X86_VPBROADCASTWrr	= 6202,
    X86_VPCLMULQDQrm	= 6203,
    X86_VPCLMULQDQrr	= 6204,
    X86_VPCMOVmr	= 6205,
    X86_VPCMOVmrY	= 6206,
    X86_VPCMOVrm	= 6207,
    X86_VPCMOVrmY	= 6208,
    X86_VPCMOVrr	= 6209,
    X86_VPCMOVrrY	= 6210,
    X86_VPCMPBZ128rmi	= 6211,
    X86_VPCMPBZ128rmi_alt	= 6212,
    X86_VPCMPBZ128rmik	= 6213,
    X86_VPCMPBZ128rmik_alt	= 6214,
    X86_VPCMPBZ128rri	= 6215,
    X86_VPCMPBZ128rri_alt	= 6216,
    X86_VPCMPBZ128rrik	= 6217,
    X86_VPCMPBZ128rrik_alt	= 6218,
    X86_VPCMPBZ256rmi	= 6219,
    X86_VPCMPBZ256rmi_alt	= 6220,
    X86_VPCMPBZ256rmik	= 6221,
    X86_VPCMPBZ256rmik_alt	= 6222,
    X86_VPCMPBZ256rri	= 6223,
    X86_VPCMPBZ256rri_alt	= 6224,
    X86_VPCMPBZ256rrik	= 6225,
    X86_VPCMPBZ256rrik_alt	= 6226,
    X86_VPCMPBZrmi	= 6227,
    X86_VPCMPBZrmi_alt	= 6228,
    X86_VPCMPBZrmik	= 6229,
    X86_VPCMPBZrmik_alt	= 6230,
    X86_VPCMPBZrri	= 6231,
    X86_VPCMPBZrri_alt	= 6232,
    X86_VPCMPBZrrik	= 6233,
    X86_VPCMPBZrrik_alt	= 6234,
    X86_VPCMPDZ128rmi	= 6235,
    X86_VPCMPDZ128rmi_alt	= 6236,
    X86_VPCMPDZ128rmib	= 6237,
    X86_VPCMPDZ128rmib_alt	= 6238,
    X86_VPCMPDZ128rmibk	= 6239,
    X86_VPCMPDZ128rmibk_alt	= 6240,
    X86_VPCMPDZ128rmik	= 6241,
    X86_VPCMPDZ128rmik_alt	= 6242,
    X86_VPCMPDZ128rri	= 6243,
    X86_VPCMPDZ128rri_alt	= 6244,
    X86_VPCMPDZ128rrik	= 6245,
    X86_VPCMPDZ128rrik_alt	= 6246,
    X86_VPCMPDZ256rmi	= 6247,
    X86_VPCMPDZ256rmi_alt	= 6248,
    X86_VPCMPDZ256rmib	= 6249,
    X86_VPCMPDZ256rmib_alt	= 6250,
    X86_VPCMPDZ256rmibk	= 6251,
    X86_VPCMPDZ256rmibk_alt	= 6252,
    X86_VPCMPDZ256rmik	= 6253,
    X86_VPCMPDZ256rmik_alt	= 6254,
    X86_VPCMPDZ256rri	= 6255,
    X86_VPCMPDZ256rri_alt	= 6256,
    X86_VPCMPDZ256rrik	= 6257,
    X86_VPCMPDZ256rrik_alt	= 6258,
    X86_VPCMPDZrmi	= 6259,
    X86_VPCMPDZrmi_alt	= 6260,
    X86_VPCMPDZrmib	= 6261,
    X86_VPCMPDZrmib_alt	= 6262,
    X86_VPCMPDZrmibk	= 6263,
    X86_VPCMPDZrmibk_alt	= 6264,
    X86_VPCMPDZrmik	= 6265,
    X86_VPCMPDZrmik_alt	= 6266,
    X86_VPCMPDZrri	= 6267,
    X86_VPCMPDZrri_alt	= 6268,
    X86_VPCMPDZrrik	= 6269,
    X86_VPCMPDZrrik_alt	= 6270,
    X86_VPCMPEQBYrm	= 6271,
    X86_VPCMPEQBYrr	= 6272,
    X86_VPCMPEQBZ128rm	= 6273,
    X86_VPCMPEQBZ128rmk	= 6274,
    X86_VPCMPEQBZ128rr	= 6275,
    X86_VPCMPEQBZ128rrk	= 6276,
    X86_VPCMPEQBZ256rm	= 6277,
    X86_VPCMPEQBZ256rmk	= 6278,
    X86_VPCMPEQBZ256rr	= 6279,
    X86_VPCMPEQBZ256rrk	= 6280,
    X86_VPCMPEQBZrm	= 6281,
    X86_VPCMPEQBZrmk	= 6282,
    X86_VPCMPEQBZrr	= 6283,
    X86_VPCMPEQBZrrk	= 6284,
    X86_VPCMPEQBrm	= 6285,
    X86_VPCMPEQBrr	= 6286,
    X86_VPCMPEQDYrm	= 6287,
    X86_VPCMPEQDYrr	= 6288,
    X86_VPCMPEQDZ128rm	= 6289,
    X86_VPCMPEQDZ128rmb	= 6290,
    X86_VPCMPEQDZ128rmbk	= 6291,
    X86_VPCMPEQDZ128rmk	= 6292,
    X86_VPCMPEQDZ128rr	= 6293,
    X86_VPCMPEQDZ128rrk	= 6294,
    X86_VPCMPEQDZ256rm	= 6295,
    X86_VPCMPEQDZ256rmb	= 6296,
    X86_VPCMPEQDZ256rmbk	= 6297,
    X86_VPCMPEQDZ256rmk	= 6298,
    X86_VPCMPEQDZ256rr	= 6299,
    X86_VPCMPEQDZ256rrk	= 6300,
    X86_VPCMPEQDZrm	= 6301,
    X86_VPCMPEQDZrmb	= 6302,
    X86_VPCMPEQDZrmbk	= 6303,
    X86_VPCMPEQDZrmk	= 6304,
    X86_VPCMPEQDZrr	= 6305,
    X86_VPCMPEQDZrrk	= 6306,
    X86_VPCMPEQDrm	= 6307,
    X86_VPCMPEQDrr	= 6308,
    X86_VPCMPEQQYrm	= 6309,
    X86_VPCMPEQQYrr	= 6310,
    X86_VPCMPEQQZ128rm	= 6311,
    X86_VPCMPEQQZ128rmb	= 6312,
    X86_VPCMPEQQZ128rmbk	= 6313,
    X86_VPCMPEQQZ128rmk	= 6314,
    X86_VPCMPEQQZ128rr	= 6315,
    X86_VPCMPEQQZ128rrk	= 6316,
    X86_VPCMPEQQZ256rm	= 6317,
    X86_VPCMPEQQZ256rmb	= 6318,
    X86_VPCMPEQQZ256rmbk	= 6319,
    X86_VPCMPEQQZ256rmk	= 6320,
    X86_VPCMPEQQZ256rr	= 6321,
    X86_VPCMPEQQZ256rrk	= 6322,
    X86_VPCMPEQQZrm	= 6323,
    X86_VPCMPEQQZrmb	= 6324,
    X86_VPCMPEQQZrmbk	= 6325,
    X86_VPCMPEQQZrmk	= 6326,
    X86_VPCMPEQQZrr	= 6327,
    X86_VPCMPEQQZrrk	= 6328,
    X86_VPCMPEQQrm	= 6329,
    X86_VPCMPEQQrr	= 6330,
    X86_VPCMPEQWYrm	= 6331,
    X86_VPCMPEQWYrr	= 6332,
    X86_VPCMPEQWZ128rm	= 6333,
    X86_VPCMPEQWZ128rmk	= 6334,
    X86_VPCMPEQWZ128rr	= 6335,
    X86_VPCMPEQWZ128rrk	= 6336,
    X86_VPCMPEQWZ256rm	= 6337,
    X86_VPCMPEQWZ256rmk	= 6338,
    X86_VPCMPEQWZ256rr	= 6339,
    X86_VPCMPEQWZ256rrk	= 6340,
    X86_VPCMPEQWZrm	= 6341,
    X86_VPCMPEQWZrmk	= 6342,
    X86_VPCMPEQWZrr	= 6343,
    X86_VPCMPEQWZrrk	= 6344,
    X86_VPCMPEQWrm	= 6345,
    X86_VPCMPEQWrr	= 6346,
    X86_VPCMPESTRIMEM	= 6347,
    X86_VPCMPESTRIREG	= 6348,
    X86_VPCMPESTRIrm	= 6349,
    X86_VPCMPESTRIrr	= 6350,
    X86_VPCMPESTRM128MEM	= 6351,
    X86_VPCMPESTRM128REG	= 6352,
    X86_VPCMPESTRM128rm	= 6353,
    X86_VPCMPESTRM128rr	= 6354,
    X86_VPCMPGTBYrm	= 6355,
    X86_VPCMPGTBYrr	= 6356,
    X86_VPCMPGTBZ128rm	= 6357,
    X86_VPCMPGTBZ128rmk	= 6358,
    X86_VPCMPGTBZ128rr	= 6359,
    X86_VPCMPGTBZ128rrk	= 6360,
    X86_VPCMPGTBZ256rm	= 6361,
    X86_VPCMPGTBZ256rmk	= 6362,
    X86_VPCMPGTBZ256rr	= 6363,
    X86_VPCMPGTBZ256rrk	= 6364,
    X86_VPCMPGTBZrm	= 6365,
    X86_VPCMPGTBZrmk	= 6366,
    X86_VPCMPGTBZrr	= 6367,
    X86_VPCMPGTBZrrk	= 6368,
    X86_VPCMPGTBrm	= 6369,
    X86_VPCMPGTBrr	= 6370,
    X86_VPCMPGTDYrm	= 6371,
    X86_VPCMPGTDYrr	= 6372,
    X86_VPCMPGTDZ128rm	= 6373,
    X86_VPCMPGTDZ128rmb	= 6374,
    X86_VPCMPGTDZ128rmbk	= 6375,
    X86_VPCMPGTDZ128rmk	= 6376,
    X86_VPCMPGTDZ128rr	= 6377,
    X86_VPCMPGTDZ128rrk	= 6378,
    X86_VPCMPGTDZ256rm	= 6379,
    X86_VPCMPGTDZ256rmb	= 6380,
    X86_VPCMPGTDZ256rmbk	= 6381,
    X86_VPCMPGTDZ256rmk	= 6382,
    X86_VPCMPGTDZ256rr	= 6383,
    X86_VPCMPGTDZ256rrk	= 6384,
    X86_VPCMPGTDZrm	= 6385,
    X86_VPCMPGTDZrmb	= 6386,
    X86_VPCMPGTDZrmbk	= 6387,
    X86_VPCMPGTDZrmk	= 6388,
    X86_VPCMPGTDZrr	= 6389,
    X86_VPCMPGTDZrrk	= 6390,
    X86_VPCMPGTDrm	= 6391,
    X86_VPCMPGTDrr	= 6392,
    X86_VPCMPGTQYrm	= 6393,
    X86_VPCMPGTQYrr	= 6394,
    X86_VPCMPGTQZ128rm	= 6395,
    X86_VPCMPGTQZ128rmb	= 6396,
    X86_VPCMPGTQZ128rmbk	= 6397,
    X86_VPCMPGTQZ128rmk	= 6398,
    X86_VPCMPGTQZ128rr	= 6399,
    X86_VPCMPGTQZ128rrk	= 6400,
    X86_VPCMPGTQZ256rm	= 6401,
    X86_VPCMPGTQZ256rmb	= 6402,
    X86_VPCMPGTQZ256rmbk	= 6403,
    X86_VPCMPGTQZ256rmk	= 6404,
    X86_VPCMPGTQZ256rr	= 6405,
    X86_VPCMPGTQZ256rrk	= 6406,
    X86_VPCMPGTQZrm	= 6407,
    X86_VPCMPGTQZrmb	= 6408,
    X86_VPCMPGTQZrmbk	= 6409,
    X86_VPCMPGTQZrmk	= 6410,
    X86_VPCMPGTQZrr	= 6411,
    X86_VPCMPGTQZrrk	= 6412,
    X86_VPCMPGTQrm	= 6413,
    X86_VPCMPGTQrr	= 6414,
    X86_VPCMPGTWYrm	= 6415,
    X86_VPCMPGTWYrr	= 6416,
    X86_VPCMPGTWZ128rm	= 6417,
    X86_VPCMPGTWZ128rmk	= 6418,
    X86_VPCMPGTWZ128rr	= 6419,
    X86_VPCMPGTWZ128rrk	= 6420,
    X86_VPCMPGTWZ256rm	= 6421,
    X86_VPCMPGTWZ256rmk	= 6422,
    X86_VPCMPGTWZ256rr	= 6423,
    X86_VPCMPGTWZ256rrk	= 6424,
    X86_VPCMPGTWZrm	= 6425,
    X86_VPCMPGTWZrmk	= 6426,
    X86_VPCMPGTWZrr	= 6427,
    X86_VPCMPGTWZrrk	= 6428,
    X86_VPCMPGTWrm	= 6429,
    X86_VPCMPGTWrr	= 6430,
    X86_VPCMPISTRIMEM	= 6431,
    X86_VPCMPISTRIREG	= 6432,
    X86_VPCMPISTRIrm	= 6433,
    X86_VPCMPISTRIrr	= 6434,
    X86_VPCMPISTRM128MEM	= 6435,
    X86_VPCMPISTRM128REG	= 6436,
    X86_VPCMPISTRM128rm	= 6437,
    X86_VPCMPISTRM128rr	= 6438,
    X86_VPCMPQZ128rmi	= 6439,
    X86_VPCMPQZ128rmi_alt	= 6440,
    X86_VPCMPQZ128rmib	= 6441,
    X86_VPCMPQZ128rmib_alt	= 6442,
    X86_VPCMPQZ128rmibk	= 6443,
    X86_VPCMPQZ128rmibk_alt	= 6444,
    X86_VPCMPQZ128rmik	= 6445,
    X86_VPCMPQZ128rmik_alt	= 6446,
    X86_VPCMPQZ128rri	= 6447,
    X86_VPCMPQZ128rri_alt	= 6448,
    X86_VPCMPQZ128rrik	= 6449,
    X86_VPCMPQZ128rrik_alt	= 6450,
    X86_VPCMPQZ256rmi	= 6451,
    X86_VPCMPQZ256rmi_alt	= 6452,
    X86_VPCMPQZ256rmib	= 6453,
    X86_VPCMPQZ256rmib_alt	= 6454,
    X86_VPCMPQZ256rmibk	= 6455,
    X86_VPCMPQZ256rmibk_alt	= 6456,
    X86_VPCMPQZ256rmik	= 6457,
    X86_VPCMPQZ256rmik_alt	= 6458,
    X86_VPCMPQZ256rri	= 6459,
    X86_VPCMPQZ256rri_alt	= 6460,
    X86_VPCMPQZ256rrik	= 6461,
    X86_VPCMPQZ256rrik_alt	= 6462,
    X86_VPCMPQZrmi	= 6463,
    X86_VPCMPQZrmi_alt	= 6464,
    X86_VPCMPQZrmib	= 6465,
    X86_VPCMPQZrmib_alt	= 6466,
    X86_VPCMPQZrmibk	= 6467,
    X86_VPCMPQZrmibk_alt	= 6468,
    X86_VPCMPQZrmik	= 6469,
    X86_VPCMPQZrmik_alt	= 6470,
    X86_VPCMPQZrri	= 6471,
    X86_VPCMPQZrri_alt	= 6472,
    X86_VPCMPQZrrik	= 6473,
    X86_VPCMPQZrrik_alt	= 6474,
    X86_VPCMPUBZ128rmi	= 6475,
    X86_VPCMPUBZ128rmi_alt	= 6476,
    X86_VPCMPUBZ128rmik	= 6477,
    X86_VPCMPUBZ128rmik_alt	= 6478,
    X86_VPCMPUBZ128rri	= 6479,
    X86_VPCMPUBZ128rri_alt	= 6480,
    X86_VPCMPUBZ128rrik	= 6481,
    X86_VPCMPUBZ128rrik_alt	= 6482,
    X86_VPCMPUBZ256rmi	= 6483,
    X86_VPCMPUBZ256rmi_alt	= 6484,
    X86_VPCMPUBZ256rmik	= 6485,
    X86_VPCMPUBZ256rmik_alt	= 6486,
    X86_VPCMPUBZ256rri	= 6487,
    X86_VPCMPUBZ256rri_alt	= 6488,
    X86_VPCMPUBZ256rrik	= 6489,
    X86_VPCMPUBZ256rrik_alt	= 6490,
    X86_VPCMPUBZrmi	= 6491,
    X86_VPCMPUBZrmi_alt	= 6492,
    X86_VPCMPUBZrmik	= 6493,
    X86_VPCMPUBZrmik_alt	= 6494,
    X86_VPCMPUBZrri	= 6495,
    X86_VPCMPUBZrri_alt	= 6496,
    X86_VPCMPUBZrrik	= 6497,
    X86_VPCMPUBZrrik_alt	= 6498,
    X86_VPCMPUDZ128rmi	= 6499,
    X86_VPCMPUDZ128rmi_alt	= 6500,
    X86_VPCMPUDZ128rmib	= 6501,
    X86_VPCMPUDZ128rmib_alt	= 6502,
    X86_VPCMPUDZ128rmibk	= 6503,
    X86_VPCMPUDZ128rmibk_alt	= 6504,
    X86_VPCMPUDZ128rmik	= 6505,
    X86_VPCMPUDZ128rmik_alt	= 6506,
    X86_VPCMPUDZ128rri	= 6507,
    X86_VPCMPUDZ128rri_alt	= 6508,
    X86_VPCMPUDZ128rrik	= 6509,
    X86_VPCMPUDZ128rrik_alt	= 6510,
    X86_VPCMPUDZ256rmi	= 6511,
    X86_VPCMPUDZ256rmi_alt	= 6512,
    X86_VPCMPUDZ256rmib	= 6513,
    X86_VPCMPUDZ256rmib_alt	= 6514,
    X86_VPCMPUDZ256rmibk	= 6515,
    X86_VPCMPUDZ256rmibk_alt	= 6516,
    X86_VPCMPUDZ256rmik	= 6517,
    X86_VPCMPUDZ256rmik_alt	= 6518,
    X86_VPCMPUDZ256rri	= 6519,
    X86_VPCMPUDZ256rri_alt	= 6520,
    X86_VPCMPUDZ256rrik	= 6521,
    X86_VPCMPUDZ256rrik_alt	= 6522,
    X86_VPCMPUDZrmi	= 6523,
    X86_VPCMPUDZrmi_alt	= 6524,
    X86_VPCMPUDZrmib	= 6525,
    X86_VPCMPUDZrmib_alt	= 6526,
    X86_VPCMPUDZrmibk	= 6527,
    X86_VPCMPUDZrmibk_alt	= 6528,
    X86_VPCMPUDZrmik	= 6529,
    X86_VPCMPUDZrmik_alt	= 6530,
    X86_VPCMPUDZrri	= 6531,
    X86_VPCMPUDZrri_alt	= 6532,
    X86_VPCMPUDZrrik	= 6533,
    X86_VPCMPUDZrrik_alt	= 6534,
    X86_VPCMPUQZ128rmi	= 6535,
    X86_VPCMPUQZ128rmi_alt	= 6536,
    X86_VPCMPUQZ128rmib	= 6537,
    X86_VPCMPUQZ128rmib_alt	= 6538,
    X86_VPCMPUQZ128rmibk	= 6539,
    X86_VPCMPUQZ128rmibk_alt	= 6540,
    X86_VPCMPUQZ128rmik	= 6541,
    X86_VPCMPUQZ128rmik_alt	= 6542,
    X86_VPCMPUQZ128rri	= 6543,
    X86_VPCMPUQZ128rri_alt	= 6544,
    X86_VPCMPUQZ128rrik	= 6545,
    X86_VPCMPUQZ128rrik_alt	= 6546,
    X86_VPCMPUQZ256rmi	= 6547,
    X86_VPCMPUQZ256rmi_alt	= 6548,
    X86_VPCMPUQZ256rmib	= 6549,
    X86_VPCMPUQZ256rmib_alt	= 6550,
    X86_VPCMPUQZ256rmibk	= 6551,
    X86_VPCMPUQZ256rmibk_alt	= 6552,
    X86_VPCMPUQZ256rmik	= 6553,
    X86_VPCMPUQZ256rmik_alt	= 6554,
    X86_VPCMPUQZ256rri	= 6555,
    X86_VPCMPUQZ256rri_alt	= 6556,
    X86_VPCMPUQZ256rrik	= 6557,
    X86_VPCMPUQZ256rrik_alt	= 6558,
    X86_VPCMPUQZrmi	= 6559,
    X86_VPCMPUQZrmi_alt	= 6560,
    X86_VPCMPUQZrmib	= 6561,
    X86_VPCMPUQZrmib_alt	= 6562,
    X86_VPCMPUQZrmibk	= 6563,
    X86_VPCMPUQZrmibk_alt	= 6564,
    X86_VPCMPUQZrmik	= 6565,
    X86_VPCMPUQZrmik_alt	= 6566,
    X86_VPCMPUQZrri	= 6567,
    X86_VPCMPUQZrri_alt	= 6568,
    X86_VPCMPUQZrrik	= 6569,
    X86_VPCMPUQZrrik_alt	= 6570,
    X86_VPCMPUWZ128rmi	= 6571,
    X86_VPCMPUWZ128rmi_alt	= 6572,
    X86_VPCMPUWZ128rmik	= 6573,
    X86_VPCMPUWZ128rmik_alt	= 6574,
    X86_VPCMPUWZ128rri	= 6575,
    X86_VPCMPUWZ128rri_alt	= 6576,
    X86_VPCMPUWZ128rrik	= 6577,
    X86_VPCMPUWZ128rrik_alt	= 6578,
    X86_VPCMPUWZ256rmi	= 6579,
    X86_VPCMPUWZ256rmi_alt	= 6580,
    X86_VPCMPUWZ256rmik	= 6581,
    X86_VPCMPUWZ256rmik_alt	= 6582,
    X86_VPCMPUWZ256rri	= 6583,
    X86_VPCMPUWZ256rri_alt	= 6584,
    X86_VPCMPUWZ256rrik	= 6585,
    X86_VPCMPUWZ256rrik_alt	= 6586,
    X86_VPCMPUWZrmi	= 6587,
    X86_VPCMPUWZrmi_alt	= 6588,
    X86_VPCMPUWZrmik	= 6589,
    X86_VPCMPUWZrmik_alt	= 6590,
    X86_VPCMPUWZrri	= 6591,
    X86_VPCMPUWZrri_alt	= 6592,
    X86_VPCMPUWZrrik	= 6593,
    X86_VPCMPUWZrrik_alt	= 6594,
    X86_VPCMPWZ128rmi	= 6595,
    X86_VPCMPWZ128rmi_alt	= 6596,
    X86_VPCMPWZ128rmik	= 6597,
    X86_VPCMPWZ128rmik_alt	= 6598,
    X86_VPCMPWZ128rri	= 6599,
    X86_VPCMPWZ128rri_alt	= 6600,
    X86_VPCMPWZ128rrik	= 6601,
    X86_VPCMPWZ128rrik_alt	= 6602,
    X86_VPCMPWZ256rmi	= 6603,
    X86_VPCMPWZ256rmi_alt	= 6604,
    X86_VPCMPWZ256rmik	= 6605,
    X86_VPCMPWZ256rmik_alt	= 6606,
    X86_VPCMPWZ256rri	= 6607,
    X86_VPCMPWZ256rri_alt	= 6608,
    X86_VPCMPWZ256rrik	= 6609,
    X86_VPCMPWZ256rrik_alt	= 6610,
    X86_VPCMPWZrmi	= 6611,
    X86_VPCMPWZrmi_alt	= 6612,
    X86_VPCMPWZrmik	= 6613,
    X86_VPCMPWZrmik_alt	= 6614,
    X86_VPCMPWZrri	= 6615,
    X86_VPCMPWZrri_alt	= 6616,
    X86_VPCMPWZrrik	= 6617,
    X86_VPCMPWZrrik_alt	= 6618,
    X86_VPCOMBmi	= 6619,
    X86_VPCOMBmi_alt	= 6620,
    X86_VPCOMBri	= 6621,
    X86_VPCOMBri_alt	= 6622,
    X86_VPCOMDmi	= 6623,
    X86_VPCOMDmi_alt	= 6624,
    X86_VPCOMDri	= 6625,
    X86_VPCOMDri_alt	= 6626,
    X86_VPCOMPRESSDZ128mrk	= 6627,
    X86_VPCOMPRESSDZ128rrk	= 6628,
    X86_VPCOMPRESSDZ128rrkz	= 6629,
    X86_VPCOMPRESSDZ256mrk	= 6630,
    X86_VPCOMPRESSDZ256rrk	= 6631,
    X86_VPCOMPRESSDZ256rrkz	= 6632,
    X86_VPCOMPRESSDZmrk	= 6633,
    X86_VPCOMPRESSDZrrk	= 6634,
    X86_VPCOMPRESSDZrrkz	= 6635,
    X86_VPCOMPRESSQZ128mrk	= 6636,
    X86_VPCOMPRESSQZ128rrk	= 6637,
    X86_VPCOMPRESSQZ128rrkz	= 6638,
    X86_VPCOMPRESSQZ256mrk	= 6639,
    X86_VPCOMPRESSQZ256rrk	= 6640,
    X86_VPCOMPRESSQZ256rrkz	= 6641,
    X86_VPCOMPRESSQZmrk	= 6642,
    X86_VPCOMPRESSQZrrk	= 6643,
    X86_VPCOMPRESSQZrrkz	= 6644,
    X86_VPCOMQmi	= 6645,
    X86_VPCOMQmi_alt	= 6646,
    X86_VPCOMQri	= 6647,
    X86_VPCOMQri_alt	= 6648,
    X86_VPCOMUBmi	= 6649,
    X86_VPCOMUBmi_alt	= 6650,
    X86_VPCOMUBri	= 6651,
    X86_VPCOMUBri_alt	= 6652,
    X86_VPCOMUDmi	= 6653,
    X86_VPCOMUDmi_alt	= 6654,
    X86_VPCOMUDri	= 6655,
    X86_VPCOMUDri_alt	= 6656,
    X86_VPCOMUQmi	= 6657,
    X86_VPCOMUQmi_alt	= 6658,
    X86_VPCOMUQri	= 6659,
    X86_VPCOMUQri_alt	= 6660,
    X86_VPCOMUWmi	= 6661,
    X86_VPCOMUWmi_alt	= 6662,
    X86_VPCOMUWri	= 6663,
    X86_VPCOMUWri_alt	= 6664,
    X86_VPCOMWmi	= 6665,
    X86_VPCOMWmi_alt	= 6666,
    X86_VPCOMWri	= 6667,
    X86_VPCOMWri_alt	= 6668,
    X86_VPCONFLICTDrm	= 6669,
    X86_VPCONFLICTDrmb	= 6670,
    X86_VPCONFLICTDrmbk	= 6671,
    X86_VPCONFLICTDrmbkz	= 6672,
    X86_VPCONFLICTDrmk	= 6673,
    X86_VPCONFLICTDrmkz	= 6674,
    X86_VPCONFLICTDrr	= 6675,
    X86_VPCONFLICTDrrk	= 6676,
    X86_VPCONFLICTDrrkz	= 6677,
    X86_VPCONFLICTQrm	= 6678,
    X86_VPCONFLICTQrmb	= 6679,
    X86_VPCONFLICTQrmbk	= 6680,
    X86_VPCONFLICTQrmbkz	= 6681,
    X86_VPCONFLICTQrmk	= 6682,
    X86_VPCONFLICTQrmkz	= 6683,
    X86_VPCONFLICTQrr	= 6684,
    X86_VPCONFLICTQrrk	= 6685,
    X86_VPCONFLICTQrrkz	= 6686,
    X86_VPERM2F128rm	= 6687,
    X86_VPERM2F128rr	= 6688,
    X86_VPERM2I128rm	= 6689,
    X86_VPERM2I128rr	= 6690,
    X86_VPERMDYrm	= 6691,
    X86_VPERMDYrr	= 6692,
    X86_VPERMDZrm	= 6693,
    X86_VPERMDZrr	= 6694,
    X86_VPERMI2Drm	= 6695,
    X86_VPERMI2Drmk	= 6696,
    X86_VPERMI2Drmkz	= 6697,
    X86_VPERMI2Drr	= 6698,
    X86_VPERMI2Drrk	= 6699,
    X86_VPERMI2Drrkz	= 6700,
    X86_VPERMI2PDrm	= 6701,
    X86_VPERMI2PDrmk	= 6702,
    X86_VPERMI2PDrmkz	= 6703,
    X86_VPERMI2PDrr	= 6704,
    X86_VPERMI2PDrrk	= 6705,
    X86_VPERMI2PDrrkz	= 6706,
    X86_VPERMI2PSrm	= 6707,
    X86_VPERMI2PSrmk	= 6708,
    X86_VPERMI2PSrmkz	= 6709,
    X86_VPERMI2PSrr	= 6710,
    X86_VPERMI2PSrrk	= 6711,
    X86_VPERMI2PSrrkz	= 6712,
    X86_VPERMI2Qrm	= 6713,
    X86_VPERMI2Qrmk	= 6714,
    X86_VPERMI2Qrmkz	= 6715,
    X86_VPERMI2Qrr	= 6716,
    X86_VPERMI2Qrrk	= 6717,
    X86_VPERMI2Qrrkz	= 6718,
    X86_VPERMIL2PDmr	= 6719,
    X86_VPERMIL2PDmrY	= 6720,
    X86_VPERMIL2PDrm	= 6721,
    X86_VPERMIL2PDrmY	= 6722,
    X86_VPERMIL2PDrr	= 6723,
    X86_VPERMIL2PDrrY	= 6724,
    X86_VPERMIL2PSmr	= 6725,
    X86_VPERMIL2PSmrY	= 6726,
    X86_VPERMIL2PSrm	= 6727,
    X86_VPERMIL2PSrmY	= 6728,
    X86_VPERMIL2PSrr	= 6729,
    X86_VPERMIL2PSrrY	= 6730,
    X86_VPERMILPDYmi	= 6731,
    X86_VPERMILPDYri	= 6732,
    X86_VPERMILPDYrm	= 6733,
    X86_VPERMILPDYrr	= 6734,
    X86_VPERMILPDZmi	= 6735,
    X86_VPERMILPDZri	= 6736,
    X86_VPERMILPDZrm	= 6737,
    X86_VPERMILPDZrr	= 6738,
    X86_VPERMILPDmi	= 6739,
    X86_VPERMILPDri	= 6740,
    X86_VPERMILPDrm	= 6741,
    X86_VPERMILPDrr	= 6742,
    X86_VPERMILPSYmi	= 6743,
    X86_VPERMILPSYri	= 6744,
    X86_VPERMILPSYrm	= 6745,
    X86_VPERMILPSYrr	= 6746,
    X86_VPERMILPSZmi	= 6747,
    X86_VPERMILPSZri	= 6748,
    X86_VPERMILPSZrm	= 6749,
    X86_VPERMILPSZrr	= 6750,
    X86_VPERMILPSmi	= 6751,
    X86_VPERMILPSri	= 6752,
    X86_VPERMILPSrm	= 6753,
    X86_VPERMILPSrr	= 6754,
    X86_VPERMPDYmi	= 6755,
    X86_VPERMPDYri	= 6756,
    X86_VPERMPDZmi	= 6757,
    X86_VPERMPDZri	= 6758,
    X86_VPERMPDZrm	= 6759,
    X86_VPERMPDZrr	= 6760,
    X86_VPERMPSYrm	= 6761,
    X86_VPERMPSYrr	= 6762,
    X86_VPERMPSZrm	= 6763,
    X86_VPERMPSZrr	= 6764,
    X86_VPERMQYmi	= 6765,
    X86_VPERMQYri	= 6766,
    X86_VPERMQZmi	= 6767,
    X86_VPERMQZri	= 6768,
    X86_VPERMQZrm	= 6769,
    X86_VPERMQZrr	= 6770,
    X86_VPERMT2Drm	= 6771,
    X86_VPERMT2Drmk	= 6772,
    X86_VPERMT2Drmkz	= 6773,
    X86_VPERMT2Drr	= 6774,
    X86_VPERMT2Drrk	= 6775,
    X86_VPERMT2Drrkz	= 6776,
    X86_VPERMT2PDrm	= 6777,
    X86_VPERMT2PDrmk	= 6778,
    X86_VPERMT2PDrmkz	= 6779,
    X86_VPERMT2PDrr	= 6780,
    X86_VPERMT2PDrrk	= 6781,
    X86_VPERMT2PDrrkz	= 6782,
    X86_VPERMT2PSrm	= 6783,
    X86_VPERMT2PSrmk	= 6784,
    X86_VPERMT2PSrmkz	= 6785,
    X86_VPERMT2PSrr	= 6786,
    X86_VPERMT2PSrrk	= 6787,
    X86_VPERMT2PSrrkz	= 6788,
    X86_VPERMT2Qrm	= 6789,
    X86_VPERMT2Qrmk	= 6790,
    X86_VPERMT2Qrmkz	= 6791,
    X86_VPERMT2Qrr	= 6792,
    X86_VPERMT2Qrrk	= 6793,
    X86_VPERMT2Qrrkz	= 6794,
    X86_VPEXPANDDZ128rmk	= 6795,
    X86_VPEXPANDDZ128rmkz	= 6796,
    X86_VPEXPANDDZ128rrk	= 6797,
    X86_VPEXPANDDZ128rrkz	= 6798,
    X86_VPEXPANDDZ256rmk	= 6799,
    X86_VPEXPANDDZ256rmkz	= 6800,
    X86_VPEXPANDDZ256rrk	= 6801,
    X86_VPEXPANDDZ256rrkz	= 6802,
    X86_VPEXPANDDZrmk	= 6803,
    X86_VPEXPANDDZrmkz	= 6804,
    X86_VPEXPANDDZrrk	= 6805,
    X86_VPEXPANDDZrrkz	= 6806,
    X86_VPEXPANDQZ128rmk	= 6807,
    X86_VPEXPANDQZ128rmkz	= 6808,
    X86_VPEXPANDQZ128rrk	= 6809,
    X86_VPEXPANDQZ128rrkz	= 6810,
    X86_VPEXPANDQZ256rmk	= 6811,
    X86_VPEXPANDQZ256rmkz	= 6812,
    X86_VPEXPANDQZ256rrk	= 6813,
    X86_VPEXPANDQZ256rrkz	= 6814,
    X86_VPEXPANDQZrmk	= 6815,
    X86_VPEXPANDQZrmkz	= 6816,
    X86_VPEXPANDQZrrk	= 6817,
    X86_VPEXPANDQZrrkz	= 6818,
    X86_VPEXTRBmr	= 6819,
    X86_VPEXTRBrr	= 6820,
    X86_VPEXTRDmr	= 6821,
    X86_VPEXTRDrr	= 6822,
    X86_VPEXTRQmr	= 6823,
    X86_VPEXTRQrr	= 6824,
    X86_VPEXTRWmr	= 6825,
    X86_VPEXTRWri	= 6826,
    X86_VPEXTRWrr_REV	= 6827,
    X86_VPGATHERDDYrm	= 6828,
    X86_VPGATHERDDZrm	= 6829,
    X86_VPGATHERDDrm	= 6830,
    X86_VPGATHERDQYrm	= 6831,
    X86_VPGATHERDQZrm	= 6832,
    X86_VPGATHERDQrm	= 6833,
    X86_VPGATHERQDYrm	= 6834,
    X86_VPGATHERQDZrm	= 6835,
    X86_VPGATHERQDrm	= 6836,
    X86_VPGATHERQQYrm	= 6837,
    X86_VPGATHERQQZrm	= 6838,
    X86_VPGATHERQQrm	= 6839,
    X86_VPHADDBDrm	= 6840,
    X86_VPHADDBDrr	= 6841,
    X86_VPHADDBQrm	= 6842,
    X86_VPHADDBQrr	= 6843,
    X86_VPHADDBWrm	= 6844,
    X86_VPHADDBWrr	= 6845,
    X86_VPHADDDQrm	= 6846,
    X86_VPHADDDQrr	= 6847,
    X86_VPHADDDYrm	= 6848,
    X86_VPHADDDYrr	= 6849,
    X86_VPHADDDrm	= 6850,
    X86_VPHADDDrr	= 6851,
    X86_VPHADDSWrm128	= 6852,
    X86_VPHADDSWrm256	= 6853,
    X86_VPHADDSWrr128	= 6854,
    X86_VPHADDSWrr256	= 6855,
    X86_VPHADDUBDrm	= 6856,
    X86_VPHADDUBDrr	= 6857,
    X86_VPHADDUBQrm	= 6858,
    X86_VPHADDUBQrr	= 6859,
    X86_VPHADDUBWrm	= 6860,
    X86_VPHADDUBWrr	= 6861,
    X86_VPHADDUDQrm	= 6862,
    X86_VPHADDUDQrr	= 6863,
    X86_VPHADDUWDrm	= 6864,
    X86_VPHADDUWDrr	= 6865,
    X86_VPHADDUWQrm	= 6866,
    X86_VPHADDUWQrr	= 6867,
    X86_VPHADDWDrm	= 6868,
    X86_VPHADDWDrr	= 6869,
    X86_VPHADDWQrm	= 6870,
    X86_VPHADDWQrr	= 6871,
    X86_VPHADDWYrm	= 6872,
    X86_VPHADDWYrr	= 6873,
    X86_VPHADDWrm	= 6874,
    X86_VPHADDWrr	= 6875,
    X86_VPHMINPOSUWrm128	= 6876,
    X86_VPHMINPOSUWrr128	= 6877,
    X86_VPHSUBBWrm	= 6878,
    X86_VPHSUBBWrr	= 6879,
    X86_VPHSUBDQrm	= 6880,
    X86_VPHSUBDQrr	= 6881,
    X86_VPHSUBDYrm	= 6882,
    X86_VPHSUBDYrr	= 6883,
    X86_VPHSUBDrm	= 6884,
    X86_VPHSUBDrr	= 6885,
    X86_VPHSUBSWrm128	= 6886,
    X86_VPHSUBSWrm256	= 6887,
    X86_VPHSUBSWrr128	= 6888,
    X86_VPHSUBSWrr256	= 6889,
    X86_VPHSUBWDrm	= 6890,
    X86_VPHSUBWDrr	= 6891,
    X86_VPHSUBWYrm	= 6892,
    X86_VPHSUBWYrr	= 6893,
    X86_VPHSUBWrm	= 6894,
    X86_VPHSUBWrr	= 6895,
    X86_VPINSRBrm	= 6896,
    X86_VPINSRBrr	= 6897,
    X86_VPINSRDrm	= 6898,
    X86_VPINSRDrr	= 6899,
    X86_VPINSRQrm	= 6900,
    X86_VPINSRQrr	= 6901,
    X86_VPINSRWrmi	= 6902,
    X86_VPINSRWrri	= 6903,
    X86_VPLZCNTDrm	= 6904,
    X86_VPLZCNTDrmb	= 6905,
    X86_VPLZCNTDrmbk	= 6906,
    X86_VPLZCNTDrmbkz	= 6907,
    X86_VPLZCNTDrmk	= 6908,
    X86_VPLZCNTDrmkz	= 6909,
    X86_VPLZCNTDrr	= 6910,
    X86_VPLZCNTDrrk	= 6911,
    X86_VPLZCNTDrrkz	= 6912,
    X86_VPLZCNTQrm	= 6913,
    X86_VPLZCNTQrmb	= 6914,
    X86_VPLZCNTQrmbk	= 6915,
    X86_VPLZCNTQrmbkz	= 6916,
    X86_VPLZCNTQrmk	= 6917,
    X86_VPLZCNTQrmkz	= 6918,
    X86_VPLZCNTQrr	= 6919,
    X86_VPLZCNTQrrk	= 6920,
    X86_VPLZCNTQrrkz	= 6921,
    X86_VPMACSDDrm	= 6922,
    X86_VPMACSDDrr	= 6923,
    X86_VPMACSDQHrm	= 6924,
    X86_VPMACSDQHrr	= 6925,
    X86_VPMACSDQLrm	= 6926,
    X86_VPMACSDQLrr	= 6927,
    X86_VPMACSSDDrm	= 6928,
    X86_VPMACSSDDrr	= 6929,
    X86_VPMACSSDQHrm	= 6930,
    X86_VPMACSSDQHrr	= 6931,
    X86_VPMACSSDQLrm	= 6932,
    X86_VPMACSSDQLrr	= 6933,
    X86_VPMACSSWDrm	= 6934,
    X86_VPMACSSWDrr	= 6935,
    X86_VPMACSSWWrm	= 6936,
    X86_VPMACSSWWrr	= 6937,
    X86_VPMACSWDrm	= 6938,
    X86_VPMACSWDrr	= 6939,
    X86_VPMACSWWrm	= 6940,
    X86_VPMACSWWrr	= 6941,
    X86_VPMADCSSWDrm	= 6942,
    X86_VPMADCSSWDrr	= 6943,
    X86_VPMADCSWDrm	= 6944,
    X86_VPMADCSWDrr	= 6945,
    X86_VPMADDUBSWrm128	= 6946,
    X86_VPMADDUBSWrm256	= 6947,
    X86_VPMADDUBSWrr128	= 6948,
    X86_VPMADDUBSWrr256	= 6949,
    X86_VPMADDWDYrm	= 6950,
    X86_VPMADDWDYrr	= 6951,
    X86_VPMADDWDrm	= 6952,
    X86_VPMADDWDrr	= 6953,
    X86_VPMASKMOVDYmr	= 6954,
    X86_VPMASKMOVDYrm	= 6955,
    X86_VPMASKMOVDmr	= 6956,
    X86_VPMASKMOVDrm	= 6957,
    X86_VPMASKMOVQYmr	= 6958,
    X86_VPMASKMOVQYrm	= 6959,
    X86_VPMASKMOVQmr	= 6960,
    X86_VPMASKMOVQrm	= 6961,
    X86_VPMAXSBYrm	= 6962,
    X86_VPMAXSBYrr	= 6963,
    X86_VPMAXSBZ128rm	= 6964,
    X86_VPMAXSBZ128rmk	= 6965,
    X86_VPMAXSBZ128rmkz	= 6966,
    X86_VPMAXSBZ128rr	= 6967,
    X86_VPMAXSBZ128rrk	= 6968,
    X86_VPMAXSBZ128rrkz	= 6969,
    X86_VPMAXSBZ256rm	= 6970,
    X86_VPMAXSBZ256rmk	= 6971,
    X86_VPMAXSBZ256rmkz	= 6972,
    X86_VPMAXSBZ256rr	= 6973,
    X86_VPMAXSBZ256rrk	= 6974,
    X86_VPMAXSBZ256rrkz	= 6975,
    X86_VPMAXSBZrm	= 6976,
    X86_VPMAXSBZrmk	= 6977,
    X86_VPMAXSBZrmkz	= 6978,
    X86_VPMAXSBZrr	= 6979,
    X86_VPMAXSBZrrk	= 6980,
    X86_VPMAXSBZrrkz	= 6981,
    X86_VPMAXSBrm	= 6982,
    X86_VPMAXSBrr	= 6983,
    X86_VPMAXSDYrm	= 6984,
    X86_VPMAXSDYrr	= 6985,
    X86_VPMAXSDZ128rm	= 6986,
    X86_VPMAXSDZ128rmb	= 6987,
    X86_VPMAXSDZ128rmbk	= 6988,
    X86_VPMAXSDZ128rmbkz	= 6989,
    X86_VPMAXSDZ128rmk	= 6990,
    X86_VPMAXSDZ128rmkz	= 6991,
    X86_VPMAXSDZ128rr	= 6992,
    X86_VPMAXSDZ128rrk	= 6993,
    X86_VPMAXSDZ128rrkz	= 6994,
    X86_VPMAXSDZ256rm	= 6995,
    X86_VPMAXSDZ256rmb	= 6996,
    X86_VPMAXSDZ256rmbk	= 6997,
    X86_VPMAXSDZ256rmbkz	= 6998,
    X86_VPMAXSDZ256rmk	= 6999,
    X86_VPMAXSDZ256rmkz	= 7000,
    X86_VPMAXSDZ256rr	= 7001,
    X86_VPMAXSDZ256rrk	= 7002,
    X86_VPMAXSDZ256rrkz	= 7003,
    X86_VPMAXSDZrm	= 7004,
    X86_VPMAXSDZrmb	= 7005,
    X86_VPMAXSDZrmbk	= 7006,
    X86_VPMAXSDZrmbkz	= 7007,
    X86_VPMAXSDZrmk	= 7008,
    X86_VPMAXSDZrmkz	= 7009,
    X86_VPMAXSDZrr	= 7010,
    X86_VPMAXSDZrrk	= 7011,
    X86_VPMAXSDZrrkz	= 7012,
    X86_VPMAXSDrm	= 7013,
    X86_VPMAXSDrr	= 7014,
    X86_VPMAXSQZ128rm	= 7015,
    X86_VPMAXSQZ128rmb	= 7016,
    X86_VPMAXSQZ128rmbk	= 7017,
    X86_VPMAXSQZ128rmbkz	= 7018,
    X86_VPMAXSQZ128rmk	= 7019,
    X86_VPMAXSQZ128rmkz	= 7020,
    X86_VPMAXSQZ128rr	= 7021,
    X86_VPMAXSQZ128rrk	= 7022,
    X86_VPMAXSQZ128rrkz	= 7023,
    X86_VPMAXSQZ256rm	= 7024,
    X86_VPMAXSQZ256rmb	= 7025,
    X86_VPMAXSQZ256rmbk	= 7026,
    X86_VPMAXSQZ256rmbkz	= 7027,
    X86_VPMAXSQZ256rmk	= 7028,
    X86_VPMAXSQZ256rmkz	= 7029,
    X86_VPMAXSQZ256rr	= 7030,
    X86_VPMAXSQZ256rrk	= 7031,
    X86_VPMAXSQZ256rrkz	= 7032,
    X86_VPMAXSQZrm	= 7033,
    X86_VPMAXSQZrmb	= 7034,
    X86_VPMAXSQZrmbk	= 7035,
    X86_VPMAXSQZrmbkz	= 7036,
    X86_VPMAXSQZrmk	= 7037,
    X86_VPMAXSQZrmkz	= 7038,
    X86_VPMAXSQZrr	= 7039,
    X86_VPMAXSQZrrk	= 7040,
    X86_VPMAXSQZrrkz	= 7041,
    X86_VPMAXSWYrm	= 7042,
    X86_VPMAXSWYrr	= 7043,
    X86_VPMAXSWZ128rm	= 7044,
    X86_VPMAXSWZ128rmk	= 7045,
    X86_VPMAXSWZ128rmkz	= 7046,
    X86_VPMAXSWZ128rr	= 7047,
    X86_VPMAXSWZ128rrk	= 7048,
    X86_VPMAXSWZ128rrkz	= 7049,
    X86_VPMAXSWZ256rm	= 7050,
    X86_VPMAXSWZ256rmk	= 7051,
    X86_VPMAXSWZ256rmkz	= 7052,
    X86_VPMAXSWZ256rr	= 7053,
    X86_VPMAXSWZ256rrk	= 7054,
    X86_VPMAXSWZ256rrkz	= 7055,
    X86_VPMAXSWZrm	= 7056,
    X86_VPMAXSWZrmk	= 7057,
    X86_VPMAXSWZrmkz	= 7058,
    X86_VPMAXSWZrr	= 7059,
    X86_VPMAXSWZrrk	= 7060,
    X86_VPMAXSWZrrkz	= 7061,
    X86_VPMAXSWrm	= 7062,
    X86_VPMAXSWrr	= 7063,
    X86_VPMAXUBYrm	= 7064,
    X86_VPMAXUBYrr	= 7065,
    X86_VPMAXUBZ128rm	= 7066,
    X86_VPMAXUBZ128rmk	= 7067,
    X86_VPMAXUBZ128rmkz	= 7068,
    X86_VPMAXUBZ128rr	= 7069,
    X86_VPMAXUBZ128rrk	= 7070,
    X86_VPMAXUBZ128rrkz	= 7071,
    X86_VPMAXUBZ256rm	= 7072,
    X86_VPMAXUBZ256rmk	= 7073,
    X86_VPMAXUBZ256rmkz	= 7074,
    X86_VPMAXUBZ256rr	= 7075,
    X86_VPMAXUBZ256rrk	= 7076,
    X86_VPMAXUBZ256rrkz	= 7077,
    X86_VPMAXUBZrm	= 7078,
    X86_VPMAXUBZrmk	= 7079,
    X86_VPMAXUBZrmkz	= 7080,
    X86_VPMAXUBZrr	= 7081,
    X86_VPMAXUBZrrk	= 7082,
    X86_VPMAXUBZrrkz	= 7083,
    X86_VPMAXUBrm	= 7084,
    X86_VPMAXUBrr	= 7085,
    X86_VPMAXUDYrm	= 7086,
    X86_VPMAXUDYrr	= 7087,
    X86_VPMAXUDZ128rm	= 7088,
    X86_VPMAXUDZ128rmb	= 7089,
    X86_VPMAXUDZ128rmbk	= 7090,
    X86_VPMAXUDZ128rmbkz	= 7091,
    X86_VPMAXUDZ128rmk	= 7092,
    X86_VPMAXUDZ128rmkz	= 7093,
    X86_VPMAXUDZ128rr	= 7094,
    X86_VPMAXUDZ128rrk	= 7095,
    X86_VPMAXUDZ128rrkz	= 7096,
    X86_VPMAXUDZ256rm	= 7097,
    X86_VPMAXUDZ256rmb	= 7098,
    X86_VPMAXUDZ256rmbk	= 7099,
    X86_VPMAXUDZ256rmbkz	= 7100,
    X86_VPMAXUDZ256rmk	= 7101,
    X86_VPMAXUDZ256rmkz	= 7102,
    X86_VPMAXUDZ256rr	= 7103,
    X86_VPMAXUDZ256rrk	= 7104,
    X86_VPMAXUDZ256rrkz	= 7105,
    X86_VPMAXUDZrm	= 7106,
    X86_VPMAXUDZrmb	= 7107,
    X86_VPMAXUDZrmbk	= 7108,
    X86_VPMAXUDZrmbkz	= 7109,
    X86_VPMAXUDZrmk	= 7110,
    X86_VPMAXUDZrmkz	= 7111,
    X86_VPMAXUDZrr	= 7112,
    X86_VPMAXUDZrrk	= 7113,
    X86_VPMAXUDZrrkz	= 7114,
    X86_VPMAXUDrm	= 7115,
    X86_VPMAXUDrr	= 7116,
    X86_VPMAXUQZ128rm	= 7117,
    X86_VPMAXUQZ128rmb	= 7118,
    X86_VPMAXUQZ128rmbk	= 7119,
    X86_VPMAXUQZ128rmbkz	= 7120,
    X86_VPMAXUQZ128rmk	= 7121,
    X86_VPMAXUQZ128rmkz	= 7122,
    X86_VPMAXUQZ128rr	= 7123,
    X86_VPMAXUQZ128rrk	= 7124,
    X86_VPMAXUQZ128rrkz	= 7125,
    X86_VPMAXUQZ256rm	= 7126,
    X86_VPMAXUQZ256rmb	= 7127,
    X86_VPMAXUQZ256rmbk	= 7128,
    X86_VPMAXUQZ256rmbkz	= 7129,
    X86_VPMAXUQZ256rmk	= 7130,
    X86_VPMAXUQZ256rmkz	= 7131,
    X86_VPMAXUQZ256rr	= 7132,
    X86_VPMAXUQZ256rrk	= 7133,
    X86_VPMAXUQZ256rrkz	= 7134,
    X86_VPMAXUQZrm	= 7135,
    X86_VPMAXUQZrmb	= 7136,
    X86_VPMAXUQZrmbk	= 7137,
    X86_VPMAXUQZrmbkz	= 7138,
    X86_VPMAXUQZrmk	= 7139,
    X86_VPMAXUQZrmkz	= 7140,
    X86_VPMAXUQZrr	= 7141,
    X86_VPMAXUQZrrk	= 7142,
    X86_VPMAXUQZrrkz	= 7143,
    X86_VPMAXUWYrm	= 7144,
    X86_VPMAXUWYrr	= 7145,
    X86_VPMAXUWZ128rm	= 7146,
    X86_VPMAXUWZ128rmk	= 7147,
    X86_VPMAXUWZ128rmkz	= 7148,
    X86_VPMAXUWZ128rr	= 7149,
    X86_VPMAXUWZ128rrk	= 7150,
    X86_VPMAXUWZ128rrkz	= 7151,
    X86_VPMAXUWZ256rm	= 7152,
    X86_VPMAXUWZ256rmk	= 7153,
    X86_VPMAXUWZ256rmkz	= 7154,
    X86_VPMAXUWZ256rr	= 7155,
    X86_VPMAXUWZ256rrk	= 7156,
    X86_VPMAXUWZ256rrkz	= 7157,
    X86_VPMAXUWZrm	= 7158,
    X86_VPMAXUWZrmk	= 7159,
    X86_VPMAXUWZrmkz	= 7160,
    X86_VPMAXUWZrr	= 7161,
    X86_VPMAXUWZrrk	= 7162,
    X86_VPMAXUWZrrkz	= 7163,
    X86_VPMAXUWrm	= 7164,
    X86_VPMAXUWrr	= 7165,
    X86_VPMINSBYrm	= 7166,
    X86_VPMINSBYrr	= 7167,
    X86_VPMINSBZ128rm	= 7168,
    X86_VPMINSBZ128rmk	= 7169,
    X86_VPMINSBZ128rmkz	= 7170,
    X86_VPMINSBZ128rr	= 7171,
    X86_VPMINSBZ128rrk	= 7172,
    X86_VPMINSBZ128rrkz	= 7173,
    X86_VPMINSBZ256rm	= 7174,
    X86_VPMINSBZ256rmk	= 7175,
    X86_VPMINSBZ256rmkz	= 7176,
    X86_VPMINSBZ256rr	= 7177,
    X86_VPMINSBZ256rrk	= 7178,
    X86_VPMINSBZ256rrkz	= 7179,
    X86_VPMINSBZrm	= 7180,
    X86_VPMINSBZrmk	= 7181,
    X86_VPMINSBZrmkz	= 7182,
    X86_VPMINSBZrr	= 7183,
    X86_VPMINSBZrrk	= 7184,
    X86_VPMINSBZrrkz	= 7185,
    X86_VPMINSBrm	= 7186,
    X86_VPMINSBrr	= 7187,
    X86_VPMINSDYrm	= 7188,
    X86_VPMINSDYrr	= 7189,
    X86_VPMINSDZ128rm	= 7190,
    X86_VPMINSDZ128rmb	= 7191,
    X86_VPMINSDZ128rmbk	= 7192,
    X86_VPMINSDZ128rmbkz	= 7193,
    X86_VPMINSDZ128rmk	= 7194,
    X86_VPMINSDZ128rmkz	= 7195,
    X86_VPMINSDZ128rr	= 7196,
    X86_VPMINSDZ128rrk	= 7197,
    X86_VPMINSDZ128rrkz	= 7198,
    X86_VPMINSDZ256rm	= 7199,
    X86_VPMINSDZ256rmb	= 7200,
    X86_VPMINSDZ256rmbk	= 7201,
    X86_VPMINSDZ256rmbkz	= 7202,
    X86_VPMINSDZ256rmk	= 7203,
    X86_VPMINSDZ256rmkz	= 7204,
    X86_VPMINSDZ256rr	= 7205,
    X86_VPMINSDZ256rrk	= 7206,
    X86_VPMINSDZ256rrkz	= 7207,
    X86_VPMINSDZrm	= 7208,
    X86_VPMINSDZrmb	= 7209,
    X86_VPMINSDZrmbk	= 7210,
    X86_VPMINSDZrmbkz	= 7211,
    X86_VPMINSDZrmk	= 7212,
    X86_VPMINSDZrmkz	= 7213,
    X86_VPMINSDZrr	= 7214,
    X86_VPMINSDZrrk	= 7215,
    X86_VPMINSDZrrkz	= 7216,
    X86_VPMINSDrm	= 7217,
    X86_VPMINSDrr	= 7218,
    X86_VPMINSQZ128rm	= 7219,
    X86_VPMINSQZ128rmb	= 7220,
    X86_VPMINSQZ128rmbk	= 7221,
    X86_VPMINSQZ128rmbkz	= 7222,
    X86_VPMINSQZ128rmk	= 7223,
    X86_VPMINSQZ128rmkz	= 7224,
    X86_VPMINSQZ128rr	= 7225,
    X86_VPMINSQZ128rrk	= 7226,
    X86_VPMINSQZ128rrkz	= 7227,
    X86_VPMINSQZ256rm	= 7228,
    X86_VPMINSQZ256rmb	= 7229,
    X86_VPMINSQZ256rmbk	= 7230,
    X86_VPMINSQZ256rmbkz	= 7231,
    X86_VPMINSQZ256rmk	= 7232,
    X86_VPMINSQZ256rmkz	= 7233,
    X86_VPMINSQZ256rr	= 7234,
    X86_VPMINSQZ256rrk	= 7235,
    X86_VPMINSQZ256rrkz	= 7236,
    X86_VPMINSQZrm	= 7237,
    X86_VPMINSQZrmb	= 7238,
    X86_VPMINSQZrmbk	= 7239,
    X86_VPMINSQZrmbkz	= 7240,
    X86_VPMINSQZrmk	= 7241,
    X86_VPMINSQZrmkz	= 7242,
    X86_VPMINSQZrr	= 7243,
    X86_VPMINSQZrrk	= 7244,
    X86_VPMINSQZrrkz	= 7245,
    X86_VPMINSWYrm	= 7246,
    X86_VPMINSWYrr	= 7247,
    X86_VPMINSWZ128rm	= 7248,
    X86_VPMINSWZ128rmk	= 7249,
    X86_VPMINSWZ128rmkz	= 7250,
    X86_VPMINSWZ128rr	= 7251,
    X86_VPMINSWZ128rrk	= 7252,
    X86_VPMINSWZ128rrkz	= 7253,
    X86_VPMINSWZ256rm	= 7254,
    X86_VPMINSWZ256rmk	= 7255,
    X86_VPMINSWZ256rmkz	= 7256,
    X86_VPMINSWZ256rr	= 7257,
    X86_VPMINSWZ256rrk	= 7258,
    X86_VPMINSWZ256rrkz	= 7259,
    X86_VPMINSWZrm	= 7260,
    X86_VPMINSWZrmk	= 7261,
    X86_VPMINSWZrmkz	= 7262,
    X86_VPMINSWZrr	= 7263,
    X86_VPMINSWZrrk	= 7264,
    X86_VPMINSWZrrkz	= 7265,
    X86_VPMINSWrm	= 7266,
    X86_VPMINSWrr	= 7267,
    X86_VPMINUBYrm	= 7268,
    X86_VPMINUBYrr	= 7269,
    X86_VPMINUBZ128rm	= 7270,
    X86_VPMINUBZ128rmk	= 7271,
    X86_VPMINUBZ128rmkz	= 7272,
    X86_VPMINUBZ128rr	= 7273,
    X86_VPMINUBZ128rrk	= 7274,
    X86_VPMINUBZ128rrkz	= 7275,
    X86_VPMINUBZ256rm	= 7276,
    X86_VPMINUBZ256rmk	= 7277,
    X86_VPMINUBZ256rmkz	= 7278,
    X86_VPMINUBZ256rr	= 7279,
    X86_VPMINUBZ256rrk	= 7280,
    X86_VPMINUBZ256rrkz	= 7281,
    X86_VPMINUBZrm	= 7282,
    X86_VPMINUBZrmk	= 7283,
    X86_VPMINUBZrmkz	= 7284,
    X86_VPMINUBZrr	= 7285,
    X86_VPMINUBZrrk	= 7286,
    X86_VPMINUBZrrkz	= 7287,
    X86_VPMINUBrm	= 7288,
    X86_VPMINUBrr	= 7289,
    X86_VPMINUDYrm	= 7290,
    X86_VPMINUDYrr	= 7291,
    X86_VPMINUDZ128rm	= 7292,
    X86_VPMINUDZ128rmb	= 7293,
    X86_VPMINUDZ128rmbk	= 7294,
    X86_VPMINUDZ128rmbkz	= 7295,
    X86_VPMINUDZ128rmk	= 7296,
    X86_VPMINUDZ128rmkz	= 7297,
    X86_VPMINUDZ128rr	= 7298,
    X86_VPMINUDZ128rrk	= 7299,
    X86_VPMINUDZ128rrkz	= 7300,
    X86_VPMINUDZ256rm	= 7301,
    X86_VPMINUDZ256rmb	= 7302,
    X86_VPMINUDZ256rmbk	= 7303,
    X86_VPMINUDZ256rmbkz	= 7304,
    X86_VPMINUDZ256rmk	= 7305,
    X86_VPMINUDZ256rmkz	= 7306,
    X86_VPMINUDZ256rr	= 7307,
    X86_VPMINUDZ256rrk	= 7308,
    X86_VPMINUDZ256rrkz	= 7309,
    X86_VPMINUDZrm	= 7310,
    X86_VPMINUDZrmb	= 7311,
    X86_VPMINUDZrmbk	= 7312,
    X86_VPMINUDZrmbkz	= 7313,
    X86_VPMINUDZrmk	= 7314,
    X86_VPMINUDZrmkz	= 7315,
    X86_VPMINUDZrr	= 7316,
    X86_VPMINUDZrrk	= 7317,
    X86_VPMINUDZrrkz	= 7318,
    X86_VPMINUDrm	= 7319,
    X86_VPMINUDrr	= 7320,
    X86_VPMINUQZ128rm	= 7321,
    X86_VPMINUQZ128rmb	= 7322,
    X86_VPMINUQZ128rmbk	= 7323,
    X86_VPMINUQZ128rmbkz	= 7324,
    X86_VPMINUQZ128rmk	= 7325,
    X86_VPMINUQZ128rmkz	= 7326,
    X86_VPMINUQZ128rr	= 7327,
    X86_VPMINUQZ128rrk	= 7328,
    X86_VPMINUQZ128rrkz	= 7329,
    X86_VPMINUQZ256rm	= 7330,
    X86_VPMINUQZ256rmb	= 7331,
    X86_VPMINUQZ256rmbk	= 7332,
    X86_VPMINUQZ256rmbkz	= 7333,
    X86_VPMINUQZ256rmk	= 7334,
    X86_VPMINUQZ256rmkz	= 7335,
    X86_VPMINUQZ256rr	= 7336,
    X86_VPMINUQZ256rrk	= 7337,
    X86_VPMINUQZ256rrkz	= 7338,
    X86_VPMINUQZrm	= 7339,
    X86_VPMINUQZrmb	= 7340,
    X86_VPMINUQZrmbk	= 7341,
    X86_VPMINUQZrmbkz	= 7342,
    X86_VPMINUQZrmk	= 7343,
    X86_VPMINUQZrmkz	= 7344,
    X86_VPMINUQZrr	= 7345,
    X86_VPMINUQZrrk	= 7346,
    X86_VPMINUQZrrkz	= 7347,
    X86_VPMINUWYrm	= 7348,
    X86_VPMINUWYrr	= 7349,
    X86_VPMINUWZ128rm	= 7350,
    X86_VPMINUWZ128rmk	= 7351,
    X86_VPMINUWZ128rmkz	= 7352,
    X86_VPMINUWZ128rr	= 7353,
    X86_VPMINUWZ128rrk	= 7354,
    X86_VPMINUWZ128rrkz	= 7355,
    X86_VPMINUWZ256rm	= 7356,
    X86_VPMINUWZ256rmk	= 7357,
    X86_VPMINUWZ256rmkz	= 7358,
    X86_VPMINUWZ256rr	= 7359,
    X86_VPMINUWZ256rrk	= 7360,
    X86_VPMINUWZ256rrkz	= 7361,
    X86_VPMINUWZrm	= 7362,
    X86_VPMINUWZrmk	= 7363,
    X86_VPMINUWZrmkz	= 7364,
    X86_VPMINUWZrr	= 7365,
    X86_VPMINUWZrrk	= 7366,
    X86_VPMINUWZrrkz	= 7367,
    X86_VPMINUWrm	= 7368,
    X86_VPMINUWrr	= 7369,
    X86_VPMOVDBmr	= 7370,
    X86_VPMOVDBmrk	= 7371,
    X86_VPMOVDBrr	= 7372,
    X86_VPMOVDBrrk	= 7373,
    X86_VPMOVDBrrkz	= 7374,
    X86_VPMOVDWmr	= 7375,
    X86_VPMOVDWmrk	= 7376,
    X86_VPMOVDWrr	= 7377,
    X86_VPMOVDWrrk	= 7378,
    X86_VPMOVDWrrkz	= 7379,
    X86_VPMOVM2BZ128rr	= 7380,
    X86_VPMOVM2BZ256rr	= 7381,
    X86_VPMOVM2BZrr	= 7382,
    X86_VPMOVM2DZ128rr	= 7383,
    X86_VPMOVM2DZ256rr	= 7384,
    X86_VPMOVM2DZrr	= 7385,
    X86_VPMOVM2QZ128rr	= 7386,
    X86_VPMOVM2QZ256rr	= 7387,
    X86_VPMOVM2QZrr	= 7388,
    X86_VPMOVM2WZ128rr	= 7389,
    X86_VPMOVM2WZ256rr	= 7390,
    X86_VPMOVM2WZrr	= 7391,
    X86_VPMOVMSKBYrr	= 7392,
    X86_VPMOVMSKBrr	= 7393,
    X86_VPMOVQBmr	= 7394,
    X86_VPMOVQBmrk	= 7395,
    X86_VPMOVQBrr	= 7396,
    X86_VPMOVQBrrk	= 7397,
    X86_VPMOVQBrrkz	= 7398,
    X86_VPMOVQDmr	= 7399,
    X86_VPMOVQDmrk	= 7400,
    X86_VPMOVQDrr	= 7401,
    X86_VPMOVQDrrk	= 7402,
    X86_VPMOVQDrrkz	= 7403,
    X86_VPMOVQWmr	= 7404,
    X86_VPMOVQWmrk	= 7405,
    X86_VPMOVQWrr	= 7406,
    X86_VPMOVQWrrk	= 7407,
    X86_VPMOVQWrrkz	= 7408,
    X86_VPMOVSDBmr	= 7409,
    X86_VPMOVSDBmrk	= 7410,
    X86_VPMOVSDBrr	= 7411,
    X86_VPMOVSDBrrk	= 7412,
    X86_VPMOVSDBrrkz	= 7413,
    X86_VPMOVSDWmr	= 7414,
    X86_VPMOVSDWmrk	= 7415,
    X86_VPMOVSDWrr	= 7416,
    X86_VPMOVSDWrrk	= 7417,
    X86_VPMOVSDWrrkz	= 7418,
    X86_VPMOVSQBmr	= 7419,
    X86_VPMOVSQBmrk	= 7420,
    X86_VPMOVSQBrr	= 7421,
    X86_VPMOVSQBrrk	= 7422,
    X86_VPMOVSQBrrkz	= 7423,
    X86_VPMOVSQDmr	= 7424,
    X86_VPMOVSQDmrk	= 7425,
    X86_VPMOVSQDrr	= 7426,
    X86_VPMOVSQDrrk	= 7427,
    X86_VPMOVSQDrrkz	= 7428,
    X86_VPMOVSQWmr	= 7429,
    X86_VPMOVSQWmrk	= 7430,
    X86_VPMOVSQWrr	= 7431,
    X86_VPMOVSQWrrk	= 7432,
    X86_VPMOVSQWrrkz	= 7433,
    X86_VPMOVSXBDYrm	= 7434,
    X86_VPMOVSXBDYrr	= 7435,
    X86_VPMOVSXBDZrm	= 7436,
    X86_VPMOVSXBDZrmk	= 7437,
    X86_VPMOVSXBDZrmkz	= 7438,
    X86_VPMOVSXBDZrr	= 7439,
    X86_VPMOVSXBDZrrk	= 7440,
    X86_VPMOVSXBDZrrkz	= 7441,
    X86_VPMOVSXBDrm	= 7442,
    X86_VPMOVSXBDrr	= 7443,
    X86_VPMOVSXBQYrm	= 7444,
    X86_VPMOVSXBQYrr	= 7445,
    X86_VPMOVSXBQZrm	= 7446,
    X86_VPMOVSXBQZrmk	= 7447,
    X86_VPMOVSXBQZrmkz	= 7448,
    X86_VPMOVSXBQZrr	= 7449,
    X86_VPMOVSXBQZrrk	= 7450,
    X86_VPMOVSXBQZrrkz	= 7451,
    X86_VPMOVSXBQrm	= 7452,
    X86_VPMOVSXBQrr	= 7453,
    X86_VPMOVSXBWYrm	= 7454,
    X86_VPMOVSXBWYrr	= 7455,
    X86_VPMOVSXBWrm	= 7456,
    X86_VPMOVSXBWrr	= 7457,
    X86_VPMOVSXDQYrm	= 7458,
    X86_VPMOVSXDQYrr	= 7459,
    X86_VPMOVSXDQZrm	= 7460,
    X86_VPMOVSXDQZrmk	= 7461,
    X86_VPMOVSXDQZrmkz	= 7462,
    X86_VPMOVSXDQZrr	= 7463,
    X86_VPMOVSXDQZrrk	= 7464,
    X86_VPMOVSXDQZrrkz	= 7465,
    X86_VPMOVSXDQrm	= 7466,
    X86_VPMOVSXDQrr	= 7467,
    X86_VPMOVSXWDYrm	= 7468,
    X86_VPMOVSXWDYrr	= 7469,
    X86_VPMOVSXWDZrm	= 7470,
    X86_VPMOVSXWDZrmk	= 7471,
    X86_VPMOVSXWDZrmkz	= 7472,
    X86_VPMOVSXWDZrr	= 7473,
    X86_VPMOVSXWDZrrk	= 7474,
    X86_VPMOVSXWDZrrkz	= 7475,
    X86_VPMOVSXWDrm	= 7476,
    X86_VPMOVSXWDrr	= 7477,
    X86_VPMOVSXWQYrm	= 7478,
    X86_VPMOVSXWQYrr	= 7479,
    X86_VPMOVSXWQZrm	= 7480,
    X86_VPMOVSXWQZrmk	= 7481,
    X86_VPMOVSXWQZrmkz	= 7482,
    X86_VPMOVSXWQZrr	= 7483,
    X86_VPMOVSXWQZrrk	= 7484,
    X86_VPMOVSXWQZrrkz	= 7485,
    X86_VPMOVSXWQrm	= 7486,
    X86_VPMOVSXWQrr	= 7487,
    X86_VPMOVUSDBmr	= 7488,
    X86_VPMOVUSDBmrk	= 7489,
    X86_VPMOVUSDBrr	= 7490,
    X86_VPMOVUSDBrrk	= 7491,
    X86_VPMOVUSDBrrkz	= 7492,
    X86_VPMOVUSDWmr	= 7493,
    X86_VPMOVUSDWmrk	= 7494,
    X86_VPMOVUSDWrr	= 7495,
    X86_VPMOVUSDWrrk	= 7496,
    X86_VPMOVUSDWrrkz	= 7497,
    X86_VPMOVUSQBmr	= 7498,
    X86_VPMOVUSQBmrk	= 7499,
    X86_VPMOVUSQBrr	= 7500,
    X86_VPMOVUSQBrrk	= 7501,
    X86_VPMOVUSQBrrkz	= 7502,
    X86_VPMOVUSQDmr	= 7503,
    X86_VPMOVUSQDmrk	= 7504,
    X86_VPMOVUSQDrr	= 7505,
    X86_VPMOVUSQDrrk	= 7506,
    X86_VPMOVUSQDrrkz	= 7507,
    X86_VPMOVUSQWmr	= 7508,
    X86_VPMOVUSQWmrk	= 7509,
    X86_VPMOVUSQWrr	= 7510,
    X86_VPMOVUSQWrrk	= 7511,
    X86_VPMOVUSQWrrkz	= 7512,
    X86_VPMOVZXBDYrm	= 7513,
    X86_VPMOVZXBDYrr	= 7514,
    X86_VPMOVZXBDZrm	= 7515,
    X86_VPMOVZXBDZrmk	= 7516,
    X86_VPMOVZXBDZrmkz	= 7517,
    X86_VPMOVZXBDZrr	= 7518,
    X86_VPMOVZXBDZrrk	= 7519,
    X86_VPMOVZXBDZrrkz	= 7520,
    X86_VPMOVZXBDrm	= 7521,
    X86_VPMOVZXBDrr	= 7522,
    X86_VPMOVZXBQYrm	= 7523,
    X86_VPMOVZXBQYrr	= 7524,
    X86_VPMOVZXBQZrm	= 7525,
    X86_VPMOVZXBQZrmk	= 7526,
    X86_VPMOVZXBQZrmkz	= 7527,
    X86_VPMOVZXBQZrr	= 7528,
    X86_VPMOVZXBQZrrk	= 7529,
    X86_VPMOVZXBQZrrkz	= 7530,
    X86_VPMOVZXBQrm	= 7531,
    X86_VPMOVZXBQrr	= 7532,
    X86_VPMOVZXBWYrm	= 7533,
    X86_VPMOVZXBWYrr	= 7534,
    X86_VPMOVZXBWrm	= 7535,
    X86_VPMOVZXBWrr	= 7536,
    X86_VPMOVZXDQYrm	= 7537,
    X86_VPMOVZXDQYrr	= 7538,
    X86_VPMOVZXDQZrm	= 7539,
    X86_VPMOVZXDQZrmk	= 7540,
    X86_VPMOVZXDQZrmkz	= 7541,
    X86_VPMOVZXDQZrr	= 7542,
    X86_VPMOVZXDQZrrk	= 7543,
    X86_VPMOVZXDQZrrkz	= 7544,
    X86_VPMOVZXDQrm	= 7545,
    X86_VPMOVZXDQrr	= 7546,
    X86_VPMOVZXWDYrm	= 7547,
    X86_VPMOVZXWDYrr	= 7548,
    X86_VPMOVZXWDZrm	= 7549,
    X86_VPMOVZXWDZrmk	= 7550,
    X86_VPMOVZXWDZrmkz	= 7551,
    X86_VPMOVZXWDZrr	= 7552,
    X86_VPMOVZXWDZrrk	= 7553,
    X86_VPMOVZXWDZrrkz	= 7554,
    X86_VPMOVZXWDrm	= 7555,
    X86_VPMOVZXWDrr	= 7556,
    X86_VPMOVZXWQYrm	= 7557,
    X86_VPMOVZXWQYrr	= 7558,
    X86_VPMOVZXWQZrm	= 7559,
    X86_VPMOVZXWQZrmk	= 7560,
    X86_VPMOVZXWQZrmkz	= 7561,
    X86_VPMOVZXWQZrr	= 7562,
    X86_VPMOVZXWQZrrk	= 7563,
    X86_VPMOVZXWQZrrkz	= 7564,
    X86_VPMOVZXWQrm	= 7565,
    X86_VPMOVZXWQrr	= 7566,
    X86_VPMULDQYrm	= 7567,
    X86_VPMULDQYrr	= 7568,
    X86_VPMULDQZrm	= 7569,
    X86_VPMULDQZrmb	= 7570,
    X86_VPMULDQZrmbk	= 7571,
    X86_VPMULDQZrmbkz	= 7572,
    X86_VPMULDQZrmk	= 7573,
    X86_VPMULDQZrmkz	= 7574,
    X86_VPMULDQZrr	= 7575,
    X86_VPMULDQZrrk	= 7576,
    X86_VPMULDQZrrkz	= 7577,
    X86_VPMULDQrm	= 7578,
    X86_VPMULDQrr	= 7579,
    X86_VPMULHRSWrm128	= 7580,
    X86_VPMULHRSWrm256	= 7581,
    X86_VPMULHRSWrr128	= 7582,
    X86_VPMULHRSWrr256	= 7583,
    X86_VPMULHUWYrm	= 7584,
    X86_VPMULHUWYrr	= 7585,
    X86_VPMULHUWrm	= 7586,
    X86_VPMULHUWrr	= 7587,
    X86_VPMULHWYrm	= 7588,
    X86_VPMULHWYrr	= 7589,
    X86_VPMULHWrm	= 7590,
    X86_VPMULHWrr	= 7591,
    X86_VPMULLDYrm	= 7592,
    X86_VPMULLDYrr	= 7593,
    X86_VPMULLDZ128rm	= 7594,
    X86_VPMULLDZ128rmb	= 7595,
    X86_VPMULLDZ128rmbk	= 7596,
    X86_VPMULLDZ128rmbkz	= 7597,
    X86_VPMULLDZ128rmk	= 7598,
    X86_VPMULLDZ128rmkz	= 7599,
    X86_VPMULLDZ128rr	= 7600,
    X86_VPMULLDZ128rrk	= 7601,
    X86_VPMULLDZ128rrkz	= 7602,
    X86_VPMULLDZ256rm	= 7603,
    X86_VPMULLDZ256rmb	= 7604,
    X86_VPMULLDZ256rmbk	= 7605,
    X86_VPMULLDZ256rmbkz	= 7606,
    X86_VPMULLDZ256rmk	= 7607,
    X86_VPMULLDZ256rmkz	= 7608,
    X86_VPMULLDZ256rr	= 7609,
    X86_VPMULLDZ256rrk	= 7610,
    X86_VPMULLDZ256rrkz	= 7611,
    X86_VPMULLDZrm	= 7612,
    X86_VPMULLDZrmb	= 7613,
    X86_VPMULLDZrmbk	= 7614,
    X86_VPMULLDZrmbkz	= 7615,
    X86_VPMULLDZrmk	= 7616,
    X86_VPMULLDZrmkz	= 7617,
    X86_VPMULLDZrr	= 7618,
    X86_VPMULLDZrrk	= 7619,
    X86_VPMULLDZrrkz	= 7620,
    X86_VPMULLDrm	= 7621,
    X86_VPMULLDrr	= 7622,
    X86_VPMULLQZ128rm	= 7623,
    X86_VPMULLQZ128rmb	= 7624,
    X86_VPMULLQZ128rmbk	= 7625,
    X86_VPMULLQZ128rmbkz	= 7626,
    X86_VPMULLQZ128rmk	= 7627,
    X86_VPMULLQZ128rmkz	= 7628,
    X86_VPMULLQZ128rr	= 7629,
    X86_VPMULLQZ128rrk	= 7630,
    X86_VPMULLQZ128rrkz	= 7631,
    X86_VPMULLQZ256rm	= 7632,
    X86_VPMULLQZ256rmb	= 7633,
    X86_VPMULLQZ256rmbk	= 7634,
    X86_VPMULLQZ256rmbkz	= 7635,
    X86_VPMULLQZ256rmk	= 7636,
    X86_VPMULLQZ256rmkz	= 7637,
    X86_VPMULLQZ256rr	= 7638,
    X86_VPMULLQZ256rrk	= 7639,
    X86_VPMULLQZ256rrkz	= 7640,
    X86_VPMULLQZrm	= 7641,
    X86_VPMULLQZrmb	= 7642,
    X86_VPMULLQZrmbk	= 7643,
    X86_VPMULLQZrmbkz	= 7644,
    X86_VPMULLQZrmk	= 7645,
    X86_VPMULLQZrmkz	= 7646,
    X86_VPMULLQZrr	= 7647,
    X86_VPMULLQZrrk	= 7648,
    X86_VPMULLQZrrkz	= 7649,
    X86_VPMULLWYrm	= 7650,
    X86_VPMULLWYrr	= 7651,
    X86_VPMULLWZ128rm	= 7652,
    X86_VPMULLWZ128rmk	= 7653,
    X86_VPMULLWZ128rmkz	= 7654,
    X86_VPMULLWZ128rr	= 7655,
    X86_VPMULLWZ128rrk	= 7656,
    X86_VPMULLWZ128rrkz	= 7657,
    X86_VPMULLWZ256rm	= 7658,
    X86_VPMULLWZ256rmk	= 7659,
    X86_VPMULLWZ256rmkz	= 7660,
    X86_VPMULLWZ256rr	= 7661,
    X86_VPMULLWZ256rrk	= 7662,
    X86_VPMULLWZ256rrkz	= 7663,
    X86_VPMULLWZrm	= 7664,
    X86_VPMULLWZrmk	= 7665,
    X86_VPMULLWZrmkz	= 7666,
    X86_VPMULLWZrr	= 7667,
    X86_VPMULLWZrrk	= 7668,
    X86_VPMULLWZrrkz	= 7669,
    X86_VPMULLWrm	= 7670,
    X86_VPMULLWrr	= 7671,
    X86_VPMULUDQYrm	= 7672,
    X86_VPMULUDQYrr	= 7673,
    X86_VPMULUDQZrm	= 7674,
    X86_VPMULUDQZrmb	= 7675,
    X86_VPMULUDQZrmbk	= 7676,
    X86_VPMULUDQZrmbkz	= 7677,
    X86_VPMULUDQZrmk	= 7678,
    X86_VPMULUDQZrmkz	= 7679,
    X86_VPMULUDQZrr	= 7680,
    X86_VPMULUDQZrrk	= 7681,
    X86_VPMULUDQZrrkz	= 7682,
    X86_VPMULUDQrm	= 7683,
    X86_VPMULUDQrr	= 7684,
    X86_VPORDZ128rm	= 7685,
    X86_VPORDZ128rmb	= 7686,
    X86_VPORDZ128rmbk	= 7687,
    X86_VPORDZ128rmbkz	= 7688,
    X86_VPORDZ128rmk	= 7689,
    X86_VPORDZ128rmkz	= 7690,
    X86_VPORDZ128rr	= 7691,
    X86_VPORDZ128rrk	= 7692,
    X86_VPORDZ128rrkz	= 7693,
    X86_VPORDZ256rm	= 7694,
    X86_VPORDZ256rmb	= 7695,
    X86_VPORDZ256rmbk	= 7696,
    X86_VPORDZ256rmbkz	= 7697,
    X86_VPORDZ256rmk	= 7698,
    X86_VPORDZ256rmkz	= 7699,
    X86_VPORDZ256rr	= 7700,
    X86_VPORDZ256rrk	= 7701,
    X86_VPORDZ256rrkz	= 7702,
    X86_VPORDZrm	= 7703,
    X86_VPORDZrmb	= 7704,
    X86_VPORDZrmbk	= 7705,
    X86_VPORDZrmbkz	= 7706,
    X86_VPORDZrmk	= 7707,
    X86_VPORDZrmkz	= 7708,
    X86_VPORDZrr	= 7709,
    X86_VPORDZrrk	= 7710,
    X86_VPORDZrrkz	= 7711,
    X86_VPORQZ128rm	= 7712,
    X86_VPORQZ128rmb	= 7713,
    X86_VPORQZ128rmbk	= 7714,
    X86_VPORQZ128rmbkz	= 7715,
    X86_VPORQZ128rmk	= 7716,
    X86_VPORQZ128rmkz	= 7717,
    X86_VPORQZ128rr	= 7718,
    X86_VPORQZ128rrk	= 7719,
    X86_VPORQZ128rrkz	= 7720,
    X86_VPORQZ256rm	= 7721,
    X86_VPORQZ256rmb	= 7722,
    X86_VPORQZ256rmbk	= 7723,
    X86_VPORQZ256rmbkz	= 7724,
    X86_VPORQZ256rmk	= 7725,
    X86_VPORQZ256rmkz	= 7726,
    X86_VPORQZ256rr	= 7727,
    X86_VPORQZ256rrk	= 7728,
    X86_VPORQZ256rrkz	= 7729,
    X86_VPORQZrm	= 7730,
    X86_VPORQZrmb	= 7731,
    X86_VPORQZrmbk	= 7732,
    X86_VPORQZrmbkz	= 7733,
    X86_VPORQZrmk	= 7734,
    X86_VPORQZrmkz	= 7735,
    X86_VPORQZrr	= 7736,
    X86_VPORQZrrk	= 7737,
    X86_VPORQZrrkz	= 7738,
    X86_VPORYrm	= 7739,
    X86_VPORYrr	= 7740,
    X86_VPORrm	= 7741,
    X86_VPORrr	= 7742,
    X86_VPPERMmr	= 7743,
    X86_VPPERMrm	= 7744,
    X86_VPPERMrr	= 7745,
    X86_VPROTBmi	= 7746,
    X86_VPROTBmr	= 7747,
    X86_VPROTBri	= 7748,
    X86_VPROTBrm	= 7749,
    X86_VPROTBrr	= 7750,
    X86_VPROTDmi	= 7751,
    X86_VPROTDmr	= 7752,
    X86_VPROTDri	= 7753,
    X86_VPROTDrm	= 7754,
    X86_VPROTDrr	= 7755,
    X86_VPROTQmi	= 7756,
    X86_VPROTQmr	= 7757,
    X86_VPROTQri	= 7758,
    X86_VPROTQrm	= 7759,
    X86_VPROTQrr	= 7760,
    X86_VPROTWmi	= 7761,
    X86_VPROTWmr	= 7762,
    X86_VPROTWri	= 7763,
    X86_VPROTWrm	= 7764,
    X86_VPROTWrr	= 7765,
    X86_VPSADBWYrm	= 7766,
    X86_VPSADBWYrr	= 7767,
    X86_VPSADBWrm	= 7768,
    X86_VPSADBWrr	= 7769,
    X86_VPSCATTERDDZmr	= 7770,
    X86_VPSCATTERDQZmr	= 7771,
    X86_VPSCATTERQDZmr	= 7772,
    X86_VPSCATTERQQZmr	= 7773,
    X86_VPSHABmr	= 7774,
    X86_VPSHABrm	= 7775,
    X86_VPSHABrr	= 7776,
    X86_VPSHADmr	= 7777,
    X86_VPSHADrm	= 7778,
    X86_VPSHADrr	= 7779,
    X86_VPSHAQmr	= 7780,
    X86_VPSHAQrm	= 7781,
    X86_VPSHAQrr	= 7782,
    X86_VPSHAWmr	= 7783,
    X86_VPSHAWrm	= 7784,
    X86_VPSHAWrr	= 7785,
    X86_VPSHLBmr	= 7786,
    X86_VPSHLBrm	= 7787,
    X86_VPSHLBrr	= 7788,
    X86_VPSHLDmr	= 7789,
    X86_VPSHLDrm	= 7790,
    X86_VPSHLDrr	= 7791,
    X86_VPSHLQmr	= 7792,
    X86_VPSHLQrm	= 7793,
    X86_VPSHLQrr	= 7794,
    X86_VPSHLWmr	= 7795,
    X86_VPSHLWrm	= 7796,
    X86_VPSHLWrr	= 7797,
    X86_VPSHUFBYrm	= 7798,
    X86_VPSHUFBYrr	= 7799,
    X86_VPSHUFBrm	= 7800,
    X86_VPSHUFBrr	= 7801,
    X86_VPSHUFDYmi	= 7802,
    X86_VPSHUFDYri	= 7803,
    X86_VPSHUFDZmi	= 7804,
    X86_VPSHUFDZri	= 7805,
    X86_VPSHUFDmi	= 7806,
    X86_VPSHUFDri	= 7807,
    X86_VPSHUFHWYmi	= 7808,
    X86_VPSHUFHWYri	= 7809,
    X86_VPSHUFHWmi	= 7810,
    X86_VPSHUFHWri	= 7811,
    X86_VPSHUFLWYmi	= 7812,
    X86_VPSHUFLWYri	= 7813,
    X86_VPSHUFLWmi	= 7814,
    X86_VPSHUFLWri	= 7815,
    X86_VPSIGNBYrm	= 7816,
    X86_VPSIGNBYrr	= 7817,
    X86_VPSIGNBrm	= 7818,
    X86_VPSIGNBrr	= 7819,
    X86_VPSIGNDYrm	= 7820,
    X86_VPSIGNDYrr	= 7821,
    X86_VPSIGNDrm	= 7822,
    X86_VPSIGNDrr	= 7823,
    X86_VPSIGNWYrm	= 7824,
    X86_VPSIGNWYrr	= 7825,
    X86_VPSIGNWrm	= 7826,
    X86_VPSIGNWrr	= 7827,
    X86_VPSLLDQYri	= 7828,
    X86_VPSLLDQri	= 7829,
    X86_VPSLLDYri	= 7830,
    X86_VPSLLDYrm	= 7831,
    X86_VPSLLDYrr	= 7832,
    X86_VPSLLDZmi	= 7833,
    X86_VPSLLDZmik	= 7834,
    X86_VPSLLDZmikz	= 7835,
    X86_VPSLLDZri	= 7836,
    X86_VPSLLDZrik	= 7837,
    X86_VPSLLDZrikz	= 7838,
    X86_VPSLLDZrm	= 7839,
    X86_VPSLLDZrmk	= 7840,
    X86_VPSLLDZrmkz	= 7841,
    X86_VPSLLDZrr	= 7842,
    X86_VPSLLDZrrk	= 7843,
    X86_VPSLLDZrrkz	= 7844,
    X86_VPSLLDri	= 7845,
    X86_VPSLLDrm	= 7846,
    X86_VPSLLDrr	= 7847,
    X86_VPSLLQYri	= 7848,
    X86_VPSLLQYrm	= 7849,
    X86_VPSLLQYrr	= 7850,
    X86_VPSLLQZmi	= 7851,
    X86_VPSLLQZmik	= 7852,
    X86_VPSLLQZmikz	= 7853,
    X86_VPSLLQZri	= 7854,
    X86_VPSLLQZrik	= 7855,
    X86_VPSLLQZrikz	= 7856,
    X86_VPSLLQZrm	= 7857,
    X86_VPSLLQZrmk	= 7858,
    X86_VPSLLQZrmkz	= 7859,
    X86_VPSLLQZrr	= 7860,
    X86_VPSLLQZrrk	= 7861,
    X86_VPSLLQZrrkz	= 7862,
    X86_VPSLLQri	= 7863,
    X86_VPSLLQrm	= 7864,
    X86_VPSLLQrr	= 7865,
    X86_VPSLLVDYrm	= 7866,
    X86_VPSLLVDYrr	= 7867,
    X86_VPSLLVDZrm	= 7868,
    X86_VPSLLVDZrmk	= 7869,
    X86_VPSLLVDZrmkz	= 7870,
    X86_VPSLLVDZrr	= 7871,
    X86_VPSLLVDZrrk	= 7872,
    X86_VPSLLVDZrrkz	= 7873,
    X86_VPSLLVDrm	= 7874,
    X86_VPSLLVDrr	= 7875,
    X86_VPSLLVQYrm	= 7876,
    X86_VPSLLVQYrr	= 7877,
    X86_VPSLLVQZrm	= 7878,
    X86_VPSLLVQZrmk	= 7879,
    X86_VPSLLVQZrmkz	= 7880,
    X86_VPSLLVQZrr	= 7881,
    X86_VPSLLVQZrrk	= 7882,
    X86_VPSLLVQZrrkz	= 7883,
    X86_VPSLLVQrm	= 7884,
    X86_VPSLLVQrr	= 7885,
    X86_VPSLLWYri	= 7886,
    X86_VPSLLWYrm	= 7887,
    X86_VPSLLWYrr	= 7888,
    X86_VPSLLWri	= 7889,
    X86_VPSLLWrm	= 7890,
    X86_VPSLLWrr	= 7891,
    X86_VPSRADYri	= 7892,
    X86_VPSRADYrm	= 7893,
    X86_VPSRADYrr	= 7894,
    X86_VPSRADZmi	= 7895,
    X86_VPSRADZmik	= 7896,
    X86_VPSRADZmikz	= 7897,
    X86_VPSRADZri	= 7898,
    X86_VPSRADZrik	= 7899,
    X86_VPSRADZrikz	= 7900,
    X86_VPSRADZrm	= 7901,
    X86_VPSRADZrmk	= 7902,
    X86_VPSRADZrmkz	= 7903,
    X86_VPSRADZrr	= 7904,
    X86_VPSRADZrrk	= 7905,
    X86_VPSRADZrrkz	= 7906,
    X86_VPSRADri	= 7907,
    X86_VPSRADrm	= 7908,
    X86_VPSRADrr	= 7909,
    X86_VPSRAQZmi	= 7910,
    X86_VPSRAQZmik	= 7911,
    X86_VPSRAQZmikz	= 7912,
    X86_VPSRAQZri	= 7913,
    X86_VPSRAQZrik	= 7914,
    X86_VPSRAQZrikz	= 7915,
    X86_VPSRAQZrm	= 7916,
    X86_VPSRAQZrmk	= 7917,
    X86_VPSRAQZrmkz	= 7918,
    X86_VPSRAQZrr	= 7919,
    X86_VPSRAQZrrk	= 7920,
    X86_VPSRAQZrrkz	= 7921,
    X86_VPSRAVDYrm	= 7922,
    X86_VPSRAVDYrr	= 7923,
    X86_VPSRAVDZrm	= 7924,
    X86_VPSRAVDZrmk	= 7925,
    X86_VPSRAVDZrmkz	= 7926,
    X86_VPSRAVDZrr	= 7927,
    X86_VPSRAVDZrrk	= 7928,
    X86_VPSRAVDZrrkz	= 7929,
    X86_VPSRAVDrm	= 7930,
    X86_VPSRAVDrr	= 7931,
    X86_VPSRAVQZrm	= 7932,
    X86_VPSRAVQZrmk	= 7933,
    X86_VPSRAVQZrmkz	= 7934,
    X86_VPSRAVQZrr	= 7935,
    X86_VPSRAVQZrrk	= 7936,
    X86_VPSRAVQZrrkz	= 7937,
    X86_VPSRAWYri	= 7938,
    X86_VPSRAWYrm	= 7939,
    X86_VPSRAWYrr	= 7940,
    X86_VPSRAWri	= 7941,
    X86_VPSRAWrm	= 7942,
    X86_VPSRAWrr	= 7943,
    X86_VPSRLDQYri	= 7944,
    X86_VPSRLDQri	= 7945,
    X86_VPSRLDYri	= 7946,
    X86_VPSRLDYrm	= 7947,
    X86_VPSRLDYrr	= 7948,
    X86_VPSRLDZmi	= 7949,
    X86_VPSRLDZmik	= 7950,
    X86_VPSRLDZmikz	= 7951,
    X86_VPSRLDZri	= 7952,
    X86_VPSRLDZrik	= 7953,
    X86_VPSRLDZrikz	= 7954,
    X86_VPSRLDZrm	= 7955,
    X86_VPSRLDZrmk	= 7956,
    X86_VPSRLDZrmkz	= 7957,
    X86_VPSRLDZrr	= 7958,
    X86_VPSRLDZrrk	= 7959,
    X86_VPSRLDZrrkz	= 7960,
    X86_VPSRLDri	= 7961,
    X86_VPSRLDrm	= 7962,
    X86_VPSRLDrr	= 7963,
    X86_VPSRLQYri	= 7964,
    X86_VPSRLQYrm	= 7965,
    X86_VPSRLQYrr	= 7966,
    X86_VPSRLQZmi	= 7967,
    X86_VPSRLQZmik	= 7968,
    X86_VPSRLQZmikz	= 7969,
    X86_VPSRLQZri	= 7970,
    X86_VPSRLQZrik	= 7971,
    X86_VPSRLQZrikz	= 7972,
    X86_VPSRLQZrm	= 7973,
    X86_VPSRLQZrmk	= 7974,
    X86_VPSRLQZrmkz	= 7975,
    X86_VPSRLQZrr	= 7976,
    X86_VPSRLQZrrk	= 7977,
    X86_VPSRLQZrrkz	= 7978,
    X86_VPSRLQri	= 7979,
    X86_VPSRLQrm	= 7980,
    X86_VPSRLQrr	= 7981,
    X86_VPSRLVDYrm	= 7982,
    X86_VPSRLVDYrr	= 7983,
    X86_VPSRLVDZrm	= 7984,
    X86_VPSRLVDZrmk	= 7985,
    X86_VPSRLVDZrmkz	= 7986,
    X86_VPSRLVDZrr	= 7987,
    X86_VPSRLVDZrrk	= 7988,
    X86_VPSRLVDZrrkz	= 7989,
    X86_VPSRLVDrm	= 7990,
    X86_VPSRLVDrr	= 7991,
    X86_VPSRLVQYrm	= 7992,
    X86_VPSRLVQYrr	= 7993,
    X86_VPSRLVQZrm	= 7994,
    X86_VPSRLVQZrmk	= 7995,
    X86_VPSRLVQZrmkz	= 7996,
    X86_VPSRLVQZrr	= 7997,
    X86_VPSRLVQZrrk	= 7998,
    X86_VPSRLVQZrrkz	= 7999,
    X86_VPSRLVQrm	= 8000,
    X86_VPSRLVQrr	= 8001,
    X86_VPSRLWYri	= 8002,
    X86_VPSRLWYrm	= 8003,
    X86_VPSRLWYrr	= 8004,
    X86_VPSRLWri	= 8005,
    X86_VPSRLWrm	= 8006,
    X86_VPSRLWrr	= 8007,
    X86_VPSUBBYrm	= 8008,
    X86_VPSUBBYrr	= 8009,
    X86_VPSUBBZ128rm	= 8010,
    X86_VPSUBBZ128rmk	= 8011,
    X86_VPSUBBZ128rmkz	= 8012,
    X86_VPSUBBZ128rr	= 8013,
    X86_VPSUBBZ128rrk	= 8014,
    X86_VPSUBBZ128rrkz	= 8015,
    X86_VPSUBBZ256rm	= 8016,
    X86_VPSUBBZ256rmk	= 8017,
    X86_VPSUBBZ256rmkz	= 8018,
    X86_VPSUBBZ256rr	= 8019,
    X86_VPSUBBZ256rrk	= 8020,
    X86_VPSUBBZ256rrkz	= 8021,
    X86_VPSUBBZrm	= 8022,
    X86_VPSUBBZrmk	= 8023,
    X86_VPSUBBZrmkz	= 8024,
    X86_VPSUBBZrr	= 8025,
    X86_VPSUBBZrrk	= 8026,
    X86_VPSUBBZrrkz	= 8027,
    X86_VPSUBBrm	= 8028,
    X86_VPSUBBrr	= 8029,
    X86_VPSUBDYrm	= 8030,
    X86_VPSUBDYrr	= 8031,
    X86_VPSUBDZ128rm	= 8032,
    X86_VPSUBDZ128rmb	= 8033,
    X86_VPSUBDZ128rmbk	= 8034,
    X86_VPSUBDZ128rmbkz	= 8035,
    X86_VPSUBDZ128rmk	= 8036,
    X86_VPSUBDZ128rmkz	= 8037,
    X86_VPSUBDZ128rr	= 8038,
    X86_VPSUBDZ128rrk	= 8039,
    X86_VPSUBDZ128rrkz	= 8040,
    X86_VPSUBDZ256rm	= 8041,
    X86_VPSUBDZ256rmb	= 8042,
    X86_VPSUBDZ256rmbk	= 8043,
    X86_VPSUBDZ256rmbkz	= 8044,
    X86_VPSUBDZ256rmk	= 8045,
    X86_VPSUBDZ256rmkz	= 8046,
    X86_VPSUBDZ256rr	= 8047,
    X86_VPSUBDZ256rrk	= 8048,
    X86_VPSUBDZ256rrkz	= 8049,
    X86_VPSUBDZrm	= 8050,
    X86_VPSUBDZrmb	= 8051,
    X86_VPSUBDZrmbk	= 8052,
    X86_VPSUBDZrmbkz	= 8053,
    X86_VPSUBDZrmk	= 8054,
    X86_VPSUBDZrmkz	= 8055,
    X86_VPSUBDZrr	= 8056,
    X86_VPSUBDZrrk	= 8057,
    X86_VPSUBDZrrkz	= 8058,
    X86_VPSUBDrm	= 8059,
    X86_VPSUBDrr	= 8060,
    X86_VPSUBQYrm	= 8061,
    X86_VPSUBQYrr	= 8062,
    X86_VPSUBQZ128rm	= 8063,
    X86_VPSUBQZ128rmb	= 8064,
    X86_VPSUBQZ128rmbk	= 8065,
    X86_VPSUBQZ128rmbkz	= 8066,
    X86_VPSUBQZ128rmk	= 8067,
    X86_VPSUBQZ128rmkz	= 8068,
    X86_VPSUBQZ128rr	= 8069,
    X86_VPSUBQZ128rrk	= 8070,
    X86_VPSUBQZ128rrkz	= 8071,
    X86_VPSUBQZ256rm	= 8072,
    X86_VPSUBQZ256rmb	= 8073,
    X86_VPSUBQZ256rmbk	= 8074,
    X86_VPSUBQZ256rmbkz	= 8075,
    X86_VPSUBQZ256rmk	= 8076,
    X86_VPSUBQZ256rmkz	= 8077,
    X86_VPSUBQZ256rr	= 8078,
    X86_VPSUBQZ256rrk	= 8079,
    X86_VPSUBQZ256rrkz	= 8080,
    X86_VPSUBQZrm	= 8081,
    X86_VPSUBQZrmb	= 8082,
    X86_VPSUBQZrmbk	= 8083,
    X86_VPSUBQZrmbkz	= 8084,
    X86_VPSUBQZrmk	= 8085,
    X86_VPSUBQZrmkz	= 8086,
    X86_VPSUBQZrr	= 8087,
    X86_VPSUBQZrrk	= 8088,
    X86_VPSUBQZrrkz	= 8089,
    X86_VPSUBQrm	= 8090,
    X86_VPSUBQrr	= 8091,
    X86_VPSUBSBYrm	= 8092,
    X86_VPSUBSBYrr	= 8093,
    X86_VPSUBSBrm	= 8094,
    X86_VPSUBSBrr	= 8095,
    X86_VPSUBSWYrm	= 8096,
    X86_VPSUBSWYrr	= 8097,
    X86_VPSUBSWrm	= 8098,
    X86_VPSUBSWrr	= 8099,
    X86_VPSUBUSBYrm	= 8100,
    X86_VPSUBUSBYrr	= 8101,
    X86_VPSUBUSBrm	= 8102,
    X86_VPSUBUSBrr	= 8103,
    X86_VPSUBUSWYrm	= 8104,
    X86_VPSUBUSWYrr	= 8105,
    X86_VPSUBUSWrm	= 8106,
    X86_VPSUBUSWrr	= 8107,
    X86_VPSUBWYrm	= 8108,
    X86_VPSUBWYrr	= 8109,
    X86_VPSUBWZ128rm	= 8110,
    X86_VPSUBWZ128rmk	= 8111,
    X86_VPSUBWZ128rmkz	= 8112,
    X86_VPSUBWZ128rr	= 8113,
    X86_VPSUBWZ128rrk	= 8114,
    X86_VPSUBWZ128rrkz	= 8115,
    X86_VPSUBWZ256rm	= 8116,
    X86_VPSUBWZ256rmk	= 8117,
    X86_VPSUBWZ256rmkz	= 8118,
    X86_VPSUBWZ256rr	= 8119,
    X86_VPSUBWZ256rrk	= 8120,
    X86_VPSUBWZ256rrkz	= 8121,
    X86_VPSUBWZrm	= 8122,
    X86_VPSUBWZrmk	= 8123,
    X86_VPSUBWZrmkz	= 8124,
    X86_VPSUBWZrr	= 8125,
    X86_VPSUBWZrrk	= 8126,
    X86_VPSUBWZrrkz	= 8127,
    X86_VPSUBWrm	= 8128,
    X86_VPSUBWrr	= 8129,
    X86_VPTESTMDZrm	= 8130,
    X86_VPTESTMDZrr	= 8131,
    X86_VPTESTMQZrm	= 8132,
    X86_VPTESTMQZrr	= 8133,
    X86_VPTESTNMDZrm	= 8134,
    X86_VPTESTNMDZrr	= 8135,
    X86_VPTESTNMQZrm	= 8136,
    X86_VPTESTNMQZrr	= 8137,
    X86_VPTESTYrm	= 8138,
    X86_VPTESTYrr	= 8139,
    X86_VPTESTrm	= 8140,
    X86_VPTESTrr	= 8141,
    X86_VPUNPCKHBWYrm	= 8142,
    X86_VPUNPCKHBWYrr	= 8143,
    X86_VPUNPCKHBWrm	= 8144,
    X86_VPUNPCKHBWrr	= 8145,
    X86_VPUNPCKHDQYrm	= 8146,
    X86_VPUNPCKHDQYrr	= 8147,
    X86_VPUNPCKHDQZrm	= 8148,
    X86_VPUNPCKHDQZrr	= 8149,
    X86_VPUNPCKHDQrm	= 8150,
    X86_VPUNPCKHDQrr	= 8151,
    X86_VPUNPCKHQDQYrm	= 8152,
    X86_VPUNPCKHQDQYrr	= 8153,
    X86_VPUNPCKHQDQZrm	= 8154,
    X86_VPUNPCKHQDQZrr	= 8155,
    X86_VPUNPCKHQDQrm	= 8156,
    X86_VPUNPCKHQDQrr	= 8157,
    X86_VPUNPCKHWDYrm	= 8158,
    X86_VPUNPCKHWDYrr	= 8159,
    X86_VPUNPCKHWDrm	= 8160,
    X86_VPUNPCKHWDrr	= 8161,
    X86_VPUNPCKLBWYrm	= 8162,
    X86_VPUNPCKLBWYrr	= 8163,
    X86_VPUNPCKLBWrm	= 8164,
    X86_VPUNPCKLBWrr	= 8165,
    X86_VPUNPCKLDQYrm	= 8166,
    X86_VPUNPCKLDQYrr	= 8167,
    X86_VPUNPCKLDQZrm	= 8168,
    X86_VPUNPCKLDQZrr	= 8169,
    X86_VPUNPCKLDQrm	= 8170,
    X86_VPUNPCKLDQrr	= 8171,
    X86_VPUNPCKLQDQYrm	= 8172,
    X86_VPUNPCKLQDQYrr	= 8173,
    X86_VPUNPCKLQDQZrm	= 8174,
    X86_VPUNPCKLQDQZrr	= 8175,
    X86_VPUNPCKLQDQrm	= 8176,
    X86_VPUNPCKLQDQrr	= 8177,
    X86_VPUNPCKLWDYrm	= 8178,
    X86_VPUNPCKLWDYrr	= 8179,
    X86_VPUNPCKLWDrm	= 8180,
    X86_VPUNPCKLWDrr	= 8181,
    X86_VPXORDZ128rm	= 8182,
    X86_VPXORDZ128rmb	= 8183,
    X86_VPXORDZ128rmbk	= 8184,
    X86_VPXORDZ128rmbkz	= 8185,
    X86_VPXORDZ128rmk	= 8186,
    X86_VPXORDZ128rmkz	= 8187,
    X86_VPXORDZ128rr	= 8188,
    X86_VPXORDZ128rrk	= 8189,
    X86_VPXORDZ128rrkz	= 8190,
    X86_VPXORDZ256rm	= 8191,
    X86_VPXORDZ256rmb	= 8192,
    X86_VPXORDZ256rmbk	= 8193,
    X86_VPXORDZ256rmbkz	= 8194,
    X86_VPXORDZ256rmk	= 8195,
    X86_VPXORDZ256rmkz	= 8196,
    X86_VPXORDZ256rr	= 8197,
    X86_VPXORDZ256rrk	= 8198,
    X86_VPXORDZ256rrkz	= 8199,
    X86_VPXORDZrm	= 8200,
    X86_VPXORDZrmb	= 8201,
    X86_VPXORDZrmbk	= 8202,
    X86_VPXORDZrmbkz	= 8203,
    X86_VPXORDZrmk	= 8204,
    X86_VPXORDZrmkz	= 8205,
    X86_VPXORDZrr	= 8206,
    X86_VPXORDZrrk	= 8207,
    X86_VPXORDZrrkz	= 8208,
    X86_VPXORQZ128rm	= 8209,
    X86_VPXORQZ128rmb	= 8210,
    X86_VPXORQZ128rmbk	= 8211,
    X86_VPXORQZ128rmbkz	= 8212,
    X86_VPXORQZ128rmk	= 8213,
    X86_VPXORQZ128rmkz	= 8214,
    X86_VPXORQZ128rr	= 8215,
    X86_VPXORQZ128rrk	= 8216,
    X86_VPXORQZ128rrkz	= 8217,
    X86_VPXORQZ256rm	= 8218,
    X86_VPXORQZ256rmb	= 8219,
    X86_VPXORQZ256rmbk	= 8220,
    X86_VPXORQZ256rmbkz	= 8221,
    X86_VPXORQZ256rmk	= 8222,
    X86_VPXORQZ256rmkz	= 8223,
    X86_VPXORQZ256rr	= 8224,
    X86_VPXORQZ256rrk	= 8225,
    X86_VPXORQZ256rrkz	= 8226,
    X86_VPXORQZrm	= 8227,
    X86_VPXORQZrmb	= 8228,
    X86_VPXORQZrmbk	= 8229,
    X86_VPXORQZrmbkz	= 8230,
    X86_VPXORQZrmk	= 8231,
    X86_VPXORQZrmkz	= 8232,
    X86_VPXORQZrr	= 8233,
    X86_VPXORQZrrk	= 8234,
    X86_VPXORQZrrkz	= 8235,
    X86_VPXORYrm	= 8236,
    X86_VPXORYrr	= 8237,
    X86_VPXORrm	= 8238,
    X86_VPXORrr	= 8239,
    X86_VRCP14PDZ128m	= 8240,
    X86_VRCP14PDZ128mb	= 8241,
    X86_VRCP14PDZ128mbk	= 8242,
    X86_VRCP14PDZ128mbkz	= 8243,
    X86_VRCP14PDZ128mk	= 8244,
    X86_VRCP14PDZ128mkz	= 8245,
    X86_VRCP14PDZ128r	= 8246,
    X86_VRCP14PDZ128rk	= 8247,
    X86_VRCP14PDZ128rkz	= 8248,
    X86_VRCP14PDZ256m	= 8249,
    X86_VRCP14PDZ256mb	= 8250,
    X86_VRCP14PDZ256mbk	= 8251,
    X86_VRCP14PDZ256mbkz	= 8252,
    X86_VRCP14PDZ256mk	= 8253,
    X86_VRCP14PDZ256mkz	= 8254,
    X86_VRCP14PDZ256r	= 8255,
    X86_VRCP14PDZ256rk	= 8256,
    X86_VRCP14PDZ256rkz	= 8257,
    X86_VRCP14PDZm	= 8258,
    X86_VRCP14PDZmb	= 8259,
    X86_VRCP14PDZmbk	= 8260,
    X86_VRCP14PDZmbkz	= 8261,
    X86_VRCP14PDZmk	= 8262,
    X86_VRCP14PDZmkz	= 8263,
    X86_VRCP14PDZr	= 8264,
    X86_VRCP14PDZrk	= 8265,
    X86_VRCP14PDZrkz	= 8266,
    X86_VRCP14PSZ128m	= 8267,
    X86_VRCP14PSZ128mb	= 8268,
    X86_VRCP14PSZ128mbk	= 8269,
    X86_VRCP14PSZ128mbkz	= 8270,
    X86_VRCP14PSZ128mk	= 8271,
    X86_VRCP14PSZ128mkz	= 8272,
    X86_VRCP14PSZ128r	= 8273,
    X86_VRCP14PSZ128rk	= 8274,
    X86_VRCP14PSZ128rkz	= 8275,
    X86_VRCP14PSZ256m	= 8276,
    X86_VRCP14PSZ256mb	= 8277,
    X86_VRCP14PSZ256mbk	= 8278,
    X86_VRCP14PSZ256mbkz	= 8279,
    X86_VRCP14PSZ256mk	= 8280,
    X86_VRCP14PSZ256mkz	= 8281,
    X86_VRCP14PSZ256r	= 8282,
    X86_VRCP14PSZ256rk	= 8283,
    X86_VRCP14PSZ256rkz	= 8284,
    X86_VRCP14PSZm	= 8285,
    X86_VRCP14PSZmb	= 8286,
    X86_VRCP14PSZmbk	= 8287,
    X86_VRCP14PSZmbkz	= 8288,
    X86_VRCP14PSZmk	= 8289,
    X86_VRCP14PSZmkz	= 8290,
    X86_VRCP14PSZr	= 8291,
    X86_VRCP14PSZrk	= 8292,
    X86_VRCP14PSZrkz	= 8293,
    X86_VRCP14SDrm	= 8294,
    X86_VRCP14SDrr	= 8295,
    X86_VRCP14SSrm	= 8296,
    X86_VRCP14SSrr	= 8297,
    X86_VRCP28PDm	= 8298,
    X86_VRCP28PDmb	= 8299,
    X86_VRCP28PDmbk	= 8300,
    X86_VRCP28PDmbkz	= 8301,
    X86_VRCP28PDmk	= 8302,
    X86_VRCP28PDmkz	= 8303,
    X86_VRCP28PDr	= 8304,
    X86_VRCP28PDrb	= 8305,
    X86_VRCP28PDrbk	= 8306,
    X86_VRCP28PDrbkz	= 8307,
    X86_VRCP28PDrk	= 8308,
    X86_VRCP28PDrkz	= 8309,
    X86_VRCP28PSm	= 8310,
    X86_VRCP28PSmb	= 8311,
    X86_VRCP28PSmbk	= 8312,
    X86_VRCP28PSmbkz	= 8313,
    X86_VRCP28PSmk	= 8314,
    X86_VRCP28PSmkz	= 8315,
    X86_VRCP28PSr	= 8316,
    X86_VRCP28PSrb	= 8317,
    X86_VRCP28PSrbk	= 8318,
    X86_VRCP28PSrbkz	= 8319,
    X86_VRCP28PSrk	= 8320,
    X86_VRCP28PSrkz	= 8321,
    X86_VRCP28SDm	= 8322,
    X86_VRCP28SDmk	= 8323,
    X86_VRCP28SDmkz	= 8324,
    X86_VRCP28SDr	= 8325,
    X86_VRCP28SDrb	= 8326,
    X86_VRCP28SDrbk	= 8327,
    X86_VRCP28SDrbkz	= 8328,
    X86_VRCP28SDrk	= 8329,
    X86_VRCP28SDrkz	= 8330,
    X86_VRCP28SSm	= 8331,
    X86_VRCP28SSmk	= 8332,
    X86_VRCP28SSmkz	= 8333,
    X86_VRCP28SSr	= 8334,
    X86_VRCP28SSrb	= 8335,
    X86_VRCP28SSrbk	= 8336,
    X86_VRCP28SSrbkz	= 8337,
    X86_VRCP28SSrk	= 8338,
    X86_VRCP28SSrkz	= 8339,
    X86_VRCPPSYm	= 8340,
    X86_VRCPPSYm_Int	= 8341,
    X86_VRCPPSYr	= 8342,
    X86_VRCPPSYr_Int	= 8343,
    X86_VRCPPSm	= 8344,
    X86_VRCPPSm_Int	= 8345,
    X86_VRCPPSr	= 8346,
    X86_VRCPPSr_Int	= 8347,
    X86_VRCPSSm	= 8348,
    X86_VRCPSSm_Int	= 8349,
    X86_VRCPSSr	= 8350,
    X86_VRNDSCALEPDZm	= 8351,
    X86_VRNDSCALEPDZr	= 8352,
    X86_VRNDSCALEPSZm	= 8353,
    X86_VRNDSCALEPSZr	= 8354,
    X86_VRNDSCALESDm	= 8355,
    X86_VRNDSCALESDmk	= 8356,
    X86_VRNDSCALESDmkz	= 8357,
    X86_VRNDSCALESDr	= 8358,
    X86_VRNDSCALESDrb	= 8359,
    X86_VRNDSCALESDrbk	= 8360,
    X86_VRNDSCALESDrbkz	= 8361,
    X86_VRNDSCALESDrk	= 8362,
    X86_VRNDSCALESDrkz	= 8363,
    X86_VRNDSCALESSm	= 8364,
    X86_VRNDSCALESSmk	= 8365,
    X86_VRNDSCALESSmkz	= 8366,
    X86_VRNDSCALESSr	= 8367,
    X86_VRNDSCALESSrb	= 8368,
    X86_VRNDSCALESSrbk	= 8369,
    X86_VRNDSCALESSrbkz	= 8370,
    X86_VRNDSCALESSrk	= 8371,
    X86_VRNDSCALESSrkz	= 8372,
    X86_VROUNDPDm	= 8373,
    X86_VROUNDPDr	= 8374,
    X86_VROUNDPSm	= 8375,
    X86_VROUNDPSr	= 8376,
    X86_VROUNDSDm	= 8377,
    X86_VROUNDSDr	= 8378,
    X86_VROUNDSDr_Int	= 8379,
    X86_VROUNDSSm	= 8380,
    X86_VROUNDSSr	= 8381,
    X86_VROUNDSSr_Int	= 8382,
    X86_VROUNDYPDm	= 8383,
    X86_VROUNDYPDr	= 8384,
    X86_VROUNDYPSm	= 8385,
    X86_VROUNDYPSr	= 8386,
    X86_VRSQRT14PDZ128m	= 8387,
    X86_VRSQRT14PDZ128mb	= 8388,
    X86_VRSQRT14PDZ128mbk	= 8389,
    X86_VRSQRT14PDZ128mbkz	= 8390,
    X86_VRSQRT14PDZ128mk	= 8391,
    X86_VRSQRT14PDZ128mkz	= 8392,
    X86_VRSQRT14PDZ128r	= 8393,
    X86_VRSQRT14PDZ128rk	= 8394,
    X86_VRSQRT14PDZ128rkz	= 8395,
    X86_VRSQRT14PDZ256m	= 8396,
    X86_VRSQRT14PDZ256mb	= 8397,
    X86_VRSQRT14PDZ256mbk	= 8398,
    X86_VRSQRT14PDZ256mbkz	= 8399,
    X86_VRSQRT14PDZ256mk	= 8400,
    X86_VRSQRT14PDZ256mkz	= 8401,
    X86_VRSQRT14PDZ256r	= 8402,
    X86_VRSQRT14PDZ256rk	= 8403,
    X86_VRSQRT14PDZ256rkz	= 8404,
    X86_VRSQRT14PDZm	= 8405,
    X86_VRSQRT14PDZmb	= 8406,
    X86_VRSQRT14PDZmbk	= 8407,
    X86_VRSQRT14PDZmbkz	= 8408,
    X86_VRSQRT14PDZmk	= 8409,
    X86_VRSQRT14PDZmkz	= 8410,
    X86_VRSQRT14PDZr	= 8411,
    X86_VRSQRT14PDZrk	= 8412,
    X86_VRSQRT14PDZrkz	= 8413,
    X86_VRSQRT14PSZ128m	= 8414,
    X86_VRSQRT14PSZ128mb	= 8415,
    X86_VRSQRT14PSZ128mbk	= 8416,
    X86_VRSQRT14PSZ128mbkz	= 8417,
    X86_VRSQRT14PSZ128mk	= 8418,
    X86_VRSQRT14PSZ128mkz	= 8419,
    X86_VRSQRT14PSZ128r	= 8420,
    X86_VRSQRT14PSZ128rk	= 8421,
    X86_VRSQRT14PSZ128rkz	= 8422,
    X86_VRSQRT14PSZ256m	= 8423,
    X86_VRSQRT14PSZ256mb	= 8424,
    X86_VRSQRT14PSZ256mbk	= 8425,
    X86_VRSQRT14PSZ256mbkz	= 8426,
    X86_VRSQRT14PSZ256mk	= 8427,
    X86_VRSQRT14PSZ256mkz	= 8428,
    X86_VRSQRT14PSZ256r	= 8429,
    X86_VRSQRT14PSZ256rk	= 8430,
    X86_VRSQRT14PSZ256rkz	= 8431,
    X86_VRSQRT14PSZm	= 8432,
    X86_VRSQRT14PSZmb	= 8433,
    X86_VRSQRT14PSZmbk	= 8434,
    X86_VRSQRT14PSZmbkz	= 8435,
    X86_VRSQRT14PSZmk	= 8436,
    X86_VRSQRT14PSZmkz	= 8437,
    X86_VRSQRT14PSZr	= 8438,
    X86_VRSQRT14PSZrk	= 8439,
    X86_VRSQRT14PSZrkz	= 8440,
    X86_VRSQRT14SDrm	= 8441,
    X86_VRSQRT14SDrr	= 8442,
    X86_VRSQRT14SSrm	= 8443,
    X86_VRSQRT14SSrr	= 8444,
    X86_VRSQRT28PDm	= 8445,
    X86_VRSQRT28PDmb	= 8446,
    X86_VRSQRT28PDmbk	= 8447,
    X86_VRSQRT28PDmbkz	= 8448,
    X86_VRSQRT28PDmk	= 8449,
    X86_VRSQRT28PDmkz	= 8450,
    X86_VRSQRT28PDr	= 8451,
    X86_VRSQRT28PDrb	= 8452,
    X86_VRSQRT28PDrbk	= 8453,
    X86_VRSQRT28PDrbkz	= 8454,
    X86_VRSQRT28PDrk	= 8455,
    X86_VRSQRT28PDrkz	= 8456,
    X86_VRSQRT28PSm	= 8457,
    X86_VRSQRT28PSmb	= 8458,
    X86_VRSQRT28PSmbk	= 8459,
    X86_VRSQRT28PSmbkz	= 8460,
    X86_VRSQRT28PSmk	= 8461,
    X86_VRSQRT28PSmkz	= 8462,
    X86_VRSQRT28PSr	= 8463,
    X86_VRSQRT28PSrb	= 8464,
    X86_VRSQRT28PSrbk	= 8465,
    X86_VRSQRT28PSrbkz	= 8466,
    X86_VRSQRT28PSrk	= 8467,
    X86_VRSQRT28PSrkz	= 8468,
    X86_VRSQRT28SDm	= 8469,
    X86_VRSQRT28SDmk	= 8470,
    X86_VRSQRT28SDmkz	= 8471,
    X86_VRSQRT28SDr	= 8472,
    X86_VRSQRT28SDrb	= 8473,
    X86_VRSQRT28SDrbk	= 8474,
    X86_VRSQRT28SDrbkz	= 8475,
    X86_VRSQRT28SDrk	= 8476,
    X86_VRSQRT28SDrkz	= 8477,
    X86_VRSQRT28SSm	= 8478,
    X86_VRSQRT28SSmk	= 8479,
    X86_VRSQRT28SSmkz	= 8480,
    X86_VRSQRT28SSr	= 8481,
    X86_VRSQRT28SSrb	= 8482,
    X86_VRSQRT28SSrbk	= 8483,
    X86_VRSQRT28SSrbkz	= 8484,
    X86_VRSQRT28SSrk	= 8485,
    X86_VRSQRT28SSrkz	= 8486,
    X86_VRSQRTPSYm	= 8487,
    X86_VRSQRTPSYm_Int	= 8488,
    X86_VRSQRTPSYr	= 8489,
    X86_VRSQRTPSYr_Int	= 8490,
    X86_VRSQRTPSm	= 8491,
    X86_VRSQRTPSm_Int	= 8492,
    X86_VRSQRTPSr	= 8493,
    X86_VRSQRTPSr_Int	= 8494,
    X86_VRSQRTSSm	= 8495,
    X86_VRSQRTSSm_Int	= 8496,
    X86_VRSQRTSSr	= 8497,
    X86_VSCATTERDPDZmr	= 8498,
    X86_VSCATTERDPSZmr	= 8499,
    X86_VSCATTERPF0DPDm	= 8500,
    X86_VSCATTERPF0DPSm	= 8501,
    X86_VSCATTERPF0QPDm	= 8502,
    X86_VSCATTERPF0QPSm	= 8503,
    X86_VSCATTERPF1DPDm	= 8504,
    X86_VSCATTERPF1DPSm	= 8505,
    X86_VSCATTERPF1QPDm	= 8506,
    X86_VSCATTERPF1QPSm	= 8507,
    X86_VSCATTERQPDZmr	= 8508,
    X86_VSCATTERQPSZmr	= 8509,
    X86_VSHUFPDYrmi	= 8510,
    X86_VSHUFPDYrri	= 8511,
    X86_VSHUFPDZrmi	= 8512,
    X86_VSHUFPDZrri	= 8513,
    X86_VSHUFPDrmi	= 8514,
    X86_VSHUFPDrri	= 8515,
    X86_VSHUFPSYrmi	= 8516,
    X86_VSHUFPSYrri	= 8517,
    X86_VSHUFPSZrmi	= 8518,
    X86_VSHUFPSZrri	= 8519,
    X86_VSHUFPSrmi	= 8520,
    X86_VSHUFPSrri	= 8521,
    X86_VSQRTPDYm	= 8522,
    X86_VSQRTPDYr	= 8523,
    X86_VSQRTPDZ128m	= 8524,
    X86_VSQRTPDZ128mb	= 8525,
    X86_VSQRTPDZ128mbk	= 8526,
    X86_VSQRTPDZ128mbkz	= 8527,
    X86_VSQRTPDZ128mk	= 8528,
    X86_VSQRTPDZ128mkz	= 8529,
    X86_VSQRTPDZ128r	= 8530,
    X86_VSQRTPDZ128rk	= 8531,
    X86_VSQRTPDZ128rkz	= 8532,
    X86_VSQRTPDZ256m	= 8533,
    X86_VSQRTPDZ256mb	= 8534,
    X86_VSQRTPDZ256mbk	= 8535,
    X86_VSQRTPDZ256mbkz	= 8536,
    X86_VSQRTPDZ256mk	= 8537,
    X86_VSQRTPDZ256mkz	= 8538,
    X86_VSQRTPDZ256r	= 8539,
    X86_VSQRTPDZ256rk	= 8540,
    X86_VSQRTPDZ256rkz	= 8541,
    X86_VSQRTPDZm	= 8542,
    X86_VSQRTPDZmb	= 8543,
    X86_VSQRTPDZmbk	= 8544,
    X86_VSQRTPDZmbkz	= 8545,
    X86_VSQRTPDZmk	= 8546,
    X86_VSQRTPDZmkz	= 8547,
    X86_VSQRTPDZr	= 8548,
    X86_VSQRTPDZrk	= 8549,
    X86_VSQRTPDZrkz	= 8550,
    X86_VSQRTPDm	= 8551,
    X86_VSQRTPDr	= 8552,
    X86_VSQRTPSYm	= 8553,
    X86_VSQRTPSYr	= 8554,
    X86_VSQRTPSZ128m	= 8555,
    X86_VSQRTPSZ128mb	= 8556,
    X86_VSQRTPSZ128mbk	= 8557,
    X86_VSQRTPSZ128mbkz	= 8558,
    X86_VSQRTPSZ128mk	= 8559,
    X86_VSQRTPSZ128mkz	= 8560,
    X86_VSQRTPSZ128r	= 8561,
    X86_VSQRTPSZ128rk	= 8562,
    X86_VSQRTPSZ128rkz	= 8563,
    X86_VSQRTPSZ256m	= 8564,
    X86_VSQRTPSZ256mb	= 8565,
    X86_VSQRTPSZ256mbk	= 8566,
    X86_VSQRTPSZ256mbkz	= 8567,
    X86_VSQRTPSZ256mk	= 8568,
    X86_VSQRTPSZ256mkz	= 8569,
    X86_VSQRTPSZ256r	= 8570,
    X86_VSQRTPSZ256rk	= 8571,
    X86_VSQRTPSZ256rkz	= 8572,
    X86_VSQRTPSZm	= 8573,
    X86_VSQRTPSZmb	= 8574,
    X86_VSQRTPSZmbk	= 8575,
    X86_VSQRTPSZmbkz	= 8576,
    X86_VSQRTPSZmk	= 8577,
    X86_VSQRTPSZmkz	= 8578,
    X86_VSQRTPSZr	= 8579,
    X86_VSQRTPSZrk	= 8580,
    X86_VSQRTPSZrkz	= 8581,
    X86_VSQRTPSm	= 8582,
    X86_VSQRTPSr	= 8583,
    X86_VSQRTSDZm	= 8584,
    X86_VSQRTSDZm_Int	= 8585,
    X86_VSQRTSDZr	= 8586,
    X86_VSQRTSDZr_Int	= 8587,
    X86_VSQRTSDm	= 8588,
    X86_VSQRTSDm_Int	= 8589,
    X86_VSQRTSDr	= 8590,
    X86_VSQRTSSZm	= 8591,
    X86_VSQRTSSZm_Int	= 8592,
    X86_VSQRTSSZr	= 8593,
    X86_VSQRTSSZr_Int	= 8594,
    X86_VSQRTSSm	= 8595,
    X86_VSQRTSSm_Int	= 8596,
    X86_VSQRTSSr	= 8597,
    X86_VSTMXCSR	= 8598,
    X86_VSUBPDYrm	= 8599,
    X86_VSUBPDYrr	= 8600,
    X86_VSUBPDZ128rm	= 8601,
    X86_VSUBPDZ128rmb	= 8602,
    X86_VSUBPDZ128rmbk	= 8603,
    X86_VSUBPDZ128rmbkz	= 8604,
    X86_VSUBPDZ128rmk	= 8605,
    X86_VSUBPDZ128rmkz	= 8606,
    X86_VSUBPDZ128rr	= 8607,
    X86_VSUBPDZ128rrk	= 8608,
    X86_VSUBPDZ128rrkz	= 8609,
    X86_VSUBPDZ256rm	= 8610,
    X86_VSUBPDZ256rmb	= 8611,
    X86_VSUBPDZ256rmbk	= 8612,
    X86_VSUBPDZ256rmbkz	= 8613,
    X86_VSUBPDZ256rmk	= 8614,
    X86_VSUBPDZ256rmkz	= 8615,
    X86_VSUBPDZ256rr	= 8616,
    X86_VSUBPDZ256rrk	= 8617,
    X86_VSUBPDZ256rrkz	= 8618,
    X86_VSUBPDZrb	= 8619,
    X86_VSUBPDZrbk	= 8620,
    X86_VSUBPDZrbkz	= 8621,
    X86_VSUBPDZrm	= 8622,
    X86_VSUBPDZrmb	= 8623,
    X86_VSUBPDZrmbk	= 8624,
    X86_VSUBPDZrmbkz	= 8625,
    X86_VSUBPDZrmk	= 8626,
    X86_VSUBPDZrmkz	= 8627,
    X86_VSUBPDZrr	= 8628,
    X86_VSUBPDZrrk	= 8629,
    X86_VSUBPDZrrkz	= 8630,
    X86_VSUBPDrm	= 8631,
    X86_VSUBPDrr	= 8632,
    X86_VSUBPSYrm	= 8633,
    X86_VSUBPSYrr	= 8634,
    X86_VSUBPSZ128rm	= 8635,
    X86_VSUBPSZ128rmb	= 8636,
    X86_VSUBPSZ128rmbk	= 8637,
    X86_VSUBPSZ128rmbkz	= 8638,
    X86_VSUBPSZ128rmk	= 8639,
    X86_VSUBPSZ128rmkz	= 8640,
    X86_VSUBPSZ128rr	= 8641,
    X86_VSUBPSZ128rrk	= 8642,
    X86_VSUBPSZ128rrkz	= 8643,
    X86_VSUBPSZ256rm	= 8644,
    X86_VSUBPSZ256rmb	= 8645,
    X86_VSUBPSZ256rmbk	= 8646,
    X86_VSUBPSZ256rmbkz	= 8647,
    X86_VSUBPSZ256rmk	= 8648,
    X86_VSUBPSZ256rmkz	= 8649,
    X86_VSUBPSZ256rr	= 8650,
    X86_VSUBPSZ256rrk	= 8651,
    X86_VSUBPSZ256rrkz	= 8652,
    X86_VSUBPSZrb	= 8653,
    X86_VSUBPSZrbk	= 8654,
    X86_VSUBPSZrbkz	= 8655,
    X86_VSUBPSZrm	= 8656,
    X86_VSUBPSZrmb	= 8657,
    X86_VSUBPSZrmbk	= 8658,
    X86_VSUBPSZrmbkz	= 8659,
    X86_VSUBPSZrmk	= 8660,
    X86_VSUBPSZrmkz	= 8661,
    X86_VSUBPSZrr	= 8662,
    X86_VSUBPSZrrk	= 8663,
    X86_VSUBPSZrrkz	= 8664,
    X86_VSUBPSrm	= 8665,
    X86_VSUBPSrr	= 8666,
    X86_VSUBSDZrm	= 8667,
    X86_VSUBSDZrm_Int	= 8668,
    X86_VSUBSDZrm_Intk	= 8669,
    X86_VSUBSDZrm_Intkz	= 8670,
    X86_VSUBSDZrr	= 8671,
    X86_VSUBSDZrr_Int	= 8672,
    X86_VSUBSDZrr_Intk	= 8673,
    X86_VSUBSDZrr_Intkz	= 8674,
    X86_VSUBSDZrrb	= 8675,
    X86_VSUBSDZrrbk	= 8676,
    X86_VSUBSDZrrbkz	= 8677,
    X86_VSUBSDrm	= 8678,
    X86_VSUBSDrm_Int	= 8679,
    X86_VSUBSDrr	= 8680,
    X86_VSUBSDrr_Int	= 8681,
    X86_VSUBSSZrm	= 8682,
    X86_VSUBSSZrm_Int	= 8683,
    X86_VSUBSSZrm_Intk	= 8684,
    X86_VSUBSSZrm_Intkz	= 8685,
    X86_VSUBSSZrr	= 8686,
    X86_VSUBSSZrr_Int	= 8687,
    X86_VSUBSSZrr_Intk	= 8688,
    X86_VSUBSSZrr_Intkz	= 8689,
    X86_VSUBSSZrrb	= 8690,
    X86_VSUBSSZrrbk	= 8691,
    X86_VSUBSSZrrbkz	= 8692,
    X86_VSUBSSrm	= 8693,
    X86_VSUBSSrm_Int	= 8694,
    X86_VSUBSSrr	= 8695,
    X86_VSUBSSrr_Int	= 8696,
    X86_VTESTPDYrm	= 8697,
    X86_VTESTPDYrr	= 8698,
    X86_VTESTPDrm	= 8699,
    X86_VTESTPDrr	= 8700,
    X86_VTESTPSYrm	= 8701,
    X86_VTESTPSYrr	= 8702,
    X86_VTESTPSrm	= 8703,
    X86_VTESTPSrr	= 8704,
    X86_VUCOMISDZrm	= 8705,
    X86_VUCOMISDZrr	= 8706,
    X86_VUCOMISDrm	= 8707,
    X86_VUCOMISDrr	= 8708,
    X86_VUCOMISSZrm	= 8709,
    X86_VUCOMISSZrr	= 8710,
    X86_VUCOMISSrm	= 8711,
    X86_VUCOMISSrr	= 8712,
    X86_VUNPCKHPDYrm	= 8713,
    X86_VUNPCKHPDYrr	= 8714,
    X86_VUNPCKHPDZrm	= 8715,
    X86_VUNPCKHPDZrr	= 8716,
    X86_VUNPCKHPDrm	= 8717,
    X86_VUNPCKHPDrr	= 8718,
    X86_VUNPCKHPSYrm	= 8719,
    X86_VUNPCKHPSYrr	= 8720,
    X86_VUNPCKHPSZrm	= 8721,
    X86_VUNPCKHPSZrr	= 8722,
    X86_VUNPCKHPSrm	= 8723,
    X86_VUNPCKHPSrr	= 8724,
    X86_VUNPCKLPDYrm	= 8725,
    X86_VUNPCKLPDYrr	= 8726,
    X86_VUNPCKLPDZrm	= 8727,
    X86_VUNPCKLPDZrr	= 8728,
    X86_VUNPCKLPDrm	= 8729,
    X86_VUNPCKLPDrr	= 8730,
    X86_VUNPCKLPSYrm	= 8731,
    X86_VUNPCKLPSYrr	= 8732,
    X86_VUNPCKLPSZrm	= 8733,
    X86_VUNPCKLPSZrr	= 8734,
    X86_VUNPCKLPSrm	= 8735,
    X86_VUNPCKLPSrr	= 8736,
    X86_VXORPDYrm	= 8737,
    X86_VXORPDYrr	= 8738,
    X86_VXORPDrm	= 8739,
    X86_VXORPDrr	= 8740,
    X86_VXORPSYrm	= 8741,
    X86_VXORPSYrr	= 8742,
    X86_VXORPSrm	= 8743,
    X86_VXORPSrr	= 8744,
    X86_VZEROALL	= 8745,
    X86_VZEROUPPER	= 8746,
    X86_V_SET0	= 8747,
    X86_V_SETALLONES	= 8748,
    X86_WAIT	= 8749,
    X86_WBINVD	= 8750,
    X86_WIN_ALLOCA	= 8751,
    X86_WIN_FTOL_32	= 8752,
    X86_WIN_FTOL_64	= 8753,
    X86_WRFSBASE	= 8754,
    X86_WRFSBASE64	= 8755,
    X86_WRGSBASE	= 8756,
    X86_WRGSBASE64	= 8757,
    X86_WRMSR	= 8758,
    X86_XABORT	= 8759,
    X86_XACQUIRE_PREFIX	= 8760,
    X86_XADD16rm	= 8761,
    X86_XADD16rr	= 8762,
    X86_XADD32rm	= 8763,
    X86_XADD32rr	= 8764,
    X86_XADD64rm	= 8765,
    X86_XADD64rr	= 8766,
    X86_XADD8rm	= 8767,
    X86_XADD8rr	= 8768,
    X86_XBEGIN	= 8769,
    X86_XBEGIN_2	= 8770,
    X86_XBEGIN_4	= 8771,
    X86_XCHG16ar	= 8772,
    X86_XCHG16rm	= 8773,
    X86_XCHG16rr	= 8774,
    X86_XCHG32ar	= 8775,
    X86_XCHG32ar64	= 8776,
    X86_XCHG32rm	= 8777,
    X86_XCHG32rr	= 8778,
    X86_XCHG64ar	= 8779,
    X86_XCHG64rm	= 8780,
    X86_XCHG64rr	= 8781,
    X86_XCHG8rm	= 8782,
    X86_XCHG8rr	= 8783,
    X86_XCH_F	= 8784,
    X86_XCRYPTCBC	= 8785,
    X86_XCRYPTCFB	= 8786,
    X86_XCRYPTCTR	= 8787,
    X86_XCRYPTECB	= 8788,
    X86_XCRYPTOFB	= 8789,
    X86_XEND	= 8790,
    X86_XGETBV	= 8791,
    X86_XLAT	= 8792,
    X86_XOR16i16	= 8793,
    X86_XOR16mi	= 8794,
    X86_XOR16mi8	= 8795,
    X86_XOR16mr	= 8796,
    X86_XOR16ri	= 8797,
    X86_XOR16ri8	= 8798,
    X86_XOR16rm	= 8799,
    X86_XOR16rr	= 8800,
    X86_XOR16rr_REV	= 8801,
    X86_XOR32i32	= 8802,
    X86_XOR32mi	= 8803,
    X86_XOR32mi8	= 8804,
    X86_XOR32mr	= 8805,
    X86_XOR32ri	= 8806,
    X86_XOR32ri8	= 8807,
    X86_XOR32rm	= 8808,
    X86_XOR32rr	= 8809,
    X86_XOR32rr_REV	= 8810,
    X86_XOR64i32	= 8811,
    X86_XOR64mi32	= 8812,
    X86_XOR64mi8	= 8813,
    X86_XOR64mr	= 8814,
    X86_XOR64ri32	= 8815,
    X86_XOR64ri8	= 8816,
    X86_XOR64rm	= 8817,
    X86_XOR64rr	= 8818,
    X86_XOR64rr_REV	= 8819,
    X86_XOR8i8	= 8820,
    X86_XOR8mi	= 8821,
    X86_XOR8mi8	= 8822,
    X86_XOR8mr	= 8823,
    X86_XOR8ri	= 8824,
    X86_XOR8ri8	= 8825,
    X86_XOR8rm	= 8826,
    X86_XOR8rr	= 8827,
    X86_XOR8rr_REV	= 8828,
    X86_XORPDrm	= 8829,
    X86_XORPDrr	= 8830,
    X86_XORPSrm	= 8831,
    X86_XORPSrr	= 8832,
    X86_XRELEASE_PREFIX	= 8833,
    X86_XRSTOR	= 8834,
    X86_XRSTOR64	= 8835,
    X86_XRSTORS	= 8836,
    X86_XRSTORS64	= 8837,
    X86_XSAVE	= 8838,
    X86_XSAVE64	= 8839,
    X86_XSAVEC	= 8840,
    X86_XSAVEC64	= 8841,
    X86_XSAVEOPT	= 8842,
    X86_XSAVEOPT64	= 8843,
    X86_XSAVES	= 8844,
    X86_XSAVES64	= 8845,
    X86_XSETBV	= 8846,
    X86_XSHA1	= 8847,
    X86_XSHA256	= 8848,
    X86_XSTORE	= 8849,
    X86_XTEST	= 8850,
    X86_fdisi8087_nop	= 8851,
    X86_feni8087_nop	= 8852,
    X86_UD0	= 8853,
    X86_ENDBR32 = 8854,
    X86_ENDBR64 = 8855,
    X86_INSTRUCTION_LIST_END = 8856
};

#endif // GET_INSTRINFO_ENUM


#ifdef GET_INSTRINFO_MC_DESC
#undef GET_INSTRINFO_MC_DESC

typedef struct x86_op_id_pair {
	uint16_t first;
	uint16_t second;
} x86_op_id_pair;

static const x86_op_id_pair x86_16_bit_eq_tbl[] = {
	{ 31, 30 },
	{ 32, 30 },
	{ 43, 34 },
	{ 44, 35 },
	{ 45, 36 },
	{ 46, 37 },
	{ 47, 38 },
	{ 48, 39 },
	{ 49, 40 },
	{ 50, 41 },
	{ 51, 42 },
	{ 52, 34 },
	{ 54, 36 },
	{ 55, 37 },
	{ 57, 39 },
	{ 58, 40 },
	{ 59, 41 },
	{ 60, 42 },
	{ 86, 74 },
	{ 87, 75 },
	{ 88, 76 },
	{ 89, 77 },
	{ 90, 78 },
	{ 91, 79 },
	{ 92, 80 },
	{ 93, 81 },
	{ 94, 82 },
	{ 95, 83 },
	{ 96, 84 },
	{ 97, 85 },
	{ 98, 74 },
	{ 100, 76 },
	{ 101, 77 },
	{ 104, 79 },
	{ 105, 80 },
	{ 106, 82 },
	{ 107, 83 },
	{ 108, 84 },
	{ 109, 85 },
	{ 138, 137 },
	{ 152, 149 },
	{ 153, 150 },
	{ 154, 151 },
	{ 185, 176 },
	{ 186, 177 },
	{ 187, 178 },
	{ 188, 179 },
	{ 189, 180 },
	{ 190, 181 },
	{ 191, 182 },
	{ 192, 183 },
	{ 193, 184 },
	{ 194, 176 },
	{ 196, 178 },
	{ 197, 179 },
	{ 199, 181 },
	{ 200, 182 },
	{ 201, 183 },
	{ 202, 184 },
	{ 286, 285 },
	{ 289, 287 },
	{ 290, 288 },
	{ 291, 287 },
	{ 292, 288 },
	{ 295, 293 },
	{ 296, 294 },
	{ 297, 293 },
	{ 298, 294 },
	{ 305, 301 },
	{ 306, 302 },
	{ 307, 303 },
	{ 308, 304 },
	{ 309, 301 },
	{ 310, 302 },
	{ 311, 303 },
	{ 312, 304 },
	{ 317, 313 },
	{ 318, 314 },
	{ 319, 315 },
	{ 320, 316 },
	{ 321, 313 },
	{ 322, 314 },
	{ 323, 315 },
	{ 324, 316 },
	{ 329, 325 },
	{ 330, 326 },
	{ 331, 327 },
	{ 332, 328 },
	{ 333, 325 },
	{ 334, 326 },
	{ 335, 327 },
	{ 336, 328 },
	{ 341, 337 },
	{ 342, 338 },
	{ 343, 339 },
	{ 344, 340 },
	{ 345, 337 },
	{ 346, 338 },
	{ 347, 339 },
	{ 348, 340 },
	{ 355, 353 },
	{ 356, 354 },
	{ 357, 353 },
	{ 359, 354 },
	{ 361, 360 },
	{ 371, 658 },
	{ 381, 379 },
	{ 382, 380 },
	{ 383, 379 },
	{ 384, 380 },
	{ 387, 385 },
	{ 388, 386 },
	{ 389, 385 },
	{ 390, 386 },
	{ 393, 391 },
	{ 394, 392 },
	{ 395, 391 },
	{ 396, 392 },
	{ 399, 397 },
	{ 400, 398 },
	{ 401, 397 },
	{ 402, 398 },
	{ 413, 411 },
	{ 414, 412 },
	{ 415, 411 },
	{ 416, 412 },
	{ 423, 421 },
	{ 424, 422 },
	{ 425, 421 },
	{ 426, 422 },
	{ 429, 427 },
	{ 430, 428 },
	{ 431, 427 },
	{ 432, 428 },
	{ 435, 433 },
	{ 436, 434 },
	{ 437, 433 },
	{ 438, 434 },
	{ 441, 439 },
	{ 442, 440 },
	{ 443, 439 },
	{ 444, 440 },
	{ 455, 453 },
	{ 456, 454 },
	{ 457, 453 },
	{ 458, 454 },
	{ 465, 463 },
	{ 466, 464 },
	{ 467, 463 },
	{ 468, 464 },
	{ 471, 469 },
	{ 472, 470 },
	{ 473, 469 },
	{ 474, 470 },
	{ 481, 479 },
	{ 482, 480 },
	{ 483, 479 },
	{ 484, 480 },
	{ 487, 485 },
	{ 488, 486 },
	{ 489, 485 },
	{ 490, 486 },
	{ 493, 491 },
	{ 494, 492 },
	{ 495, 491 },
	{ 496, 492 },
	{ 503, 501 },
	{ 504, 502 },
	{ 505, 501 },
	{ 506, 502 },
	{ 510, 509 },
	{ 533, 524 },
	{ 534, 525 },
	{ 535, 526 },
	{ 536, 527 },
	{ 537, 528 },
	{ 538, 529 },
	{ 539, 530 },
	{ 540, 531 },
	{ 541, 532 },
	{ 542, 524 },
	{ 544, 526 },
	{ 545, 527 },
	{ 547, 529 },
	{ 548, 530 },
	{ 549, 531 },
	{ 550, 532 },
	{ 573, 579 },
	{ 574, 579 },
	{ 583, 581 },
	{ 584, 582 },
	{ 585, 581 },
	{ 586, 582 },
	{ 605, 604 },
	{ 608, 607 },
	{ 666, 663 },
	{ 667, 664 },
	{ 668, 665 },
	{ 669, 663 },
	{ 670, 664 },
	{ 675, 673 },
	{ 676, 674 },
	{ 677, 673 },
	{ 678, 674 },
	{ 688, 687 },
	{ 699, 696 },
	{ 700, 697 },
	{ 701, 698 },
	{ 714, 713 },
	{ 728, 725 },
	{ 729, 726 },
	{ 730, 727 },
	{ 753, 751 },
	{ 754, 752 },
	{ 758, 756 },
	{ 759, 757 },
	{ 772, 771 },
	{ 774, 773 },
	{ 790, 789 },
	{ 791, 789 },
	{ 793, 792 },
	{ 794, 792 },
	{ 796, 795 },
	{ 797, 795 },
	{ 868, 866 },
	{ 869, 867 },
	{ 870, 866 },
	{ 871, 867 },
	{ 875, 874 },
	{ 876, 874 },
	{ 880, 877 },
	{ 881, 878 },
	{ 882, 879 },
	{ 883, 877 },
	{ 884, 878 },
	{ 885, 879 },
	{ 894, 886 },
	{ 895, 887 },
	{ 896, 888 },
	{ 897, 889 },
	{ 898, 890 },
	{ 899, 891 },
	{ 900, 892 },
	{ 901, 893 },
	{ 902, 886 },
	{ 903, 887 },
	{ 904, 888 },
	{ 906, 890 },
	{ 907, 891 },
	{ 909, 893 },
	{ 914, 912 },
	{ 915, 913 },
	{ 921, 918 },
	{ 922, 919 },
	{ 923, 920 },
	{ 924, 918 },
	{ 925, 919 },
	{ 933, 934 },
	{ 937, 936 },
	{ 950, 949 },
	{ 951, 949 },
	{ 953, 952 },
	{ 954, 952 },
	{ 958, 955 },
	{ 959, 956 },
	{ 960, 957 },
	{ 961, 955 },
	{ 962, 956 },
	{ 963, 957 },
	{ 965, 964 },
	{ 967, 966 },
	{ 968, 966 },
	{ 972, 969 },
	{ 973, 970 },
	{ 974, 971 },
	{ 975, 969 },
	{ 976, 970 },
	{ 977, 971 },
	{ 1114, 1112 },
	{ 1115, 1113 },
	{ 1116, 1112 },
	{ 1117, 1113 },
	{ 1147, 1148 },
	{ 1149, 1150 },
	{ 1161, 1166 },
	{ 1162, 1167 },
	{ 1163, 1168 },
	{ 1164, 1169 },
	{ 1165, 1170 },
	{ 1173, 1174 },
	{ 1177, 1182 },
	{ 1180, 1181 },
	{ 1189, 1190 },
	{ 1193, 1194 },
	{ 1198, 1199 },
	{ 1202, 1203 },
	{ 1207, 1205 },
	{ 1208, 1206 },
	{ 1209, 1205 },
	{ 1210, 1206 },
	{ 1213, 1211 },
	{ 1214, 1211 },
	{ 1220, 1219 },
	{ 1240, 1239 },
	{ 1242, 1239 },
	{ 1246, 1245 },
	{ 1249, 1248 },
	{ 1250, 1248 },
	{ 1252, 1251 },
	{ 1253, 1251 },
	{ 1255, 1254 },
	{ 1256, 1254 },
	{ 1258, 1257 },
	{ 1259, 1257 },
	{ 1267, 1264 },
	{ 1268, 1265 },
	{ 1269, 1266 },
	{ 1271, 1265 },
	{ 1272, 1266 },
	{ 1278, 1275 },
	{ 1279, 1276 },
	{ 1280, 1277 },
	{ 1282, 1276 },
	{ 1283, 1277 },
	{ 1287, 1286 },
	{ 1288, 1286 },
	{ 1291, 1290 },
	{ 1292, 1290 },
	{ 1297, 1294 },
	{ 1298, 1295 },
	{ 1299, 1296 },
	{ 1301, 1295 },
	{ 1302, 1296 },
	{ 1309, 1306 },
	{ 1310, 1307 },
	{ 1311, 1308 },
	{ 1313, 1307 },
	{ 1314, 1308 },
	{ 1320, 1317 },
	{ 1321, 1318 },
	{ 1322, 1319 },
	{ 1324, 1318 },
	{ 1325, 1319 },
	{ 1329, 1331 },
	{ 1330, 1331 },
	{ 1335, 1337 },
	{ 1336, 1337 },
	{ 1338, 1340 },
	{ 1339, 1340 },
	{ 1343, 1341 },
	{ 1344, 1342 },
	{ 1345, 1341 },
	{ 1346, 1342 },
	{ 1348, 1347 },
	{ 1349, 1347 },
	{ 1353, 1352 },
	{ 1354, 1352 },
	{ 1358, 1356 },
	{ 1359, 1357 },
	{ 1360, 1356 },
	{ 1361, 1357 },
	{ 1453, 1463 },
	{ 1454, 1464 },
	{ 1542, 1545 },
	{ 1543, 1546 },
	{ 1544, 1547 },
	{ 1557, 1560 },
	{ 1558, 1561 },
	{ 1559, 1562 },
	{ 1567, 1577 },
	{ 1568, 1578 },
	{ 1599, 1598 },
	{ 1600, 1598 },
	{ 1605, 1604 },
	{ 1606, 1604 },
	{ 1615, 1598 },
	{ 1616, 1598 },
	{ 1616, 1599 },
	{ 1616, 1615 },
	{ 1617, 1598 },
	{ 1617, 1600 },
	{ 1617, 1615 },
	{ 1620, 1601 },
	{ 1621, 1602 },
	{ 1622, 1603 },
	{ 1623, 1604 },
	{ 1624, 1604 },
	{ 1624, 1605 },
	{ 1624, 1623 },
	{ 1625, 1604 },
	{ 1625, 1606 },
	{ 1625, 1623 },
	{ 1629, 1607 },
	{ 1631, 1608 },
	{ 1632, 1609 },
	{ 1633, 1610 },
	{ 1634, 1611 },
	{ 1635, 1612 },
	{ 1636, 1613 },
	{ 1637, 1614 },
	{ 1638, 1598 },
	{ 1638, 1599 },
	{ 1639, 1598 },
	{ 1639, 1600 },
	{ 1643, 1602 },
	{ 1644, 1603 },
	{ 1645, 1604 },
	{ 1645, 1605 },
	{ 1646, 1604 },
	{ 1646, 1606 },
	{ 1649, 1607 },
	{ 1651, 1609 },
	{ 1652, 1610 },
	{ 1653, 1611 },
	{ 1654, 1612 },
	{ 1655, 1613 },
	{ 1656, 1614 },
	{ 1662, 1661 },
	{ 1663, 1661 },
	{ 1668, 1667 },
	{ 1669, 1667 },
	{ 1687, 1685 },
	{ 1688, 1686 },
	{ 1689, 1685 },
	{ 1690, 1686 },
	{ 1742, 1752 },
	{ 1745, 1752 },
	{ 1758, 1753 },
	{ 1760, 1754 },
	{ 1763, 1762 },
	{ 1765, 1753 },
	{ 1767, 1766 },
	{ 1768, 1754 },
	{ 1786, 1781 },
	{ 1788, 1782 },
	{ 1797, 1795 },
	{ 1798, 1796 },
	{ 1799, 1795 },
	{ 1800, 1796 },
	{ 1822, 1821 },
	{ 1836, 1833 },
	{ 1837, 1834 },
	{ 1838, 1835 },
	{ 1843, 1841 },
	{ 1844, 1842 },
	{ 1845, 1841 },
	{ 1846, 1842 },
	{ 1850, 1852 },
	{ 1854, 1856 },
	{ 1858, 1860 },
	{ 1862, 1864 },
	{ 1867, 1874 },
	{ 1868, 1875 },
	{ 1869, 1876 },
	{ 1870, 1877 },
	{ 1871, 1878 },
	{ 1872, 1879 },
	{ 1873, 1880 },
	{ 1883, 1881 },
	{ 1884, 1882 },
	{ 1885, 1881 },
	{ 1886, 1882 },
	{ 1898, 1889 },
	{ 1899, 1890 },
	{ 1900, 1891 },
	{ 1901, 1892 },
	{ 1903, 1893 },
	{ 1904, 1894 },
	{ 1905, 1895 },
	{ 1906, 1896 },
	{ 1907, 1897 },
	{ 1908, 1889 },
	{ 1910, 1891 },
	{ 1911, 1892 },
	{ 1913, 1894 },
	{ 1914, 1895 },
	{ 1915, 1896 },
	{ 1916, 1897 },
	{ 1932, 1930 },
	{ 1933, 1931 },
	{ 1937, 1938 },
	{ 1957, 1967 },
	{ 1958, 1968 },
	{ 1992, 1994 },
	{ 1993, 1995 },
	{ 2008, 2010 },
	{ 2009, 2011 },
	{ 2033, 2035 },
	{ 2133, 2135 },
	{ 2134, 2136 },
	{ 2145, 2147 },
	{ 2146, 2148 },
	{ 2174, 2171 },
	{ 2175, 2172 },
	{ 2176, 2173 },
	{ 2177, 2171 },
	{ 2178, 2172 },
	{ 2179, 2173 },
	{ 2181, 2180 },
	{ 2184, 2182 },
	{ 2185, 2183 },
	{ 2186, 2182 },
	{ 2187, 2183 },
	{ 2189, 2188 },
	{ 2191, 2190 },
	{ 2193, 2192 },
	{ 2194, 2192 },
	{ 2196, 2195 },
	{ 2197, 2195 },
	{ 2199, 2198 },
	{ 2200, 2198 },
	{ 2202, 2201 },
	{ 2231, 2234 },
	{ 2232, 2235 },
	{ 2233, 2236 },
	{ 2247, 2250 },
	{ 2248, 2251 },
	{ 2249, 2252 },
	{ 2257, 2267 },
	{ 2258, 2268 },
	{ 2293, 2289 },
	{ 2294, 2290 },
	{ 2295, 2291 },
	{ 2296, 2292 },
	{ 2298, 2297 },
	{ 2299, 2289 },
	{ 2300, 2290 },
	{ 2301, 2291 },
	{ 2302, 2292 },
	{ 2304, 2303 },
	{ 2306, 2305 },
	{ 2308, 2307 },
	{ 2310, 2309 },
	{ 2312, 2311 },
	{ 2313, 2311 },
	{ 2315, 2314 },
	{ 2316, 2314 },
	{ 2318, 2317 },
	{ 2319, 2317 },
	{ 2321, 2320 },
	{ 2323, 2322 },
	{ 2332, 2326 },
	{ 2333, 2327 },
	{ 2334, 2328 },
	{ 2335, 2329 },
	{ 2336, 2330 },
	{ 2337, 2331 },
	{ 2338, 2326 },
	{ 2339, 2327 },
	{ 2340, 2328 },
	{ 2341, 2329 },
	{ 2342, 2330 },
	{ 2343, 2331 },
	{ 2364, 2358 },
	{ 2365, 2359 },
	{ 2366, 2360 },
	{ 2367, 2361 },
	{ 2368, 2362 },
	{ 2369, 2363 },
	{ 2370, 2358 },
	{ 2371, 2359 },
	{ 2372, 2360 },
	{ 2373, 2361 },
	{ 2374, 2362 },
	{ 2375, 2363 },
	{ 2389, 2388 },
	{ 2390, 2388 },
	{ 2392, 2391 },
	{ 2393, 2391 },
	{ 2403, 2402 },
	{ 2404, 2402 },
	{ 2407, 2406 },
	{ 2408, 2406 },
	{ 2412, 2410 },
	{ 2413, 2411 },
	{ 2415, 2411 },
	{ 2429, 2431 },
	{ 2437, 2439 },
	{ 2440, 2442 },
	{ 2441, 2442 },
	{ 2443, 2445 },
	{ 2444, 2445 },
	{ 2453, 2447 },
	{ 2454, 2448 },
	{ 2455, 2449 },
	{ 2456, 2450 },
	{ 2457, 2451 },
	{ 2458, 2452 },
	{ 2459, 2447 },
	{ 2460, 2448 },
	{ 2461, 2449 },
	{ 2462, 2450 },
	{ 2463, 2451 },
	{ 2464, 2452 },
	{ 2477, 2471 },
	{ 2478, 2472 },
	{ 2479, 2473 },
	{ 2480, 2474 },
	{ 2481, 2475 },
	{ 2482, 2476 },
	{ 2483, 2471 },
	{ 2484, 2472 },
	{ 2485, 2473 },
	{ 2486, 2474 },
	{ 2487, 2475 },
	{ 2488, 2476 },
	{ 2525, 2519 },
	{ 2526, 2520 },
	{ 2527, 2521 },
	{ 2528, 2522 },
	{ 2529, 2523 },
	{ 2530, 2524 },
	{ 2531, 2519 },
	{ 2532, 2520 },
	{ 2533, 2521 },
	{ 2534, 2522 },
	{ 2535, 2523 },
	{ 2536, 2524 },
	{ 2550, 2544 },
	{ 2551, 2545 },
	{ 2552, 2546 },
	{ 2553, 2547 },
	{ 2554, 2548 },
	{ 2555, 2549 },
	{ 2556, 2544 },
	{ 2557, 2545 },
	{ 2558, 2546 },
	{ 2559, 2547 },
	{ 2560, 2548 },
	{ 2561, 2549 },
	{ 2581, 2572 },
	{ 2582, 2573 },
	{ 2583, 2574 },
	{ 2584, 2575 },
	{ 2585, 2576 },
	{ 2586, 2577 },
	{ 2587, 2578 },
	{ 2588, 2579 },
	{ 2589, 2580 },
	{ 2590, 2572 },
	{ 2592, 2574 },
	{ 2593, 2575 },
	{ 2595, 2577 },
	{ 2596, 2578 },
	{ 2597, 2579 },
	{ 2598, 2580 },
	{ 2609, 2611 },
	{ 2610, 2611 },
	{ 2629, 2628 },
	{ 2630, 2628 },
	{ 2660, 2659 },
	{ 2661, 2659 },
	{ 2682, 2676 },
	{ 2683, 2677 },
	{ 2684, 2678 },
	{ 2685, 2679 },
	{ 2686, 2680 },
	{ 2687, 2681 },
	{ 2688, 2676 },
	{ 2689, 2677 },
	{ 2690, 2678 },
	{ 2691, 2679 },
	{ 2692, 2680 },
	{ 2693, 2681 },
	{ 2704, 2700 },
	{ 2705, 2701 },
	{ 2706, 2702 },
	{ 2707, 2703 },
	{ 2708, 2700 },
	{ 2709, 2701 },
	{ 2710, 2702 },
	{ 2711, 2703 },
	{ 2722, 2716 },
	{ 2723, 2717 },
	{ 2724, 2718 },
	{ 2725, 2719 },
	{ 2726, 2720 },
	{ 2727, 2721 },
	{ 2728, 2716 },
	{ 2729, 2717 },
	{ 2730, 2718 },
	{ 2731, 2719 },
	{ 2732, 2720 },
	{ 2733, 2721 },
	{ 2744, 2740 },
	{ 2745, 2741 },
	{ 2746, 2742 },
	{ 2747, 2743 },
	{ 2748, 2740 },
	{ 2749, 2741 },
	{ 2750, 2742 },
	{ 2751, 2743 },
	{ 2761, 2760 },
	{ 2762, 2760 },
	{ 2770, 2769 },
	{ 2771, 2768 },
	{ 2772, 2769 },
	{ 2775, 2774 },
	{ 2776, 2774 },
	{ 2800, 2802 },
	{ 2801, 2802 },
	{ 2804, 2803 },
	{ 2805, 2803 },
	{ 2842, 2833 },
	{ 2843, 2834 },
	{ 2844, 2835 },
	{ 2845, 2836 },
	{ 2846, 2837 },
	{ 2847, 2838 },
	{ 2848, 2839 },
	{ 2849, 2840 },
	{ 2850, 2841 },
	{ 2851, 2833 },
	{ 2853, 2835 },
	{ 2854, 2836 },
	{ 2856, 2838 },
	{ 2857, 2839 },
	{ 2858, 2840 },
	{ 2859, 2841 },
	{ 2876, 2875 },
	{ 2887, 2884 },
	{ 2888, 2885 },
	{ 2889, 2886 },
	{ 2902, 2901 },
	{ 2916, 2913 },
	{ 2917, 2914 },
	{ 2918, 2915 },
	{ 2953, 2946 },
	{ 2954, 2947 },
	{ 2955, 2948 },
	{ 2956, 2949 },
	{ 2957, 2950 },
	{ 2958, 2951 },
	{ 2959, 2952 },
	{ 2960, 2946 },
	{ 2965, 2951 },
	{ 2966, 2952 },
	{ 2988, 2986 },
	{ 2989, 2987 },
	{ 2990, 2986 },
	{ 2991, 2987 },
	{ 5335, 5302 },
	{ 5336, 5303 },
	{ 5337, 5304 },
	{ 5338, 5305 },
	{ 5339, 5306 },
	{ 5340, 5307 },
	{ 5341, 5308 },
	{ 5342, 5309 },
	{ 5343, 5310 },
	{ 5344, 5311 },
	{ 5345, 5312 },
	{ 5346, 5313 },
	{ 5347, 5314 },
	{ 5348, 5315 },
	{ 5349, 5316 },
	{ 5350, 5317 },
	{ 5351, 5318 },
	{ 5352, 5319 },
	{ 5353, 5320 },
	{ 5354, 5321 },
	{ 5355, 5322 },
	{ 5356, 5323 },
	{ 5357, 5324 },
	{ 5358, 5325 },
	{ 5359, 5326 },
	{ 5360, 5327 },
	{ 5361, 5328 },
	{ 5362, 5329 },
	{ 5363, 5330 },
	{ 5364, 5331 },
	{ 5365, 5332 },
	{ 5366, 5333 },
	{ 5367, 5334 },
	{ 5368, 5302 },
	{ 5369, 5303 },
	{ 5370, 5304 },
	{ 5371, 5305 },
	{ 5372, 5306 },
	{ 5373, 5307 },
	{ 5374, 5308 },
	{ 5375, 5309 },
	{ 5376, 5310 },
	{ 5377, 5311 },
	{ 5378, 5312 },
	{ 5379, 5313 },
	{ 5380, 5314 },
	{ 5381, 5315 },
	{ 5382, 5316 },
	{ 5383, 5317 },
	{ 5384, 5318 },
	{ 5385, 5319 },
	{ 5386, 5320 },
	{ 5387, 5321 },
	{ 5388, 5322 },
	{ 5389, 5323 },
	{ 5390, 5324 },
	{ 5391, 5325 },
	{ 5392, 5326 },
	{ 5393, 5327 },
	{ 5394, 5328 },
	{ 5395, 5329 },
	{ 5396, 5330 },
	{ 5397, 5331 },
	{ 5398, 5332 },
	{ 5399, 5333 },
	{ 5400, 5334 },
	{ 5844, 5891 },
	{ 5845, 5892 },
	{ 5846, 5893 },
	{ 5850, 5894 },
	{ 5851, 5895 },
	{ 5852, 5896 },
	{ 5853, 5897 },
	{ 5854, 5898 },
	{ 5855, 5899 },
	{ 5859, 5900 },
	{ 5860, 5901 },
	{ 5861, 5902 },
	{ 5862, 5903 },
	{ 5863, 5904 },
	{ 5864, 5905 },
	{ 5868, 5906 },
	{ 5869, 5907 },
	{ 5870, 5908 },
	{ 5871, 5909 },
	{ 5872, 5910 },
	{ 5873, 5911 },
	{ 5874, 5912 },
	{ 6087, 6111 },
	{ 6090, 6112 },
	{ 6091, 6113 },
	{ 6092, 6114 },
	{ 6093, 6115 },
	{ 6094, 6116 },
	{ 6095, 6117 },
	{ 6098, 6118 },
	{ 6099, 6119 },
	{ 6100, 6120 },
	{ 6101, 6121 },
	{ 6102, 6122 },
	{ 6103, 6123 },
	{ 6106, 6124 },
	{ 6107, 6125 },
	{ 6108, 6126 },
	{ 6109, 6127 },
	{ 6110, 6128 },
	{ 6173, 6190 },
	{ 6174, 6191 },
	{ 6179, 6192 },
	{ 6180, 6193 },
	{ 6181, 6194 },
	{ 6182, 6195 },
	{ 6183, 6196 },
	{ 6184, 6197 },
	{ 6185, 6198 },
	{ 6186, 6199 },
	{ 6187, 6200 },
	{ 6188, 6201 },
	{ 6189, 6202 },
	{ 6309, 6331 },
	{ 6310, 6332 },
	{ 6311, 6333 },
	{ 6314, 6334 },
	{ 6315, 6335 },
	{ 6316, 6336 },
	{ 6317, 6337 },
	{ 6320, 6338 },
	{ 6321, 6339 },
	{ 6322, 6340 },
	{ 6323, 6341 },
	{ 6326, 6342 },
	{ 6327, 6343 },
	{ 6328, 6344 },
	{ 6329, 6345 },
	{ 6330, 6346 },
	{ 6393, 6415 },
	{ 6394, 6416 },
	{ 6395, 6417 },
	{ 6398, 6418 },
	{ 6399, 6419 },
	{ 6400, 6420 },
	{ 6401, 6421 },
	{ 6404, 6422 },
	{ 6405, 6423 },
	{ 6406, 6424 },
	{ 6407, 6425 },
	{ 6410, 6426 },
	{ 6411, 6427 },
	{ 6412, 6428 },
	{ 6413, 6429 },
	{ 6414, 6430 },
	{ 6439, 6595 },
	{ 6440, 6596 },
	{ 6445, 6597 },
	{ 6446, 6598 },
	{ 6447, 6599 },
	{ 6448, 6600 },
	{ 6449, 6601 },
	{ 6450, 6602 },
	{ 6451, 6603 },
	{ 6452, 6604 },
	{ 6457, 6605 },
	{ 6458, 6606 },
	{ 6459, 6607 },
	{ 6460, 6608 },
	{ 6461, 6609 },
	{ 6462, 6610 },
	{ 6463, 6611 },
	{ 6464, 6612 },
	{ 6469, 6613 },
	{ 6470, 6614 },
	{ 6471, 6615 },
	{ 6472, 6616 },
	{ 6473, 6617 },
	{ 6474, 6618 },
	{ 6535, 6571 },
	{ 6536, 6572 },
	{ 6541, 6573 },
	{ 6542, 6574 },
	{ 6543, 6575 },
	{ 6544, 6576 },
	{ 6545, 6577 },
	{ 6546, 6578 },
	{ 6547, 6579 },
	{ 6548, 6580 },
	{ 6553, 6581 },
	{ 6554, 6582 },
	{ 6555, 6583 },
	{ 6556, 6584 },
	{ 6557, 6585 },
	{ 6558, 6586 },
	{ 6559, 6587 },
	{ 6560, 6588 },
	{ 6565, 6589 },
	{ 6566, 6590 },
	{ 6567, 6591 },
	{ 6568, 6592 },
	{ 6569, 6593 },
	{ 6570, 6594 },
	{ 6645, 6665 },
	{ 6646, 6666 },
	{ 6647, 6667 },
	{ 6648, 6668 },
	{ 6657, 6661 },
	{ 6658, 6662 },
	{ 6659, 6663 },
	{ 6660, 6664 },
	{ 6823, 6825 },
	{ 6842, 6844 },
	{ 6843, 6845 },
	{ 6858, 6860 },
	{ 6859, 6861 },
	{ 7015, 7044 },
	{ 7019, 7045 },
	{ 7020, 7046 },
	{ 7021, 7047 },
	{ 7022, 7048 },
	{ 7023, 7049 },
	{ 7024, 7050 },
	{ 7028, 7051 },
	{ 7029, 7052 },
	{ 7030, 7053 },
	{ 7031, 7054 },
	{ 7032, 7055 },
	{ 7033, 7056 },
	{ 7037, 7057 },
	{ 7038, 7058 },
	{ 7039, 7059 },
	{ 7040, 7060 },
	{ 7041, 7061 },
	{ 7117, 7146 },
	{ 7121, 7147 },
	{ 7122, 7148 },
	{ 7123, 7149 },
	{ 7124, 7150 },
	{ 7125, 7151 },
	{ 7126, 7152 },
	{ 7130, 7153 },
	{ 7131, 7154 },
	{ 7132, 7155 },
	{ 7133, 7156 },
	{ 7134, 7157 },
	{ 7135, 7158 },
	{ 7139, 7159 },
	{ 7140, 7160 },
	{ 7141, 7161 },
	{ 7142, 7162 },
	{ 7143, 7163 },
	{ 7219, 7248 },
	{ 7223, 7249 },
	{ 7224, 7250 },
	{ 7225, 7251 },
	{ 7226, 7252 },
	{ 7227, 7253 },
	{ 7228, 7254 },
	{ 7232, 7255 },
	{ 7233, 7256 },
	{ 7234, 7257 },
	{ 7235, 7258 },
	{ 7236, 7259 },
	{ 7237, 7260 },
	{ 7241, 7261 },
	{ 7242, 7262 },
	{ 7243, 7263 },
	{ 7244, 7264 },
	{ 7245, 7265 },
	{ 7321, 7350 },
	{ 7325, 7351 },
	{ 7326, 7352 },
	{ 7327, 7353 },
	{ 7328, 7354 },
	{ 7329, 7355 },
	{ 7330, 7356 },
	{ 7334, 7357 },
	{ 7335, 7358 },
	{ 7336, 7359 },
	{ 7337, 7360 },
	{ 7338, 7361 },
	{ 7339, 7362 },
	{ 7343, 7363 },
	{ 7344, 7364 },
	{ 7345, 7365 },
	{ 7346, 7366 },
	{ 7347, 7367 },
	{ 7386, 7389 },
	{ 7387, 7390 },
	{ 7388, 7391 },
	{ 7444, 7454 },
	{ 7445, 7455 },
	{ 7452, 7456 },
	{ 7453, 7457 },
	{ 7523, 7533 },
	{ 7524, 7534 },
	{ 7531, 7535 },
	{ 7532, 7536 },
	{ 7623, 7652 },
	{ 7627, 7653 },
	{ 7628, 7654 },
	{ 7629, 7655 },
	{ 7630, 7656 },
	{ 7631, 7657 },
	{ 7632, 7658 },
	{ 7636, 7659 },
	{ 7637, 7660 },
	{ 7638, 7661 },
	{ 7639, 7662 },
	{ 7640, 7663 },
	{ 7641, 7664 },
	{ 7645, 7665 },
	{ 7646, 7666 },
	{ 7647, 7667 },
	{ 7648, 7668 },
	{ 7649, 7669 },
	{ 7756, 7761 },
	{ 7757, 7762 },
	{ 7758, 7763 },
	{ 7759, 7764 },
	{ 7760, 7765 },
	{ 7780, 7783 },
	{ 7781, 7784 },
	{ 7782, 7785 },
	{ 7792, 7795 },
	{ 7793, 7796 },
	{ 7794, 7797 },
	{ 7848, 7886 },
	{ 7849, 7887 },
	{ 7850, 7888 },
	{ 7863, 7889 },
	{ 7864, 7890 },
	{ 7865, 7891 },
	{ 7964, 8002 },
	{ 7965, 8003 },
	{ 7966, 8004 },
	{ 7979, 8005 },
	{ 7980, 8006 },
	{ 7981, 8007 },
	{ 8061, 8108 },
	{ 8062, 8109 },
	{ 8063, 8110 },
	{ 8067, 8111 },
	{ 8068, 8112 },
	{ 8069, 8113 },
	{ 8070, 8114 },
	{ 8071, 8115 },
	{ 8072, 8116 },
	{ 8076, 8117 },
	{ 8077, 8118 },
	{ 8078, 8119 },
	{ 8079, 8120 },
	{ 8080, 8121 },
	{ 8081, 8122 },
	{ 8085, 8123 },
	{ 8086, 8124 },
	{ 8087, 8125 },
	{ 8088, 8126 },
	{ 8089, 8127 },
	{ 8090, 8128 },
	{ 8091, 8129 },
	{ 8763, 8761 },
	{ 8764, 8762 },
	{ 8765, 8761 },
	{ 8766, 8762 },
	{ 8775, 8772 },
	{ 8777, 8773 },
	{ 8778, 8774 },
	{ 8779, 8772 },
	{ 8780, 8773 },
	{ 8781, 8774 },
	{ 8802, 8793 },
	{ 8803, 8794 },
	{ 8804, 8795 },
	{ 8805, 8796 },
	{ 8806, 8797 },
	{ 8807, 8798 },
	{ 8808, 8799 },
	{ 8809, 8800 },
	{ 8810, 8801 },
	{ 8811, 8793 },
	{ 8813, 8795 },
	{ 8814, 8796 },
	{ 8816, 8798 },
	{ 8817, 8799 },
	{ 8818, 8800 },
	{ 8819, 8801 },
};

static const uint16_t x86_16_bit_eq_lookup[] = {
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 1, 2, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 3, 4, 5, 6, 7, 
	8, 9, 10, 11, 12, 0, 13, 14, 0, 15, 16, 17, 
	18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 
	29, 30, 31, 0, 32, 33, 0, 0, 34, 35, 36, 37, 
	38, 39, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 40, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 41, 42, 43, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 44, 45, 46, 47, 48, 49, 50, 
	51, 52, 53, 0, 54, 55, 0, 56, 57, 58, 59, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 60, 0, 
	0, 61, 62, 63, 64, 0, 0, 65, 66, 67, 68, 0, 
	0, 0, 0, 0, 0, 69, 70, 71, 72, 73, 74, 75, 
	76, 0, 0, 0, 0, 77, 78, 79, 80, 81, 82, 83, 
	84, 0, 0, 0, 0, 85, 86, 87, 88, 89, 90, 91, 
	92, 0, 0, 0, 0, 93, 94, 95, 96, 97, 98, 99, 
	100, 0, 0, 0, 0, 0, 0, 101, 102, 103, 0, 104, 
	0, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 106, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 107, 108, 109, 
	110, 0, 0, 111, 112, 113, 114, 0, 0, 115, 116, 117, 
	118, 0, 0, 119, 120, 121, 122, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 123, 124, 125, 126, 0, 0, 0, 
	0, 0, 0, 127, 128, 129, 130, 0, 0, 131, 132, 133, 
	134, 0, 0, 135, 136, 137, 138, 0, 0, 139, 140, 141, 
	142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 143, 
	144, 145, 146, 0, 0, 0, 0, 0, 0, 147, 148, 149, 
	150, 0, 0, 151, 152, 153, 154, 0, 0, 0, 0, 0, 
	0, 155, 156, 157, 158, 0, 0, 159, 160, 161, 162, 0, 
	0, 163, 164, 165, 166, 0, 0, 0, 0, 0, 0, 167, 
	168, 169, 170, 0, 0, 0, 171, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 172, 173, 174, 175, 176, 177, 178, 
	179, 180, 181, 0, 182, 183, 0, 184, 185, 186, 187, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 188, 189, 0, 
	0, 0, 0, 0, 0, 0, 0, 190, 191, 192, 193, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 194, 0, 0, 195, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 196, 197, 198, 199, 200, 0, 
	0, 0, 0, 201, 202, 203, 204, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 205, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 206, 207, 208, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 209, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 210, 211, 212, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 213, 214, 0, 
	0, 0, 215, 216, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 217, 0, 218, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 219, 220, 
	0, 221, 222, 0, 223, 224, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 225, 226, 227, 228, 0, 0, 0, 229, 
	230, 0, 0, 0, 231, 232, 233, 234, 235, 236, 0, 0, 
	0, 0, 0, 0, 0, 0, 237, 238, 239, 240, 241, 242, 
	243, 244, 245, 246, 247, 0, 248, 249, 0, 250, 0, 0, 
	0, 0, 251, 252, 0, 0, 0, 0, 0, 253, 254, 255, 
	256, 257, 0, 0, 0, 0, 0, 0, 0, 258, 0, 0, 
	0, 259, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 260, 261, 0, 262, 263, 0, 0, 0, 264, 265, 
	266, 267, 268, 269, 0, 270, 0, 271, 272, 0, 0, 0, 
	273, 274, 275, 276, 277, 278, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 279, 280, 
	281, 282, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 283, 0, 284, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 285, 286, 287, 
	288, 289, 0, 0, 0, 0, 0, 0, 0, 290, 0, 0, 
	0, 291, 0, 0, 292, 0, 0, 0, 0, 0, 0, 0, 
	0, 293, 0, 0, 0, 294, 0, 0, 0, 0, 295, 0, 
	0, 0, 296, 0, 0, 0, 0, 297, 298, 299, 300, 0, 
	0, 301, 302, 0, 0, 0, 0, 0, 303, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 304, 0, 305, 0, 0, 0, 306, 0, 
	0, 307, 308, 0, 309, 310, 0, 311, 312, 0, 313, 314, 
	0, 0, 0, 0, 0, 0, 0, 315, 316, 317, 0, 318, 
	319, 0, 0, 0, 0, 0, 320, 321, 322, 0, 323, 324, 
	0, 0, 0, 325, 326, 0, 0, 327, 328, 0, 0, 0, 
	0, 329, 330, 331, 0, 332, 333, 0, 0, 0, 0, 0, 
	0, 334, 335, 336, 0, 337, 338, 0, 0, 0, 0, 0, 
	339, 340, 341, 0, 342, 343, 0, 0, 0, 344, 345, 0, 
	0, 0, 0, 346, 347, 0, 348, 349, 0, 0, 0, 350, 
	351, 352, 353, 0, 354, 355, 0, 0, 0, 356, 357, 0, 
	0, 0, 358, 359, 360, 361, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 362, 363, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 364, 365, 366, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 367, 368, 369, 
	0, 0, 0, 0, 0, 0, 0, 370, 371, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 372, 373, 0, 0, 0, 0, 374, 375, 0, 
	0, 0, 0, 0, 0, 0, 0, 376, 377, 380, 0, 0, 
	383, 384, 385, 386, 387, 390, 0, 0, 0, 393, 0, 394, 
	395, 396, 397, 398, 399, 400, 401, 403, 0, 0, 0, 405, 
	406, 407, 409, 0, 0, 411, 0, 412, 413, 414, 415, 416, 
	417, 0, 0, 0, 0, 0, 418, 419, 0, 0, 0, 0, 
	420, 421, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 422, 423, 424, 425, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 426, 0, 0, 427, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 428, 0, 429, 0, 0, 430, 
	0, 431, 0, 432, 433, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 434, 0, 
	435, 0, 0, 0, 0, 0, 0, 0, 0, 436, 437, 438, 
	439, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 440, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	441, 442, 443, 0, 0, 0, 0, 444, 445, 446, 447, 0, 
	0, 0, 448, 0, 0, 0, 449, 0, 0, 0, 450, 0, 
	0, 0, 451, 0, 0, 0, 0, 452, 453, 454, 455, 456, 
	457, 458, 0, 0, 0, 0, 0, 0, 0, 0, 0, 459, 
	460, 461, 462, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 463, 464, 465, 466, 0, 467, 468, 469, 470, 471, 
	472, 0, 473, 474, 0, 475, 476, 477, 478, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	479, 480, 0, 0, 0, 481, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 482, 483, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	484, 485, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 486, 487, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 488, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 489, 490, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 491, 492, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 493, 494, 495, 496, 497, 498, 0, 499, 0, 0, 
	500, 501, 502, 503, 0, 504, 0, 505, 0, 506, 507, 0, 
	508, 509, 0, 510, 511, 0, 512, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 513, 
	514, 515, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 516, 517, 518, 0, 0, 0, 0, 0, 0, 
	0, 519, 520, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 521, 522, 523, 524, 0, 525, 526, 527, 528, 529, 0, 
	530, 0, 531, 0, 532, 0, 533, 0, 534, 535, 0, 536, 
	537, 0, 538, 539, 0, 540, 0, 541, 0, 0, 0, 0, 
	0, 0, 0, 0, 542, 543, 544, 545, 546, 547, 548, 549, 
	550, 551, 552, 553, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 566, 567, 0, 568, 569, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 570, 571, 0, 0, 572, 573, 0, 0, 0, 
	574, 575, 0, 576, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 577, 0, 0, 0, 0, 0, 0, 
	0, 578, 0, 0, 579, 580, 0, 581, 582, 0, 0, 0, 
	0, 0, 0, 0, 0, 583, 584, 585, 586, 587, 588, 589, 
	590, 591, 592, 593, 594, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 595, 596, 597, 598, 599, 600, 601, 
	602, 603, 604, 605, 606, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 607, 608, 609, 610, 611, 612, 613, 
	614, 615, 616, 617, 618, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 619, 620, 621, 622, 623, 624, 
	625, 626, 627, 628, 629, 630, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 0, 
	641, 642, 0, 643, 644, 645, 646, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 647, 648, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 649, 650, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 651, 652, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 653, 654, 655, 656, 657, 658, 
	659, 660, 661, 662, 663, 664, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 665, 666, 667, 668, 669, 670, 671, 672, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 673, 674, 
	675, 676, 677, 678, 679, 680, 681, 682, 683, 684, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 685, 686, 687, 688, 
	689, 690, 691, 692, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 693, 694, 0, 0, 0, 0, 0, 0, 0, 695, 696, 
	697, 0, 0, 698, 699, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 700, 701, 0, 0, 702, 703, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 704, 705, 
	706, 707, 708, 709, 710, 711, 712, 713, 0, 714, 715, 0, 
	716, 717, 718, 719, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 720, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 721, 722, 723, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 724, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	725, 726, 727, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 728, 729, 730, 731, 732, 733, 734, 735, 0, 0, 0, 
	0, 736, 737, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	738, 739, 740, 741, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 742, 743, 744, 745, 746, 
	747, 748, 749, 750, 751, 752, 753, 754, 755, 756, 757, 758, 
	759, 760, 761, 762, 763, 764, 765, 766, 767, 768, 769, 770, 
	771, 772, 773, 774, 775, 776, 777, 778, 779, 780, 781, 782, 
	783, 784, 785, 786, 787, 788, 789, 790, 791, 792, 793, 794, 
	795, 796, 797, 798, 799, 800, 801, 802, 803, 804, 805, 806, 
	807, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	808, 809, 810, 0, 0, 0, 811, 812, 813, 814, 815, 816, 
	0, 0, 0, 817, 818, 819, 820, 821, 822, 0, 0, 0, 
	823, 824, 825, 826, 827, 828, 829, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 830, 0, 0, 831, 832, 833, 834, 835, 836, 
	0, 0, 837, 838, 839, 840, 841, 842, 0, 0, 843, 844, 
	845, 846, 847, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 848, 849, 0, 0, 0, 0, 850, 
	851, 852, 853, 854, 855, 856, 857, 858, 859, 860, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 861, 862, 863, 
	0, 0, 864, 865, 866, 867, 0, 0, 868, 869, 870, 871, 
	0, 0, 872, 873, 874, 875, 876, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 877, 878, 879, 
	0, 0, 880, 881, 882, 883, 0, 0, 884, 885, 886, 887, 
	0, 0, 888, 889, 890, 891, 892, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 893, 894, 0, 0, 0, 
	0, 895, 896, 897, 898, 899, 900, 901, 902, 0, 0, 0, 
	0, 903, 904, 905, 906, 907, 908, 909, 910, 0, 0, 0, 
	0, 911, 912, 913, 914, 915, 916, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 917, 918, 0, 0, 0, 
	0, 919, 920, 921, 922, 923, 924, 925, 926, 0, 0, 0, 
	0, 927, 928, 929, 930, 931, 932, 933, 934, 0, 0, 0, 
	0, 935, 936, 937, 938, 939, 940, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 941, 942, 943, 
	944, 0, 0, 0, 0, 0, 0, 0, 0, 945, 946, 947, 
	948, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 949, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 950, 951, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 952, 953, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 954, 0, 0, 0, 955, 
	956, 957, 958, 959, 960, 0, 0, 0, 961, 962, 963, 964, 
	965, 966, 0, 0, 0, 967, 968, 969, 970, 971, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 972, 0, 0, 0, 973, 974, 975, 976, 977, 978, 0, 
	0, 0, 979, 980, 981, 982, 983, 984, 0, 0, 0, 985, 
	986, 987, 988, 989, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 990, 0, 0, 0, 991, 
	992, 993, 994, 995, 996, 0, 0, 0, 997, 998, 999, 1000, 
	1001, 1002, 0, 0, 0, 1003, 1004, 1005, 1006, 1007, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 1008, 0, 0, 0, 1009, 1010, 1011, 1012, 1013, 1014, 0, 
	0, 0, 1015, 1016, 1017, 1018, 1019, 1020, 0, 0, 0, 1021, 
	1022, 1023, 1024, 1025, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 1026, 1027, 1028, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 1029, 1030, 0, 0, 0, 0, 0, 0, 
	1031, 1032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1033, 
	1034, 0, 0, 0, 0, 0, 0, 1035, 1036, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 1037, 0, 0, 0, 1038, 1039, 1040, 1041, 1042, 
	1043, 0, 0, 0, 1044, 1045, 1046, 1047, 1048, 1049, 0, 0, 
	0, 1050, 1051, 1052, 1053, 1054, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 1055, 1056, 1057, 1058, 1059, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 1060, 1061, 1062, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 1063, 1064, 1065, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	1066, 1067, 1068, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 1069, 1070, 1071, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 1072, 1073, 1074, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1075, 
	1076, 1077, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 1078, 1079, 1080, 
	0, 0, 0, 1081, 1082, 1083, 1084, 1085, 1086, 0, 0, 0, 
	1087, 1088, 1089, 1090, 1091, 1092, 0, 0, 0, 1093, 1094, 1095, 
	1096, 1097, 1098, 1099, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 1100, 1101, 1102, 1103, 0, 0, 0, 0, 0, 
	0, 0, 0, 1104, 0, 1105, 1106, 1107, 1108, 1109, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 1110, 1111, 1112, 1113, 1114, 1115, 
	1116, 1117, 1118, 1119, 0, 1120, 1121, 0, 1122, 1123, 1124, 1125, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
	0, 0, 0, 0, 0, 0, 0, 0, 0, };

static const bool is_64bit_insn[] = {
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	false,
	false,
	true,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	true,
	false,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	true,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	true,
	true,
	false,
	true,
	true,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	true,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	false,
	false,
	true,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	true,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	true,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	false,
	true,
	false,
	false,
	true,
	false,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	true,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	true,
	false,
	false,
	true,
	false,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	false,
	true,
	false,
	false,
	true,
	false,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	true,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	false,
	false,
	true,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	true,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	true,
	false,
	true,
	true,
	false,
	true,
	false,
	false,
	true,
	false,
	true,
	true,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	true,
	false,
	true,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	true,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	false,
	false,
	true,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	true,
	false,
	false,
	true,
	true,
	false,
	true,
	true,
	false,
	true,
	true,
	false,
	true,
	true,
	false,
	true,
	false,
	true,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	true,
	false,
	true,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	true,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	true,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	false,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
	true,
	false,
	true,
	false,
	true,
	false,
	true,
	false,
	true,
	false,
	true,
	false,
	false,
	false,
	false,
	false,
	false,
	false,
};

#endif // GET_INSTRINFO_MC_DESC
