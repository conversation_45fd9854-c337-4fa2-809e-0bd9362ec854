// For Capstone Engine. AUTO-GENERATED FILE, DO NOT EDIT
package capstone;

public class M680x_const {
	public static final int M680X_OPERAND_COUNT = 9;

	// M680X registers and special registers

	public static final int M680X_REG_INVALID = 0;
	public static final int M680X_REG_A = 1;
	public static final int M680X_REG_B = 2;
	public static final int M680X_REG_E = 3;
	public static final int M680X_REG_F = 4;
	public static final int M680X_REG_0 = 5;
	public static final int M680X_REG_D = 6;
	public static final int M680X_REG_W = 7;
	public static final int M680X_REG_CC = 8;
	public static final int M680X_REG_DP = 9;
	public static final int M680X_REG_MD = 10;
	public static final int M680X_REG_HX = 11;
	public static final int M680X_REG_H = 12;
	public static final int M680X_REG_X = 13;
	public static final int M680X_REG_Y = 14;
	public static final int M680X_REG_S = 15;
	public static final int M680X_REG_U = 16;
	public static final int M680X_REG_V = 17;
	public static final int M680X_REG_Q = 18;
	public static final int M680X_REG_PC = 19;
	public static final int M680X_REG_TMP2 = 20;
	public static final int M680X_REG_TMP3 = 21;
	public static final int M680X_REG_ENDING = 22;

	// Operand type for instruction's operands

	public static final int M680X_OP_INVALID = 0;
	public static final int M680X_OP_REGISTER = 1;
	public static final int M680X_OP_IMMEDIATE = 2;
	public static final int M680X_OP_INDEXED = 3;
	public static final int M680X_OP_EXTENDED = 4;
	public static final int M680X_OP_DIRECT = 5;
	public static final int M680X_OP_RELATIVE = 6;
	public static final int M680X_OP_CONSTANT = 7;

	// Supported bit values for mem.idx.offset_bits

	public static final int M680X_OFFSET_NONE = 0;
	public static final int M680X_OFFSET_BITS_5 = 5;
	public static final int M680X_OFFSET_BITS_8 = 8;
	public static final int M680X_OFFSET_BITS_9 = 9;
	public static final int M680X_OFFSET_BITS_16 = 16;

	// Supported bit flags for mem.idx.flags

	// These flags can be comined
	public static final int M680X_IDX_INDIRECT = 1;
	public static final int M680X_IDX_NO_COMMA = 2;
	public static final int M680X_IDX_POST_INC_DEC = 4;

	// Group of M680X instructions

	public static final int M680X_GRP_INVALID = 0;

	// Generic groups
	public static final int M680X_GRP_JUMP = 1;
	public static final int M680X_GRP_CALL = 2;
	public static final int M680X_GRP_RET = 3;
	public static final int M680X_GRP_INT = 4;
	public static final int M680X_GRP_IRET = 5;
	public static final int M680X_GRP_PRIV = 6;
	public static final int M680X_GRP_BRAREL = 7;

	// Architecture-specific groups
	public static final int M680X_GRP_ENDING = 8;

	// M680X instruction flags:
	public static final int M680X_FIRST_OP_IN_MNEM = 1;
	public static final int M680X_SECOND_OP_IN_MNEM = 2;

	// M680X instruction IDs

	public static final int M680X_INS_INVLD = 0;
	public static final int M680X_INS_ABA = 1;
	public static final int M680X_INS_ABX = 2;
	public static final int M680X_INS_ABY = 3;
	public static final int M680X_INS_ADC = 4;
	public static final int M680X_INS_ADCA = 5;
	public static final int M680X_INS_ADCB = 6;
	public static final int M680X_INS_ADCD = 7;
	public static final int M680X_INS_ADCR = 8;
	public static final int M680X_INS_ADD = 9;
	public static final int M680X_INS_ADDA = 10;
	public static final int M680X_INS_ADDB = 11;
	public static final int M680X_INS_ADDD = 12;
	public static final int M680X_INS_ADDE = 13;
	public static final int M680X_INS_ADDF = 14;
	public static final int M680X_INS_ADDR = 15;
	public static final int M680X_INS_ADDW = 16;
	public static final int M680X_INS_AIM = 17;
	public static final int M680X_INS_AIS = 18;
	public static final int M680X_INS_AIX = 19;
	public static final int M680X_INS_AND = 20;
	public static final int M680X_INS_ANDA = 21;
	public static final int M680X_INS_ANDB = 22;
	public static final int M680X_INS_ANDCC = 23;
	public static final int M680X_INS_ANDD = 24;
	public static final int M680X_INS_ANDR = 25;
	public static final int M680X_INS_ASL = 26;
	public static final int M680X_INS_ASLA = 27;
	public static final int M680X_INS_ASLB = 28;
	public static final int M680X_INS_ASLD = 29;
	public static final int M680X_INS_ASR = 30;
	public static final int M680X_INS_ASRA = 31;
	public static final int M680X_INS_ASRB = 32;
	public static final int M680X_INS_ASRD = 33;
	public static final int M680X_INS_ASRX = 34;
	public static final int M680X_INS_BAND = 35;
	public static final int M680X_INS_BCC = 36;
	public static final int M680X_INS_BCLR = 37;
	public static final int M680X_INS_BCS = 38;
	public static final int M680X_INS_BEOR = 39;
	public static final int M680X_INS_BEQ = 40;
	public static final int M680X_INS_BGE = 41;
	public static final int M680X_INS_BGND = 42;
	public static final int M680X_INS_BGT = 43;
	public static final int M680X_INS_BHCC = 44;
	public static final int M680X_INS_BHCS = 45;
	public static final int M680X_INS_BHI = 46;
	public static final int M680X_INS_BIAND = 47;
	public static final int M680X_INS_BIEOR = 48;
	public static final int M680X_INS_BIH = 49;
	public static final int M680X_INS_BIL = 50;
	public static final int M680X_INS_BIOR = 51;
	public static final int M680X_INS_BIT = 52;
	public static final int M680X_INS_BITA = 53;
	public static final int M680X_INS_BITB = 54;
	public static final int M680X_INS_BITD = 55;
	public static final int M680X_INS_BITMD = 56;
	public static final int M680X_INS_BLE = 57;
	public static final int M680X_INS_BLS = 58;
	public static final int M680X_INS_BLT = 59;
	public static final int M680X_INS_BMC = 60;
	public static final int M680X_INS_BMI = 61;
	public static final int M680X_INS_BMS = 62;
	public static final int M680X_INS_BNE = 63;
	public static final int M680X_INS_BOR = 64;
	public static final int M680X_INS_BPL = 65;
	public static final int M680X_INS_BRCLR = 66;
	public static final int M680X_INS_BRSET = 67;
	public static final int M680X_INS_BRA = 68;
	public static final int M680X_INS_BRN = 69;
	public static final int M680X_INS_BSET = 70;
	public static final int M680X_INS_BSR = 71;
	public static final int M680X_INS_BVC = 72;
	public static final int M680X_INS_BVS = 73;
	public static final int M680X_INS_CALL = 74;
	public static final int M680X_INS_CBA = 75;
	public static final int M680X_INS_CBEQ = 76;
	public static final int M680X_INS_CBEQA = 77;
	public static final int M680X_INS_CBEQX = 78;
	public static final int M680X_INS_CLC = 79;
	public static final int M680X_INS_CLI = 80;
	public static final int M680X_INS_CLR = 81;
	public static final int M680X_INS_CLRA = 82;
	public static final int M680X_INS_CLRB = 83;
	public static final int M680X_INS_CLRD = 84;
	public static final int M680X_INS_CLRE = 85;
	public static final int M680X_INS_CLRF = 86;
	public static final int M680X_INS_CLRH = 87;
	public static final int M680X_INS_CLRW = 88;
	public static final int M680X_INS_CLRX = 89;
	public static final int M680X_INS_CLV = 90;
	public static final int M680X_INS_CMP = 91;
	public static final int M680X_INS_CMPA = 92;
	public static final int M680X_INS_CMPB = 93;
	public static final int M680X_INS_CMPD = 94;
	public static final int M680X_INS_CMPE = 95;
	public static final int M680X_INS_CMPF = 96;
	public static final int M680X_INS_CMPR = 97;
	public static final int M680X_INS_CMPS = 98;
	public static final int M680X_INS_CMPU = 99;
	public static final int M680X_INS_CMPW = 100;
	public static final int M680X_INS_CMPX = 101;
	public static final int M680X_INS_CMPY = 102;
	public static final int M680X_INS_COM = 103;
	public static final int M680X_INS_COMA = 104;
	public static final int M680X_INS_COMB = 105;
	public static final int M680X_INS_COMD = 106;
	public static final int M680X_INS_COME = 107;
	public static final int M680X_INS_COMF = 108;
	public static final int M680X_INS_COMW = 109;
	public static final int M680X_INS_COMX = 110;
	public static final int M680X_INS_CPD = 111;
	public static final int M680X_INS_CPHX = 112;
	public static final int M680X_INS_CPS = 113;
	public static final int M680X_INS_CPX = 114;
	public static final int M680X_INS_CPY = 115;
	public static final int M680X_INS_CWAI = 116;
	public static final int M680X_INS_DAA = 117;
	public static final int M680X_INS_DBEQ = 118;
	public static final int M680X_INS_DBNE = 119;
	public static final int M680X_INS_DBNZ = 120;
	public static final int M680X_INS_DBNZA = 121;
	public static final int M680X_INS_DBNZX = 122;
	public static final int M680X_INS_DEC = 123;
	public static final int M680X_INS_DECA = 124;
	public static final int M680X_INS_DECB = 125;
	public static final int M680X_INS_DECD = 126;
	public static final int M680X_INS_DECE = 127;
	public static final int M680X_INS_DECF = 128;
	public static final int M680X_INS_DECW = 129;
	public static final int M680X_INS_DECX = 130;
	public static final int M680X_INS_DES = 131;
	public static final int M680X_INS_DEX = 132;
	public static final int M680X_INS_DEY = 133;
	public static final int M680X_INS_DIV = 134;
	public static final int M680X_INS_DIVD = 135;
	public static final int M680X_INS_DIVQ = 136;
	public static final int M680X_INS_EDIV = 137;
	public static final int M680X_INS_EDIVS = 138;
	public static final int M680X_INS_EIM = 139;
	public static final int M680X_INS_EMACS = 140;
	public static final int M680X_INS_EMAXD = 141;
	public static final int M680X_INS_EMAXM = 142;
	public static final int M680X_INS_EMIND = 143;
	public static final int M680X_INS_EMINM = 144;
	public static final int M680X_INS_EMUL = 145;
	public static final int M680X_INS_EMULS = 146;
	public static final int M680X_INS_EOR = 147;
	public static final int M680X_INS_EORA = 148;
	public static final int M680X_INS_EORB = 149;
	public static final int M680X_INS_EORD = 150;
	public static final int M680X_INS_EORR = 151;
	public static final int M680X_INS_ETBL = 152;
	public static final int M680X_INS_EXG = 153;
	public static final int M680X_INS_FDIV = 154;
	public static final int M680X_INS_IBEQ = 155;
	public static final int M680X_INS_IBNE = 156;
	public static final int M680X_INS_IDIV = 157;
	public static final int M680X_INS_IDIVS = 158;
	public static final int M680X_INS_ILLGL = 159;
	public static final int M680X_INS_INC = 160;
	public static final int M680X_INS_INCA = 161;
	public static final int M680X_INS_INCB = 162;
	public static final int M680X_INS_INCD = 163;
	public static final int M680X_INS_INCE = 164;
	public static final int M680X_INS_INCF = 165;
	public static final int M680X_INS_INCW = 166;
	public static final int M680X_INS_INCX = 167;
	public static final int M680X_INS_INS = 168;
	public static final int M680X_INS_INX = 169;
	public static final int M680X_INS_INY = 170;
	public static final int M680X_INS_JMP = 171;
	public static final int M680X_INS_JSR = 172;
	public static final int M680X_INS_LBCC = 173;
	public static final int M680X_INS_LBCS = 174;
	public static final int M680X_INS_LBEQ = 175;
	public static final int M680X_INS_LBGE = 176;
	public static final int M680X_INS_LBGT = 177;
	public static final int M680X_INS_LBHI = 178;
	public static final int M680X_INS_LBLE = 179;
	public static final int M680X_INS_LBLS = 180;
	public static final int M680X_INS_LBLT = 181;
	public static final int M680X_INS_LBMI = 182;
	public static final int M680X_INS_LBNE = 183;
	public static final int M680X_INS_LBPL = 184;
	public static final int M680X_INS_LBRA = 185;
	public static final int M680X_INS_LBRN = 186;
	public static final int M680X_INS_LBSR = 187;
	public static final int M680X_INS_LBVC = 188;
	public static final int M680X_INS_LBVS = 189;
	public static final int M680X_INS_LDA = 190;
	public static final int M680X_INS_LDAA = 191;
	public static final int M680X_INS_LDAB = 192;
	public static final int M680X_INS_LDB = 193;
	public static final int M680X_INS_LDBT = 194;
	public static final int M680X_INS_LDD = 195;
	public static final int M680X_INS_LDE = 196;
	public static final int M680X_INS_LDF = 197;
	public static final int M680X_INS_LDHX = 198;
	public static final int M680X_INS_LDMD = 199;
	public static final int M680X_INS_LDQ = 200;
	public static final int M680X_INS_LDS = 201;
	public static final int M680X_INS_LDU = 202;
	public static final int M680X_INS_LDW = 203;
	public static final int M680X_INS_LDX = 204;
	public static final int M680X_INS_LDY = 205;
	public static final int M680X_INS_LEAS = 206;
	public static final int M680X_INS_LEAU = 207;
	public static final int M680X_INS_LEAX = 208;
	public static final int M680X_INS_LEAY = 209;
	public static final int M680X_INS_LSL = 210;
	public static final int M680X_INS_LSLA = 211;
	public static final int M680X_INS_LSLB = 212;
	public static final int M680X_INS_LSLD = 213;
	public static final int M680X_INS_LSLX = 214;
	public static final int M680X_INS_LSR = 215;
	public static final int M680X_INS_LSRA = 216;
	public static final int M680X_INS_LSRB = 217;
	public static final int M680X_INS_LSRD = 218;
	public static final int M680X_INS_LSRW = 219;
	public static final int M680X_INS_LSRX = 220;
	public static final int M680X_INS_MAXA = 221;
	public static final int M680X_INS_MAXM = 222;
	public static final int M680X_INS_MEM = 223;
	public static final int M680X_INS_MINA = 224;
	public static final int M680X_INS_MINM = 225;
	public static final int M680X_INS_MOV = 226;
	public static final int M680X_INS_MOVB = 227;
	public static final int M680X_INS_MOVW = 228;
	public static final int M680X_INS_MUL = 229;
	public static final int M680X_INS_MULD = 230;
	public static final int M680X_INS_NEG = 231;
	public static final int M680X_INS_NEGA = 232;
	public static final int M680X_INS_NEGB = 233;
	public static final int M680X_INS_NEGD = 234;
	public static final int M680X_INS_NEGX = 235;
	public static final int M680X_INS_NOP = 236;
	public static final int M680X_INS_NSA = 237;
	public static final int M680X_INS_OIM = 238;
	public static final int M680X_INS_ORA = 239;
	public static final int M680X_INS_ORAA = 240;
	public static final int M680X_INS_ORAB = 241;
	public static final int M680X_INS_ORB = 242;
	public static final int M680X_INS_ORCC = 243;
	public static final int M680X_INS_ORD = 244;
	public static final int M680X_INS_ORR = 245;
	public static final int M680X_INS_PSHA = 246;
	public static final int M680X_INS_PSHB = 247;
	public static final int M680X_INS_PSHC = 248;
	public static final int M680X_INS_PSHD = 249;
	public static final int M680X_INS_PSHH = 250;
	public static final int M680X_INS_PSHS = 251;
	public static final int M680X_INS_PSHSW = 252;
	public static final int M680X_INS_PSHU = 253;
	public static final int M680X_INS_PSHUW = 254;
	public static final int M680X_INS_PSHX = 255;
	public static final int M680X_INS_PSHY = 256;
	public static final int M680X_INS_PULA = 257;
	public static final int M680X_INS_PULB = 258;
	public static final int M680X_INS_PULC = 259;
	public static final int M680X_INS_PULD = 260;
	public static final int M680X_INS_PULH = 261;
	public static final int M680X_INS_PULS = 262;
	public static final int M680X_INS_PULSW = 263;
	public static final int M680X_INS_PULU = 264;
	public static final int M680X_INS_PULUW = 265;
	public static final int M680X_INS_PULX = 266;
	public static final int M680X_INS_PULY = 267;
	public static final int M680X_INS_REV = 268;
	public static final int M680X_INS_REVW = 269;
	public static final int M680X_INS_ROL = 270;
	public static final int M680X_INS_ROLA = 271;
	public static final int M680X_INS_ROLB = 272;
	public static final int M680X_INS_ROLD = 273;
	public static final int M680X_INS_ROLW = 274;
	public static final int M680X_INS_ROLX = 275;
	public static final int M680X_INS_ROR = 276;
	public static final int M680X_INS_RORA = 277;
	public static final int M680X_INS_RORB = 278;
	public static final int M680X_INS_RORD = 279;
	public static final int M680X_INS_RORW = 280;
	public static final int M680X_INS_RORX = 281;
	public static final int M680X_INS_RSP = 282;
	public static final int M680X_INS_RTC = 283;
	public static final int M680X_INS_RTI = 284;
	public static final int M680X_INS_RTS = 285;
	public static final int M680X_INS_SBA = 286;
	public static final int M680X_INS_SBC = 287;
	public static final int M680X_INS_SBCA = 288;
	public static final int M680X_INS_SBCB = 289;
	public static final int M680X_INS_SBCD = 290;
	public static final int M680X_INS_SBCR = 291;
	public static final int M680X_INS_SEC = 292;
	public static final int M680X_INS_SEI = 293;
	public static final int M680X_INS_SEV = 294;
	public static final int M680X_INS_SEX = 295;
	public static final int M680X_INS_SEXW = 296;
	public static final int M680X_INS_SLP = 297;
	public static final int M680X_INS_STA = 298;
	public static final int M680X_INS_STAA = 299;
	public static final int M680X_INS_STAB = 300;
	public static final int M680X_INS_STB = 301;
	public static final int M680X_INS_STBT = 302;
	public static final int M680X_INS_STD = 303;
	public static final int M680X_INS_STE = 304;
	public static final int M680X_INS_STF = 305;
	public static final int M680X_INS_STOP = 306;
	public static final int M680X_INS_STHX = 307;
	public static final int M680X_INS_STQ = 308;
	public static final int M680X_INS_STS = 309;
	public static final int M680X_INS_STU = 310;
	public static final int M680X_INS_STW = 311;
	public static final int M680X_INS_STX = 312;
	public static final int M680X_INS_STY = 313;
	public static final int M680X_INS_SUB = 314;
	public static final int M680X_INS_SUBA = 315;
	public static final int M680X_INS_SUBB = 316;
	public static final int M680X_INS_SUBD = 317;
	public static final int M680X_INS_SUBE = 318;
	public static final int M680X_INS_SUBF = 319;
	public static final int M680X_INS_SUBR = 320;
	public static final int M680X_INS_SUBW = 321;
	public static final int M680X_INS_SWI = 322;
	public static final int M680X_INS_SWI2 = 323;
	public static final int M680X_INS_SWI3 = 324;
	public static final int M680X_INS_SYNC = 325;
	public static final int M680X_INS_TAB = 326;
	public static final int M680X_INS_TAP = 327;
	public static final int M680X_INS_TAX = 328;
	public static final int M680X_INS_TBA = 329;
	public static final int M680X_INS_TBEQ = 330;
	public static final int M680X_INS_TBL = 331;
	public static final int M680X_INS_TBNE = 332;
	public static final int M680X_INS_TEST = 333;
	public static final int M680X_INS_TFM = 334;
	public static final int M680X_INS_TFR = 335;
	public static final int M680X_INS_TIM = 336;
	public static final int M680X_INS_TPA = 337;
	public static final int M680X_INS_TST = 338;
	public static final int M680X_INS_TSTA = 339;
	public static final int M680X_INS_TSTB = 340;
	public static final int M680X_INS_TSTD = 341;
	public static final int M680X_INS_TSTE = 342;
	public static final int M680X_INS_TSTF = 343;
	public static final int M680X_INS_TSTW = 344;
	public static final int M680X_INS_TSTX = 345;
	public static final int M680X_INS_TSX = 346;
	public static final int M680X_INS_TSY = 347;
	public static final int M680X_INS_TXA = 348;
	public static final int M680X_INS_TXS = 349;
	public static final int M680X_INS_TYS = 350;
	public static final int M680X_INS_WAI = 351;
	public static final int M680X_INS_WAIT = 352;
	public static final int M680X_INS_WAV = 353;
	public static final int M680X_INS_WAVR = 354;
	public static final int M680X_INS_XGDX = 355;
	public static final int M680X_INS_XGDY = 356;
	public static final int M680X_INS_ENDING = 357;
}