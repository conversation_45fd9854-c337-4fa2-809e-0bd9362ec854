/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Target Register Enum Values                                                 *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine, http://www.capstone-engine.org */
/* By <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2015 */


#ifdef GET_REGINFO_ENUM
#undef GET_REGINFO_ENUM

enum {
  ARM_NoRegister,
  ARM_APSR = 1,
  ARM_APSR_NZCV = 2,
  ARM_CPSR = 3,
  ARM_FPEXC = 4,
  ARM_FPINST = 5,
  ARM_FPSCR = 6,
  ARM_FPSCR_NZCV = 7,
  ARM_FPSID = 8,
  ARM_ITSTATE = 9,
  ARM_LR = 10,
  ARM_PC = 11,
  ARM_SP = 12,
  ARM_SPSR = 13,
  ARM_D0 = 14,
  ARM_D1 = 15,
  ARM_D2 = 16,
  ARM_D3 = 17,
  ARM_D4 = 18,
  ARM_D5 = 19,
  ARM_D6 = 20,
  ARM_D7 = 21,
  ARM_D8 = 22,
  ARM_D9 = 23,
  ARM_D10 = 24,
  ARM_D11 = 25,
  ARM_D12 = 26,
  ARM_D13 = 27,
  ARM_D14 = 28,
  ARM_D15 = 29,
  ARM_D16 = 30,
  ARM_D17 = 31,
  ARM_D18 = 32,
  ARM_D19 = 33,
  ARM_D20 = 34,
  ARM_D21 = 35,
  ARM_D22 = 36,
  ARM_D23 = 37,
  ARM_D24 = 38,
  ARM_D25 = 39,
  ARM_D26 = 40,
  ARM_D27 = 41,
  ARM_D28 = 42,
  ARM_D29 = 43,
  ARM_D30 = 44,
  ARM_D31 = 45,
  ARM_FPINST2 = 46,
  ARM_MVFR0 = 47,
  ARM_MVFR1 = 48,
  ARM_MVFR2 = 49,
  ARM_Q0 = 50,
  ARM_Q1 = 51,
  ARM_Q2 = 52,
  ARM_Q3 = 53,
  ARM_Q4 = 54,
  ARM_Q5 = 55,
  ARM_Q6 = 56,
  ARM_Q7 = 57,
  ARM_Q8 = 58,
  ARM_Q9 = 59,
  ARM_Q10 = 60,
  ARM_Q11 = 61,
  ARM_Q12 = 62,
  ARM_Q13 = 63,
  ARM_Q14 = 64,
  ARM_Q15 = 65,
  ARM_R0 = 66,
  ARM_R1 = 67,
  ARM_R2 = 68,
  ARM_R3 = 69,
  ARM_R4 = 70,
  ARM_R5 = 71,
  ARM_R6 = 72,
  ARM_R7 = 73,
  ARM_R8 = 74,
  ARM_R9 = 75,
  ARM_R10 = 76,
  ARM_R11 = 77,
  ARM_R12 = 78,
  ARM_S0 = 79,
  ARM_S1 = 80,
  ARM_S2 = 81,
  ARM_S3 = 82,
  ARM_S4 = 83,
  ARM_S5 = 84,
  ARM_S6 = 85,
  ARM_S7 = 86,
  ARM_S8 = 87,
  ARM_S9 = 88,
  ARM_S10 = 89,
  ARM_S11 = 90,
  ARM_S12 = 91,
  ARM_S13 = 92,
  ARM_S14 = 93,
  ARM_S15 = 94,
  ARM_S16 = 95,
  ARM_S17 = 96,
  ARM_S18 = 97,
  ARM_S19 = 98,
  ARM_S20 = 99,
  ARM_S21 = 100,
  ARM_S22 = 101,
  ARM_S23 = 102,
  ARM_S24 = 103,
  ARM_S25 = 104,
  ARM_S26 = 105,
  ARM_S27 = 106,
  ARM_S28 = 107,
  ARM_S29 = 108,
  ARM_S30 = 109,
  ARM_S31 = 110,
  ARM_D0_D2 = 111,
  ARM_D1_D3 = 112,
  ARM_D2_D4 = 113,
  ARM_D3_D5 = 114,
  ARM_D4_D6 = 115,
  ARM_D5_D7 = 116,
  ARM_D6_D8 = 117,
  ARM_D7_D9 = 118,
  ARM_D8_D10 = 119,
  ARM_D9_D11 = 120,
  ARM_D10_D12 = 121,
  ARM_D11_D13 = 122,
  ARM_D12_D14 = 123,
  ARM_D13_D15 = 124,
  ARM_D14_D16 = 125,
  ARM_D15_D17 = 126,
  ARM_D16_D18 = 127,
  ARM_D17_D19 = 128,
  ARM_D18_D20 = 129,
  ARM_D19_D21 = 130,
  ARM_D20_D22 = 131,
  ARM_D21_D23 = 132,
  ARM_D22_D24 = 133,
  ARM_D23_D25 = 134,
  ARM_D24_D26 = 135,
  ARM_D25_D27 = 136,
  ARM_D26_D28 = 137,
  ARM_D27_D29 = 138,
  ARM_D28_D30 = 139,
  ARM_D29_D31 = 140,
  ARM_Q0_Q1 = 141,
  ARM_Q1_Q2 = 142,
  ARM_Q2_Q3 = 143,
  ARM_Q3_Q4 = 144,
  ARM_Q4_Q5 = 145,
  ARM_Q5_Q6 = 146,
  ARM_Q6_Q7 = 147,
  ARM_Q7_Q8 = 148,
  ARM_Q8_Q9 = 149,
  ARM_Q9_Q10 = 150,
  ARM_Q10_Q11 = 151,
  ARM_Q11_Q12 = 152,
  ARM_Q12_Q13 = 153,
  ARM_Q13_Q14 = 154,
  ARM_Q14_Q15 = 155,
  ARM_Q0_Q1_Q2_Q3 = 156,
  ARM_Q1_Q2_Q3_Q4 = 157,
  ARM_Q2_Q3_Q4_Q5 = 158,
  ARM_Q3_Q4_Q5_Q6 = 159,
  ARM_Q4_Q5_Q6_Q7 = 160,
  ARM_Q5_Q6_Q7_Q8 = 161,
  ARM_Q6_Q7_Q8_Q9 = 162,
  ARM_Q7_Q8_Q9_Q10 = 163,
  ARM_Q8_Q9_Q10_Q11 = 164,
  ARM_Q9_Q10_Q11_Q12 = 165,
  ARM_Q10_Q11_Q12_Q13 = 166,
  ARM_Q11_Q12_Q13_Q14 = 167,
  ARM_Q12_Q13_Q14_Q15 = 168,
  ARM_R12_SP = 169,
  ARM_R0_R1 = 170,
  ARM_R2_R3 = 171,
  ARM_R4_R5 = 172,
  ARM_R6_R7 = 173,
  ARM_R8_R9 = 174,
  ARM_R10_R11 = 175,
  ARM_D0_D1_D2 = 176,
  ARM_D1_D2_D3 = 177,
  ARM_D2_D3_D4 = 178,
  ARM_D3_D4_D5 = 179,
  ARM_D4_D5_D6 = 180,
  ARM_D5_D6_D7 = 181,
  ARM_D6_D7_D8 = 182,
  ARM_D7_D8_D9 = 183,
  ARM_D8_D9_D10 = 184,
  ARM_D9_D10_D11 = 185,
  ARM_D10_D11_D12 = 186,
  ARM_D11_D12_D13 = 187,
  ARM_D12_D13_D14 = 188,
  ARM_D13_D14_D15 = 189,
  ARM_D14_D15_D16 = 190,
  ARM_D15_D16_D17 = 191,
  ARM_D16_D17_D18 = 192,
  ARM_D17_D18_D19 = 193,
  ARM_D18_D19_D20 = 194,
  ARM_D19_D20_D21 = 195,
  ARM_D20_D21_D22 = 196,
  ARM_D21_D22_D23 = 197,
  ARM_D22_D23_D24 = 198,
  ARM_D23_D24_D25 = 199,
  ARM_D24_D25_D26 = 200,
  ARM_D25_D26_D27 = 201,
  ARM_D26_D27_D28 = 202,
  ARM_D27_D28_D29 = 203,
  ARM_D28_D29_D30 = 204,
  ARM_D29_D30_D31 = 205,
  ARM_D0_D2_D4 = 206,
  ARM_D1_D3_D5 = 207,
  ARM_D2_D4_D6 = 208,
  ARM_D3_D5_D7 = 209,
  ARM_D4_D6_D8 = 210,
  ARM_D5_D7_D9 = 211,
  ARM_D6_D8_D10 = 212,
  ARM_D7_D9_D11 = 213,
  ARM_D8_D10_D12 = 214,
  ARM_D9_D11_D13 = 215,
  ARM_D10_D12_D14 = 216,
  ARM_D11_D13_D15 = 217,
  ARM_D12_D14_D16 = 218,
  ARM_D13_D15_D17 = 219,
  ARM_D14_D16_D18 = 220,
  ARM_D15_D17_D19 = 221,
  ARM_D16_D18_D20 = 222,
  ARM_D17_D19_D21 = 223,
  ARM_D18_D20_D22 = 224,
  ARM_D19_D21_D23 = 225,
  ARM_D20_D22_D24 = 226,
  ARM_D21_D23_D25 = 227,
  ARM_D22_D24_D26 = 228,
  ARM_D23_D25_D27 = 229,
  ARM_D24_D26_D28 = 230,
  ARM_D25_D27_D29 = 231,
  ARM_D26_D28_D30 = 232,
  ARM_D27_D29_D31 = 233,
  ARM_D0_D2_D4_D6 = 234,
  ARM_D1_D3_D5_D7 = 235,
  ARM_D2_D4_D6_D8 = 236,
  ARM_D3_D5_D7_D9 = 237,
  ARM_D4_D6_D8_D10 = 238,
  ARM_D5_D7_D9_D11 = 239,
  ARM_D6_D8_D10_D12 = 240,
  ARM_D7_D9_D11_D13 = 241,
  ARM_D8_D10_D12_D14 = 242,
  ARM_D9_D11_D13_D15 = 243,
  ARM_D10_D12_D14_D16 = 244,
  ARM_D11_D13_D15_D17 = 245,
  ARM_D12_D14_D16_D18 = 246,
  ARM_D13_D15_D17_D19 = 247,
  ARM_D14_D16_D18_D20 = 248,
  ARM_D15_D17_D19_D21 = 249,
  ARM_D16_D18_D20_D22 = 250,
  ARM_D17_D19_D21_D23 = 251,
  ARM_D18_D20_D22_D24 = 252,
  ARM_D19_D21_D23_D25 = 253,
  ARM_D20_D22_D24_D26 = 254,
  ARM_D21_D23_D25_D27 = 255,
  ARM_D22_D24_D26_D28 = 256,
  ARM_D23_D25_D27_D29 = 257,
  ARM_D24_D26_D28_D30 = 258,
  ARM_D25_D27_D29_D31 = 259,
  ARM_D1_D2 = 260,
  ARM_D3_D4 = 261,
  ARM_D5_D6 = 262,
  ARM_D7_D8 = 263,
  ARM_D9_D10 = 264,
  ARM_D11_D12 = 265,
  ARM_D13_D14 = 266,
  ARM_D15_D16 = 267,
  ARM_D17_D18 = 268,
  ARM_D19_D20 = 269,
  ARM_D21_D22 = 270,
  ARM_D23_D24 = 271,
  ARM_D25_D26 = 272,
  ARM_D27_D28 = 273,
  ARM_D29_D30 = 274,
  ARM_D1_D2_D3_D4 = 275,
  ARM_D3_D4_D5_D6 = 276,
  ARM_D5_D6_D7_D8 = 277,
  ARM_D7_D8_D9_D10 = 278,
  ARM_D9_D10_D11_D12 = 279,
  ARM_D11_D12_D13_D14 = 280,
  ARM_D13_D14_D15_D16 = 281,
  ARM_D15_D16_D17_D18 = 282,
  ARM_D17_D18_D19_D20 = 283,
  ARM_D19_D20_D21_D22 = 284,
  ARM_D21_D22_D23_D24 = 285,
  ARM_D23_D24_D25_D26 = 286,
  ARM_D25_D26_D27_D28 = 287,
  ARM_D27_D28_D29_D30 = 288,
  ARM_NUM_TARGET_REGS 	// 289
};

// Register classes
enum {
  ARM_SPRRegClassID = 0,
  ARM_GPRRegClassID = 1,
  ARM_GPRwithAPSRRegClassID = 2,
  ARM_SPR_8RegClassID = 3,
  ARM_GPRnopcRegClassID = 4,
  ARM_rGPRRegClassID = 5,
  ARM_hGPRRegClassID = 6,
  ARM_tGPRRegClassID = 7,
  ARM_GPRnopc_and_hGPRRegClassID = 8,
  ARM_hGPR_and_rGPRRegClassID = 9,
  ARM_tcGPRRegClassID = 10,
  ARM_tGPR_and_tcGPRRegClassID = 11,
  ARM_CCRRegClassID = 12,
  ARM_GPRspRegClassID = 13,
  ARM_hGPR_and_tcGPRRegClassID = 14,
  ARM_DPRRegClassID = 15,
  ARM_DPR_VFP2RegClassID = 16,
  ARM_DPR_8RegClassID = 17,
  ARM_GPRPairRegClassID = 18,
  ARM_GPRPair_with_gsub_1_in_rGPRRegClassID = 19,
  ARM_GPRPair_with_gsub_0_in_tGPRRegClassID = 20,
  ARM_GPRPair_with_gsub_0_in_hGPRRegClassID = 21,
  ARM_GPRPair_with_gsub_0_in_tcGPRRegClassID = 22,
  ARM_GPRPair_with_gsub_1_in_hGPR_and_rGPRRegClassID = 23,
  ARM_GPRPair_with_gsub_1_in_tcGPRRegClassID = 24,
  ARM_GPRPair_with_gsub_1_in_GPRspRegClassID = 25,
  ARM_DPairSpcRegClassID = 26,
  ARM_DPairSpc_with_ssub_0RegClassID = 27,
  ARM_DPairSpc_with_dsub_2_then_ssub_0RegClassID = 28,
  ARM_DPairSpc_with_dsub_0_in_DPR_8RegClassID = 29,
  ARM_DPairSpc_with_dsub_2_in_DPR_8RegClassID = 30,
  ARM_DPairRegClassID = 31,
  ARM_DPair_with_ssub_0RegClassID = 32,
  ARM_QPRRegClassID = 33,
  ARM_DPair_with_ssub_2RegClassID = 34,
  ARM_DPair_with_dsub_0_in_DPR_8RegClassID = 35,
  ARM_QPR_VFP2RegClassID = 36,
  ARM_DPair_with_dsub_1_in_DPR_8RegClassID = 37,
  ARM_QPR_8RegClassID = 38,
  ARM_DTripleRegClassID = 39,
  ARM_DTripleSpcRegClassID = 40,
  ARM_DTripleSpc_with_ssub_0RegClassID = 41,
  ARM_DTriple_with_ssub_0RegClassID = 42,
  ARM_DTriple_with_dsub_1_dsub_2_in_QPRRegClassID = 43,
  ARM_DTriple_with_qsub_0_in_QPRRegClassID = 44,
  ARM_DTriple_with_ssub_2RegClassID = 45,
  ARM_DTripleSpc_with_dsub_2_then_ssub_0RegClassID = 46,
  ARM_DTriple_with_dsub_2_then_ssub_0RegClassID = 47,
  ARM_DTripleSpc_with_dsub_4_then_ssub_0RegClassID = 48,
  ARM_DTripleSpc_with_dsub_0_in_DPR_8RegClassID = 49,
  ARM_DTriple_with_dsub_0_in_DPR_8RegClassID = 50,
  ARM_DTriple_with_qsub_0_in_QPR_VFP2RegClassID = 51,
  ARM_DTriple_with_ssub_0_and_DTriple_with_dsub_1_dsub_2_in_QPRRegClassID = 52,
  ARM_DTriple_with_dsub_1_dsub_2_in_QPR_VFP2RegClassID = 53,
  ARM_DTriple_with_dsub_1_in_DPR_8RegClassID = 54,
  ARM_DTriple_with_dsub_2_then_ssub_0_and_DTriple_with_qsub_0_in_QPRRegClassID = 55,
  ARM_DTripleSpc_with_dsub_2_in_DPR_8RegClassID = 56,
  ARM_DTriple_with_dsub_2_in_DPR_8RegClassID = 57,
  ARM_DTripleSpc_with_dsub_4_in_DPR_8RegClassID = 58,
  ARM_DTriple_with_dsub_0_in_DPR_8_and_DTriple_with_dsub_1_dsub_2_in_QPRRegClassID = 59,
  ARM_DTriple_with_qsub_0_in_QPR_8RegClassID = 60,
  ARM_DTriple_with_dsub_1_dsub_2_in_QPR_8RegClassID = 61,
  ARM_DTriple_with_dsub_2_in_DPR_8_and_DTriple_with_qsub_0_in_QPRRegClassID = 62,
  ARM_DQuadSpcRegClassID = 63,
  ARM_DQuadSpc_with_ssub_0RegClassID = 64,
  ARM_DQuadSpc_with_dsub_2_then_ssub_0RegClassID = 65,
  ARM_DQuadSpc_with_dsub_4_then_ssub_0RegClassID = 66,
  ARM_DQuadSpc_with_dsub_0_in_DPR_8RegClassID = 67,
  ARM_DQuadSpc_with_dsub_2_in_DPR_8RegClassID = 68,
  ARM_DQuadSpc_with_dsub_4_in_DPR_8RegClassID = 69,
  ARM_DQuadRegClassID = 70,
  ARM_DQuad_with_ssub_0RegClassID = 71,
  ARM_DQuad_with_ssub_2RegClassID = 72,
  ARM_QQPRRegClassID = 73,
  ARM_DQuad_with_dsub_1_dsub_2_in_QPRRegClassID = 74,
  ARM_DQuad_with_dsub_2_then_ssub_0RegClassID = 75,
  ARM_DQuad_with_dsub_3_then_ssub_0RegClassID = 76,
  ARM_DQuad_with_dsub_0_in_DPR_8RegClassID = 77,
  ARM_DQuad_with_qsub_0_in_QPR_VFP2RegClassID = 78,
  ARM_DQuad_with_ssub_0_and_DQuad_with_dsub_1_dsub_2_in_QPRRegClassID = 79,
  ARM_DQuad_with_dsub_1_dsub_2_in_QPR_VFP2RegClassID = 80,
  ARM_DQuad_with_dsub_1_in_DPR_8RegClassID = 81,
  ARM_DQuad_with_qsub_1_in_QPR_VFP2RegClassID = 82,
  ARM_DQuad_with_dsub_2_in_DPR_8RegClassID = 83,
  ARM_DQuad_with_dsub_3_then_ssub_0_and_DQuad_with_dsub_1_dsub_2_in_QPRRegClassID = 84,
  ARM_DQuad_with_dsub_3_in_DPR_8RegClassID = 85,
  ARM_DQuad_with_dsub_0_in_DPR_8_and_DQuad_with_dsub_1_dsub_2_in_QPRRegClassID = 86,
  ARM_DQuad_with_qsub_0_in_QPR_8RegClassID = 87,
  ARM_DQuad_with_dsub_1_dsub_2_in_QPR_8RegClassID = 88,
  ARM_DQuad_with_qsub_1_in_QPR_8RegClassID = 89,
  ARM_DQuad_with_dsub_3_in_DPR_8_and_DQuad_with_dsub_1_dsub_2_in_QPRRegClassID = 90,
  ARM_QQQQPRRegClassID = 91,
  ARM_QQQQPR_with_ssub_0RegClassID = 92,
  ARM_QQQQPR_with_dsub_2_then_ssub_0RegClassID = 93,
  ARM_QQQQPR_with_dsub_5_then_ssub_0RegClassID = 94,
  ARM_QQQQPR_with_dsub_7_then_ssub_0RegClassID = 95,
  ARM_QQQQPR_with_dsub_0_in_DPR_8RegClassID = 96,
  ARM_QQQQPR_with_dsub_2_in_DPR_8RegClassID = 97,
  ARM_QQQQPR_with_dsub_4_in_DPR_8RegClassID = 98,
  ARM_QQQQPR_with_dsub_6_in_DPR_8RegClassID = 99,
};

// Subregister indices
enum {
  ARM_NoSubRegister,
  ARM_dsub_0,	// 1
  ARM_dsub_1,	// 2
  ARM_dsub_2,	// 3
  ARM_dsub_3,	// 4
  ARM_dsub_4,	// 5
  ARM_dsub_5,	// 6
  ARM_dsub_6,	// 7
  ARM_dsub_7,	// 8
  ARM_gsub_0,	// 9
  ARM_gsub_1,	// 10
  ARM_qqsub_0,	// 11
  ARM_qqsub_1,	// 12
  ARM_qsub_0,	// 13
  ARM_qsub_1,	// 14
  ARM_qsub_2,	// 15
  ARM_qsub_3,	// 16
  ARM_ssub_0,	// 17
  ARM_ssub_1,	// 18
  ARM_ssub_2,	// 19
  ARM_ssub_3,	// 20
  ARM_dsub_2_then_ssub_0,	// 21
  ARM_dsub_2_then_ssub_1,	// 22
  ARM_dsub_3_then_ssub_0,	// 23
  ARM_dsub_3_then_ssub_1,	// 24
  ARM_dsub_7_then_ssub_0,	// 25
  ARM_dsub_7_then_ssub_1,	// 26
  ARM_dsub_6_then_ssub_0,	// 27
  ARM_dsub_6_then_ssub_1,	// 28
  ARM_dsub_5_then_ssub_0,	// 29
  ARM_dsub_5_then_ssub_1,	// 30
  ARM_dsub_4_then_ssub_0,	// 31
  ARM_dsub_4_then_ssub_1,	// 32
  ARM_dsub_0_dsub_2,	// 33
  ARM_dsub_0_dsub_1_dsub_2,	// 34
  ARM_dsub_1_dsub_3,	// 35
  ARM_dsub_1_dsub_2_dsub_3,	// 36
  ARM_dsub_1_dsub_2,	// 37
  ARM_dsub_0_dsub_2_dsub_4,	// 38
  ARM_dsub_0_dsub_2_dsub_4_dsub_6,	// 39
  ARM_dsub_1_dsub_3_dsub_5,	// 40
  ARM_dsub_1_dsub_3_dsub_5_dsub_7,	// 41
  ARM_dsub_1_dsub_2_dsub_3_dsub_4,	// 42
  ARM_dsub_2_dsub_4,	// 43
  ARM_dsub_2_dsub_3_dsub_4,	// 44
  ARM_dsub_2_dsub_4_dsub_6,	// 45
  ARM_dsub_3_dsub_5,	// 46
  ARM_dsub_3_dsub_4_dsub_5,	// 47
  ARM_dsub_3_dsub_5_dsub_7,	// 48
  ARM_dsub_3_dsub_4,	// 49
  ARM_dsub_3_dsub_4_dsub_5_dsub_6,	// 50
  ARM_dsub_4_dsub_6,	// 51
  ARM_dsub_4_dsub_5_dsub_6,	// 52
  ARM_dsub_5_dsub_7,	// 53
  ARM_dsub_5_dsub_6_dsub_7,	// 54
  ARM_dsub_5_dsub_6,	// 55
  ARM_qsub_1_qsub_2,	// 56
  ARM_NUM_TARGET_SUBREGS
};

#endif // GET_REGINFO_ENUM

/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*MC Register Information                                                     *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine, http://www.capstone-engine.org */
/* By Nguyen Anh Quynh <<EMAIL>>, 2013-2015 */


#ifdef GET_REGINFO_MC_DESC
#undef GET_REGINFO_MC_DESC

static const MCPhysReg ARMRegDiffLists[] = {
  /* 0 */ 64924, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,
  /* 17 */ 32, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,
  /* 32 */ 36, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,
  /* 45 */ 40, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,
  /* 56 */ 64450, 1, 1, 1, 1, 1, 1, 1, 0,
  /* 65 */ 64984, 1, 1, 1, 1, 1, 1, 1, 0,
  /* 74 */ 65252, 1, 1, 1, 1, 1, 1, 1, 0,
  /* 83 */ 38, 1, 1, 1, 1, 1, 1, 0,
  /* 91 */ 40, 1, 1, 1, 1, 1, 0,
  /* 98 */ 65196, 1, 1, 1, 1, 1, 0,
  /* 105 */ 40, 1, 1, 1, 1, 0,
  /* 111 */ 42, 1, 1, 1, 1, 0,
  /* 117 */ 42, 1, 1, 1, 0,
  /* 122 */ 64510, 1, 1, 1, 0,
  /* 127 */ 65015, 1, 1, 1, 0,
  /* 132 */ 65282, 1, 1, 1, 0,
  /* 137 */ 65348, 1, 1, 1, 0,
  /* 142 */ 13, 1, 1, 0,
  /* 146 */ 42, 1, 1, 0,
  /* 150 */ 65388, 1, 1, 0,
  /* 154 */ 137, 65489, 48, 65489, 12, 121, 65416, 1, 1, 0,
  /* 164 */ 136, 65490, 47, 65490, 12, 121, 65416, 1, 1, 0,
  /* 174 */ 135, 65491, 46, 65491, 12, 121, 65416, 1, 1, 0,
  /* 184 */ 134, 65492, 45, 65492, 12, 121, 65416, 1, 1, 0,
  /* 194 */ 133, 65493, 44, 65493, 12, 121, 65416, 1, 1, 0,
  /* 204 */ 132, 65494, 43, 65494, 12, 121, 65416, 1, 1, 0,
  /* 214 */ 131, 65495, 42, 65495, 12, 121, 65416, 1, 1, 0,
  /* 224 */ 130, 65496, 41, 65496, 12, 121, 65416, 1, 1, 0,
  /* 234 */ 129, 65497, 40, 65497, 12, 121, 65416, 1, 1, 0,
  /* 244 */ 128, 65498, 39, 65498, 12, 121, 65416, 1, 1, 0,
  /* 254 */ 65489, 133, 65416, 1, 1, 0,
  /* 260 */ 65490, 133, 65416, 1, 1, 0,
  /* 266 */ 65491, 133, 65416, 1, 1, 0,
  /* 272 */ 65492, 133, 65416, 1, 1, 0,
  /* 278 */ 65493, 133, 65416, 1, 1, 0,
  /* 284 */ 65494, 133, 65416, 1, 1, 0,
  /* 290 */ 65495, 133, 65416, 1, 1, 0,
  /* 296 */ 65496, 133, 65416, 1, 1, 0,
  /* 302 */ 65497, 133, 65416, 1, 1, 0,
  /* 308 */ 65498, 133, 65416, 1, 1, 0,
  /* 314 */ 127, 65499, 38, 65499, 133, 65416, 1, 1, 0,
  /* 323 */ 65080, 1, 3, 1, 3, 1, 3, 1, 0,
  /* 332 */ 65136, 1, 3, 1, 3, 1, 0,
  /* 339 */ 65326, 1, 3, 1, 0,
  /* 344 */ 13, 1, 0,
  /* 347 */ 14, 1, 0,
  /* 350 */ 65, 1, 0,
  /* 353 */ 65500, 65, 1, 65471, 66, 1, 0,
  /* 360 */ 65291, 66, 1, 65470, 67, 1, 0,
  /* 367 */ 65439, 65, 1, 65472, 67, 1, 0,
  /* 374 */ 65501, 67, 1, 65469, 68, 1, 0,
  /* 381 */ 65439, 66, 1, 65471, 68, 1, 0,
  /* 388 */ 65292, 68, 1, 65468, 69, 1, 0,
  /* 395 */ 65439, 67, 1, 65470, 69, 1, 0,
  /* 402 */ 65502, 69, 1, 65467, 70, 1, 0,
  /* 409 */ 65439, 68, 1, 65469, 70, 1, 0,
  /* 416 */ 65293, 70, 1, 65466, 71, 1, 0,
  /* 423 */ 65439, 69, 1, 65468, 71, 1, 0,
  /* 430 */ 65503, 71, 1, 65465, 72, 1, 0,
  /* 437 */ 65439, 70, 1, 65467, 72, 1, 0,
  /* 444 */ 65294, 72, 1, 65464, 73, 1, 0,
  /* 451 */ 65439, 71, 1, 65466, 73, 1, 0,
  /* 458 */ 65504, 73, 1, 65463, 74, 1, 0,
  /* 465 */ 65439, 72, 1, 65465, 74, 1, 0,
  /* 472 */ 65295, 74, 1, 65462, 75, 1, 0,
  /* 479 */ 65439, 73, 1, 65464, 75, 1, 0,
  /* 486 */ 65505, 75, 1, 65461, 76, 1, 0,
  /* 493 */ 65439, 74, 1, 65463, 76, 1, 0,
  /* 500 */ 65296, 76, 1, 65460, 77, 1, 0,
  /* 507 */ 65439, 75, 1, 65462, 77, 1, 0,
  /* 514 */ 65506, 77, 1, 65459, 78, 1, 0,
  /* 521 */ 65439, 76, 1, 65461, 78, 1, 0,
  /* 528 */ 65297, 78, 1, 65458, 79, 1, 0,
  /* 535 */ 65439, 77, 1, 65460, 79, 1, 0,
  /* 542 */ 65507, 79, 1, 65457, 80, 1, 0,
  /* 549 */ 65439, 78, 1, 65459, 80, 1, 0,
  /* 556 */ 65045, 1, 0,
  /* 559 */ 65260, 1, 0,
  /* 562 */ 65299, 1, 0,
  /* 565 */ 65300, 1, 0,
  /* 568 */ 65301, 1, 0,
  /* 571 */ 65302, 1, 0,
  /* 574 */ 65303, 1, 0,
  /* 577 */ 65304, 1, 0,
  /* 580 */ 65305, 1, 0,
  /* 583 */ 65453, 1, 65499, 133, 1, 65416, 1, 0,
  /* 591 */ 138, 65488, 49, 65488, 12, 121, 65416, 1, 0,
  /* 600 */ 65488, 13, 121, 65416, 1, 0,
  /* 606 */ 65489, 13, 121, 65416, 1, 0,
  /* 612 */ 65490, 13, 121, 65416, 1, 0,
  /* 618 */ 65491, 13, 121, 65416, 1, 0,
  /* 624 */ 65492, 13, 121, 65416, 1, 0,
  /* 630 */ 65493, 13, 121, 65416, 1, 0,
  /* 636 */ 65494, 13, 121, 65416, 1, 0,
  /* 642 */ 65495, 13, 121, 65416, 1, 0,
  /* 648 */ 65496, 13, 121, 65416, 1, 0,
  /* 654 */ 65497, 13, 121, 65416, 1, 0,
  /* 660 */ 65498, 13, 121, 65416, 1, 0,
  /* 666 */ 65464, 1, 65488, 133, 65416, 121, 65416, 1, 0,
  /* 675 */ 65463, 1, 65489, 133, 65416, 121, 65416, 1, 0,
  /* 684 */ 65462, 1, 65490, 133, 65416, 121, 65416, 1, 0,
  /* 693 */ 65461, 1, 65491, 133, 65416, 121, 65416, 1, 0,
  /* 702 */ 65460, 1, 65492, 133, 65416, 121, 65416, 1, 0,
  /* 711 */ 65459, 1, 65493, 133, 65416, 121, 65416, 1, 0,
  /* 720 */ 65458, 1, 65494, 133, 65416, 121, 65416, 1, 0,
  /* 729 */ 65457, 1, 65495, 133, 65416, 121, 65416, 1, 0,
  /* 738 */ 65456, 1, 65496, 133, 65416, 121, 65416, 1, 0,
  /* 747 */ 65455, 1, 65497, 133, 65416, 121, 65416, 1, 0,
  /* 756 */ 65454, 1, 65498, 133, 65416, 121, 65416, 1, 0,
  /* 765 */ 65488, 133, 65416, 1, 0,
  /* 770 */ 65499, 134, 65416, 1, 0,
  /* 775 */ 126, 65500, 37, 65500, 133, 65417, 1, 0,
  /* 783 */ 65432, 1, 0,
  /* 786 */ 65433, 1, 0,
  /* 789 */ 65434, 1, 0,
  /* 792 */ 65435, 1, 0,
  /* 795 */ 65436, 1, 0,
  /* 798 */ 65437, 1, 0,
  /* 801 */ 65464, 1, 0,
  /* 804 */ 65508, 1, 0,
  /* 807 */ 65509, 1, 0,
  /* 810 */ 65510, 1, 0,
  /* 813 */ 65511, 1, 0,
  /* 816 */ 65512, 1, 0,
  /* 819 */ 65513, 1, 0,
  /* 822 */ 65514, 1, 0,
  /* 825 */ 65515, 1, 0,
  /* 828 */ 65520, 1, 0,
  /* 831 */ 65080, 1, 3, 1, 3, 1, 2, 0,
  /* 839 */ 65136, 1, 3, 1, 2, 0,
  /* 845 */ 65326, 1, 2, 0,
  /* 849 */ 65080, 1, 3, 1, 2, 2, 0,
  /* 856 */ 65136, 1, 2, 2, 0,
  /* 861 */ 65080, 1, 2, 2, 2, 0,
  /* 867 */ 65330, 2, 2, 2, 0,
  /* 872 */ 65080, 1, 3, 2, 2, 0,
  /* 878 */ 65358, 2, 2, 0,
  /* 882 */ 65080, 1, 3, 1, 3, 2, 0,
  /* 889 */ 65136, 1, 3, 2, 0,
  /* 894 */ 65344, 76, 1, 65461, 78, 1, 65459, 80, 1, 12, 2, 0,
  /* 906 */ 65344, 75, 1, 65462, 77, 1, 65460, 79, 1, 13, 2, 0,
  /* 918 */ 65344, 74, 1, 65463, 76, 1, 65461, 78, 1, 14, 2, 0,
  /* 930 */ 65344, 73, 1, 65464, 75, 1, 65462, 77, 1, 15, 2, 0,
  /* 942 */ 65344, 72, 1, 65465, 74, 1, 65463, 76, 1, 16, 2, 0,
  /* 954 */ 65344, 71, 1, 65466, 73, 1, 65464, 75, 1, 17, 2, 0,
  /* 966 */ 65344, 70, 1, 65467, 72, 1, 65465, 74, 1, 18, 2, 0,
  /* 978 */ 65344, 69, 1, 65468, 71, 1, 65466, 73, 1, 19, 2, 0,
  /* 990 */ 65344, 68, 1, 65469, 70, 1, 65467, 72, 1, 20, 2, 0,
  /* 1002 */ 65344, 67, 1, 65470, 69, 1, 65468, 71, 1, 21, 2, 0,
  /* 1014 */ 65344, 66, 1, 65471, 68, 1, 65469, 70, 1, 22, 2, 0,
  /* 1026 */ 65344, 65, 1, 65472, 67, 1, 65470, 69, 1, 23, 2, 0,
  /* 1038 */ 65344, 2, 2, 93, 2, 0,
  /* 1044 */ 65344, 80, 1, 65457, 2, 93, 2, 0,
  /* 1052 */ 65344, 79, 1, 65458, 2, 93, 2, 0,
  /* 1060 */ 65344, 78, 1, 65459, 80, 1, 65457, 93, 2, 0,
  /* 1070 */ 65344, 77, 1, 65460, 79, 1, 65458, 93, 2, 0,
  /* 1080 */ 65439, 2, 0,
  /* 1083 */ 65453, 2, 0,
  /* 1086 */ 65080, 1, 3, 1, 3, 1, 3, 0,
  /* 1094 */ 65136, 1, 3, 1, 3, 0,
  /* 1100 */ 65326, 1, 3, 0,
  /* 1104 */ 5, 0,
  /* 1106 */ 140, 65486, 13, 0,
  /* 1110 */ 14, 0,
  /* 1112 */ 126, 65501, 15, 0,
  /* 1116 */ 10, 66, 0,
  /* 1119 */ 65445, 65514, 1, 22, 65515, 1, 94, 65, 65472, 65, 69, 0,
  /* 1131 */ 65445, 65513, 1, 23, 65514, 1, 94, 65, 65472, 65, 70, 0,
  /* 1143 */ 65445, 65512, 1, 24, 65513, 1, 94, 65, 65472, 65, 71, 0,
  /* 1155 */ 65445, 65511, 1, 25, 65512, 1, 94, 65, 65472, 65, 72, 0,
  /* 1167 */ 65445, 65510, 1, 26, 65511, 1, 94, 65, 65472, 65, 73, 0,
  /* 1179 */ 65445, 65509, 1, 27, 65510, 1, 94, 65, 65472, 65, 74, 0,
  /* 1191 */ 65445, 65508, 1, 28, 65509, 1, 94, 65, 65472, 65, 75, 0,
  /* 1203 */ 65445, 65507, 79, 1, 65457, 80, 1, 65484, 65508, 1, 94, 65, 65472, 65, 76, 0,
  /* 1219 */ 65445, 65506, 77, 1, 65459, 78, 1, 65487, 65507, 79, 1, 65457, 80, 1, 13, 65, 65472, 65, 77, 0,
  /* 1239 */ 65445, 65505, 75, 1, 65461, 76, 1, 65490, 65506, 77, 1, 65459, 78, 1, 15, 65, 65472, 65, 78, 0,
  /* 1259 */ 65445, 65504, 73, 1, 65463, 74, 1, 65493, 65505, 75, 1, 65461, 76, 1, 17, 65, 65472, 65, 79, 0,
  /* 1279 */ 65445, 65503, 71, 1, 65465, 72, 1, 65496, 65504, 73, 1, 65463, 74, 1, 19, 65, 65472, 65, 80, 0,
  /* 1299 */ 65445, 65502, 69, 1, 65467, 70, 1, 65499, 65503, 71, 1, 65465, 72, 1, 21, 65, 65472, 65, 81, 0,
  /* 1319 */ 65445, 65501, 67, 1, 65469, 68, 1, 65502, 65502, 69, 1, 65467, 70, 1, 23, 65, 65472, 65, 82, 0,
  /* 1339 */ 65445, 65500, 65, 1, 65471, 66, 1, 65505, 65501, 67, 1, 65469, 68, 1, 25, 65, 65472, 65, 83, 0,
  /* 1359 */ 91, 0,
  /* 1361 */ 98, 0,
  /* 1363 */ 99, 0,
  /* 1365 */ 100, 0,
  /* 1367 */ 101, 0,
  /* 1369 */ 102, 0,
  /* 1371 */ 103, 0,
  /* 1373 */ 104, 0,
  /* 1375 */ 65374, 1, 1, 20, 75, 135, 0,
  /* 1382 */ 65374, 1, 1, 21, 74, 136, 0,
  /* 1389 */ 65374, 1, 1, 22, 73, 137, 0,
  /* 1396 */ 65374, 1, 1, 23, 72, 138, 0,
  /* 1403 */ 65374, 1, 1, 24, 71, 139, 0,
  /* 1410 */ 65374, 1, 1, 25, 70, 140, 0,
  /* 1417 */ 65374, 1, 1, 26, 69, 141, 0,
  /* 1424 */ 65374, 79, 1, 65457, 80, 1, 65456, 27, 68, 142, 0,
  /* 1435 */ 65374, 77, 1, 65459, 78, 1, 65458, 79, 1, 65484, 67, 143, 0,
  /* 1448 */ 65374, 75, 1, 65461, 76, 1, 65460, 77, 1, 65487, 66, 144, 0,
  /* 1461 */ 65374, 73, 1, 65463, 74, 1, 65462, 75, 1, 65490, 65, 145, 0,
  /* 1474 */ 65374, 71, 1, 65465, 72, 1, 65464, 73, 1, 65493, 64, 146, 0,
  /* 1487 */ 65374, 69, 1, 65467, 70, 1, 65466, 71, 1, 65496, 63, 147, 0,
  /* 1500 */ 65374, 67, 1, 65469, 68, 1, 65468, 69, 1, 65499, 62, 148, 0,
  /* 1513 */ 65374, 65, 1, 65471, 66, 1, 65470, 67, 1, 65502, 61, 149, 0,
  /* 1526 */ 157, 0,
  /* 1528 */ 65289, 1, 1, 1, 229, 1, 65400, 65, 65472, 65, 65396, 0,
  /* 1540 */ 65288, 1, 1, 1, 230, 1, 65399, 65, 65472, 65, 65397, 0,
  /* 1552 */ 65287, 1, 1, 1, 231, 1, 65398, 65, 65472, 65, 65398, 0,
  /* 1564 */ 65286, 1, 1, 1, 232, 1, 65397, 65, 65472, 65, 65399, 0,
  /* 1576 */ 65285, 1, 1, 1, 233, 1, 65396, 65, 65472, 65, 65400, 0,
  /* 1588 */ 65284, 1, 1, 1, 234, 1, 65395, 65, 65472, 65, 65401, 0,
  /* 1600 */ 65521, 65445, 65512, 1, 24, 65513, 1, 94, 65, 65472, 65, 71, 65419, 65445, 65514, 1, 22, 65515, 1, 94, 65, 65472, 65, 69, 65492, 28, 65509, 28, 28, 65386, 65, 30, 65442, 65, 30, 40, 15, 65402, 0,
  /* 1639 */ 65521, 65445, 65511, 1, 25, 65512, 1, 94, 65, 65472, 65, 72, 65419, 65445, 65513, 1, 23, 65514, 1, 94, 65, 65472, 65, 70, 65491, 28, 65509, 28, 29, 65385, 65, 30, 65442, 65, 30, 41, 15, 65402, 0,
  /* 1678 */ 65521, 65445, 65510, 1, 26, 65511, 1, 94, 65, 65472, 65, 73, 65419, 65445, 65512, 1, 24, 65513, 1, 94, 65, 65472, 65, 71, 65490, 28, 65509, 28, 30, 65384, 65, 30, 65442, 65, 30, 42, 15, 65402, 0,
  /* 1717 */ 65521, 65445, 65509, 1, 27, 65510, 1, 94, 65, 65472, 65, 74, 65419, 65445, 65511, 1, 25, 65512, 1, 94, 65, 65472, 65, 72, 65489, 28, 65509, 28, 31, 65383, 65, 30, 65442, 65, 30, 43, 15, 65402, 0,
  /* 1756 */ 65521, 65445, 65508, 1, 28, 65509, 1, 94, 65, 65472, 65, 75, 65419, 65445, 65510, 1, 26, 65511, 1, 94, 65, 65472, 65, 73, 65488, 28, 65509, 28, 32, 65382, 65, 30, 65442, 65, 30, 44, 15, 65402, 0,
  /* 1795 */ 65521, 65445, 65507, 79, 1, 65457, 80, 1, 65484, 65508, 1, 94, 65, 65472, 65, 76, 65419, 65445, 65509, 1, 27, 65510, 1, 94, 65, 65472, 65, 74, 65487, 28, 65509, 28, 33, 65381, 65, 30, 65442, 65, 30, 45, 15, 65402, 0,
  /* 1838 */ 65521, 65445, 65506, 77, 1, 65459, 78, 1, 65487, 65507, 79, 1, 65457, 80, 1, 13, 65, 65472, 65, 77, 65419, 65445, 65508, 1, 28, 65509, 1, 94, 65, 65472, 65, 75, 65486, 28, 65509, 28, 34, 65380, 65, 30, 65442, 65, 30, 46, 15, 65402, 0,
  /* 1885 */ 65521, 65445, 65505, 75, 1, 65461, 76, 1, 65490, 65506, 77, 1, 65459, 78, 1, 15, 65, 65472, 65, 78, 65419, 65445, 65507, 79, 1, 65457, 80, 1, 65484, 65508, 1, 94, 65, 65472, 65, 76, 65485, 28, 65509, 28, 35, 65379, 65, 30, 65442, 65, 30, 47, 15, 65402, 0,
  /* 1936 */ 65521, 65445, 65504, 73, 1, 65463, 74, 1, 65493, 65505, 75, 1, 65461, 76, 1, 17, 65, 65472, 65, 79, 65419, 65445, 65506, 77, 1, 65459, 78, 1, 65487, 65507, 79, 1, 65457, 80, 1, 13, 65, 65472, 65, 77, 65484, 28, 65509, 28, 36, 65378, 65, 30, 65442, 65, 30, 48, 15, 65402, 0,
  /* 1991 */ 65521, 65445, 65503, 71, 1, 65465, 72, 1, 65496, 65504, 73, 1, 65463, 74, 1, 19, 65, 65472, 65, 80, 65419, 65445, 65505, 75, 1, 65461, 76, 1, 65490, 65506, 77, 1, 65459, 78, 1, 15, 65, 65472, 65, 78, 65483, 28, 65509, 28, 37, 65377, 65, 30, 65442, 65, 30, 49, 15, 65402, 0,
  /* 2046 */ 65521, 65445, 65502, 69, 1, 65467, 70, 1, 65499, 65503, 71, 1, 65465, 72, 1, 21, 65, 65472, 65, 81, 65419, 65445, 65504, 73, 1, 65463, 74, 1, 65493, 65505, 75, 1, 65461, 76, 1, 17, 65, 65472, 65, 79, 65482, 28, 65509, 28, 38, 65376, 65, 30, 65442, 65, 30, 50, 15, 65402, 0,
  /* 2101 */ 65521, 65445, 65501, 67, 1, 65469, 68, 1, 65502, 65502, 69, 1, 65467, 70, 1, 23, 65, 65472, 65, 82, 65419, 65445, 65503, 71, 1, 65465, 72, 1, 65496, 65504, 73, 1, 65463, 74, 1, 19, 65, 65472, 65, 80, 65481, 28, 65509, 28, 39, 65375, 65, 30, 65442, 65, 30, 51, 15, 65402, 0,
  /* 2156 */ 65521, 65445, 65500, 65, 1, 65471, 66, 1, 65505, 65501, 67, 1, 65469, 68, 1, 25, 65, 65472, 65, 83, 65419, 65445, 65502, 69, 1, 65467, 70, 1, 65499, 65503, 71, 1, 65465, 72, 1, 21, 65, 65472, 65, 81, 65480, 28, 65509, 28, 40, 65374, 65, 30, 65442, 65, 30, 52, 15, 65402, 0,
  /* 2211 */ 65283, 80, 1, 65456, 1, 1, 235, 1, 65394, 65, 65472, 65, 65402, 0,
  /* 2225 */ 65282, 78, 1, 65458, 79, 1, 65457, 80, 1, 65456, 236, 1, 65393, 65, 65472, 65, 65403, 0,
  /* 2243 */ 65281, 76, 1, 65460, 77, 1, 65459, 78, 1, 65458, 79, 1, 157, 1, 65392, 65, 65472, 65, 65404, 0,
  /* 2263 */ 65280, 74, 1, 65462, 75, 1, 65461, 76, 1, 65460, 77, 1, 160, 1, 65391, 65, 65472, 65, 65405, 0,
  /* 2283 */ 65279, 72, 1, 65464, 73, 1, 65463, 74, 1, 65462, 75, 1, 163, 1, 65390, 65, 65472, 65, 65406, 0,
  /* 2303 */ 65278, 70, 1, 65466, 71, 1, 65465, 72, 1, 65464, 73, 1, 166, 1, 65389, 65, 65472, 65, 65407, 0,
  /* 2323 */ 65277, 68, 1, 65468, 69, 1, 65467, 70, 1, 65466, 71, 1, 169, 1, 65388, 65, 65472, 65, 65408, 0,
  /* 2343 */ 65276, 66, 1, 65470, 67, 1, 65469, 68, 1, 65468, 69, 1, 172, 1, 65387, 65, 65472, 65, 65409, 0,
  /* 2363 */ 22, 73, 2, 63, 65488, 120, 65465, 1, 65487, 75, 26, 65447, 65, 26, 30, 65416, 66, 26, 29, 65416, 0,
  /* 2384 */ 21, 74, 2, 63, 65487, 120, 65466, 1, 65486, 76, 26, 65446, 66, 26, 29, 65416, 0,
  /* 2401 */ 65, 65487, 77, 26, 65446, 66, 26, 29, 65416, 0,
  /* 2411 */ 22, 73, 2, 134, 65465, 1, 65487, 50, 65487, 75, 26, 31, 65416, 65, 26, 30, 65416, 0,
  /* 2429 */ 21, 74, 135, 65466, 1, 65486, 77, 26, 30, 65416, 0,
  /* 2440 */ 65, 65487, 77, 26, 30, 65416, 0,
  /* 2447 */ 139, 65487, 50, 65487, 12, 121, 65416, 0,
  /* 2455 */ 65487, 13, 121, 65416, 0,
  /* 2460 */ 65465, 1, 65487, 133, 65416, 121, 65416, 0,
  /* 2468 */ 65466, 1, 65486, 133, 65416, 0,
  /* 2474 */ 65487, 133, 65416, 0,
  /* 2478 */ 65469, 35, 62, 148, 65452, 1, 65500, 66, 28, 40, 65417, 0,
  /* 2490 */ 65470, 35, 62, 148, 65452, 1, 65500, 66, 28, 40, 65417, 0,
  /* 2502 */ 65, 65500, 66, 28, 40, 65417, 0,
  /* 2509 */ 65452, 1, 65500, 134, 65417, 0,
  /* 2515 */ 65316, 74, 1, 65463, 76, 1, 65461, 78, 1, 65459, 80, 1, 10, 95, 65443, 95, 65443, 0,
  /* 2533 */ 65316, 73, 1, 65464, 75, 1, 65462, 77, 1, 65460, 79, 1, 11, 95, 65443, 95, 65443, 0,
  /* 2551 */ 65316, 72, 1, 65465, 74, 1, 65463, 76, 1, 65461, 78, 1, 12, 95, 65443, 95, 65443, 0,
  /* 2569 */ 65316, 71, 1, 65466, 73, 1, 65464, 75, 1, 65462, 77, 1, 13, 95, 65443, 95, 65443, 0,
  /* 2587 */ 65316, 70, 1, 65467, 72, 1, 65465, 74, 1, 65463, 76, 1, 14, 95, 65443, 95, 65443, 0,
  /* 2605 */ 65316, 69, 1, 65468, 71, 1, 65466, 73, 1, 65464, 75, 1, 15, 95, 65443, 95, 65443, 0,
  /* 2623 */ 65316, 68, 1, 65469, 70, 1, 65467, 72, 1, 65465, 74, 1, 16, 95, 65443, 95, 65443, 0,
  /* 2641 */ 65316, 67, 1, 65470, 69, 1, 65468, 71, 1, 65466, 73, 1, 17, 95, 65443, 95, 65443, 0,
  /* 2659 */ 65316, 66, 1, 65471, 68, 1, 65469, 70, 1, 65467, 72, 1, 18, 95, 65443, 95, 65443, 0,
  /* 2677 */ 65316, 65, 1, 65472, 67, 1, 65470, 69, 1, 65468, 71, 1, 19, 95, 65443, 95, 65443, 0,
  /* 2695 */ 65316, 2, 2, 2, 91, 95, 65443, 95, 65443, 0,
  /* 2705 */ 65316, 80, 1, 65457, 2, 2, 91, 95, 65443, 95, 65443, 0,
  /* 2717 */ 65316, 79, 1, 65458, 2, 2, 91, 95, 65443, 95, 65443, 0,
  /* 2729 */ 65316, 78, 1, 65459, 80, 1, 65457, 2, 91, 95, 65443, 95, 65443, 0,
  /* 2743 */ 65316, 77, 1, 65460, 79, 1, 65458, 2, 91, 95, 65443, 95, 65443, 0,
  /* 2757 */ 65316, 76, 1, 65461, 78, 1, 65459, 80, 1, 65457, 91, 95, 65443, 95, 65443, 0,
  /* 2773 */ 65316, 75, 1, 65462, 77, 1, 65460, 79, 1, 65458, 91, 95, 65443, 95, 65443, 0,
  /* 2789 */ 20, 75, 65, 65486, 78, 26, 65445, 0,
  /* 2797 */ 23, 72, 2, 63, 65489, 120, 65464, 1, 65488, 74, 26, 65448, 64, 26, 31, 65416, 65, 26, 30, 65416, 92, 65445, 0,
  /* 2820 */ 65, 65488, 76, 26, 65447, 65, 26, 30, 65416, 92, 65445, 0,
  /* 2832 */ 26, 65446, 92, 65445, 0,
  /* 2837 */ 23, 72, 2, 135, 65464, 1, 65488, 49, 65488, 74, 26, 32, 65416, 64, 26, 31, 65416, 65, 26, 65446, 0,
  /* 2858 */ 65, 65488, 76, 26, 31, 65416, 65, 26, 65446, 0,
  /* 2868 */ 24, 71, 2, 63, 65490, 120, 65463, 1, 65489, 73, 26, 65449, 63, 26, 32, 65416, 64, 26, 31, 65416, 91, 65446, 0,
  /* 2891 */ 65, 65489, 75, 26, 65448, 64, 26, 31, 65416, 91, 65446, 0,
  /* 2903 */ 24, 71, 2, 136, 65463, 1, 65489, 48, 65489, 73, 26, 33, 65416, 63, 26, 32, 65416, 64, 26, 65447, 91, 65446, 0,
  /* 2926 */ 65, 65489, 75, 26, 32, 65416, 64, 26, 65447, 91, 65446, 0,
  /* 2938 */ 25, 70, 2, 63, 65491, 120, 65462, 1, 65490, 72, 26, 65450, 62, 26, 33, 65416, 63, 26, 32, 65416, 90, 65447, 0,
  /* 2961 */ 65, 65490, 74, 26, 65449, 63, 26, 32, 65416, 90, 65447, 0,
  /* 2973 */ 25, 70, 2, 137, 65462, 1, 65490, 47, 65490, 72, 26, 34, 65416, 62, 26, 33, 65416, 63, 26, 65448, 90, 65447, 0,
  /* 2996 */ 65, 65490, 74, 26, 33, 65416, 63, 26, 65448, 90, 65447, 0,
  /* 3008 */ 26, 69, 2, 63, 65492, 120, 65461, 1, 65491, 71, 26, 65451, 61, 26, 34, 65416, 62, 26, 33, 65416, 89, 65448, 0,
  /* 3031 */ 65, 65491, 73, 26, 65450, 62, 26, 33, 65416, 89, 65448, 0,
  /* 3043 */ 26, 69, 2, 138, 65461, 1, 65491, 46, 65491, 71, 26, 35, 65416, 61, 26, 34, 65416, 62, 26, 65449, 89, 65448, 0,
  /* 3066 */ 65, 65491, 73, 26, 34, 65416, 62, 26, 65449, 89, 65448, 0,
  /* 3078 */ 27, 68, 2, 63, 65493, 120, 65460, 1, 65492, 70, 26, 65452, 60, 26, 35, 65416, 61, 26, 34, 65416, 88, 65449, 0,
  /* 3101 */ 65, 65492, 72, 26, 65451, 61, 26, 34, 65416, 88, 65449, 0,
  /* 3113 */ 27, 68, 2, 139, 65460, 1, 65492, 45, 65492, 70, 26, 36, 65416, 60, 26, 35, 65416, 61, 26, 65450, 88, 65449, 0,
  /* 3136 */ 65, 65492, 72, 26, 35, 65416, 61, 26, 65450, 88, 65449, 0,
  /* 3148 */ 65455, 28, 67, 2, 63, 65494, 120, 65459, 1, 65493, 69, 26, 65453, 59, 26, 36, 65416, 60, 26, 35, 65416, 87, 65450, 0,
  /* 3172 */ 65456, 28, 67, 2, 63, 65494, 120, 65459, 1, 65493, 69, 26, 65453, 59, 26, 36, 65416, 60, 26, 35, 65416, 87, 65450, 0,
  /* 3196 */ 65, 65493, 71, 26, 65452, 60, 26, 35, 65416, 87, 65450, 0,
  /* 3208 */ 28, 67, 2, 140, 65459, 1, 65493, 44, 65493, 69, 26, 37, 65416, 59, 26, 36, 65416, 60, 26, 65451, 87, 65450, 0,
  /* 3231 */ 65, 65493, 71, 26, 36, 65416, 60, 26, 65451, 87, 65450, 0,
  /* 3243 */ 65457, 29, 66, 2, 63, 65495, 120, 65458, 1, 65494, 68, 26, 65454, 58, 26, 37, 65416, 59, 26, 36, 65416, 86, 65451, 0,
  /* 3267 */ 65458, 29, 66, 2, 63, 65495, 120, 65458, 1, 65494, 68, 26, 65454, 58, 26, 37, 65416, 59, 26, 36, 65416, 86, 65451, 0,
  /* 3291 */ 65, 65494, 70, 26, 65453, 59, 26, 36, 65416, 86, 65451, 0,
  /* 3303 */ 65456, 29, 66, 2, 141, 65458, 1, 65494, 43, 65494, 68, 26, 38, 65416, 58, 26, 37, 65416, 59, 26, 65452, 86, 65451, 0,
  /* 3327 */ 65457, 29, 66, 2, 141, 65458, 1, 65494, 43, 65494, 68, 26, 38, 65416, 58, 26, 37, 65416, 59, 26, 65452, 86, 65451, 0,
  /* 3351 */ 65, 65494, 70, 26, 37, 65416, 59, 26, 65452, 86, 65451, 0,
  /* 3363 */ 65459, 30, 65, 2, 63, 65496, 120, 65457, 1, 65495, 67, 26, 65455, 57, 26, 38, 65416, 58, 26, 37, 65416, 85, 65452, 0,
  /* 3387 */ 65460, 30, 65, 2, 63, 65496, 120, 65457, 1, 65495, 67, 26, 65455, 57, 26, 38, 65416, 58, 26, 37, 65416, 85, 65452, 0,
  /* 3411 */ 65, 65495, 69, 26, 65454, 58, 26, 37, 65416, 85, 65452, 0,
  /* 3423 */ 65458, 30, 65, 2, 142, 65457, 1, 65495, 42, 65495, 67, 26, 39, 65416, 57, 26, 38, 65416, 58, 26, 65453, 85, 65452, 0,
  /* 3447 */ 65459, 30, 65, 2, 142, 65457, 1, 65495, 42, 65495, 67, 26, 39, 65416, 57, 26, 38, 65416, 58, 26, 65453, 85, 65452, 0,
  /* 3471 */ 65, 65495, 69, 26, 38, 65416, 58, 26, 65453, 85, 65452, 0,
  /* 3483 */ 65461, 31, 64, 2, 63, 65497, 120, 65456, 1, 65496, 66, 26, 65456, 56, 26, 39, 65416, 57, 26, 38, 65416, 84, 65453, 0,
  /* 3507 */ 65462, 31, 64, 2, 63, 65497, 120, 65456, 1, 65496, 66, 26, 65456, 56, 26, 39, 65416, 57, 26, 38, 65416, 84, 65453, 0,
  /* 3531 */ 65, 65496, 68, 26, 65455, 57, 26, 38, 65416, 84, 65453, 0,
  /* 3543 */ 65460, 31, 64, 2, 143, 65456, 1, 65496, 41, 65496, 66, 26, 40, 65416, 56, 26, 39, 65416, 57, 26, 65454, 84, 65453, 0,
  /* 3567 */ 65461, 31, 64, 2, 143, 65456, 1, 65496, 41, 65496, 66, 26, 40, 65416, 56, 26, 39, 65416, 57, 26, 65454, 84, 65453, 0,
  /* 3591 */ 65, 65496, 68, 26, 39, 65416, 57, 26, 65454, 84, 65453, 0,
  /* 3603 */ 65463, 32, 63, 2, 63, 65498, 120, 65455, 1, 65497, 65, 26, 65457, 55, 26, 40, 65416, 56, 26, 39, 65416, 83, 65454, 0,
  /* 3627 */ 65464, 32, 63, 2, 63, 65498, 120, 65455, 1, 65497, 65, 26, 65457, 55, 26, 40, 65416, 56, 26, 39, 65416, 83, 65454, 0,
  /* 3651 */ 65, 65497, 67, 26, 65456, 56, 26, 39, 65416, 83, 65454, 0,
  /* 3663 */ 65462, 32, 63, 2, 144, 65455, 1, 65497, 40, 65497, 65, 26, 41, 65416, 55, 26, 40, 65416, 56, 26, 65455, 83, 65454, 0,
  /* 3687 */ 65463, 32, 63, 2, 144, 65455, 1, 65497, 40, 65497, 65, 26, 41, 65416, 55, 26, 40, 65416, 56, 26, 65455, 83, 65454, 0,
  /* 3711 */ 65, 65497, 67, 26, 40, 65416, 56, 26, 65455, 83, 65454, 0,
  /* 3723 */ 65465, 33, 62, 2, 63, 65499, 120, 65454, 1, 65498, 64, 2, 26, 41, 65416, 55, 26, 40, 65416, 82, 65455, 0,
  /* 3745 */ 65466, 33, 62, 2, 63, 65499, 120, 65454, 1, 65498, 64, 2, 26, 41, 65416, 55, 26, 40, 65416, 82, 65455, 0,
  /* 3767 */ 65, 65498, 66, 26, 65457, 55, 26, 40, 65416, 82, 65455, 0,
  /* 3779 */ 65464, 33, 62, 2, 145, 65454, 1, 65498, 39, 65498, 64, 26, 42, 65416, 54, 26, 41, 65416, 55, 26, 65456, 82, 65455, 0,
  /* 3803 */ 65465, 33, 62, 2, 145, 65454, 1, 65498, 39, 65498, 64, 26, 42, 65416, 54, 26, 41, 65416, 55, 26, 65456, 82, 65455, 0,
  /* 3827 */ 65, 65498, 66, 26, 41, 65416, 55, 26, 65456, 82, 65455, 0,
  /* 3839 */ 65298, 80, 1, 65456, 0,
  /* 3844 */ 65467, 34, 61, 2, 63, 65500, 120, 65453, 1, 65499, 65, 2, 26, 40, 1, 65416, 81, 65456, 0,
  /* 3863 */ 65468, 34, 61, 2, 63, 65500, 120, 65453, 1, 65499, 65, 2, 26, 40, 1, 65416, 81, 65456, 0,
  /* 3882 */ 65, 65499, 65, 2, 26, 41, 65416, 81, 65456, 0,
  /* 3892 */ 65466, 34, 61, 2, 146, 65453, 1, 65499, 38, 65499, 63, 2, 26, 41, 1, 65416, 54, 26, 65457, 81, 65456, 0,
  /* 3914 */ 65467, 34, 61, 2, 146, 65453, 1, 65499, 38, 65499, 63, 2, 26, 41, 1, 65416, 54, 26, 65457, 81, 65456, 0,
  /* 3936 */ 65, 65499, 65, 26, 42, 65416, 54, 26, 65457, 81, 65456, 0,
  /* 3948 */ 65439, 80, 1, 65457, 0,
  /* 3953 */ 28, 65457, 0,
  /* 3956 */ 65468, 35, 60, 2, 147, 65452, 1, 65500, 37, 65500, 64, 2, 26, 41, 65417, 80, 65457, 0,
  /* 3974 */ 65469, 35, 60, 2, 147, 65452, 1, 65500, 37, 65500, 64, 2, 26, 41, 65417, 80, 65457, 0,
  /* 3992 */ 65, 65500, 64, 2, 26, 41, 65417, 80, 65457, 0,
  /* 4002 */ 26, 65458, 80, 65457, 0,
  /* 4007 */ 65439, 79, 1, 65458, 0,
  /* 4012 */ 65470, 36, 61, 65, 65501, 65, 28, 65458, 0,
  /* 4021 */ 65471, 36, 61, 65, 65501, 65, 28, 65458, 0,
  /* 4030 */ 65374, 1, 1, 229, 65402, 65461, 0,
  /* 4037 */ 65374, 1, 1, 230, 65401, 65462, 0,
  /* 4044 */ 65374, 1, 1, 231, 65400, 65463, 0,
  /* 4051 */ 65374, 1, 1, 232, 65399, 65464, 0,
  /* 4058 */ 65374, 1, 1, 233, 65398, 65465, 0,
  /* 4065 */ 65374, 1, 1, 234, 65397, 65466, 0,
  /* 4072 */ 65374, 1, 1, 235, 65396, 65467, 0,
  /* 4079 */ 65374, 80, 1, 65456, 1, 236, 65395, 65468, 0,
  /* 4088 */ 65374, 78, 1, 65458, 79, 1, 65457, 80, 1, 156, 65394, 65469, 0,
  /* 4101 */ 65374, 76, 1, 65460, 77, 1, 65459, 78, 1, 159, 65393, 65470, 0,
  /* 4114 */ 65445, 65470, 0,
  /* 4117 */ 65374, 74, 1, 65462, 75, 1, 65461, 76, 1, 162, 65392, 65471, 0,
  /* 4130 */ 65374, 72, 1, 65464, 73, 1, 65463, 74, 1, 165, 65391, 65472, 0,
  /* 4143 */ 65374, 70, 1, 65466, 71, 1, 65465, 72, 1, 168, 65390, 65473, 0,
  /* 4156 */ 65374, 68, 1, 65468, 69, 1, 65467, 70, 1, 171, 65389, 65474, 0,
  /* 4169 */ 65374, 66, 1, 65470, 67, 1, 65469, 68, 1, 174, 65388, 65475, 0,
  /* 4182 */ 65534, 0,
  /* 4184 */ 65535, 0,
};

static const uint16_t ARMSubRegIdxLists[] = {
  /* 0 */ 1, 2, 0,
  /* 3 */ 1, 17, 18, 2, 0,
  /* 8 */ 1, 3, 0,
  /* 11 */ 1, 17, 18, 3, 0,
  /* 16 */ 9, 10, 0,
  /* 19 */ 17, 18, 0,
  /* 22 */ 1, 17, 18, 2, 19, 20, 0,
  /* 29 */ 1, 17, 18, 3, 21, 22, 0,
  /* 36 */ 1, 2, 3, 13, 33, 37, 0,
  /* 43 */ 1, 17, 18, 2, 3, 13, 33, 37, 0,
  /* 52 */ 1, 17, 18, 2, 19, 20, 3, 13, 33, 37, 0,
  /* 63 */ 1, 17, 18, 2, 19, 20, 3, 21, 22, 13, 33, 37, 0,
  /* 76 */ 13, 1, 2, 14, 3, 4, 33, 34, 35, 36, 37, 0,
  /* 88 */ 13, 1, 17, 18, 2, 19, 20, 14, 3, 4, 33, 34, 35, 36, 37, 0,
  /* 104 */ 1, 2, 3, 4, 13, 14, 33, 34, 35, 36, 37, 0,
  /* 116 */ 1, 17, 18, 2, 3, 4, 13, 14, 33, 34, 35, 36, 37, 0,
  /* 130 */ 1, 17, 18, 2, 19, 20, 3, 21, 22, 4, 13, 14, 33, 34, 35, 36, 37, 0,
  /* 148 */ 1, 17, 18, 2, 19, 20, 3, 21, 22, 4, 23, 24, 13, 14, 33, 34, 35, 36, 37, 0,
  /* 168 */ 13, 1, 17, 18, 2, 19, 20, 14, 3, 21, 22, 4, 23, 24, 33, 34, 35, 36, 37, 0,
  /* 188 */ 1, 3, 5, 33, 43, 0,
  /* 194 */ 1, 17, 18, 3, 5, 33, 43, 0,
  /* 202 */ 1, 17, 18, 3, 21, 22, 5, 33, 43, 0,
  /* 212 */ 1, 17, 18, 3, 21, 22, 5, 31, 32, 33, 43, 0,
  /* 224 */ 1, 3, 5, 7, 33, 38, 43, 45, 51, 0,
  /* 234 */ 1, 17, 18, 3, 5, 7, 33, 38, 43, 45, 51, 0,
  /* 246 */ 1, 17, 18, 3, 21, 22, 5, 7, 33, 38, 43, 45, 51, 0,
  /* 260 */ 1, 17, 18, 3, 21, 22, 5, 31, 32, 7, 33, 38, 43, 45, 51, 0,
  /* 276 */ 1, 17, 18, 3, 21, 22, 5, 31, 32, 7, 27, 28, 33, 38, 43, 45, 51, 0,
  /* 294 */ 11, 13, 1, 2, 14, 3, 4, 33, 34, 35, 36, 37, 12, 15, 5, 6, 16, 7, 8, 51, 52, 53, 54, 55, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 56, 0,
  /* 333 */ 11, 13, 1, 17, 18, 2, 19, 20, 14, 3, 4, 33, 34, 35, 36, 37, 12, 15, 5, 6, 16, 7, 8, 51, 52, 53, 54, 55, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 56, 0,
  /* 376 */ 11, 13, 1, 17, 18, 2, 19, 20, 14, 3, 21, 22, 4, 23, 24, 33, 34, 35, 36, 37, 12, 15, 5, 6, 16, 7, 8, 51, 52, 53, 54, 55, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 56, 0,
  /* 423 */ 11, 13, 1, 17, 18, 2, 19, 20, 14, 3, 21, 22, 4, 23, 24, 33, 34, 35, 36, 37, 12, 15, 5, 31, 32, 6, 29, 30, 16, 7, 8, 51, 52, 53, 54, 55, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 56, 0,
  /* 474 */ 11, 13, 1, 17, 18, 2, 19, 20, 14, 3, 21, 22, 4, 23, 24, 33, 34, 35, 36, 37, 12, 15, 5, 31, 32, 6, 29, 30, 16, 7, 27, 28, 8, 25, 26, 51, 52, 53, 54, 55, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 56, 0,
};


static MCRegisterDesc ARMRegDesc[] = { // Descriptors
  { 12, 0, 0, 0, 0, 0 },
  { 1235, 16, 16, 2, 66945, 0 },
  { 1268, 16, 16, 2, 66945, 0 },
  { 1240, 16, 16, 2, 66945, 0 },
  { 1199, 16, 16, 2, 66945, 0 },
  { 1250, 16, 16, 2, 66945, 0 },
  { 1226, 16, 16, 2, 17664, 0 },
  { 1257, 16, 16, 2, 17664, 0 },
  { 1205, 16, 16, 2, 66913, 0 },
  { 1211, 16, 16, 2, 66913, 0 },
  { 1232, 16, 16, 2, 66913, 0 },
  { 1196, 16, 16, 2, 66913, 0 },
  { 1223, 16, 1526, 2, 66913, 0 },
  { 1245, 16, 16, 2, 66913, 0 },
  { 119, 350, 4013, 19, 13250, 8 },
  { 248, 357, 2479, 19, 13250, 8 },
  { 363, 364, 3957, 19, 13250, 8 },
  { 479, 378, 3845, 19, 13250, 8 },
  { 605, 392, 3893, 19, 13250, 8 },
  { 723, 406, 3724, 19, 13250, 8 },
  { 837, 420, 3780, 19, 13250, 8 },
  { 943, 434, 3604, 19, 13250, 8 },
  { 1057, 448, 3664, 19, 13250, 8 },
  { 1163, 462, 3484, 19, 13250, 8 },
  { 9, 476, 3544, 19, 13250, 8 },
  { 141, 490, 3364, 19, 13250, 8 },
  { 282, 504, 3424, 19, 13250, 8 },
  { 408, 518, 3244, 19, 13250, 8 },
  { 523, 532, 3304, 19, 13250, 8 },
  { 649, 546, 3149, 19, 13250, 8 },
  { 768, 16, 3208, 2, 17761, 0 },
  { 882, 16, 3078, 2, 17761, 0 },
  { 988, 16, 3113, 2, 17761, 0 },
  { 1102, 16, 3008, 2, 17761, 0 },
  { 59, 16, 3043, 2, 17761, 0 },
  { 192, 16, 2938, 2, 17761, 0 },
  { 336, 16, 2973, 2, 17761, 0 },
  { 456, 16, 2868, 2, 17761, 0 },
  { 575, 16, 2903, 2, 17761, 0 },
  { 697, 16, 2797, 2, 17761, 0 },
  { 804, 16, 2837, 2, 17761, 0 },
  { 914, 16, 2363, 2, 17761, 0 },
  { 1024, 16, 2411, 2, 17761, 0 },
  { 1134, 16, 2384, 2, 17761, 0 },
  { 95, 16, 2429, 2, 17761, 0 },
  { 224, 16, 2789, 2, 17761, 0 },
  { 390, 16, 16, 2, 17761, 0 },
  { 125, 16, 16, 2, 17761, 0 },
  { 257, 16, 16, 2, 17761, 0 },
  { 381, 16, 16, 2, 17761, 0 },
  { 122, 353, 1112, 22, 2196, 11 },
  { 254, 374, 775, 22, 2196, 11 },
  { 378, 402, 314, 22, 2196, 11 },
  { 500, 430, 244, 22, 2196, 11 },
  { 629, 458, 234, 22, 2196, 11 },
  { 744, 486, 224, 22, 2196, 11 },
  { 861, 514, 214, 22, 2196, 11 },
  { 964, 542, 204, 22, 2196, 11 },
  { 1081, 804, 194, 0, 12818, 20 },
  { 1184, 807, 184, 0, 12818, 20 },
  { 35, 810, 174, 0, 12818, 20 },
  { 168, 813, 164, 0, 12818, 20 },
  { 312, 816, 154, 0, 12818, 20 },
  { 436, 819, 591, 0, 12818, 20 },
  { 555, 822, 2447, 0, 12818, 20 },
  { 677, 825, 1106, 0, 12818, 20 },
  { 128, 16, 1373, 2, 66913, 0 },
  { 260, 16, 1371, 2, 66913, 0 },
  { 384, 16, 1371, 2, 66913, 0 },
  { 506, 16, 1369, 2, 66913, 0 },
  { 632, 16, 1369, 2, 66913, 0 },
  { 750, 16, 1367, 2, 66913, 0 },
  { 864, 16, 1367, 2, 66913, 0 },
  { 970, 16, 1365, 2, 66913, 0 },
  { 1084, 16, 1365, 2, 66913, 0 },
  { 1190, 16, 1363, 2, 66913, 0 },
  { 39, 16, 1363, 2, 66913, 0 },
  { 176, 16, 1361, 2, 66913, 0 },
  { 316, 16, 1359, 2, 66913, 0 },
  { 131, 16, 4021, 2, 65585, 0 },
  { 269, 16, 4012, 2, 65585, 0 },
  { 387, 16, 2490, 2, 65585, 0 },
  { 509, 16, 2478, 2, 65585, 0 },
  { 635, 16, 3974, 2, 65585, 0 },
  { 753, 16, 3956, 2, 65585, 0 },
  { 867, 16, 3863, 2, 65585, 0 },
  { 973, 16, 3844, 2, 65585, 0 },
  { 1087, 16, 3914, 2, 65585, 0 },
  { 1193, 16, 3892, 2, 65585, 0 },
  { 43, 16, 3745, 2, 65585, 0 },
  { 180, 16, 3723, 2, 65585, 0 },
  { 320, 16, 3803, 2, 65585, 0 },
  { 440, 16, 3779, 2, 65585, 0 },
  { 559, 16, 3627, 2, 65585, 0 },
  { 681, 16, 3603, 2, 65585, 0 },
  { 788, 16, 3687, 2, 65585, 0 },
  { 898, 16, 3663, 2, 65585, 0 },
  { 1008, 16, 3507, 2, 65585, 0 },
  { 1118, 16, 3483, 2, 65585, 0 },
  { 79, 16, 3567, 2, 65585, 0 },
  { 212, 16, 3543, 2, 65585, 0 },
  { 356, 16, 3387, 2, 65585, 0 },
  { 472, 16, 3363, 2, 65585, 0 },
  { 595, 16, 3447, 2, 65585, 0 },
  { 713, 16, 3423, 2, 65585, 0 },
  { 824, 16, 3267, 2, 65585, 0 },
  { 930, 16, 3243, 2, 65585, 0 },
  { 1044, 16, 3327, 2, 65585, 0 },
  { 1150, 16, 3303, 2, 65585, 0 },
  { 115, 16, 3172, 2, 65585, 0 },
  { 244, 16, 3148, 2, 65585, 0 },
  { 360, 367, 4015, 29, 5426, 23 },
  { 476, 381, 2502, 29, 5426, 23 },
  { 602, 395, 3992, 29, 5426, 23 },
  { 720, 409, 3882, 29, 5426, 23 },
  { 834, 423, 3936, 29, 5426, 23 },
  { 940, 437, 3767, 29, 5426, 23 },
  { 1054, 451, 3827, 29, 5426, 23 },
  { 1160, 465, 3651, 29, 5426, 23 },
  { 6, 479, 3711, 29, 5426, 23 },
  { 151, 493, 3531, 29, 5426, 23 },
  { 278, 507, 3591, 29, 5426, 23 },
  { 404, 521, 3411, 29, 5426, 23 },
  { 519, 535, 3471, 29, 5426, 23 },
  { 645, 549, 3291, 29, 5426, 23 },
  { 764, 4007, 3351, 11, 17602, 35 },
  { 878, 3948, 3196, 11, 13522, 35 },
  { 984, 1080, 3231, 8, 17329, 39 },
  { 1098, 1080, 3101, 8, 17329, 39 },
  { 55, 1080, 3136, 8, 17329, 39 },
  { 204, 1080, 3031, 8, 17329, 39 },
  { 332, 1080, 3066, 8, 17329, 39 },
  { 452, 1080, 2961, 8, 17329, 39 },
  { 571, 1080, 2996, 8, 17329, 39 },
  { 693, 1080, 2891, 8, 17329, 39 },
  { 800, 1080, 2926, 8, 17329, 39 },
  { 910, 1080, 2820, 8, 17329, 39 },
  { 1020, 1080, 2858, 8, 17329, 39 },
  { 1130, 1080, 2401, 8, 17329, 39 },
  { 91, 1080, 2440, 8, 17329, 39 },
  { 236, 1080, 2791, 8, 17329, 39 },
  { 251, 1339, 1114, 168, 1044, 57 },
  { 375, 1319, 347, 168, 1044, 57 },
  { 497, 1299, 142, 168, 1044, 57 },
  { 626, 1279, 142, 168, 1044, 57 },
  { 741, 1259, 142, 168, 1044, 57 },
  { 858, 1239, 142, 168, 1044, 57 },
  { 961, 1219, 142, 168, 1044, 57 },
  { 1078, 1203, 142, 88, 1456, 74 },
  { 1181, 1191, 142, 76, 2114, 87 },
  { 32, 1179, 142, 76, 2114, 87 },
  { 164, 1167, 142, 76, 2114, 87 },
  { 308, 1155, 142, 76, 2114, 87 },
  { 432, 1143, 142, 76, 2114, 87 },
  { 551, 1131, 344, 76, 2114, 87 },
  { 673, 1119, 1108, 76, 2114, 87 },
  { 491, 2156, 16, 474, 4, 92 },
  { 620, 2101, 16, 474, 4, 92 },
  { 735, 2046, 16, 474, 4, 92 },
  { 852, 1991, 16, 474, 4, 92 },
  { 955, 1936, 16, 474, 4, 92 },
  { 1072, 1885, 16, 423, 272, 109 },
  { 1175, 1838, 16, 376, 512, 124 },
  { 26, 1795, 16, 333, 720, 137 },
  { 158, 1756, 16, 294, 1186, 148 },
  { 301, 1717, 16, 294, 1186, 148 },
  { 424, 1678, 16, 294, 1186, 148 },
  { 543, 1639, 16, 294, 1186, 148 },
  { 665, 1600, 16, 294, 1186, 148 },
  { 1219, 4114, 16, 16, 17856, 2 },
  { 263, 783, 16, 16, 8946, 5 },
  { 503, 786, 16, 16, 8946, 5 },
  { 747, 789, 16, 16, 8946, 5 },
  { 967, 792, 16, 16, 8946, 5 },
  { 1187, 795, 16, 16, 8946, 5 },
  { 172, 798, 16, 16, 8946, 5 },
  { 366, 1513, 1113, 63, 1570, 28 },
  { 482, 4169, 2511, 63, 1570, 28 },
  { 611, 1500, 778, 63, 1570, 28 },
  { 726, 4156, 770, 63, 1570, 28 },
  { 843, 1487, 317, 63, 1570, 28 },
  { 946, 4143, 660, 63, 1570, 28 },
  { 1063, 1474, 308, 63, 1570, 28 },
  { 1166, 4130, 654, 63, 1570, 28 },
  { 16, 1461, 302, 63, 1570, 28 },
  { 134, 4117, 648, 63, 1570, 28 },
  { 289, 1448, 296, 63, 1570, 28 },
  { 412, 4101, 642, 63, 1570, 28 },
  { 531, 1435, 290, 63, 1570, 28 },
  { 653, 4088, 636, 63, 1570, 28 },
  { 776, 1424, 284, 52, 1680, 42 },
  { 886, 4079, 630, 43, 1872, 48 },
  { 996, 1417, 278, 36, 2401, 53 },
  { 1106, 4072, 624, 36, 2401, 53 },
  { 67, 1410, 272, 36, 2401, 53 },
  { 184, 4065, 618, 36, 2401, 53 },
  { 344, 1403, 266, 36, 2401, 53 },
  { 460, 4058, 612, 36, 2401, 53 },
  { 583, 1396, 260, 36, 2401, 53 },
  { 701, 4051, 606, 36, 2401, 53 },
  { 812, 1389, 254, 36, 2401, 53 },
  { 918, 4044, 600, 36, 2401, 53 },
  { 1032, 1382, 765, 36, 2401, 53 },
  { 1138, 4037, 2455, 36, 2401, 53 },
  { 103, 1375, 2474, 36, 2401, 53 },
  { 216, 4030, 1107, 36, 2401, 53 },
  { 599, 1026, 4018, 212, 5314, 192 },
  { 717, 1014, 3953, 212, 5314, 192 },
  { 831, 1002, 4002, 212, 5314, 192 },
  { 937, 990, 3909, 212, 5314, 192 },
  { 1051, 978, 3909, 212, 5314, 192 },
  { 1157, 966, 3798, 212, 5314, 192 },
  { 3, 954, 3798, 212, 5314, 192 },
  { 148, 942, 3682, 212, 5314, 192 },
  { 275, 930, 3682, 212, 5314, 192 },
  { 401, 918, 3562, 212, 5314, 192 },
  { 515, 906, 3562, 212, 5314, 192 },
  { 641, 894, 3442, 212, 5314, 192 },
  { 760, 1070, 3442, 202, 17506, 199 },
  { 874, 1060, 3322, 202, 13426, 199 },
  { 980, 1052, 3322, 194, 14226, 205 },
  { 1094, 1044, 3226, 194, 13698, 205 },
  { 51, 1038, 3226, 188, 14049, 210 },
  { 200, 1038, 3131, 188, 14049, 210 },
  { 328, 1038, 3131, 188, 14049, 210 },
  { 448, 1038, 3061, 188, 14049, 210 },
  { 567, 1038, 3061, 188, 14049, 210 },
  { 689, 1038, 2991, 188, 14049, 210 },
  { 796, 1038, 2991, 188, 14049, 210 },
  { 906, 1038, 2921, 188, 14049, 210 },
  { 1016, 1038, 2921, 188, 14049, 210 },
  { 1126, 1038, 2832, 188, 14049, 210 },
  { 87, 1038, 2855, 188, 14049, 210 },
  { 232, 1038, 2794, 188, 14049, 210 },
  { 828, 2677, 4010, 276, 5170, 157 },
  { 934, 2659, 3951, 276, 5170, 157 },
  { 1048, 2641, 3951, 276, 5170, 157 },
  { 1154, 2623, 3842, 276, 5170, 157 },
  { 0, 2605, 3842, 276, 5170, 157 },
  { 145, 2587, 3743, 276, 5170, 157 },
  { 272, 2569, 3743, 276, 5170, 157 },
  { 398, 2551, 3625, 276, 5170, 157 },
  { 512, 2533, 3625, 276, 5170, 157 },
  { 638, 2515, 3505, 276, 5170, 157 },
  { 756, 2773, 3505, 260, 17378, 166 },
  { 870, 2757, 3385, 260, 13298, 166 },
  { 976, 2743, 3385, 246, 14114, 174 },
  { 1090, 2729, 3265, 246, 13586, 174 },
  { 47, 2717, 3265, 234, 13954, 181 },
  { 196, 2705, 3170, 234, 13778, 181 },
  { 324, 2695, 3170, 224, 13873, 187 },
  { 444, 2695, 3099, 224, 13873, 187 },
  { 563, 2695, 3099, 224, 13873, 187 },
  { 685, 2695, 3029, 224, 13873, 187 },
  { 792, 2695, 3029, 224, 13873, 187 },
  { 902, 2695, 2959, 224, 13873, 187 },
  { 1012, 2695, 2959, 224, 13873, 187 },
  { 1122, 2695, 2856, 224, 13873, 187 },
  { 83, 2695, 2856, 224, 13873, 187 },
  { 228, 2695, 2795, 224, 13873, 187 },
  { 369, 360, 2509, 22, 1956, 11 },
  { 614, 388, 583, 22, 1956, 11 },
  { 846, 416, 756, 22, 1956, 11 },
  { 1066, 444, 747, 22, 1956, 11 },
  { 19, 472, 738, 22, 1956, 11 },
  { 293, 500, 729, 22, 1956, 11 },
  { 535, 528, 720, 22, 1956, 11 },
  { 780, 3839, 711, 3, 2336, 16 },
  { 1000, 562, 702, 0, 8898, 20 },
  { 71, 565, 693, 0, 8898, 20 },
  { 348, 568, 684, 0, 8898, 20 },
  { 587, 571, 675, 0, 8898, 20 },
  { 816, 574, 666, 0, 8898, 20 },
  { 1036, 577, 2460, 0, 8898, 20 },
  { 107, 580, 2468, 0, 8898, 20 },
  { 608, 2343, 2488, 148, 900, 57 },
  { 840, 2323, 588, 148, 900, 57 },
  { 1060, 2303, 588, 148, 900, 57 },
  { 13, 2283, 588, 148, 900, 57 },
  { 286, 2263, 588, 148, 900, 57 },
  { 527, 2243, 588, 148, 900, 57 },
  { 772, 2225, 588, 130, 1328, 66 },
  { 992, 2211, 588, 116, 1776, 81 },
  { 63, 1588, 588, 104, 2034, 87 },
  { 340, 1576, 588, 104, 2034, 87 },
  { 579, 1564, 588, 104, 2034, 87 },
  { 808, 1552, 588, 104, 2034, 87 },
  { 1028, 1540, 588, 104, 2034, 87 },
  { 99, 1528, 2382, 104, 2034, 87 },
};

  // SPR Register Class...
  static MCPhysReg SPR[] = {
    ARM_S0, ARM_S2, ARM_S4, ARM_S6, ARM_S8, ARM_S10, ARM_S12, ARM_S14, ARM_S16, ARM_S18, ARM_S20, ARM_S22, ARM_S24, ARM_S26, ARM_S28, ARM_S30, ARM_S1, ARM_S3, ARM_S5, ARM_S7, ARM_S9, ARM_S11, ARM_S13, ARM_S15, ARM_S17, ARM_S19, ARM_S21, ARM_S23, ARM_S25, ARM_S27, ARM_S29, ARM_S31, 
  };

  // SPR Bit set.
  static const uint8_t SPRBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0xff, 0xff, 0xff, 0x7f, 
  };

  // GPR Register Class...
  static MCPhysReg GPR[] = {
    ARM_R0, ARM_R1, ARM_R2, ARM_R3, ARM_R4, ARM_R5, ARM_R6, ARM_R7, ARM_R8, ARM_R9, ARM_R10, ARM_R11, ARM_R12, ARM_SP, ARM_LR, ARM_PC, 
  };

  // GPR Bit set.
  static const uint8_t GPRBits[] = {
    0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0x7f, 
  };

  // GPRwithAPSR Register Class...
  static MCPhysReg GPRwithAPSR[] = {
    ARM_R0, ARM_R1, ARM_R2, ARM_R3, ARM_R4, ARM_R5, ARM_R6, ARM_R7, ARM_R8, ARM_R9, ARM_R10, ARM_R11, ARM_R12, ARM_SP, ARM_LR, ARM_APSR_NZCV, 
  };

  // GPRwithAPSR Bit set.
  static const uint8_t GPRwithAPSRBits[] = {
    0x04, 0x14, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0x7f, 
  };

  // SPR_8 Register Class...
  static MCPhysReg SPR_8[] = {
    ARM_S0, ARM_S1, ARM_S2, ARM_S3, ARM_S4, ARM_S5, ARM_S6, ARM_S7, ARM_S8, ARM_S9, ARM_S10, ARM_S11, ARM_S12, ARM_S13, ARM_S14, ARM_S15, 
  };

  // SPR_8 Bit set.
  static const uint8_t SPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0xff, 0x7f, 
  };

  // GPRnopc Register Class...
  static MCPhysReg GPRnopc[] = {
    ARM_R0, ARM_R1, ARM_R2, ARM_R3, ARM_R4, ARM_R5, ARM_R6, ARM_R7, ARM_R8, ARM_R9, ARM_R10, ARM_R11, ARM_R12, ARM_SP, ARM_LR, 
  };

  // GPRnopc Bit set.
  static const uint8_t GPRnopcBits[] = {
    0x00, 0x14, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0x7f, 
  };

  // rGPR Register Class...
  static MCPhysReg rGPR[] = {
    ARM_R0, ARM_R1, ARM_R2, ARM_R3, ARM_R4, ARM_R5, ARM_R6, ARM_R7, ARM_R8, ARM_R9, ARM_R10, ARM_R11, ARM_R12, ARM_LR, 
  };

  // rGPR Bit set.
  static const uint8_t rGPRBits[] = {
    0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0x7f, 
  };

  // hGPR Register Class...
  static MCPhysReg hGPR[] = {
    ARM_R8, ARM_R9, ARM_R10, ARM_R11, ARM_R12, ARM_SP, ARM_LR, ARM_PC, 
  };

  // hGPR Bit set.
  static const uint8_t hGPRBits[] = {
    0x00, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7c, 
  };

  // tGPR Register Class...
  static MCPhysReg tGPR[] = {
    ARM_R0, ARM_R1, ARM_R2, ARM_R3, ARM_R4, ARM_R5, ARM_R6, ARM_R7, 
  };

  // tGPR Bit set.
  static const uint8_t tGPRBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0x03, 
  };

  // GPRnopc_and_hGPR Register Class...
  static MCPhysReg GPRnopc_and_hGPR[] = {
    ARM_R8, ARM_R9, ARM_R10, ARM_R11, ARM_R12, ARM_SP, ARM_LR, 
  };

  // GPRnopc_and_hGPR Bit set.
  static const uint8_t GPRnopc_and_hGPRBits[] = {
    0x00, 0x14, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7c, 
  };

  // hGPR_and_rGPR Register Class...
  static MCPhysReg hGPR_and_rGPR[] = {
    ARM_R8, ARM_R9, ARM_R10, ARM_R11, ARM_R12, ARM_LR, 
  };

  // hGPR_and_rGPR Bit set.
  static const uint8_t hGPR_and_rGPRBits[] = {
    0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7c, 
  };

  // tcGPR Register Class...
  static MCPhysReg tcGPR[] = {
    ARM_R0, ARM_R1, ARM_R2, ARM_R3, ARM_R12, 
  };

  // tcGPR Bit set.
  static const uint8_t tcGPRBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3c, 0x40, 
  };

  // tGPR_and_tcGPR Register Class...
  static MCPhysReg tGPR_and_tcGPR[] = {
    ARM_R0, ARM_R1, ARM_R2, ARM_R3, 
  };

  // tGPR_and_tcGPR Bit set.
  static const uint8_t tGPR_and_tcGPRBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3c, 
  };

  // CCR Register Class...
  static MCPhysReg CCR[] = {
    ARM_CPSR, 
  };

  // CCR Bit set.
  static const uint8_t CCRBits[] = {
    0x08, 
  };

  // GPRsp Register Class...
  static MCPhysReg GPRsp[] = {
    ARM_SP, 
  };

  // GPRsp Bit set.
  static const uint8_t GPRspBits[] = {
    0x00, 0x10, 
  };

  // hGPR_and_tcGPR Register Class...
  static MCPhysReg hGPR_and_tcGPR[] = {
    ARM_R12, 
  };

  // hGPR_and_tcGPR Bit set.
  static const uint8_t hGPR_and_tcGPRBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 
  };

  // DPR Register Class...
  static MCPhysReg DPR[] = {
    ARM_D0, ARM_D1, ARM_D2, ARM_D3, ARM_D4, ARM_D5, ARM_D6, ARM_D7, ARM_D8, ARM_D9, ARM_D10, ARM_D11, ARM_D12, ARM_D13, ARM_D14, ARM_D15, ARM_D16, ARM_D17, ARM_D18, ARM_D19, ARM_D20, ARM_D21, ARM_D22, ARM_D23, ARM_D24, ARM_D25, ARM_D26, ARM_D27, ARM_D28, ARM_D29, ARM_D30, ARM_D31, 
  };

  // DPR Bit set.
  static const uint8_t DPRBits[] = {
    0x00, 0xc0, 0xff, 0xff, 0xff, 0x3f, 
  };

  // DPR_VFP2 Register Class...
  static MCPhysReg DPR_VFP2[] = {
    ARM_D0, ARM_D1, ARM_D2, ARM_D3, ARM_D4, ARM_D5, ARM_D6, ARM_D7, ARM_D8, ARM_D9, ARM_D10, ARM_D11, ARM_D12, ARM_D13, ARM_D14, ARM_D15, 
  };

  // DPR_VFP2 Bit set.
  static const uint8_t DPR_VFP2Bits[] = {
    0x00, 0xc0, 0xff, 0x3f, 
  };

  // DPR_8 Register Class...
  static MCPhysReg DPR_8[] = {
    ARM_D0, ARM_D1, ARM_D2, ARM_D3, ARM_D4, ARM_D5, ARM_D6, ARM_D7, 
  };

  // DPR_8 Bit set.
  static const uint8_t DPR_8Bits[] = {
    0x00, 0xc0, 0x3f, 
  };

  // GPRPair Register Class...
  static MCPhysReg GPRPair[] = {
    ARM_R0_R1, ARM_R2_R3, ARM_R4_R5, ARM_R6_R7, ARM_R8_R9, ARM_R10_R11, ARM_R12_SP, 
  };

  // GPRPair Bit set.
  static const uint8_t GPRPairBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 
  };

  // GPRPair_with_gsub_1_in_rGPR Register Class...
  static MCPhysReg GPRPair_with_gsub_1_in_rGPR[] = {
    ARM_R0_R1, ARM_R2_R3, ARM_R4_R5, ARM_R6_R7, ARM_R8_R9, ARM_R10_R11, 
  };

  // GPRPair_with_gsub_1_in_rGPR Bit set.
  static const uint8_t GPRPair_with_gsub_1_in_rGPRBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 
  };

  // GPRPair_with_gsub_0_in_tGPR Register Class...
  static MCPhysReg GPRPair_with_gsub_0_in_tGPR[] = {
    ARM_R0_R1, ARM_R2_R3, ARM_R4_R5, ARM_R6_R7, 
  };

  // GPRPair_with_gsub_0_in_tGPR Bit set.
  static const uint8_t GPRPair_with_gsub_0_in_tGPRBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3c, 
  };

  // GPRPair_with_gsub_0_in_hGPR Register Class...
  static MCPhysReg GPRPair_with_gsub_0_in_hGPR[] = {
    ARM_R8_R9, ARM_R10_R11, ARM_R12_SP, 
  };

  // GPRPair_with_gsub_0_in_hGPR Bit set.
  static const uint8_t GPRPair_with_gsub_0_in_hGPRBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc2, 
  };

  // GPRPair_with_gsub_0_in_tcGPR Register Class...
  static MCPhysReg GPRPair_with_gsub_0_in_tcGPR[] = {
    ARM_R0_R1, ARM_R2_R3, ARM_R12_SP, 
  };

  // GPRPair_with_gsub_0_in_tcGPR Bit set.
  static const uint8_t GPRPair_with_gsub_0_in_tcGPRBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 
  };

  // GPRPair_with_gsub_1_in_hGPR_and_rGPR Register Class...
  static MCPhysReg GPRPair_with_gsub_1_in_hGPR_and_rGPR[] = {
    ARM_R8_R9, ARM_R10_R11, 
  };

  // GPRPair_with_gsub_1_in_hGPR_and_rGPR Bit set.
  static const uint8_t GPRPair_with_gsub_1_in_hGPR_and_rGPRBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 
  };

  // GPRPair_with_gsub_1_in_tcGPR Register Class...
  static MCPhysReg GPRPair_with_gsub_1_in_tcGPR[] = {
    ARM_R0_R1, ARM_R2_R3, 
  };

  // GPRPair_with_gsub_1_in_tcGPR Bit set.
  static const uint8_t GPRPair_with_gsub_1_in_tcGPRBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 
  };

  // GPRPair_with_gsub_1_in_GPRsp Register Class...
  static MCPhysReg GPRPair_with_gsub_1_in_GPRsp[] = {
    ARM_R12_SP, 
  };

  // GPRPair_with_gsub_1_in_GPRsp Bit set.
  static const uint8_t GPRPair_with_gsub_1_in_GPRspBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 
  };

  // DPairSpc Register Class...
  static MCPhysReg DPairSpc[] = {
    ARM_D0_D2, ARM_D1_D3, ARM_D2_D4, ARM_D3_D5, ARM_D4_D6, ARM_D5_D7, ARM_D6_D8, ARM_D7_D9, ARM_D8_D10, ARM_D9_D11, ARM_D10_D12, ARM_D11_D13, ARM_D12_D14, ARM_D13_D15, ARM_D14_D16, ARM_D15_D17, ARM_D16_D18, ARM_D17_D19, ARM_D18_D20, ARM_D19_D21, ARM_D20_D22, ARM_D21_D23, ARM_D22_D24, ARM_D23_D25, ARM_D24_D26, ARM_D25_D27, ARM_D26_D28, ARM_D27_D29, ARM_D28_D30, ARM_D29_D31, 
  };

  // DPairSpc Bit set.
  static const uint8_t DPairSpcBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0xff, 0xff, 0xff, 0x1f, 
  };

  // DPairSpc_with_ssub_0 Register Class...
  static MCPhysReg DPairSpc_with_ssub_0[] = {
    ARM_D0_D2, ARM_D1_D3, ARM_D2_D4, ARM_D3_D5, ARM_D4_D6, ARM_D5_D7, ARM_D6_D8, ARM_D7_D9, ARM_D8_D10, ARM_D9_D11, ARM_D10_D12, ARM_D11_D13, ARM_D12_D14, ARM_D13_D15, ARM_D14_D16, ARM_D15_D17, 
  };

  // DPairSpc_with_ssub_0 Bit set.
  static const uint8_t DPairSpc_with_ssub_0Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0xff, 0x7f, 
  };

  // DPairSpc_with_dsub_2_then_ssub_0 Register Class...
  static MCPhysReg DPairSpc_with_dsub_2_then_ssub_0[] = {
    ARM_D0_D2, ARM_D1_D3, ARM_D2_D4, ARM_D3_D5, ARM_D4_D6, ARM_D5_D7, ARM_D6_D8, ARM_D7_D9, ARM_D8_D10, ARM_D9_D11, ARM_D10_D12, ARM_D11_D13, ARM_D12_D14, ARM_D13_D15, 
  };

  // DPairSpc_with_dsub_2_then_ssub_0 Bit set.
  static const uint8_t DPairSpc_with_dsub_2_then_ssub_0Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0xff, 0x1f, 
  };

  // DPairSpc_with_dsub_0_in_DPR_8 Register Class...
  static MCPhysReg DPairSpc_with_dsub_0_in_DPR_8[] = {
    ARM_D0_D2, ARM_D1_D3, ARM_D2_D4, ARM_D3_D5, ARM_D4_D6, ARM_D5_D7, ARM_D6_D8, ARM_D7_D9, 
  };

  // DPairSpc_with_dsub_0_in_DPR_8 Bit set.
  static const uint8_t DPairSpc_with_dsub_0_in_DPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x7f, 
  };

  // DPairSpc_with_dsub_2_in_DPR_8 Register Class...
  static MCPhysReg DPairSpc_with_dsub_2_in_DPR_8[] = {
    ARM_D0_D2, ARM_D1_D3, ARM_D2_D4, ARM_D3_D5, ARM_D4_D6, ARM_D5_D7, 
  };

  // DPairSpc_with_dsub_2_in_DPR_8 Bit set.
  static const uint8_t DPairSpc_with_dsub_2_in_DPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x1f, 
  };

  // DPair Register Class...
  static MCPhysReg DPair[] = {
    ARM_Q0, ARM_D1_D2, ARM_Q1, ARM_D3_D4, ARM_Q2, ARM_D5_D6, ARM_Q3, ARM_D7_D8, ARM_Q4, ARM_D9_D10, ARM_Q5, ARM_D11_D12, ARM_Q6, ARM_D13_D14, ARM_Q7, ARM_D15_D16, ARM_Q8, ARM_D17_D18, ARM_Q9, ARM_D19_D20, ARM_Q10, ARM_D21_D22, ARM_Q11, ARM_D23_D24, ARM_Q12, ARM_D25_D26, ARM_Q13, ARM_D27_D28, ARM_Q14, ARM_D29_D30, ARM_Q15, 
  };

  // DPair Bit set.
  static const uint8_t DPairBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0x07, 
  };

  // DPair_with_ssub_0 Register Class...
  static MCPhysReg DPair_with_ssub_0[] = {
    ARM_Q0, ARM_D1_D2, ARM_Q1, ARM_D3_D4, ARM_Q2, ARM_D5_D6, ARM_Q3, ARM_D7_D8, ARM_Q4, ARM_D9_D10, ARM_Q5, ARM_D11_D12, ARM_Q6, ARM_D13_D14, ARM_Q7, ARM_D15_D16, 
  };

  // DPair_with_ssub_0 Bit set.
  static const uint8_t DPair_with_ssub_0Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x0f, 
  };

  // QPR Register Class...
  static MCPhysReg QPR[] = {
    ARM_Q0, ARM_Q1, ARM_Q2, ARM_Q3, ARM_Q4, ARM_Q5, ARM_Q6, ARM_Q7, ARM_Q8, ARM_Q9, ARM_Q10, ARM_Q11, ARM_Q12, ARM_Q13, ARM_Q14, ARM_Q15, 
  };

  // QPR Bit set.
  static const uint8_t QPRBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0xff, 0x03, 
  };

  // DPair_with_ssub_2 Register Class...
  static MCPhysReg DPair_with_ssub_2[] = {
    ARM_Q0, ARM_D1_D2, ARM_Q1, ARM_D3_D4, ARM_Q2, ARM_D5_D6, ARM_Q3, ARM_D7_D8, ARM_Q4, ARM_D9_D10, ARM_Q5, ARM_D11_D12, ARM_Q6, ARM_D13_D14, ARM_Q7, 
  };

  // DPair_with_ssub_2 Bit set.
  static const uint8_t DPair_with_ssub_2Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x07, 
  };

  // DPair_with_dsub_0_in_DPR_8 Register Class...
  static MCPhysReg DPair_with_dsub_0_in_DPR_8[] = {
    ARM_Q0, ARM_D1_D2, ARM_Q1, ARM_D3_D4, ARM_Q2, ARM_D5_D6, ARM_Q3, ARM_D7_D8, 
  };

  // DPair_with_dsub_0_in_DPR_8 Bit set.
  static const uint8_t DPair_with_dsub_0_in_DPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 
  };

  // QPR_VFP2 Register Class...
  static MCPhysReg QPR_VFP2[] = {
    ARM_Q0, ARM_Q1, ARM_Q2, ARM_Q3, ARM_Q4, ARM_Q5, ARM_Q6, ARM_Q7, 
  };

  // QPR_VFP2 Bit set.
  static const uint8_t QPR_VFP2Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc, 0x03, 
  };

  // DPair_with_dsub_1_in_DPR_8 Register Class...
  static MCPhysReg DPair_with_dsub_1_in_DPR_8[] = {
    ARM_Q0, ARM_D1_D2, ARM_Q1, ARM_D3_D4, ARM_Q2, ARM_D5_D6, ARM_Q3, 
  };

  // DPair_with_dsub_1_in_DPR_8 Bit set.
  static const uint8_t DPair_with_dsub_1_in_DPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 
  };

  // QPR_8 Register Class...
  static MCPhysReg QPR_8[] = {
    ARM_Q0, ARM_Q1, ARM_Q2, ARM_Q3, 
  };

  // QPR_8 Bit set.
  static const uint8_t QPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3c, 
  };

  // DTriple Register Class...
  static MCPhysReg DTriple[] = {
    ARM_D0_D1_D2, ARM_D1_D2_D3, ARM_D2_D3_D4, ARM_D3_D4_D5, ARM_D4_D5_D6, ARM_D5_D6_D7, ARM_D6_D7_D8, ARM_D7_D8_D9, ARM_D8_D9_D10, ARM_D9_D10_D11, ARM_D10_D11_D12, ARM_D11_D12_D13, ARM_D12_D13_D14, ARM_D13_D14_D15, ARM_D14_D15_D16, ARM_D15_D16_D17, ARM_D16_D17_D18, ARM_D17_D18_D19, ARM_D18_D19_D20, ARM_D19_D20_D21, ARM_D20_D21_D22, ARM_D21_D22_D23, ARM_D22_D23_D24, ARM_D23_D24_D25, ARM_D24_D25_D26, ARM_D25_D26_D27, ARM_D26_D27_D28, ARM_D27_D28_D29, ARM_D28_D29_D30, ARM_D29_D30_D31, 
  };

  // DTriple Bit set.
  static const uint8_t DTripleBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x3f, 
  };

  // DTripleSpc Register Class...
  static MCPhysReg DTripleSpc[] = {
    ARM_D0_D2_D4, ARM_D1_D3_D5, ARM_D2_D4_D6, ARM_D3_D5_D7, ARM_D4_D6_D8, ARM_D5_D7_D9, ARM_D6_D8_D10, ARM_D7_D9_D11, ARM_D8_D10_D12, ARM_D9_D11_D13, ARM_D10_D12_D14, ARM_D11_D13_D15, ARM_D12_D14_D16, ARM_D13_D15_D17, ARM_D14_D16_D18, ARM_D15_D17_D19, ARM_D16_D18_D20, ARM_D17_D19_D21, ARM_D18_D20_D22, ARM_D19_D21_D23, ARM_D20_D22_D24, ARM_D21_D23_D25, ARM_D22_D24_D26, ARM_D23_D25_D27, ARM_D24_D26_D28, ARM_D25_D27_D29, ARM_D26_D28_D30, ARM_D27_D29_D31, 
  };

  // DTripleSpc Bit set.
  static const uint8_t DTripleSpcBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0xff, 0xff, 0xff, 0x03, 
  };

  // DTripleSpc_with_ssub_0 Register Class...
  static MCPhysReg DTripleSpc_with_ssub_0[] = {
    ARM_D0_D2_D4, ARM_D1_D3_D5, ARM_D2_D4_D6, ARM_D3_D5_D7, ARM_D4_D6_D8, ARM_D5_D7_D9, ARM_D6_D8_D10, ARM_D7_D9_D11, ARM_D8_D10_D12, ARM_D9_D11_D13, ARM_D10_D12_D14, ARM_D11_D13_D15, ARM_D12_D14_D16, ARM_D13_D15_D17, ARM_D14_D16_D18, ARM_D15_D17_D19, 
  };

  // DTripleSpc_with_ssub_0 Bit set.
  static const uint8_t DTripleSpc_with_ssub_0Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0xff, 0x3f, 
  };

  // DTriple_with_ssub_0 Register Class...
  static MCPhysReg DTriple_with_ssub_0[] = {
    ARM_D0_D1_D2, ARM_D1_D2_D3, ARM_D2_D3_D4, ARM_D3_D4_D5, ARM_D4_D5_D6, ARM_D5_D6_D7, ARM_D6_D7_D8, ARM_D7_D8_D9, ARM_D8_D9_D10, ARM_D9_D10_D11, ARM_D10_D11_D12, ARM_D11_D12_D13, ARM_D12_D13_D14, ARM_D13_D14_D15, ARM_D14_D15_D16, ARM_D15_D16_D17, 
  };

  // DTriple_with_ssub_0 Bit set.
  static const uint8_t DTriple_with_ssub_0Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 
  };

  // DTriple_with_dsub_1_dsub_2_in_QPR Register Class...
  static MCPhysReg DTriple_with_dsub_1_dsub_2_in_QPR[] = {
    ARM_D1_D2_D3, ARM_D3_D4_D5, ARM_D5_D6_D7, ARM_D7_D8_D9, ARM_D9_D10_D11, ARM_D11_D12_D13, ARM_D13_D14_D15, ARM_D15_D16_D17, ARM_D17_D18_D19, ARM_D19_D20_D21, ARM_D21_D22_D23, ARM_D23_D24_D25, ARM_D25_D26_D27, ARM_D27_D28_D29, ARM_D29_D30_D31, 
  };

  // DTriple_with_dsub_1_dsub_2_in_QPR Bit set.
  static const uint8_t DTriple_with_dsub_1_dsub_2_in_QPRBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xaa, 0xaa, 0xaa, 0x2a, 
  };

  // DTriple_with_qsub_0_in_QPR Register Class...
  static MCPhysReg DTriple_with_qsub_0_in_QPR[] = {
    ARM_D0_D1_D2, ARM_D2_D3_D4, ARM_D4_D5_D6, ARM_D6_D7_D8, ARM_D8_D9_D10, ARM_D10_D11_D12, ARM_D12_D13_D14, ARM_D14_D15_D16, ARM_D16_D17_D18, ARM_D18_D19_D20, ARM_D20_D21_D22, ARM_D22_D23_D24, ARM_D24_D25_D26, ARM_D26_D27_D28, ARM_D28_D29_D30, 
  };

  // DTriple_with_qsub_0_in_QPR Bit set.
  static const uint8_t DTriple_with_qsub_0_in_QPRBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x55, 0x55, 0x15, 
  };

  // DTriple_with_ssub_2 Register Class...
  static MCPhysReg DTriple_with_ssub_2[] = {
    ARM_D0_D1_D2, ARM_D1_D2_D3, ARM_D2_D3_D4, ARM_D3_D4_D5, ARM_D4_D5_D6, ARM_D5_D6_D7, ARM_D6_D7_D8, ARM_D7_D8_D9, ARM_D8_D9_D10, ARM_D9_D10_D11, ARM_D10_D11_D12, ARM_D11_D12_D13, ARM_D12_D13_D14, ARM_D13_D14_D15, ARM_D14_D15_D16, 
  };

  // DTriple_with_ssub_2 Bit set.
  static const uint8_t DTriple_with_ssub_2Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x7f, 
  };

  // DTripleSpc_with_dsub_2_then_ssub_0 Register Class...
  static MCPhysReg DTripleSpc_with_dsub_2_then_ssub_0[] = {
    ARM_D0_D2_D4, ARM_D1_D3_D5, ARM_D2_D4_D6, ARM_D3_D5_D7, ARM_D4_D6_D8, ARM_D5_D7_D9, ARM_D6_D8_D10, ARM_D7_D9_D11, ARM_D8_D10_D12, ARM_D9_D11_D13, ARM_D10_D12_D14, ARM_D11_D13_D15, ARM_D12_D14_D16, ARM_D13_D15_D17, 
  };

  // DTripleSpc_with_dsub_2_then_ssub_0 Bit set.
  static const uint8_t DTripleSpc_with_dsub_2_then_ssub_0Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0xff, 0x0f, 
  };

  // DTriple_with_dsub_2_then_ssub_0 Register Class...
  static MCPhysReg DTriple_with_dsub_2_then_ssub_0[] = {
    ARM_D0_D1_D2, ARM_D1_D2_D3, ARM_D2_D3_D4, ARM_D3_D4_D5, ARM_D4_D5_D6, ARM_D5_D6_D7, ARM_D6_D7_D8, ARM_D7_D8_D9, ARM_D8_D9_D10, ARM_D9_D10_D11, ARM_D10_D11_D12, ARM_D11_D12_D13, ARM_D12_D13_D14, ARM_D13_D14_D15, 
  };

  // DTriple_with_dsub_2_then_ssub_0 Bit set.
  static const uint8_t DTriple_with_dsub_2_then_ssub_0Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x3f, 
  };

  // DTripleSpc_with_dsub_4_then_ssub_0 Register Class...
  static MCPhysReg DTripleSpc_with_dsub_4_then_ssub_0[] = {
    ARM_D0_D2_D4, ARM_D1_D3_D5, ARM_D2_D4_D6, ARM_D3_D5_D7, ARM_D4_D6_D8, ARM_D5_D7_D9, ARM_D6_D8_D10, ARM_D7_D9_D11, ARM_D8_D10_D12, ARM_D9_D11_D13, ARM_D10_D12_D14, ARM_D11_D13_D15, 
  };

  // DTripleSpc_with_dsub_4_then_ssub_0 Bit set.
  static const uint8_t DTripleSpc_with_dsub_4_then_ssub_0Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0xff, 0x03, 
  };

  // DTripleSpc_with_dsub_0_in_DPR_8 Register Class...
  static MCPhysReg DTripleSpc_with_dsub_0_in_DPR_8[] = {
    ARM_D0_D2_D4, ARM_D1_D3_D5, ARM_D2_D4_D6, ARM_D3_D5_D7, ARM_D4_D6_D8, ARM_D5_D7_D9, ARM_D6_D8_D10, ARM_D7_D9_D11, 
  };

  // DTripleSpc_with_dsub_0_in_DPR_8 Bit set.
  static const uint8_t DTripleSpc_with_dsub_0_in_DPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // DTriple_with_dsub_0_in_DPR_8 Register Class...
  static MCPhysReg DTriple_with_dsub_0_in_DPR_8[] = {
    ARM_D0_D1_D2, ARM_D1_D2_D3, ARM_D2_D3_D4, ARM_D3_D4_D5, ARM_D4_D5_D6, ARM_D5_D6_D7, ARM_D6_D7_D8, ARM_D7_D8_D9, 
  };

  // DTriple_with_dsub_0_in_DPR_8 Bit set.
  static const uint8_t DTriple_with_dsub_0_in_DPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 
  };

  // DTriple_with_qsub_0_in_QPR_VFP2 Register Class...
  static MCPhysReg DTriple_with_qsub_0_in_QPR_VFP2[] = {
    ARM_D0_D1_D2, ARM_D2_D3_D4, ARM_D4_D5_D6, ARM_D6_D7_D8, ARM_D8_D9_D10, ARM_D10_D11_D12, ARM_D12_D13_D14, ARM_D14_D15_D16, 
  };

  // DTriple_with_qsub_0_in_QPR_VFP2 Bit set.
  static const uint8_t DTriple_with_qsub_0_in_QPR_VFP2Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x55, 
  };

  // DTriple_with_ssub_0_and_DTriple_with_dsub_1_dsub_2_in_QPR Register Class...
  static MCPhysReg DTriple_with_ssub_0_and_DTriple_with_dsub_1_dsub_2_in_QPR[] = {
    ARM_D1_D2_D3, ARM_D3_D4_D5, ARM_D5_D6_D7, ARM_D7_D8_D9, ARM_D9_D10_D11, ARM_D11_D12_D13, ARM_D13_D14_D15, ARM_D15_D16_D17, 
  };

  // DTriple_with_ssub_0_and_DTriple_with_dsub_1_dsub_2_in_QPR Bit set.
  static const uint8_t DTriple_with_ssub_0_and_DTriple_with_dsub_1_dsub_2_in_QPRBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xaa, 0xaa, 
  };

  // DTriple_with_dsub_1_dsub_2_in_QPR_VFP2 Register Class...
  static MCPhysReg DTriple_with_dsub_1_dsub_2_in_QPR_VFP2[] = {
    ARM_D1_D2_D3, ARM_D3_D4_D5, ARM_D5_D6_D7, ARM_D7_D8_D9, ARM_D9_D10_D11, ARM_D11_D12_D13, ARM_D13_D14_D15, 
  };

  // DTriple_with_dsub_1_dsub_2_in_QPR_VFP2 Bit set.
  static const uint8_t DTriple_with_dsub_1_dsub_2_in_QPR_VFP2Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xaa, 0x2a, 
  };

  // DTriple_with_dsub_1_in_DPR_8 Register Class...
  static MCPhysReg DTriple_with_dsub_1_in_DPR_8[] = {
    ARM_D0_D1_D2, ARM_D1_D2_D3, ARM_D2_D3_D4, ARM_D3_D4_D5, ARM_D4_D5_D6, ARM_D5_D6_D7, ARM_D6_D7_D8, 
  };

  // DTriple_with_dsub_1_in_DPR_8 Bit set.
  static const uint8_t DTriple_with_dsub_1_in_DPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 
  };

  // DTriple_with_dsub_2_then_ssub_0_and_DTriple_with_qsub_0_in_QPR Register Class...
  static MCPhysReg DTriple_with_dsub_2_then_ssub_0_and_DTriple_with_qsub_0_in_QPR[] = {
    ARM_D0_D1_D2, ARM_D2_D3_D4, ARM_D4_D5_D6, ARM_D6_D7_D8, ARM_D8_D9_D10, ARM_D10_D11_D12, ARM_D12_D13_D14, 
  };

  // DTriple_with_dsub_2_then_ssub_0_and_DTriple_with_qsub_0_in_QPR Bit set.
  static const uint8_t DTriple_with_dsub_2_then_ssub_0_and_DTriple_with_qsub_0_in_QPRBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x15, 
  };

  // DTripleSpc_with_dsub_2_in_DPR_8 Register Class...
  static MCPhysReg DTripleSpc_with_dsub_2_in_DPR_8[] = {
    ARM_D0_D2_D4, ARM_D1_D3_D5, ARM_D2_D4_D6, ARM_D3_D5_D7, ARM_D4_D6_D8, ARM_D5_D7_D9, 
  };

  // DTripleSpc_with_dsub_2_in_DPR_8 Bit set.
  static const uint8_t DTripleSpc_with_dsub_2_in_DPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x0f, 
  };

  // DTriple_with_dsub_2_in_DPR_8 Register Class...
  static MCPhysReg DTriple_with_dsub_2_in_DPR_8[] = {
    ARM_D0_D1_D2, ARM_D1_D2_D3, ARM_D2_D3_D4, ARM_D3_D4_D5, ARM_D4_D5_D6, ARM_D5_D6_D7, 
  };

  // DTriple_with_dsub_2_in_DPR_8 Bit set.
  static const uint8_t DTriple_with_dsub_2_in_DPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 
  };

  // DTripleSpc_with_dsub_4_in_DPR_8 Register Class...
  static MCPhysReg DTripleSpc_with_dsub_4_in_DPR_8[] = {
    ARM_D0_D2_D4, ARM_D1_D3_D5, ARM_D2_D4_D6, ARM_D3_D5_D7, 
  };

  // DTripleSpc_with_dsub_4_in_DPR_8 Bit set.
  static const uint8_t DTripleSpc_with_dsub_4_in_DPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x03, 
  };

  // DTriple_with_dsub_0_in_DPR_8_and_DTriple_with_dsub_1_dsub_2_in_QPR Register Class...
  static MCPhysReg DTriple_with_dsub_0_in_DPR_8_and_DTriple_with_dsub_1_dsub_2_in_QPR[] = {
    ARM_D1_D2_D3, ARM_D3_D4_D5, ARM_D5_D6_D7, ARM_D7_D8_D9, 
  };

  // DTriple_with_dsub_0_in_DPR_8_and_DTriple_with_dsub_1_dsub_2_in_QPR Bit set.
  static const uint8_t DTriple_with_dsub_0_in_DPR_8_and_DTriple_with_dsub_1_dsub_2_in_QPRBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xaa, 
  };

  // DTriple_with_qsub_0_in_QPR_8 Register Class...
  static MCPhysReg DTriple_with_qsub_0_in_QPR_8[] = {
    ARM_D0_D1_D2, ARM_D2_D3_D4, ARM_D4_D5_D6, ARM_D6_D7_D8, 
  };

  // DTriple_with_qsub_0_in_QPR_8 Bit set.
  static const uint8_t DTriple_with_qsub_0_in_QPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 
  };

  // DTriple_with_dsub_1_dsub_2_in_QPR_8 Register Class...
  static MCPhysReg DTriple_with_dsub_1_dsub_2_in_QPR_8[] = {
    ARM_D1_D2_D3, ARM_D3_D4_D5, ARM_D5_D6_D7, 
  };

  // DTriple_with_dsub_1_dsub_2_in_QPR_8 Bit set.
  static const uint8_t DTriple_with_dsub_1_dsub_2_in_QPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2a, 
  };

  // DTriple_with_dsub_2_in_DPR_8_and_DTriple_with_qsub_0_in_QPR Register Class...
  static MCPhysReg DTriple_with_dsub_2_in_DPR_8_and_DTriple_with_qsub_0_in_QPR[] = {
    ARM_D0_D1_D2, ARM_D2_D3_D4, ARM_D4_D5_D6, 
  };

  // DTriple_with_dsub_2_in_DPR_8_and_DTriple_with_qsub_0_in_QPR Bit set.
  static const uint8_t DTriple_with_dsub_2_in_DPR_8_and_DTriple_with_qsub_0_in_QPRBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 
  };

  // DQuadSpc Register Class...
  static MCPhysReg DQuadSpc[] = {
    ARM_D0_D2_D4, ARM_D1_D3_D5, ARM_D2_D4_D6, ARM_D3_D5_D7, ARM_D4_D6_D8, ARM_D5_D7_D9, ARM_D6_D8_D10, ARM_D7_D9_D11, ARM_D8_D10_D12, ARM_D9_D11_D13, ARM_D10_D12_D14, ARM_D11_D13_D15, ARM_D12_D14_D16, ARM_D13_D15_D17, ARM_D14_D16_D18, ARM_D15_D17_D19, ARM_D16_D18_D20, ARM_D17_D19_D21, ARM_D18_D20_D22, ARM_D19_D21_D23, ARM_D20_D22_D24, ARM_D21_D23_D25, ARM_D22_D24_D26, ARM_D23_D25_D27, ARM_D24_D26_D28, ARM_D25_D27_D29, ARM_D26_D28_D30, ARM_D27_D29_D31, 
  };

  // DQuadSpc Bit set.
  static const uint8_t DQuadSpcBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0xff, 0xff, 0xff, 0x03, 
  };

  // DQuadSpc_with_ssub_0 Register Class...
  static MCPhysReg DQuadSpc_with_ssub_0[] = {
    ARM_D0_D2_D4, ARM_D1_D3_D5, ARM_D2_D4_D6, ARM_D3_D5_D7, ARM_D4_D6_D8, ARM_D5_D7_D9, ARM_D6_D8_D10, ARM_D7_D9_D11, ARM_D8_D10_D12, ARM_D9_D11_D13, ARM_D10_D12_D14, ARM_D11_D13_D15, ARM_D12_D14_D16, ARM_D13_D15_D17, ARM_D14_D16_D18, ARM_D15_D17_D19, 
  };

  // DQuadSpc_with_ssub_0 Bit set.
  static const uint8_t DQuadSpc_with_ssub_0Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0xff, 0x3f, 
  };

  // DQuadSpc_with_dsub_2_then_ssub_0 Register Class...
  static MCPhysReg DQuadSpc_with_dsub_2_then_ssub_0[] = {
    ARM_D0_D2_D4, ARM_D1_D3_D5, ARM_D2_D4_D6, ARM_D3_D5_D7, ARM_D4_D6_D8, ARM_D5_D7_D9, ARM_D6_D8_D10, ARM_D7_D9_D11, ARM_D8_D10_D12, ARM_D9_D11_D13, ARM_D10_D12_D14, ARM_D11_D13_D15, ARM_D12_D14_D16, ARM_D13_D15_D17, 
  };

  // DQuadSpc_with_dsub_2_then_ssub_0 Bit set.
  static const uint8_t DQuadSpc_with_dsub_2_then_ssub_0Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0xff, 0x0f, 
  };

  // DQuadSpc_with_dsub_4_then_ssub_0 Register Class...
  static MCPhysReg DQuadSpc_with_dsub_4_then_ssub_0[] = {
    ARM_D0_D2_D4, ARM_D1_D3_D5, ARM_D2_D4_D6, ARM_D3_D5_D7, ARM_D4_D6_D8, ARM_D5_D7_D9, ARM_D6_D8_D10, ARM_D7_D9_D11, ARM_D8_D10_D12, ARM_D9_D11_D13, ARM_D10_D12_D14, ARM_D11_D13_D15, 
  };

  // DQuadSpc_with_dsub_4_then_ssub_0 Bit set.
  static const uint8_t DQuadSpc_with_dsub_4_then_ssub_0Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0xff, 0x03, 
  };

  // DQuadSpc_with_dsub_0_in_DPR_8 Register Class...
  static MCPhysReg DQuadSpc_with_dsub_0_in_DPR_8[] = {
    ARM_D0_D2_D4, ARM_D1_D3_D5, ARM_D2_D4_D6, ARM_D3_D5_D7, ARM_D4_D6_D8, ARM_D5_D7_D9, ARM_D6_D8_D10, ARM_D7_D9_D11, 
  };

  // DQuadSpc_with_dsub_0_in_DPR_8 Bit set.
  static const uint8_t DQuadSpc_with_dsub_0_in_DPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x3f, 
  };

  // DQuadSpc_with_dsub_2_in_DPR_8 Register Class...
  static MCPhysReg DQuadSpc_with_dsub_2_in_DPR_8[] = {
    ARM_D0_D2_D4, ARM_D1_D3_D5, ARM_D2_D4_D6, ARM_D3_D5_D7, ARM_D4_D6_D8, ARM_D5_D7_D9, 
  };

  // DQuadSpc_with_dsub_2_in_DPR_8 Bit set.
  static const uint8_t DQuadSpc_with_dsub_2_in_DPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x0f, 
  };

  // DQuadSpc_with_dsub_4_in_DPR_8 Register Class...
  static MCPhysReg DQuadSpc_with_dsub_4_in_DPR_8[] = {
    ARM_D0_D2_D4, ARM_D1_D3_D5, ARM_D2_D4_D6, ARM_D3_D5_D7, 
  };

  // DQuadSpc_with_dsub_4_in_DPR_8 Bit set.
  static const uint8_t DQuadSpc_with_dsub_4_in_DPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x03, 
  };

  // DQuad Register Class...
  static MCPhysReg DQuad[] = {
    ARM_Q0_Q1, ARM_D1_D2_D3_D4, ARM_Q1_Q2, ARM_D3_D4_D5_D6, ARM_Q2_Q3, ARM_D5_D6_D7_D8, ARM_Q3_Q4, ARM_D7_D8_D9_D10, ARM_Q4_Q5, ARM_D9_D10_D11_D12, ARM_Q5_Q6, ARM_D11_D12_D13_D14, ARM_Q6_Q7, ARM_D13_D14_D15_D16, ARM_Q7_Q8, ARM_D15_D16_D17_D18, ARM_Q8_Q9, ARM_D17_D18_D19_D20, ARM_Q9_Q10, ARM_D19_D20_D21_D22, ARM_Q10_Q11, ARM_D21_D22_D23_D24, ARM_Q11_Q12, ARM_D23_D24_D25_D26, ARM_Q12_Q13, ARM_D25_D26_D27_D28, ARM_Q13_Q14, ARM_D27_D28_D29_D30, ARM_Q14_Q15, 
  };

  // DQuad Bit set.
  static const uint8_t DQuadBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0xff, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0xff, 0x01, 
  };

  // DQuad_with_ssub_0 Register Class...
  static MCPhysReg DQuad_with_ssub_0[] = {
    ARM_Q0_Q1, ARM_D1_D2_D3_D4, ARM_Q1_Q2, ARM_D3_D4_D5_D6, ARM_Q2_Q3, ARM_D5_D6_D7_D8, ARM_Q3_Q4, ARM_D7_D8_D9_D10, ARM_Q4_Q5, ARM_D9_D10_D11_D12, ARM_Q5_Q6, ARM_D11_D12_D13_D14, ARM_Q6_Q7, ARM_D13_D14_D15_D16, ARM_Q7_Q8, ARM_D15_D16_D17_D18, 
  };

  // DQuad_with_ssub_0 Bit set.
  static const uint8_t DQuad_with_ssub_0Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x07, 
  };

  // DQuad_with_ssub_2 Register Class...
  static MCPhysReg DQuad_with_ssub_2[] = {
    ARM_Q0_Q1, ARM_D1_D2_D3_D4, ARM_Q1_Q2, ARM_D3_D4_D5_D6, ARM_Q2_Q3, ARM_D5_D6_D7_D8, ARM_Q3_Q4, ARM_D7_D8_D9_D10, ARM_Q4_Q5, ARM_D9_D10_D11_D12, ARM_Q5_Q6, ARM_D11_D12_D13_D14, ARM_Q6_Q7, ARM_D13_D14_D15_D16, ARM_Q7_Q8, 
  };

  // DQuad_with_ssub_2 Bit set.
  static const uint8_t DQuad_with_ssub_2Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x1f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x03, 
  };

  // QQPR Register Class...
  static MCPhysReg QQPR[] = {
    ARM_Q0_Q1, ARM_Q1_Q2, ARM_Q2_Q3, ARM_Q3_Q4, ARM_Q4_Q5, ARM_Q5_Q6, ARM_Q6_Q7, ARM_Q7_Q8, ARM_Q8_Q9, ARM_Q9_Q10, ARM_Q10_Q11, ARM_Q11_Q12, ARM_Q12_Q13, ARM_Q13_Q14, ARM_Q14_Q15, 
  };

  // QQPR Bit set.
  static const uint8_t QQPRBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0xff, 0x0f, 
  };

  // DQuad_with_dsub_1_dsub_2_in_QPR Register Class...
  static MCPhysReg DQuad_with_dsub_1_dsub_2_in_QPR[] = {
    ARM_D1_D2_D3_D4, ARM_D3_D4_D5_D6, ARM_D5_D6_D7_D8, ARM_D7_D8_D9_D10, ARM_D9_D10_D11_D12, ARM_D11_D12_D13_D14, ARM_D13_D14_D15_D16, ARM_D15_D16_D17_D18, ARM_D17_D18_D19_D20, ARM_D19_D20_D21_D22, ARM_D21_D22_D23_D24, ARM_D23_D24_D25_D26, ARM_D25_D26_D27_D28, ARM_D27_D28_D29_D30, 
  };

  // DQuad_with_dsub_1_dsub_2_in_QPR Bit set.
  static const uint8_t DQuad_with_dsub_1_dsub_2_in_QPRBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0xff, 0x01, 
  };

  // DQuad_with_dsub_2_then_ssub_0 Register Class...
  static MCPhysReg DQuad_with_dsub_2_then_ssub_0[] = {
    ARM_Q0_Q1, ARM_D1_D2_D3_D4, ARM_Q1_Q2, ARM_D3_D4_D5_D6, ARM_Q2_Q3, ARM_D5_D6_D7_D8, ARM_Q3_Q4, ARM_D7_D8_D9_D10, ARM_Q4_Q5, ARM_D9_D10_D11_D12, ARM_Q5_Q6, ARM_D11_D12_D13_D14, ARM_Q6_Q7, ARM_D13_D14_D15_D16, 
  };

  // DQuad_with_dsub_2_then_ssub_0 Bit set.
  static const uint8_t DQuad_with_dsub_2_then_ssub_0Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x03, 
  };

  // DQuad_with_dsub_3_then_ssub_0 Register Class...
  static MCPhysReg DQuad_with_dsub_3_then_ssub_0[] = {
    ARM_Q0_Q1, ARM_D1_D2_D3_D4, ARM_Q1_Q2, ARM_D3_D4_D5_D6, ARM_Q2_Q3, ARM_D5_D6_D7_D8, ARM_Q3_Q4, ARM_D7_D8_D9_D10, ARM_Q4_Q5, ARM_D9_D10_D11_D12, ARM_Q5_Q6, ARM_D11_D12_D13_D14, ARM_Q6_Q7, 
  };

  // DQuad_with_dsub_3_then_ssub_0 Bit set.
  static const uint8_t DQuad_with_dsub_3_then_ssub_0Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x01, 
  };

  // DQuad_with_dsub_0_in_DPR_8 Register Class...
  static MCPhysReg DQuad_with_dsub_0_in_DPR_8[] = {
    ARM_Q0_Q1, ARM_D1_D2_D3_D4, ARM_Q1_Q2, ARM_D3_D4_D5_D6, ARM_Q2_Q3, ARM_D5_D6_D7_D8, ARM_Q3_Q4, ARM_D7_D8_D9_D10, 
  };

  // DQuad_with_dsub_0_in_DPR_8 Bit set.
  static const uint8_t DQuad_with_dsub_0_in_DPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 
  };

  // DQuad_with_qsub_0_in_QPR_VFP2 Register Class...
  static MCPhysReg DQuad_with_qsub_0_in_QPR_VFP2[] = {
    ARM_Q0_Q1, ARM_Q1_Q2, ARM_Q2_Q3, ARM_Q3_Q4, ARM_Q4_Q5, ARM_Q5_Q6, ARM_Q6_Q7, ARM_Q7_Q8, 
  };

  // DQuad_with_qsub_0_in_QPR_VFP2 Bit set.
  static const uint8_t DQuad_with_qsub_0_in_QPR_VFP2Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x1f, 
  };

  // DQuad_with_ssub_0_and_DQuad_with_dsub_1_dsub_2_in_QPR Register Class...
  static MCPhysReg DQuad_with_ssub_0_and_DQuad_with_dsub_1_dsub_2_in_QPR[] = {
    ARM_D1_D2_D3_D4, ARM_D3_D4_D5_D6, ARM_D5_D6_D7_D8, ARM_D7_D8_D9_D10, ARM_D9_D10_D11_D12, ARM_D11_D12_D13_D14, ARM_D13_D14_D15_D16, ARM_D15_D16_D17_D18, 
  };

  // DQuad_with_ssub_0_and_DQuad_with_dsub_1_dsub_2_in_QPR Bit set.
  static const uint8_t DQuad_with_ssub_0_and_DQuad_with_dsub_1_dsub_2_in_QPRBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x07, 
  };

  // DQuad_with_dsub_1_dsub_2_in_QPR_VFP2 Register Class...
  static MCPhysReg DQuad_with_dsub_1_dsub_2_in_QPR_VFP2[] = {
    ARM_D1_D2_D3_D4, ARM_D3_D4_D5_D6, ARM_D5_D6_D7_D8, ARM_D7_D8_D9_D10, ARM_D9_D10_D11_D12, ARM_D11_D12_D13_D14, ARM_D13_D14_D15_D16, 
  };

  // DQuad_with_dsub_1_dsub_2_in_QPR_VFP2 Bit set.
  static const uint8_t DQuad_with_dsub_1_dsub_2_in_QPR_VFP2Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x03, 
  };

  // DQuad_with_dsub_1_in_DPR_8 Register Class...
  static MCPhysReg DQuad_with_dsub_1_in_DPR_8[] = {
    ARM_Q0_Q1, ARM_D1_D2_D3_D4, ARM_Q1_Q2, ARM_D3_D4_D5_D6, ARM_Q2_Q3, ARM_D5_D6_D7_D8, ARM_Q3_Q4, 
  };

  // DQuad_with_dsub_1_in_DPR_8 Bit set.
  static const uint8_t DQuad_with_dsub_1_in_DPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 
  };

  // DQuad_with_qsub_1_in_QPR_VFP2 Register Class...
  static MCPhysReg DQuad_with_qsub_1_in_QPR_VFP2[] = {
    ARM_Q0_Q1, ARM_Q1_Q2, ARM_Q2_Q3, ARM_Q3_Q4, ARM_Q4_Q5, ARM_Q5_Q6, ARM_Q6_Q7, 
  };

  // DQuad_with_qsub_1_in_QPR_VFP2 Bit set.
  static const uint8_t DQuad_with_qsub_1_in_QPR_VFP2Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x0f, 
  };

  // DQuad_with_dsub_2_in_DPR_8 Register Class...
  static MCPhysReg DQuad_with_dsub_2_in_DPR_8[] = {
    ARM_Q0_Q1, ARM_D1_D2_D3_D4, ARM_Q1_Q2, ARM_D3_D4_D5_D6, ARM_Q2_Q3, ARM_D5_D6_D7_D8, 
  };

  // DQuad_with_dsub_2_in_DPR_8 Bit set.
  static const uint8_t DQuad_with_dsub_2_in_DPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 
  };

  // DQuad_with_dsub_3_then_ssub_0_and_DQuad_with_dsub_1_dsub_2_in_QPR Register Class...
  static MCPhysReg DQuad_with_dsub_3_then_ssub_0_and_DQuad_with_dsub_1_dsub_2_in_QPR[] = {
    ARM_D1_D2_D3_D4, ARM_D3_D4_D5_D6, ARM_D5_D6_D7_D8, ARM_D7_D8_D9_D10, ARM_D9_D10_D11_D12, ARM_D11_D12_D13_D14, 
  };

  // DQuad_with_dsub_3_then_ssub_0_and_DQuad_with_dsub_1_dsub_2_in_QPR Bit set.
  static const uint8_t DQuad_with_dsub_3_then_ssub_0_and_DQuad_with_dsub_1_dsub_2_in_QPRBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf8, 0x01, 
  };

  // DQuad_with_dsub_3_in_DPR_8 Register Class...
  static MCPhysReg DQuad_with_dsub_3_in_DPR_8[] = {
    ARM_Q0_Q1, ARM_D1_D2_D3_D4, ARM_Q1_Q2, ARM_D3_D4_D5_D6, ARM_Q2_Q3, 
  };

  // DQuad_with_dsub_3_in_DPR_8 Bit set.
  static const uint8_t DQuad_with_dsub_3_in_DPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 
  };

  // DQuad_with_dsub_0_in_DPR_8_and_DQuad_with_dsub_1_dsub_2_in_QPR Register Class...
  static MCPhysReg DQuad_with_dsub_0_in_DPR_8_and_DQuad_with_dsub_1_dsub_2_in_QPR[] = {
    ARM_D1_D2_D3_D4, ARM_D3_D4_D5_D6, ARM_D5_D6_D7_D8, ARM_D7_D8_D9_D10, 
  };

  // DQuad_with_dsub_0_in_DPR_8_and_DQuad_with_dsub_1_dsub_2_in_QPR Bit set.
  static const uint8_t DQuad_with_dsub_0_in_DPR_8_and_DQuad_with_dsub_1_dsub_2_in_QPRBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 
  };

  // DQuad_with_qsub_0_in_QPR_8 Register Class...
  static MCPhysReg DQuad_with_qsub_0_in_QPR_8[] = {
    ARM_Q0_Q1, ARM_Q1_Q2, ARM_Q2_Q3, ARM_Q3_Q4, 
  };

  // DQuad_with_qsub_0_in_QPR_8 Bit set.
  static const uint8_t DQuad_with_qsub_0_in_QPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0x01, 
  };

  // DQuad_with_dsub_1_dsub_2_in_QPR_8 Register Class...
  static MCPhysReg DQuad_with_dsub_1_dsub_2_in_QPR_8[] = {
    ARM_D1_D2_D3_D4, ARM_D3_D4_D5_D6, ARM_D5_D6_D7_D8, 
  };

  // DQuad_with_dsub_1_dsub_2_in_QPR_8 Bit set.
  static const uint8_t DQuad_with_dsub_1_dsub_2_in_QPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 
  };

  // DQuad_with_qsub_1_in_QPR_8 Register Class...
  static MCPhysReg DQuad_with_qsub_1_in_QPR_8[] = {
    ARM_Q0_Q1, ARM_Q1_Q2, ARM_Q2_Q3, 
  };

  // DQuad_with_qsub_1_in_QPR_8 Bit set.
  static const uint8_t DQuad_with_qsub_1_in_QPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 
  };

  // DQuad_with_dsub_3_in_DPR_8_and_DQuad_with_dsub_1_dsub_2_in_QPR Register Class...
  static MCPhysReg DQuad_with_dsub_3_in_DPR_8_and_DQuad_with_dsub_1_dsub_2_in_QPR[] = {
    ARM_D1_D2_D3_D4, ARM_D3_D4_D5_D6, 
  };

  // DQuad_with_dsub_3_in_DPR_8_and_DQuad_with_dsub_1_dsub_2_in_QPR Bit set.
  static const uint8_t DQuad_with_dsub_3_in_DPR_8_and_DQuad_with_dsub_1_dsub_2_in_QPRBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 
  };

  // QQQQPR Register Class...
  static MCPhysReg QQQQPR[] = {
    ARM_Q0_Q1_Q2_Q3, ARM_Q1_Q2_Q3_Q4, ARM_Q2_Q3_Q4_Q5, ARM_Q3_Q4_Q5_Q6, ARM_Q4_Q5_Q6_Q7, ARM_Q5_Q6_Q7_Q8, ARM_Q6_Q7_Q8_Q9, ARM_Q7_Q8_Q9_Q10, ARM_Q8_Q9_Q10_Q11, ARM_Q9_Q10_Q11_Q12, ARM_Q10_Q11_Q12_Q13, ARM_Q11_Q12_Q13_Q14, ARM_Q12_Q13_Q14_Q15, 
  };

  // QQQQPR Bit set.
  static const uint8_t QQQQPRBits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0xff, 0x01, 
  };

  // QQQQPR_with_ssub_0 Register Class...
  static MCPhysReg QQQQPR_with_ssub_0[] = {
    ARM_Q0_Q1_Q2_Q3, ARM_Q1_Q2_Q3_Q4, ARM_Q2_Q3_Q4_Q5, ARM_Q3_Q4_Q5_Q6, ARM_Q4_Q5_Q6_Q7, ARM_Q5_Q6_Q7_Q8, ARM_Q6_Q7_Q8_Q9, ARM_Q7_Q8_Q9_Q10, 
  };

  // QQQQPR_with_ssub_0 Bit set.
  static const uint8_t QQQQPR_with_ssub_0Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x0f, 
  };

  // QQQQPR_with_dsub_2_then_ssub_0 Register Class...
  static MCPhysReg QQQQPR_with_dsub_2_then_ssub_0[] = {
    ARM_Q0_Q1_Q2_Q3, ARM_Q1_Q2_Q3_Q4, ARM_Q2_Q3_Q4_Q5, ARM_Q3_Q4_Q5_Q6, ARM_Q4_Q5_Q6_Q7, ARM_Q5_Q6_Q7_Q8, ARM_Q6_Q7_Q8_Q9, 
  };

  // QQQQPR_with_dsub_2_then_ssub_0 Bit set.
  static const uint8_t QQQQPR_with_dsub_2_then_ssub_0Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x07, 
  };

  // QQQQPR_with_dsub_5_then_ssub_0 Register Class...
  static MCPhysReg QQQQPR_with_dsub_5_then_ssub_0[] = {
    ARM_Q0_Q1_Q2_Q3, ARM_Q1_Q2_Q3_Q4, ARM_Q2_Q3_Q4_Q5, ARM_Q3_Q4_Q5_Q6, ARM_Q4_Q5_Q6_Q7, ARM_Q5_Q6_Q7_Q8, 
  };

  // QQQQPR_with_dsub_5_then_ssub_0 Bit set.
  static const uint8_t QQQQPR_with_dsub_5_then_ssub_0Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x03, 
  };

  // QQQQPR_with_dsub_7_then_ssub_0 Register Class...
  static MCPhysReg QQQQPR_with_dsub_7_then_ssub_0[] = {
    ARM_Q0_Q1_Q2_Q3, ARM_Q1_Q2_Q3_Q4, ARM_Q2_Q3_Q4_Q5, ARM_Q3_Q4_Q5_Q6, ARM_Q4_Q5_Q6_Q7, 
  };

  // QQQQPR_with_dsub_7_then_ssub_0 Bit set.
  static const uint8_t QQQQPR_with_dsub_7_then_ssub_0Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x01, 
  };

  // QQQQPR_with_dsub_0_in_DPR_8 Register Class...
  static MCPhysReg QQQQPR_with_dsub_0_in_DPR_8[] = {
    ARM_Q0_Q1_Q2_Q3, ARM_Q1_Q2_Q3_Q4, ARM_Q2_Q3_Q4_Q5, ARM_Q3_Q4_Q5_Q6, 
  };

  // QQQQPR_with_dsub_0_in_DPR_8 Bit set.
  static const uint8_t QQQQPR_with_dsub_0_in_DPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf0, 
  };

  // QQQQPR_with_dsub_2_in_DPR_8 Register Class...
  static MCPhysReg QQQQPR_with_dsub_2_in_DPR_8[] = {
    ARM_Q0_Q1_Q2_Q3, ARM_Q1_Q2_Q3_Q4, ARM_Q2_Q3_Q4_Q5, 
  };

  // QQQQPR_with_dsub_2_in_DPR_8 Bit set.
  static const uint8_t QQQQPR_with_dsub_2_in_DPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 
  };

  // QQQQPR_with_dsub_4_in_DPR_8 Register Class...
  static MCPhysReg QQQQPR_with_dsub_4_in_DPR_8[] = {
    ARM_Q0_Q1_Q2_Q3, ARM_Q1_Q2_Q3_Q4, 
  };

  // QQQQPR_with_dsub_4_in_DPR_8 Bit set.
  static const uint8_t QQQQPR_with_dsub_4_in_DPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 
  };

  // QQQQPR_with_dsub_6_in_DPR_8 Register Class...
  static MCPhysReg QQQQPR_with_dsub_6_in_DPR_8[] = {
    ARM_Q0_Q1_Q2_Q3, 
  };

  // QQQQPR_with_dsub_6_in_DPR_8 Bit set.
  static const uint8_t QQQQPR_with_dsub_6_in_DPR_8Bits[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 
  };

static MCRegisterClass ARMMCRegisterClasses[] = {
  { SPR, SPRBits, 2228, 32, sizeof(SPRBits), ARM_SPRRegClassID, 4, 4, 1, 1 },
  { GPR, GPRBits, 1512, 16, sizeof(GPRBits), ARM_GPRRegClassID, 4, 4, 1, 1 },
  { GPRwithAPSR, GPRwithAPSRBits, 2232, 16, sizeof(GPRwithAPSRBits), ARM_GPRwithAPSRRegClassID, 4, 4, 1, 1 },
  { SPR_8, SPR_8Bits, 1487, 16, sizeof(SPR_8Bits), ARM_SPR_8RegClassID, 4, 4, 1, 1 },
  { GPRnopc, GPRnopcBits, 2273, 15, sizeof(GPRnopcBits), ARM_GPRnopcRegClassID, 4, 4, 1, 1 },
  { rGPR, rGPRBits, 1666, 14, sizeof(rGPRBits), ARM_rGPRRegClassID, 4, 4, 1, 1 },
  { hGPR, hGPRBits, 1601, 8, sizeof(hGPRBits), ARM_hGPRRegClassID, 4, 4, 1, 1 },
  { tGPR, tGPRBits, 1722, 8, sizeof(tGPRBits), ARM_tGPRRegClassID, 4, 4, 1, 1 },
  { GPRnopc_and_hGPR, GPRnopc_and_hGPRBits, 1589, 7, sizeof(GPRnopc_and_hGPRBits), ARM_GPRnopc_and_hGPRRegClassID, 4, 4, 1, 1 },
  { hGPR_and_rGPR, hGPR_and_rGPRBits, 1657, 6, sizeof(hGPR_and_rGPRBits), ARM_hGPR_and_rGPRRegClassID, 4, 4, 1, 1 },
  { tcGPR, tcGPRBits, 1510, 5, sizeof(tcGPRBits), ARM_tcGPRRegClassID, 4, 4, 1, 1 },
  { tGPR_and_tcGPR, tGPR_and_tcGPRBits, 1516, 4, sizeof(tGPR_and_tcGPRBits), ARM_tGPR_and_tcGPRRegClassID, 4, 4, 1, 1 },
  { CCR, CCRBits, 1493, 1, sizeof(CCRBits), ARM_CCRRegClassID, 4, 4, -1, 0 },
  { GPRsp, GPRspBits, 2318, 1, sizeof(GPRspBits), ARM_GPRspRegClassID, 4, 4, 1, 1 },
  { hGPR_and_tcGPR, hGPR_and_tcGPRBits, 1501, 1, sizeof(hGPR_and_tcGPRBits), ARM_hGPR_and_tcGPRRegClassID, 4, 4, 1, 1 },
  { DPR, DPRBits, 1497, 32, sizeof(DPRBits), ARM_DPRRegClassID, 8, 8, 1, 1 },
  { DPR_VFP2, DPR_VFP2Bits, 494, 16, sizeof(DPR_VFP2Bits), ARM_DPR_VFP2RegClassID, 8, 8, 1, 1 },
  { DPR_8, DPR_8Bits, 749, 8, sizeof(DPR_8Bits), ARM_DPR_8RegClassID, 8, 8, 1, 1 },
  { GPRPair, GPRPairBits, 2330, 7, sizeof(GPRPairBits), ARM_GPRPairRegClassID, 8, 8, 1, 1 },
  { GPRPair_with_gsub_1_in_rGPR, GPRPair_with_gsub_1_in_rGPRBits, 1671, 6, sizeof(GPRPair_with_gsub_1_in_rGPRBits), ARM_GPRPair_with_gsub_1_in_rGPRRegClassID, 8, 8, 1, 1 },
  { GPRPair_with_gsub_0_in_tGPR, GPRPair_with_gsub_0_in_tGPRBits, 1699, 4, sizeof(GPRPair_with_gsub_0_in_tGPRBits), ARM_GPRPair_with_gsub_0_in_tGPRRegClassID, 8, 8, 1, 1 },
  { GPRPair_with_gsub_0_in_hGPR, GPRPair_with_gsub_0_in_hGPRBits, 1606, 3, sizeof(GPRPair_with_gsub_0_in_hGPRBits), ARM_GPRPair_with_gsub_0_in_hGPRRegClassID, 8, 8, 1, 1 },
  { GPRPair_with_gsub_0_in_tcGPR, GPRPair_with_gsub_0_in_tcGPRBits, 1531, 3, sizeof(GPRPair_with_gsub_0_in_tcGPRBits), ARM_GPRPair_with_gsub_0_in_tcGPRRegClassID, 8, 8, 1, 1 },
  { GPRPair_with_gsub_1_in_hGPR_and_rGPR, GPRPair_with_gsub_1_in_hGPR_and_rGPRBits, 1634, 2, sizeof(GPRPair_with_gsub_1_in_hGPR_and_rGPRBits), ARM_GPRPair_with_gsub_1_in_hGPR_and_rGPRRegClassID, 8, 8, 1, 1 },
  { GPRPair_with_gsub_1_in_tcGPR, GPRPair_with_gsub_1_in_tcGPRBits, 1560, 2, sizeof(GPRPair_with_gsub_1_in_tcGPRBits), ARM_GPRPair_with_gsub_1_in_tcGPRRegClassID, 8, 8, 1, 1 },
  { GPRPair_with_gsub_1_in_GPRsp, GPRPair_with_gsub_1_in_GPRspBits, 2295, 1, sizeof(GPRPair_with_gsub_1_in_GPRspBits), ARM_GPRPair_with_gsub_1_in_GPRspRegClassID, 8, 8, 1, 1 },
  { DPairSpc, DPairSpcBits, 2264, 30, sizeof(DPairSpcBits), ARM_DPairSpcRegClassID, 16, 8, 1, 1 },
  { DPairSpc_with_ssub_0, DPairSpc_with_ssub_0Bits, 63, 16, sizeof(DPairSpc_with_ssub_0Bits), ARM_DPairSpc_with_ssub_0RegClassID, 16, 8, 1, 1 },
  { DPairSpc_with_dsub_2_then_ssub_0, DPairSpc_with_dsub_2_then_ssub_0Bits, 239, 14, sizeof(DPairSpc_with_dsub_2_then_ssub_0Bits), ARM_DPairSpc_with_dsub_2_then_ssub_0RegClassID, 16, 8, 1, 1 },
  { DPairSpc_with_dsub_0_in_DPR_8, DPairSpc_with_dsub_0_in_DPR_8Bits, 817, 8, sizeof(DPairSpc_with_dsub_0_in_DPR_8Bits), ARM_DPairSpc_with_dsub_0_in_DPR_8RegClassID, 16, 8, 1, 1 },
  { DPairSpc_with_dsub_2_in_DPR_8, DPairSpc_with_dsub_2_in_DPR_8Bits, 1103, 6, sizeof(DPairSpc_with_dsub_2_in_DPR_8Bits), ARM_DPairSpc_with_dsub_2_in_DPR_8RegClassID, 16, 8, 1, 1 },
  { DPair, DPairBits, 2324, 31, sizeof(DPairBits), ARM_DPairRegClassID, 16, 16, 1, 1 },
  { DPair_with_ssub_0, DPair_with_ssub_0Bits, 122, 16, sizeof(DPair_with_ssub_0Bits), ARM_DPair_with_ssub_0RegClassID, 16, 16, 1, 1 },
  { QPR, QPRBits, 1730, 16, sizeof(QPRBits), ARM_QPRRegClassID, 16, 16, 1, 1 },
  { DPair_with_ssub_2, DPair_with_ssub_2Bits, 709, 15, sizeof(DPair_with_ssub_2Bits), ARM_DPair_with_ssub_2RegClassID, 16, 16, 1, 1 },
  { DPair_with_dsub_0_in_DPR_8, DPair_with_dsub_0_in_DPR_8Bits, 903, 8, sizeof(DPair_with_dsub_0_in_DPR_8Bits), ARM_DPair_with_dsub_0_in_DPR_8RegClassID, 16, 16, 1, 1 },
  { QPR_VFP2, QPR_VFP2Bits, 524, 8, sizeof(QPR_VFP2Bits), ARM_QPR_VFP2RegClassID, 16, 16, 1, 1 },
  { DPair_with_dsub_1_in_DPR_8, DPair_with_dsub_1_in_DPR_8Bits, 986, 7, sizeof(DPair_with_dsub_1_in_DPR_8Bits), ARM_DPair_with_dsub_1_in_DPR_8RegClassID, 16, 16, 1, 1 },
  { QPR_8, QPR_8Bits, 1355, 4, sizeof(QPR_8Bits), ARM_QPR_8RegClassID, 16, 16, 1, 1 },
  { DTriple, DTripleBits, 2287, 30, sizeof(DTripleBits), ARM_DTripleRegClassID, 24, 8, 1, 1 },
  { DTripleSpc, DTripleSpcBits, 2253, 28, sizeof(DTripleSpcBits), ARM_DTripleSpcRegClassID, 24, 8, 1, 1 },
  { DTripleSpc_with_ssub_0, DTripleSpc_with_ssub_0Bits, 40, 16, sizeof(DTripleSpc_with_ssub_0Bits), ARM_DTripleSpc_with_ssub_0RegClassID, 24, 8, 1, 1 },
  { DTriple_with_ssub_0, DTriple_with_ssub_0Bits, 102, 16, sizeof(DTriple_with_ssub_0Bits), ARM_DTriple_with_ssub_0RegClassID, 24, 8, 1, 1 },
  { DTriple_with_dsub_1_dsub_2_in_QPR, DTriple_with_dsub_1_dsub_2_in_QPRBits, 2127, 15, sizeof(DTriple_with_dsub_1_dsub_2_in_QPRBits), ARM_DTriple_with_dsub_1_dsub_2_in_QPRRegClassID, 24, 8, 1, 1 },
  { DTriple_with_qsub_0_in_QPR, DTriple_with_qsub_0_in_QPRBits, 1770, 15, sizeof(DTriple_with_qsub_0_in_QPRBits), ARM_DTriple_with_qsub_0_in_QPRRegClassID, 24, 8, 1, 1 },
  { DTriple_with_ssub_2, DTriple_with_ssub_2Bits, 689, 15, sizeof(DTriple_with_ssub_2Bits), ARM_DTriple_with_ssub_2RegClassID, 24, 8, 1, 1 },
  { DTripleSpc_with_dsub_2_then_ssub_0, DTripleSpc_with_dsub_2_then_ssub_0Bits, 204, 14, sizeof(DTripleSpc_with_dsub_2_then_ssub_0Bits), ARM_DTripleSpc_with_dsub_2_then_ssub_0RegClassID, 24, 8, 1, 1 },
  { DTriple_with_dsub_2_then_ssub_0, DTriple_with_dsub_2_then_ssub_0Bits, 302, 14, sizeof(DTriple_with_dsub_2_then_ssub_0Bits), ARM_DTriple_with_dsub_2_then_ssub_0RegClassID, 24, 8, 1, 1 },
  { DTripleSpc_with_dsub_4_then_ssub_0, DTripleSpc_with_dsub_4_then_ssub_0Bits, 397, 12, sizeof(DTripleSpc_with_dsub_4_then_ssub_0Bits), ARM_DTripleSpc_with_dsub_4_then_ssub_0RegClassID, 24, 8, 1, 1 },
  { DTripleSpc_with_dsub_0_in_DPR_8, DTripleSpc_with_dsub_0_in_DPR_8Bits, 785, 8, sizeof(DTripleSpc_with_dsub_0_in_DPR_8Bits), ARM_DTripleSpc_with_dsub_0_in_DPR_8RegClassID, 24, 8, 1, 1 },
  { DTriple_with_dsub_0_in_DPR_8, DTriple_with_dsub_0_in_DPR_8Bits, 874, 8, sizeof(DTriple_with_dsub_0_in_DPR_8Bits), ARM_DTriple_with_dsub_0_in_DPR_8RegClassID, 24, 8, 1, 1 },
  { DTriple_with_qsub_0_in_QPR_VFP2, DTriple_with_qsub_0_in_QPR_VFP2Bits, 533, 8, sizeof(DTriple_with_qsub_0_in_QPR_VFP2Bits), ARM_DTriple_with_qsub_0_in_QPR_VFP2RegClassID, 24, 8, 1, 1 },
  { DTriple_with_ssub_0_and_DTriple_with_dsub_1_dsub_2_in_QPR, DTriple_with_ssub_0_and_DTriple_with_dsub_1_dsub_2_in_QPRBits, 2103, 8, sizeof(DTriple_with_ssub_0_and_DTriple_with_dsub_1_dsub_2_in_QPRBits), ARM_DTriple_with_ssub_0_and_DTriple_with_dsub_1_dsub_2_in_QPRRegClassID, 24, 8, 1, 1 },
  { DTriple_with_dsub_1_dsub_2_in_QPR_VFP2, DTriple_with_dsub_1_dsub_2_in_QPR_VFP2Bits, 632, 7, sizeof(DTriple_with_dsub_1_dsub_2_in_QPR_VFP2Bits), ARM_DTriple_with_dsub_1_dsub_2_in_QPR_VFP2RegClassID, 24, 8, 1, 1 },
  { DTriple_with_dsub_1_in_DPR_8, DTriple_with_dsub_1_in_DPR_8Bits, 957, 7, sizeof(DTriple_with_dsub_1_in_DPR_8Bits), ARM_DTriple_with_dsub_1_in_DPR_8RegClassID, 24, 8, 1, 1 },
  { DTriple_with_dsub_2_then_ssub_0_and_DTriple_with_qsub_0_in_QPR, DTriple_with_dsub_2_then_ssub_0_and_DTriple_with_qsub_0_in_QPRBits, 1734, 7, sizeof(DTriple_with_dsub_2_then_ssub_0_and_DTriple_with_qsub_0_in_QPRBits), ARM_DTriple_with_dsub_2_then_ssub_0_and_DTriple_with_qsub_0_in_QPRRegClassID, 24, 8, 1, 1 },
  { DTripleSpc_with_dsub_2_in_DPR_8, DTripleSpc_with_dsub_2_in_DPR_8Bits, 1071, 6, sizeof(DTripleSpc_with_dsub_2_in_DPR_8Bits), ARM_DTripleSpc_with_dsub_2_in_DPR_8RegClassID, 24, 8, 1, 1 },
  { DTriple_with_dsub_2_in_DPR_8, DTriple_with_dsub_2_in_DPR_8Bits, 1160, 6, sizeof(DTriple_with_dsub_2_in_DPR_8Bits), ARM_DTriple_with_dsub_2_in_DPR_8RegClassID, 24, 8, 1, 1 },
  { DTripleSpc_with_dsub_4_in_DPR_8, DTripleSpc_with_dsub_4_in_DPR_8Bits, 1274, 4, sizeof(DTripleSpc_with_dsub_4_in_DPR_8Bits), ARM_DTripleSpc_with_dsub_4_in_DPR_8RegClassID, 24, 8, 1, 1 },
  { DTriple_with_dsub_0_in_DPR_8_and_DTriple_with_dsub_1_dsub_2_in_QPR, DTriple_with_dsub_0_in_DPR_8_and_DTriple_with_dsub_1_dsub_2_in_QPRBits, 2161, 4, sizeof(DTriple_with_dsub_0_in_DPR_8_and_DTriple_with_dsub_1_dsub_2_in_QPRBits), ARM_DTriple_with_dsub_0_in_DPR_8_and_DTriple_with_dsub_1_dsub_2_in_QPRRegClassID, 24, 8, 1, 1 },
  { DTriple_with_qsub_0_in_QPR_8, DTriple_with_qsub_0_in_QPR_8Bits, 1361, 4, sizeof(DTriple_with_qsub_0_in_QPR_8Bits), ARM_DTriple_with_qsub_0_in_QPR_8RegClassID, 24, 8, 1, 1 },
  { DTriple_with_dsub_1_dsub_2_in_QPR_8, DTriple_with_dsub_1_dsub_2_in_QPR_8Bits, 1451, 3, sizeof(DTriple_with_dsub_1_dsub_2_in_QPR_8Bits), ARM_DTriple_with_dsub_1_dsub_2_in_QPR_8RegClassID, 24, 8, 1, 1 },
  { DTriple_with_dsub_2_in_DPR_8_and_DTriple_with_qsub_0_in_QPR, DTriple_with_dsub_2_in_DPR_8_and_DTriple_with_qsub_0_in_QPRBits, 1797, 3, sizeof(DTriple_with_dsub_2_in_DPR_8_and_DTriple_with_qsub_0_in_QPRBits), ARM_DTriple_with_dsub_2_in_DPR_8_and_DTriple_with_qsub_0_in_QPRRegClassID, 24, 8, 1, 1 },
  { DQuadSpc, DQuadSpcBits, 2244, 28, sizeof(DQuadSpcBits), ARM_DQuadSpcRegClassID, 32, 8, 1, 1 },
  { DQuadSpc_with_ssub_0, DQuadSpc_with_ssub_0Bits, 19, 16, sizeof(DQuadSpc_with_ssub_0Bits), ARM_DQuadSpc_with_ssub_0RegClassID, 32, 8, 1, 1 },
  { DQuadSpc_with_dsub_2_then_ssub_0, DQuadSpc_with_dsub_2_then_ssub_0Bits, 171, 14, sizeof(DQuadSpc_with_dsub_2_then_ssub_0Bits), ARM_DQuadSpc_with_dsub_2_then_ssub_0RegClassID, 32, 8, 1, 1 },
  { DQuadSpc_with_dsub_4_then_ssub_0, DQuadSpc_with_dsub_4_then_ssub_0Bits, 364, 12, sizeof(DQuadSpc_with_dsub_4_then_ssub_0Bits), ARM_DQuadSpc_with_dsub_4_then_ssub_0RegClassID, 32, 8, 1, 1 },
  { DQuadSpc_with_dsub_0_in_DPR_8, DQuadSpc_with_dsub_0_in_DPR_8Bits, 755, 8, sizeof(DQuadSpc_with_dsub_0_in_DPR_8Bits), ARM_DQuadSpc_with_dsub_0_in_DPR_8RegClassID, 32, 8, 1, 1 },
  { DQuadSpc_with_dsub_2_in_DPR_8, DQuadSpc_with_dsub_2_in_DPR_8Bits, 1041, 6, sizeof(DQuadSpc_with_dsub_2_in_DPR_8Bits), ARM_DQuadSpc_with_dsub_2_in_DPR_8RegClassID, 32, 8, 1, 1 },
  { DQuadSpc_with_dsub_4_in_DPR_8, DQuadSpc_with_dsub_4_in_DPR_8Bits, 1244, 4, sizeof(DQuadSpc_with_dsub_4_in_DPR_8Bits), ARM_DQuadSpc_with_dsub_4_in_DPR_8RegClassID, 32, 8, 1, 1 },
  { DQuad, DQuadBits, 2281, 29, sizeof(DQuadBits), ARM_DQuadRegClassID, 32, 32, 1, 1 },
  { DQuad_with_ssub_0, DQuad_with_ssub_0Bits, 84, 16, sizeof(DQuad_with_ssub_0Bits), ARM_DQuad_with_ssub_0RegClassID, 32, 32, 1, 1 },
  { DQuad_with_ssub_2, DQuad_with_ssub_2Bits, 671, 15, sizeof(DQuad_with_ssub_2Bits), ARM_DQuad_with_ssub_2RegClassID, 32, 32, 1, 1 },
  { QQPR, QQPRBits, 1729, 15, sizeof(QQPRBits), ARM_QQPRRegClassID, 32, 32, 1, 1 },
  { DQuad_with_dsub_1_dsub_2_in_QPR, DQuad_with_dsub_1_dsub_2_in_QPRBits, 1879, 14, sizeof(DQuad_with_dsub_1_dsub_2_in_QPRBits), ARM_DQuad_with_dsub_1_dsub_2_in_QPRRegClassID, 32, 32, 1, 1 },
  { DQuad_with_dsub_2_then_ssub_0, DQuad_with_dsub_2_then_ssub_0Bits, 272, 14, sizeof(DQuad_with_dsub_2_then_ssub_0Bits), ARM_DQuad_with_dsub_2_then_ssub_0RegClassID, 32, 32, 1, 1 },
  { DQuad_with_dsub_3_then_ssub_0, DQuad_with_dsub_3_then_ssub_0Bits, 334, 13, sizeof(DQuad_with_dsub_3_then_ssub_0Bits), ARM_DQuad_with_dsub_3_then_ssub_0RegClassID, 32, 32, 1, 1 },
  { DQuad_with_dsub_0_in_DPR_8, DQuad_with_dsub_0_in_DPR_8Bits, 847, 8, sizeof(DQuad_with_dsub_0_in_DPR_8Bits), ARM_DQuad_with_dsub_0_in_DPR_8RegClassID, 32, 32, 1, 1 },
  { DQuad_with_qsub_0_in_QPR_VFP2, DQuad_with_qsub_0_in_QPR_VFP2Bits, 503, 8, sizeof(DQuad_with_qsub_0_in_QPR_VFP2Bits), ARM_DQuad_with_qsub_0_in_QPR_VFP2RegClassID, 32, 32, 1, 1 },
  { DQuad_with_ssub_0_and_DQuad_with_dsub_1_dsub_2_in_QPR, DQuad_with_ssub_0_and_DQuad_with_dsub_1_dsub_2_in_QPRBits, 1857, 8, sizeof(DQuad_with_ssub_0_and_DQuad_with_dsub_1_dsub_2_in_QPRBits), ARM_DQuad_with_ssub_0_and_DQuad_with_dsub_1_dsub_2_in_QPRRegClassID, 32, 32, 1, 1 },
  { DQuad_with_dsub_1_dsub_2_in_QPR_VFP2, DQuad_with_dsub_1_dsub_2_in_QPR_VFP2Bits, 595, 7, sizeof(DQuad_with_dsub_1_dsub_2_in_QPR_VFP2Bits), ARM_DQuad_with_dsub_1_dsub_2_in_QPR_VFP2RegClassID, 32, 32, 1, 1 },
  { DQuad_with_dsub_1_in_DPR_8, DQuad_with_dsub_1_in_DPR_8Bits, 930, 7, sizeof(DQuad_with_dsub_1_in_DPR_8Bits), ARM_DQuad_with_dsub_1_in_DPR_8RegClassID, 32, 32, 1, 1 },
  { DQuad_with_qsub_1_in_QPR_VFP2, DQuad_with_qsub_1_in_QPR_VFP2Bits, 565, 7, sizeof(DQuad_with_qsub_1_in_QPR_VFP2Bits), ARM_DQuad_with_qsub_1_in_QPR_VFP2RegClassID, 32, 32, 1, 1 },
  { DQuad_with_dsub_2_in_DPR_8, DQuad_with_dsub_2_in_DPR_8Bits, 1133, 6, sizeof(DQuad_with_dsub_2_in_DPR_8Bits), ARM_DQuad_with_dsub_2_in_DPR_8RegClassID, 32, 32, 1, 1 },
  { DQuad_with_dsub_3_then_ssub_0_and_DQuad_with_dsub_1_dsub_2_in_QPR, DQuad_with_dsub_3_then_ssub_0_and_DQuad_with_dsub_1_dsub_2_in_QPRBits, 1911, 6, sizeof(DQuad_with_dsub_3_then_ssub_0_and_DQuad_with_dsub_1_dsub_2_in_QPRBits), ARM_DQuad_with_dsub_3_then_ssub_0_and_DQuad_with_dsub_1_dsub_2_in_QPRRegClassID, 32, 32, 1, 1 },
  { DQuad_with_dsub_3_in_DPR_8, DQuad_with_dsub_3_in_DPR_8Bits, 1189, 5, sizeof(DQuad_with_dsub_3_in_DPR_8Bits), ARM_DQuad_with_dsub_3_in_DPR_8RegClassID, 32, 32, 1, 1 },
  { DQuad_with_dsub_0_in_DPR_8_and_DQuad_with_dsub_1_dsub_2_in_QPR, DQuad_with_dsub_0_in_DPR_8_and_DQuad_with_dsub_1_dsub_2_in_QPRBits, 1977, 4, sizeof(DQuad_with_dsub_0_in_DPR_8_and_DQuad_with_dsub_1_dsub_2_in_QPRBits), ARM_DQuad_with_dsub_0_in_DPR_8_and_DQuad_with_dsub_1_dsub_2_in_QPRRegClassID, 32, 32, 1, 1 },
  { DQuad_with_qsub_0_in_QPR_8, DQuad_with_qsub_0_in_QPR_8Bits, 1334, 4, sizeof(DQuad_with_qsub_0_in_QPR_8Bits), ARM_DQuad_with_qsub_0_in_QPR_8RegClassID, 32, 32, 1, 1 },
  { DQuad_with_dsub_1_dsub_2_in_QPR_8, DQuad_with_dsub_1_dsub_2_in_QPR_8Bits, 1417, 3, sizeof(DQuad_with_dsub_1_dsub_2_in_QPR_8Bits), ARM_DQuad_with_dsub_1_dsub_2_in_QPR_8RegClassID, 32, 32, 1, 1 },
  { DQuad_with_qsub_1_in_QPR_8, DQuad_with_qsub_1_in_QPR_8Bits, 1390, 3, sizeof(DQuad_with_qsub_1_in_QPR_8Bits), ARM_DQuad_with_qsub_1_in_QPR_8RegClassID, 32, 32, 1, 1 },
  { DQuad_with_dsub_3_in_DPR_8_and_DQuad_with_dsub_1_dsub_2_in_QPR, DQuad_with_dsub_3_in_DPR_8_and_DQuad_with_dsub_1_dsub_2_in_QPRBits, 2040, 2, sizeof(DQuad_with_dsub_3_in_DPR_8_and_DQuad_with_dsub_1_dsub_2_in_QPRBits), ARM_DQuad_with_dsub_3_in_DPR_8_and_DQuad_with_dsub_1_dsub_2_in_QPRRegClassID, 32, 32, 1, 1 },
  { QQQQPR, QQQQPRBits, 1727, 13, sizeof(QQQQPRBits), ARM_QQQQPRRegClassID, 64, 32, 1, 1 },
  { QQQQPR_with_ssub_0, QQQQPR_with_ssub_0Bits, 0, 8, sizeof(QQQQPR_with_ssub_0Bits), ARM_QQQQPR_with_ssub_0RegClassID, 64, 32, 1, 1 },
  { QQQQPR_with_dsub_2_then_ssub_0, QQQQPR_with_dsub_2_then_ssub_0Bits, 140, 7, sizeof(QQQQPR_with_dsub_2_then_ssub_0Bits), ARM_QQQQPR_with_dsub_2_then_ssub_0RegClassID, 64, 32, 1, 1 },
  { QQQQPR_with_dsub_5_then_ssub_0, QQQQPR_with_dsub_5_then_ssub_0Bits, 432, 6, sizeof(QQQQPR_with_dsub_5_then_ssub_0Bits), ARM_QQQQPR_with_dsub_5_then_ssub_0RegClassID, 64, 32, 1, 1 },
  { QQQQPR_with_dsub_7_then_ssub_0, QQQQPR_with_dsub_7_then_ssub_0Bits, 463, 5, sizeof(QQQQPR_with_dsub_7_then_ssub_0Bits), ARM_QQQQPR_with_dsub_7_then_ssub_0RegClassID, 64, 32, 1, 1 },
  { QQQQPR_with_dsub_0_in_DPR_8, QQQQPR_with_dsub_0_in_DPR_8Bits, 727, 4, sizeof(QQQQPR_with_dsub_0_in_DPR_8Bits), ARM_QQQQPR_with_dsub_0_in_DPR_8RegClassID, 64, 32, 1, 1 },
  { QQQQPR_with_dsub_2_in_DPR_8, QQQQPR_with_dsub_2_in_DPR_8Bits, 1013, 3, sizeof(QQQQPR_with_dsub_2_in_DPR_8Bits), ARM_QQQQPR_with_dsub_2_in_DPR_8RegClassID, 64, 32, 1, 1 },
  { QQQQPR_with_dsub_4_in_DPR_8, QQQQPR_with_dsub_4_in_DPR_8Bits, 1216, 2, sizeof(QQQQPR_with_dsub_4_in_DPR_8Bits), ARM_QQQQPR_with_dsub_4_in_DPR_8RegClassID, 64, 32, 1, 1 },
  { QQQQPR_with_dsub_6_in_DPR_8, QQQQPR_with_dsub_6_in_DPR_8Bits, 1306, 1, sizeof(QQQQPR_with_dsub_6_in_DPR_8Bits), ARM_QQQQPR_with_dsub_6_in_DPR_8RegClassID, 64, 32, 1, 1 },
};

#endif // GET_REGINFO_MC_DESC
