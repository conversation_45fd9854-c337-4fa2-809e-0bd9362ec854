// For Capstone Engine. AUTO-GENERATED FILE, DO NOT EDIT
package capstone;

public class Ppc_const {

	// PPC branch codes for some branch instructions

	public static final int PPC_BC_INVALID = 0;
	public static final int PPC_BC_LT = (0<<5)|12;
	public static final int PPC_BC_LE = (1<<5)|4;
	public static final int PPC_BC_EQ = (2<<5)|12;
	public static final int PPC_BC_GE = (0<<5)|4;
	public static final int PPC_BC_GT = (1<<5)|12;
	public static final int PPC_BC_NE = (2<<5)|4;
	public static final int PPC_BC_UN = (3<<5)|12;
	public static final int PPC_BC_NU = (3<<5)|4;
	public static final int PPC_BC_SO = (4<<5)|12;
	public static final int PPC_BC_NS = (4<<5)|4;

	// PPC branch hint for some branch instructions

	public static final int PPC_BH_INVALID = 0;
	public static final int PPC_BH_PLUS = 1;
	public static final int PPC_BH_MINUS = 2;

	// Operand type for instruction's operands

	public static final int PPC_OP_INVALID = 0;
	public static final int PPC_OP_REG = 1;
	public static final int PPC_OP_IMM = 2;
	public static final int PPC_OP_MEM = 3;
	public static final int PPC_OP_CRX = 64;

	// PPC registers

	public static final int PPC_REG_INVALID = 0;
	public static final int PPC_REG_CARRY = 1;
	public static final int PPC_REG_CR0 = 2;
	public static final int PPC_REG_CR1 = 3;
	public static final int PPC_REG_CR2 = 4;
	public static final int PPC_REG_CR3 = 5;
	public static final int PPC_REG_CR4 = 6;
	public static final int PPC_REG_CR5 = 7;
	public static final int PPC_REG_CR6 = 8;
	public static final int PPC_REG_CR7 = 9;
	public static final int PPC_REG_CTR = 10;
	public static final int PPC_REG_F0 = 11;
	public static final int PPC_REG_F1 = 12;
	public static final int PPC_REG_F2 = 13;
	public static final int PPC_REG_F3 = 14;
	public static final int PPC_REG_F4 = 15;
	public static final int PPC_REG_F5 = 16;
	public static final int PPC_REG_F6 = 17;
	public static final int PPC_REG_F7 = 18;
	public static final int PPC_REG_F8 = 19;
	public static final int PPC_REG_F9 = 20;
	public static final int PPC_REG_F10 = 21;
	public static final int PPC_REG_F11 = 22;
	public static final int PPC_REG_F12 = 23;
	public static final int PPC_REG_F13 = 24;
	public static final int PPC_REG_F14 = 25;
	public static final int PPC_REG_F15 = 26;
	public static final int PPC_REG_F16 = 27;
	public static final int PPC_REG_F17 = 28;
	public static final int PPC_REG_F18 = 29;
	public static final int PPC_REG_F19 = 30;
	public static final int PPC_REG_F20 = 31;
	public static final int PPC_REG_F21 = 32;
	public static final int PPC_REG_F22 = 33;
	public static final int PPC_REG_F23 = 34;
	public static final int PPC_REG_F24 = 35;
	public static final int PPC_REG_F25 = 36;
	public static final int PPC_REG_F26 = 37;
	public static final int PPC_REG_F27 = 38;
	public static final int PPC_REG_F28 = 39;
	public static final int PPC_REG_F29 = 40;
	public static final int PPC_REG_F30 = 41;
	public static final int PPC_REG_F31 = 42;
	public static final int PPC_REG_LR = 43;
	public static final int PPC_REG_R0 = 44;
	public static final int PPC_REG_R1 = 45;
	public static final int PPC_REG_R2 = 46;
	public static final int PPC_REG_R3 = 47;
	public static final int PPC_REG_R4 = 48;
	public static final int PPC_REG_R5 = 49;
	public static final int PPC_REG_R6 = 50;
	public static final int PPC_REG_R7 = 51;
	public static final int PPC_REG_R8 = 52;
	public static final int PPC_REG_R9 = 53;
	public static final int PPC_REG_R10 = 54;
	public static final int PPC_REG_R11 = 55;
	public static final int PPC_REG_R12 = 56;
	public static final int PPC_REG_R13 = 57;
	public static final int PPC_REG_R14 = 58;
	public static final int PPC_REG_R15 = 59;
	public static final int PPC_REG_R16 = 60;
	public static final int PPC_REG_R17 = 61;
	public static final int PPC_REG_R18 = 62;
	public static final int PPC_REG_R19 = 63;
	public static final int PPC_REG_R20 = 64;
	public static final int PPC_REG_R21 = 65;
	public static final int PPC_REG_R22 = 66;
	public static final int PPC_REG_R23 = 67;
	public static final int PPC_REG_R24 = 68;
	public static final int PPC_REG_R25 = 69;
	public static final int PPC_REG_R26 = 70;
	public static final int PPC_REG_R27 = 71;
	public static final int PPC_REG_R28 = 72;
	public static final int PPC_REG_R29 = 73;
	public static final int PPC_REG_R30 = 74;
	public static final int PPC_REG_R31 = 75;
	public static final int PPC_REG_V0 = 76;
	public static final int PPC_REG_V1 = 77;
	public static final int PPC_REG_V2 = 78;
	public static final int PPC_REG_V3 = 79;
	public static final int PPC_REG_V4 = 80;
	public static final int PPC_REG_V5 = 81;
	public static final int PPC_REG_V6 = 82;
	public static final int PPC_REG_V7 = 83;
	public static final int PPC_REG_V8 = 84;
	public static final int PPC_REG_V9 = 85;
	public static final int PPC_REG_V10 = 86;
	public static final int PPC_REG_V11 = 87;
	public static final int PPC_REG_V12 = 88;
	public static final int PPC_REG_V13 = 89;
	public static final int PPC_REG_V14 = 90;
	public static final int PPC_REG_V15 = 91;
	public static final int PPC_REG_V16 = 92;
	public static final int PPC_REG_V17 = 93;
	public static final int PPC_REG_V18 = 94;
	public static final int PPC_REG_V19 = 95;
	public static final int PPC_REG_V20 = 96;
	public static final int PPC_REG_V21 = 97;
	public static final int PPC_REG_V22 = 98;
	public static final int PPC_REG_V23 = 99;
	public static final int PPC_REG_V24 = 100;
	public static final int PPC_REG_V25 = 101;
	public static final int PPC_REG_V26 = 102;
	public static final int PPC_REG_V27 = 103;
	public static final int PPC_REG_V28 = 104;
	public static final int PPC_REG_V29 = 105;
	public static final int PPC_REG_V30 = 106;
	public static final int PPC_REG_V31 = 107;
	public static final int PPC_REG_VRSAVE = 108;
	public static final int PPC_REG_VS0 = 109;
	public static final int PPC_REG_VS1 = 110;
	public static final int PPC_REG_VS2 = 111;
	public static final int PPC_REG_VS3 = 112;
	public static final int PPC_REG_VS4 = 113;
	public static final int PPC_REG_VS5 = 114;
	public static final int PPC_REG_VS6 = 115;
	public static final int PPC_REG_VS7 = 116;
	public static final int PPC_REG_VS8 = 117;
	public static final int PPC_REG_VS9 = 118;
	public static final int PPC_REG_VS10 = 119;
	public static final int PPC_REG_VS11 = 120;
	public static final int PPC_REG_VS12 = 121;
	public static final int PPC_REG_VS13 = 122;
	public static final int PPC_REG_VS14 = 123;
	public static final int PPC_REG_VS15 = 124;
	public static final int PPC_REG_VS16 = 125;
	public static final int PPC_REG_VS17 = 126;
	public static final int PPC_REG_VS18 = 127;
	public static final int PPC_REG_VS19 = 128;
	public static final int PPC_REG_VS20 = 129;
	public static final int PPC_REG_VS21 = 130;
	public static final int PPC_REG_VS22 = 131;
	public static final int PPC_REG_VS23 = 132;
	public static final int PPC_REG_VS24 = 133;
	public static final int PPC_REG_VS25 = 134;
	public static final int PPC_REG_VS26 = 135;
	public static final int PPC_REG_VS27 = 136;
	public static final int PPC_REG_VS28 = 137;
	public static final int PPC_REG_VS29 = 138;
	public static final int PPC_REG_VS30 = 139;
	public static final int PPC_REG_VS31 = 140;
	public static final int PPC_REG_VS32 = 141;
	public static final int PPC_REG_VS33 = 142;
	public static final int PPC_REG_VS34 = 143;
	public static final int PPC_REG_VS35 = 144;
	public static final int PPC_REG_VS36 = 145;
	public static final int PPC_REG_VS37 = 146;
	public static final int PPC_REG_VS38 = 147;
	public static final int PPC_REG_VS39 = 148;
	public static final int PPC_REG_VS40 = 149;
	public static final int PPC_REG_VS41 = 150;
	public static final int PPC_REG_VS42 = 151;
	public static final int PPC_REG_VS43 = 152;
	public static final int PPC_REG_VS44 = 153;
	public static final int PPC_REG_VS45 = 154;
	public static final int PPC_REG_VS46 = 155;
	public static final int PPC_REG_VS47 = 156;
	public static final int PPC_REG_VS48 = 157;
	public static final int PPC_REG_VS49 = 158;
	public static final int PPC_REG_VS50 = 159;
	public static final int PPC_REG_VS51 = 160;
	public static final int PPC_REG_VS52 = 161;
	public static final int PPC_REG_VS53 = 162;
	public static final int PPC_REG_VS54 = 163;
	public static final int PPC_REG_VS55 = 164;
	public static final int PPC_REG_VS56 = 165;
	public static final int PPC_REG_VS57 = 166;
	public static final int PPC_REG_VS58 = 167;
	public static final int PPC_REG_VS59 = 168;
	public static final int PPC_REG_VS60 = 169;
	public static final int PPC_REG_VS61 = 170;
	public static final int PPC_REG_VS62 = 171;
	public static final int PPC_REG_VS63 = 172;
	public static final int PPC_REG_Q0 = 173;
	public static final int PPC_REG_Q1 = 174;
	public static final int PPC_REG_Q2 = 175;
	public static final int PPC_REG_Q3 = 176;
	public static final int PPC_REG_Q4 = 177;
	public static final int PPC_REG_Q5 = 178;
	public static final int PPC_REG_Q6 = 179;
	public static final int PPC_REG_Q7 = 180;
	public static final int PPC_REG_Q8 = 181;
	public static final int PPC_REG_Q9 = 182;
	public static final int PPC_REG_Q10 = 183;
	public static final int PPC_REG_Q11 = 184;
	public static final int PPC_REG_Q12 = 185;
	public static final int PPC_REG_Q13 = 186;
	public static final int PPC_REG_Q14 = 187;
	public static final int PPC_REG_Q15 = 188;
	public static final int PPC_REG_Q16 = 189;
	public static final int PPC_REG_Q17 = 190;
	public static final int PPC_REG_Q18 = 191;
	public static final int PPC_REG_Q19 = 192;
	public static final int PPC_REG_Q20 = 193;
	public static final int PPC_REG_Q21 = 194;
	public static final int PPC_REG_Q22 = 195;
	public static final int PPC_REG_Q23 = 196;
	public static final int PPC_REG_Q24 = 197;
	public static final int PPC_REG_Q25 = 198;
	public static final int PPC_REG_Q26 = 199;
	public static final int PPC_REG_Q27 = 200;
	public static final int PPC_REG_Q28 = 201;
	public static final int PPC_REG_Q29 = 202;
	public static final int PPC_REG_Q30 = 203;
	public static final int PPC_REG_Q31 = 204;
	public static final int PPC_REG_RM = 205;
	public static final int PPC_REG_CTR8 = 206;
	public static final int PPC_REG_LR8 = 207;
	public static final int PPC_REG_CR1EQ = 208;
	public static final int PPC_REG_X2 = 209;
	public static final int PPC_REG_ENDING = 210;

	// PPC instruction

	public static final int PPC_INS_INVALID = 0;
	public static final int PPC_INS_ADD = 1;
	public static final int PPC_INS_ADDC = 2;
	public static final int PPC_INS_ADDE = 3;
	public static final int PPC_INS_ADDI = 4;
	public static final int PPC_INS_ADDIC = 5;
	public static final int PPC_INS_ADDIS = 6;
	public static final int PPC_INS_ADDME = 7;
	public static final int PPC_INS_ADDZE = 8;
	public static final int PPC_INS_AND = 9;
	public static final int PPC_INS_ANDC = 10;
	public static final int PPC_INS_ANDIS = 11;
	public static final int PPC_INS_ANDI = 12;
	public static final int PPC_INS_ATTN = 13;
	public static final int PPC_INS_B = 14;
	public static final int PPC_INS_BA = 15;
	public static final int PPC_INS_BC = 16;
	public static final int PPC_INS_BCCTR = 17;
	public static final int PPC_INS_BCCTRL = 18;
	public static final int PPC_INS_BCL = 19;
	public static final int PPC_INS_BCLR = 20;
	public static final int PPC_INS_BCLRL = 21;
	public static final int PPC_INS_BCTR = 22;
	public static final int PPC_INS_BCTRL = 23;
	public static final int PPC_INS_BCT = 24;
	public static final int PPC_INS_BDNZ = 25;
	public static final int PPC_INS_BDNZA = 26;
	public static final int PPC_INS_BDNZL = 27;
	public static final int PPC_INS_BDNZLA = 28;
	public static final int PPC_INS_BDNZLR = 29;
	public static final int PPC_INS_BDNZLRL = 30;
	public static final int PPC_INS_BDZ = 31;
	public static final int PPC_INS_BDZA = 32;
	public static final int PPC_INS_BDZL = 33;
	public static final int PPC_INS_BDZLA = 34;
	public static final int PPC_INS_BDZLR = 35;
	public static final int PPC_INS_BDZLRL = 36;
	public static final int PPC_INS_BL = 37;
	public static final int PPC_INS_BLA = 38;
	public static final int PPC_INS_BLR = 39;
	public static final int PPC_INS_BLRL = 40;
	public static final int PPC_INS_BRINC = 41;
	public static final int PPC_INS_CMPB = 42;
	public static final int PPC_INS_CMPD = 43;
	public static final int PPC_INS_CMPDI = 44;
	public static final int PPC_INS_CMPLD = 45;
	public static final int PPC_INS_CMPLDI = 46;
	public static final int PPC_INS_CMPLW = 47;
	public static final int PPC_INS_CMPLWI = 48;
	public static final int PPC_INS_CMPW = 49;
	public static final int PPC_INS_CMPWI = 50;
	public static final int PPC_INS_CNTLZD = 51;
	public static final int PPC_INS_CNTLZW = 52;
	public static final int PPC_INS_CREQV = 53;
	public static final int PPC_INS_CRXOR = 54;
	public static final int PPC_INS_CRAND = 55;
	public static final int PPC_INS_CRANDC = 56;
	public static final int PPC_INS_CRNAND = 57;
	public static final int PPC_INS_CRNOR = 58;
	public static final int PPC_INS_CROR = 59;
	public static final int PPC_INS_CRORC = 60;
	public static final int PPC_INS_DCBA = 61;
	public static final int PPC_INS_DCBF = 62;
	public static final int PPC_INS_DCBI = 63;
	public static final int PPC_INS_DCBST = 64;
	public static final int PPC_INS_DCBT = 65;
	public static final int PPC_INS_DCBTST = 66;
	public static final int PPC_INS_DCBZ = 67;
	public static final int PPC_INS_DCBZL = 68;
	public static final int PPC_INS_DCCCI = 69;
	public static final int PPC_INS_DIVD = 70;
	public static final int PPC_INS_DIVDU = 71;
	public static final int PPC_INS_DIVW = 72;
	public static final int PPC_INS_DIVWU = 73;
	public static final int PPC_INS_DSS = 74;
	public static final int PPC_INS_DSSALL = 75;
	public static final int PPC_INS_DST = 76;
	public static final int PPC_INS_DSTST = 77;
	public static final int PPC_INS_DSTSTT = 78;
	public static final int PPC_INS_DSTT = 79;
	public static final int PPC_INS_EQV = 80;
	public static final int PPC_INS_EVABS = 81;
	public static final int PPC_INS_EVADDIW = 82;
	public static final int PPC_INS_EVADDSMIAAW = 83;
	public static final int PPC_INS_EVADDSSIAAW = 84;
	public static final int PPC_INS_EVADDUMIAAW = 85;
	public static final int PPC_INS_EVADDUSIAAW = 86;
	public static final int PPC_INS_EVADDW = 87;
	public static final int PPC_INS_EVAND = 88;
	public static final int PPC_INS_EVANDC = 89;
	public static final int PPC_INS_EVCMPEQ = 90;
	public static final int PPC_INS_EVCMPGTS = 91;
	public static final int PPC_INS_EVCMPGTU = 92;
	public static final int PPC_INS_EVCMPLTS = 93;
	public static final int PPC_INS_EVCMPLTU = 94;
	public static final int PPC_INS_EVCNTLSW = 95;
	public static final int PPC_INS_EVCNTLZW = 96;
	public static final int PPC_INS_EVDIVWS = 97;
	public static final int PPC_INS_EVDIVWU = 98;
	public static final int PPC_INS_EVEQV = 99;
	public static final int PPC_INS_EVEXTSB = 100;
	public static final int PPC_INS_EVEXTSH = 101;
	public static final int PPC_INS_EVLDD = 102;
	public static final int PPC_INS_EVLDDX = 103;
	public static final int PPC_INS_EVLDH = 104;
	public static final int PPC_INS_EVLDHX = 105;
	public static final int PPC_INS_EVLDW = 106;
	public static final int PPC_INS_EVLDWX = 107;
	public static final int PPC_INS_EVLHHESPLAT = 108;
	public static final int PPC_INS_EVLHHESPLATX = 109;
	public static final int PPC_INS_EVLHHOSSPLAT = 110;
	public static final int PPC_INS_EVLHHOSSPLATX = 111;
	public static final int PPC_INS_EVLHHOUSPLAT = 112;
	public static final int PPC_INS_EVLHHOUSPLATX = 113;
	public static final int PPC_INS_EVLWHE = 114;
	public static final int PPC_INS_EVLWHEX = 115;
	public static final int PPC_INS_EVLWHOS = 116;
	public static final int PPC_INS_EVLWHOSX = 117;
	public static final int PPC_INS_EVLWHOU = 118;
	public static final int PPC_INS_EVLWHOUX = 119;
	public static final int PPC_INS_EVLWHSPLAT = 120;
	public static final int PPC_INS_EVLWHSPLATX = 121;
	public static final int PPC_INS_EVLWWSPLAT = 122;
	public static final int PPC_INS_EVLWWSPLATX = 123;
	public static final int PPC_INS_EVMERGEHI = 124;
	public static final int PPC_INS_EVMERGEHILO = 125;
	public static final int PPC_INS_EVMERGELO = 126;
	public static final int PPC_INS_EVMERGELOHI = 127;
	public static final int PPC_INS_EVMHEGSMFAA = 128;
	public static final int PPC_INS_EVMHEGSMFAN = 129;
	public static final int PPC_INS_EVMHEGSMIAA = 130;
	public static final int PPC_INS_EVMHEGSMIAN = 131;
	public static final int PPC_INS_EVMHEGUMIAA = 132;
	public static final int PPC_INS_EVMHEGUMIAN = 133;
	public static final int PPC_INS_EVMHESMF = 134;
	public static final int PPC_INS_EVMHESMFA = 135;
	public static final int PPC_INS_EVMHESMFAAW = 136;
	public static final int PPC_INS_EVMHESMFANW = 137;
	public static final int PPC_INS_EVMHESMI = 138;
	public static final int PPC_INS_EVMHESMIA = 139;
	public static final int PPC_INS_EVMHESMIAAW = 140;
	public static final int PPC_INS_EVMHESMIANW = 141;
	public static final int PPC_INS_EVMHESSF = 142;
	public static final int PPC_INS_EVMHESSFA = 143;
	public static final int PPC_INS_EVMHESSFAAW = 144;
	public static final int PPC_INS_EVMHESSFANW = 145;
	public static final int PPC_INS_EVMHESSIAAW = 146;
	public static final int PPC_INS_EVMHESSIANW = 147;
	public static final int PPC_INS_EVMHEUMI = 148;
	public static final int PPC_INS_EVMHEUMIA = 149;
	public static final int PPC_INS_EVMHEUMIAAW = 150;
	public static final int PPC_INS_EVMHEUMIANW = 151;
	public static final int PPC_INS_EVMHEUSIAAW = 152;
	public static final int PPC_INS_EVMHEUSIANW = 153;
	public static final int PPC_INS_EVMHOGSMFAA = 154;
	public static final int PPC_INS_EVMHOGSMFAN = 155;
	public static final int PPC_INS_EVMHOGSMIAA = 156;
	public static final int PPC_INS_EVMHOGSMIAN = 157;
	public static final int PPC_INS_EVMHOGUMIAA = 158;
	public static final int PPC_INS_EVMHOGUMIAN = 159;
	public static final int PPC_INS_EVMHOSMF = 160;
	public static final int PPC_INS_EVMHOSMFA = 161;
	public static final int PPC_INS_EVMHOSMFAAW = 162;
	public static final int PPC_INS_EVMHOSMFANW = 163;
	public static final int PPC_INS_EVMHOSMI = 164;
	public static final int PPC_INS_EVMHOSMIA = 165;
	public static final int PPC_INS_EVMHOSMIAAW = 166;
	public static final int PPC_INS_EVMHOSMIANW = 167;
	public static final int PPC_INS_EVMHOSSF = 168;
	public static final int PPC_INS_EVMHOSSFA = 169;
	public static final int PPC_INS_EVMHOSSFAAW = 170;
	public static final int PPC_INS_EVMHOSSFANW = 171;
	public static final int PPC_INS_EVMHOSSIAAW = 172;
	public static final int PPC_INS_EVMHOSSIANW = 173;
	public static final int PPC_INS_EVMHOUMI = 174;
	public static final int PPC_INS_EVMHOUMIA = 175;
	public static final int PPC_INS_EVMHOUMIAAW = 176;
	public static final int PPC_INS_EVMHOUMIANW = 177;
	public static final int PPC_INS_EVMHOUSIAAW = 178;
	public static final int PPC_INS_EVMHOUSIANW = 179;
	public static final int PPC_INS_EVMRA = 180;
	public static final int PPC_INS_EVMWHSMF = 181;
	public static final int PPC_INS_EVMWHSMFA = 182;
	public static final int PPC_INS_EVMWHSMI = 183;
	public static final int PPC_INS_EVMWHSMIA = 184;
	public static final int PPC_INS_EVMWHSSF = 185;
	public static final int PPC_INS_EVMWHSSFA = 186;
	public static final int PPC_INS_EVMWHUMI = 187;
	public static final int PPC_INS_EVMWHUMIA = 188;
	public static final int PPC_INS_EVMWLSMIAAW = 189;
	public static final int PPC_INS_EVMWLSMIANW = 190;
	public static final int PPC_INS_EVMWLSSIAAW = 191;
	public static final int PPC_INS_EVMWLSSIANW = 192;
	public static final int PPC_INS_EVMWLUMI = 193;
	public static final int PPC_INS_EVMWLUMIA = 194;
	public static final int PPC_INS_EVMWLUMIAAW = 195;
	public static final int PPC_INS_EVMWLUMIANW = 196;
	public static final int PPC_INS_EVMWLUSIAAW = 197;
	public static final int PPC_INS_EVMWLUSIANW = 198;
	public static final int PPC_INS_EVMWSMF = 199;
	public static final int PPC_INS_EVMWSMFA = 200;
	public static final int PPC_INS_EVMWSMFAA = 201;
	public static final int PPC_INS_EVMWSMFAN = 202;
	public static final int PPC_INS_EVMWSMI = 203;
	public static final int PPC_INS_EVMWSMIA = 204;
	public static final int PPC_INS_EVMWSMIAA = 205;
	public static final int PPC_INS_EVMWSMIAN = 206;
	public static final int PPC_INS_EVMWSSF = 207;
	public static final int PPC_INS_EVMWSSFA = 208;
	public static final int PPC_INS_EVMWSSFAA = 209;
	public static final int PPC_INS_EVMWSSFAN = 210;
	public static final int PPC_INS_EVMWUMI = 211;
	public static final int PPC_INS_EVMWUMIA = 212;
	public static final int PPC_INS_EVMWUMIAA = 213;
	public static final int PPC_INS_EVMWUMIAN = 214;
	public static final int PPC_INS_EVNAND = 215;
	public static final int PPC_INS_EVNEG = 216;
	public static final int PPC_INS_EVNOR = 217;
	public static final int PPC_INS_EVOR = 218;
	public static final int PPC_INS_EVORC = 219;
	public static final int PPC_INS_EVRLW = 220;
	public static final int PPC_INS_EVRLWI = 221;
	public static final int PPC_INS_EVRNDW = 222;
	public static final int PPC_INS_EVSLW = 223;
	public static final int PPC_INS_EVSLWI = 224;
	public static final int PPC_INS_EVSPLATFI = 225;
	public static final int PPC_INS_EVSPLATI = 226;
	public static final int PPC_INS_EVSRWIS = 227;
	public static final int PPC_INS_EVSRWIU = 228;
	public static final int PPC_INS_EVSRWS = 229;
	public static final int PPC_INS_EVSRWU = 230;
	public static final int PPC_INS_EVSTDD = 231;
	public static final int PPC_INS_EVSTDDX = 232;
	public static final int PPC_INS_EVSTDH = 233;
	public static final int PPC_INS_EVSTDHX = 234;
	public static final int PPC_INS_EVSTDW = 235;
	public static final int PPC_INS_EVSTDWX = 236;
	public static final int PPC_INS_EVSTWHE = 237;
	public static final int PPC_INS_EVSTWHEX = 238;
	public static final int PPC_INS_EVSTWHO = 239;
	public static final int PPC_INS_EVSTWHOX = 240;
	public static final int PPC_INS_EVSTWWE = 241;
	public static final int PPC_INS_EVSTWWEX = 242;
	public static final int PPC_INS_EVSTWWO = 243;
	public static final int PPC_INS_EVSTWWOX = 244;
	public static final int PPC_INS_EVSUBFSMIAAW = 245;
	public static final int PPC_INS_EVSUBFSSIAAW = 246;
	public static final int PPC_INS_EVSUBFUMIAAW = 247;
	public static final int PPC_INS_EVSUBFUSIAAW = 248;
	public static final int PPC_INS_EVSUBFW = 249;
	public static final int PPC_INS_EVSUBIFW = 250;
	public static final int PPC_INS_EVXOR = 251;
	public static final int PPC_INS_EXTSB = 252;
	public static final int PPC_INS_EXTSH = 253;
	public static final int PPC_INS_EXTSW = 254;
	public static final int PPC_INS_EIEIO = 255;
	public static final int PPC_INS_FABS = 256;
	public static final int PPC_INS_FADD = 257;
	public static final int PPC_INS_FADDS = 258;
	public static final int PPC_INS_FCFID = 259;
	public static final int PPC_INS_FCFIDS = 260;
	public static final int PPC_INS_FCFIDU = 261;
	public static final int PPC_INS_FCFIDUS = 262;
	public static final int PPC_INS_FCMPU = 263;
	public static final int PPC_INS_FCPSGN = 264;
	public static final int PPC_INS_FCTID = 265;
	public static final int PPC_INS_FCTIDUZ = 266;
	public static final int PPC_INS_FCTIDZ = 267;
	public static final int PPC_INS_FCTIW = 268;
	public static final int PPC_INS_FCTIWUZ = 269;
	public static final int PPC_INS_FCTIWZ = 270;
	public static final int PPC_INS_FDIV = 271;
	public static final int PPC_INS_FDIVS = 272;
	public static final int PPC_INS_FMADD = 273;
	public static final int PPC_INS_FMADDS = 274;
	public static final int PPC_INS_FMR = 275;
	public static final int PPC_INS_FMSUB = 276;
	public static final int PPC_INS_FMSUBS = 277;
	public static final int PPC_INS_FMUL = 278;
	public static final int PPC_INS_FMULS = 279;
	public static final int PPC_INS_FNABS = 280;
	public static final int PPC_INS_FNEG = 281;
	public static final int PPC_INS_FNMADD = 282;
	public static final int PPC_INS_FNMADDS = 283;
	public static final int PPC_INS_FNMSUB = 284;
	public static final int PPC_INS_FNMSUBS = 285;
	public static final int PPC_INS_FRE = 286;
	public static final int PPC_INS_FRES = 287;
	public static final int PPC_INS_FRIM = 288;
	public static final int PPC_INS_FRIN = 289;
	public static final int PPC_INS_FRIP = 290;
	public static final int PPC_INS_FRIZ = 291;
	public static final int PPC_INS_FRSP = 292;
	public static final int PPC_INS_FRSQRTE = 293;
	public static final int PPC_INS_FRSQRTES = 294;
	public static final int PPC_INS_FSEL = 295;
	public static final int PPC_INS_FSQRT = 296;
	public static final int PPC_INS_FSQRTS = 297;
	public static final int PPC_INS_FSUB = 298;
	public static final int PPC_INS_FSUBS = 299;
	public static final int PPC_INS_ICBI = 300;
	public static final int PPC_INS_ICBT = 301;
	public static final int PPC_INS_ICCCI = 302;
	public static final int PPC_INS_ISEL = 303;
	public static final int PPC_INS_ISYNC = 304;
	public static final int PPC_INS_LA = 305;
	public static final int PPC_INS_LBZ = 306;
	public static final int PPC_INS_LBZCIX = 307;
	public static final int PPC_INS_LBZU = 308;
	public static final int PPC_INS_LBZUX = 309;
	public static final int PPC_INS_LBZX = 310;
	public static final int PPC_INS_LD = 311;
	public static final int PPC_INS_LDARX = 312;
	public static final int PPC_INS_LDBRX = 313;
	public static final int PPC_INS_LDCIX = 314;
	public static final int PPC_INS_LDU = 315;
	public static final int PPC_INS_LDUX = 316;
	public static final int PPC_INS_LDX = 317;
	public static final int PPC_INS_LFD = 318;
	public static final int PPC_INS_LFDU = 319;
	public static final int PPC_INS_LFDUX = 320;
	public static final int PPC_INS_LFDX = 321;
	public static final int PPC_INS_LFIWAX = 322;
	public static final int PPC_INS_LFIWZX = 323;
	public static final int PPC_INS_LFS = 324;
	public static final int PPC_INS_LFSU = 325;
	public static final int PPC_INS_LFSUX = 326;
	public static final int PPC_INS_LFSX = 327;
	public static final int PPC_INS_LHA = 328;
	public static final int PPC_INS_LHAU = 329;
	public static final int PPC_INS_LHAUX = 330;
	public static final int PPC_INS_LHAX = 331;
	public static final int PPC_INS_LHBRX = 332;
	public static final int PPC_INS_LHZ = 333;
	public static final int PPC_INS_LHZCIX = 334;
	public static final int PPC_INS_LHZU = 335;
	public static final int PPC_INS_LHZUX = 336;
	public static final int PPC_INS_LHZX = 337;
	public static final int PPC_INS_LI = 338;
	public static final int PPC_INS_LIS = 339;
	public static final int PPC_INS_LMW = 340;
	public static final int PPC_INS_LSWI = 341;
	public static final int PPC_INS_LVEBX = 342;
	public static final int PPC_INS_LVEHX = 343;
	public static final int PPC_INS_LVEWX = 344;
	public static final int PPC_INS_LVSL = 345;
	public static final int PPC_INS_LVSR = 346;
	public static final int PPC_INS_LVX = 347;
	public static final int PPC_INS_LVXL = 348;
	public static final int PPC_INS_LWA = 349;
	public static final int PPC_INS_LWARX = 350;
	public static final int PPC_INS_LWAUX = 351;
	public static final int PPC_INS_LWAX = 352;
	public static final int PPC_INS_LWBRX = 353;
	public static final int PPC_INS_LWZ = 354;
	public static final int PPC_INS_LWZCIX = 355;
	public static final int PPC_INS_LWZU = 356;
	public static final int PPC_INS_LWZUX = 357;
	public static final int PPC_INS_LWZX = 358;
	public static final int PPC_INS_LXSDX = 359;
	public static final int PPC_INS_LXVD2X = 360;
	public static final int PPC_INS_LXVDSX = 361;
	public static final int PPC_INS_LXVW4X = 362;
	public static final int PPC_INS_MBAR = 363;
	public static final int PPC_INS_MCRF = 364;
	public static final int PPC_INS_MCRFS = 365;
	public static final int PPC_INS_MFCR = 366;
	public static final int PPC_INS_MFCTR = 367;
	public static final int PPC_INS_MFDCR = 368;
	public static final int PPC_INS_MFFS = 369;
	public static final int PPC_INS_MFLR = 370;
	public static final int PPC_INS_MFMSR = 371;
	public static final int PPC_INS_MFOCRF = 372;
	public static final int PPC_INS_MFSPR = 373;
	public static final int PPC_INS_MFSR = 374;
	public static final int PPC_INS_MFSRIN = 375;
	public static final int PPC_INS_MFTB = 376;
	public static final int PPC_INS_MFVSCR = 377;
	public static final int PPC_INS_MSYNC = 378;
	public static final int PPC_INS_MTCRF = 379;
	public static final int PPC_INS_MTCTR = 380;
	public static final int PPC_INS_MTDCR = 381;
	public static final int PPC_INS_MTFSB0 = 382;
	public static final int PPC_INS_MTFSB1 = 383;
	public static final int PPC_INS_MTFSF = 384;
	public static final int PPC_INS_MTFSFI = 385;
	public static final int PPC_INS_MTLR = 386;
	public static final int PPC_INS_MTMSR = 387;
	public static final int PPC_INS_MTMSRD = 388;
	public static final int PPC_INS_MTOCRF = 389;
	public static final int PPC_INS_MTSPR = 390;
	public static final int PPC_INS_MTSR = 391;
	public static final int PPC_INS_MTSRIN = 392;
	public static final int PPC_INS_MTVSCR = 393;
	public static final int PPC_INS_MULHD = 394;
	public static final int PPC_INS_MULHDU = 395;
	public static final int PPC_INS_MULHW = 396;
	public static final int PPC_INS_MULHWU = 397;
	public static final int PPC_INS_MULLD = 398;
	public static final int PPC_INS_MULLI = 399;
	public static final int PPC_INS_MULLW = 400;
	public static final int PPC_INS_NAND = 401;
	public static final int PPC_INS_NEG = 402;
	public static final int PPC_INS_NOP = 403;
	public static final int PPC_INS_ORI = 404;
	public static final int PPC_INS_NOR = 405;
	public static final int PPC_INS_OR = 406;
	public static final int PPC_INS_ORC = 407;
	public static final int PPC_INS_ORIS = 408;
	public static final int PPC_INS_POPCNTD = 409;
	public static final int PPC_INS_POPCNTW = 410;
	public static final int PPC_INS_QVALIGNI = 411;
	public static final int PPC_INS_QVESPLATI = 412;
	public static final int PPC_INS_QVFABS = 413;
	public static final int PPC_INS_QVFADD = 414;
	public static final int PPC_INS_QVFADDS = 415;
	public static final int PPC_INS_QVFCFID = 416;
	public static final int PPC_INS_QVFCFIDS = 417;
	public static final int PPC_INS_QVFCFIDU = 418;
	public static final int PPC_INS_QVFCFIDUS = 419;
	public static final int PPC_INS_QVFCMPEQ = 420;
	public static final int PPC_INS_QVFCMPGT = 421;
	public static final int PPC_INS_QVFCMPLT = 422;
	public static final int PPC_INS_QVFCPSGN = 423;
	public static final int PPC_INS_QVFCTID = 424;
	public static final int PPC_INS_QVFCTIDU = 425;
	public static final int PPC_INS_QVFCTIDUZ = 426;
	public static final int PPC_INS_QVFCTIDZ = 427;
	public static final int PPC_INS_QVFCTIW = 428;
	public static final int PPC_INS_QVFCTIWU = 429;
	public static final int PPC_INS_QVFCTIWUZ = 430;
	public static final int PPC_INS_QVFCTIWZ = 431;
	public static final int PPC_INS_QVFLOGICAL = 432;
	public static final int PPC_INS_QVFMADD = 433;
	public static final int PPC_INS_QVFMADDS = 434;
	public static final int PPC_INS_QVFMR = 435;
	public static final int PPC_INS_QVFMSUB = 436;
	public static final int PPC_INS_QVFMSUBS = 437;
	public static final int PPC_INS_QVFMUL = 438;
	public static final int PPC_INS_QVFMULS = 439;
	public static final int PPC_INS_QVFNABS = 440;
	public static final int PPC_INS_QVFNEG = 441;
	public static final int PPC_INS_QVFNMADD = 442;
	public static final int PPC_INS_QVFNMADDS = 443;
	public static final int PPC_INS_QVFNMSUB = 444;
	public static final int PPC_INS_QVFNMSUBS = 445;
	public static final int PPC_INS_QVFPERM = 446;
	public static final int PPC_INS_QVFRE = 447;
	public static final int PPC_INS_QVFRES = 448;
	public static final int PPC_INS_QVFRIM = 449;
	public static final int PPC_INS_QVFRIN = 450;
	public static final int PPC_INS_QVFRIP = 451;
	public static final int PPC_INS_QVFRIZ = 452;
	public static final int PPC_INS_QVFRSP = 453;
	public static final int PPC_INS_QVFRSQRTE = 454;
	public static final int PPC_INS_QVFRSQRTES = 455;
	public static final int PPC_INS_QVFSEL = 456;
	public static final int PPC_INS_QVFSUB = 457;
	public static final int PPC_INS_QVFSUBS = 458;
	public static final int PPC_INS_QVFTSTNAN = 459;
	public static final int PPC_INS_QVFXMADD = 460;
	public static final int PPC_INS_QVFXMADDS = 461;
	public static final int PPC_INS_QVFXMUL = 462;
	public static final int PPC_INS_QVFXMULS = 463;
	public static final int PPC_INS_QVFXXCPNMADD = 464;
	public static final int PPC_INS_QVFXXCPNMADDS = 465;
	public static final int PPC_INS_QVFXXMADD = 466;
	public static final int PPC_INS_QVFXXMADDS = 467;
	public static final int PPC_INS_QVFXXNPMADD = 468;
	public static final int PPC_INS_QVFXXNPMADDS = 469;
	public static final int PPC_INS_QVGPCI = 470;
	public static final int PPC_INS_QVLFCDUX = 471;
	public static final int PPC_INS_QVLFCDUXA = 472;
	public static final int PPC_INS_QVLFCDX = 473;
	public static final int PPC_INS_QVLFCDXA = 474;
	public static final int PPC_INS_QVLFCSUX = 475;
	public static final int PPC_INS_QVLFCSUXA = 476;
	public static final int PPC_INS_QVLFCSX = 477;
	public static final int PPC_INS_QVLFCSXA = 478;
	public static final int PPC_INS_QVLFDUX = 479;
	public static final int PPC_INS_QVLFDUXA = 480;
	public static final int PPC_INS_QVLFDX = 481;
	public static final int PPC_INS_QVLFDXA = 482;
	public static final int PPC_INS_QVLFIWAX = 483;
	public static final int PPC_INS_QVLFIWAXA = 484;
	public static final int PPC_INS_QVLFIWZX = 485;
	public static final int PPC_INS_QVLFIWZXA = 486;
	public static final int PPC_INS_QVLFSUX = 487;
	public static final int PPC_INS_QVLFSUXA = 488;
	public static final int PPC_INS_QVLFSX = 489;
	public static final int PPC_INS_QVLFSXA = 490;
	public static final int PPC_INS_QVLPCLDX = 491;
	public static final int PPC_INS_QVLPCLSX = 492;
	public static final int PPC_INS_QVLPCRDX = 493;
	public static final int PPC_INS_QVLPCRSX = 494;
	public static final int PPC_INS_QVSTFCDUX = 495;
	public static final int PPC_INS_QVSTFCDUXA = 496;
	public static final int PPC_INS_QVSTFCDUXI = 497;
	public static final int PPC_INS_QVSTFCDUXIA = 498;
	public static final int PPC_INS_QVSTFCDX = 499;
	public static final int PPC_INS_QVSTFCDXA = 500;
	public static final int PPC_INS_QVSTFCDXI = 501;
	public static final int PPC_INS_QVSTFCDXIA = 502;
	public static final int PPC_INS_QVSTFCSUX = 503;
	public static final int PPC_INS_QVSTFCSUXA = 504;
	public static final int PPC_INS_QVSTFCSUXI = 505;
	public static final int PPC_INS_QVSTFCSUXIA = 506;
	public static final int PPC_INS_QVSTFCSX = 507;
	public static final int PPC_INS_QVSTFCSXA = 508;
	public static final int PPC_INS_QVSTFCSXI = 509;
	public static final int PPC_INS_QVSTFCSXIA = 510;
	public static final int PPC_INS_QVSTFDUX = 511;
	public static final int PPC_INS_QVSTFDUXA = 512;
	public static final int PPC_INS_QVSTFDUXI = 513;
	public static final int PPC_INS_QVSTFDUXIA = 514;
	public static final int PPC_INS_QVSTFDX = 515;
	public static final int PPC_INS_QVSTFDXA = 516;
	public static final int PPC_INS_QVSTFDXI = 517;
	public static final int PPC_INS_QVSTFDXIA = 518;
	public static final int PPC_INS_QVSTFIWX = 519;
	public static final int PPC_INS_QVSTFIWXA = 520;
	public static final int PPC_INS_QVSTFSUX = 521;
	public static final int PPC_INS_QVSTFSUXA = 522;
	public static final int PPC_INS_QVSTFSUXI = 523;
	public static final int PPC_INS_QVSTFSUXIA = 524;
	public static final int PPC_INS_QVSTFSX = 525;
	public static final int PPC_INS_QVSTFSXA = 526;
	public static final int PPC_INS_QVSTFSXI = 527;
	public static final int PPC_INS_QVSTFSXIA = 528;
	public static final int PPC_INS_RFCI = 529;
	public static final int PPC_INS_RFDI = 530;
	public static final int PPC_INS_RFI = 531;
	public static final int PPC_INS_RFID = 532;
	public static final int PPC_INS_RFMCI = 533;
	public static final int PPC_INS_RLDCL = 534;
	public static final int PPC_INS_RLDCR = 535;
	public static final int PPC_INS_RLDIC = 536;
	public static final int PPC_INS_RLDICL = 537;
	public static final int PPC_INS_RLDICR = 538;
	public static final int PPC_INS_RLDIMI = 539;
	public static final int PPC_INS_RLWIMI = 540;
	public static final int PPC_INS_RLWINM = 541;
	public static final int PPC_INS_RLWNM = 542;
	public static final int PPC_INS_SC = 543;
	public static final int PPC_INS_SLBIA = 544;
	public static final int PPC_INS_SLBIE = 545;
	public static final int PPC_INS_SLBMFEE = 546;
	public static final int PPC_INS_SLBMTE = 547;
	public static final int PPC_INS_SLD = 548;
	public static final int PPC_INS_SLW = 549;
	public static final int PPC_INS_SRAD = 550;
	public static final int PPC_INS_SRADI = 551;
	public static final int PPC_INS_SRAW = 552;
	public static final int PPC_INS_SRAWI = 553;
	public static final int PPC_INS_SRD = 554;
	public static final int PPC_INS_SRW = 555;
	public static final int PPC_INS_STB = 556;
	public static final int PPC_INS_STBCIX = 557;
	public static final int PPC_INS_STBU = 558;
	public static final int PPC_INS_STBUX = 559;
	public static final int PPC_INS_STBX = 560;
	public static final int PPC_INS_STD = 561;
	public static final int PPC_INS_STDBRX = 562;
	public static final int PPC_INS_STDCIX = 563;
	public static final int PPC_INS_STDCX = 564;
	public static final int PPC_INS_STDU = 565;
	public static final int PPC_INS_STDUX = 566;
	public static final int PPC_INS_STDX = 567;
	public static final int PPC_INS_STFD = 568;
	public static final int PPC_INS_STFDU = 569;
	public static final int PPC_INS_STFDUX = 570;
	public static final int PPC_INS_STFDX = 571;
	public static final int PPC_INS_STFIWX = 572;
	public static final int PPC_INS_STFS = 573;
	public static final int PPC_INS_STFSU = 574;
	public static final int PPC_INS_STFSUX = 575;
	public static final int PPC_INS_STFSX = 576;
	public static final int PPC_INS_STH = 577;
	public static final int PPC_INS_STHBRX = 578;
	public static final int PPC_INS_STHCIX = 579;
	public static final int PPC_INS_STHU = 580;
	public static final int PPC_INS_STHUX = 581;
	public static final int PPC_INS_STHX = 582;
	public static final int PPC_INS_STMW = 583;
	public static final int PPC_INS_STSWI = 584;
	public static final int PPC_INS_STVEBX = 585;
	public static final int PPC_INS_STVEHX = 586;
	public static final int PPC_INS_STVEWX = 587;
	public static final int PPC_INS_STVX = 588;
	public static final int PPC_INS_STVXL = 589;
	public static final int PPC_INS_STW = 590;
	public static final int PPC_INS_STWBRX = 591;
	public static final int PPC_INS_STWCIX = 592;
	public static final int PPC_INS_STWCX = 593;
	public static final int PPC_INS_STWU = 594;
	public static final int PPC_INS_STWUX = 595;
	public static final int PPC_INS_STWX = 596;
	public static final int PPC_INS_STXSDX = 597;
	public static final int PPC_INS_STXVD2X = 598;
	public static final int PPC_INS_STXVW4X = 599;
	public static final int PPC_INS_SUBF = 600;
	public static final int PPC_INS_SUBFC = 601;
	public static final int PPC_INS_SUBFE = 602;
	public static final int PPC_INS_SUBFIC = 603;
	public static final int PPC_INS_SUBFME = 604;
	public static final int PPC_INS_SUBFZE = 605;
	public static final int PPC_INS_SYNC = 606;
	public static final int PPC_INS_TD = 607;
	public static final int PPC_INS_TDI = 608;
	public static final int PPC_INS_TLBIA = 609;
	public static final int PPC_INS_TLBIE = 610;
	public static final int PPC_INS_TLBIEL = 611;
	public static final int PPC_INS_TLBIVAX = 612;
	public static final int PPC_INS_TLBLD = 613;
	public static final int PPC_INS_TLBLI = 614;
	public static final int PPC_INS_TLBRE = 615;
	public static final int PPC_INS_TLBSX = 616;
	public static final int PPC_INS_TLBSYNC = 617;
	public static final int PPC_INS_TLBWE = 618;
	public static final int PPC_INS_TRAP = 619;
	public static final int PPC_INS_TW = 620;
	public static final int PPC_INS_TWI = 621;
	public static final int PPC_INS_VADDCUW = 622;
	public static final int PPC_INS_VADDFP = 623;
	public static final int PPC_INS_VADDSBS = 624;
	public static final int PPC_INS_VADDSHS = 625;
	public static final int PPC_INS_VADDSWS = 626;
	public static final int PPC_INS_VADDUBM = 627;
	public static final int PPC_INS_VADDUBS = 628;
	public static final int PPC_INS_VADDUDM = 629;
	public static final int PPC_INS_VADDUHM = 630;
	public static final int PPC_INS_VADDUHS = 631;
	public static final int PPC_INS_VADDUWM = 632;
	public static final int PPC_INS_VADDUWS = 633;
	public static final int PPC_INS_VAND = 634;
	public static final int PPC_INS_VANDC = 635;
	public static final int PPC_INS_VAVGSB = 636;
	public static final int PPC_INS_VAVGSH = 637;
	public static final int PPC_INS_VAVGSW = 638;
	public static final int PPC_INS_VAVGUB = 639;
	public static final int PPC_INS_VAVGUH = 640;
	public static final int PPC_INS_VAVGUW = 641;
	public static final int PPC_INS_VCFSX = 642;
	public static final int PPC_INS_VCFUX = 643;
	public static final int PPC_INS_VCLZB = 644;
	public static final int PPC_INS_VCLZD = 645;
	public static final int PPC_INS_VCLZH = 646;
	public static final int PPC_INS_VCLZW = 647;
	public static final int PPC_INS_VCMPBFP = 648;
	public static final int PPC_INS_VCMPEQFP = 649;
	public static final int PPC_INS_VCMPEQUB = 650;
	public static final int PPC_INS_VCMPEQUD = 651;
	public static final int PPC_INS_VCMPEQUH = 652;
	public static final int PPC_INS_VCMPEQUW = 653;
	public static final int PPC_INS_VCMPGEFP = 654;
	public static final int PPC_INS_VCMPGTFP = 655;
	public static final int PPC_INS_VCMPGTSB = 656;
	public static final int PPC_INS_VCMPGTSD = 657;
	public static final int PPC_INS_VCMPGTSH = 658;
	public static final int PPC_INS_VCMPGTSW = 659;
	public static final int PPC_INS_VCMPGTUB = 660;
	public static final int PPC_INS_VCMPGTUD = 661;
	public static final int PPC_INS_VCMPGTUH = 662;
	public static final int PPC_INS_VCMPGTUW = 663;
	public static final int PPC_INS_VCTSXS = 664;
	public static final int PPC_INS_VCTUXS = 665;
	public static final int PPC_INS_VEQV = 666;
	public static final int PPC_INS_VEXPTEFP = 667;
	public static final int PPC_INS_VLOGEFP = 668;
	public static final int PPC_INS_VMADDFP = 669;
	public static final int PPC_INS_VMAXFP = 670;
	public static final int PPC_INS_VMAXSB = 671;
	public static final int PPC_INS_VMAXSD = 672;
	public static final int PPC_INS_VMAXSH = 673;
	public static final int PPC_INS_VMAXSW = 674;
	public static final int PPC_INS_VMAXUB = 675;
	public static final int PPC_INS_VMAXUD = 676;
	public static final int PPC_INS_VMAXUH = 677;
	public static final int PPC_INS_VMAXUW = 678;
	public static final int PPC_INS_VMHADDSHS = 679;
	public static final int PPC_INS_VMHRADDSHS = 680;
	public static final int PPC_INS_VMINUD = 681;
	public static final int PPC_INS_VMINFP = 682;
	public static final int PPC_INS_VMINSB = 683;
	public static final int PPC_INS_VMINSD = 684;
	public static final int PPC_INS_VMINSH = 685;
	public static final int PPC_INS_VMINSW = 686;
	public static final int PPC_INS_VMINUB = 687;
	public static final int PPC_INS_VMINUH = 688;
	public static final int PPC_INS_VMINUW = 689;
	public static final int PPC_INS_VMLADDUHM = 690;
	public static final int PPC_INS_VMRGHB = 691;
	public static final int PPC_INS_VMRGHH = 692;
	public static final int PPC_INS_VMRGHW = 693;
	public static final int PPC_INS_VMRGLB = 694;
	public static final int PPC_INS_VMRGLH = 695;
	public static final int PPC_INS_VMRGLW = 696;
	public static final int PPC_INS_VMSUMMBM = 697;
	public static final int PPC_INS_VMSUMSHM = 698;
	public static final int PPC_INS_VMSUMSHS = 699;
	public static final int PPC_INS_VMSUMUBM = 700;
	public static final int PPC_INS_VMSUMUHM = 701;
	public static final int PPC_INS_VMSUMUHS = 702;
	public static final int PPC_INS_VMULESB = 703;
	public static final int PPC_INS_VMULESH = 704;
	public static final int PPC_INS_VMULESW = 705;
	public static final int PPC_INS_VMULEUB = 706;
	public static final int PPC_INS_VMULEUH = 707;
	public static final int PPC_INS_VMULEUW = 708;
	public static final int PPC_INS_VMULOSB = 709;
	public static final int PPC_INS_VMULOSH = 710;
	public static final int PPC_INS_VMULOSW = 711;
	public static final int PPC_INS_VMULOUB = 712;
	public static final int PPC_INS_VMULOUH = 713;
	public static final int PPC_INS_VMULOUW = 714;
	public static final int PPC_INS_VMULUWM = 715;
	public static final int PPC_INS_VNAND = 716;
	public static final int PPC_INS_VNMSUBFP = 717;
	public static final int PPC_INS_VNOR = 718;
	public static final int PPC_INS_VOR = 719;
	public static final int PPC_INS_VORC = 720;
	public static final int PPC_INS_VPERM = 721;
	public static final int PPC_INS_VPKPX = 722;
	public static final int PPC_INS_VPKSHSS = 723;
	public static final int PPC_INS_VPKSHUS = 724;
	public static final int PPC_INS_VPKSWSS = 725;
	public static final int PPC_INS_VPKSWUS = 726;
	public static final int PPC_INS_VPKUHUM = 727;
	public static final int PPC_INS_VPKUHUS = 728;
	public static final int PPC_INS_VPKUWUM = 729;
	public static final int PPC_INS_VPKUWUS = 730;
	public static final int PPC_INS_VPOPCNTB = 731;
	public static final int PPC_INS_VPOPCNTD = 732;
	public static final int PPC_INS_VPOPCNTH = 733;
	public static final int PPC_INS_VPOPCNTW = 734;
	public static final int PPC_INS_VREFP = 735;
	public static final int PPC_INS_VRFIM = 736;
	public static final int PPC_INS_VRFIN = 737;
	public static final int PPC_INS_VRFIP = 738;
	public static final int PPC_INS_VRFIZ = 739;
	public static final int PPC_INS_VRLB = 740;
	public static final int PPC_INS_VRLD = 741;
	public static final int PPC_INS_VRLH = 742;
	public static final int PPC_INS_VRLW = 743;
	public static final int PPC_INS_VRSQRTEFP = 744;
	public static final int PPC_INS_VSEL = 745;
	public static final int PPC_INS_VSL = 746;
	public static final int PPC_INS_VSLB = 747;
	public static final int PPC_INS_VSLD = 748;
	public static final int PPC_INS_VSLDOI = 749;
	public static final int PPC_INS_VSLH = 750;
	public static final int PPC_INS_VSLO = 751;
	public static final int PPC_INS_VSLW = 752;
	public static final int PPC_INS_VSPLTB = 753;
	public static final int PPC_INS_VSPLTH = 754;
	public static final int PPC_INS_VSPLTISB = 755;
	public static final int PPC_INS_VSPLTISH = 756;
	public static final int PPC_INS_VSPLTISW = 757;
	public static final int PPC_INS_VSPLTW = 758;
	public static final int PPC_INS_VSR = 759;
	public static final int PPC_INS_VSRAB = 760;
	public static final int PPC_INS_VSRAD = 761;
	public static final int PPC_INS_VSRAH = 762;
	public static final int PPC_INS_VSRAW = 763;
	public static final int PPC_INS_VSRB = 764;
	public static final int PPC_INS_VSRD = 765;
	public static final int PPC_INS_VSRH = 766;
	public static final int PPC_INS_VSRO = 767;
	public static final int PPC_INS_VSRW = 768;
	public static final int PPC_INS_VSUBCUW = 769;
	public static final int PPC_INS_VSUBFP = 770;
	public static final int PPC_INS_VSUBSBS = 771;
	public static final int PPC_INS_VSUBSHS = 772;
	public static final int PPC_INS_VSUBSWS = 773;
	public static final int PPC_INS_VSUBUBM = 774;
	public static final int PPC_INS_VSUBUBS = 775;
	public static final int PPC_INS_VSUBUDM = 776;
	public static final int PPC_INS_VSUBUHM = 777;
	public static final int PPC_INS_VSUBUHS = 778;
	public static final int PPC_INS_VSUBUWM = 779;
	public static final int PPC_INS_VSUBUWS = 780;
	public static final int PPC_INS_VSUM2SWS = 781;
	public static final int PPC_INS_VSUM4SBS = 782;
	public static final int PPC_INS_VSUM4SHS = 783;
	public static final int PPC_INS_VSUM4UBS = 784;
	public static final int PPC_INS_VSUMSWS = 785;
	public static final int PPC_INS_VUPKHPX = 786;
	public static final int PPC_INS_VUPKHSB = 787;
	public static final int PPC_INS_VUPKHSH = 788;
	public static final int PPC_INS_VUPKLPX = 789;
	public static final int PPC_INS_VUPKLSB = 790;
	public static final int PPC_INS_VUPKLSH = 791;
	public static final int PPC_INS_VXOR = 792;
	public static final int PPC_INS_WAIT = 793;
	public static final int PPC_INS_WRTEE = 794;
	public static final int PPC_INS_WRTEEI = 795;
	public static final int PPC_INS_XOR = 796;
	public static final int PPC_INS_XORI = 797;
	public static final int PPC_INS_XORIS = 798;
	public static final int PPC_INS_XSABSDP = 799;
	public static final int PPC_INS_XSADDDP = 800;
	public static final int PPC_INS_XSCMPODP = 801;
	public static final int PPC_INS_XSCMPUDP = 802;
	public static final int PPC_INS_XSCPSGNDP = 803;
	public static final int PPC_INS_XSCVDPSP = 804;
	public static final int PPC_INS_XSCVDPSXDS = 805;
	public static final int PPC_INS_XSCVDPSXWS = 806;
	public static final int PPC_INS_XSCVDPUXDS = 807;
	public static final int PPC_INS_XSCVDPUXWS = 808;
	public static final int PPC_INS_XSCVSPDP = 809;
	public static final int PPC_INS_XSCVSXDDP = 810;
	public static final int PPC_INS_XSCVUXDDP = 811;
	public static final int PPC_INS_XSDIVDP = 812;
	public static final int PPC_INS_XSMADDADP = 813;
	public static final int PPC_INS_XSMADDMDP = 814;
	public static final int PPC_INS_XSMAXDP = 815;
	public static final int PPC_INS_XSMINDP = 816;
	public static final int PPC_INS_XSMSUBADP = 817;
	public static final int PPC_INS_XSMSUBMDP = 818;
	public static final int PPC_INS_XSMULDP = 819;
	public static final int PPC_INS_XSNABSDP = 820;
	public static final int PPC_INS_XSNEGDP = 821;
	public static final int PPC_INS_XSNMADDADP = 822;
	public static final int PPC_INS_XSNMADDMDP = 823;
	public static final int PPC_INS_XSNMSUBADP = 824;
	public static final int PPC_INS_XSNMSUBMDP = 825;
	public static final int PPC_INS_XSRDPI = 826;
	public static final int PPC_INS_XSRDPIC = 827;
	public static final int PPC_INS_XSRDPIM = 828;
	public static final int PPC_INS_XSRDPIP = 829;
	public static final int PPC_INS_XSRDPIZ = 830;
	public static final int PPC_INS_XSREDP = 831;
	public static final int PPC_INS_XSRSQRTEDP = 832;
	public static final int PPC_INS_XSSQRTDP = 833;
	public static final int PPC_INS_XSSUBDP = 834;
	public static final int PPC_INS_XSTDIVDP = 835;
	public static final int PPC_INS_XSTSQRTDP = 836;
	public static final int PPC_INS_XVABSDP = 837;
	public static final int PPC_INS_XVABSSP = 838;
	public static final int PPC_INS_XVADDDP = 839;
	public static final int PPC_INS_XVADDSP = 840;
	public static final int PPC_INS_XVCMPEQDP = 841;
	public static final int PPC_INS_XVCMPEQSP = 842;
	public static final int PPC_INS_XVCMPGEDP = 843;
	public static final int PPC_INS_XVCMPGESP = 844;
	public static final int PPC_INS_XVCMPGTDP = 845;
	public static final int PPC_INS_XVCMPGTSP = 846;
	public static final int PPC_INS_XVCPSGNDP = 847;
	public static final int PPC_INS_XVCPSGNSP = 848;
	public static final int PPC_INS_XVCVDPSP = 849;
	public static final int PPC_INS_XVCVDPSXDS = 850;
	public static final int PPC_INS_XVCVDPSXWS = 851;
	public static final int PPC_INS_XVCVDPUXDS = 852;
	public static final int PPC_INS_XVCVDPUXWS = 853;
	public static final int PPC_INS_XVCVSPDP = 854;
	public static final int PPC_INS_XVCVSPSXDS = 855;
	public static final int PPC_INS_XVCVSPSXWS = 856;
	public static final int PPC_INS_XVCVSPUXDS = 857;
	public static final int PPC_INS_XVCVSPUXWS = 858;
	public static final int PPC_INS_XVCVSXDDP = 859;
	public static final int PPC_INS_XVCVSXDSP = 860;
	public static final int PPC_INS_XVCVSXWDP = 861;
	public static final int PPC_INS_XVCVSXWSP = 862;
	public static final int PPC_INS_XVCVUXDDP = 863;
	public static final int PPC_INS_XVCVUXDSP = 864;
	public static final int PPC_INS_XVCVUXWDP = 865;
	public static final int PPC_INS_XVCVUXWSP = 866;
	public static final int PPC_INS_XVDIVDP = 867;
	public static final int PPC_INS_XVDIVSP = 868;
	public static final int PPC_INS_XVMADDADP = 869;
	public static final int PPC_INS_XVMADDASP = 870;
	public static final int PPC_INS_XVMADDMDP = 871;
	public static final int PPC_INS_XVMADDMSP = 872;
	public static final int PPC_INS_XVMAXDP = 873;
	public static final int PPC_INS_XVMAXSP = 874;
	public static final int PPC_INS_XVMINDP = 875;
	public static final int PPC_INS_XVMINSP = 876;
	public static final int PPC_INS_XVMSUBADP = 877;
	public static final int PPC_INS_XVMSUBASP = 878;
	public static final int PPC_INS_XVMSUBMDP = 879;
	public static final int PPC_INS_XVMSUBMSP = 880;
	public static final int PPC_INS_XVMULDP = 881;
	public static final int PPC_INS_XVMULSP = 882;
	public static final int PPC_INS_XVNABSDP = 883;
	public static final int PPC_INS_XVNABSSP = 884;
	public static final int PPC_INS_XVNEGDP = 885;
	public static final int PPC_INS_XVNEGSP = 886;
	public static final int PPC_INS_XVNMADDADP = 887;
	public static final int PPC_INS_XVNMADDASP = 888;
	public static final int PPC_INS_XVNMADDMDP = 889;
	public static final int PPC_INS_XVNMADDMSP = 890;
	public static final int PPC_INS_XVNMSUBADP = 891;
	public static final int PPC_INS_XVNMSUBASP = 892;
	public static final int PPC_INS_XVNMSUBMDP = 893;
	public static final int PPC_INS_XVNMSUBMSP = 894;
	public static final int PPC_INS_XVRDPI = 895;
	public static final int PPC_INS_XVRDPIC = 896;
	public static final int PPC_INS_XVRDPIM = 897;
	public static final int PPC_INS_XVRDPIP = 898;
	public static final int PPC_INS_XVRDPIZ = 899;
	public static final int PPC_INS_XVREDP = 900;
	public static final int PPC_INS_XVRESP = 901;
	public static final int PPC_INS_XVRSPI = 902;
	public static final int PPC_INS_XVRSPIC = 903;
	public static final int PPC_INS_XVRSPIM = 904;
	public static final int PPC_INS_XVRSPIP = 905;
	public static final int PPC_INS_XVRSPIZ = 906;
	public static final int PPC_INS_XVRSQRTEDP = 907;
	public static final int PPC_INS_XVRSQRTESP = 908;
	public static final int PPC_INS_XVSQRTDP = 909;
	public static final int PPC_INS_XVSQRTSP = 910;
	public static final int PPC_INS_XVSUBDP = 911;
	public static final int PPC_INS_XVSUBSP = 912;
	public static final int PPC_INS_XVTDIVDP = 913;
	public static final int PPC_INS_XVTDIVSP = 914;
	public static final int PPC_INS_XVTSQRTDP = 915;
	public static final int PPC_INS_XVTSQRTSP = 916;
	public static final int PPC_INS_XXLAND = 917;
	public static final int PPC_INS_XXLANDC = 918;
	public static final int PPC_INS_XXLEQV = 919;
	public static final int PPC_INS_XXLNAND = 920;
	public static final int PPC_INS_XXLNOR = 921;
	public static final int PPC_INS_XXLOR = 922;
	public static final int PPC_INS_XXLORC = 923;
	public static final int PPC_INS_XXLXOR = 924;
	public static final int PPC_INS_XXMRGHW = 925;
	public static final int PPC_INS_XXMRGLW = 926;
	public static final int PPC_INS_XXPERMDI = 927;
	public static final int PPC_INS_XXSEL = 928;
	public static final int PPC_INS_XXSLDWI = 929;
	public static final int PPC_INS_XXSPLTW = 930;
	public static final int PPC_INS_BCA = 931;
	public static final int PPC_INS_BCLA = 932;
	public static final int PPC_INS_SLWI = 933;
	public static final int PPC_INS_SRWI = 934;
	public static final int PPC_INS_SLDI = 935;
	public static final int PPC_INS_BTA = 936;
	public static final int PPC_INS_CRSET = 937;
	public static final int PPC_INS_CRNOT = 938;
	public static final int PPC_INS_CRMOVE = 939;
	public static final int PPC_INS_CRCLR = 940;
	public static final int PPC_INS_MFBR0 = 941;
	public static final int PPC_INS_MFBR1 = 942;
	public static final int PPC_INS_MFBR2 = 943;
	public static final int PPC_INS_MFBR3 = 944;
	public static final int PPC_INS_MFBR4 = 945;
	public static final int PPC_INS_MFBR5 = 946;
	public static final int PPC_INS_MFBR6 = 947;
	public static final int PPC_INS_MFBR7 = 948;
	public static final int PPC_INS_MFXER = 949;
	public static final int PPC_INS_MFRTCU = 950;
	public static final int PPC_INS_MFRTCL = 951;
	public static final int PPC_INS_MFDSCR = 952;
	public static final int PPC_INS_MFDSISR = 953;
	public static final int PPC_INS_MFDAR = 954;
	public static final int PPC_INS_MFSRR2 = 955;
	public static final int PPC_INS_MFSRR3 = 956;
	public static final int PPC_INS_MFCFAR = 957;
	public static final int PPC_INS_MFAMR = 958;
	public static final int PPC_INS_MFPID = 959;
	public static final int PPC_INS_MFTBLO = 960;
	public static final int PPC_INS_MFTBHI = 961;
	public static final int PPC_INS_MFDBATU = 962;
	public static final int PPC_INS_MFDBATL = 963;
	public static final int PPC_INS_MFIBATU = 964;
	public static final int PPC_INS_MFIBATL = 965;
	public static final int PPC_INS_MFDCCR = 966;
	public static final int PPC_INS_MFICCR = 967;
	public static final int PPC_INS_MFDEAR = 968;
	public static final int PPC_INS_MFESR = 969;
	public static final int PPC_INS_MFSPEFSCR = 970;
	public static final int PPC_INS_MFTCR = 971;
	public static final int PPC_INS_MFASR = 972;
	public static final int PPC_INS_MFPVR = 973;
	public static final int PPC_INS_MFTBU = 974;
	public static final int PPC_INS_MTCR = 975;
	public static final int PPC_INS_MTBR0 = 976;
	public static final int PPC_INS_MTBR1 = 977;
	public static final int PPC_INS_MTBR2 = 978;
	public static final int PPC_INS_MTBR3 = 979;
	public static final int PPC_INS_MTBR4 = 980;
	public static final int PPC_INS_MTBR5 = 981;
	public static final int PPC_INS_MTBR6 = 982;
	public static final int PPC_INS_MTBR7 = 983;
	public static final int PPC_INS_MTXER = 984;
	public static final int PPC_INS_MTDSCR = 985;
	public static final int PPC_INS_MTDSISR = 986;
	public static final int PPC_INS_MTDAR = 987;
	public static final int PPC_INS_MTSRR2 = 988;
	public static final int PPC_INS_MTSRR3 = 989;
	public static final int PPC_INS_MTCFAR = 990;
	public static final int PPC_INS_MTAMR = 991;
	public static final int PPC_INS_MTPID = 992;
	public static final int PPC_INS_MTTBL = 993;
	public static final int PPC_INS_MTTBU = 994;
	public static final int PPC_INS_MTTBLO = 995;
	public static final int PPC_INS_MTTBHI = 996;
	public static final int PPC_INS_MTDBATU = 997;
	public static final int PPC_INS_MTDBATL = 998;
	public static final int PPC_INS_MTIBATU = 999;
	public static final int PPC_INS_MTIBATL = 1000;
	public static final int PPC_INS_MTDCCR = 1001;
	public static final int PPC_INS_MTICCR = 1002;
	public static final int PPC_INS_MTDEAR = 1003;
	public static final int PPC_INS_MTESR = 1004;
	public static final int PPC_INS_MTSPEFSCR = 1005;
	public static final int PPC_INS_MTTCR = 1006;
	public static final int PPC_INS_NOT = 1007;
	public static final int PPC_INS_MR = 1008;
	public static final int PPC_INS_ROTLD = 1009;
	public static final int PPC_INS_ROTLDI = 1010;
	public static final int PPC_INS_CLRLDI = 1011;
	public static final int PPC_INS_ROTLWI = 1012;
	public static final int PPC_INS_CLRLWI = 1013;
	public static final int PPC_INS_ROTLW = 1014;
	public static final int PPC_INS_SUB = 1015;
	public static final int PPC_INS_SUBC = 1016;
	public static final int PPC_INS_LWSYNC = 1017;
	public static final int PPC_INS_PTESYNC = 1018;
	public static final int PPC_INS_TDLT = 1019;
	public static final int PPC_INS_TDEQ = 1020;
	public static final int PPC_INS_TDGT = 1021;
	public static final int PPC_INS_TDNE = 1022;
	public static final int PPC_INS_TDLLT = 1023;
	public static final int PPC_INS_TDLGT = 1024;
	public static final int PPC_INS_TDU = 1025;
	public static final int PPC_INS_TDLTI = 1026;
	public static final int PPC_INS_TDEQI = 1027;
	public static final int PPC_INS_TDGTI = 1028;
	public static final int PPC_INS_TDNEI = 1029;
	public static final int PPC_INS_TDLLTI = 1030;
	public static final int PPC_INS_TDLGTI = 1031;
	public static final int PPC_INS_TDUI = 1032;
	public static final int PPC_INS_TLBREHI = 1033;
	public static final int PPC_INS_TLBRELO = 1034;
	public static final int PPC_INS_TLBWEHI = 1035;
	public static final int PPC_INS_TLBWELO = 1036;
	public static final int PPC_INS_TWLT = 1037;
	public static final int PPC_INS_TWEQ = 1038;
	public static final int PPC_INS_TWGT = 1039;
	public static final int PPC_INS_TWNE = 1040;
	public static final int PPC_INS_TWLLT = 1041;
	public static final int PPC_INS_TWLGT = 1042;
	public static final int PPC_INS_TWU = 1043;
	public static final int PPC_INS_TWLTI = 1044;
	public static final int PPC_INS_TWEQI = 1045;
	public static final int PPC_INS_TWGTI = 1046;
	public static final int PPC_INS_TWNEI = 1047;
	public static final int PPC_INS_TWLLTI = 1048;
	public static final int PPC_INS_TWLGTI = 1049;
	public static final int PPC_INS_TWUI = 1050;
	public static final int PPC_INS_WAITRSV = 1051;
	public static final int PPC_INS_WAITIMPL = 1052;
	public static final int PPC_INS_XNOP = 1053;
	public static final int PPC_INS_XVMOVDP = 1054;
	public static final int PPC_INS_XVMOVSP = 1055;
	public static final int PPC_INS_XXSPLTD = 1056;
	public static final int PPC_INS_XXMRGHD = 1057;
	public static final int PPC_INS_XXMRGLD = 1058;
	public static final int PPC_INS_XXSWAPD = 1059;
	public static final int PPC_INS_BT = 1060;
	public static final int PPC_INS_BF = 1061;
	public static final int PPC_INS_BDNZT = 1062;
	public static final int PPC_INS_BDNZF = 1063;
	public static final int PPC_INS_BDZF = 1064;
	public static final int PPC_INS_BDZT = 1065;
	public static final int PPC_INS_BFA = 1066;
	public static final int PPC_INS_BDNZTA = 1067;
	public static final int PPC_INS_BDNZFA = 1068;
	public static final int PPC_INS_BDZTA = 1069;
	public static final int PPC_INS_BDZFA = 1070;
	public static final int PPC_INS_BTCTR = 1071;
	public static final int PPC_INS_BFCTR = 1072;
	public static final int PPC_INS_BTCTRL = 1073;
	public static final int PPC_INS_BFCTRL = 1074;
	public static final int PPC_INS_BTL = 1075;
	public static final int PPC_INS_BFL = 1076;
	public static final int PPC_INS_BDNZTL = 1077;
	public static final int PPC_INS_BDNZFL = 1078;
	public static final int PPC_INS_BDZTL = 1079;
	public static final int PPC_INS_BDZFL = 1080;
	public static final int PPC_INS_BTLA = 1081;
	public static final int PPC_INS_BFLA = 1082;
	public static final int PPC_INS_BDNZTLA = 1083;
	public static final int PPC_INS_BDNZFLA = 1084;
	public static final int PPC_INS_BDZTLA = 1085;
	public static final int PPC_INS_BDZFLA = 1086;
	public static final int PPC_INS_BTLR = 1087;
	public static final int PPC_INS_BFLR = 1088;
	public static final int PPC_INS_BDNZTLR = 1089;
	public static final int PPC_INS_BDZTLR = 1090;
	public static final int PPC_INS_BDZFLR = 1091;
	public static final int PPC_INS_BTLRL = 1092;
	public static final int PPC_INS_BFLRL = 1093;
	public static final int PPC_INS_BDNZTLRL = 1094;
	public static final int PPC_INS_BDNZFLRL = 1095;
	public static final int PPC_INS_BDZTLRL = 1096;
	public static final int PPC_INS_BDZFLRL = 1097;
	public static final int PPC_INS_QVFAND = 1098;
	public static final int PPC_INS_QVFCLR = 1099;
	public static final int PPC_INS_QVFANDC = 1100;
	public static final int PPC_INS_QVFCTFB = 1101;
	public static final int PPC_INS_QVFXOR = 1102;
	public static final int PPC_INS_QVFOR = 1103;
	public static final int PPC_INS_QVFNOR = 1104;
	public static final int PPC_INS_QVFEQU = 1105;
	public static final int PPC_INS_QVFNOT = 1106;
	public static final int PPC_INS_QVFORC = 1107;
	public static final int PPC_INS_QVFNAND = 1108;
	public static final int PPC_INS_QVFSET = 1109;
	public static final int PPC_INS_ENDING = 1110;

	// Group of PPC instructions

	public static final int PPC_GRP_INVALID = 0;

	// Generic groups
	public static final int PPC_GRP_JUMP = 1;

	// Architecture-specific groups
	public static final int PPC_GRP_ALTIVEC = 128;
	public static final int PPC_GRP_MODE32 = 129;
	public static final int PPC_GRP_MODE64 = 130;
	public static final int PPC_GRP_BOOKE = 131;
	public static final int PPC_GRP_NOTBOOKE = 132;
	public static final int PPC_GRP_SPE = 133;
	public static final int PPC_GRP_VSX = 134;
	public static final int PPC_GRP_E500 = 135;
	public static final int PPC_GRP_PPC4XX = 136;
	public static final int PPC_GRP_PPC6XX = 137;
	public static final int PPC_GRP_ICBT = 138;
	public static final int PPC_GRP_P8ALTIVEC = 139;
	public static final int PPC_GRP_P8VECTOR = 140;
	public static final int PPC_GRP_QPX = 141;
	public static final int PPC_GRP_ENDING = 142;
}