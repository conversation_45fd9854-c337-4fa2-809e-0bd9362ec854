(* For Capstone Engine. AUTO-GENERATED FILE, DO NOT EDIT [m680x_const.ml] *)
let _M680X_OPERAND_COUNT = 9;;

(* M680X registers and special registers *)

let _M680X_REG_INVALID = 0;;
let _M680X_REG_A = 1;;
let _M680X_REG_B = 2;;
let _M680X_REG_E = 3;;
let _M680X_REG_F = 4;;
let _M680X_REG_0 = 5;;
let _M680X_REG_D = 6;;
let _M680X_REG_W = 7;;
let _M680X_REG_CC = 8;;
let _M680X_REG_DP = 9;;
let _M680X_REG_MD = 10;;
let _M680X_REG_HX = 11;;
let _M680X_REG_H = 12;;
let _M680X_REG_X = 13;;
let _M680X_REG_Y = 14;;
let _M680X_REG_S = 15;;
let _M680X_REG_U = 16;;
let _M680X_REG_V = 17;;
let _M680X_REG_Q = 18;;
let _M680X_REG_PC = 19;;
let _M680X_REG_TMP2 = 20;;
let _M680X_REG_TMP3 = 21;;
let _M680X_REG_ENDING = 22;;

(* Operand type for instruction's operands *)

let _M680X_OP_INVALID = 0;;
let _M680X_OP_REGISTER = 1;;
let _M680X_OP_IMMEDIATE = 2;;
let _M680X_OP_INDEXED = 3;;
let _M680X_OP_EXTENDED = 4;;
let _M680X_OP_DIRECT = 5;;
let _M680X_OP_RELATIVE = 6;;
let _M680X_OP_CONSTANT = 7;;

(* Supported bit values for mem.idx.offset_bits *)

let _M680X_OFFSET_NONE = 0;;
let _M680X_OFFSET_BITS_5 = 5;;
let _M680X_OFFSET_BITS_8 = 8;;
let _M680X_OFFSET_BITS_9 = 9;;
let _M680X_OFFSET_BITS_16 = 16;;

(* Supported bit flags for mem.idx.flags *)

(* These flags can be comined *)
let _M680X_IDX_INDIRECT = 1;;
let _M680X_IDX_NO_COMMA = 2;;
let _M680X_IDX_POST_INC_DEC = 4;;

(* Group of M680X instructions *)

let _M680X_GRP_INVALID = 0;;

(* Generic groups *)
let _M680X_GRP_JUMP = 1;;
let _M680X_GRP_CALL = 2;;
let _M680X_GRP_RET = 3;;
let _M680X_GRP_INT = 4;;
let _M680X_GRP_IRET = 5;;
let _M680X_GRP_PRIV = 6;;
let _M680X_GRP_BRAREL = 7;;

(* Architecture-specific groups *)
let _M680X_GRP_ENDING = 8;;

(* M680X instruction flags: *)
let _M680X_FIRST_OP_IN_MNEM = 1;;
let _M680X_SECOND_OP_IN_MNEM = 2;;

(* M680X instruction IDs *)

let _M680X_INS_INVLD = 0;;
let _M680X_INS_ABA = 1;;
let _M680X_INS_ABX = 2;;
let _M680X_INS_ABY = 3;;
let _M680X_INS_ADC = 4;;
let _M680X_INS_ADCA = 5;;
let _M680X_INS_ADCB = 6;;
let _M680X_INS_ADCD = 7;;
let _M680X_INS_ADCR = 8;;
let _M680X_INS_ADD = 9;;
let _M680X_INS_ADDA = 10;;
let _M680X_INS_ADDB = 11;;
let _M680X_INS_ADDD = 12;;
let _M680X_INS_ADDE = 13;;
let _M680X_INS_ADDF = 14;;
let _M680X_INS_ADDR = 15;;
let _M680X_INS_ADDW = 16;;
let _M680X_INS_AIM = 17;;
let _M680X_INS_AIS = 18;;
let _M680X_INS_AIX = 19;;
let _M680X_INS_AND = 20;;
let _M680X_INS_ANDA = 21;;
let _M680X_INS_ANDB = 22;;
let _M680X_INS_ANDCC = 23;;
let _M680X_INS_ANDD = 24;;
let _M680X_INS_ANDR = 25;;
let _M680X_INS_ASL = 26;;
let _M680X_INS_ASLA = 27;;
let _M680X_INS_ASLB = 28;;
let _M680X_INS_ASLD = 29;;
let _M680X_INS_ASR = 30;;
let _M680X_INS_ASRA = 31;;
let _M680X_INS_ASRB = 32;;
let _M680X_INS_ASRD = 33;;
let _M680X_INS_ASRX = 34;;
let _M680X_INS_BAND = 35;;
let _M680X_INS_BCC = 36;;
let _M680X_INS_BCLR = 37;;
let _M680X_INS_BCS = 38;;
let _M680X_INS_BEOR = 39;;
let _M680X_INS_BEQ = 40;;
let _M680X_INS_BGE = 41;;
let _M680X_INS_BGND = 42;;
let _M680X_INS_BGT = 43;;
let _M680X_INS_BHCC = 44;;
let _M680X_INS_BHCS = 45;;
let _M680X_INS_BHI = 46;;
let _M680X_INS_BIAND = 47;;
let _M680X_INS_BIEOR = 48;;
let _M680X_INS_BIH = 49;;
let _M680X_INS_BIL = 50;;
let _M680X_INS_BIOR = 51;;
let _M680X_INS_BIT = 52;;
let _M680X_INS_BITA = 53;;
let _M680X_INS_BITB = 54;;
let _M680X_INS_BITD = 55;;
let _M680X_INS_BITMD = 56;;
let _M680X_INS_BLE = 57;;
let _M680X_INS_BLS = 58;;
let _M680X_INS_BLT = 59;;
let _M680X_INS_BMC = 60;;
let _M680X_INS_BMI = 61;;
let _M680X_INS_BMS = 62;;
let _M680X_INS_BNE = 63;;
let _M680X_INS_BOR = 64;;
let _M680X_INS_BPL = 65;;
let _M680X_INS_BRCLR = 66;;
let _M680X_INS_BRSET = 67;;
let _M680X_INS_BRA = 68;;
let _M680X_INS_BRN = 69;;
let _M680X_INS_BSET = 70;;
let _M680X_INS_BSR = 71;;
let _M680X_INS_BVC = 72;;
let _M680X_INS_BVS = 73;;
let _M680X_INS_CALL = 74;;
let _M680X_INS_CBA = 75;;
let _M680X_INS_CBEQ = 76;;
let _M680X_INS_CBEQA = 77;;
let _M680X_INS_CBEQX = 78;;
let _M680X_INS_CLC = 79;;
let _M680X_INS_CLI = 80;;
let _M680X_INS_CLR = 81;;
let _M680X_INS_CLRA = 82;;
let _M680X_INS_CLRB = 83;;
let _M680X_INS_CLRD = 84;;
let _M680X_INS_CLRE = 85;;
let _M680X_INS_CLRF = 86;;
let _M680X_INS_CLRH = 87;;
let _M680X_INS_CLRW = 88;;
let _M680X_INS_CLRX = 89;;
let _M680X_INS_CLV = 90;;
let _M680X_INS_CMP = 91;;
let _M680X_INS_CMPA = 92;;
let _M680X_INS_CMPB = 93;;
let _M680X_INS_CMPD = 94;;
let _M680X_INS_CMPE = 95;;
let _M680X_INS_CMPF = 96;;
let _M680X_INS_CMPR = 97;;
let _M680X_INS_CMPS = 98;;
let _M680X_INS_CMPU = 99;;
let _M680X_INS_CMPW = 100;;
let _M680X_INS_CMPX = 101;;
let _M680X_INS_CMPY = 102;;
let _M680X_INS_COM = 103;;
let _M680X_INS_COMA = 104;;
let _M680X_INS_COMB = 105;;
let _M680X_INS_COMD = 106;;
let _M680X_INS_COME = 107;;
let _M680X_INS_COMF = 108;;
let _M680X_INS_COMW = 109;;
let _M680X_INS_COMX = 110;;
let _M680X_INS_CPD = 111;;
let _M680X_INS_CPHX = 112;;
let _M680X_INS_CPS = 113;;
let _M680X_INS_CPX = 114;;
let _M680X_INS_CPY = 115;;
let _M680X_INS_CWAI = 116;;
let _M680X_INS_DAA = 117;;
let _M680X_INS_DBEQ = 118;;
let _M680X_INS_DBNE = 119;;
let _M680X_INS_DBNZ = 120;;
let _M680X_INS_DBNZA = 121;;
let _M680X_INS_DBNZX = 122;;
let _M680X_INS_DEC = 123;;
let _M680X_INS_DECA = 124;;
let _M680X_INS_DECB = 125;;
let _M680X_INS_DECD = 126;;
let _M680X_INS_DECE = 127;;
let _M680X_INS_DECF = 128;;
let _M680X_INS_DECW = 129;;
let _M680X_INS_DECX = 130;;
let _M680X_INS_DES = 131;;
let _M680X_INS_DEX = 132;;
let _M680X_INS_DEY = 133;;
let _M680X_INS_DIV = 134;;
let _M680X_INS_DIVD = 135;;
let _M680X_INS_DIVQ = 136;;
let _M680X_INS_EDIV = 137;;
let _M680X_INS_EDIVS = 138;;
let _M680X_INS_EIM = 139;;
let _M680X_INS_EMACS = 140;;
let _M680X_INS_EMAXD = 141;;
let _M680X_INS_EMAXM = 142;;
let _M680X_INS_EMIND = 143;;
let _M680X_INS_EMINM = 144;;
let _M680X_INS_EMUL = 145;;
let _M680X_INS_EMULS = 146;;
let _M680X_INS_EOR = 147;;
let _M680X_INS_EORA = 148;;
let _M680X_INS_EORB = 149;;
let _M680X_INS_EORD = 150;;
let _M680X_INS_EORR = 151;;
let _M680X_INS_ETBL = 152;;
let _M680X_INS_EXG = 153;;
let _M680X_INS_FDIV = 154;;
let _M680X_INS_IBEQ = 155;;
let _M680X_INS_IBNE = 156;;
let _M680X_INS_IDIV = 157;;
let _M680X_INS_IDIVS = 158;;
let _M680X_INS_ILLGL = 159;;
let _M680X_INS_INC = 160;;
let _M680X_INS_INCA = 161;;
let _M680X_INS_INCB = 162;;
let _M680X_INS_INCD = 163;;
let _M680X_INS_INCE = 164;;
let _M680X_INS_INCF = 165;;
let _M680X_INS_INCW = 166;;
let _M680X_INS_INCX = 167;;
let _M680X_INS_INS = 168;;
let _M680X_INS_INX = 169;;
let _M680X_INS_INY = 170;;
let _M680X_INS_JMP = 171;;
let _M680X_INS_JSR = 172;;
let _M680X_INS_LBCC = 173;;
let _M680X_INS_LBCS = 174;;
let _M680X_INS_LBEQ = 175;;
let _M680X_INS_LBGE = 176;;
let _M680X_INS_LBGT = 177;;
let _M680X_INS_LBHI = 178;;
let _M680X_INS_LBLE = 179;;
let _M680X_INS_LBLS = 180;;
let _M680X_INS_LBLT = 181;;
let _M680X_INS_LBMI = 182;;
let _M680X_INS_LBNE = 183;;
let _M680X_INS_LBPL = 184;;
let _M680X_INS_LBRA = 185;;
let _M680X_INS_LBRN = 186;;
let _M680X_INS_LBSR = 187;;
let _M680X_INS_LBVC = 188;;
let _M680X_INS_LBVS = 189;;
let _M680X_INS_LDA = 190;;
let _M680X_INS_LDAA = 191;;
let _M680X_INS_LDAB = 192;;
let _M680X_INS_LDB = 193;;
let _M680X_INS_LDBT = 194;;
let _M680X_INS_LDD = 195;;
let _M680X_INS_LDE = 196;;
let _M680X_INS_LDF = 197;;
let _M680X_INS_LDHX = 198;;
let _M680X_INS_LDMD = 199;;
let _M680X_INS_LDQ = 200;;
let _M680X_INS_LDS = 201;;
let _M680X_INS_LDU = 202;;
let _M680X_INS_LDW = 203;;
let _M680X_INS_LDX = 204;;
let _M680X_INS_LDY = 205;;
let _M680X_INS_LEAS = 206;;
let _M680X_INS_LEAU = 207;;
let _M680X_INS_LEAX = 208;;
let _M680X_INS_LEAY = 209;;
let _M680X_INS_LSL = 210;;
let _M680X_INS_LSLA = 211;;
let _M680X_INS_LSLB = 212;;
let _M680X_INS_LSLD = 213;;
let _M680X_INS_LSLX = 214;;
let _M680X_INS_LSR = 215;;
let _M680X_INS_LSRA = 216;;
let _M680X_INS_LSRB = 217;;
let _M680X_INS_LSRD = 218;;
let _M680X_INS_LSRW = 219;;
let _M680X_INS_LSRX = 220;;
let _M680X_INS_MAXA = 221;;
let _M680X_INS_MAXM = 222;;
let _M680X_INS_MEM = 223;;
let _M680X_INS_MINA = 224;;
let _M680X_INS_MINM = 225;;
let _M680X_INS_MOV = 226;;
let _M680X_INS_MOVB = 227;;
let _M680X_INS_MOVW = 228;;
let _M680X_INS_MUL = 229;;
let _M680X_INS_MULD = 230;;
let _M680X_INS_NEG = 231;;
let _M680X_INS_NEGA = 232;;
let _M680X_INS_NEGB = 233;;
let _M680X_INS_NEGD = 234;;
let _M680X_INS_NEGX = 235;;
let _M680X_INS_NOP = 236;;
let _M680X_INS_NSA = 237;;
let _M680X_INS_OIM = 238;;
let _M680X_INS_ORA = 239;;
let _M680X_INS_ORAA = 240;;
let _M680X_INS_ORAB = 241;;
let _M680X_INS_ORB = 242;;
let _M680X_INS_ORCC = 243;;
let _M680X_INS_ORD = 244;;
let _M680X_INS_ORR = 245;;
let _M680X_INS_PSHA = 246;;
let _M680X_INS_PSHB = 247;;
let _M680X_INS_PSHC = 248;;
let _M680X_INS_PSHD = 249;;
let _M680X_INS_PSHH = 250;;
let _M680X_INS_PSHS = 251;;
let _M680X_INS_PSHSW = 252;;
let _M680X_INS_PSHU = 253;;
let _M680X_INS_PSHUW = 254;;
let _M680X_INS_PSHX = 255;;
let _M680X_INS_PSHY = 256;;
let _M680X_INS_PULA = 257;;
let _M680X_INS_PULB = 258;;
let _M680X_INS_PULC = 259;;
let _M680X_INS_PULD = 260;;
let _M680X_INS_PULH = 261;;
let _M680X_INS_PULS = 262;;
let _M680X_INS_PULSW = 263;;
let _M680X_INS_PULU = 264;;
let _M680X_INS_PULUW = 265;;
let _M680X_INS_PULX = 266;;
let _M680X_INS_PULY = 267;;
let _M680X_INS_REV = 268;;
let _M680X_INS_REVW = 269;;
let _M680X_INS_ROL = 270;;
let _M680X_INS_ROLA = 271;;
let _M680X_INS_ROLB = 272;;
let _M680X_INS_ROLD = 273;;
let _M680X_INS_ROLW = 274;;
let _M680X_INS_ROLX = 275;;
let _M680X_INS_ROR = 276;;
let _M680X_INS_RORA = 277;;
let _M680X_INS_RORB = 278;;
let _M680X_INS_RORD = 279;;
let _M680X_INS_RORW = 280;;
let _M680X_INS_RORX = 281;;
let _M680X_INS_RSP = 282;;
let _M680X_INS_RTC = 283;;
let _M680X_INS_RTI = 284;;
let _M680X_INS_RTS = 285;;
let _M680X_INS_SBA = 286;;
let _M680X_INS_SBC = 287;;
let _M680X_INS_SBCA = 288;;
let _M680X_INS_SBCB = 289;;
let _M680X_INS_SBCD = 290;;
let _M680X_INS_SBCR = 291;;
let _M680X_INS_SEC = 292;;
let _M680X_INS_SEI = 293;;
let _M680X_INS_SEV = 294;;
let _M680X_INS_SEX = 295;;
let _M680X_INS_SEXW = 296;;
let _M680X_INS_SLP = 297;;
let _M680X_INS_STA = 298;;
let _M680X_INS_STAA = 299;;
let _M680X_INS_STAB = 300;;
let _M680X_INS_STB = 301;;
let _M680X_INS_STBT = 302;;
let _M680X_INS_STD = 303;;
let _M680X_INS_STE = 304;;
let _M680X_INS_STF = 305;;
let _M680X_INS_STOP = 306;;
let _M680X_INS_STHX = 307;;
let _M680X_INS_STQ = 308;;
let _M680X_INS_STS = 309;;
let _M680X_INS_STU = 310;;
let _M680X_INS_STW = 311;;
let _M680X_INS_STX = 312;;
let _M680X_INS_STY = 313;;
let _M680X_INS_SUB = 314;;
let _M680X_INS_SUBA = 315;;
let _M680X_INS_SUBB = 316;;
let _M680X_INS_SUBD = 317;;
let _M680X_INS_SUBE = 318;;
let _M680X_INS_SUBF = 319;;
let _M680X_INS_SUBR = 320;;
let _M680X_INS_SUBW = 321;;
let _M680X_INS_SWI = 322;;
let _M680X_INS_SWI2 = 323;;
let _M680X_INS_SWI3 = 324;;
let _M680X_INS_SYNC = 325;;
let _M680X_INS_TAB = 326;;
let _M680X_INS_TAP = 327;;
let _M680X_INS_TAX = 328;;
let _M680X_INS_TBA = 329;;
let _M680X_INS_TBEQ = 330;;
let _M680X_INS_TBL = 331;;
let _M680X_INS_TBNE = 332;;
let _M680X_INS_TEST = 333;;
let _M680X_INS_TFM = 334;;
let _M680X_INS_TFR = 335;;
let _M680X_INS_TIM = 336;;
let _M680X_INS_TPA = 337;;
let _M680X_INS_TST = 338;;
let _M680X_INS_TSTA = 339;;
let _M680X_INS_TSTB = 340;;
let _M680X_INS_TSTD = 341;;
let _M680X_INS_TSTE = 342;;
let _M680X_INS_TSTF = 343;;
let _M680X_INS_TSTW = 344;;
let _M680X_INS_TSTX = 345;;
let _M680X_INS_TSX = 346;;
let _M680X_INS_TSY = 347;;
let _M680X_INS_TXA = 348;;
let _M680X_INS_TXS = 349;;
let _M680X_INS_TYS = 350;;
let _M680X_INS_WAI = 351;;
let _M680X_INS_WAIT = 352;;
let _M680X_INS_WAV = 353;;
let _M680X_INS_WAVR = 354;;
let _M680X_INS_XGDX = 355;;
let _M680X_INS_XGDY = 356;;
let _M680X_INS_ENDING = 357;;
