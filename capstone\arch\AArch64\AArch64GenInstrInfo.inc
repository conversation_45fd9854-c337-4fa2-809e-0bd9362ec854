/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Target Instruction Enum Values                                              *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine, http://www.capstone-engine.org */
/* By <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2015 */


#ifdef GET_INSTRINFO_ENUM
#undef GET_INSTRINFO_ENUM

enum {
    AArch64_PHI	= 0,
    AArch64_INLINEASM	= 1,
    AArch64_CFI_INSTRUCTION	= 2,
    AArch64_EH_LABEL	= 3,
    AArch64_GC_LABEL	= 4,
    AArch64_KILL	= 5,
    AArch64_EXTRACT_SUBREG	= 6,
    AArch64_INSERT_SUBREG	= 7,
    AArch64_IMPLICIT_DEF	= 8,
    AArch64_SUBREG_TO_REG	= 9,
    AArch64_COPY_TO_REGCLASS	= 10,
    AArch64_DBG_VALUE	= 11,
    AArch64_REG_SEQUENCE	= 12,
    AArch64_COPY	= 13,
    AArch64_BUNDLE	= 14,
    AArch64_LIFETIME_START	= 15,
    AArch64_LIFETIME_END	= 16,
    AArch64_STACKMAP	= 17,
    AArch64_PATCHPOINT	= 18,
    AArch64_LOAD_STACK_GUARD	= 19,
    AArch64_STATEPOINT	= 20,
    AArch64_FRAME_ALLOC	= 21,
    AArch64_ABSv16i8	= 22,
    AArch64_ABSv1i64	= 23,
    AArch64_ABSv2i32	= 24,
    AArch64_ABSv2i64	= 25,
    AArch64_ABSv4i16	= 26,
    AArch64_ABSv4i32	= 27,
    AArch64_ABSv8i16	= 28,
    AArch64_ABSv8i8	= 29,
    AArch64_ADCSWr	= 30,
    AArch64_ADCSXr	= 31,
    AArch64_ADCWr	= 32,
    AArch64_ADCXr	= 33,
    AArch64_ADDHNv2i64_v2i32	= 34,
    AArch64_ADDHNv2i64_v4i32	= 35,
    AArch64_ADDHNv4i32_v4i16	= 36,
    AArch64_ADDHNv4i32_v8i16	= 37,
    AArch64_ADDHNv8i16_v16i8	= 38,
    AArch64_ADDHNv8i16_v8i8	= 39,
    AArch64_ADDPv16i8	= 40,
    AArch64_ADDPv2i32	= 41,
    AArch64_ADDPv2i64	= 42,
    AArch64_ADDPv2i64p	= 43,
    AArch64_ADDPv4i16	= 44,
    AArch64_ADDPv4i32	= 45,
    AArch64_ADDPv8i16	= 46,
    AArch64_ADDPv8i8	= 47,
    AArch64_ADDSWri	= 48,
    AArch64_ADDSWrr	= 49,
    AArch64_ADDSWrs	= 50,
    AArch64_ADDSWrx	= 51,
    AArch64_ADDSXri	= 52,
    AArch64_ADDSXrr	= 53,
    AArch64_ADDSXrs	= 54,
    AArch64_ADDSXrx	= 55,
    AArch64_ADDSXrx64	= 56,
    AArch64_ADDVv16i8v	= 57,
    AArch64_ADDVv4i16v	= 58,
    AArch64_ADDVv4i32v	= 59,
    AArch64_ADDVv8i16v	= 60,
    AArch64_ADDVv8i8v	= 61,
    AArch64_ADDWri	= 62,
    AArch64_ADDWrr	= 63,
    AArch64_ADDWrs	= 64,
    AArch64_ADDWrx	= 65,
    AArch64_ADDXri	= 66,
    AArch64_ADDXrr	= 67,
    AArch64_ADDXrs	= 68,
    AArch64_ADDXrx	= 69,
    AArch64_ADDXrx64	= 70,
    AArch64_ADDv16i8	= 71,
    AArch64_ADDv1i64	= 72,
    AArch64_ADDv2i32	= 73,
    AArch64_ADDv2i64	= 74,
    AArch64_ADDv4i16	= 75,
    AArch64_ADDv4i32	= 76,
    AArch64_ADDv8i16	= 77,
    AArch64_ADDv8i8	= 78,
    AArch64_ADJCALLSTACKDOWN	= 79,
    AArch64_ADJCALLSTACKUP	= 80,
    AArch64_ADR	= 81,
    AArch64_ADRP	= 82,
    AArch64_AESDrr	= 83,
    AArch64_AESErr	= 84,
    AArch64_AESIMCrr	= 85,
    AArch64_AESMCrr	= 86,
    AArch64_ANDSWri	= 87,
    AArch64_ANDSWrr	= 88,
    AArch64_ANDSWrs	= 89,
    AArch64_ANDSXri	= 90,
    AArch64_ANDSXrr	= 91,
    AArch64_ANDSXrs	= 92,
    AArch64_ANDWri	= 93,
    AArch64_ANDWrr	= 94,
    AArch64_ANDWrs	= 95,
    AArch64_ANDXri	= 96,
    AArch64_ANDXrr	= 97,
    AArch64_ANDXrs	= 98,
    AArch64_ANDv16i8	= 99,
    AArch64_ANDv8i8	= 100,
    AArch64_ASRVWr	= 101,
    AArch64_ASRVXr	= 102,
    AArch64_B	= 103,
    AArch64_BFMWri	= 104,
    AArch64_BFMXri	= 105,
    AArch64_BICSWrr	= 106,
    AArch64_BICSWrs	= 107,
    AArch64_BICSXrr	= 108,
    AArch64_BICSXrs	= 109,
    AArch64_BICWrr	= 110,
    AArch64_BICWrs	= 111,
    AArch64_BICXrr	= 112,
    AArch64_BICXrs	= 113,
    AArch64_BICv16i8	= 114,
    AArch64_BICv2i32	= 115,
    AArch64_BICv4i16	= 116,
    AArch64_BICv4i32	= 117,
    AArch64_BICv8i16	= 118,
    AArch64_BICv8i8	= 119,
    AArch64_BIFv16i8	= 120,
    AArch64_BIFv8i8	= 121,
    AArch64_BITv16i8	= 122,
    AArch64_BITv8i8	= 123,
    AArch64_BL	= 124,
    AArch64_BLR	= 125,
    AArch64_BR	= 126,
    AArch64_BRK	= 127,
    AArch64_BSLv16i8	= 128,
    AArch64_BSLv8i8	= 129,
    AArch64_Bcc	= 130,
    AArch64_CBNZW	= 131,
    AArch64_CBNZX	= 132,
    AArch64_CBZW	= 133,
    AArch64_CBZX	= 134,
    AArch64_CCMNWi	= 135,
    AArch64_CCMNWr	= 136,
    AArch64_CCMNXi	= 137,
    AArch64_CCMNXr	= 138,
    AArch64_CCMPWi	= 139,
    AArch64_CCMPWr	= 140,
    AArch64_CCMPXi	= 141,
    AArch64_CCMPXr	= 142,
    AArch64_CLREX	= 143,
    AArch64_CLSWr	= 144,
    AArch64_CLSXr	= 145,
    AArch64_CLSv16i8	= 146,
    AArch64_CLSv2i32	= 147,
    AArch64_CLSv4i16	= 148,
    AArch64_CLSv4i32	= 149,
    AArch64_CLSv8i16	= 150,
    AArch64_CLSv8i8	= 151,
    AArch64_CLZWr	= 152,
    AArch64_CLZXr	= 153,
    AArch64_CLZv16i8	= 154,
    AArch64_CLZv2i32	= 155,
    AArch64_CLZv4i16	= 156,
    AArch64_CLZv4i32	= 157,
    AArch64_CLZv8i16	= 158,
    AArch64_CLZv8i8	= 159,
    AArch64_CMEQv16i8	= 160,
    AArch64_CMEQv16i8rz	= 161,
    AArch64_CMEQv1i64	= 162,
    AArch64_CMEQv1i64rz	= 163,
    AArch64_CMEQv2i32	= 164,
    AArch64_CMEQv2i32rz	= 165,
    AArch64_CMEQv2i64	= 166,
    AArch64_CMEQv2i64rz	= 167,
    AArch64_CMEQv4i16	= 168,
    AArch64_CMEQv4i16rz	= 169,
    AArch64_CMEQv4i32	= 170,
    AArch64_CMEQv4i32rz	= 171,
    AArch64_CMEQv8i16	= 172,
    AArch64_CMEQv8i16rz	= 173,
    AArch64_CMEQv8i8	= 174,
    AArch64_CMEQv8i8rz	= 175,
    AArch64_CMGEv16i8	= 176,
    AArch64_CMGEv16i8rz	= 177,
    AArch64_CMGEv1i64	= 178,
    AArch64_CMGEv1i64rz	= 179,
    AArch64_CMGEv2i32	= 180,
    AArch64_CMGEv2i32rz	= 181,
    AArch64_CMGEv2i64	= 182,
    AArch64_CMGEv2i64rz	= 183,
    AArch64_CMGEv4i16	= 184,
    AArch64_CMGEv4i16rz	= 185,
    AArch64_CMGEv4i32	= 186,
    AArch64_CMGEv4i32rz	= 187,
    AArch64_CMGEv8i16	= 188,
    AArch64_CMGEv8i16rz	= 189,
    AArch64_CMGEv8i8	= 190,
    AArch64_CMGEv8i8rz	= 191,
    AArch64_CMGTv16i8	= 192,
    AArch64_CMGTv16i8rz	= 193,
    AArch64_CMGTv1i64	= 194,
    AArch64_CMGTv1i64rz	= 195,
    AArch64_CMGTv2i32	= 196,
    AArch64_CMGTv2i32rz	= 197,
    AArch64_CMGTv2i64	= 198,
    AArch64_CMGTv2i64rz	= 199,
    AArch64_CMGTv4i16	= 200,
    AArch64_CMGTv4i16rz	= 201,
    AArch64_CMGTv4i32	= 202,
    AArch64_CMGTv4i32rz	= 203,
    AArch64_CMGTv8i16	= 204,
    AArch64_CMGTv8i16rz	= 205,
    AArch64_CMGTv8i8	= 206,
    AArch64_CMGTv8i8rz	= 207,
    AArch64_CMHIv16i8	= 208,
    AArch64_CMHIv1i64	= 209,
    AArch64_CMHIv2i32	= 210,
    AArch64_CMHIv2i64	= 211,
    AArch64_CMHIv4i16	= 212,
    AArch64_CMHIv4i32	= 213,
    AArch64_CMHIv8i16	= 214,
    AArch64_CMHIv8i8	= 215,
    AArch64_CMHSv16i8	= 216,
    AArch64_CMHSv1i64	= 217,
    AArch64_CMHSv2i32	= 218,
    AArch64_CMHSv2i64	= 219,
    AArch64_CMHSv4i16	= 220,
    AArch64_CMHSv4i32	= 221,
    AArch64_CMHSv8i16	= 222,
    AArch64_CMHSv8i8	= 223,
    AArch64_CMLEv16i8rz	= 224,
    AArch64_CMLEv1i64rz	= 225,
    AArch64_CMLEv2i32rz	= 226,
    AArch64_CMLEv2i64rz	= 227,
    AArch64_CMLEv4i16rz	= 228,
    AArch64_CMLEv4i32rz	= 229,
    AArch64_CMLEv8i16rz	= 230,
    AArch64_CMLEv8i8rz	= 231,
    AArch64_CMLTv16i8rz	= 232,
    AArch64_CMLTv1i64rz	= 233,
    AArch64_CMLTv2i32rz	= 234,
    AArch64_CMLTv2i64rz	= 235,
    AArch64_CMLTv4i16rz	= 236,
    AArch64_CMLTv4i32rz	= 237,
    AArch64_CMLTv8i16rz	= 238,
    AArch64_CMLTv8i8rz	= 239,
    AArch64_CMTSTv16i8	= 240,
    AArch64_CMTSTv1i64	= 241,
    AArch64_CMTSTv2i32	= 242,
    AArch64_CMTSTv2i64	= 243,
    AArch64_CMTSTv4i16	= 244,
    AArch64_CMTSTv4i32	= 245,
    AArch64_CMTSTv8i16	= 246,
    AArch64_CMTSTv8i8	= 247,
    AArch64_CNTv16i8	= 248,
    AArch64_CNTv8i8	= 249,
    AArch64_CPYi16	= 250,
    AArch64_CPYi32	= 251,
    AArch64_CPYi64	= 252,
    AArch64_CPYi8	= 253,
    AArch64_CRC32Brr	= 254,
    AArch64_CRC32CBrr	= 255,
    AArch64_CRC32CHrr	= 256,
    AArch64_CRC32CWrr	= 257,
    AArch64_CRC32CXrr	= 258,
    AArch64_CRC32Hrr	= 259,
    AArch64_CRC32Wrr	= 260,
    AArch64_CRC32Xrr	= 261,
    AArch64_CSELWr	= 262,
    AArch64_CSELXr	= 263,
    AArch64_CSINCWr	= 264,
    AArch64_CSINCXr	= 265,
    AArch64_CSINVWr	= 266,
    AArch64_CSINVXr	= 267,
    AArch64_CSNEGWr	= 268,
    AArch64_CSNEGXr	= 269,
    AArch64_DCPS1	= 270,
    AArch64_DCPS2	= 271,
    AArch64_DCPS3	= 272,
    AArch64_DMB	= 273,
    AArch64_DRPS	= 274,
    AArch64_DSB	= 275,
    AArch64_DUPv16i8gpr	= 276,
    AArch64_DUPv16i8lane	= 277,
    AArch64_DUPv2i32gpr	= 278,
    AArch64_DUPv2i32lane	= 279,
    AArch64_DUPv2i64gpr	= 280,
    AArch64_DUPv2i64lane	= 281,
    AArch64_DUPv4i16gpr	= 282,
    AArch64_DUPv4i16lane	= 283,
    AArch64_DUPv4i32gpr	= 284,
    AArch64_DUPv4i32lane	= 285,
    AArch64_DUPv8i16gpr	= 286,
    AArch64_DUPv8i16lane	= 287,
    AArch64_DUPv8i8gpr	= 288,
    AArch64_DUPv8i8lane	= 289,
    AArch64_EONWrr	= 290,
    AArch64_EONWrs	= 291,
    AArch64_EONXrr	= 292,
    AArch64_EONXrs	= 293,
    AArch64_EORWri	= 294,
    AArch64_EORWrr	= 295,
    AArch64_EORWrs	= 296,
    AArch64_EORXri	= 297,
    AArch64_EORXrr	= 298,
    AArch64_EORXrs	= 299,
    AArch64_EORv16i8	= 300,
    AArch64_EORv8i8	= 301,
    AArch64_ERET	= 302,
    AArch64_EXTRWrri	= 303,
    AArch64_EXTRXrri	= 304,
    AArch64_EXTv16i8	= 305,
    AArch64_EXTv8i8	= 306,
    AArch64_F128CSEL	= 307,
    AArch64_FABD32	= 308,
    AArch64_FABD64	= 309,
    AArch64_FABDv2f32	= 310,
    AArch64_FABDv2f64	= 311,
    AArch64_FABDv4f32	= 312,
    AArch64_FABSDr	= 313,
    AArch64_FABSSr	= 314,
    AArch64_FABSv2f32	= 315,
    AArch64_FABSv2f64	= 316,
    AArch64_FABSv4f32	= 317,
    AArch64_FACGE32	= 318,
    AArch64_FACGE64	= 319,
    AArch64_FACGEv2f32	= 320,
    AArch64_FACGEv2f64	= 321,
    AArch64_FACGEv4f32	= 322,
    AArch64_FACGT32	= 323,
    AArch64_FACGT64	= 324,
    AArch64_FACGTv2f32	= 325,
    AArch64_FACGTv2f64	= 326,
    AArch64_FACGTv4f32	= 327,
    AArch64_FADDDrr	= 328,
    AArch64_FADDPv2f32	= 329,
    AArch64_FADDPv2f64	= 330,
    AArch64_FADDPv2i32p	= 331,
    AArch64_FADDPv2i64p	= 332,
    AArch64_FADDPv4f32	= 333,
    AArch64_FADDSrr	= 334,
    AArch64_FADDv2f32	= 335,
    AArch64_FADDv2f64	= 336,
    AArch64_FADDv4f32	= 337,
    AArch64_FCCMPDrr	= 338,
    AArch64_FCCMPEDrr	= 339,
    AArch64_FCCMPESrr	= 340,
    AArch64_FCCMPSrr	= 341,
    AArch64_FCMEQ32	= 342,
    AArch64_FCMEQ64	= 343,
    AArch64_FCMEQv1i32rz	= 344,
    AArch64_FCMEQv1i64rz	= 345,
    AArch64_FCMEQv2f32	= 346,
    AArch64_FCMEQv2f64	= 347,
    AArch64_FCMEQv2i32rz	= 348,
    AArch64_FCMEQv2i64rz	= 349,
    AArch64_FCMEQv4f32	= 350,
    AArch64_FCMEQv4i32rz	= 351,
    AArch64_FCMGE32	= 352,
    AArch64_FCMGE64	= 353,
    AArch64_FCMGEv1i32rz	= 354,
    AArch64_FCMGEv1i64rz	= 355,
    AArch64_FCMGEv2f32	= 356,
    AArch64_FCMGEv2f64	= 357,
    AArch64_FCMGEv2i32rz	= 358,
    AArch64_FCMGEv2i64rz	= 359,
    AArch64_FCMGEv4f32	= 360,
    AArch64_FCMGEv4i32rz	= 361,
    AArch64_FCMGT32	= 362,
    AArch64_FCMGT64	= 363,
    AArch64_FCMGTv1i32rz	= 364,
    AArch64_FCMGTv1i64rz	= 365,
    AArch64_FCMGTv2f32	= 366,
    AArch64_FCMGTv2f64	= 367,
    AArch64_FCMGTv2i32rz	= 368,
    AArch64_FCMGTv2i64rz	= 369,
    AArch64_FCMGTv4f32	= 370,
    AArch64_FCMGTv4i32rz	= 371,
    AArch64_FCMLEv1i32rz	= 372,
    AArch64_FCMLEv1i64rz	= 373,
    AArch64_FCMLEv2i32rz	= 374,
    AArch64_FCMLEv2i64rz	= 375,
    AArch64_FCMLEv4i32rz	= 376,
    AArch64_FCMLTv1i32rz	= 377,
    AArch64_FCMLTv1i64rz	= 378,
    AArch64_FCMLTv2i32rz	= 379,
    AArch64_FCMLTv2i64rz	= 380,
    AArch64_FCMLTv4i32rz	= 381,
    AArch64_FCMPDri	= 382,
    AArch64_FCMPDrr	= 383,
    AArch64_FCMPEDri	= 384,
    AArch64_FCMPEDrr	= 385,
    AArch64_FCMPESri	= 386,
    AArch64_FCMPESrr	= 387,
    AArch64_FCMPSri	= 388,
    AArch64_FCMPSrr	= 389,
    AArch64_FCSELDrrr	= 390,
    AArch64_FCSELSrrr	= 391,
    AArch64_FCVTASUWDr	= 392,
    AArch64_FCVTASUWSr	= 393,
    AArch64_FCVTASUXDr	= 394,
    AArch64_FCVTASUXSr	= 395,
    AArch64_FCVTASv1i32	= 396,
    AArch64_FCVTASv1i64	= 397,
    AArch64_FCVTASv2f32	= 398,
    AArch64_FCVTASv2f64	= 399,
    AArch64_FCVTASv4f32	= 400,
    AArch64_FCVTAUUWDr	= 401,
    AArch64_FCVTAUUWSr	= 402,
    AArch64_FCVTAUUXDr	= 403,
    AArch64_FCVTAUUXSr	= 404,
    AArch64_FCVTAUv1i32	= 405,
    AArch64_FCVTAUv1i64	= 406,
    AArch64_FCVTAUv2f32	= 407,
    AArch64_FCVTAUv2f64	= 408,
    AArch64_FCVTAUv4f32	= 409,
    AArch64_FCVTDHr	= 410,
    AArch64_FCVTDSr	= 411,
    AArch64_FCVTHDr	= 412,
    AArch64_FCVTHSr	= 413,
    AArch64_FCVTLv2i32	= 414,
    AArch64_FCVTLv4i16	= 415,
    AArch64_FCVTLv4i32	= 416,
    AArch64_FCVTLv8i16	= 417,
    AArch64_FCVTMSUWDr	= 418,
    AArch64_FCVTMSUWSr	= 419,
    AArch64_FCVTMSUXDr	= 420,
    AArch64_FCVTMSUXSr	= 421,
    AArch64_FCVTMSv1i32	= 422,
    AArch64_FCVTMSv1i64	= 423,
    AArch64_FCVTMSv2f32	= 424,
    AArch64_FCVTMSv2f64	= 425,
    AArch64_FCVTMSv4f32	= 426,
    AArch64_FCVTMUUWDr	= 427,
    AArch64_FCVTMUUWSr	= 428,
    AArch64_FCVTMUUXDr	= 429,
    AArch64_FCVTMUUXSr	= 430,
    AArch64_FCVTMUv1i32	= 431,
    AArch64_FCVTMUv1i64	= 432,
    AArch64_FCVTMUv2f32	= 433,
    AArch64_FCVTMUv2f64	= 434,
    AArch64_FCVTMUv4f32	= 435,
    AArch64_FCVTNSUWDr	= 436,
    AArch64_FCVTNSUWSr	= 437,
    AArch64_FCVTNSUXDr	= 438,
    AArch64_FCVTNSUXSr	= 439,
    AArch64_FCVTNSv1i32	= 440,
    AArch64_FCVTNSv1i64	= 441,
    AArch64_FCVTNSv2f32	= 442,
    AArch64_FCVTNSv2f64	= 443,
    AArch64_FCVTNSv4f32	= 444,
    AArch64_FCVTNUUWDr	= 445,
    AArch64_FCVTNUUWSr	= 446,
    AArch64_FCVTNUUXDr	= 447,
    AArch64_FCVTNUUXSr	= 448,
    AArch64_FCVTNUv1i32	= 449,
    AArch64_FCVTNUv1i64	= 450,
    AArch64_FCVTNUv2f32	= 451,
    AArch64_FCVTNUv2f64	= 452,
    AArch64_FCVTNUv4f32	= 453,
    AArch64_FCVTNv2i32	= 454,
    AArch64_FCVTNv4i16	= 455,
    AArch64_FCVTNv4i32	= 456,
    AArch64_FCVTNv8i16	= 457,
    AArch64_FCVTPSUWDr	= 458,
    AArch64_FCVTPSUWSr	= 459,
    AArch64_FCVTPSUXDr	= 460,
    AArch64_FCVTPSUXSr	= 461,
    AArch64_FCVTPSv1i32	= 462,
    AArch64_FCVTPSv1i64	= 463,
    AArch64_FCVTPSv2f32	= 464,
    AArch64_FCVTPSv2f64	= 465,
    AArch64_FCVTPSv4f32	= 466,
    AArch64_FCVTPUUWDr	= 467,
    AArch64_FCVTPUUWSr	= 468,
    AArch64_FCVTPUUXDr	= 469,
    AArch64_FCVTPUUXSr	= 470,
    AArch64_FCVTPUv1i32	= 471,
    AArch64_FCVTPUv1i64	= 472,
    AArch64_FCVTPUv2f32	= 473,
    AArch64_FCVTPUv2f64	= 474,
    AArch64_FCVTPUv4f32	= 475,
    AArch64_FCVTSDr	= 476,
    AArch64_FCVTSHr	= 477,
    AArch64_FCVTXNv1i64	= 478,
    AArch64_FCVTXNv2f32	= 479,
    AArch64_FCVTXNv4f32	= 480,
    AArch64_FCVTZSSWDri	= 481,
    AArch64_FCVTZSSWSri	= 482,
    AArch64_FCVTZSSXDri	= 483,
    AArch64_FCVTZSSXSri	= 484,
    AArch64_FCVTZSUWDr	= 485,
    AArch64_FCVTZSUWSr	= 486,
    AArch64_FCVTZSUXDr	= 487,
    AArch64_FCVTZSUXSr	= 488,
    AArch64_FCVTZS_IntSWDri	= 489,
    AArch64_FCVTZS_IntSWSri	= 490,
    AArch64_FCVTZS_IntSXDri	= 491,
    AArch64_FCVTZS_IntSXSri	= 492,
    AArch64_FCVTZS_IntUWDr	= 493,
    AArch64_FCVTZS_IntUWSr	= 494,
    AArch64_FCVTZS_IntUXDr	= 495,
    AArch64_FCVTZS_IntUXSr	= 496,
    AArch64_FCVTZS_Intv2f32	= 497,
    AArch64_FCVTZS_Intv2f64	= 498,
    AArch64_FCVTZS_Intv4f32	= 499,
    AArch64_FCVTZSd	= 500,
    AArch64_FCVTZSs	= 501,
    AArch64_FCVTZSv1i32	= 502,
    AArch64_FCVTZSv1i64	= 503,
    AArch64_FCVTZSv2f32	= 504,
    AArch64_FCVTZSv2f64	= 505,
    AArch64_FCVTZSv2i32_shift	= 506,
    AArch64_FCVTZSv2i64_shift	= 507,
    AArch64_FCVTZSv4f32	= 508,
    AArch64_FCVTZSv4i32_shift	= 509,
    AArch64_FCVTZUSWDri	= 510,
    AArch64_FCVTZUSWSri	= 511,
    AArch64_FCVTZUSXDri	= 512,
    AArch64_FCVTZUSXSri	= 513,
    AArch64_FCVTZUUWDr	= 514,
    AArch64_FCVTZUUWSr	= 515,
    AArch64_FCVTZUUXDr	= 516,
    AArch64_FCVTZUUXSr	= 517,
    AArch64_FCVTZU_IntSWDri	= 518,
    AArch64_FCVTZU_IntSWSri	= 519,
    AArch64_FCVTZU_IntSXDri	= 520,
    AArch64_FCVTZU_IntSXSri	= 521,
    AArch64_FCVTZU_IntUWDr	= 522,
    AArch64_FCVTZU_IntUWSr	= 523,
    AArch64_FCVTZU_IntUXDr	= 524,
    AArch64_FCVTZU_IntUXSr	= 525,
    AArch64_FCVTZU_Intv2f32	= 526,
    AArch64_FCVTZU_Intv2f64	= 527,
    AArch64_FCVTZU_Intv4f32	= 528,
    AArch64_FCVTZUd	= 529,
    AArch64_FCVTZUs	= 530,
    AArch64_FCVTZUv1i32	= 531,
    AArch64_FCVTZUv1i64	= 532,
    AArch64_FCVTZUv2f32	= 533,
    AArch64_FCVTZUv2f64	= 534,
    AArch64_FCVTZUv2i32_shift	= 535,
    AArch64_FCVTZUv2i64_shift	= 536,
    AArch64_FCVTZUv4f32	= 537,
    AArch64_FCVTZUv4i32_shift	= 538,
    AArch64_FDIVDrr	= 539,
    AArch64_FDIVSrr	= 540,
    AArch64_FDIVv2f32	= 541,
    AArch64_FDIVv2f64	= 542,
    AArch64_FDIVv4f32	= 543,
    AArch64_FMADDDrrr	= 544,
    AArch64_FMADDSrrr	= 545,
    AArch64_FMAXDrr	= 546,
    AArch64_FMAXNMDrr	= 547,
    AArch64_FMAXNMPv2f32	= 548,
    AArch64_FMAXNMPv2f64	= 549,
    AArch64_FMAXNMPv2i32p	= 550,
    AArch64_FMAXNMPv2i64p	= 551,
    AArch64_FMAXNMPv4f32	= 552,
    AArch64_FMAXNMSrr	= 553,
    AArch64_FMAXNMVv4i32v	= 554,
    AArch64_FMAXNMv2f32	= 555,
    AArch64_FMAXNMv2f64	= 556,
    AArch64_FMAXNMv4f32	= 557,
    AArch64_FMAXPv2f32	= 558,
    AArch64_FMAXPv2f64	= 559,
    AArch64_FMAXPv2i32p	= 560,
    AArch64_FMAXPv2i64p	= 561,
    AArch64_FMAXPv4f32	= 562,
    AArch64_FMAXSrr	= 563,
    AArch64_FMAXVv4i32v	= 564,
    AArch64_FMAXv2f32	= 565,
    AArch64_FMAXv2f64	= 566,
    AArch64_FMAXv4f32	= 567,
    AArch64_FMINDrr	= 568,
    AArch64_FMINNMDrr	= 569,
    AArch64_FMINNMPv2f32	= 570,
    AArch64_FMINNMPv2f64	= 571,
    AArch64_FMINNMPv2i32p	= 572,
    AArch64_FMINNMPv2i64p	= 573,
    AArch64_FMINNMPv4f32	= 574,
    AArch64_FMINNMSrr	= 575,
    AArch64_FMINNMVv4i32v	= 576,
    AArch64_FMINNMv2f32	= 577,
    AArch64_FMINNMv2f64	= 578,
    AArch64_FMINNMv4f32	= 579,
    AArch64_FMINPv2f32	= 580,
    AArch64_FMINPv2f64	= 581,
    AArch64_FMINPv2i32p	= 582,
    AArch64_FMINPv2i64p	= 583,
    AArch64_FMINPv4f32	= 584,
    AArch64_FMINSrr	= 585,
    AArch64_FMINVv4i32v	= 586,
    AArch64_FMINv2f32	= 587,
    AArch64_FMINv2f64	= 588,
    AArch64_FMINv4f32	= 589,
    AArch64_FMLAv1i32_indexed	= 590,
    AArch64_FMLAv1i64_indexed	= 591,
    AArch64_FMLAv2f32	= 592,
    AArch64_FMLAv2f64	= 593,
    AArch64_FMLAv2i32_indexed	= 594,
    AArch64_FMLAv2i64_indexed	= 595,
    AArch64_FMLAv4f32	= 596,
    AArch64_FMLAv4i32_indexed	= 597,
    AArch64_FMLSv1i32_indexed	= 598,
    AArch64_FMLSv1i64_indexed	= 599,
    AArch64_FMLSv2f32	= 600,
    AArch64_FMLSv2f64	= 601,
    AArch64_FMLSv2i32_indexed	= 602,
    AArch64_FMLSv2i64_indexed	= 603,
    AArch64_FMLSv4f32	= 604,
    AArch64_FMLSv4i32_indexed	= 605,
    AArch64_FMOVDXHighr	= 606,
    AArch64_FMOVDXr	= 607,
    AArch64_FMOVDi	= 608,
    AArch64_FMOVDr	= 609,
    AArch64_FMOVSWr	= 610,
    AArch64_FMOVSi	= 611,
    AArch64_FMOVSr	= 612,
    AArch64_FMOVWSr	= 613,
    AArch64_FMOVXDHighr	= 614,
    AArch64_FMOVXDr	= 615,
    AArch64_FMOVv2f32_ns	= 616,
    AArch64_FMOVv2f64_ns	= 617,
    AArch64_FMOVv4f32_ns	= 618,
    AArch64_FMSUBDrrr	= 619,
    AArch64_FMSUBSrrr	= 620,
    AArch64_FMULDrr	= 621,
    AArch64_FMULSrr	= 622,
    AArch64_FMULX32	= 623,
    AArch64_FMULX64	= 624,
    AArch64_FMULXv1i32_indexed	= 625,
    AArch64_FMULXv1i64_indexed	= 626,
    AArch64_FMULXv2f32	= 627,
    AArch64_FMULXv2f64	= 628,
    AArch64_FMULXv2i32_indexed	= 629,
    AArch64_FMULXv2i64_indexed	= 630,
    AArch64_FMULXv4f32	= 631,
    AArch64_FMULXv4i32_indexed	= 632,
    AArch64_FMULv1i32_indexed	= 633,
    AArch64_FMULv1i64_indexed	= 634,
    AArch64_FMULv2f32	= 635,
    AArch64_FMULv2f64	= 636,
    AArch64_FMULv2i32_indexed	= 637,
    AArch64_FMULv2i64_indexed	= 638,
    AArch64_FMULv4f32	= 639,
    AArch64_FMULv4i32_indexed	= 640,
    AArch64_FNEGDr	= 641,
    AArch64_FNEGSr	= 642,
    AArch64_FNEGv2f32	= 643,
    AArch64_FNEGv2f64	= 644,
    AArch64_FNEGv4f32	= 645,
    AArch64_FNMADDDrrr	= 646,
    AArch64_FNMADDSrrr	= 647,
    AArch64_FNMSUBDrrr	= 648,
    AArch64_FNMSUBSrrr	= 649,
    AArch64_FNMULDrr	= 650,
    AArch64_FNMULSrr	= 651,
    AArch64_FRECPEv1i32	= 652,
    AArch64_FRECPEv1i64	= 653,
    AArch64_FRECPEv2f32	= 654,
    AArch64_FRECPEv2f64	= 655,
    AArch64_FRECPEv4f32	= 656,
    AArch64_FRECPS32	= 657,
    AArch64_FRECPS64	= 658,
    AArch64_FRECPSv2f32	= 659,
    AArch64_FRECPSv2f64	= 660,
    AArch64_FRECPSv4f32	= 661,
    AArch64_FRECPXv1i32	= 662,
    AArch64_FRECPXv1i64	= 663,
    AArch64_FRINTADr	= 664,
    AArch64_FRINTASr	= 665,
    AArch64_FRINTAv2f32	= 666,
    AArch64_FRINTAv2f64	= 667,
    AArch64_FRINTAv4f32	= 668,
    AArch64_FRINTIDr	= 669,
    AArch64_FRINTISr	= 670,
    AArch64_FRINTIv2f32	= 671,
    AArch64_FRINTIv2f64	= 672,
    AArch64_FRINTIv4f32	= 673,
    AArch64_FRINTMDr	= 674,
    AArch64_FRINTMSr	= 675,
    AArch64_FRINTMv2f32	= 676,
    AArch64_FRINTMv2f64	= 677,
    AArch64_FRINTMv4f32	= 678,
    AArch64_FRINTNDr	= 679,
    AArch64_FRINTNSr	= 680,
    AArch64_FRINTNv2f32	= 681,
    AArch64_FRINTNv2f64	= 682,
    AArch64_FRINTNv4f32	= 683,
    AArch64_FRINTPDr	= 684,
    AArch64_FRINTPSr	= 685,
    AArch64_FRINTPv2f32	= 686,
    AArch64_FRINTPv2f64	= 687,
    AArch64_FRINTPv4f32	= 688,
    AArch64_FRINTXDr	= 689,
    AArch64_FRINTXSr	= 690,
    AArch64_FRINTXv2f32	= 691,
    AArch64_FRINTXv2f64	= 692,
    AArch64_FRINTXv4f32	= 693,
    AArch64_FRINTZDr	= 694,
    AArch64_FRINTZSr	= 695,
    AArch64_FRINTZv2f32	= 696,
    AArch64_FRINTZv2f64	= 697,
    AArch64_FRINTZv4f32	= 698,
    AArch64_FRSQRTEv1i32	= 699,
    AArch64_FRSQRTEv1i64	= 700,
    AArch64_FRSQRTEv2f32	= 701,
    AArch64_FRSQRTEv2f64	= 702,
    AArch64_FRSQRTEv4f32	= 703,
    AArch64_FRSQRTS32	= 704,
    AArch64_FRSQRTS64	= 705,
    AArch64_FRSQRTSv2f32	= 706,
    AArch64_FRSQRTSv2f64	= 707,
    AArch64_FRSQRTSv4f32	= 708,
    AArch64_FSQRTDr	= 709,
    AArch64_FSQRTSr	= 710,
    AArch64_FSQRTv2f32	= 711,
    AArch64_FSQRTv2f64	= 712,
    AArch64_FSQRTv4f32	= 713,
    AArch64_FSUBDrr	= 714,
    AArch64_FSUBSrr	= 715,
    AArch64_FSUBv2f32	= 716,
    AArch64_FSUBv2f64	= 717,
    AArch64_FSUBv4f32	= 718,
    AArch64_HINT	= 719,
    AArch64_HLT	= 720,
    AArch64_HVC	= 721,
    AArch64_INSvi16gpr	= 722,
    AArch64_INSvi16lane	= 723,
    AArch64_INSvi32gpr	= 724,
    AArch64_INSvi32lane	= 725,
    AArch64_INSvi64gpr	= 726,
    AArch64_INSvi64lane	= 727,
    AArch64_INSvi8gpr	= 728,
    AArch64_INSvi8lane	= 729,
    AArch64_ISB	= 730,
    AArch64_LD1Fourv16b	= 731,
    AArch64_LD1Fourv16b_POST	= 732,
    AArch64_LD1Fourv1d	= 733,
    AArch64_LD1Fourv1d_POST	= 734,
    AArch64_LD1Fourv2d	= 735,
    AArch64_LD1Fourv2d_POST	= 736,
    AArch64_LD1Fourv2s	= 737,
    AArch64_LD1Fourv2s_POST	= 738,
    AArch64_LD1Fourv4h	= 739,
    AArch64_LD1Fourv4h_POST	= 740,
    AArch64_LD1Fourv4s	= 741,
    AArch64_LD1Fourv4s_POST	= 742,
    AArch64_LD1Fourv8b	= 743,
    AArch64_LD1Fourv8b_POST	= 744,
    AArch64_LD1Fourv8h	= 745,
    AArch64_LD1Fourv8h_POST	= 746,
    AArch64_LD1Onev16b	= 747,
    AArch64_LD1Onev16b_POST	= 748,
    AArch64_LD1Onev1d	= 749,
    AArch64_LD1Onev1d_POST	= 750,
    AArch64_LD1Onev2d	= 751,
    AArch64_LD1Onev2d_POST	= 752,
    AArch64_LD1Onev2s	= 753,
    AArch64_LD1Onev2s_POST	= 754,
    AArch64_LD1Onev4h	= 755,
    AArch64_LD1Onev4h_POST	= 756,
    AArch64_LD1Onev4s	= 757,
    AArch64_LD1Onev4s_POST	= 758,
    AArch64_LD1Onev8b	= 759,
    AArch64_LD1Onev8b_POST	= 760,
    AArch64_LD1Onev8h	= 761,
    AArch64_LD1Onev8h_POST	= 762,
    AArch64_LD1Rv16b	= 763,
    AArch64_LD1Rv16b_POST	= 764,
    AArch64_LD1Rv1d	= 765,
    AArch64_LD1Rv1d_POST	= 766,
    AArch64_LD1Rv2d	= 767,
    AArch64_LD1Rv2d_POST	= 768,
    AArch64_LD1Rv2s	= 769,
    AArch64_LD1Rv2s_POST	= 770,
    AArch64_LD1Rv4h	= 771,
    AArch64_LD1Rv4h_POST	= 772,
    AArch64_LD1Rv4s	= 773,
    AArch64_LD1Rv4s_POST	= 774,
    AArch64_LD1Rv8b	= 775,
    AArch64_LD1Rv8b_POST	= 776,
    AArch64_LD1Rv8h	= 777,
    AArch64_LD1Rv8h_POST	= 778,
    AArch64_LD1Threev16b	= 779,
    AArch64_LD1Threev16b_POST	= 780,
    AArch64_LD1Threev1d	= 781,
    AArch64_LD1Threev1d_POST	= 782,
    AArch64_LD1Threev2d	= 783,
    AArch64_LD1Threev2d_POST	= 784,
    AArch64_LD1Threev2s	= 785,
    AArch64_LD1Threev2s_POST	= 786,
    AArch64_LD1Threev4h	= 787,
    AArch64_LD1Threev4h_POST	= 788,
    AArch64_LD1Threev4s	= 789,
    AArch64_LD1Threev4s_POST	= 790,
    AArch64_LD1Threev8b	= 791,
    AArch64_LD1Threev8b_POST	= 792,
    AArch64_LD1Threev8h	= 793,
    AArch64_LD1Threev8h_POST	= 794,
    AArch64_LD1Twov16b	= 795,
    AArch64_LD1Twov16b_POST	= 796,
    AArch64_LD1Twov1d	= 797,
    AArch64_LD1Twov1d_POST	= 798,
    AArch64_LD1Twov2d	= 799,
    AArch64_LD1Twov2d_POST	= 800,
    AArch64_LD1Twov2s	= 801,
    AArch64_LD1Twov2s_POST	= 802,
    AArch64_LD1Twov4h	= 803,
    AArch64_LD1Twov4h_POST	= 804,
    AArch64_LD1Twov4s	= 805,
    AArch64_LD1Twov4s_POST	= 806,
    AArch64_LD1Twov8b	= 807,
    AArch64_LD1Twov8b_POST	= 808,
    AArch64_LD1Twov8h	= 809,
    AArch64_LD1Twov8h_POST	= 810,
    AArch64_LD1i16	= 811,
    AArch64_LD1i16_POST	= 812,
    AArch64_LD1i32	= 813,
    AArch64_LD1i32_POST	= 814,
    AArch64_LD1i64	= 815,
    AArch64_LD1i64_POST	= 816,
    AArch64_LD1i8	= 817,
    AArch64_LD1i8_POST	= 818,
    AArch64_LD2Rv16b	= 819,
    AArch64_LD2Rv16b_POST	= 820,
    AArch64_LD2Rv1d	= 821,
    AArch64_LD2Rv1d_POST	= 822,
    AArch64_LD2Rv2d	= 823,
    AArch64_LD2Rv2d_POST	= 824,
    AArch64_LD2Rv2s	= 825,
    AArch64_LD2Rv2s_POST	= 826,
    AArch64_LD2Rv4h	= 827,
    AArch64_LD2Rv4h_POST	= 828,
    AArch64_LD2Rv4s	= 829,
    AArch64_LD2Rv4s_POST	= 830,
    AArch64_LD2Rv8b	= 831,
    AArch64_LD2Rv8b_POST	= 832,
    AArch64_LD2Rv8h	= 833,
    AArch64_LD2Rv8h_POST	= 834,
    AArch64_LD2Twov16b	= 835,
    AArch64_LD2Twov16b_POST	= 836,
    AArch64_LD2Twov2d	= 837,
    AArch64_LD2Twov2d_POST	= 838,
    AArch64_LD2Twov2s	= 839,
    AArch64_LD2Twov2s_POST	= 840,
    AArch64_LD2Twov4h	= 841,
    AArch64_LD2Twov4h_POST	= 842,
    AArch64_LD2Twov4s	= 843,
    AArch64_LD2Twov4s_POST	= 844,
    AArch64_LD2Twov8b	= 845,
    AArch64_LD2Twov8b_POST	= 846,
    AArch64_LD2Twov8h	= 847,
    AArch64_LD2Twov8h_POST	= 848,
    AArch64_LD2i16	= 849,
    AArch64_LD2i16_POST	= 850,
    AArch64_LD2i32	= 851,
    AArch64_LD2i32_POST	= 852,
    AArch64_LD2i64	= 853,
    AArch64_LD2i64_POST	= 854,
    AArch64_LD2i8	= 855,
    AArch64_LD2i8_POST	= 856,
    AArch64_LD3Rv16b	= 857,
    AArch64_LD3Rv16b_POST	= 858,
    AArch64_LD3Rv1d	= 859,
    AArch64_LD3Rv1d_POST	= 860,
    AArch64_LD3Rv2d	= 861,
    AArch64_LD3Rv2d_POST	= 862,
    AArch64_LD3Rv2s	= 863,
    AArch64_LD3Rv2s_POST	= 864,
    AArch64_LD3Rv4h	= 865,
    AArch64_LD3Rv4h_POST	= 866,
    AArch64_LD3Rv4s	= 867,
    AArch64_LD3Rv4s_POST	= 868,
    AArch64_LD3Rv8b	= 869,
    AArch64_LD3Rv8b_POST	= 870,
    AArch64_LD3Rv8h	= 871,
    AArch64_LD3Rv8h_POST	= 872,
    AArch64_LD3Threev16b	= 873,
    AArch64_LD3Threev16b_POST	= 874,
    AArch64_LD3Threev2d	= 875,
    AArch64_LD3Threev2d_POST	= 876,
    AArch64_LD3Threev2s	= 877,
    AArch64_LD3Threev2s_POST	= 878,
    AArch64_LD3Threev4h	= 879,
    AArch64_LD3Threev4h_POST	= 880,
    AArch64_LD3Threev4s	= 881,
    AArch64_LD3Threev4s_POST	= 882,
    AArch64_LD3Threev8b	= 883,
    AArch64_LD3Threev8b_POST	= 884,
    AArch64_LD3Threev8h	= 885,
    AArch64_LD3Threev8h_POST	= 886,
    AArch64_LD3i16	= 887,
    AArch64_LD3i16_POST	= 888,
    AArch64_LD3i32	= 889,
    AArch64_LD3i32_POST	= 890,
    AArch64_LD3i64	= 891,
    AArch64_LD3i64_POST	= 892,
    AArch64_LD3i8	= 893,
    AArch64_LD3i8_POST	= 894,
    AArch64_LD4Fourv16b	= 895,
    AArch64_LD4Fourv16b_POST	= 896,
    AArch64_LD4Fourv2d	= 897,
    AArch64_LD4Fourv2d_POST	= 898,
    AArch64_LD4Fourv2s	= 899,
    AArch64_LD4Fourv2s_POST	= 900,
    AArch64_LD4Fourv4h	= 901,
    AArch64_LD4Fourv4h_POST	= 902,
    AArch64_LD4Fourv4s	= 903,
    AArch64_LD4Fourv4s_POST	= 904,
    AArch64_LD4Fourv8b	= 905,
    AArch64_LD4Fourv8b_POST	= 906,
    AArch64_LD4Fourv8h	= 907,
    AArch64_LD4Fourv8h_POST	= 908,
    AArch64_LD4Rv16b	= 909,
    AArch64_LD4Rv16b_POST	= 910,
    AArch64_LD4Rv1d	= 911,
    AArch64_LD4Rv1d_POST	= 912,
    AArch64_LD4Rv2d	= 913,
    AArch64_LD4Rv2d_POST	= 914,
    AArch64_LD4Rv2s	= 915,
    AArch64_LD4Rv2s_POST	= 916,
    AArch64_LD4Rv4h	= 917,
    AArch64_LD4Rv4h_POST	= 918,
    AArch64_LD4Rv4s	= 919,
    AArch64_LD4Rv4s_POST	= 920,
    AArch64_LD4Rv8b	= 921,
    AArch64_LD4Rv8b_POST	= 922,
    AArch64_LD4Rv8h	= 923,
    AArch64_LD4Rv8h_POST	= 924,
    AArch64_LD4i16	= 925,
    AArch64_LD4i16_POST	= 926,
    AArch64_LD4i32	= 927,
    AArch64_LD4i32_POST	= 928,
    AArch64_LD4i64	= 929,
    AArch64_LD4i64_POST	= 930,
    AArch64_LD4i8	= 931,
    AArch64_LD4i8_POST	= 932,
    AArch64_LDARB	= 933,
    AArch64_LDARH	= 934,
    AArch64_LDARW	= 935,
    AArch64_LDARX	= 936,
    AArch64_LDAXPW	= 937,
    AArch64_LDAXPX	= 938,
    AArch64_LDAXRB	= 939,
    AArch64_LDAXRH	= 940,
    AArch64_LDAXRW	= 941,
    AArch64_LDAXRX	= 942,
    AArch64_LDNPDi	= 943,
    AArch64_LDNPQi	= 944,
    AArch64_LDNPSi	= 945,
    AArch64_LDNPWi	= 946,
    AArch64_LDNPXi	= 947,
    AArch64_LDPDi	= 948,
    AArch64_LDPDpost	= 949,
    AArch64_LDPDpre	= 950,
    AArch64_LDPQi	= 951,
    AArch64_LDPQpost	= 952,
    AArch64_LDPQpre	= 953,
    AArch64_LDPSWi	= 954,
    AArch64_LDPSWpost	= 955,
    AArch64_LDPSWpre	= 956,
    AArch64_LDPSi	= 957,
    AArch64_LDPSpost	= 958,
    AArch64_LDPSpre	= 959,
    AArch64_LDPWi	= 960,
    AArch64_LDPWpost	= 961,
    AArch64_LDPWpre	= 962,
    AArch64_LDPXi	= 963,
    AArch64_LDPXpost	= 964,
    AArch64_LDPXpre	= 965,
    AArch64_LDRBBpost	= 966,
    AArch64_LDRBBpre	= 967,
    AArch64_LDRBBroW	= 968,
    AArch64_LDRBBroX	= 969,
    AArch64_LDRBBui	= 970,
    AArch64_LDRBpost	= 971,
    AArch64_LDRBpre	= 972,
    AArch64_LDRBroW	= 973,
    AArch64_LDRBroX	= 974,
    AArch64_LDRBui	= 975,
    AArch64_LDRDl	= 976,
    AArch64_LDRDpost	= 977,
    AArch64_LDRDpre	= 978,
    AArch64_LDRDroW	= 979,
    AArch64_LDRDroX	= 980,
    AArch64_LDRDui	= 981,
    AArch64_LDRHHpost	= 982,
    AArch64_LDRHHpre	= 983,
    AArch64_LDRHHroW	= 984,
    AArch64_LDRHHroX	= 985,
    AArch64_LDRHHui	= 986,
    AArch64_LDRHpost	= 987,
    AArch64_LDRHpre	= 988,
    AArch64_LDRHroW	= 989,
    AArch64_LDRHroX	= 990,
    AArch64_LDRHui	= 991,
    AArch64_LDRQl	= 992,
    AArch64_LDRQpost	= 993,
    AArch64_LDRQpre	= 994,
    AArch64_LDRQroW	= 995,
    AArch64_LDRQroX	= 996,
    AArch64_LDRQui	= 997,
    AArch64_LDRSBWpost	= 998,
    AArch64_LDRSBWpre	= 999,
    AArch64_LDRSBWroW	= 1000,
    AArch64_LDRSBWroX	= 1001,
    AArch64_LDRSBWui	= 1002,
    AArch64_LDRSBXpost	= 1003,
    AArch64_LDRSBXpre	= 1004,
    AArch64_LDRSBXroW	= 1005,
    AArch64_LDRSBXroX	= 1006,
    AArch64_LDRSBXui	= 1007,
    AArch64_LDRSHWpost	= 1008,
    AArch64_LDRSHWpre	= 1009,
    AArch64_LDRSHWroW	= 1010,
    AArch64_LDRSHWroX	= 1011,
    AArch64_LDRSHWui	= 1012,
    AArch64_LDRSHXpost	= 1013,
    AArch64_LDRSHXpre	= 1014,
    AArch64_LDRSHXroW	= 1015,
    AArch64_LDRSHXroX	= 1016,
    AArch64_LDRSHXui	= 1017,
    AArch64_LDRSWl	= 1018,
    AArch64_LDRSWpost	= 1019,
    AArch64_LDRSWpre	= 1020,
    AArch64_LDRSWroW	= 1021,
    AArch64_LDRSWroX	= 1022,
    AArch64_LDRSWui	= 1023,
    AArch64_LDRSl	= 1024,
    AArch64_LDRSpost	= 1025,
    AArch64_LDRSpre	= 1026,
    AArch64_LDRSroW	= 1027,
    AArch64_LDRSroX	= 1028,
    AArch64_LDRSui	= 1029,
    AArch64_LDRWl	= 1030,
    AArch64_LDRWpost	= 1031,
    AArch64_LDRWpre	= 1032,
    AArch64_LDRWroW	= 1033,
    AArch64_LDRWroX	= 1034,
    AArch64_LDRWui	= 1035,
    AArch64_LDRXl	= 1036,
    AArch64_LDRXpost	= 1037,
    AArch64_LDRXpre	= 1038,
    AArch64_LDRXroW	= 1039,
    AArch64_LDRXroX	= 1040,
    AArch64_LDRXui	= 1041,
    AArch64_LDTRBi	= 1042,
    AArch64_LDTRHi	= 1043,
    AArch64_LDTRSBWi	= 1044,
    AArch64_LDTRSBXi	= 1045,
    AArch64_LDTRSHWi	= 1046,
    AArch64_LDTRSHXi	= 1047,
    AArch64_LDTRSWi	= 1048,
    AArch64_LDTRWi	= 1049,
    AArch64_LDTRXi	= 1050,
    AArch64_LDURBBi	= 1051,
    AArch64_LDURBi	= 1052,
    AArch64_LDURDi	= 1053,
    AArch64_LDURHHi	= 1054,
    AArch64_LDURHi	= 1055,
    AArch64_LDURQi	= 1056,
    AArch64_LDURSBWi	= 1057,
    AArch64_LDURSBXi	= 1058,
    AArch64_LDURSHWi	= 1059,
    AArch64_LDURSHXi	= 1060,
    AArch64_LDURSWi	= 1061,
    AArch64_LDURSi	= 1062,
    AArch64_LDURWi	= 1063,
    AArch64_LDURXi	= 1064,
    AArch64_LDXPW	= 1065,
    AArch64_LDXPX	= 1066,
    AArch64_LDXRB	= 1067,
    AArch64_LDXRH	= 1068,
    AArch64_LDXRW	= 1069,
    AArch64_LDXRX	= 1070,
    AArch64_LOADgot	= 1071,
    AArch64_LSLVWr	= 1072,
    AArch64_LSLVXr	= 1073,
    AArch64_LSRVWr	= 1074,
    AArch64_LSRVXr	= 1075,
    AArch64_MADDWrrr	= 1076,
    AArch64_MADDXrrr	= 1077,
    AArch64_MLAv16i8	= 1078,
    AArch64_MLAv2i32	= 1079,
    AArch64_MLAv2i32_indexed	= 1080,
    AArch64_MLAv4i16	= 1081,
    AArch64_MLAv4i16_indexed	= 1082,
    AArch64_MLAv4i32	= 1083,
    AArch64_MLAv4i32_indexed	= 1084,
    AArch64_MLAv8i16	= 1085,
    AArch64_MLAv8i16_indexed	= 1086,
    AArch64_MLAv8i8	= 1087,
    AArch64_MLSv16i8	= 1088,
    AArch64_MLSv2i32	= 1089,
    AArch64_MLSv2i32_indexed	= 1090,
    AArch64_MLSv4i16	= 1091,
    AArch64_MLSv4i16_indexed	= 1092,
    AArch64_MLSv4i32	= 1093,
    AArch64_MLSv4i32_indexed	= 1094,
    AArch64_MLSv8i16	= 1095,
    AArch64_MLSv8i16_indexed	= 1096,
    AArch64_MLSv8i8	= 1097,
    AArch64_MOVID	= 1098,
    AArch64_MOVIv16b_ns	= 1099,
    AArch64_MOVIv2d_ns	= 1100,
    AArch64_MOVIv2i32	= 1101,
    AArch64_MOVIv2s_msl	= 1102,
    AArch64_MOVIv4i16	= 1103,
    AArch64_MOVIv4i32	= 1104,
    AArch64_MOVIv4s_msl	= 1105,
    AArch64_MOVIv8b_ns	= 1106,
    AArch64_MOVIv8i16	= 1107,
    AArch64_MOVKWi	= 1108,
    AArch64_MOVKXi	= 1109,
    AArch64_MOVNWi	= 1110,
    AArch64_MOVNXi	= 1111,
    AArch64_MOVZWi	= 1112,
    AArch64_MOVZXi	= 1113,
    AArch64_MOVaddr	= 1114,
    AArch64_MOVaddrBA	= 1115,
    AArch64_MOVaddrCP	= 1116,
    AArch64_MOVaddrEXT	= 1117,
    AArch64_MOVaddrJT	= 1118,
    AArch64_MOVaddrTLS	= 1119,
    AArch64_MOVi32imm	= 1120,
    AArch64_MOVi64imm	= 1121,
    AArch64_MRS	= 1122,
    AArch64_MSR	= 1123,
    AArch64_MSRpstate	= 1124,
    AArch64_MSUBWrrr	= 1125,
    AArch64_MSUBXrrr	= 1126,
    AArch64_MULv16i8	= 1127,
    AArch64_MULv2i32	= 1128,
    AArch64_MULv2i32_indexed	= 1129,
    AArch64_MULv4i16	= 1130,
    AArch64_MULv4i16_indexed	= 1131,
    AArch64_MULv4i32	= 1132,
    AArch64_MULv4i32_indexed	= 1133,
    AArch64_MULv8i16	= 1134,
    AArch64_MULv8i16_indexed	= 1135,
    AArch64_MULv8i8	= 1136,
    AArch64_MVNIv2i32	= 1137,
    AArch64_MVNIv2s_msl	= 1138,
    AArch64_MVNIv4i16	= 1139,
    AArch64_MVNIv4i32	= 1140,
    AArch64_MVNIv4s_msl	= 1141,
    AArch64_MVNIv8i16	= 1142,
    AArch64_NEGv16i8	= 1143,
    AArch64_NEGv1i64	= 1144,
    AArch64_NEGv2i32	= 1145,
    AArch64_NEGv2i64	= 1146,
    AArch64_NEGv4i16	= 1147,
    AArch64_NEGv4i32	= 1148,
    AArch64_NEGv8i16	= 1149,
    AArch64_NEGv8i8	= 1150,
    AArch64_NOTv16i8	= 1151,
    AArch64_NOTv8i8	= 1152,
    AArch64_ORNWrr	= 1153,
    AArch64_ORNWrs	= 1154,
    AArch64_ORNXrr	= 1155,
    AArch64_ORNXrs	= 1156,
    AArch64_ORNv16i8	= 1157,
    AArch64_ORNv8i8	= 1158,
    AArch64_ORRWri	= 1159,
    AArch64_ORRWrr	= 1160,
    AArch64_ORRWrs	= 1161,
    AArch64_ORRXri	= 1162,
    AArch64_ORRXrr	= 1163,
    AArch64_ORRXrs	= 1164,
    AArch64_ORRv16i8	= 1165,
    AArch64_ORRv2i32	= 1166,
    AArch64_ORRv4i16	= 1167,
    AArch64_ORRv4i32	= 1168,
    AArch64_ORRv8i16	= 1169,
    AArch64_ORRv8i8	= 1170,
    AArch64_PMULLv16i8	= 1171,
    AArch64_PMULLv1i64	= 1172,
    AArch64_PMULLv2i64	= 1173,
    AArch64_PMULLv8i8	= 1174,
    AArch64_PMULv16i8	= 1175,
    AArch64_PMULv8i8	= 1176,
    AArch64_PRFMl	= 1177,
    AArch64_PRFMroW	= 1178,
    AArch64_PRFMroX	= 1179,
    AArch64_PRFMui	= 1180,
    AArch64_PRFUMi	= 1181,
    AArch64_RADDHNv2i64_v2i32	= 1182,
    AArch64_RADDHNv2i64_v4i32	= 1183,
    AArch64_RADDHNv4i32_v4i16	= 1184,
    AArch64_RADDHNv4i32_v8i16	= 1185,
    AArch64_RADDHNv8i16_v16i8	= 1186,
    AArch64_RADDHNv8i16_v8i8	= 1187,
    AArch64_RBITWr	= 1188,
    AArch64_RBITXr	= 1189,
    AArch64_RBITv16i8	= 1190,
    AArch64_RBITv8i8	= 1191,
    AArch64_RET	= 1192,
    AArch64_RET_ReallyLR	= 1193,
    AArch64_REV16Wr	= 1194,
    AArch64_REV16Xr	= 1195,
    AArch64_REV16v16i8	= 1196,
    AArch64_REV16v8i8	= 1197,
    AArch64_REV32Xr	= 1198,
    AArch64_REV32v16i8	= 1199,
    AArch64_REV32v4i16	= 1200,
    AArch64_REV32v8i16	= 1201,
    AArch64_REV32v8i8	= 1202,
    AArch64_REV64v16i8	= 1203,
    AArch64_REV64v2i32	= 1204,
    AArch64_REV64v4i16	= 1205,
    AArch64_REV64v4i32	= 1206,
    AArch64_REV64v8i16	= 1207,
    AArch64_REV64v8i8	= 1208,
    AArch64_REVWr	= 1209,
    AArch64_REVXr	= 1210,
    AArch64_RORVWr	= 1211,
    AArch64_RORVXr	= 1212,
    AArch64_RSHRNv16i8_shift	= 1213,
    AArch64_RSHRNv2i32_shift	= 1214,
    AArch64_RSHRNv4i16_shift	= 1215,
    AArch64_RSHRNv4i32_shift	= 1216,
    AArch64_RSHRNv8i16_shift	= 1217,
    AArch64_RSHRNv8i8_shift	= 1218,
    AArch64_RSUBHNv2i64_v2i32	= 1219,
    AArch64_RSUBHNv2i64_v4i32	= 1220,
    AArch64_RSUBHNv4i32_v4i16	= 1221,
    AArch64_RSUBHNv4i32_v8i16	= 1222,
    AArch64_RSUBHNv8i16_v16i8	= 1223,
    AArch64_RSUBHNv8i16_v8i8	= 1224,
    AArch64_SABALv16i8_v8i16	= 1225,
    AArch64_SABALv2i32_v2i64	= 1226,
    AArch64_SABALv4i16_v4i32	= 1227,
    AArch64_SABALv4i32_v2i64	= 1228,
    AArch64_SABALv8i16_v4i32	= 1229,
    AArch64_SABALv8i8_v8i16	= 1230,
    AArch64_SABAv16i8	= 1231,
    AArch64_SABAv2i32	= 1232,
    AArch64_SABAv4i16	= 1233,
    AArch64_SABAv4i32	= 1234,
    AArch64_SABAv8i16	= 1235,
    AArch64_SABAv8i8	= 1236,
    AArch64_SABDLv16i8_v8i16	= 1237,
    AArch64_SABDLv2i32_v2i64	= 1238,
    AArch64_SABDLv4i16_v4i32	= 1239,
    AArch64_SABDLv4i32_v2i64	= 1240,
    AArch64_SABDLv8i16_v4i32	= 1241,
    AArch64_SABDLv8i8_v8i16	= 1242,
    AArch64_SABDv16i8	= 1243,
    AArch64_SABDv2i32	= 1244,
    AArch64_SABDv4i16	= 1245,
    AArch64_SABDv4i32	= 1246,
    AArch64_SABDv8i16	= 1247,
    AArch64_SABDv8i8	= 1248,
    AArch64_SADALPv16i8_v8i16	= 1249,
    AArch64_SADALPv2i32_v1i64	= 1250,
    AArch64_SADALPv4i16_v2i32	= 1251,
    AArch64_SADALPv4i32_v2i64	= 1252,
    AArch64_SADALPv8i16_v4i32	= 1253,
    AArch64_SADALPv8i8_v4i16	= 1254,
    AArch64_SADDLPv16i8_v8i16	= 1255,
    AArch64_SADDLPv2i32_v1i64	= 1256,
    AArch64_SADDLPv4i16_v2i32	= 1257,
    AArch64_SADDLPv4i32_v2i64	= 1258,
    AArch64_SADDLPv8i16_v4i32	= 1259,
    AArch64_SADDLPv8i8_v4i16	= 1260,
    AArch64_SADDLVv16i8v	= 1261,
    AArch64_SADDLVv4i16v	= 1262,
    AArch64_SADDLVv4i32v	= 1263,
    AArch64_SADDLVv8i16v	= 1264,
    AArch64_SADDLVv8i8v	= 1265,
    AArch64_SADDLv16i8_v8i16	= 1266,
    AArch64_SADDLv2i32_v2i64	= 1267,
    AArch64_SADDLv4i16_v4i32	= 1268,
    AArch64_SADDLv4i32_v2i64	= 1269,
    AArch64_SADDLv8i16_v4i32	= 1270,
    AArch64_SADDLv8i8_v8i16	= 1271,
    AArch64_SADDWv16i8_v8i16	= 1272,
    AArch64_SADDWv2i32_v2i64	= 1273,
    AArch64_SADDWv4i16_v4i32	= 1274,
    AArch64_SADDWv4i32_v2i64	= 1275,
    AArch64_SADDWv8i16_v4i32	= 1276,
    AArch64_SADDWv8i8_v8i16	= 1277,
    AArch64_SBCSWr	= 1278,
    AArch64_SBCSXr	= 1279,
    AArch64_SBCWr	= 1280,
    AArch64_SBCXr	= 1281,
    AArch64_SBFMWri	= 1282,
    AArch64_SBFMXri	= 1283,
    AArch64_SCVTFSWDri	= 1284,
    AArch64_SCVTFSWSri	= 1285,
    AArch64_SCVTFSXDri	= 1286,
    AArch64_SCVTFSXSri	= 1287,
    AArch64_SCVTFUWDri	= 1288,
    AArch64_SCVTFUWSri	= 1289,
    AArch64_SCVTFUXDri	= 1290,
    AArch64_SCVTFUXSri	= 1291,
    AArch64_SCVTFd	= 1292,
    AArch64_SCVTFs	= 1293,
    AArch64_SCVTFv1i32	= 1294,
    AArch64_SCVTFv1i64	= 1295,
    AArch64_SCVTFv2f32	= 1296,
    AArch64_SCVTFv2f64	= 1297,
    AArch64_SCVTFv2i32_shift	= 1298,
    AArch64_SCVTFv2i64_shift	= 1299,
    AArch64_SCVTFv4f32	= 1300,
    AArch64_SCVTFv4i32_shift	= 1301,
    AArch64_SDIVWr	= 1302,
    AArch64_SDIVXr	= 1303,
    AArch64_SDIV_IntWr	= 1304,
    AArch64_SDIV_IntXr	= 1305,
    AArch64_SHA1Crrr	= 1306,
    AArch64_SHA1Hrr	= 1307,
    AArch64_SHA1Mrrr	= 1308,
    AArch64_SHA1Prrr	= 1309,
    AArch64_SHA1SU0rrr	= 1310,
    AArch64_SHA1SU1rr	= 1311,
    AArch64_SHA256H2rrr	= 1312,
    AArch64_SHA256Hrrr	= 1313,
    AArch64_SHA256SU0rr	= 1314,
    AArch64_SHA256SU1rrr	= 1315,
    AArch64_SHADDv16i8	= 1316,
    AArch64_SHADDv2i32	= 1317,
    AArch64_SHADDv4i16	= 1318,
    AArch64_SHADDv4i32	= 1319,
    AArch64_SHADDv8i16	= 1320,
    AArch64_SHADDv8i8	= 1321,
    AArch64_SHLLv16i8	= 1322,
    AArch64_SHLLv2i32	= 1323,
    AArch64_SHLLv4i16	= 1324,
    AArch64_SHLLv4i32	= 1325,
    AArch64_SHLLv8i16	= 1326,
    AArch64_SHLLv8i8	= 1327,
    AArch64_SHLd	= 1328,
    AArch64_SHLv16i8_shift	= 1329,
    AArch64_SHLv2i32_shift	= 1330,
    AArch64_SHLv2i64_shift	= 1331,
    AArch64_SHLv4i16_shift	= 1332,
    AArch64_SHLv4i32_shift	= 1333,
    AArch64_SHLv8i16_shift	= 1334,
    AArch64_SHLv8i8_shift	= 1335,
    AArch64_SHRNv16i8_shift	= 1336,
    AArch64_SHRNv2i32_shift	= 1337,
    AArch64_SHRNv4i16_shift	= 1338,
    AArch64_SHRNv4i32_shift	= 1339,
    AArch64_SHRNv8i16_shift	= 1340,
    AArch64_SHRNv8i8_shift	= 1341,
    AArch64_SHSUBv16i8	= 1342,
    AArch64_SHSUBv2i32	= 1343,
    AArch64_SHSUBv4i16	= 1344,
    AArch64_SHSUBv4i32	= 1345,
    AArch64_SHSUBv8i16	= 1346,
    AArch64_SHSUBv8i8	= 1347,
    AArch64_SLId	= 1348,
    AArch64_SLIv16i8_shift	= 1349,
    AArch64_SLIv2i32_shift	= 1350,
    AArch64_SLIv2i64_shift	= 1351,
    AArch64_SLIv4i16_shift	= 1352,
    AArch64_SLIv4i32_shift	= 1353,
    AArch64_SLIv8i16_shift	= 1354,
    AArch64_SLIv8i8_shift	= 1355,
    AArch64_SMADDLrrr	= 1356,
    AArch64_SMAXPv16i8	= 1357,
    AArch64_SMAXPv2i32	= 1358,
    AArch64_SMAXPv4i16	= 1359,
    AArch64_SMAXPv4i32	= 1360,
    AArch64_SMAXPv8i16	= 1361,
    AArch64_SMAXPv8i8	= 1362,
    AArch64_SMAXVv16i8v	= 1363,
    AArch64_SMAXVv4i16v	= 1364,
    AArch64_SMAXVv4i32v	= 1365,
    AArch64_SMAXVv8i16v	= 1366,
    AArch64_SMAXVv8i8v	= 1367,
    AArch64_SMAXv16i8	= 1368,
    AArch64_SMAXv2i32	= 1369,
    AArch64_SMAXv4i16	= 1370,
    AArch64_SMAXv4i32	= 1371,
    AArch64_SMAXv8i16	= 1372,
    AArch64_SMAXv8i8	= 1373,
    AArch64_SMC	= 1374,
    AArch64_SMINPv16i8	= 1375,
    AArch64_SMINPv2i32	= 1376,
    AArch64_SMINPv4i16	= 1377,
    AArch64_SMINPv4i32	= 1378,
    AArch64_SMINPv8i16	= 1379,
    AArch64_SMINPv8i8	= 1380,
    AArch64_SMINVv16i8v	= 1381,
    AArch64_SMINVv4i16v	= 1382,
    AArch64_SMINVv4i32v	= 1383,
    AArch64_SMINVv8i16v	= 1384,
    AArch64_SMINVv8i8v	= 1385,
    AArch64_SMINv16i8	= 1386,
    AArch64_SMINv2i32	= 1387,
    AArch64_SMINv4i16	= 1388,
    AArch64_SMINv4i32	= 1389,
    AArch64_SMINv8i16	= 1390,
    AArch64_SMINv8i8	= 1391,
    AArch64_SMLALv16i8_v8i16	= 1392,
    AArch64_SMLALv2i32_indexed	= 1393,
    AArch64_SMLALv2i32_v2i64	= 1394,
    AArch64_SMLALv4i16_indexed	= 1395,
    AArch64_SMLALv4i16_v4i32	= 1396,
    AArch64_SMLALv4i32_indexed	= 1397,
    AArch64_SMLALv4i32_v2i64	= 1398,
    AArch64_SMLALv8i16_indexed	= 1399,
    AArch64_SMLALv8i16_v4i32	= 1400,
    AArch64_SMLALv8i8_v8i16	= 1401,
    AArch64_SMLSLv16i8_v8i16	= 1402,
    AArch64_SMLSLv2i32_indexed	= 1403,
    AArch64_SMLSLv2i32_v2i64	= 1404,
    AArch64_SMLSLv4i16_indexed	= 1405,
    AArch64_SMLSLv4i16_v4i32	= 1406,
    AArch64_SMLSLv4i32_indexed	= 1407,
    AArch64_SMLSLv4i32_v2i64	= 1408,
    AArch64_SMLSLv8i16_indexed	= 1409,
    AArch64_SMLSLv8i16_v4i32	= 1410,
    AArch64_SMLSLv8i8_v8i16	= 1411,
    AArch64_SMOVvi16to32	= 1412,
    AArch64_SMOVvi16to64	= 1413,
    AArch64_SMOVvi32to64	= 1414,
    AArch64_SMOVvi8to32	= 1415,
    AArch64_SMOVvi8to64	= 1416,
    AArch64_SMSUBLrrr	= 1417,
    AArch64_SMULHrr	= 1418,
    AArch64_SMULLv16i8_v8i16	= 1419,
    AArch64_SMULLv2i32_indexed	= 1420,
    AArch64_SMULLv2i32_v2i64	= 1421,
    AArch64_SMULLv4i16_indexed	= 1422,
    AArch64_SMULLv4i16_v4i32	= 1423,
    AArch64_SMULLv4i32_indexed	= 1424,
    AArch64_SMULLv4i32_v2i64	= 1425,
    AArch64_SMULLv8i16_indexed	= 1426,
    AArch64_SMULLv8i16_v4i32	= 1427,
    AArch64_SMULLv8i8_v8i16	= 1428,
    AArch64_SQABSv16i8	= 1429,
    AArch64_SQABSv1i16	= 1430,
    AArch64_SQABSv1i32	= 1431,
    AArch64_SQABSv1i64	= 1432,
    AArch64_SQABSv1i8	= 1433,
    AArch64_SQABSv2i32	= 1434,
    AArch64_SQABSv2i64	= 1435,
    AArch64_SQABSv4i16	= 1436,
    AArch64_SQABSv4i32	= 1437,
    AArch64_SQABSv8i16	= 1438,
    AArch64_SQABSv8i8	= 1439,
    AArch64_SQADDv16i8	= 1440,
    AArch64_SQADDv1i16	= 1441,
    AArch64_SQADDv1i32	= 1442,
    AArch64_SQADDv1i64	= 1443,
    AArch64_SQADDv1i8	= 1444,
    AArch64_SQADDv2i32	= 1445,
    AArch64_SQADDv2i64	= 1446,
    AArch64_SQADDv4i16	= 1447,
    AArch64_SQADDv4i32	= 1448,
    AArch64_SQADDv8i16	= 1449,
    AArch64_SQADDv8i8	= 1450,
    AArch64_SQDMLALi16	= 1451,
    AArch64_SQDMLALi32	= 1452,
    AArch64_SQDMLALv1i32_indexed	= 1453,
    AArch64_SQDMLALv1i64_indexed	= 1454,
    AArch64_SQDMLALv2i32_indexed	= 1455,
    AArch64_SQDMLALv2i32_v2i64	= 1456,
    AArch64_SQDMLALv4i16_indexed	= 1457,
    AArch64_SQDMLALv4i16_v4i32	= 1458,
    AArch64_SQDMLALv4i32_indexed	= 1459,
    AArch64_SQDMLALv4i32_v2i64	= 1460,
    AArch64_SQDMLALv8i16_indexed	= 1461,
    AArch64_SQDMLALv8i16_v4i32	= 1462,
    AArch64_SQDMLSLi16	= 1463,
    AArch64_SQDMLSLi32	= 1464,
    AArch64_SQDMLSLv1i32_indexed	= 1465,
    AArch64_SQDMLSLv1i64_indexed	= 1466,
    AArch64_SQDMLSLv2i32_indexed	= 1467,
    AArch64_SQDMLSLv2i32_v2i64	= 1468,
    AArch64_SQDMLSLv4i16_indexed	= 1469,
    AArch64_SQDMLSLv4i16_v4i32	= 1470,
    AArch64_SQDMLSLv4i32_indexed	= 1471,
    AArch64_SQDMLSLv4i32_v2i64	= 1472,
    AArch64_SQDMLSLv8i16_indexed	= 1473,
    AArch64_SQDMLSLv8i16_v4i32	= 1474,
    AArch64_SQDMULHv1i16	= 1475,
    AArch64_SQDMULHv1i16_indexed	= 1476,
    AArch64_SQDMULHv1i32	= 1477,
    AArch64_SQDMULHv1i32_indexed	= 1478,
    AArch64_SQDMULHv2i32	= 1479,
    AArch64_SQDMULHv2i32_indexed	= 1480,
    AArch64_SQDMULHv4i16	= 1481,
    AArch64_SQDMULHv4i16_indexed	= 1482,
    AArch64_SQDMULHv4i32	= 1483,
    AArch64_SQDMULHv4i32_indexed	= 1484,
    AArch64_SQDMULHv8i16	= 1485,
    AArch64_SQDMULHv8i16_indexed	= 1486,
    AArch64_SQDMULLi16	= 1487,
    AArch64_SQDMULLi32	= 1488,
    AArch64_SQDMULLv1i32_indexed	= 1489,
    AArch64_SQDMULLv1i64_indexed	= 1490,
    AArch64_SQDMULLv2i32_indexed	= 1491,
    AArch64_SQDMULLv2i32_v2i64	= 1492,
    AArch64_SQDMULLv4i16_indexed	= 1493,
    AArch64_SQDMULLv4i16_v4i32	= 1494,
    AArch64_SQDMULLv4i32_indexed	= 1495,
    AArch64_SQDMULLv4i32_v2i64	= 1496,
    AArch64_SQDMULLv8i16_indexed	= 1497,
    AArch64_SQDMULLv8i16_v4i32	= 1498,
    AArch64_SQNEGv16i8	= 1499,
    AArch64_SQNEGv1i16	= 1500,
    AArch64_SQNEGv1i32	= 1501,
    AArch64_SQNEGv1i64	= 1502,
    AArch64_SQNEGv1i8	= 1503,
    AArch64_SQNEGv2i32	= 1504,
    AArch64_SQNEGv2i64	= 1505,
    AArch64_SQNEGv4i16	= 1506,
    AArch64_SQNEGv4i32	= 1507,
    AArch64_SQNEGv8i16	= 1508,
    AArch64_SQNEGv8i8	= 1509,
    AArch64_SQRDMULHv1i16	= 1510,
    AArch64_SQRDMULHv1i16_indexed	= 1511,
    AArch64_SQRDMULHv1i32	= 1512,
    AArch64_SQRDMULHv1i32_indexed	= 1513,
    AArch64_SQRDMULHv2i32	= 1514,
    AArch64_SQRDMULHv2i32_indexed	= 1515,
    AArch64_SQRDMULHv4i16	= 1516,
    AArch64_SQRDMULHv4i16_indexed	= 1517,
    AArch64_SQRDMULHv4i32	= 1518,
    AArch64_SQRDMULHv4i32_indexed	= 1519,
    AArch64_SQRDMULHv8i16	= 1520,
    AArch64_SQRDMULHv8i16_indexed	= 1521,
    AArch64_SQRSHLv16i8	= 1522,
    AArch64_SQRSHLv1i16	= 1523,
    AArch64_SQRSHLv1i32	= 1524,
    AArch64_SQRSHLv1i64	= 1525,
    AArch64_SQRSHLv1i8	= 1526,
    AArch64_SQRSHLv2i32	= 1527,
    AArch64_SQRSHLv2i64	= 1528,
    AArch64_SQRSHLv4i16	= 1529,
    AArch64_SQRSHLv4i32	= 1530,
    AArch64_SQRSHLv8i16	= 1531,
    AArch64_SQRSHLv8i8	= 1532,
    AArch64_SQRSHRNb	= 1533,
    AArch64_SQRSHRNh	= 1534,
    AArch64_SQRSHRNs	= 1535,
    AArch64_SQRSHRNv16i8_shift	= 1536,
    AArch64_SQRSHRNv2i32_shift	= 1537,
    AArch64_SQRSHRNv4i16_shift	= 1538,
    AArch64_SQRSHRNv4i32_shift	= 1539,
    AArch64_SQRSHRNv8i16_shift	= 1540,
    AArch64_SQRSHRNv8i8_shift	= 1541,
    AArch64_SQRSHRUNb	= 1542,
    AArch64_SQRSHRUNh	= 1543,
    AArch64_SQRSHRUNs	= 1544,
    AArch64_SQRSHRUNv16i8_shift	= 1545,
    AArch64_SQRSHRUNv2i32_shift	= 1546,
    AArch64_SQRSHRUNv4i16_shift	= 1547,
    AArch64_SQRSHRUNv4i32_shift	= 1548,
    AArch64_SQRSHRUNv8i16_shift	= 1549,
    AArch64_SQRSHRUNv8i8_shift	= 1550,
    AArch64_SQSHLUb	= 1551,
    AArch64_SQSHLUd	= 1552,
    AArch64_SQSHLUh	= 1553,
    AArch64_SQSHLUs	= 1554,
    AArch64_SQSHLUv16i8_shift	= 1555,
    AArch64_SQSHLUv2i32_shift	= 1556,
    AArch64_SQSHLUv2i64_shift	= 1557,
    AArch64_SQSHLUv4i16_shift	= 1558,
    AArch64_SQSHLUv4i32_shift	= 1559,
    AArch64_SQSHLUv8i16_shift	= 1560,
    AArch64_SQSHLUv8i8_shift	= 1561,
    AArch64_SQSHLb	= 1562,
    AArch64_SQSHLd	= 1563,
    AArch64_SQSHLh	= 1564,
    AArch64_SQSHLs	= 1565,
    AArch64_SQSHLv16i8	= 1566,
    AArch64_SQSHLv16i8_shift	= 1567,
    AArch64_SQSHLv1i16	= 1568,
    AArch64_SQSHLv1i32	= 1569,
    AArch64_SQSHLv1i64	= 1570,
    AArch64_SQSHLv1i8	= 1571,
    AArch64_SQSHLv2i32	= 1572,
    AArch64_SQSHLv2i32_shift	= 1573,
    AArch64_SQSHLv2i64	= 1574,
    AArch64_SQSHLv2i64_shift	= 1575,
    AArch64_SQSHLv4i16	= 1576,
    AArch64_SQSHLv4i16_shift	= 1577,
    AArch64_SQSHLv4i32	= 1578,
    AArch64_SQSHLv4i32_shift	= 1579,
    AArch64_SQSHLv8i16	= 1580,
    AArch64_SQSHLv8i16_shift	= 1581,
    AArch64_SQSHLv8i8	= 1582,
    AArch64_SQSHLv8i8_shift	= 1583,
    AArch64_SQSHRNb	= 1584,
    AArch64_SQSHRNh	= 1585,
    AArch64_SQSHRNs	= 1586,
    AArch64_SQSHRNv16i8_shift	= 1587,
    AArch64_SQSHRNv2i32_shift	= 1588,
    AArch64_SQSHRNv4i16_shift	= 1589,
    AArch64_SQSHRNv4i32_shift	= 1590,
    AArch64_SQSHRNv8i16_shift	= 1591,
    AArch64_SQSHRNv8i8_shift	= 1592,
    AArch64_SQSHRUNb	= 1593,
    AArch64_SQSHRUNh	= 1594,
    AArch64_SQSHRUNs	= 1595,
    AArch64_SQSHRUNv16i8_shift	= 1596,
    AArch64_SQSHRUNv2i32_shift	= 1597,
    AArch64_SQSHRUNv4i16_shift	= 1598,
    AArch64_SQSHRUNv4i32_shift	= 1599,
    AArch64_SQSHRUNv8i16_shift	= 1600,
    AArch64_SQSHRUNv8i8_shift	= 1601,
    AArch64_SQSUBv16i8	= 1602,
    AArch64_SQSUBv1i16	= 1603,
    AArch64_SQSUBv1i32	= 1604,
    AArch64_SQSUBv1i64	= 1605,
    AArch64_SQSUBv1i8	= 1606,
    AArch64_SQSUBv2i32	= 1607,
    AArch64_SQSUBv2i64	= 1608,
    AArch64_SQSUBv4i16	= 1609,
    AArch64_SQSUBv4i32	= 1610,
    AArch64_SQSUBv8i16	= 1611,
    AArch64_SQSUBv8i8	= 1612,
    AArch64_SQXTNv16i8	= 1613,
    AArch64_SQXTNv1i16	= 1614,
    AArch64_SQXTNv1i32	= 1615,
    AArch64_SQXTNv1i8	= 1616,
    AArch64_SQXTNv2i32	= 1617,
    AArch64_SQXTNv4i16	= 1618,
    AArch64_SQXTNv4i32	= 1619,
    AArch64_SQXTNv8i16	= 1620,
    AArch64_SQXTNv8i8	= 1621,
    AArch64_SQXTUNv16i8	= 1622,
    AArch64_SQXTUNv1i16	= 1623,
    AArch64_SQXTUNv1i32	= 1624,
    AArch64_SQXTUNv1i8	= 1625,
    AArch64_SQXTUNv2i32	= 1626,
    AArch64_SQXTUNv4i16	= 1627,
    AArch64_SQXTUNv4i32	= 1628,
    AArch64_SQXTUNv8i16	= 1629,
    AArch64_SQXTUNv8i8	= 1630,
    AArch64_SRHADDv16i8	= 1631,
    AArch64_SRHADDv2i32	= 1632,
    AArch64_SRHADDv4i16	= 1633,
    AArch64_SRHADDv4i32	= 1634,
    AArch64_SRHADDv8i16	= 1635,
    AArch64_SRHADDv8i8	= 1636,
    AArch64_SRId	= 1637,
    AArch64_SRIv16i8_shift	= 1638,
    AArch64_SRIv2i32_shift	= 1639,
    AArch64_SRIv2i64_shift	= 1640,
    AArch64_SRIv4i16_shift	= 1641,
    AArch64_SRIv4i32_shift	= 1642,
    AArch64_SRIv8i16_shift	= 1643,
    AArch64_SRIv8i8_shift	= 1644,
    AArch64_SRSHLv16i8	= 1645,
    AArch64_SRSHLv1i64	= 1646,
    AArch64_SRSHLv2i32	= 1647,
    AArch64_SRSHLv2i64	= 1648,
    AArch64_SRSHLv4i16	= 1649,
    AArch64_SRSHLv4i32	= 1650,
    AArch64_SRSHLv8i16	= 1651,
    AArch64_SRSHLv8i8	= 1652,
    AArch64_SRSHRd	= 1653,
    AArch64_SRSHRv16i8_shift	= 1654,
    AArch64_SRSHRv2i32_shift	= 1655,
    AArch64_SRSHRv2i64_shift	= 1656,
    AArch64_SRSHRv4i16_shift	= 1657,
    AArch64_SRSHRv4i32_shift	= 1658,
    AArch64_SRSHRv8i16_shift	= 1659,
    AArch64_SRSHRv8i8_shift	= 1660,
    AArch64_SRSRAd	= 1661,
    AArch64_SRSRAv16i8_shift	= 1662,
    AArch64_SRSRAv2i32_shift	= 1663,
    AArch64_SRSRAv2i64_shift	= 1664,
    AArch64_SRSRAv4i16_shift	= 1665,
    AArch64_SRSRAv4i32_shift	= 1666,
    AArch64_SRSRAv8i16_shift	= 1667,
    AArch64_SRSRAv8i8_shift	= 1668,
    AArch64_SSHLLv16i8_shift	= 1669,
    AArch64_SSHLLv2i32_shift	= 1670,
    AArch64_SSHLLv4i16_shift	= 1671,
    AArch64_SSHLLv4i32_shift	= 1672,
    AArch64_SSHLLv8i16_shift	= 1673,
    AArch64_SSHLLv8i8_shift	= 1674,
    AArch64_SSHLv16i8	= 1675,
    AArch64_SSHLv1i64	= 1676,
    AArch64_SSHLv2i32	= 1677,
    AArch64_SSHLv2i64	= 1678,
    AArch64_SSHLv4i16	= 1679,
    AArch64_SSHLv4i32	= 1680,
    AArch64_SSHLv8i16	= 1681,
    AArch64_SSHLv8i8	= 1682,
    AArch64_SSHRd	= 1683,
    AArch64_SSHRv16i8_shift	= 1684,
    AArch64_SSHRv2i32_shift	= 1685,
    AArch64_SSHRv2i64_shift	= 1686,
    AArch64_SSHRv4i16_shift	= 1687,
    AArch64_SSHRv4i32_shift	= 1688,
    AArch64_SSHRv8i16_shift	= 1689,
    AArch64_SSHRv8i8_shift	= 1690,
    AArch64_SSRAd	= 1691,
    AArch64_SSRAv16i8_shift	= 1692,
    AArch64_SSRAv2i32_shift	= 1693,
    AArch64_SSRAv2i64_shift	= 1694,
    AArch64_SSRAv4i16_shift	= 1695,
    AArch64_SSRAv4i32_shift	= 1696,
    AArch64_SSRAv8i16_shift	= 1697,
    AArch64_SSRAv8i8_shift	= 1698,
    AArch64_SSUBLv16i8_v8i16	= 1699,
    AArch64_SSUBLv2i32_v2i64	= 1700,
    AArch64_SSUBLv4i16_v4i32	= 1701,
    AArch64_SSUBLv4i32_v2i64	= 1702,
    AArch64_SSUBLv8i16_v4i32	= 1703,
    AArch64_SSUBLv8i8_v8i16	= 1704,
    AArch64_SSUBWv16i8_v8i16	= 1705,
    AArch64_SSUBWv2i32_v2i64	= 1706,
    AArch64_SSUBWv4i16_v4i32	= 1707,
    AArch64_SSUBWv4i32_v2i64	= 1708,
    AArch64_SSUBWv8i16_v4i32	= 1709,
    AArch64_SSUBWv8i8_v8i16	= 1710,
    AArch64_ST1Fourv16b	= 1711,
    AArch64_ST1Fourv16b_POST	= 1712,
    AArch64_ST1Fourv1d	= 1713,
    AArch64_ST1Fourv1d_POST	= 1714,
    AArch64_ST1Fourv2d	= 1715,
    AArch64_ST1Fourv2d_POST	= 1716,
    AArch64_ST1Fourv2s	= 1717,
    AArch64_ST1Fourv2s_POST	= 1718,
    AArch64_ST1Fourv4h	= 1719,
    AArch64_ST1Fourv4h_POST	= 1720,
    AArch64_ST1Fourv4s	= 1721,
    AArch64_ST1Fourv4s_POST	= 1722,
    AArch64_ST1Fourv8b	= 1723,
    AArch64_ST1Fourv8b_POST	= 1724,
    AArch64_ST1Fourv8h	= 1725,
    AArch64_ST1Fourv8h_POST	= 1726,
    AArch64_ST1Onev16b	= 1727,
    AArch64_ST1Onev16b_POST	= 1728,
    AArch64_ST1Onev1d	= 1729,
    AArch64_ST1Onev1d_POST	= 1730,
    AArch64_ST1Onev2d	= 1731,
    AArch64_ST1Onev2d_POST	= 1732,
    AArch64_ST1Onev2s	= 1733,
    AArch64_ST1Onev2s_POST	= 1734,
    AArch64_ST1Onev4h	= 1735,
    AArch64_ST1Onev4h_POST	= 1736,
    AArch64_ST1Onev4s	= 1737,
    AArch64_ST1Onev4s_POST	= 1738,
    AArch64_ST1Onev8b	= 1739,
    AArch64_ST1Onev8b_POST	= 1740,
    AArch64_ST1Onev8h	= 1741,
    AArch64_ST1Onev8h_POST	= 1742,
    AArch64_ST1Threev16b	= 1743,
    AArch64_ST1Threev16b_POST	= 1744,
    AArch64_ST1Threev1d	= 1745,
    AArch64_ST1Threev1d_POST	= 1746,
    AArch64_ST1Threev2d	= 1747,
    AArch64_ST1Threev2d_POST	= 1748,
    AArch64_ST1Threev2s	= 1749,
    AArch64_ST1Threev2s_POST	= 1750,
    AArch64_ST1Threev4h	= 1751,
    AArch64_ST1Threev4h_POST	= 1752,
    AArch64_ST1Threev4s	= 1753,
    AArch64_ST1Threev4s_POST	= 1754,
    AArch64_ST1Threev8b	= 1755,
    AArch64_ST1Threev8b_POST	= 1756,
    AArch64_ST1Threev8h	= 1757,
    AArch64_ST1Threev8h_POST	= 1758,
    AArch64_ST1Twov16b	= 1759,
    AArch64_ST1Twov16b_POST	= 1760,
    AArch64_ST1Twov1d	= 1761,
    AArch64_ST1Twov1d_POST	= 1762,
    AArch64_ST1Twov2d	= 1763,
    AArch64_ST1Twov2d_POST	= 1764,
    AArch64_ST1Twov2s	= 1765,
    AArch64_ST1Twov2s_POST	= 1766,
    AArch64_ST1Twov4h	= 1767,
    AArch64_ST1Twov4h_POST	= 1768,
    AArch64_ST1Twov4s	= 1769,
    AArch64_ST1Twov4s_POST	= 1770,
    AArch64_ST1Twov8b	= 1771,
    AArch64_ST1Twov8b_POST	= 1772,
    AArch64_ST1Twov8h	= 1773,
    AArch64_ST1Twov8h_POST	= 1774,
    AArch64_ST1i16	= 1775,
    AArch64_ST1i16_POST	= 1776,
    AArch64_ST1i32	= 1777,
    AArch64_ST1i32_POST	= 1778,
    AArch64_ST1i64	= 1779,
    AArch64_ST1i64_POST	= 1780,
    AArch64_ST1i8	= 1781,
    AArch64_ST1i8_POST	= 1782,
    AArch64_ST2Twov16b	= 1783,
    AArch64_ST2Twov16b_POST	= 1784,
    AArch64_ST2Twov2d	= 1785,
    AArch64_ST2Twov2d_POST	= 1786,
    AArch64_ST2Twov2s	= 1787,
    AArch64_ST2Twov2s_POST	= 1788,
    AArch64_ST2Twov4h	= 1789,
    AArch64_ST2Twov4h_POST	= 1790,
    AArch64_ST2Twov4s	= 1791,
    AArch64_ST2Twov4s_POST	= 1792,
    AArch64_ST2Twov8b	= 1793,
    AArch64_ST2Twov8b_POST	= 1794,
    AArch64_ST2Twov8h	= 1795,
    AArch64_ST2Twov8h_POST	= 1796,
    AArch64_ST2i16	= 1797,
    AArch64_ST2i16_POST	= 1798,
    AArch64_ST2i32	= 1799,
    AArch64_ST2i32_POST	= 1800,
    AArch64_ST2i64	= 1801,
    AArch64_ST2i64_POST	= 1802,
    AArch64_ST2i8	= 1803,
    AArch64_ST2i8_POST	= 1804,
    AArch64_ST3Threev16b	= 1805,
    AArch64_ST3Threev16b_POST	= 1806,
    AArch64_ST3Threev2d	= 1807,
    AArch64_ST3Threev2d_POST	= 1808,
    AArch64_ST3Threev2s	= 1809,
    AArch64_ST3Threev2s_POST	= 1810,
    AArch64_ST3Threev4h	= 1811,
    AArch64_ST3Threev4h_POST	= 1812,
    AArch64_ST3Threev4s	= 1813,
    AArch64_ST3Threev4s_POST	= 1814,
    AArch64_ST3Threev8b	= 1815,
    AArch64_ST3Threev8b_POST	= 1816,
    AArch64_ST3Threev8h	= 1817,
    AArch64_ST3Threev8h_POST	= 1818,
    AArch64_ST3i16	= 1819,
    AArch64_ST3i16_POST	= 1820,
    AArch64_ST3i32	= 1821,
    AArch64_ST3i32_POST	= 1822,
    AArch64_ST3i64	= 1823,
    AArch64_ST3i64_POST	= 1824,
    AArch64_ST3i8	= 1825,
    AArch64_ST3i8_POST	= 1826,
    AArch64_ST4Fourv16b	= 1827,
    AArch64_ST4Fourv16b_POST	= 1828,
    AArch64_ST4Fourv2d	= 1829,
    AArch64_ST4Fourv2d_POST	= 1830,
    AArch64_ST4Fourv2s	= 1831,
    AArch64_ST4Fourv2s_POST	= 1832,
    AArch64_ST4Fourv4h	= 1833,
    AArch64_ST4Fourv4h_POST	= 1834,
    AArch64_ST4Fourv4s	= 1835,
    AArch64_ST4Fourv4s_POST	= 1836,
    AArch64_ST4Fourv8b	= 1837,
    AArch64_ST4Fourv8b_POST	= 1838,
    AArch64_ST4Fourv8h	= 1839,
    AArch64_ST4Fourv8h_POST	= 1840,
    AArch64_ST4i16	= 1841,
    AArch64_ST4i16_POST	= 1842,
    AArch64_ST4i32	= 1843,
    AArch64_ST4i32_POST	= 1844,
    AArch64_ST4i64	= 1845,
    AArch64_ST4i64_POST	= 1846,
    AArch64_ST4i8	= 1847,
    AArch64_ST4i8_POST	= 1848,
    AArch64_STLRB	= 1849,
    AArch64_STLRH	= 1850,
    AArch64_STLRW	= 1851,
    AArch64_STLRX	= 1852,
    AArch64_STLXPW	= 1853,
    AArch64_STLXPX	= 1854,
    AArch64_STLXRB	= 1855,
    AArch64_STLXRH	= 1856,
    AArch64_STLXRW	= 1857,
    AArch64_STLXRX	= 1858,
    AArch64_STNPDi	= 1859,
    AArch64_STNPQi	= 1860,
    AArch64_STNPSi	= 1861,
    AArch64_STNPWi	= 1862,
    AArch64_STNPXi	= 1863,
    AArch64_STPDi	= 1864,
    AArch64_STPDpost	= 1865,
    AArch64_STPDpre	= 1866,
    AArch64_STPQi	= 1867,
    AArch64_STPQpost	= 1868,
    AArch64_STPQpre	= 1869,
    AArch64_STPSi	= 1870,
    AArch64_STPSpost	= 1871,
    AArch64_STPSpre	= 1872,
    AArch64_STPWi	= 1873,
    AArch64_STPWpost	= 1874,
    AArch64_STPWpre	= 1875,
    AArch64_STPXi	= 1876,
    AArch64_STPXpost	= 1877,
    AArch64_STPXpre	= 1878,
    AArch64_STRBBpost	= 1879,
    AArch64_STRBBpre	= 1880,
    AArch64_STRBBroW	= 1881,
    AArch64_STRBBroX	= 1882,
    AArch64_STRBBui	= 1883,
    AArch64_STRBpost	= 1884,
    AArch64_STRBpre	= 1885,
    AArch64_STRBroW	= 1886,
    AArch64_STRBroX	= 1887,
    AArch64_STRBui	= 1888,
    AArch64_STRDpost	= 1889,
    AArch64_STRDpre	= 1890,
    AArch64_STRDroW	= 1891,
    AArch64_STRDroX	= 1892,
    AArch64_STRDui	= 1893,
    AArch64_STRHHpost	= 1894,
    AArch64_STRHHpre	= 1895,
    AArch64_STRHHroW	= 1896,
    AArch64_STRHHroX	= 1897,
    AArch64_STRHHui	= 1898,
    AArch64_STRHpost	= 1899,
    AArch64_STRHpre	= 1900,
    AArch64_STRHroW	= 1901,
    AArch64_STRHroX	= 1902,
    AArch64_STRHui	= 1903,
    AArch64_STRQpost	= 1904,
    AArch64_STRQpre	= 1905,
    AArch64_STRQroW	= 1906,
    AArch64_STRQroX	= 1907,
    AArch64_STRQui	= 1908,
    AArch64_STRSpost	= 1909,
    AArch64_STRSpre	= 1910,
    AArch64_STRSroW	= 1911,
    AArch64_STRSroX	= 1912,
    AArch64_STRSui	= 1913,
    AArch64_STRWpost	= 1914,
    AArch64_STRWpre	= 1915,
    AArch64_STRWroW	= 1916,
    AArch64_STRWroX	= 1917,
    AArch64_STRWui	= 1918,
    AArch64_STRXpost	= 1919,
    AArch64_STRXpre	= 1920,
    AArch64_STRXroW	= 1921,
    AArch64_STRXroX	= 1922,
    AArch64_STRXui	= 1923,
    AArch64_STTRBi	= 1924,
    AArch64_STTRHi	= 1925,
    AArch64_STTRWi	= 1926,
    AArch64_STTRXi	= 1927,
    AArch64_STURBBi	= 1928,
    AArch64_STURBi	= 1929,
    AArch64_STURDi	= 1930,
    AArch64_STURHHi	= 1931,
    AArch64_STURHi	= 1932,
    AArch64_STURQi	= 1933,
    AArch64_STURSi	= 1934,
    AArch64_STURWi	= 1935,
    AArch64_STURXi	= 1936,
    AArch64_STXPW	= 1937,
    AArch64_STXPX	= 1938,
    AArch64_STXRB	= 1939,
    AArch64_STXRH	= 1940,
    AArch64_STXRW	= 1941,
    AArch64_STXRX	= 1942,
    AArch64_SUBHNv2i64_v2i32	= 1943,
    AArch64_SUBHNv2i64_v4i32	= 1944,
    AArch64_SUBHNv4i32_v4i16	= 1945,
    AArch64_SUBHNv4i32_v8i16	= 1946,
    AArch64_SUBHNv8i16_v16i8	= 1947,
    AArch64_SUBHNv8i16_v8i8	= 1948,
    AArch64_SUBSWri	= 1949,
    AArch64_SUBSWrr	= 1950,
    AArch64_SUBSWrs	= 1951,
    AArch64_SUBSWrx	= 1952,
    AArch64_SUBSXri	= 1953,
    AArch64_SUBSXrr	= 1954,
    AArch64_SUBSXrs	= 1955,
    AArch64_SUBSXrx	= 1956,
    AArch64_SUBSXrx64	= 1957,
    AArch64_SUBWri	= 1958,
    AArch64_SUBWrr	= 1959,
    AArch64_SUBWrs	= 1960,
    AArch64_SUBWrx	= 1961,
    AArch64_SUBXri	= 1962,
    AArch64_SUBXrr	= 1963,
    AArch64_SUBXrs	= 1964,
    AArch64_SUBXrx	= 1965,
    AArch64_SUBXrx64	= 1966,
    AArch64_SUBv16i8	= 1967,
    AArch64_SUBv1i64	= 1968,
    AArch64_SUBv2i32	= 1969,
    AArch64_SUBv2i64	= 1970,
    AArch64_SUBv4i16	= 1971,
    AArch64_SUBv4i32	= 1972,
    AArch64_SUBv8i16	= 1973,
    AArch64_SUBv8i8	= 1974,
    AArch64_SUQADDv16i8	= 1975,
    AArch64_SUQADDv1i16	= 1976,
    AArch64_SUQADDv1i32	= 1977,
    AArch64_SUQADDv1i64	= 1978,
    AArch64_SUQADDv1i8	= 1979,
    AArch64_SUQADDv2i32	= 1980,
    AArch64_SUQADDv2i64	= 1981,
    AArch64_SUQADDv4i16	= 1982,
    AArch64_SUQADDv4i32	= 1983,
    AArch64_SUQADDv8i16	= 1984,
    AArch64_SUQADDv8i8	= 1985,
    AArch64_SVC	= 1986,
    AArch64_SYSLxt	= 1987,
    AArch64_SYSxt	= 1988,
    AArch64_TBLv16i8Four	= 1989,
    AArch64_TBLv16i8One	= 1990,
    AArch64_TBLv16i8Three	= 1991,
    AArch64_TBLv16i8Two	= 1992,
    AArch64_TBLv8i8Four	= 1993,
    AArch64_TBLv8i8One	= 1994,
    AArch64_TBLv8i8Three	= 1995,
    AArch64_TBLv8i8Two	= 1996,
    AArch64_TBNZW	= 1997,
    AArch64_TBNZX	= 1998,
    AArch64_TBXv16i8Four	= 1999,
    AArch64_TBXv16i8One	= 2000,
    AArch64_TBXv16i8Three	= 2001,
    AArch64_TBXv16i8Two	= 2002,
    AArch64_TBXv8i8Four	= 2003,
    AArch64_TBXv8i8One	= 2004,
    AArch64_TBXv8i8Three	= 2005,
    AArch64_TBXv8i8Two	= 2006,
    AArch64_TBZW	= 2007,
    AArch64_TBZX	= 2008,
    AArch64_TCRETURNdi	= 2009,
    AArch64_TCRETURNri	= 2010,
    AArch64_TLSDESCCALL	= 2011,
    AArch64_TLSDESC_BLR	= 2012,
    AArch64_TRN1v16i8	= 2013,
    AArch64_TRN1v2i32	= 2014,
    AArch64_TRN1v2i64	= 2015,
    AArch64_TRN1v4i16	= 2016,
    AArch64_TRN1v4i32	= 2017,
    AArch64_TRN1v8i16	= 2018,
    AArch64_TRN1v8i8	= 2019,
    AArch64_TRN2v16i8	= 2020,
    AArch64_TRN2v2i32	= 2021,
    AArch64_TRN2v2i64	= 2022,
    AArch64_TRN2v4i16	= 2023,
    AArch64_TRN2v4i32	= 2024,
    AArch64_TRN2v8i16	= 2025,
    AArch64_TRN2v8i8	= 2026,
    AArch64_UABALv16i8_v8i16	= 2027,
    AArch64_UABALv2i32_v2i64	= 2028,
    AArch64_UABALv4i16_v4i32	= 2029,
    AArch64_UABALv4i32_v2i64	= 2030,
    AArch64_UABALv8i16_v4i32	= 2031,
    AArch64_UABALv8i8_v8i16	= 2032,
    AArch64_UABAv16i8	= 2033,
    AArch64_UABAv2i32	= 2034,
    AArch64_UABAv4i16	= 2035,
    AArch64_UABAv4i32	= 2036,
    AArch64_UABAv8i16	= 2037,
    AArch64_UABAv8i8	= 2038,
    AArch64_UABDLv16i8_v8i16	= 2039,
    AArch64_UABDLv2i32_v2i64	= 2040,
    AArch64_UABDLv4i16_v4i32	= 2041,
    AArch64_UABDLv4i32_v2i64	= 2042,
    AArch64_UABDLv8i16_v4i32	= 2043,
    AArch64_UABDLv8i8_v8i16	= 2044,
    AArch64_UABDv16i8	= 2045,
    AArch64_UABDv2i32	= 2046,
    AArch64_UABDv4i16	= 2047,
    AArch64_UABDv4i32	= 2048,
    AArch64_UABDv8i16	= 2049,
    AArch64_UABDv8i8	= 2050,
    AArch64_UADALPv16i8_v8i16	= 2051,
    AArch64_UADALPv2i32_v1i64	= 2052,
    AArch64_UADALPv4i16_v2i32	= 2053,
    AArch64_UADALPv4i32_v2i64	= 2054,
    AArch64_UADALPv8i16_v4i32	= 2055,
    AArch64_UADALPv8i8_v4i16	= 2056,
    AArch64_UADDLPv16i8_v8i16	= 2057,
    AArch64_UADDLPv2i32_v1i64	= 2058,
    AArch64_UADDLPv4i16_v2i32	= 2059,
    AArch64_UADDLPv4i32_v2i64	= 2060,
    AArch64_UADDLPv8i16_v4i32	= 2061,
    AArch64_UADDLPv8i8_v4i16	= 2062,
    AArch64_UADDLVv16i8v	= 2063,
    AArch64_UADDLVv4i16v	= 2064,
    AArch64_UADDLVv4i32v	= 2065,
    AArch64_UADDLVv8i16v	= 2066,
    AArch64_UADDLVv8i8v	= 2067,
    AArch64_UADDLv16i8_v8i16	= 2068,
    AArch64_UADDLv2i32_v2i64	= 2069,
    AArch64_UADDLv4i16_v4i32	= 2070,
    AArch64_UADDLv4i32_v2i64	= 2071,
    AArch64_UADDLv8i16_v4i32	= 2072,
    AArch64_UADDLv8i8_v8i16	= 2073,
    AArch64_UADDWv16i8_v8i16	= 2074,
    AArch64_UADDWv2i32_v2i64	= 2075,
    AArch64_UADDWv4i16_v4i32	= 2076,
    AArch64_UADDWv4i32_v2i64	= 2077,
    AArch64_UADDWv8i16_v4i32	= 2078,
    AArch64_UADDWv8i8_v8i16	= 2079,
    AArch64_UBFMWri	= 2080,
    AArch64_UBFMXri	= 2081,
    AArch64_UCVTFSWDri	= 2082,
    AArch64_UCVTFSWSri	= 2083,
    AArch64_UCVTFSXDri	= 2084,
    AArch64_UCVTFSXSri	= 2085,
    AArch64_UCVTFUWDri	= 2086,
    AArch64_UCVTFUWSri	= 2087,
    AArch64_UCVTFUXDri	= 2088,
    AArch64_UCVTFUXSri	= 2089,
    AArch64_UCVTFd	= 2090,
    AArch64_UCVTFs	= 2091,
    AArch64_UCVTFv1i32	= 2092,
    AArch64_UCVTFv1i64	= 2093,
    AArch64_UCVTFv2f32	= 2094,
    AArch64_UCVTFv2f64	= 2095,
    AArch64_UCVTFv2i32_shift	= 2096,
    AArch64_UCVTFv2i64_shift	= 2097,
    AArch64_UCVTFv4f32	= 2098,
    AArch64_UCVTFv4i32_shift	= 2099,
    AArch64_UDIVWr	= 2100,
    AArch64_UDIVXr	= 2101,
    AArch64_UDIV_IntWr	= 2102,
    AArch64_UDIV_IntXr	= 2103,
    AArch64_UHADDv16i8	= 2104,
    AArch64_UHADDv2i32	= 2105,
    AArch64_UHADDv4i16	= 2106,
    AArch64_UHADDv4i32	= 2107,
    AArch64_UHADDv8i16	= 2108,
    AArch64_UHADDv8i8	= 2109,
    AArch64_UHSUBv16i8	= 2110,
    AArch64_UHSUBv2i32	= 2111,
    AArch64_UHSUBv4i16	= 2112,
    AArch64_UHSUBv4i32	= 2113,
    AArch64_UHSUBv8i16	= 2114,
    AArch64_UHSUBv8i8	= 2115,
    AArch64_UMADDLrrr	= 2116,
    AArch64_UMAXPv16i8	= 2117,
    AArch64_UMAXPv2i32	= 2118,
    AArch64_UMAXPv4i16	= 2119,
    AArch64_UMAXPv4i32	= 2120,
    AArch64_UMAXPv8i16	= 2121,
    AArch64_UMAXPv8i8	= 2122,
    AArch64_UMAXVv16i8v	= 2123,
    AArch64_UMAXVv4i16v	= 2124,
    AArch64_UMAXVv4i32v	= 2125,
    AArch64_UMAXVv8i16v	= 2126,
    AArch64_UMAXVv8i8v	= 2127,
    AArch64_UMAXv16i8	= 2128,
    AArch64_UMAXv2i32	= 2129,
    AArch64_UMAXv4i16	= 2130,
    AArch64_UMAXv4i32	= 2131,
    AArch64_UMAXv8i16	= 2132,
    AArch64_UMAXv8i8	= 2133,
    AArch64_UMINPv16i8	= 2134,
    AArch64_UMINPv2i32	= 2135,
    AArch64_UMINPv4i16	= 2136,
    AArch64_UMINPv4i32	= 2137,
    AArch64_UMINPv8i16	= 2138,
    AArch64_UMINPv8i8	= 2139,
    AArch64_UMINVv16i8v	= 2140,
    AArch64_UMINVv4i16v	= 2141,
    AArch64_UMINVv4i32v	= 2142,
    AArch64_UMINVv8i16v	= 2143,
    AArch64_UMINVv8i8v	= 2144,
    AArch64_UMINv16i8	= 2145,
    AArch64_UMINv2i32	= 2146,
    AArch64_UMINv4i16	= 2147,
    AArch64_UMINv4i32	= 2148,
    AArch64_UMINv8i16	= 2149,
    AArch64_UMINv8i8	= 2150,
    AArch64_UMLALv16i8_v8i16	= 2151,
    AArch64_UMLALv2i32_indexed	= 2152,
    AArch64_UMLALv2i32_v2i64	= 2153,
    AArch64_UMLALv4i16_indexed	= 2154,
    AArch64_UMLALv4i16_v4i32	= 2155,
    AArch64_UMLALv4i32_indexed	= 2156,
    AArch64_UMLALv4i32_v2i64	= 2157,
    AArch64_UMLALv8i16_indexed	= 2158,
    AArch64_UMLALv8i16_v4i32	= 2159,
    AArch64_UMLALv8i8_v8i16	= 2160,
    AArch64_UMLSLv16i8_v8i16	= 2161,
    AArch64_UMLSLv2i32_indexed	= 2162,
    AArch64_UMLSLv2i32_v2i64	= 2163,
    AArch64_UMLSLv4i16_indexed	= 2164,
    AArch64_UMLSLv4i16_v4i32	= 2165,
    AArch64_UMLSLv4i32_indexed	= 2166,
    AArch64_UMLSLv4i32_v2i64	= 2167,
    AArch64_UMLSLv8i16_indexed	= 2168,
    AArch64_UMLSLv8i16_v4i32	= 2169,
    AArch64_UMLSLv8i8_v8i16	= 2170,
    AArch64_UMOVvi16	= 2171,
    AArch64_UMOVvi32	= 2172,
    AArch64_UMOVvi64	= 2173,
    AArch64_UMOVvi8	= 2174,
    AArch64_UMSUBLrrr	= 2175,
    AArch64_UMULHrr	= 2176,
    AArch64_UMULLv16i8_v8i16	= 2177,
    AArch64_UMULLv2i32_indexed	= 2178,
    AArch64_UMULLv2i32_v2i64	= 2179,
    AArch64_UMULLv4i16_indexed	= 2180,
    AArch64_UMULLv4i16_v4i32	= 2181,
    AArch64_UMULLv4i32_indexed	= 2182,
    AArch64_UMULLv4i32_v2i64	= 2183,
    AArch64_UMULLv8i16_indexed	= 2184,
    AArch64_UMULLv8i16_v4i32	= 2185,
    AArch64_UMULLv8i8_v8i16	= 2186,
    AArch64_UQADDv16i8	= 2187,
    AArch64_UQADDv1i16	= 2188,
    AArch64_UQADDv1i32	= 2189,
    AArch64_UQADDv1i64	= 2190,
    AArch64_UQADDv1i8	= 2191,
    AArch64_UQADDv2i32	= 2192,
    AArch64_UQADDv2i64	= 2193,
    AArch64_UQADDv4i16	= 2194,
    AArch64_UQADDv4i32	= 2195,
    AArch64_UQADDv8i16	= 2196,
    AArch64_UQADDv8i8	= 2197,
    AArch64_UQRSHLv16i8	= 2198,
    AArch64_UQRSHLv1i16	= 2199,
    AArch64_UQRSHLv1i32	= 2200,
    AArch64_UQRSHLv1i64	= 2201,
    AArch64_UQRSHLv1i8	= 2202,
    AArch64_UQRSHLv2i32	= 2203,
    AArch64_UQRSHLv2i64	= 2204,
    AArch64_UQRSHLv4i16	= 2205,
    AArch64_UQRSHLv4i32	= 2206,
    AArch64_UQRSHLv8i16	= 2207,
    AArch64_UQRSHLv8i8	= 2208,
    AArch64_UQRSHRNb	= 2209,
    AArch64_UQRSHRNh	= 2210,
    AArch64_UQRSHRNs	= 2211,
    AArch64_UQRSHRNv16i8_shift	= 2212,
    AArch64_UQRSHRNv2i32_shift	= 2213,
    AArch64_UQRSHRNv4i16_shift	= 2214,
    AArch64_UQRSHRNv4i32_shift	= 2215,
    AArch64_UQRSHRNv8i16_shift	= 2216,
    AArch64_UQRSHRNv8i8_shift	= 2217,
    AArch64_UQSHLb	= 2218,
    AArch64_UQSHLd	= 2219,
    AArch64_UQSHLh	= 2220,
    AArch64_UQSHLs	= 2221,
    AArch64_UQSHLv16i8	= 2222,
    AArch64_UQSHLv16i8_shift	= 2223,
    AArch64_UQSHLv1i16	= 2224,
    AArch64_UQSHLv1i32	= 2225,
    AArch64_UQSHLv1i64	= 2226,
    AArch64_UQSHLv1i8	= 2227,
    AArch64_UQSHLv2i32	= 2228,
    AArch64_UQSHLv2i32_shift	= 2229,
    AArch64_UQSHLv2i64	= 2230,
    AArch64_UQSHLv2i64_shift	= 2231,
    AArch64_UQSHLv4i16	= 2232,
    AArch64_UQSHLv4i16_shift	= 2233,
    AArch64_UQSHLv4i32	= 2234,
    AArch64_UQSHLv4i32_shift	= 2235,
    AArch64_UQSHLv8i16	= 2236,
    AArch64_UQSHLv8i16_shift	= 2237,
    AArch64_UQSHLv8i8	= 2238,
    AArch64_UQSHLv8i8_shift	= 2239,
    AArch64_UQSHRNb	= 2240,
    AArch64_UQSHRNh	= 2241,
    AArch64_UQSHRNs	= 2242,
    AArch64_UQSHRNv16i8_shift	= 2243,
    AArch64_UQSHRNv2i32_shift	= 2244,
    AArch64_UQSHRNv4i16_shift	= 2245,
    AArch64_UQSHRNv4i32_shift	= 2246,
    AArch64_UQSHRNv8i16_shift	= 2247,
    AArch64_UQSHRNv8i8_shift	= 2248,
    AArch64_UQSUBv16i8	= 2249,
    AArch64_UQSUBv1i16	= 2250,
    AArch64_UQSUBv1i32	= 2251,
    AArch64_UQSUBv1i64	= 2252,
    AArch64_UQSUBv1i8	= 2253,
    AArch64_UQSUBv2i32	= 2254,
    AArch64_UQSUBv2i64	= 2255,
    AArch64_UQSUBv4i16	= 2256,
    AArch64_UQSUBv4i32	= 2257,
    AArch64_UQSUBv8i16	= 2258,
    AArch64_UQSUBv8i8	= 2259,
    AArch64_UQXTNv16i8	= 2260,
    AArch64_UQXTNv1i16	= 2261,
    AArch64_UQXTNv1i32	= 2262,
    AArch64_UQXTNv1i8	= 2263,
    AArch64_UQXTNv2i32	= 2264,
    AArch64_UQXTNv4i16	= 2265,
    AArch64_UQXTNv4i32	= 2266,
    AArch64_UQXTNv8i16	= 2267,
    AArch64_UQXTNv8i8	= 2268,
    AArch64_URECPEv2i32	= 2269,
    AArch64_URECPEv4i32	= 2270,
    AArch64_URHADDv16i8	= 2271,
    AArch64_URHADDv2i32	= 2272,
    AArch64_URHADDv4i16	= 2273,
    AArch64_URHADDv4i32	= 2274,
    AArch64_URHADDv8i16	= 2275,
    AArch64_URHADDv8i8	= 2276,
    AArch64_URSHLv16i8	= 2277,
    AArch64_URSHLv1i64	= 2278,
    AArch64_URSHLv2i32	= 2279,
    AArch64_URSHLv2i64	= 2280,
    AArch64_URSHLv4i16	= 2281,
    AArch64_URSHLv4i32	= 2282,
    AArch64_URSHLv8i16	= 2283,
    AArch64_URSHLv8i8	= 2284,
    AArch64_URSHRd	= 2285,
    AArch64_URSHRv16i8_shift	= 2286,
    AArch64_URSHRv2i32_shift	= 2287,
    AArch64_URSHRv2i64_shift	= 2288,
    AArch64_URSHRv4i16_shift	= 2289,
    AArch64_URSHRv4i32_shift	= 2290,
    AArch64_URSHRv8i16_shift	= 2291,
    AArch64_URSHRv8i8_shift	= 2292,
    AArch64_URSQRTEv2i32	= 2293,
    AArch64_URSQRTEv4i32	= 2294,
    AArch64_URSRAd	= 2295,
    AArch64_URSRAv16i8_shift	= 2296,
    AArch64_URSRAv2i32_shift	= 2297,
    AArch64_URSRAv2i64_shift	= 2298,
    AArch64_URSRAv4i16_shift	= 2299,
    AArch64_URSRAv4i32_shift	= 2300,
    AArch64_URSRAv8i16_shift	= 2301,
    AArch64_URSRAv8i8_shift	= 2302,
    AArch64_USHLLv16i8_shift	= 2303,
    AArch64_USHLLv2i32_shift	= 2304,
    AArch64_USHLLv4i16_shift	= 2305,
    AArch64_USHLLv4i32_shift	= 2306,
    AArch64_USHLLv8i16_shift	= 2307,
    AArch64_USHLLv8i8_shift	= 2308,
    AArch64_USHLv16i8	= 2309,
    AArch64_USHLv1i64	= 2310,
    AArch64_USHLv2i32	= 2311,
    AArch64_USHLv2i64	= 2312,
    AArch64_USHLv4i16	= 2313,
    AArch64_USHLv4i32	= 2314,
    AArch64_USHLv8i16	= 2315,
    AArch64_USHLv8i8	= 2316,
    AArch64_USHRd	= 2317,
    AArch64_USHRv16i8_shift	= 2318,
    AArch64_USHRv2i32_shift	= 2319,
    AArch64_USHRv2i64_shift	= 2320,
    AArch64_USHRv4i16_shift	= 2321,
    AArch64_USHRv4i32_shift	= 2322,
    AArch64_USHRv8i16_shift	= 2323,
    AArch64_USHRv8i8_shift	= 2324,
    AArch64_USQADDv16i8	= 2325,
    AArch64_USQADDv1i16	= 2326,
    AArch64_USQADDv1i32	= 2327,
    AArch64_USQADDv1i64	= 2328,
    AArch64_USQADDv1i8	= 2329,
    AArch64_USQADDv2i32	= 2330,
    AArch64_USQADDv2i64	= 2331,
    AArch64_USQADDv4i16	= 2332,
    AArch64_USQADDv4i32	= 2333,
    AArch64_USQADDv8i16	= 2334,
    AArch64_USQADDv8i8	= 2335,
    AArch64_USRAd	= 2336,
    AArch64_USRAv16i8_shift	= 2337,
    AArch64_USRAv2i32_shift	= 2338,
    AArch64_USRAv2i64_shift	= 2339,
    AArch64_USRAv4i16_shift	= 2340,
    AArch64_USRAv4i32_shift	= 2341,
    AArch64_USRAv8i16_shift	= 2342,
    AArch64_USRAv8i8_shift	= 2343,
    AArch64_USUBLv16i8_v8i16	= 2344,
    AArch64_USUBLv2i32_v2i64	= 2345,
    AArch64_USUBLv4i16_v4i32	= 2346,
    AArch64_USUBLv4i32_v2i64	= 2347,
    AArch64_USUBLv8i16_v4i32	= 2348,
    AArch64_USUBLv8i8_v8i16	= 2349,
    AArch64_USUBWv16i8_v8i16	= 2350,
    AArch64_USUBWv2i32_v2i64	= 2351,
    AArch64_USUBWv4i16_v4i32	= 2352,
    AArch64_USUBWv4i32_v2i64	= 2353,
    AArch64_USUBWv8i16_v4i32	= 2354,
    AArch64_USUBWv8i8_v8i16	= 2355,
    AArch64_UZP1v16i8	= 2356,
    AArch64_UZP1v2i32	= 2357,
    AArch64_UZP1v2i64	= 2358,
    AArch64_UZP1v4i16	= 2359,
    AArch64_UZP1v4i32	= 2360,
    AArch64_UZP1v8i16	= 2361,
    AArch64_UZP1v8i8	= 2362,
    AArch64_UZP2v16i8	= 2363,
    AArch64_UZP2v2i32	= 2364,
    AArch64_UZP2v2i64	= 2365,
    AArch64_UZP2v4i16	= 2366,
    AArch64_UZP2v4i32	= 2367,
    AArch64_UZP2v8i16	= 2368,
    AArch64_UZP2v8i8	= 2369,
    AArch64_XTNv16i8	= 2370,
    AArch64_XTNv2i32	= 2371,
    AArch64_XTNv4i16	= 2372,
    AArch64_XTNv4i32	= 2373,
    AArch64_XTNv8i16	= 2374,
    AArch64_XTNv8i8	= 2375,
    AArch64_ZIP1v16i8	= 2376,
    AArch64_ZIP1v2i32	= 2377,
    AArch64_ZIP1v2i64	= 2378,
    AArch64_ZIP1v4i16	= 2379,
    AArch64_ZIP1v4i32	= 2380,
    AArch64_ZIP1v8i16	= 2381,
    AArch64_ZIP1v8i8	= 2382,
    AArch64_ZIP2v16i8	= 2383,
    AArch64_ZIP2v2i32	= 2384,
    AArch64_ZIP2v2i64	= 2385,
    AArch64_ZIP2v4i16	= 2386,
    AArch64_ZIP2v4i32	= 2387,
    AArch64_ZIP2v8i16	= 2388,
    AArch64_ZIP2v8i8	= 2389,
    AArch64_INSTRUCTION_LIST_END = 2390
};

#endif // GET_INSTRINFO_ENUM

