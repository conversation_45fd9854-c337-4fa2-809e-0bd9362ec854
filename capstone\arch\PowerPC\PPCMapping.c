/* Capstone Disassembly Engine */
/* By <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2015 */

#ifdef CAPSTONE_HAS_POWERPC

#include <stdio.h>	// debug
#include <string.h>

#include "../../utils.h"

#include "PPCMapping.h"

#define GET_INSTRINFO_ENUM
#include "PPCGenInstrInfo.inc"

#ifndef CAPSTONE_DIET
static const name_map reg_name_maps[] = {
	{ PPC_REG_INVALID, NULL },

	{ PPC_REG_CARRY, "ca" },
	{ PPC_REG_CR0, "cr0" },
	{ PPC_REG_CR1, "cr1" },
	{ PPC_REG_CR2, "cr2" },
	{ PPC_REG_CR3, "cr3" },
	{ PPC_REG_CR4, "cr4" },
	{ PPC_REG_CR5, "cr5" },
	{ PPC_REG_CR6, "cr6" },
	{ PPC_REG_CR7, "cr7" },
	{ PPC_REG_CTR, "ctr" },
	{ PPC_REG_F0, "f0" },
	{ PPC_REG_F1, "f1" },
	{ PPC_REG_F2, "f2" },
	{ PPC_REG_F3, "f3" },
	{ PPC_REG_F4, "f4" },
	{ PPC_REG_F5, "f5" },
	{ PPC_REG_F6, "f6" },
	{ PPC_REG_F7, "f7" },
	{ PPC_REG_F8, "f8" },
	{ PPC_REG_F9, "f9" },
	{ PPC_REG_F10, "f10" },
	{ PPC_REG_F11, "f11" },
	{ PPC_REG_F12, "f12" },
	{ PPC_REG_F13, "f13" },
	{ PPC_REG_F14, "f14" },
	{ PPC_REG_F15, "f15" },
	{ PPC_REG_F16, "f16" },
	{ PPC_REG_F17, "f17" },
	{ PPC_REG_F18, "f18" },
	{ PPC_REG_F19, "f19" },
	{ PPC_REG_F20, "f20" },
	{ PPC_REG_F21, "f21" },
	{ PPC_REG_F22, "f22" },
	{ PPC_REG_F23, "f23" },
	{ PPC_REG_F24, "f24" },
	{ PPC_REG_F25, "f25" },
	{ PPC_REG_F26, "f26" },
	{ PPC_REG_F27, "f27" },
	{ PPC_REG_F28, "f28" },
	{ PPC_REG_F29, "f29" },
	{ PPC_REG_F30, "f30" },
	{ PPC_REG_F31, "f31" },
	{ PPC_REG_LR, "lr" },
	{ PPC_REG_R0, "r0" },
	{ PPC_REG_R1, "r1" },
	{ PPC_REG_R2, "r2" },
	{ PPC_REG_R3, "r3" },
	{ PPC_REG_R4, "r4" },
	{ PPC_REG_R5, "r5" },
	{ PPC_REG_R6, "r6" },
	{ PPC_REG_R7, "r7" },
	{ PPC_REG_R8, "r8" },
	{ PPC_REG_R9, "r9" },
	{ PPC_REG_R10, "r10" },
	{ PPC_REG_R11, "r11" },
	{ PPC_REG_R12, "r12" },
	{ PPC_REG_R13, "r13" },
	{ PPC_REG_R14, "r14" },
	{ PPC_REG_R15, "r15" },
	{ PPC_REG_R16, "r16" },
	{ PPC_REG_R17, "r17" },
	{ PPC_REG_R18, "r18" },
	{ PPC_REG_R19, "r19" },
	{ PPC_REG_R20, "r20" },
	{ PPC_REG_R21, "r21" },
	{ PPC_REG_R22, "r22" },
	{ PPC_REG_R23, "r23" },
	{ PPC_REG_R24, "r24" },
	{ PPC_REG_R25, "r25" },
	{ PPC_REG_R26, "r26" },
	{ PPC_REG_R27, "r27" },
	{ PPC_REG_R28, "r28" },
	{ PPC_REG_R29, "r29" },
	{ PPC_REG_R30, "r30" },
	{ PPC_REG_R31, "r31" },
	{ PPC_REG_V0, "v0" },
	{ PPC_REG_V1, "v1" },
	{ PPC_REG_V2, "v2" },
	{ PPC_REG_V3, "v3" },
	{ PPC_REG_V4, "v4" },
	{ PPC_REG_V5, "v5" },
	{ PPC_REG_V6, "v6" },
	{ PPC_REG_V7, "v7" },
	{ PPC_REG_V8, "v8" },
	{ PPC_REG_V9, "v9" },
	{ PPC_REG_V10, "v10" },
	{ PPC_REG_V11, "v11" },
	{ PPC_REG_V12, "v12" },
	{ PPC_REG_V13, "v13" },
	{ PPC_REG_V14, "v14" },
	{ PPC_REG_V15, "v15" },
	{ PPC_REG_V16, "v16" },
	{ PPC_REG_V17, "v17" },
	{ PPC_REG_V18, "v18" },
	{ PPC_REG_V19, "v19" },
	{ PPC_REG_V20, "v20" },
	{ PPC_REG_V21, "v21" },
	{ PPC_REG_V22, "v22" },
	{ PPC_REG_V23, "v23" },
	{ PPC_REG_V24, "v24" },
	{ PPC_REG_V25, "v25" },
	{ PPC_REG_V26, "v26" },
	{ PPC_REG_V27, "v27" },
	{ PPC_REG_V28, "v28" },
	{ PPC_REG_V29, "v29" },
	{ PPC_REG_V30, "v30" },
	{ PPC_REG_V31, "v31" },
	{ PPC_REG_VRSAVE, "vrsave" },
	{ PPC_REG_VS0, "vs0"},
	{ PPC_REG_VS1, "vs1"},
	{ PPC_REG_VS2, "vs2"},
	{ PPC_REG_VS3, "vs3"},
	{ PPC_REG_VS4, "vs4"},
	{ PPC_REG_VS5, "vs5"},
	{ PPC_REG_VS6, "vs6"},
	{ PPC_REG_VS7, "vs7"},
	{ PPC_REG_VS8, "vs8"},
	{ PPC_REG_VS9, "vs9"},
	{ PPC_REG_VS10, "vs10"},
	{ PPC_REG_VS11, "vs11"},
	{ PPC_REG_VS12, "vs12"},
	{ PPC_REG_VS13, "vs13"},
	{ PPC_REG_VS14, "vs14"},
	{ PPC_REG_VS15, "vs15"},
	{ PPC_REG_VS16, "vs16"},
	{ PPC_REG_VS17, "vs17"},
	{ PPC_REG_VS18, "vs18"},
	{ PPC_REG_VS19, "vs19"},
	{ PPC_REG_VS20, "vs20"},
	{ PPC_REG_VS21, "vs21"},
	{ PPC_REG_VS22, "vs22"},
	{ PPC_REG_VS23, "vs23"},
	{ PPC_REG_VS24, "vs24"},
	{ PPC_REG_VS25, "vs25"},
	{ PPC_REG_VS26, "vs26"},
	{ PPC_REG_VS27, "vs27"},
	{ PPC_REG_VS28, "vs28"},
	{ PPC_REG_VS29, "vs29"},
	{ PPC_REG_VS30, "vs30"},
	{ PPC_REG_VS31, "vs31"},
	{ PPC_REG_VS32, "vs32"},
	{ PPC_REG_VS33, "vs33"},
	{ PPC_REG_VS34, "vs34"},
	{ PPC_REG_VS35, "vs35"},
	{ PPC_REG_VS36, "vs36"},
	{ PPC_REG_VS37, "vs37"},
	{ PPC_REG_VS38, "vs38"},
	{ PPC_REG_VS39, "vs39"},
	{ PPC_REG_VS40, "vs40"},
	{ PPC_REG_VS41, "vs41"},
	{ PPC_REG_VS42, "vs42"},
	{ PPC_REG_VS43, "vs43"},
	{ PPC_REG_VS44, "vs44"},
	{ PPC_REG_VS45, "vs45"},
	{ PPC_REG_VS46, "vs46"},
	{ PPC_REG_VS47, "vs47"},
	{ PPC_REG_VS48, "vs48"},
	{ PPC_REG_VS49, "vs49"},
	{ PPC_REG_VS50, "vs50"},
	{ PPC_REG_VS51, "vs51"},
	{ PPC_REG_VS52, "vs52"},
	{ PPC_REG_VS53, "vs53"},
	{ PPC_REG_VS54, "vs54"},
	{ PPC_REG_VS55, "vs55"},
	{ PPC_REG_VS56, "vs56"},
	{ PPC_REG_VS57, "vs57"},
	{ PPC_REG_VS58, "vs58"},
	{ PPC_REG_VS59, "vs59"},
	{ PPC_REG_VS60, "vs60"},
	{ PPC_REG_VS61, "vs61"},
	{ PPC_REG_VS62, "vs62"},
	{ PPC_REG_VS63, "vs63"},
	{ PPC_REG_Q0, "q0" },
	{ PPC_REG_Q1, "q1" },
	{ PPC_REG_Q2, "q2" },
	{ PPC_REG_Q3, "q3" },
	{ PPC_REG_Q4, "q4" },
	{ PPC_REG_Q5, "q5" },
	{ PPC_REG_Q6, "q6" },
	{ PPC_REG_Q7, "q7" },
	{ PPC_REG_Q8, "q8" },
	{ PPC_REG_Q9, "q9" },
	{ PPC_REG_Q10, "q10" },
	{ PPC_REG_Q11, "q11" },
	{ PPC_REG_Q12, "q12" },
	{ PPC_REG_Q13, "q13" },
	{ PPC_REG_Q14, "q14" },
	{ PPC_REG_Q15, "q15" },
	{ PPC_REG_Q16, "q16" },
	{ PPC_REG_Q17, "q17" },
	{ PPC_REG_Q18, "q18" },
	{ PPC_REG_Q19, "q19" },
	{ PPC_REG_Q20, "q20" },
	{ PPC_REG_Q21, "q21" },
	{ PPC_REG_Q22, "q22" },
	{ PPC_REG_Q23, "q23" },
	{ PPC_REG_Q24, "q24" },
	{ PPC_REG_Q25, "q25" },
	{ PPC_REG_Q26, "q26" },
	{ PPC_REG_Q27, "q27" },
	{ PPC_REG_Q28, "q28" },
	{ PPC_REG_Q29, "q29" },
	{ PPC_REG_Q30, "q30" },
	{ PPC_REG_Q31, "q31" },

	// extras
	{ PPC_REG_RM, "rm" },
	{ PPC_REG_CTR8, "ctr8" },
	{ PPC_REG_LR8, "lr8" },
	{ PPC_REG_CR1EQ, "cr1eq" },
	{ PPC_REG_X2, "x2" },
};
#endif

const char *PPC_reg_name(csh handle, unsigned int reg)
{
#ifndef CAPSTONE_DIET
	if (reg >= ARR_SIZE(reg_name_maps))
		return NULL;

	return reg_name_maps[reg].name;
#else
	return NULL;
#endif
}

static const insn_map insns[] = {
	// dummy item
	{
		0, 0,
#ifndef CAPSTONE_DIET
		{ 0 }, { 0 }, { 0 }, 0, 0
#endif
	},

#include "PPCMappingInsn.inc"
};

// given internal insn id, return public instruction info
void PPC_get_insn_id(cs_struct *h, cs_insn *insn, unsigned int id)
{
	int i;

	i = insn_find(insns, ARR_SIZE(insns), id, &h->insn_cache);
	if (i != 0) {
		insn->id = insns[i].mapid;

		if (h->detail) {
#ifndef CAPSTONE_DIET
			cs_struct handle;
			handle.detail = h->detail;

			memcpy(insn->detail->regs_read, insns[i].regs_use, sizeof(insns[i].regs_use));
			insn->detail->regs_read_count = (uint8_t)count_positive(insns[i].regs_use);

			memcpy(insn->detail->regs_write, insns[i].regs_mod, sizeof(insns[i].regs_mod));
			insn->detail->regs_write_count = (uint8_t)count_positive(insns[i].regs_mod);

			memcpy(insn->detail->groups, insns[i].groups, sizeof(insns[i].groups));
			insn->detail->groups_count = (uint8_t)count_positive8(insns[i].groups);

			if (insns[i].branch || insns[i].indirect_branch) {
				// this insn also belongs to JUMP group. add JUMP group
				insn->detail->groups[insn->detail->groups_count] = PPC_GRP_JUMP;
				insn->detail->groups_count++;
			}

			insn->detail->ppc.update_cr0 = cs_reg_write((csh)&handle, insn, PPC_REG_CR0);
#endif
		}
	}
}

#ifndef CAPSTONE_DIET
static const name_map insn_name_maps[] = {
	{ PPC_INS_INVALID, NULL },

	{ PPC_INS_ADD, "add" },
	{ PPC_INS_ADDC, "addc" },
	{ PPC_INS_ADDE, "adde" },
	{ PPC_INS_ADDI, "addi" },
	{ PPC_INS_ADDIC, "addic" },
	{ PPC_INS_ADDIS, "addis" },
	{ PPC_INS_ADDME, "addme" },
	{ PPC_INS_ADDZE, "addze" },
	{ PPC_INS_AND, "and" },
	{ PPC_INS_ANDC, "andc" },
	{ PPC_INS_ANDIS, "andis" },
	{ PPC_INS_ANDI, "andi" },
	{ PPC_INS_ATTN, "attn" },
	{ PPC_INS_B, "b" },
	{ PPC_INS_BA, "ba" },
	{ PPC_INS_BC, "bc" },
	{ PPC_INS_BCCTR, "bcctr" },
	{ PPC_INS_BCCTRL, "bcctrl" },
	{ PPC_INS_BCL, "bcl" },
	{ PPC_INS_BCLR, "bclr" },
	{ PPC_INS_BCLRL, "bclrl" },
	{ PPC_INS_BCTR, "bctr" },
	{ PPC_INS_BCTRL, "bctrl" },
	{ PPC_INS_BCT, "bct" },
	{ PPC_INS_BDNZ, "bdnz" },
	{ PPC_INS_BDNZA, "bdnza" },
	{ PPC_INS_BDNZL, "bdnzl" },
	{ PPC_INS_BDNZLA, "bdnzla" },
	{ PPC_INS_BDNZLR, "bdnzlr" },
	{ PPC_INS_BDNZLRL, "bdnzlrl" },
	{ PPC_INS_BDZ, "bdz" },
	{ PPC_INS_BDZA, "bdza" },
	{ PPC_INS_BDZL, "bdzl" },
	{ PPC_INS_BDZLA, "bdzla" },
	{ PPC_INS_BDZLR, "bdzlr" },
	{ PPC_INS_BDZLRL, "bdzlrl" },
	{ PPC_INS_BL, "bl" },
	{ PPC_INS_BLA, "bla" },
	{ PPC_INS_BLR, "blr" },
	{ PPC_INS_BLRL, "blrl" },
	{ PPC_INS_BRINC, "brinc" },
	{ PPC_INS_CMPB, "cmpb" },
	{ PPC_INS_CMPD, "cmpd" },
	{ PPC_INS_CMPDI, "cmpdi" },
	{ PPC_INS_CMPLD, "cmpld" },
	{ PPC_INS_CMPLDI, "cmpldi" },
	{ PPC_INS_CMPLW, "cmplw" },
	{ PPC_INS_CMPLWI, "cmplwi" },
	{ PPC_INS_CMPW, "cmpw" },
	{ PPC_INS_CMPWI, "cmpwi" },
	{ PPC_INS_CNTLZD, "cntlzd" },
	{ PPC_INS_CNTLZW, "cntlzw" },
	{ PPC_INS_CREQV, "creqv" },
	{ PPC_INS_CRXOR, "crxor" },
	{ PPC_INS_CRAND, "crand" },
	{ PPC_INS_CRANDC, "crandc" },
	{ PPC_INS_CRNAND, "crnand" },
	{ PPC_INS_CRNOR, "crnor" },
	{ PPC_INS_CROR, "cror" },
	{ PPC_INS_CRORC, "crorc" },
	{ PPC_INS_DCBA, "dcba" },
	{ PPC_INS_DCBF, "dcbf" },
	{ PPC_INS_DCBI, "dcbi" },
	{ PPC_INS_DCBST, "dcbst" },
	{ PPC_INS_DCBT, "dcbt" },
	{ PPC_INS_DCBTST, "dcbtst" },
	{ PPC_INS_DCBZ, "dcbz" },
	{ PPC_INS_DCBZL, "dcbzl" },
	{ PPC_INS_DCCCI, "dccci" },
	{ PPC_INS_DIVD, "divd" },
	{ PPC_INS_DIVDU, "divdu" },
	{ PPC_INS_DIVW, "divw" },
	{ PPC_INS_DIVWU, "divwu" },
	{ PPC_INS_DSS, "dss" },
	{ PPC_INS_DSSALL, "dssall" },
	{ PPC_INS_DST, "dst" },
	{ PPC_INS_DSTST, "dstst" },
	{ PPC_INS_DSTSTT, "dststt" },
	{ PPC_INS_DSTT, "dstt" },
	{ PPC_INS_EQV, "eqv" },
	{ PPC_INS_EVABS, "evabs" },
	{ PPC_INS_EVADDIW, "evaddiw" },
	{ PPC_INS_EVADDSMIAAW, "evaddsmiaaw" },
	{ PPC_INS_EVADDSSIAAW, "evaddssiaaw" },
	{ PPC_INS_EVADDUMIAAW, "evaddumiaaw" },
	{ PPC_INS_EVADDUSIAAW, "evaddusiaaw" },
	{ PPC_INS_EVADDW, "evaddw" },
	{ PPC_INS_EVAND, "evand" },
	{ PPC_INS_EVANDC, "evandc" },
	{ PPC_INS_EVCMPEQ, "evcmpeq" },
	{ PPC_INS_EVCMPGTS, "evcmpgts" },
	{ PPC_INS_EVCMPGTU, "evcmpgtu" },
	{ PPC_INS_EVCMPLTS, "evcmplts" },
	{ PPC_INS_EVCMPLTU, "evcmpltu" },
	{ PPC_INS_EVCNTLSW, "evcntlsw" },
	{ PPC_INS_EVCNTLZW, "evcntlzw" },
	{ PPC_INS_EVDIVWS, "evdivws" },
	{ PPC_INS_EVDIVWU, "evdivwu" },
	{ PPC_INS_EVEQV, "eveqv" },
	{ PPC_INS_EVEXTSB, "evextsb" },
	{ PPC_INS_EVEXTSH, "evextsh" },
	{ PPC_INS_EVLDD, "evldd" },
	{ PPC_INS_EVLDDX, "evlddx" },
	{ PPC_INS_EVLDH, "evldh" },
	{ PPC_INS_EVLDHX, "evldhx" },
	{ PPC_INS_EVLDW, "evldw" },
	{ PPC_INS_EVLDWX, "evldwx" },
	{ PPC_INS_EVLHHESPLAT, "evlhhesplat" },
	{ PPC_INS_EVLHHESPLATX, "evlhhesplatx" },
	{ PPC_INS_EVLHHOSSPLAT, "evlhhossplat" },
	{ PPC_INS_EVLHHOSSPLATX, "evlhhossplatx" },
	{ PPC_INS_EVLHHOUSPLAT, "evlhhousplat" },
	{ PPC_INS_EVLHHOUSPLATX, "evlhhousplatx" },
	{ PPC_INS_EVLWHE, "evlwhe" },
	{ PPC_INS_EVLWHEX, "evlwhex" },
	{ PPC_INS_EVLWHOS, "evlwhos" },
	{ PPC_INS_EVLWHOSX, "evlwhosx" },
	{ PPC_INS_EVLWHOU, "evlwhou" },
	{ PPC_INS_EVLWHOUX, "evlwhoux" },
	{ PPC_INS_EVLWHSPLAT, "evlwhsplat" },
	{ PPC_INS_EVLWHSPLATX, "evlwhsplatx" },
	{ PPC_INS_EVLWWSPLAT, "evlwwsplat" },
	{ PPC_INS_EVLWWSPLATX, "evlwwsplatx" },
	{ PPC_INS_EVMERGEHI, "evmergehi" },
	{ PPC_INS_EVMERGEHILO, "evmergehilo" },
	{ PPC_INS_EVMERGELO, "evmergelo" },
	{ PPC_INS_EVMERGELOHI, "evmergelohi" },
	{ PPC_INS_EVMHEGSMFAA, "evmhegsmfaa" },
	{ PPC_INS_EVMHEGSMFAN, "evmhegsmfan" },
	{ PPC_INS_EVMHEGSMIAA, "evmhegsmiaa" },
	{ PPC_INS_EVMHEGSMIAN, "evmhegsmian" },
	{ PPC_INS_EVMHEGUMIAA, "evmhegumiaa" },
	{ PPC_INS_EVMHEGUMIAN, "evmhegumian" },
	{ PPC_INS_EVMHESMF, "evmhesmf" },
	{ PPC_INS_EVMHESMFA, "evmhesmfa" },
	{ PPC_INS_EVMHESMFAAW, "evmhesmfaaw" },
	{ PPC_INS_EVMHESMFANW, "evmhesmfanw" },
	{ PPC_INS_EVMHESMI, "evmhesmi" },
	{ PPC_INS_EVMHESMIA, "evmhesmia" },
	{ PPC_INS_EVMHESMIAAW, "evmhesmiaaw" },
	{ PPC_INS_EVMHESMIANW, "evmhesmianw" },
	{ PPC_INS_EVMHESSF, "evmhessf" },
	{ PPC_INS_EVMHESSFA, "evmhessfa" },
	{ PPC_INS_EVMHESSFAAW, "evmhessfaaw" },
	{ PPC_INS_EVMHESSFANW, "evmhessfanw" },
	{ PPC_INS_EVMHESSIAAW, "evmhessiaaw" },
	{ PPC_INS_EVMHESSIANW, "evmhessianw" },
	{ PPC_INS_EVMHEUMI, "evmheumi" },
	{ PPC_INS_EVMHEUMIA, "evmheumia" },
	{ PPC_INS_EVMHEUMIAAW, "evmheumiaaw" },
	{ PPC_INS_EVMHEUMIANW, "evmheumianw" },
	{ PPC_INS_EVMHEUSIAAW, "evmheusiaaw" },
	{ PPC_INS_EVMHEUSIANW, "evmheusianw" },
	{ PPC_INS_EVMHOGSMFAA, "evmhogsmfaa" },
	{ PPC_INS_EVMHOGSMFAN, "evmhogsmfan" },
	{ PPC_INS_EVMHOGSMIAA, "evmhogsmiaa" },
	{ PPC_INS_EVMHOGSMIAN, "evmhogsmian" },
	{ PPC_INS_EVMHOGUMIAA, "evmhogumiaa" },
	{ PPC_INS_EVMHOGUMIAN, "evmhogumian" },
	{ PPC_INS_EVMHOSMF, "evmhosmf" },
	{ PPC_INS_EVMHOSMFA, "evmhosmfa" },
	{ PPC_INS_EVMHOSMFAAW, "evmhosmfaaw" },
	{ PPC_INS_EVMHOSMFANW, "evmhosmfanw" },
	{ PPC_INS_EVMHOSMI, "evmhosmi" },
	{ PPC_INS_EVMHOSMIA, "evmhosmia" },
	{ PPC_INS_EVMHOSMIAAW, "evmhosmiaaw" },
	{ PPC_INS_EVMHOSMIANW, "evmhosmianw" },
	{ PPC_INS_EVMHOSSF, "evmhossf" },
	{ PPC_INS_EVMHOSSFA, "evmhossfa" },
	{ PPC_INS_EVMHOSSFAAW, "evmhossfaaw" },
	{ PPC_INS_EVMHOSSFANW, "evmhossfanw" },
	{ PPC_INS_EVMHOSSIAAW, "evmhossiaaw" },
	{ PPC_INS_EVMHOSSIANW, "evmhossianw" },
	{ PPC_INS_EVMHOUMI, "evmhoumi" },
	{ PPC_INS_EVMHOUMIA, "evmhoumia" },
	{ PPC_INS_EVMHOUMIAAW, "evmhoumiaaw" },
	{ PPC_INS_EVMHOUMIANW, "evmhoumianw" },
	{ PPC_INS_EVMHOUSIAAW, "evmhousiaaw" },
	{ PPC_INS_EVMHOUSIANW, "evmhousianw" },
	{ PPC_INS_EVMRA, "evmra" },
	{ PPC_INS_EVMWHSMF, "evmwhsmf" },
	{ PPC_INS_EVMWHSMFA, "evmwhsmfa" },
	{ PPC_INS_EVMWHSMI, "evmwhsmi" },
	{ PPC_INS_EVMWHSMIA, "evmwhsmia" },
	{ PPC_INS_EVMWHSSF, "evmwhssf" },
	{ PPC_INS_EVMWHSSFA, "evmwhssfa" },
	{ PPC_INS_EVMWHUMI, "evmwhumi" },
	{ PPC_INS_EVMWHUMIA, "evmwhumia" },
	{ PPC_INS_EVMWLSMIAAW, "evmwlsmiaaw" },
	{ PPC_INS_EVMWLSMIANW, "evmwlsmianw" },
	{ PPC_INS_EVMWLSSIAAW, "evmwlssiaaw" },
	{ PPC_INS_EVMWLSSIANW, "evmwlssianw" },
	{ PPC_INS_EVMWLUMI, "evmwlumi" },
	{ PPC_INS_EVMWLUMIA, "evmwlumia" },
	{ PPC_INS_EVMWLUMIAAW, "evmwlumiaaw" },
	{ PPC_INS_EVMWLUMIANW, "evmwlumianw" },
	{ PPC_INS_EVMWLUSIAAW, "evmwlusiaaw" },
	{ PPC_INS_EVMWLUSIANW, "evmwlusianw" },
	{ PPC_INS_EVMWSMF, "evmwsmf" },
	{ PPC_INS_EVMWSMFA, "evmwsmfa" },
	{ PPC_INS_EVMWSMFAA, "evmwsmfaa" },
	{ PPC_INS_EVMWSMFAN, "evmwsmfan" },
	{ PPC_INS_EVMWSMI, "evmwsmi" },
	{ PPC_INS_EVMWSMIA, "evmwsmia" },
	{ PPC_INS_EVMWSMIAA, "evmwsmiaa" },
	{ PPC_INS_EVMWSMIAN, "evmwsmian" },
	{ PPC_INS_EVMWSSF, "evmwssf" },
	{ PPC_INS_EVMWSSFA, "evmwssfa" },
	{ PPC_INS_EVMWSSFAA, "evmwssfaa" },
	{ PPC_INS_EVMWSSFAN, "evmwssfan" },
	{ PPC_INS_EVMWUMI, "evmwumi" },
	{ PPC_INS_EVMWUMIA, "evmwumia" },
	{ PPC_INS_EVMWUMIAA, "evmwumiaa" },
	{ PPC_INS_EVMWUMIAN, "evmwumian" },
	{ PPC_INS_EVNAND, "evnand" },
	{ PPC_INS_EVNEG, "evneg" },
	{ PPC_INS_EVNOR, "evnor" },
	{ PPC_INS_EVOR, "evor" },
	{ PPC_INS_EVORC, "evorc" },
	{ PPC_INS_EVRLW, "evrlw" },
	{ PPC_INS_EVRLWI, "evrlwi" },
	{ PPC_INS_EVRNDW, "evrndw" },
	{ PPC_INS_EVSLW, "evslw" },
	{ PPC_INS_EVSLWI, "evslwi" },
	{ PPC_INS_EVSPLATFI, "evsplatfi" },
	{ PPC_INS_EVSPLATI, "evsplati" },
	{ PPC_INS_EVSRWIS, "evsrwis" },
	{ PPC_INS_EVSRWIU, "evsrwiu" },
	{ PPC_INS_EVSRWS, "evsrws" },
	{ PPC_INS_EVSRWU, "evsrwu" },
	{ PPC_INS_EVSTDD, "evstdd" },
	{ PPC_INS_EVSTDDX, "evstddx" },
	{ PPC_INS_EVSTDH, "evstdh" },
	{ PPC_INS_EVSTDHX, "evstdhx" },
	{ PPC_INS_EVSTDW, "evstdw" },
	{ PPC_INS_EVSTDWX, "evstdwx" },
	{ PPC_INS_EVSTWHE, "evstwhe" },
	{ PPC_INS_EVSTWHEX, "evstwhex" },
	{ PPC_INS_EVSTWHO, "evstwho" },
	{ PPC_INS_EVSTWHOX, "evstwhox" },
	{ PPC_INS_EVSTWWE, "evstwwe" },
	{ PPC_INS_EVSTWWEX, "evstwwex" },
	{ PPC_INS_EVSTWWO, "evstwwo" },
	{ PPC_INS_EVSTWWOX, "evstwwox" },
	{ PPC_INS_EVSUBFSMIAAW, "evsubfsmiaaw" },
	{ PPC_INS_EVSUBFSSIAAW, "evsubfssiaaw" },
	{ PPC_INS_EVSUBFUMIAAW, "evsubfumiaaw" },
	{ PPC_INS_EVSUBFUSIAAW, "evsubfusiaaw" },
	{ PPC_INS_EVSUBFW, "evsubfw" },
	{ PPC_INS_EVSUBIFW, "evsubifw" },
	{ PPC_INS_EVXOR, "evxor" },
	{ PPC_INS_EXTSB, "extsb" },
	{ PPC_INS_EXTSH, "extsh" },
	{ PPC_INS_EXTSW, "extsw" },
	{ PPC_INS_EIEIO, "eieio" },
	{ PPC_INS_FABS, "fabs" },
	{ PPC_INS_FADD, "fadd" },
	{ PPC_INS_FADDS, "fadds" },
	{ PPC_INS_FCFID, "fcfid" },
	{ PPC_INS_FCFIDS, "fcfids" },
	{ PPC_INS_FCFIDU, "fcfidu" },
	{ PPC_INS_FCFIDUS, "fcfidus" },
	{ PPC_INS_FCMPU, "fcmpu" },
	{ PPC_INS_FCPSGN, "fcpsgn" },
	{ PPC_INS_FCTID, "fctid" },
	{ PPC_INS_FCTIDUZ, "fctiduz" },
	{ PPC_INS_FCTIDZ, "fctidz" },
	{ PPC_INS_FCTIW, "fctiw" },
	{ PPC_INS_FCTIWUZ, "fctiwuz" },
	{ PPC_INS_FCTIWZ, "fctiwz" },
	{ PPC_INS_FDIV, "fdiv" },
	{ PPC_INS_FDIVS, "fdivs" },
	{ PPC_INS_FMADD, "fmadd" },
	{ PPC_INS_FMADDS, "fmadds" },
	{ PPC_INS_FMR, "fmr" },
	{ PPC_INS_FMSUB, "fmsub" },
	{ PPC_INS_FMSUBS, "fmsubs" },
	{ PPC_INS_FMUL, "fmul" },
	{ PPC_INS_FMULS, "fmuls" },
	{ PPC_INS_FNABS, "fnabs" },
	{ PPC_INS_FNEG, "fneg" },
	{ PPC_INS_FNMADD, "fnmadd" },
	{ PPC_INS_FNMADDS, "fnmadds" },
	{ PPC_INS_FNMSUB, "fnmsub" },
	{ PPC_INS_FNMSUBS, "fnmsubs" },
	{ PPC_INS_FRE, "fre" },
	{ PPC_INS_FRES, "fres" },
	{ PPC_INS_FRIM, "frim" },
	{ PPC_INS_FRIN, "frin" },
	{ PPC_INS_FRIP, "frip" },
	{ PPC_INS_FRIZ, "friz" },
	{ PPC_INS_FRSP, "frsp" },
	{ PPC_INS_FRSQRTE, "frsqrte" },
	{ PPC_INS_FRSQRTES, "frsqrtes" },
	{ PPC_INS_FSEL, "fsel" },
	{ PPC_INS_FSQRT, "fsqrt" },
	{ PPC_INS_FSQRTS, "fsqrts" },
	{ PPC_INS_FSUB, "fsub" },
	{ PPC_INS_FSUBS, "fsubs" },
	{ PPC_INS_ICBI, "icbi" },
	{ PPC_INS_ICBT, "icbt" },
	{ PPC_INS_ICCCI, "iccci" },
	{ PPC_INS_ISEL, "isel" },
	{ PPC_INS_ISYNC, "isync" },
	{ PPC_INS_LA, "la" },
	{ PPC_INS_LBZ, "lbz" },
	{ PPC_INS_LBZCIX, "lbzcix" },
	{ PPC_INS_LBZU, "lbzu" },
	{ PPC_INS_LBZUX, "lbzux" },
	{ PPC_INS_LBZX, "lbzx" },
	{ PPC_INS_LD, "ld" },
	{ PPC_INS_LDARX, "ldarx" },
	{ PPC_INS_LDBRX, "ldbrx" },
	{ PPC_INS_LDCIX, "ldcix" },
	{ PPC_INS_LDU, "ldu" },
	{ PPC_INS_LDUX, "ldux" },
	{ PPC_INS_LDX, "ldx" },
	{ PPC_INS_LFD, "lfd" },
	{ PPC_INS_LFDU, "lfdu" },
	{ PPC_INS_LFDUX, "lfdux" },
	{ PPC_INS_LFDX, "lfdx" },
	{ PPC_INS_LFIWAX, "lfiwax" },
	{ PPC_INS_LFIWZX, "lfiwzx" },
	{ PPC_INS_LFS, "lfs" },
	{ PPC_INS_LFSU, "lfsu" },
	{ PPC_INS_LFSUX, "lfsux" },
	{ PPC_INS_LFSX, "lfsx" },
	{ PPC_INS_LHA, "lha" },
	{ PPC_INS_LHAU, "lhau" },
	{ PPC_INS_LHAUX, "lhaux" },
	{ PPC_INS_LHAX, "lhax" },
	{ PPC_INS_LHBRX, "lhbrx" },
	{ PPC_INS_LHZ, "lhz" },
	{ PPC_INS_LHZCIX, "lhzcix" },
	{ PPC_INS_LHZU, "lhzu" },
	{ PPC_INS_LHZUX, "lhzux" },
	{ PPC_INS_LHZX, "lhzx" },
	{ PPC_INS_LI, "li" },
	{ PPC_INS_LIS, "lis" },
	{ PPC_INS_LMW, "lmw" },
	{ PPC_INS_LSWI, "lswi" },
	{ PPC_INS_LVEBX, "lvebx" },
	{ PPC_INS_LVEHX, "lvehx" },
	{ PPC_INS_LVEWX, "lvewx" },
	{ PPC_INS_LVSL, "lvsl" },
	{ PPC_INS_LVSR, "lvsr" },
	{ PPC_INS_LVX, "lvx" },
	{ PPC_INS_LVXL, "lvxl" },
	{ PPC_INS_LWA, "lwa" },
	{ PPC_INS_LWARX, "lwarx" },
	{ PPC_INS_LWAUX, "lwaux" },
	{ PPC_INS_LWAX, "lwax" },
	{ PPC_INS_LWBRX, "lwbrx" },
	{ PPC_INS_LWZ, "lwz" },
	{ PPC_INS_LWZCIX, "lwzcix" },
	{ PPC_INS_LWZU, "lwzu" },
	{ PPC_INS_LWZUX, "lwzux" },
	{ PPC_INS_LWZX, "lwzx" },
	{ PPC_INS_LXSDX, "lxsdx" },
	{ PPC_INS_LXVD2X, "lxvd2x" },
	{ PPC_INS_LXVDSX, "lxvdsx" },
	{ PPC_INS_LXVW4X, "lxvw4x" },
	{ PPC_INS_MBAR, "mbar" },
	{ PPC_INS_MCRF, "mcrf" },
	{ PPC_INS_MCRFS, "mcrfs" },
	{ PPC_INS_MFCR, "mfcr" },
	{ PPC_INS_MFCTR, "mfctr" },
	{ PPC_INS_MFDCR, "mfdcr" },
	{ PPC_INS_MFFS, "mffs" },
	{ PPC_INS_MFLR, "mflr" },
	{ PPC_INS_MFMSR, "mfmsr" },
	{ PPC_INS_MFOCRF, "mfocrf" },
	{ PPC_INS_MFSPR, "mfspr" },
	{ PPC_INS_MFSR, "mfsr" },
	{ PPC_INS_MFSRIN, "mfsrin" },
	{ PPC_INS_MFTB, "mftb" },
	{ PPC_INS_MFVSCR, "mfvscr" },
	{ PPC_INS_MSYNC, "msync" },
	{ PPC_INS_MTCRF, "mtcrf" },
	{ PPC_INS_MTCTR, "mtctr" },
	{ PPC_INS_MTDCR, "mtdcr" },
	{ PPC_INS_MTFSB0, "mtfsb0" },
	{ PPC_INS_MTFSB1, "mtfsb1" },
	{ PPC_INS_MTFSF, "mtfsf" },
	{ PPC_INS_MTFSFI, "mtfsfi" },
	{ PPC_INS_MTLR, "mtlr" },
	{ PPC_INS_MTMSR, "mtmsr" },
	{ PPC_INS_MTMSRD, "mtmsrd" },
	{ PPC_INS_MTOCRF, "mtocrf" },
	{ PPC_INS_MTSPR, "mtspr" },
	{ PPC_INS_MTSR, "mtsr" },
	{ PPC_INS_MTSRIN, "mtsrin" },
	{ PPC_INS_MTVSCR, "mtvscr" },
	{ PPC_INS_MULHD, "mulhd" },
	{ PPC_INS_MULHDU, "mulhdu" },
	{ PPC_INS_MULHW, "mulhw" },
	{ PPC_INS_MULHWU, "mulhwu" },
	{ PPC_INS_MULLD, "mulld" },
	{ PPC_INS_MULLI, "mulli" },
	{ PPC_INS_MULLW, "mullw" },
	{ PPC_INS_NAND, "nand" },
	{ PPC_INS_NEG, "neg" },
	{ PPC_INS_NOP, "nop" },
	{ PPC_INS_ORI, "ori" },
	{ PPC_INS_NOR, "nor" },
	{ PPC_INS_OR, "or" },
	{ PPC_INS_ORC, "orc" },
	{ PPC_INS_ORIS, "oris" },
	{ PPC_INS_POPCNTD, "popcntd" },
	{ PPC_INS_POPCNTW, "popcntw" },
	{ PPC_INS_QVALIGNI, "qvaligni" },
	{ PPC_INS_QVESPLATI, "qvesplati" },
	{ PPC_INS_QVFABS, "qvfabs" },
	{ PPC_INS_QVFADD, "qvfadd" },
	{ PPC_INS_QVFADDS, "qvfadds" },
	{ PPC_INS_QVFCFID, "qvfcfid" },
	{ PPC_INS_QVFCFIDS, "qvfcfids" },
	{ PPC_INS_QVFCFIDU, "qvfcfidu" },
	{ PPC_INS_QVFCFIDUS, "qvfcfidus" },
	{ PPC_INS_QVFCMPEQ, "qvfcmpeq" },
	{ PPC_INS_QVFCMPGT, "qvfcmpgt" },
	{ PPC_INS_QVFCMPLT, "qvfcmplt" },
	{ PPC_INS_QVFCPSGN, "qvfcpsgn" },
	{ PPC_INS_QVFCTID, "qvfctid" },
	{ PPC_INS_QVFCTIDU, "qvfctidu" },
	{ PPC_INS_QVFCTIDUZ, "qvfctiduz" },
	{ PPC_INS_QVFCTIDZ, "qvfctidz" },
	{ PPC_INS_QVFCTIW, "qvfctiw" },
	{ PPC_INS_QVFCTIWU, "qvfctiwu" },
	{ PPC_INS_QVFCTIWUZ, "qvfctiwuz" },
	{ PPC_INS_QVFCTIWZ, "qvfctiwz" },
	{ PPC_INS_QVFLOGICAL, "qvflogical" },
	{ PPC_INS_QVFMADD, "qvfmadd" },
	{ PPC_INS_QVFMADDS, "qvfmadds" },
	{ PPC_INS_QVFMR, "qvfmr" },
	{ PPC_INS_QVFMSUB, "qvfmsub" },
	{ PPC_INS_QVFMSUBS, "qvfmsubs" },
	{ PPC_INS_QVFMUL, "qvfmul" },
	{ PPC_INS_QVFMULS, "qvfmuls" },
	{ PPC_INS_QVFNABS, "qvfnabs" },
	{ PPC_INS_QVFNEG, "qvfneg" },
	{ PPC_INS_QVFNMADD, "qvfnmadd" },
	{ PPC_INS_QVFNMADDS, "qvfnmadds" },
	{ PPC_INS_QVFNMSUB, "qvfnmsub" },
	{ PPC_INS_QVFNMSUBS, "qvfnmsubs" },
	{ PPC_INS_QVFPERM, "qvfperm" },
	{ PPC_INS_QVFRE, "qvfre" },
	{ PPC_INS_QVFRES, "qvfres" },
	{ PPC_INS_QVFRIM, "qvfrim" },
	{ PPC_INS_QVFRIN, "qvfrin" },
	{ PPC_INS_QVFRIP, "qvfrip" },
	{ PPC_INS_QVFRIZ, "qvfriz" },
	{ PPC_INS_QVFRSP, "qvfrsp" },
	{ PPC_INS_QVFRSQRTE, "qvfrsqrte" },
	{ PPC_INS_QVFRSQRTES, "qvfrsqrtes" },
	{ PPC_INS_QVFSEL, "qvfsel" },
	{ PPC_INS_QVFSUB, "qvfsub" },
	{ PPC_INS_QVFSUBS, "qvfsubs" },
	{ PPC_INS_QVFTSTNAN, "qvftstnan" },
	{ PPC_INS_QVFXMADD, "qvfxmadd" },
	{ PPC_INS_QVFXMADDS, "qvfxmadds" },
	{ PPC_INS_QVFXMUL, "qvfxmul" },
	{ PPC_INS_QVFXMULS, "qvfxmuls" },
	{ PPC_INS_QVFXXCPNMADD, "qvfxxcpnmadd" },
	{ PPC_INS_QVFXXCPNMADDS, "qvfxxcpnmadds" },
	{ PPC_INS_QVFXXMADD, "qvfxxmadd" },
	{ PPC_INS_QVFXXMADDS, "qvfxxmadds" },
	{ PPC_INS_QVFXXNPMADD, "qvfxxnpmadd" },
	{ PPC_INS_QVFXXNPMADDS, "qvfxxnpmadds" },
	{ PPC_INS_QVGPCI, "qvgpci" },
	{ PPC_INS_QVLFCDUX, "qvlfcdux" },
	{ PPC_INS_QVLFCDUXA, "qvlfcduxa" },
	{ PPC_INS_QVLFCDX, "qvlfcdx" },
	{ PPC_INS_QVLFCDXA, "qvlfcdxa" },
	{ PPC_INS_QVLFCSUX, "qvlfcsux" },
	{ PPC_INS_QVLFCSUXA, "qvlfcsuxa" },
	{ PPC_INS_QVLFCSX, "qvlfcsx" },
	{ PPC_INS_QVLFCSXA, "qvlfcsxa" },
	{ PPC_INS_QVLFDUX, "qvlfdux" },
	{ PPC_INS_QVLFDUXA, "qvlfduxa" },
	{ PPC_INS_QVLFDX, "qvlfdx" },
	{ PPC_INS_QVLFDXA, "qvlfdxa" },
	{ PPC_INS_QVLFIWAX, "qvlfiwax" },
	{ PPC_INS_QVLFIWAXA, "qvlfiwaxa" },
	{ PPC_INS_QVLFIWZX, "qvlfiwzx" },
	{ PPC_INS_QVLFIWZXA, "qvlfiwzxa" },
	{ PPC_INS_QVLFSUX, "qvlfsux" },
	{ PPC_INS_QVLFSUXA, "qvlfsuxa" },
	{ PPC_INS_QVLFSX, "qvlfsx" },
	{ PPC_INS_QVLFSXA, "qvlfsxa" },
	{ PPC_INS_QVLPCLDX, "qvlpcldx" },
	{ PPC_INS_QVLPCLSX, "qvlpclsx" },
	{ PPC_INS_QVLPCRDX, "qvlpcrdx" },
	{ PPC_INS_QVLPCRSX, "qvlpcrsx" },
	{ PPC_INS_QVSTFCDUX, "qvstfcdux" },
	{ PPC_INS_QVSTFCDUXA, "qvstfcduxa" },
	{ PPC_INS_QVSTFCDUXI, "qvstfcduxi" },
	{ PPC_INS_QVSTFCDUXIA, "qvstfcduxia" },
	{ PPC_INS_QVSTFCDX, "qvstfcdx" },
	{ PPC_INS_QVSTFCDXA, "qvstfcdxa" },
	{ PPC_INS_QVSTFCDXI, "qvstfcdxi" },
	{ PPC_INS_QVSTFCDXIA, "qvstfcdxia" },
	{ PPC_INS_QVSTFCSUX, "qvstfcsux" },
	{ PPC_INS_QVSTFCSUXA, "qvstfcsuxa" },
	{ PPC_INS_QVSTFCSUXI, "qvstfcsuxi" },
	{ PPC_INS_QVSTFCSUXIA, "qvstfcsuxia" },
	{ PPC_INS_QVSTFCSX, "qvstfcsx" },
	{ PPC_INS_QVSTFCSXA, "qvstfcsxa" },
	{ PPC_INS_QVSTFCSXI, "qvstfcsxi" },
	{ PPC_INS_QVSTFCSXIA, "qvstfcsxia" },
	{ PPC_INS_QVSTFDUX, "qvstfdux" },
	{ PPC_INS_QVSTFDUXA, "qvstfduxa" },
	{ PPC_INS_QVSTFDUXI, "qvstfduxi" },
	{ PPC_INS_QVSTFDUXIA, "qvstfduxia" },
	{ PPC_INS_QVSTFDX, "qvstfdx" },
	{ PPC_INS_QVSTFDXA, "qvstfdxa" },
	{ PPC_INS_QVSTFDXI, "qvstfdxi" },
	{ PPC_INS_QVSTFDXIA, "qvstfdxia" },
	{ PPC_INS_QVSTFIWX, "qvstfiwx" },
	{ PPC_INS_QVSTFIWXA, "qvstfiwxa" },
	{ PPC_INS_QVSTFSUX, "qvstfsux" },
	{ PPC_INS_QVSTFSUXA, "qvstfsuxa" },
	{ PPC_INS_QVSTFSUXI, "qvstfsuxi" },
	{ PPC_INS_QVSTFSUXIA, "qvstfsuxia" },
	{ PPC_INS_QVSTFSX, "qvstfsx" },
	{ PPC_INS_QVSTFSXA, "qvstfsxa" },
	{ PPC_INS_QVSTFSXI, "qvstfsxi" },
	{ PPC_INS_QVSTFSXIA, "qvstfsxia" },
	{ PPC_INS_RFCI, "rfci" },
	{ PPC_INS_RFDI, "rfdi" },
	{ PPC_INS_RFI, "rfi" },
	{ PPC_INS_RFID, "rfid" },
	{ PPC_INS_RFMCI, "rfmci" },
	{ PPC_INS_RLDCL, "rldcl" },
	{ PPC_INS_RLDCR, "rldcr" },
	{ PPC_INS_RLDIC, "rldic" },
	{ PPC_INS_RLDICL, "rldicl" },
	{ PPC_INS_RLDICR, "rldicr" },
	{ PPC_INS_RLDIMI, "rldimi" },
	{ PPC_INS_RLWIMI, "rlwimi" },
	{ PPC_INS_RLWINM, "rlwinm" },
	{ PPC_INS_RLWNM, "rlwnm" },
	{ PPC_INS_SC, "sc" },
	{ PPC_INS_SLBIA, "slbia" },
	{ PPC_INS_SLBIE, "slbie" },
	{ PPC_INS_SLBMFEE, "slbmfee" },
	{ PPC_INS_SLBMTE, "slbmte" },
	{ PPC_INS_SLD, "sld" },
	{ PPC_INS_SLW, "slw" },
	{ PPC_INS_SRAD, "srad" },
	{ PPC_INS_SRADI, "sradi" },
	{ PPC_INS_SRAW, "sraw" },
	{ PPC_INS_SRAWI, "srawi" },
	{ PPC_INS_SRD, "srd" },
	{ PPC_INS_SRW, "srw" },
	{ PPC_INS_STB, "stb" },
	{ PPC_INS_STBCIX, "stbcix" },
	{ PPC_INS_STBU, "stbu" },
	{ PPC_INS_STBUX, "stbux" },
	{ PPC_INS_STBX, "stbx" },
	{ PPC_INS_STD, "std" },
	{ PPC_INS_STDBRX, "stdbrx" },
	{ PPC_INS_STDCIX, "stdcix" },
	{ PPC_INS_STDCX, "stdcx" },
	{ PPC_INS_STDU, "stdu" },
	{ PPC_INS_STDUX, "stdux" },
	{ PPC_INS_STDX, "stdx" },
	{ PPC_INS_STFD, "stfd" },
	{ PPC_INS_STFDU, "stfdu" },
	{ PPC_INS_STFDUX, "stfdux" },
	{ PPC_INS_STFDX, "stfdx" },
	{ PPC_INS_STFIWX, "stfiwx" },
	{ PPC_INS_STFS, "stfs" },
	{ PPC_INS_STFSU, "stfsu" },
	{ PPC_INS_STFSUX, "stfsux" },
	{ PPC_INS_STFSX, "stfsx" },
	{ PPC_INS_STH, "sth" },
	{ PPC_INS_STHBRX, "sthbrx" },
	{ PPC_INS_STHCIX, "sthcix" },
	{ PPC_INS_STHU, "sthu" },
	{ PPC_INS_STHUX, "sthux" },
	{ PPC_INS_STHX, "sthx" },
	{ PPC_INS_STMW, "stmw" },
	{ PPC_INS_STSWI, "stswi" },
	{ PPC_INS_STVEBX, "stvebx" },
	{ PPC_INS_STVEHX, "stvehx" },
	{ PPC_INS_STVEWX, "stvewx" },
	{ PPC_INS_STVX, "stvx" },
	{ PPC_INS_STVXL, "stvxl" },
	{ PPC_INS_STW, "stw" },
	{ PPC_INS_STWBRX, "stwbrx" },
	{ PPC_INS_STWCIX, "stwcix" },
	{ PPC_INS_STWCX, "stwcx" },
	{ PPC_INS_STWU, "stwu" },
	{ PPC_INS_STWUX, "stwux" },
	{ PPC_INS_STWX, "stwx" },
	{ PPC_INS_STXSDX, "stxsdx" },
	{ PPC_INS_STXVD2X, "stxvd2x" },
	{ PPC_INS_STXVW4X, "stxvw4x" },
	{ PPC_INS_SUBF, "subf" },
	{ PPC_INS_SUBFC, "subfc" },
	{ PPC_INS_SUBFE, "subfe" },
	{ PPC_INS_SUBFIC, "subfic" },
	{ PPC_INS_SUBFME, "subfme" },
	{ PPC_INS_SUBFZE, "subfze" },
	{ PPC_INS_SYNC, "sync" },
	{ PPC_INS_TD, "td" },
	{ PPC_INS_TDI, "tdi" },
	{ PPC_INS_TLBIA, "tlbia" },
	{ PPC_INS_TLBIE, "tlbie" },
	{ PPC_INS_TLBIEL, "tlbiel" },
	{ PPC_INS_TLBIVAX, "tlbivax" },
	{ PPC_INS_TLBLD, "tlbld" },
	{ PPC_INS_TLBLI, "tlbli" },
	{ PPC_INS_TLBRE, "tlbre" },
	{ PPC_INS_TLBSX, "tlbsx" },
	{ PPC_INS_TLBSYNC, "tlbsync" },
	{ PPC_INS_TLBWE, "tlbwe" },
	{ PPC_INS_TRAP, "trap" },
	{ PPC_INS_TW, "tw" },
	{ PPC_INS_TWI, "twi" },
	{ PPC_INS_VADDCUW, "vaddcuw" },
	{ PPC_INS_VADDFP, "vaddfp" },
	{ PPC_INS_VADDSBS, "vaddsbs" },
	{ PPC_INS_VADDSHS, "vaddshs" },
	{ PPC_INS_VADDSWS, "vaddsws" },
	{ PPC_INS_VADDUBM, "vaddubm" },
	{ PPC_INS_VADDUBS, "vaddubs" },
	{ PPC_INS_VADDUDM, "vaddudm" },
	{ PPC_INS_VADDUHM, "vadduhm" },
	{ PPC_INS_VADDUHS, "vadduhs" },
	{ PPC_INS_VADDUWM, "vadduwm" },
	{ PPC_INS_VADDUWS, "vadduws" },
	{ PPC_INS_VAND, "vand" },
	{ PPC_INS_VANDC, "vandc" },
	{ PPC_INS_VAVGSB, "vavgsb" },
	{ PPC_INS_VAVGSH, "vavgsh" },
	{ PPC_INS_VAVGSW, "vavgsw" },
	{ PPC_INS_VAVGUB, "vavgub" },
	{ PPC_INS_VAVGUH, "vavguh" },
	{ PPC_INS_VAVGUW, "vavguw" },
	{ PPC_INS_VCFSX, "vcfsx" },
	{ PPC_INS_VCFUX, "vcfux" },
	{ PPC_INS_VCLZB, "vclzb" },
	{ PPC_INS_VCLZD, "vclzd" },
	{ PPC_INS_VCLZH, "vclzh" },
	{ PPC_INS_VCLZW, "vclzw" },
	{ PPC_INS_VCMPBFP, "vcmpbfp" },
	{ PPC_INS_VCMPEQFP, "vcmpeqfp" },
	{ PPC_INS_VCMPEQUB, "vcmpequb" },
	{ PPC_INS_VCMPEQUD, "vcmpequd" },
	{ PPC_INS_VCMPEQUH, "vcmpequh" },
	{ PPC_INS_VCMPEQUW, "vcmpequw" },
	{ PPC_INS_VCMPGEFP, "vcmpgefp" },
	{ PPC_INS_VCMPGTFP, "vcmpgtfp" },
	{ PPC_INS_VCMPGTSB, "vcmpgtsb" },
	{ PPC_INS_VCMPGTSD, "vcmpgtsd" },
	{ PPC_INS_VCMPGTSH, "vcmpgtsh" },
	{ PPC_INS_VCMPGTSW, "vcmpgtsw" },
	{ PPC_INS_VCMPGTUB, "vcmpgtub" },
	{ PPC_INS_VCMPGTUD, "vcmpgtud" },
	{ PPC_INS_VCMPGTUH, "vcmpgtuh" },
	{ PPC_INS_VCMPGTUW, "vcmpgtuw" },
	{ PPC_INS_VCTSXS, "vctsxs" },
	{ PPC_INS_VCTUXS, "vctuxs" },
	{ PPC_INS_VEQV, "veqv" },
	{ PPC_INS_VEXPTEFP, "vexptefp" },
	{ PPC_INS_VLOGEFP, "vlogefp" },
	{ PPC_INS_VMADDFP, "vmaddfp" },
	{ PPC_INS_VMAXFP, "vmaxfp" },
	{ PPC_INS_VMAXSB, "vmaxsb" },
	{ PPC_INS_VMAXSD, "vmaxsd" },
	{ PPC_INS_VMAXSH, "vmaxsh" },
	{ PPC_INS_VMAXSW, "vmaxsw" },
	{ PPC_INS_VMAXUB, "vmaxub" },
	{ PPC_INS_VMAXUD, "vmaxud" },
	{ PPC_INS_VMAXUH, "vmaxuh" },
	{ PPC_INS_VMAXUW, "vmaxuw" },
	{ PPC_INS_VMHADDSHS, "vmhaddshs" },
	{ PPC_INS_VMHRADDSHS, "vmhraddshs" },
	{ PPC_INS_VMINUD, "vminud" },
	{ PPC_INS_VMINFP, "vminfp" },
	{ PPC_INS_VMINSB, "vminsb" },
	{ PPC_INS_VMINSD, "vminsd" },
	{ PPC_INS_VMINSH, "vminsh" },
	{ PPC_INS_VMINSW, "vminsw" },
	{ PPC_INS_VMINUB, "vminub" },
	{ PPC_INS_VMINUH, "vminuh" },
	{ PPC_INS_VMINUW, "vminuw" },
	{ PPC_INS_VMLADDUHM, "vmladduhm" },
	{ PPC_INS_VMRGHB, "vmrghb" },
	{ PPC_INS_VMRGHH, "vmrghh" },
	{ PPC_INS_VMRGHW, "vmrghw" },
	{ PPC_INS_VMRGLB, "vmrglb" },
	{ PPC_INS_VMRGLH, "vmrglh" },
	{ PPC_INS_VMRGLW, "vmrglw" },
	{ PPC_INS_VMSUMMBM, "vmsummbm" },
	{ PPC_INS_VMSUMSHM, "vmsumshm" },
	{ PPC_INS_VMSUMSHS, "vmsumshs" },
	{ PPC_INS_VMSUMUBM, "vmsumubm" },
	{ PPC_INS_VMSUMUHM, "vmsumuhm" },
	{ PPC_INS_VMSUMUHS, "vmsumuhs" },
	{ PPC_INS_VMULESB, "vmulesb" },
	{ PPC_INS_VMULESH, "vmulesh" },
	{ PPC_INS_VMULESW, "vmulesw" },
	{ PPC_INS_VMULEUB, "vmuleub" },
	{ PPC_INS_VMULEUH, "vmuleuh" },
	{ PPC_INS_VMULEUW, "vmuleuw" },
	{ PPC_INS_VMULOSB, "vmulosb" },
	{ PPC_INS_VMULOSH, "vmulosh" },
	{ PPC_INS_VMULOSW, "vmulosw" },
	{ PPC_INS_VMULOUB, "vmuloub" },
	{ PPC_INS_VMULOUH, "vmulouh" },
	{ PPC_INS_VMULOUW, "vmulouw" },
	{ PPC_INS_VMULUWM, "vmuluwm" },
	{ PPC_INS_VNAND, "vnand" },
	{ PPC_INS_VNMSUBFP, "vnmsubfp" },
	{ PPC_INS_VNOR, "vnor" },
	{ PPC_INS_VOR, "vor" },
	{ PPC_INS_VORC, "vorc" },
	{ PPC_INS_VPERM, "vperm" },
	{ PPC_INS_VPKPX, "vpkpx" },
	{ PPC_INS_VPKSHSS, "vpkshss" },
	{ PPC_INS_VPKSHUS, "vpkshus" },
	{ PPC_INS_VPKSWSS, "vpkswss" },
	{ PPC_INS_VPKSWUS, "vpkswus" },
	{ PPC_INS_VPKUHUM, "vpkuhum" },
	{ PPC_INS_VPKUHUS, "vpkuhus" },
	{ PPC_INS_VPKUWUM, "vpkuwum" },
	{ PPC_INS_VPKUWUS, "vpkuwus" },
	{ PPC_INS_VPOPCNTB, "vpopcntb" },
	{ PPC_INS_VPOPCNTD, "vpopcntd" },
	{ PPC_INS_VPOPCNTH, "vpopcnth" },
	{ PPC_INS_VPOPCNTW, "vpopcntw" },
	{ PPC_INS_VREFP, "vrefp" },
	{ PPC_INS_VRFIM, "vrfim" },
	{ PPC_INS_VRFIN, "vrfin" },
	{ PPC_INS_VRFIP, "vrfip" },
	{ PPC_INS_VRFIZ, "vrfiz" },
	{ PPC_INS_VRLB, "vrlb" },
	{ PPC_INS_VRLD, "vrld" },
	{ PPC_INS_VRLH, "vrlh" },
	{ PPC_INS_VRLW, "vrlw" },
	{ PPC_INS_VRSQRTEFP, "vrsqrtefp" },
	{ PPC_INS_VSEL, "vsel" },
	{ PPC_INS_VSL, "vsl" },
	{ PPC_INS_VSLB, "vslb" },
	{ PPC_INS_VSLD, "vsld" },
	{ PPC_INS_VSLDOI, "vsldoi" },
	{ PPC_INS_VSLH, "vslh" },
	{ PPC_INS_VSLO, "vslo" },
	{ PPC_INS_VSLW, "vslw" },
	{ PPC_INS_VSPLTB, "vspltb" },
	{ PPC_INS_VSPLTH, "vsplth" },
	{ PPC_INS_VSPLTISB, "vspltisb" },
	{ PPC_INS_VSPLTISH, "vspltish" },
	{ PPC_INS_VSPLTISW, "vspltisw" },
	{ PPC_INS_VSPLTW, "vspltw" },
	{ PPC_INS_VSR, "vsr" },
	{ PPC_INS_VSRAB, "vsrab" },
	{ PPC_INS_VSRAD, "vsrad" },
	{ PPC_INS_VSRAH, "vsrah" },
	{ PPC_INS_VSRAW, "vsraw" },
	{ PPC_INS_VSRB, "vsrb" },
	{ PPC_INS_VSRD, "vsrd" },
	{ PPC_INS_VSRH, "vsrh" },
	{ PPC_INS_VSRO, "vsro" },
	{ PPC_INS_VSRW, "vsrw" },
	{ PPC_INS_VSUBCUW, "vsubcuw" },
	{ PPC_INS_VSUBFP, "vsubfp" },
	{ PPC_INS_VSUBSBS, "vsubsbs" },
	{ PPC_INS_VSUBSHS, "vsubshs" },
	{ PPC_INS_VSUBSWS, "vsubsws" },
	{ PPC_INS_VSUBUBM, "vsububm" },
	{ PPC_INS_VSUBUBS, "vsububs" },
	{ PPC_INS_VSUBUDM, "vsubudm" },
	{ PPC_INS_VSUBUHM, "vsubuhm" },
	{ PPC_INS_VSUBUHS, "vsubuhs" },
	{ PPC_INS_VSUBUWM, "vsubuwm" },
	{ PPC_INS_VSUBUWS, "vsubuws" },
	{ PPC_INS_VSUM2SWS, "vsum2sws" },
	{ PPC_INS_VSUM4SBS, "vsum4sbs" },
	{ PPC_INS_VSUM4SHS, "vsum4shs" },
	{ PPC_INS_VSUM4UBS, "vsum4ubs" },
	{ PPC_INS_VSUMSWS, "vsumsws" },
	{ PPC_INS_VUPKHPX, "vupkhpx" },
	{ PPC_INS_VUPKHSB, "vupkhsb" },
	{ PPC_INS_VUPKHSH, "vupkhsh" },
	{ PPC_INS_VUPKLPX, "vupklpx" },
	{ PPC_INS_VUPKLSB, "vupklsb" },
	{ PPC_INS_VUPKLSH, "vupklsh" },
	{ PPC_INS_VXOR, "vxor" },
	{ PPC_INS_WAIT, "wait" },
	{ PPC_INS_WRTEE, "wrtee" },
	{ PPC_INS_WRTEEI, "wrteei" },
	{ PPC_INS_XOR, "xor" },
	{ PPC_INS_XORI, "xori" },
	{ PPC_INS_XORIS, "xoris" },
	{ PPC_INS_XSABSDP, "xsabsdp" },
	{ PPC_INS_XSADDDP, "xsadddp" },
	{ PPC_INS_XSCMPODP, "xscmpodp" },
	{ PPC_INS_XSCMPUDP, "xscmpudp" },
	{ PPC_INS_XSCPSGNDP, "xscpsgndp" },
	{ PPC_INS_XSCVDPSP, "xscvdpsp" },
	{ PPC_INS_XSCVDPSXDS, "xscvdpsxds" },
	{ PPC_INS_XSCVDPSXWS, "xscvdpsxws" },
	{ PPC_INS_XSCVDPUXDS, "xscvdpuxds" },
	{ PPC_INS_XSCVDPUXWS, "xscvdpuxws" },
	{ PPC_INS_XSCVSPDP, "xscvspdp" },
	{ PPC_INS_XSCVSXDDP, "xscvsxddp" },
	{ PPC_INS_XSCVUXDDP, "xscvuxddp" },
	{ PPC_INS_XSDIVDP, "xsdivdp" },
	{ PPC_INS_XSMADDADP, "xsmaddadp" },
	{ PPC_INS_XSMADDMDP, "xsmaddmdp" },
	{ PPC_INS_XSMAXDP, "xsmaxdp" },
	{ PPC_INS_XSMINDP, "xsmindp" },
	{ PPC_INS_XSMSUBADP, "xsmsubadp" },
	{ PPC_INS_XSMSUBMDP, "xsmsubmdp" },
	{ PPC_INS_XSMULDP, "xsmuldp" },
	{ PPC_INS_XSNABSDP, "xsnabsdp" },
	{ PPC_INS_XSNEGDP, "xsnegdp" },
	{ PPC_INS_XSNMADDADP, "xsnmaddadp" },
	{ PPC_INS_XSNMADDMDP, "xsnmaddmdp" },
	{ PPC_INS_XSNMSUBADP, "xsnmsubadp" },
	{ PPC_INS_XSNMSUBMDP, "xsnmsubmdp" },
	{ PPC_INS_XSRDPI, "xsrdpi" },
	{ PPC_INS_XSRDPIC, "xsrdpic" },
	{ PPC_INS_XSRDPIM, "xsrdpim" },
	{ PPC_INS_XSRDPIP, "xsrdpip" },
	{ PPC_INS_XSRDPIZ, "xsrdpiz" },
	{ PPC_INS_XSREDP, "xsredp" },
	{ PPC_INS_XSRSQRTEDP, "xsrsqrtedp" },
	{ PPC_INS_XSSQRTDP, "xssqrtdp" },
	{ PPC_INS_XSSUBDP, "xssubdp" },
	{ PPC_INS_XSTDIVDP, "xstdivdp" },
	{ PPC_INS_XSTSQRTDP, "xstsqrtdp" },
	{ PPC_INS_XVABSDP, "xvabsdp" },
	{ PPC_INS_XVABSSP, "xvabssp" },
	{ PPC_INS_XVADDDP, "xvadddp" },
	{ PPC_INS_XVADDSP, "xvaddsp" },
	{ PPC_INS_XVCMPEQDP, "xvcmpeqdp" },
	{ PPC_INS_XVCMPEQSP, "xvcmpeqsp" },
	{ PPC_INS_XVCMPGEDP, "xvcmpgedp" },
	{ PPC_INS_XVCMPGESP, "xvcmpgesp" },
	{ PPC_INS_XVCMPGTDP, "xvcmpgtdp" },
	{ PPC_INS_XVCMPGTSP, "xvcmpgtsp" },
	{ PPC_INS_XVCPSGNDP, "xvcpsgndp" },
	{ PPC_INS_XVCPSGNSP, "xvcpsgnsp" },
	{ PPC_INS_XVCVDPSP, "xvcvdpsp" },
	{ PPC_INS_XVCVDPSXDS, "xvcvdpsxds" },
	{ PPC_INS_XVCVDPSXWS, "xvcvdpsxws" },
	{ PPC_INS_XVCVDPUXDS, "xvcvdpuxds" },
	{ PPC_INS_XVCVDPUXWS, "xvcvdpuxws" },
	{ PPC_INS_XVCVSPDP, "xvcvspdp" },
	{ PPC_INS_XVCVSPSXDS, "xvcvspsxds" },
	{ PPC_INS_XVCVSPSXWS, "xvcvspsxws" },
	{ PPC_INS_XVCVSPUXDS, "xvcvspuxds" },
	{ PPC_INS_XVCVSPUXWS, "xvcvspuxws" },
	{ PPC_INS_XVCVSXDDP, "xvcvsxddp" },
	{ PPC_INS_XVCVSXDSP, "xvcvsxdsp" },
	{ PPC_INS_XVCVSXWDP, "xvcvsxwdp" },
	{ PPC_INS_XVCVSXWSP, "xvcvsxwsp" },
	{ PPC_INS_XVCVUXDDP, "xvcvuxddp" },
	{ PPC_INS_XVCVUXDSP, "xvcvuxdsp" },
	{ PPC_INS_XVCVUXWDP, "xvcvuxwdp" },
	{ PPC_INS_XVCVUXWSP, "xvcvuxwsp" },
	{ PPC_INS_XVDIVDP, "xvdivdp" },
	{ PPC_INS_XVDIVSP, "xvdivsp" },
	{ PPC_INS_XVMADDADP, "xvmaddadp" },
	{ PPC_INS_XVMADDASP, "xvmaddasp" },
	{ PPC_INS_XVMADDMDP, "xvmaddmdp" },
	{ PPC_INS_XVMADDMSP, "xvmaddmsp" },
	{ PPC_INS_XVMAXDP, "xvmaxdp" },
	{ PPC_INS_XVMAXSP, "xvmaxsp" },
	{ PPC_INS_XVMINDP, "xvmindp" },
	{ PPC_INS_XVMINSP, "xvminsp" },
	{ PPC_INS_XVMSUBADP, "xvmsubadp" },
	{ PPC_INS_XVMSUBASP, "xvmsubasp" },
	{ PPC_INS_XVMSUBMDP, "xvmsubmdp" },
	{ PPC_INS_XVMSUBMSP, "xvmsubmsp" },
	{ PPC_INS_XVMULDP, "xvmuldp" },
	{ PPC_INS_XVMULSP, "xvmulsp" },
	{ PPC_INS_XVNABSDP, "xvnabsdp" },
	{ PPC_INS_XVNABSSP, "xvnabssp" },
	{ PPC_INS_XVNEGDP, "xvnegdp" },
	{ PPC_INS_XVNEGSP, "xvnegsp" },
	{ PPC_INS_XVNMADDADP, "xvnmaddadp" },
	{ PPC_INS_XVNMADDASP, "xvnmaddasp" },
	{ PPC_INS_XVNMADDMDP, "xvnmaddmdp" },
	{ PPC_INS_XVNMADDMSP, "xvnmaddmsp" },
	{ PPC_INS_XVNMSUBADP, "xvnmsubadp" },
	{ PPC_INS_XVNMSUBASP, "xvnmsubasp" },
	{ PPC_INS_XVNMSUBMDP, "xvnmsubmdp" },
	{ PPC_INS_XVNMSUBMSP, "xvnmsubmsp" },
	{ PPC_INS_XVRDPI, "xvrdpi" },
	{ PPC_INS_XVRDPIC, "xvrdpic" },
	{ PPC_INS_XVRDPIM, "xvrdpim" },
	{ PPC_INS_XVRDPIP, "xvrdpip" },
	{ PPC_INS_XVRDPIZ, "xvrdpiz" },
	{ PPC_INS_XVREDP, "xvredp" },
	{ PPC_INS_XVRESP, "xvresp" },
	{ PPC_INS_XVRSPI, "xvrspi" },
	{ PPC_INS_XVRSPIC, "xvrspic" },
	{ PPC_INS_XVRSPIM, "xvrspim" },
	{ PPC_INS_XVRSPIP, "xvrspip" },
	{ PPC_INS_XVRSPIZ, "xvrspiz" },
	{ PPC_INS_XVRSQRTEDP, "xvrsqrtedp" },
	{ PPC_INS_XVRSQRTESP, "xvrsqrtesp" },
	{ PPC_INS_XVSQRTDP, "xvsqrtdp" },
	{ PPC_INS_XVSQRTSP, "xvsqrtsp" },
	{ PPC_INS_XVSUBDP, "xvsubdp" },
	{ PPC_INS_XVSUBSP, "xvsubsp" },
	{ PPC_INS_XVTDIVDP, "xvtdivdp" },
	{ PPC_INS_XVTDIVSP, "xvtdivsp" },
	{ PPC_INS_XVTSQRTDP, "xvtsqrtdp" },
	{ PPC_INS_XVTSQRTSP, "xvtsqrtsp" },
	{ PPC_INS_XXLAND, "xxland" },
	{ PPC_INS_XXLANDC, "xxlandc" },
	{ PPC_INS_XXLEQV, "xxleqv" },
	{ PPC_INS_XXLNAND, "xxlnand" },
	{ PPC_INS_XXLNOR, "xxlnor" },
	{ PPC_INS_XXLOR, "xxlor" },
	{ PPC_INS_XXLORC, "xxlorc" },
	{ PPC_INS_XXLXOR, "xxlxor" },
	{ PPC_INS_XXMRGHW, "xxmrghw" },
	{ PPC_INS_XXMRGLW, "xxmrglw" },
	{ PPC_INS_XXPERMDI, "xxpermdi" },
	{ PPC_INS_XXSEL, "xxsel" },
	{ PPC_INS_XXSLDWI, "xxsldwi" },
	{ PPC_INS_XXSPLTW, "xxspltw" },
	{ PPC_INS_BCA, "bca" },
	{ PPC_INS_BCLA, "bcla" },

	// extra & alias instructions
	{ PPC_INS_SLWI, "slwi" },
	{ PPC_INS_SRWI, "srwi" },
	{ PPC_INS_SLDI, "sldi" },
	{ PPC_INS_BTA, "bta" },
	{ PPC_INS_CRSET, "crset" },
	{ PPC_INS_CRNOT, "crnot" },
	{ PPC_INS_CRMOVE, "crmove" },
	{ PPC_INS_CRCLR, "crclr" },
	{ PPC_INS_MFBR0, "mfbr0" },
	{ PPC_INS_MFBR1, "mfbr1" },
	{ PPC_INS_MFBR2, "mfbr2" },
	{ PPC_INS_MFBR3, "mfbr3" },
	{ PPC_INS_MFBR4, "mfbr4" },
	{ PPC_INS_MFBR5, "mfbr5" },
	{ PPC_INS_MFBR6, "mfbr6" },
	{ PPC_INS_MFBR7, "mfbr7" },
	{ PPC_INS_MFXER, "mfxer" },
	{ PPC_INS_MFRTCU, "mfrtcu" },
	{ PPC_INS_MFRTCL, "mfrtcl" },
	{ PPC_INS_MFDSCR, "mfdscr" },
	{ PPC_INS_MFDSISR, "mfdsisr" },
	{ PPC_INS_MFDAR, "mfdar" },
	{ PPC_INS_MFSRR2, "mfsrr2" },
	{ PPC_INS_MFSRR3, "mfsrr3" },
	{ PPC_INS_MFCFAR, "mfcfar" },
	{ PPC_INS_MFAMR, "mfamr" },
	{ PPC_INS_MFPID, "mfpid" },
	{ PPC_INS_MFTBLO, "mftblo" },
	{ PPC_INS_MFTBHI, "mftbhi" },
	{ PPC_INS_MFDBATU, "mfdbatu" },
	{ PPC_INS_MFDBATL, "mfdbatl" },
	{ PPC_INS_MFIBATU, "mfibatu" },
	{ PPC_INS_MFIBATL, "mfibatl" },
	{ PPC_INS_MFDCCR, "mfdccr" },
	{ PPC_INS_MFICCR, "mficcr" },
	{ PPC_INS_MFDEAR, "mfdear" },
	{ PPC_INS_MFESR, "mfesr" },
	{ PPC_INS_MFSPEFSCR, "mfspefscr" },
	{ PPC_INS_MFTCR, "mftcr" },
	{ PPC_INS_MFASR, "mfasr" },
	{ PPC_INS_MFPVR, "mfpvr" },
	{ PPC_INS_MFTBU, "mftbu" },
	{ PPC_INS_MTCR, "mtcr" },
	{ PPC_INS_MTBR0, "mtbr0" },
	{ PPC_INS_MTBR1, "mtbr1" },
	{ PPC_INS_MTBR2, "mtbr2" },
	{ PPC_INS_MTBR3, "mtbr3" },
	{ PPC_INS_MTBR4, "mtbr4" },
	{ PPC_INS_MTBR5, "mtbr5" },
	{ PPC_INS_MTBR6, "mtbr6" },
	{ PPC_INS_MTBR7, "mtbr7" },
	{ PPC_INS_MTXER, "mtxer" },
	{ PPC_INS_MTDSCR, "mtdscr" },
	{ PPC_INS_MTDSISR, "mtdsisr" },
	{ PPC_INS_MTDAR, "mtdar" },
	{ PPC_INS_MTSRR2, "mtsrr2" },
	{ PPC_INS_MTSRR3, "mtsrr3" },
	{ PPC_INS_MTCFAR, "mtcfar" },
	{ PPC_INS_MTAMR, "mtamr" },
	{ PPC_INS_MTPID, "mtpid" },
	{ PPC_INS_MTTBL, "mttbl" },
	{ PPC_INS_MTTBU, "mttbu" },
	{ PPC_INS_MTTBLO, "mttblo" },
	{ PPC_INS_MTTBHI, "mttbhi" },
	{ PPC_INS_MTDBATU, "mtdbatu" },
	{ PPC_INS_MTDBATL, "mtdbatl" },
	{ PPC_INS_MTIBATU, "mtibatu" },
	{ PPC_INS_MTIBATL, "mtibatl" },
	{ PPC_INS_MTDCCR, "mtdccr" },
	{ PPC_INS_MTICCR, "mticcr" },
	{ PPC_INS_MTDEAR, "mtdear" },
	{ PPC_INS_MTESR, "mtesr" },
	{ PPC_INS_MTSPEFSCR, "mtspefscr" },
	{ PPC_INS_MTTCR, "mttcr" },
	{ PPC_INS_NOT, "not" },
	{ PPC_INS_MR, "mr" },
	{ PPC_INS_ROTLD, "rotld" },
	{ PPC_INS_ROTLDI, "rotldi" },
	{ PPC_INS_CLRLDI, "clrldi" },
	{ PPC_INS_ROTLWI, "rotlwi" },
	{ PPC_INS_CLRLWI, "clrlwi" },
	{ PPC_INS_ROTLW, "rotlw" },
	{ PPC_INS_SUB, "sub" },
	{ PPC_INS_SUBC, "subc" },
	{ PPC_INS_LWSYNC, "lwsync" },
	{ PPC_INS_PTESYNC, "ptesync" },
	{ PPC_INS_TDLT, "tdlt" },
	{ PPC_INS_TDEQ, "tdeq" },
	{ PPC_INS_TDGT, "tdgt" },
	{ PPC_INS_TDNE, "tdne" },
	{ PPC_INS_TDLLT, "tdllt" },
	{ PPC_INS_TDLGT, "tdlgt" },
	{ PPC_INS_TDU, "tdu" },
	{ PPC_INS_TDLTI, "tdlti" },
	{ PPC_INS_TDEQI, "tdeqi" },
	{ PPC_INS_TDGTI, "tdgti" },
	{ PPC_INS_TDNEI, "tdnei" },
	{ PPC_INS_TDLLTI, "tdllti" },
	{ PPC_INS_TDLGTI, "tdlgti" },
	{ PPC_INS_TDUI, "tdui" },
	{ PPC_INS_TLBREHI, "tlbrehi" },
	{ PPC_INS_TLBRELO, "tlbrelo" },
	{ PPC_INS_TLBWEHI, "tlbwehi" },
	{ PPC_INS_TLBWELO, "tlbwelo" },
	{ PPC_INS_TWLT, "twlt" },
	{ PPC_INS_TWEQ, "tweq" },
	{ PPC_INS_TWGT, "twgt" },
	{ PPC_INS_TWNE, "twne" },
	{ PPC_INS_TWLLT, "twllt" },
	{ PPC_INS_TWLGT, "twlgt" },
	{ PPC_INS_TWU, "twu" },
	{ PPC_INS_TWLTI, "twlti" },
	{ PPC_INS_TWEQI, "tweqi" },
	{ PPC_INS_TWGTI, "twgti" },
	{ PPC_INS_TWNEI, "twnei" },
	{ PPC_INS_TWLLTI, "twllti" },
	{ PPC_INS_TWLGTI, "twlgti" },
	{ PPC_INS_TWUI, "twui" },
	{ PPC_INS_WAITRSV, "waitrsv" },
	{ PPC_INS_WAITIMPL, "waitimpl" },
	{ PPC_INS_XNOP, "xnop" },
	{ PPC_INS_XVMOVDP, "xvmovdp" },
	{ PPC_INS_XVMOVSP, "xvmovsp" },
	{ PPC_INS_XXSPLTD, "xxspltd" },
	{ PPC_INS_XXMRGHD, "xxmrghd" },
	{ PPC_INS_XXMRGLD, "xxmrgld" },
	{ PPC_INS_XXSWAPD, "xxswapd" },
	{ PPC_INS_BT, "bt" },
	{ PPC_INS_BF, "bf" },
	{ PPC_INS_BDNZT, "bdnzt" },
	{ PPC_INS_BDNZF, "bdnzf" },
	{ PPC_INS_BDZF, "bdzf" },
	{ PPC_INS_BDZT, "bdzt" },
	{ PPC_INS_BFA, "bfa" },
	{ PPC_INS_BDNZTA, "bdnzta" },
	{ PPC_INS_BDNZFA, "bdnzfa" },
	{ PPC_INS_BDZTA, "bdzta" },
	{ PPC_INS_BDZFA, "bdzfa" },
	{ PPC_INS_BTCTR, "btctr" },
	{ PPC_INS_BFCTR, "bfctr" },
	{ PPC_INS_BTCTRL, "btctrl" },
	{ PPC_INS_BFCTRL, "bfctrl" },
	{ PPC_INS_BTL, "btl" },
	{ PPC_INS_BFL, "bfl" },
	{ PPC_INS_BDNZTL, "bdnztl" },
	{ PPC_INS_BDNZFL, "bdnzfl" },
	{ PPC_INS_BDZTL, "bdztl" },
	{ PPC_INS_BDZFL, "bdzfl" },
	{ PPC_INS_BTLA, "btla" },
	{ PPC_INS_BFLA, "bfla" },
	{ PPC_INS_BDNZTLA, "bdnztla" },
	{ PPC_INS_BDNZFLA, "bdnzfla" },
	{ PPC_INS_BDZTLA, "bdztla" },
	{ PPC_INS_BDZFLA, "bdzfla" },
	{ PPC_INS_BTLR, "btlr" },
	{ PPC_INS_BFLR, "bflr" },
	{ PPC_INS_BDNZTLR, "bdnztlr" },
	{ PPC_INS_BDZTLR, "bdztlr" },
	{ PPC_INS_BDZFLR, "bdzflr" },
	{ PPC_INS_BTLRL, "btlrl" },
	{ PPC_INS_BFLRL, "bflrl" },
	{ PPC_INS_BDNZTLRL, "bdnztlrl" },
	{ PPC_INS_BDNZFLRL, "bdnzflrl" },
	{ PPC_INS_BDZTLRL, "bdztlrl" },
	{ PPC_INS_BDZFLRL, "bdzflrl" },

	// QPX
	{ PPC_INS_QVFAND, "qvfand" },
	{ PPC_INS_QVFCLR, "qvfclr" },
	{ PPC_INS_QVFANDC, "qvfandc" },
	{ PPC_INS_QVFCTFB, "qvfctfb" },
	{ PPC_INS_QVFXOR, "qvfxor" },
	{ PPC_INS_QVFOR, "qvfor" },
	{ PPC_INS_QVFNOR, "qvfnor" },
	{ PPC_INS_QVFEQU, "qvfequ" },
	{ PPC_INS_QVFNOT, "qvfnot" },
	{ PPC_INS_QVFORC, "qvforc" },
	{ PPC_INS_QVFNAND, "qvfnand" },
	{ PPC_INS_QVFSET, "qvfset" },
};

// special alias insn
static const name_map alias_insn_names[] = {
	{ 0, NULL }
};
#endif

const char *PPC_insn_name(csh handle, unsigned int id)
{
#ifndef CAPSTONE_DIET
	unsigned int i;

	if (id >= PPC_INS_ENDING)
		return NULL;

	// handle special alias first
	for (i = 0; i < ARR_SIZE(alias_insn_names); i++) {
		if (alias_insn_names[i].id == id)
			return alias_insn_names[i].name;
	}

	return insn_name_maps[id].name;
#else
	return NULL;
#endif
}

#ifndef CAPSTONE_DIET
static const name_map group_name_maps[] = {
	// generic groups
	{ PPC_GRP_INVALID, NULL },
	{ PPC_GRP_JUMP,	"jump" },

	// architecture-specific groups
	{ PPC_GRP_ALTIVEC, "altivec" },
	{ PPC_GRP_MODE32, "mode32" },
	{ PPC_GRP_MODE64, "mode64" },
	{ PPC_GRP_BOOKE, "booke" },
	{ PPC_GRP_NOTBOOKE, "notbooke" },
	{ PPC_GRP_SPE, "spe" },
	{ PPC_GRP_VSX, "vsx" },
	{ PPC_GRP_E500, "e500" },
	{ PPC_GRP_PPC4XX, "ppc4xx" },
	{ PPC_GRP_PPC6XX, "ppc6xx" },
	{ PPC_GRP_ICBT, "icbt" },
	{ PPC_GRP_P8ALTIVEC, "p8altivec" },
	{ PPC_GRP_P8VECTOR, "p8vector" },
	{ PPC_GRP_QPX, "qpx" },
};
#endif

const char *PPC_group_name(csh handle, unsigned int id)
{
#ifndef CAPSTONE_DIET
	return id2name(group_name_maps, ARR_SIZE(group_name_maps), id);
#else
	return NULL;
#endif
}

// map internal raw register to 'public' register
ppc_reg PPC_map_register(unsigned int r)
{
	static unsigned int map[] = { 0,
		0, PPC_REG_CARRY, PPC_REG_CTR, 0, PPC_REG_LR,
		0, PPC_REG_VRSAVE, PPC_REG_R0, 0, PPC_REG_CR0,
		PPC_REG_CR1, PPC_REG_CR2, PPC_REG_CR3, PPC_REG_CR4, PPC_REG_CR5,
		PPC_REG_CR6, PPC_REG_CR7, PPC_REG_CTR, PPC_REG_F0, PPC_REG_F1,
		PPC_REG_F2, PPC_REG_F3, PPC_REG_F4, PPC_REG_F5, PPC_REG_F6,
		PPC_REG_F7, PPC_REG_F8, PPC_REG_F9, PPC_REG_F10, PPC_REG_F11,
		PPC_REG_F12, PPC_REG_F13, PPC_REG_F14, PPC_REG_F15, PPC_REG_F16,
		PPC_REG_F17, PPC_REG_F18, PPC_REG_F19, PPC_REG_F20, PPC_REG_F21,
		PPC_REG_F22, PPC_REG_F23, PPC_REG_F24, PPC_REG_F25, PPC_REG_F26,
		PPC_REG_F27, PPC_REG_F28, PPC_REG_F29, PPC_REG_F30, PPC_REG_F31,
		0, PPC_REG_LR, PPC_REG_Q0, PPC_REG_Q1, PPC_REG_Q2,
		PPC_REG_Q3, PPC_REG_Q4, PPC_REG_Q5, PPC_REG_Q6, PPC_REG_Q7,
		PPC_REG_Q8, PPC_REG_Q9, PPC_REG_Q10, PPC_REG_Q11, PPC_REG_Q12,
		PPC_REG_Q13, PPC_REG_Q14, PPC_REG_Q15, PPC_REG_Q16, PPC_REG_Q17,
		PPC_REG_Q18, PPC_REG_Q19, PPC_REG_Q20, PPC_REG_Q21, PPC_REG_Q22,
		PPC_REG_Q23, PPC_REG_Q24, PPC_REG_Q25, PPC_REG_Q26, PPC_REG_Q27,
		PPC_REG_Q28, PPC_REG_Q29, PPC_REG_Q30, PPC_REG_Q31, PPC_REG_R0,
		PPC_REG_R1, PPC_REG_R2, PPC_REG_R3, PPC_REG_R4, PPC_REG_R5,
		PPC_REG_R6, PPC_REG_R7, PPC_REG_R8, PPC_REG_R9, PPC_REG_R10,
		PPC_REG_R11, PPC_REG_R12, PPC_REG_R13, PPC_REG_R14, PPC_REG_R15,
		PPC_REG_R16, PPC_REG_R17, PPC_REG_R18, PPC_REG_R19, PPC_REG_R20,
		PPC_REG_R21, PPC_REG_R22, PPC_REG_R23, PPC_REG_R24, PPC_REG_R25,
		PPC_REG_R26, PPC_REG_R27, PPC_REG_R28, PPC_REG_R29, PPC_REG_R30,
		PPC_REG_R31, PPC_REG_V0, PPC_REG_V1, PPC_REG_V2, PPC_REG_V3,
		PPC_REG_V4, PPC_REG_V5, PPC_REG_V6, PPC_REG_V7, PPC_REG_V8,
		PPC_REG_V9, PPC_REG_V10, PPC_REG_V11, PPC_REG_V12, PPC_REG_V13,
		PPC_REG_V14, PPC_REG_V15, PPC_REG_V16, PPC_REG_V17, PPC_REG_V18,
		PPC_REG_V19, PPC_REG_V20, PPC_REG_V21, PPC_REG_V22, PPC_REG_V23,
		PPC_REG_V24, PPC_REG_V25, PPC_REG_V26, PPC_REG_V27, PPC_REG_V28,
		PPC_REG_V29, PPC_REG_V30, PPC_REG_V31, PPC_REG_VS32, PPC_REG_VS33,
		PPC_REG_VS34, PPC_REG_VS35, PPC_REG_VS36, PPC_REG_VS37, PPC_REG_VS38,
		PPC_REG_VS39, PPC_REG_VS40, PPC_REG_VS41, PPC_REG_VS42, PPC_REG_VS43,
		PPC_REG_VS44, PPC_REG_VS45, PPC_REG_VS46, PPC_REG_VS47, PPC_REG_VS48,
		PPC_REG_VS49, PPC_REG_VS50, PPC_REG_VS51, PPC_REG_VS52, PPC_REG_VS53,
		PPC_REG_VS54, PPC_REG_VS55, PPC_REG_VS56, PPC_REG_VS57, PPC_REG_VS58,
		PPC_REG_VS59, PPC_REG_VS60, PPC_REG_VS61, PPC_REG_VS62, PPC_REG_VS63,
		PPC_REG_VS32, PPC_REG_VS33, PPC_REG_VS34, PPC_REG_VS35, PPC_REG_VS36,
		PPC_REG_VS37, PPC_REG_VS38, PPC_REG_VS39, PPC_REG_VS40, PPC_REG_VS41,
		PPC_REG_VS42, PPC_REG_VS43, PPC_REG_VS44, PPC_REG_VS45, PPC_REG_VS46,
		PPC_REG_VS47, PPC_REG_VS48, PPC_REG_VS49, PPC_REG_VS50, PPC_REG_VS51,
		PPC_REG_VS52, PPC_REG_VS53, PPC_REG_VS54, PPC_REG_VS55, PPC_REG_VS56,
		PPC_REG_VS57, PPC_REG_VS58, PPC_REG_VS59, PPC_REG_VS60, PPC_REG_VS61,
		PPC_REG_VS62, PPC_REG_VS63, PPC_REG_VS0, PPC_REG_VS1, PPC_REG_VS2,
		PPC_REG_VS3, PPC_REG_VS4, PPC_REG_VS5, PPC_REG_VS6, PPC_REG_VS7,
		PPC_REG_VS8, PPC_REG_VS9, PPC_REG_VS10, PPC_REG_VS11, PPC_REG_VS12,
		PPC_REG_VS13, PPC_REG_VS14, PPC_REG_VS15, PPC_REG_VS16, PPC_REG_VS17,
		PPC_REG_VS18, PPC_REG_VS19, PPC_REG_VS20, PPC_REG_VS21, PPC_REG_VS22,
		PPC_REG_VS23, PPC_REG_VS24, PPC_REG_VS25, PPC_REG_VS26, PPC_REG_VS27,
		PPC_REG_VS28, PPC_REG_VS29, PPC_REG_VS30, PPC_REG_VS31, PPC_REG_R0,
		PPC_REG_R1, PPC_REG_R2, PPC_REG_R3, PPC_REG_R4, PPC_REG_R5,
		PPC_REG_R6, PPC_REG_R7, PPC_REG_R8, PPC_REG_R9, PPC_REG_R10,
		PPC_REG_R11, PPC_REG_R12, PPC_REG_R13, PPC_REG_R14, PPC_REG_R15,
		PPC_REG_R16, PPC_REG_R17, PPC_REG_R18, PPC_REG_R19, PPC_REG_R20,
		PPC_REG_R21, PPC_REG_R22, PPC_REG_R23, PPC_REG_R24, PPC_REG_R25,
		PPC_REG_R26, PPC_REG_R27, PPC_REG_R28, PPC_REG_R29, PPC_REG_R30,
		PPC_REG_R31, PPC_REG_R0, PPC_REG_R2, PPC_REG_R6, PPC_REG_R10,
		PPC_REG_R14, PPC_REG_R18, PPC_REG_R22, PPC_REG_R26, PPC_REG_R30,
		PPC_REG_R1, PPC_REG_R5, PPC_REG_R9, PPC_REG_R13, PPC_REG_R17,
		PPC_REG_R21, PPC_REG_R25, PPC_REG_R29, PPC_REG_R0, PPC_REG_R4,
		PPC_REG_R8, PPC_REG_R12, PPC_REG_R16, PPC_REG_R20, PPC_REG_R24,
		PPC_REG_R28, PPC_REG_R3, PPC_REG_R7, PPC_REG_R11, PPC_REG_R15,
		PPC_REG_R19, PPC_REG_R23, PPC_REG_R27, PPC_REG_R31 };

	if (r < ARR_SIZE(map))
		return map[r];

	// cannot find this register
	return 0;
}

static const struct ppc_alias alias_insn_name_maps[] = {
	//{ PPC_INS_BTA, "bta" },
	{ PPC_INS_B, PPC_BC_LT, "blt" },
	{ PPC_INS_B, PPC_BC_LE, "ble" },
	{ PPC_INS_B, PPC_BC_EQ, "beq" },
	{ PPC_INS_B, PPC_BC_GE, "bge" },
	{ PPC_INS_B, PPC_BC_GT, "bgt" },
	{ PPC_INS_B, PPC_BC_NE, "bne" },
	{ PPC_INS_B, PPC_BC_UN, "bun" },
	{ PPC_INS_B, PPC_BC_NU, "bnu" },
	{ PPC_INS_B, PPC_BC_SO, "bso" },
	{ PPC_INS_B, PPC_BC_NS, "bns" },

	{ PPC_INS_BA, PPC_BC_LT, "blta" },
	{ PPC_INS_BA, PPC_BC_LE, "blea" },
	{ PPC_INS_BA, PPC_BC_EQ, "beqa" },
	{ PPC_INS_BA, PPC_BC_GE, "bgea" },
	{ PPC_INS_BA, PPC_BC_GT, "bgta" },
	{ PPC_INS_BA, PPC_BC_NE, "bnea" },
	{ PPC_INS_BA, PPC_BC_UN, "buna" },
	{ PPC_INS_BA, PPC_BC_NU, "bnua" },
	{ PPC_INS_BA, PPC_BC_SO, "bsoa" },
	{ PPC_INS_BA, PPC_BC_NS, "bnsa" },

	{ PPC_INS_BCTR, PPC_BC_LT, "bltctr" },
	{ PPC_INS_BCTR, PPC_BC_LE, "blectr" },
	{ PPC_INS_BCTR, PPC_BC_EQ, "beqctr" },
	{ PPC_INS_BCTR, PPC_BC_GE, "bgectr" },
	{ PPC_INS_BCTR, PPC_BC_GT, "bgtctr" },
	{ PPC_INS_BCTR, PPC_BC_NE, "bnectr" },
	{ PPC_INS_BCTR, PPC_BC_UN, "bunctr" },
	{ PPC_INS_BCTR, PPC_BC_NU, "bnuctr" },
	{ PPC_INS_BCTR, PPC_BC_SO, "bsoctr" },
	{ PPC_INS_BCTR, PPC_BC_NS, "bnsctr" },

	{ PPC_INS_BCTRL, PPC_BC_LT, "bltctrl" },
	{ PPC_INS_BCTRL, PPC_BC_LE, "blectrl" },
	{ PPC_INS_BCTRL, PPC_BC_EQ, "beqctrl" },
	{ PPC_INS_BCTRL, PPC_BC_GE, "bgectrl" },
	{ PPC_INS_BCTRL, PPC_BC_GT, "bgtctrl" },
	{ PPC_INS_BCTRL, PPC_BC_NE, "bnectrl" },
	{ PPC_INS_BCTRL, PPC_BC_UN, "bunctrl" },
	{ PPC_INS_BCTRL, PPC_BC_NU, "bnuctrl" },
	{ PPC_INS_BCTRL, PPC_BC_SO, "bsoctrl" },
	{ PPC_INS_BCTRL, PPC_BC_NS, "bnsctrl" },

	{ PPC_INS_BL, PPC_BC_LT, "bltl" },
	{ PPC_INS_BL, PPC_BC_LE, "blel" },
	{ PPC_INS_BL, PPC_BC_EQ, "beql" },
	{ PPC_INS_BL, PPC_BC_GE, "bgel" },
	{ PPC_INS_BL, PPC_BC_GT, "bgtl" },
	{ PPC_INS_BL, PPC_BC_NE, "bnel" },
	{ PPC_INS_BL, PPC_BC_UN, "bunl" },
	{ PPC_INS_BL, PPC_BC_NU, "bnul" },
	{ PPC_INS_BL, PPC_BC_SO, "bsol" },
	{ PPC_INS_BL, PPC_BC_NS, "bnsl" },

	{ PPC_INS_BLA, PPC_BC_LT, "bltla" },
	{ PPC_INS_BLA, PPC_BC_LE, "blela" },
	{ PPC_INS_BLA, PPC_BC_EQ, "beqla" },
	{ PPC_INS_BLA, PPC_BC_GE, "bgela" },
	{ PPC_INS_BLA, PPC_BC_GT, "bgtla" },
	{ PPC_INS_BLA, PPC_BC_NE, "bnela" },
	{ PPC_INS_BLA, PPC_BC_UN, "bunla" },
	{ PPC_INS_BLA, PPC_BC_NU, "bnula" },
	{ PPC_INS_BLA, PPC_BC_SO, "bsola" },
	{ PPC_INS_BLA, PPC_BC_NS, "bnsla" },

	{ PPC_INS_BLR, PPC_BC_LT, "bltlr" },
	{ PPC_INS_BLR, PPC_BC_LE, "blelr" },
	{ PPC_INS_BLR, PPC_BC_EQ, "beqlr" },
	{ PPC_INS_BLR, PPC_BC_GE, "bgelr" },
	{ PPC_INS_BLR, PPC_BC_GT, "bgtlr" },
	{ PPC_INS_BLR, PPC_BC_NE, "bnelr" },
	{ PPC_INS_BLR, PPC_BC_UN, "bunlr" },
	{ PPC_INS_BLR, PPC_BC_NU, "bnulr" },
	{ PPC_INS_BLR, PPC_BC_SO, "bsolr" },
	{ PPC_INS_BLR, PPC_BC_NS, "bnslr" },

	{ PPC_INS_BLRL, PPC_BC_LT, "bltlrl" },
	{ PPC_INS_BLRL, PPC_BC_LE, "blelrl" },
	{ PPC_INS_BLRL, PPC_BC_EQ, "beqlrl" },
	{ PPC_INS_BLRL, PPC_BC_GE, "bgelrl" },
	{ PPC_INS_BLRL, PPC_BC_GT, "bgtlrl" },
	{ PPC_INS_BLRL, PPC_BC_NE, "bnelrl" },
	{ PPC_INS_BLRL, PPC_BC_UN, "bunlrl" },
	{ PPC_INS_BLRL, PPC_BC_NU, "bnulrl" },
	{ PPC_INS_BLRL, PPC_BC_SO, "bsolrl" },
	{ PPC_INS_BLRL, PPC_BC_NS, "bnslrl" },
};

// given alias mnemonic, return instruction ID & CC
bool PPC_alias_insn(const char *name, struct ppc_alias *alias)
{
	size_t i;
#ifndef CAPSTONE_DIET
	int x;
#endif

	for(i = 0; i < ARR_SIZE(alias_insn_name_maps); i++) {
		if (!strcmp(name, alias_insn_name_maps[i].mnem)) {
			alias->id = alias_insn_name_maps[i].id;
			alias->cc = alias_insn_name_maps[i].cc;
			return true;
		}
	}

#ifndef CAPSTONE_DIET
	// not really an alias insn
	x = name2id(&insn_name_maps[1], ARR_SIZE(insn_name_maps) - 1, name);
	if (x != -1) {
		alias->id = insn_name_maps[x].id;
		alias->cc = PPC_BC_INVALID;
		return true;
	}
#endif

	// not found
	return false;
}

// list all relative branch instructions
static const unsigned int insn_abs[] = {
	PPC_BA,
	PPC_BCCA,
	PPC_BCCLA,
	PPC_BDNZA,
	PPC_BDNZAm,
	PPC_BDNZAp,
	PPC_BDNZLA,
	PPC_BDNZLAm,
	PPC_BDNZLAp,
	PPC_BDZA,
	PPC_BDZAm,
	PPC_BDZAp,
	PPC_BDZLAm,
	PPC_BDZLAp,
	PPC_BLA,
	PPC_gBCA,
	PPC_gBCLA,
	0
};

// check if this insn is relative branch
bool PPC_abs_branch(cs_struct *h, unsigned int id)
{
	int i;

	for (i = 0; insn_abs[i]; i++) {
		if (id == insn_abs[i]) {
			return true;
		}
	}

	// not found
	return false;
}

#endif
