/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Assembly Writer Source Fragment                                             *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine, http://www.capstone-engine.org */
/* By <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2015 */

/// printInstruction - This method is automatically generated by tablegen
/// from the instruction set description.
static void printInstruction(MCInst *MI, SStream *O, MCRegisterInfo *MRI)
{
  static const uint32_t OpInfo[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    2694U,	// DBG_VALUE
    0U,	// REG_SEQUENCE
    0U,	// COPY
    2687U,	// BUNDLE
    2704U,	// LIFETIME_START
    2674U,	// LIFETIME_END
    0U,	// STACKMAP
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    0U,	// STATEPOINT
    0U,	// FRAME_ALLOC
    6182U,	// ABSv16i8
    553920550U,	// ABSv1i64
    1074272294U,	// ABSv2i32
    1611405350U,	// ABSv2i64
    2148538406U,	// ABSv4i16
    2685671462U,	// ABSv4i32
    3222804518U,	// ABSv8i16
    3759937574U,	// ABSv8i8
    17049662U,	// ADCSWr
    17049662U,	// ADCSXr
    17048298U,	// ADCWr
    17048298U,	// ADCXr
    537400863U,	// ADDHNv2i64_v2i32
    571748634U,	// ADDHNv2i64_v4i32
    1074796063U,	// ADDHNv4i32_v4i16
    1108881690U,	// ADDHNv4i32_v8i16
    1644179738U,	// ADDHNv8i16_v16i8
    1612453407U,	// ADDHNv8i16_v8i8
    2147489464U,	// ADDPv16i8
    2684884664U,	// ADDPv2i32
    537663160U,	// ADDPv2i64
    1610884792U,	// ADDPv2i64p
    3222279864U,	// ADDPv4i16
    1075058360U,	// ADDPv4i32
    1612191416U,	// ADDPv8i16
    3759937208U,	// ADDPv8i8
    17049674U,	// ADDSWri
    0U,	// ADDSWrr
    17049674U,	// ADDSWrs
    17049674U,	// ADDSWrx
    17049674U,	// ADDSXri
    0U,	// ADDSXrr
    17049674U,	// ADDSXrs
    17049674U,	// ADDSXrx
    17049674U,	// ADDSXrx64
    272671U,	// ADDVv16i8v
    2147756319U,	// ADDVv4i16v
    2684627231U,	// ADDVv4i32v
    3221498143U,	// ADDVv8i16v
    3758369055U,	// ADDVv8i8v
    17048359U,	// ADDWri
    0U,	// ADDWrr
    17048359U,	// ADDWrs
    17048359U,	// ADDWrx
    17048359U,	// ADDXri
    0U,	// ADDXrr
    17048359U,	// ADDXrs
    17048359U,	// ADDXrx
    17048359U,	// ADDXrx64
    2147488551U,	// ADDv16i8
    17048359U,	// ADDv1i64
    2684883751U,	// ADDv2i32
    537662247U,	// ADDv2i64
    3222278951U,	// ADDv4i16
    1075057447U,	// ADDv4i32
    1612190503U,	// ADDv8i16
    3759936295U,	// ADDv8i8
    0U,	// ADJCALLSTACKDOWN
    0U,	// ADJCALLSTACKUP
    553920403U,	// ADR
    50603811U,	// ADRP
    33567598U,	// AESDrr
    33567656U,	// AESErr
    4852U,	// AESIMCrr
    4860U,	// AESMCrr
    17049680U,	// ANDSWri
    0U,	// ANDSWrr
    17049680U,	// ANDSWrs
    17049680U,	// ANDSXri
    0U,	// ANDSXrr
    17049680U,	// ANDSXrs
    17048425U,	// ANDWri
    0U,	// ANDWrr
    17048425U,	// ANDWrs
    17048425U,	// ANDXri
    0U,	// ANDXrr
    17048425U,	// ANDXrs
    2147488617U,	// ANDv16i8
    3759936361U,	// ANDv8i8
    17049553U,	// ASRVWr
    17049553U,	// ASRVXr
    16935U,	// B
    67380710U,	// BFMWri
    67380710U,	// BFMXri
    0U,	// BICSWrr
    17049668U,	// BICSWrs
    0U,	// BICSXrr
    17049668U,	// BICSXrs
    0U,	// BICWrr
    17048303U,	// BICWrs
    0U,	// BICXrr
    17048303U,	// BICXrs
    2147488495U,	// BICv16i8
    84423407U,	// BICv2i32
    84947695U,	// BICv4i16
    85209839U,	// BICv4i32
    85471983U,	// BICv8i16
    3759936239U,	// BICv8i8
    2147488704U,	// BIFv16i8
    3759936448U,	// BIFv8i8
    2181052603U,	// BITv16i8
    3793500347U,	// BITv8i8
    17641U,	// BL
    2107319U,	// BLR
    2107279U,	// BR
    21688U,	// BRK
    2181051810U,	// BSLv16i8
    3793499554U,	// BSLv8i8
    27247U,	// Bcc
    100936257U,	// CBNZW
    100936257U,	// CBNZX
    100936242U,	// CBZW
    100936242U,	// CBZX
    17049144U,	// CCMNWi
    17049144U,	// CCMNWr
    17049144U,	// CCMNXi
    17049144U,	// CCMNXr
    17049316U,	// CCMPWi
    17049316U,	// CCMPWr
    17049316U,	// CCMPXi
    17049316U,	// CCMPXr
    2107924U,	// CLREX
    553920604U,	// CLSWr
    553920604U,	// CLSXr
    6236U,	// CLSv16i8
    1074272348U,	// CLSv2i32
    2148538460U,	// CLSv4i16
    2685671516U,	// CLSv4i32
    3222804572U,	// CLSv8i16
    3759937628U,	// CLSv8i8
    553921084U,	// CLZWr
    553921084U,	// CLZXr
    6716U,	// CLZv16i8
    1074272828U,	// CLZv2i32
    2148538940U,	// CLZv4i16
    2685671996U,	// CLZv4i32
    3222805052U,	// CLZv8i16
    3759938108U,	// CLZv8i8
    2147489643U,	// CMEQv16i8
    5995U,	// CMEQv16i8rz
    17049451U,	// CMEQv1i64
    553920363U,	// CMEQv1i64rz
    2684884843U,	// CMEQv2i32
    1074272107U,	// CMEQv2i32rz
    537663339U,	// CMEQv2i64
    1611405163U,	// CMEQv2i64rz
    3222280043U,	// CMEQv4i16
    2148538219U,	// CMEQv4i16rz
    1075058539U,	// CMEQv4i32
    2685671275U,	// CMEQv4i32rz
    1612191595U,	// CMEQv8i16
    3222804331U,	// CMEQv8i16rz
    3759937387U,	// CMEQv8i8
    3759937387U,	// CMEQv8i8rz
    2147488636U,	// CMGEv16i8
    4988U,	// CMGEv16i8rz
    17048444U,	// CMGEv1i64
    553919356U,	// CMGEv1i64rz
    2684883836U,	// CMGEv2i32
    1074271100U,	// CMGEv2i32rz
    537662332U,	// CMGEv2i64
    1611404156U,	// CMGEv2i64rz
    3222279036U,	// CMGEv4i16
    2148537212U,	// CMGEv4i16rz
    1075057532U,	// CMGEv4i32
    2685670268U,	// CMGEv4i32rz
    1612190588U,	// CMGEv8i16
    3222803324U,	// CMGEv8i16rz
    3759936380U,	// CMGEv8i8
    3759936380U,	// CMGEv8i8rz
    2147489972U,	// CMGTv16i8
    6324U,	// CMGTv16i8rz
    17049780U,	// CMGTv1i64
    553920692U,	// CMGTv1i64rz
    2684885172U,	// CMGTv2i32
    1074272436U,	// CMGTv2i32rz
    537663668U,	// CMGTv2i64
    1611405492U,	// CMGTv2i64rz
    3222280372U,	// CMGTv4i16
    2148538548U,	// CMGTv4i16rz
    1075058868U,	// CMGTv4i32
    2685671604U,	// CMGTv4i32rz
    1612191924U,	// CMGTv8i16
    3222804660U,	// CMGTv8i16rz
    3759937716U,	// CMGTv8i8
    3759937716U,	// CMGTv8i8rz
    2147488916U,	// CMHIv16i8
    17048724U,	// CMHIv1i64
    2684884116U,	// CMHIv2i32
    537662612U,	// CMHIv2i64
    3222279316U,	// CMHIv4i16
    1075057812U,	// CMHIv4i32
    1612190868U,	// CMHIv8i16
    3759936660U,	// CMHIv8i8
    2147489878U,	// CMHSv16i8
    17049686U,	// CMHSv1i64
    2684885078U,	// CMHSv2i32
    537663574U,	// CMHSv2i64
    3222280278U,	// CMHSv4i16
    1075058774U,	// CMHSv4i32
    1612191830U,	// CMHSv8i16
    3759937622U,	// CMHSv8i8
    4995U,	// CMLEv16i8rz
    553919363U,	// CMLEv1i64rz
    1074271107U,	// CMLEv2i32rz
    1611404163U,	// CMLEv2i64rz
    2148537219U,	// CMLEv4i16rz
    2685670275U,	// CMLEv4i32rz
    3222803331U,	// CMLEv8i16rz
    3759936387U,	// CMLEv8i8rz
    6342U,	// CMLTv16i8rz
    553920710U,	// CMLTv1i64rz
    1074272454U,	// CMLTv2i32rz
    1611405510U,	// CMLTv2i64rz
    2148538566U,	// CMLTv4i16rz
    2685671622U,	// CMLTv4i32rz
    3222804678U,	// CMLTv8i16rz
    3759937734U,	// CMLTv8i8rz
    2147490013U,	// CMTSTv16i8
    17049821U,	// CMTSTv1i64
    2684885213U,	// CMTSTv2i32
    537663709U,	// CMTSTv2i64
    3222280413U,	// CMTSTv4i16
    1075058909U,	// CMTSTv4i32
    1612191965U,	// CMTSTv8i16
    3759937757U,	// CMTSTv8i8
    6348U,	// CNTv16i8
    3759937740U,	// CNTv8i8
    272763U,	// CPYi16
    537143675U,	// CPYi32
    1074014587U,	// CPYi64
    1610885499U,	// CPYi8
    17048098U,	// CRC32Brr
    17048106U,	// CRC32CBrr
    17048575U,	// CRC32CHrr
    17050039U,	// CRC32CWrr
    17050123U,	// CRC32CXrr
    17048558U,	// CRC32Hrr
    17050017U,	// CRC32Wrr
    17050092U,	// CRC32Xrr
    17048888U,	// CSELWr
    17048888U,	// CSELXr
    17048323U,	// CSINCWr
    17048323U,	// CSINCXr
    17049971U,	// CSINVWr
    17049971U,	// CSINVXr
    17048544U,	// CSNEGWr
    17048544U,	// CSNEGXr
    20524U,	// DCPS1
    20889U,	// DCPS2
    20938U,	// DCPS3
    29235U,	// DMB
    2719U,	// DRPS
    29324U,	// DSB
    553654070U,	// DUPv16i8gpr
    1610618678U,	// DUPv16i8lane
    554178358U,	// DUPv2i32gpr
    537401142U,	// DUPv2i32lane
    554440502U,	// DUPv2i64gpr
    1074534198U,	// DUPv2i64lane
    554702646U,	// DUPv4i16gpr
    1054518U,	// DUPv4i16lane
    554964790U,	// DUPv4i32gpr
    538187574U,	// DUPv4i32lane
    555226934U,	// DUPv8i16gpr
    1578806U,	// DUPv8i16lane
    555489078U,	// DUPv8i8gpr
    1612453686U,	// DUPv8i8lane
    0U,	// EONWrr
    17049150U,	// EONWrs
    0U,	// EONXrr
    17049150U,	// EONXrs
    17049538U,	// EORWri
    0U,	// EORWrr
    17049538U,	// EORWrs
    17049538U,	// EORXri
    0U,	// EORXrr
    17049538U,	// EORXrs
    2147489730U,	// EORv16i8
    3759937474U,	// EORv8i8
    2724U,	// ERET
    17049585U,	// EXTRWrri
    17049585U,	// EXTRXrri
    2147490026U,	// EXTv16i8
    3759937770U,	// EXTv8i8
    0U,	// F128CSEL
    17048340U,	// FABD32
    17048340U,	// FABD64
    2684883732U,	// FABDv2f32
    537662228U,	// FABDv2f64
    1075057428U,	// FABDv4f32
    553920549U,	// FABSDr
    553920549U,	// FABSSr
    1074272293U,	// FABSv2f32
    1611405349U,	// FABSv2f64
    2685671461U,	// FABSv4f32
    17048436U,	// FACGE32
    17048436U,	// FACGE64
    2684883828U,	// FACGEv2f32
    537662324U,	// FACGEv2f64
    1075057524U,	// FACGEv4f32
    17049772U,	// FACGT32
    17049772U,	// FACGT64
    2684885164U,	// FACGTv2f32
    537663660U,	// FACGTv2f64
    1075058860U,	// FACGTv4f32
    17048358U,	// FADDDrr
    2684884663U,	// FADDPv2f32
    537663159U,	// FADDPv2f64
    1074013879U,	// FADDPv2i32p
    1610884791U,	// FADDPv2i64p
    1075058359U,	// FADDPv4f32
    17048358U,	// FADDSrr
    2684883750U,	// FADDv2f32
    537662246U,	// FADDv2f64
    1075057446U,	// FADDv4f32
    17049315U,	// FCCMPDrr
    17048473U,	// FCCMPEDrr
    17048473U,	// FCCMPESrr
    17049315U,	// FCCMPSrr
    17049450U,	// FCMEQ32
    17049450U,	// FCMEQ64
    2164533098U,	// FCMEQv1i32rz
    2164533098U,	// FCMEQv1i64rz
    2684884842U,	// FCMEQv2f32
    537663338U,	// FCMEQv2f64
    2684884842U,	// FCMEQv2i32rz
    3222017898U,	// FCMEQv2i64rz
    1075058538U,	// FCMEQv4f32
    3759413098U,	// FCMEQv4i32rz
    17048443U,	// FCMGE32
    17048443U,	// FCMGE64
    2164532091U,	// FCMGEv1i32rz
    2164532091U,	// FCMGEv1i64rz
    2684883835U,	// FCMGEv2f32
    537662331U,	// FCMGEv2f64
    2684883835U,	// FCMGEv2i32rz
    3222016891U,	// FCMGEv2i64rz
    1075057531U,	// FCMGEv4f32
    3759412091U,	// FCMGEv4i32rz
    17049779U,	// FCMGT32
    17049779U,	// FCMGT64
    2164533427U,	// FCMGTv1i32rz
    2164533427U,	// FCMGTv1i64rz
    2684885171U,	// FCMGTv2f32
    537663667U,	// FCMGTv2f64
    2684885171U,	// FCMGTv2i32rz
    3222018227U,	// FCMGTv2i64rz
    1075058867U,	// FCMGTv4f32
    3759413427U,	// FCMGTv4i32rz
    2164532098U,	// FCMLEv1i32rz
    2164532098U,	// FCMLEv1i64rz
    2684883842U,	// FCMLEv2i32rz
    3222016898U,	// FCMLEv2i64rz
    3759412098U,	// FCMLEv4i32rz
    2164533445U,	// FCMLTv1i32rz
    2164533445U,	// FCMLTv1i64rz
    2684885189U,	// FCMLTv2i32rz
    3222018245U,	// FCMLTv2i64rz
    3759413445U,	// FCMLTv4i32rz
    2369258U,	// FCMPDri
    553920234U,	// FCMPDrr
    2368417U,	// FCMPEDri
    553919393U,	// FCMPEDrr
    2368417U,	// FCMPESri
    553919393U,	// FCMPESrr
    2369258U,	// FCMPSri
    553920234U,	// FCMPSrr
    17048887U,	// FCSELDrrr
    17048887U,	// FCSELSrrr
    553920541U,	// FCVTASUWDr
    553920541U,	// FCVTASUWSr
    553920541U,	// FCVTASUXDr
    553920541U,	// FCVTASUXSr
    553920541U,	// FCVTASv1i32
    553920541U,	// FCVTASv1i64
    1074272285U,	// FCVTASv2f32
    1611405341U,	// FCVTASv2f64
    2685671453U,	// FCVTASv4f32
    553920751U,	// FCVTAUUWDr
    553920751U,	// FCVTAUUWSr
    553920751U,	// FCVTAUUXDr
    553920751U,	// FCVTAUUXSr
    553920751U,	// FCVTAUv1i32
    553920751U,	// FCVTAUv1i64
    1074272495U,	// FCVTAUv2f32
    1611405551U,	// FCVTAUv2f64
    2685671663U,	// FCVTAUv4f32
    553920740U,	// FCVTDHr
    553920740U,	// FCVTDSr
    553920740U,	// FCVTHDr
    553920740U,	// FCVTHSr
    1074533828U,	// FCVTLv2i32
    2148799940U,	// FCVTLv4i16
    2685145352U,	// FCVTLv4i32
    3222540552U,	// FCVTLv8i16
    553920615U,	// FCVTMSUWDr
    553920615U,	// FCVTMSUWSr
    553920615U,	// FCVTMSUXDr
    553920615U,	// FCVTMSUXSr
    553920615U,	// FCVTMSv1i32
    553920615U,	// FCVTMSv1i64
    1074272359U,	// FCVTMSv2f32
    1611405415U,	// FCVTMSv2f64
    2685671527U,	// FCVTMSv4f32
    553920767U,	// FCVTMUUWDr
    553920767U,	// FCVTMUUWSr
    553920767U,	// FCVTMUUXDr
    553920767U,	// FCVTMUUXSr
    553920767U,	// FCVTMUv1i32
    553920767U,	// FCVTMUv1i64
    1074272511U,	// FCVTMUv2f32
    1611405567U,	// FCVTMUv2f64
    2685671679U,	// FCVTMUv4f32
    553920628U,	// FCVTNSUWDr
    553920628U,	// FCVTNSUWSr
    553920628U,	// FCVTNSUXDr
    553920628U,	// FCVTNSUXSr
    553920628U,	// FCVTNSv1i32
    553920628U,	// FCVTNSv1i64
    1074272372U,	// FCVTNSv2f32
    1611405428U,	// FCVTNSv2f64
    2685671540U,	// FCVTNSv4f32
    553920775U,	// FCVTNUUWDr
    553920775U,	// FCVTNUUWSr
    553920775U,	// FCVTNUUXDr
    553920775U,	// FCVTNUUXSr
    553920775U,	// FCVTNUv1i32
    553920775U,	// FCVTNUv1i64
    1074272519U,	// FCVTNUv2f32
    1611405575U,	// FCVTNUv2f64
    2685671687U,	// FCVTNUv4f32
    1611142770U,	// FCVTNv2i32
    2685408882U,	// FCVTNv4i16
    1645490510U,	// FCVTNv4i32
    2719494478U,	// FCVTNv8i16
    553920644U,	// FCVTPSUWDr
    553920644U,	// FCVTPSUWSr
    553920644U,	// FCVTPSUXDr
    553920644U,	// FCVTPSUXSr
    553920644U,	// FCVTPSv1i32
    553920644U,	// FCVTPSv1i64
    1074272388U,	// FCVTPSv2f32
    1611405444U,	// FCVTPSv2f64
    2685671556U,	// FCVTPSv4f32
    553920783U,	// FCVTPUUWDr
    553920783U,	// FCVTPUUWSr
    553920783U,	// FCVTPUUXDr
    553920783U,	// FCVTPUUXSr
    553920783U,	// FCVTPUv1i32
    553920783U,	// FCVTPUv1i64
    1074272527U,	// FCVTPUv2f32
    1611405583U,	// FCVTPUv2f64
    2685671695U,	// FCVTPUv4f32
    553920740U,	// FCVTSDr
    553920740U,	// FCVTSHr
    553920168U,	// FCVTXNv1i64
    1611142824U,	// FCVTXNv2f32
    1645490564U,	// FCVTXNv4f32
    17049759U,	// FCVTZSSWDri
    17049759U,	// FCVTZSSWSri
    17049759U,	// FCVTZSSXDri
    17049759U,	// FCVTZSSXSri
    553920671U,	// FCVTZSUWDr
    553920671U,	// FCVTZSUWSr
    553920671U,	// FCVTZSUXDr
    553920671U,	// FCVTZSUXSr
    17049759U,	// FCVTZS_IntSWDri
    17049759U,	// FCVTZS_IntSWSri
    17049759U,	// FCVTZS_IntSXDri
    17049759U,	// FCVTZS_IntSXSri
    553920671U,	// FCVTZS_IntUWDr
    553920671U,	// FCVTZS_IntUWSr
    553920671U,	// FCVTZS_IntUXDr
    553920671U,	// FCVTZS_IntUXSr
    1074272415U,	// FCVTZS_Intv2f32
    1611405471U,	// FCVTZS_Intv2f64
    2685671583U,	// FCVTZS_Intv4f32
    17049759U,	// FCVTZSd
    17049759U,	// FCVTZSs
    553920671U,	// FCVTZSv1i32
    553920671U,	// FCVTZSv1i64
    1074272415U,	// FCVTZSv2f32
    1611405471U,	// FCVTZSv2f64
    2684885151U,	// FCVTZSv2i32_shift
    537663647U,	// FCVTZSv2i64_shift
    2685671583U,	// FCVTZSv4f32
    1075058847U,	// FCVTZSv4i32_shift
    17049879U,	// FCVTZUSWDri
    17049879U,	// FCVTZUSWSri
    17049879U,	// FCVTZUSXDri
    17049879U,	// FCVTZUSXSri
    553920791U,	// FCVTZUUWDr
    553920791U,	// FCVTZUUWSr
    553920791U,	// FCVTZUUXDr
    553920791U,	// FCVTZUUXSr
    17049879U,	// FCVTZU_IntSWDri
    17049879U,	// FCVTZU_IntSWSri
    17049879U,	// FCVTZU_IntSXDri
    17049879U,	// FCVTZU_IntSXSri
    553920791U,	// FCVTZU_IntUWDr
    553920791U,	// FCVTZU_IntUWSr
    553920791U,	// FCVTZU_IntUXDr
    553920791U,	// FCVTZU_IntUXSr
    1074272535U,	// FCVTZU_Intv2f32
    1611405591U,	// FCVTZU_Intv2f64
    2685671703U,	// FCVTZU_Intv4f32
    17049879U,	// FCVTZUd
    17049879U,	// FCVTZUs
    553920791U,	// FCVTZUv1i32
    553920791U,	// FCVTZUv1i64
    1074272535U,	// FCVTZUv2f32
    1611405591U,	// FCVTZUv2f64
    2684885271U,	// FCVTZUv2i32_shift
    537663767U,	// FCVTZUv2i64_shift
    2685671703U,	// FCVTZUv4f32
    1075058967U,	// FCVTZUv4i32_shift
    17049898U,	// FDIVDrr
    17049898U,	// FDIVSrr
    2684885290U,	// FDIVv2f32
    537663786U,	// FDIVv2f64
    1075058986U,	// FDIVv4f32
    17048394U,	// FMADDDrrr
    17048394U,	// FMADDSrrr
    17050100U,	// FMAXDrr
    17049087U,	// FMAXNMDrr
    2684884729U,	// FMAXNMPv2f32
    537663225U,	// FMAXNMPv2f64
    1074013945U,	// FMAXNMPv2i32p
    1610884857U,	// FMAXNMPv2i64p
    1075058425U,	// FMAXNMPv4f32
    17049087U,	// FMAXNMSrr
    2684627285U,	// FMAXNMVv4i32v
    2684884479U,	// FMAXNMv2f32
    537662975U,	// FMAXNMv2f64
    1075058175U,	// FMAXNMv4f32
    2684884802U,	// FMAXPv2f32
    537663298U,	// FMAXPv2f64
    1074014018U,	// FMAXPv2i32p
    1610884930U,	// FMAXPv2i64p
    1075058498U,	// FMAXPv4f32
    17050100U,	// FMAXSrr
    2684627340U,	// FMAXVv4i32v
    2684885492U,	// FMAXv2f32
    537663988U,	// FMAXv2f64
    1075059188U,	// FMAXv4f32
    17049126U,	// FMINDrr
    17049079U,	// FMINNMDrr
    2684884720U,	// FMINNMPv2f32
    537663216U,	// FMINNMPv2f64
    1074013936U,	// FMINNMPv2i32p
    1610884848U,	// FMINNMPv2i64p
    1075058416U,	// FMINNMPv4f32
    17049079U,	// FMINNMSrr
    2684627276U,	// FMINNMVv4i32v
    2684884471U,	// FMINNMv2f32
    537662967U,	// FMINNMv2f64
    1075058167U,	// FMINNMv4f32
    2684884744U,	// FMINPv2f32
    537663240U,	// FMINPv2f64
    1074013960U,	// FMINPv2i32p
    1610884872U,	// FMINPv2i64p
    1075058440U,	// FMINPv4f32
    17049126U,	// FMINSrr
    2684627294U,	// FMINVv4i32v
    2684884518U,	// FMINv2f32
    537663014U,	// FMINv2f64
    1075058214U,	// FMINv4f32
    67404282U,	// FMLAv1i32_indexed
    67404282U,	// FMLAv1i64_indexed
    2718446074U,	// FMLAv2f32
    571224570U,	// FMLAv2f64
    2718446074U,	// FMLAv2i32_indexed
    571224570U,	// FMLAv2i64_indexed
    1108619770U,	// FMLAv4f32
    1108619770U,	// FMLAv4i32_indexed
    67405921U,	// FMLSv1i32_indexed
    67405921U,	// FMLSv1i64_indexed
    2718447713U,	// FMLSv2f32
    571226209U,	// FMLSv2f64
    2718447713U,	// FMLSv2i32_indexed
    571226209U,	// FMLSv2i64_indexed
    1108621409U,	// FMLSv4f32
    1108621409U,	// FMLSv4i32_indexed
    1074014586U,	// FMOVDXHighr
    553920890U,	// FMOVDXr
    117713274U,	// FMOVDi
    553920890U,	// FMOVDr
    553920890U,	// FMOVSWr
    117713274U,	// FMOVSi
    553920890U,	// FMOVSr
    553920890U,	// FMOVWSr
    556276090U,	// FMOVXDHighr
    553920890U,	// FMOVXDr
    117971322U,	// FMOVv2f32_ns
    118233466U,	// FMOVv2f64_ns
    118757754U,	// FMOVv4f32_ns
    17048257U,	// FMSUBDrrr
    17048257U,	// FMSUBSrrr
    17049035U,	// FMULDrr
    17049035U,	// FMULSrr
    17050139U,	// FMULX32
    17050139U,	// FMULX64
    17050139U,	// FMULXv1i32_indexed
    17050139U,	// FMULXv1i64_indexed
    2684885531U,	// FMULXv2f32
    537664027U,	// FMULXv2f64
    2684885531U,	// FMULXv2i32_indexed
    537664027U,	// FMULXv2i64_indexed
    1075059227U,	// FMULXv4f32
    1075059227U,	// FMULXv4i32_indexed
    17049035U,	// FMULv1i32_indexed
    17049035U,	// FMULv1i64_indexed
    2684884427U,	// FMULv2f32
    537662923U,	// FMULv2f64
    2684884427U,	// FMULv2i32_indexed
    537662923U,	// FMULv2i64_indexed
    1075058123U,	// FMULv4f32
    1075058123U,	// FMULv4i32_indexed
    553919443U,	// FNEGDr
    553919443U,	// FNEGSr
    1074271187U,	// FNEGv2f32
    1611404243U,	// FNEGv2f64
    2685670355U,	// FNEGv4f32
    17048401U,	// FNMADDDrrr
    17048401U,	// FNMADDSrrr
    17048264U,	// FNMSUBDrrr
    17048264U,	// FNMSUBSrrr
    17049041U,	// FNMULDrr
    17049041U,	// FNMULSrr
    553919369U,	// FRECPEv1i32
    553919369U,	// FRECPEv1i64
    1074271113U,	// FRECPEv2f32
    1611404169U,	// FRECPEv2f64
    2685670281U,	// FRECPEv4f32
    17049724U,	// FRECPS32
    17049724U,	// FRECPS64
    2684885116U,	// FRECPSv2f32
    537663612U,	// FRECPSv2f64
    1075058812U,	// FRECPSv4f32
    553921058U,	// FRECPXv1i32
    553921058U,	// FRECPXv1i64
    553919002U,	// FRINTADr
    553919002U,	// FRINTASr
    1074270746U,	// FRINTAv2f32
    1611403802U,	// FRINTAv2f64
    2685669914U,	// FRINTAv4f32
    553919658U,	// FRINTIDr
    553919658U,	// FRINTISr
    1074271402U,	// FRINTIv2f32
    1611404458U,	// FRINTIv2f64
    2685670570U,	// FRINTIv4f32
    553920007U,	// FRINTMDr
    553920007U,	// FRINTMSr
    1074271751U,	// FRINTMv2f32
    1611404807U,	// FRINTMv2f64
    2685670919U,	// FRINTMv4f32
    553920106U,	// FRINTNDr
    553920106U,	// FRINTNSr
    1074271850U,	// FRINTNv2f32
    1611404906U,	// FRINTNv2f64
    2685671018U,	// FRINTNv4f32
    553920297U,	// FRINTPDr
    553920297U,	// FRINTPSr
    1074272041U,	// FRINTPv2f32
    1611405097U,	// FRINTPv2f64
    2685671209U,	// FRINTPv4f32
    553921066U,	// FRINTXDr
    553921066U,	// FRINTXSr
    1074272810U,	// FRINTXv2f32
    1611405866U,	// FRINTXv2f64
    2685671978U,	// FRINTXv4f32
    553921101U,	// FRINTZDr
    553921101U,	// FRINTZSr
    1074272845U,	// FRINTZv2f32
    1611405901U,	// FRINTZv2f64
    2685672013U,	// FRINTZv4f32
    553919406U,	// FRSQRTEv1i32
    553919406U,	// FRSQRTEv1i64
    1074271150U,	// FRSQRTEv2f32
    1611404206U,	// FRSQRTEv2f64
    2685670318U,	// FRSQRTEv4f32
    17049745U,	// FRSQRTS32
    17049745U,	// FRSQRTS64
    2684885137U,	// FRSQRTSv2f32
    537663633U,	// FRSQRTSv2f64
    1075058833U,	// FRSQRTSv4f32
    553920726U,	// FSQRTDr
    553920726U,	// FSQRTSr
    1074272470U,	// FSQRTv2f32
    1611405526U,	// FSQRTv2f64
    2685671638U,	// FSQRTv4f32
    17048237U,	// FSUBDrr
    17048237U,	// FSUBSrr
    2684883629U,	// FSUBv2f32
    537662125U,	// FSUBv2f64
    1075057325U,	// FSUBv4f32
    23145U,	// HINT
    22720U,	// HLT
    21258U,	// HVC
    137115759U,	// INSvi16gpr
    153892975U,	// INSvi16lane
    137377903U,	// INSvi32gpr
    691026031U,	// INSvi32lane
    136853615U,	// INSvi64gpr
    1227372655U,	// INSvi64lane
    137640047U,	// INSvi8gpr
    1765029999U,	// INSvi8lane
    29329U,	// ISB
    36885U,	// LD1Fourv16b
    3710997U,	// LD1Fourv16b_POST
    45077U,	// LD1Fourv1d
    3981333U,	// LD1Fourv1d_POST
    53269U,	// LD1Fourv2d
    3727381U,	// LD1Fourv2d_POST
    61461U,	// LD1Fourv2s
    3997717U,	// LD1Fourv2s_POST
    69653U,	// LD1Fourv4h
    4005909U,	// LD1Fourv4h_POST
    77845U,	// LD1Fourv4s
    3751957U,	// LD1Fourv4s_POST
    86037U,	// LD1Fourv8b
    4022293U,	// LD1Fourv8b_POST
    94229U,	// LD1Fourv8h
    3768341U,	// LD1Fourv8h_POST
    36885U,	// LD1Onev16b
    4235285U,	// LD1Onev16b_POST
    45077U,	// LD1Onev1d
    4505621U,	// LD1Onev1d_POST
    53269U,	// LD1Onev2d
    4251669U,	// LD1Onev2d_POST
    61461U,	// LD1Onev2s
    4522005U,	// LD1Onev2s_POST
    69653U,	// LD1Onev4h
    4530197U,	// LD1Onev4h_POST
    77845U,	// LD1Onev4s
    4276245U,	// LD1Onev4s_POST
    86037U,	// LD1Onev8b
    4546581U,	// LD1Onev8b_POST
    94229U,	// LD1Onev8h
    4292629U,	// LD1Onev8h_POST
    38769U,	// LD1Rv16b
    4761457U,	// LD1Rv16b_POST
    46961U,	// LD1Rv1d
    4507505U,	// LD1Rv1d_POST
    55153U,	// LD1Rv2d
    4515697U,	// LD1Rv2d_POST
    63345U,	// LD1Rv2s
    5048177U,	// LD1Rv2s_POST
    71537U,	// LD1Rv4h
    5318513U,	// LD1Rv4h_POST
    79729U,	// LD1Rv4s
    5064561U,	// LD1Rv4s_POST
    87921U,	// LD1Rv8b
    4810609U,	// LD1Rv8b_POST
    96113U,	// LD1Rv8h
    5343089U,	// LD1Rv8h_POST
    36885U,	// LD1Threev16b
    5546005U,	// LD1Threev16b_POST
    45077U,	// LD1Threev1d
    5816341U,	// LD1Threev1d_POST
    53269U,	// LD1Threev2d
    5562389U,	// LD1Threev2d_POST
    61461U,	// LD1Threev2s
    5832725U,	// LD1Threev2s_POST
    69653U,	// LD1Threev4h
    5840917U,	// LD1Threev4h_POST
    77845U,	// LD1Threev4s
    5586965U,	// LD1Threev4s_POST
    86037U,	// LD1Threev8b
    5857301U,	// LD1Threev8b_POST
    94229U,	// LD1Threev8h
    5603349U,	// LD1Threev8h_POST
    36885U,	// LD1Twov16b
    3973141U,	// LD1Twov16b_POST
    45077U,	// LD1Twov1d
    4243477U,	// LD1Twov1d_POST
    53269U,	// LD1Twov2d
    3989525U,	// LD1Twov2d_POST
    61461U,	// LD1Twov2s
    4259861U,	// LD1Twov2s_POST
    69653U,	// LD1Twov4h
    4268053U,	// LD1Twov4h_POST
    77845U,	// LD1Twov4s
    4014101U,	// LD1Twov4s_POST
    86037U,	// LD1Twov8b
    4284437U,	// LD1Twov8b_POST
    94229U,	// LD1Twov8h
    4030485U,	// LD1Twov8h_POST
    6131733U,	// LD1i16
    6397973U,	// LD1i16_POST
    6139925U,	// LD1i32
    6668309U,	// LD1i32_POST
    6148117U,	// LD1i64
    6938645U,	// LD1i64_POST
    6156309U,	// LD1i8
    7208981U,	// LD1i8_POST
    38775U,	// LD2Rv16b
    5285751U,	// LD2Rv16b_POST
    46967U,	// LD2Rv1d
    4245367U,	// LD2Rv1d_POST
    55159U,	// LD2Rv2d
    4253559U,	// LD2Rv2d_POST
    63351U,	// LD2Rv2s
    4523895U,	// LD2Rv2s_POST
    71543U,	// LD2Rv4h
    5056375U,	// LD2Rv4h_POST
    79735U,	// LD2Rv4s
    4540279U,	// LD2Rv4s_POST
    87927U,	// LD2Rv8b
    5334903U,	// LD2Rv8b_POST
    96119U,	// LD2Rv8h
    5080951U,	// LD2Rv8h_POST
    36947U,	// LD2Twov16b
    3973203U,	// LD2Twov16b_POST
    53331U,	// LD2Twov2d
    3989587U,	// LD2Twov2d_POST
    61523U,	// LD2Twov2s
    4259923U,	// LD2Twov2s_POST
    69715U,	// LD2Twov4h
    4268115U,	// LD2Twov4h_POST
    77907U,	// LD2Twov4s
    4014163U,	// LD2Twov4s_POST
    86099U,	// LD2Twov8b
    4284499U,	// LD2Twov8b_POST
    94291U,	// LD2Twov8h
    4030547U,	// LD2Twov8h_POST
    6131795U,	// LD2i16
    6660179U,	// LD2i16_POST
    6139987U,	// LD2i32
    6930515U,	// LD2i32_POST
    6148179U,	// LD2i64
    7462995U,	// LD2i64_POST
    6156371U,	// LD2i8
    6422611U,	// LD2i8_POST
    38781U,	// LD3Rv16b
    7645053U,	// LD3Rv16b_POST
    46973U,	// LD3Rv1d
    5818237U,	// LD3Rv1d_POST
    55165U,	// LD3Rv2d
    5826429U,	// LD3Rv2d_POST
    63357U,	// LD3Rv2s
    7931773U,	// LD3Rv2s_POST
    71549U,	// LD3Rv4h
    8202109U,	// LD3Rv4h_POST
    79741U,	// LD3Rv4s
    7948157U,	// LD3Rv4s_POST
    87933U,	// LD3Rv8b
    7694205U,	// LD3Rv8b_POST
    96125U,	// LD3Rv8h
    8226685U,	// LD3Rv8h_POST
    37317U,	// LD3Threev16b
    5546437U,	// LD3Threev16b_POST
    53701U,	// LD3Threev2d
    5562821U,	// LD3Threev2d_POST
    61893U,	// LD3Threev2s
    5833157U,	// LD3Threev2s_POST
    70085U,	// LD3Threev4h
    5841349U,	// LD3Threev4h_POST
    78277U,	// LD3Threev4s
    5587397U,	// LD3Threev4s_POST
    86469U,	// LD3Threev8b
    5857733U,	// LD3Threev8b_POST
    94661U,	// LD3Threev8h
    5603781U,	// LD3Threev8h_POST
    6132165U,	// LD3i16
    8495557U,	// LD3i16_POST
    6140357U,	// LD3i32
    8765893U,	// LD3i32_POST
    6148549U,	// LD3i64
    9036229U,	// LD3i64_POST
    6156741U,	// LD3i8
    9306565U,	// LD3i8_POST
    37341U,	// LD4Fourv16b
    3711453U,	// LD4Fourv16b_POST
    53725U,	// LD4Fourv2d
    3727837U,	// LD4Fourv2d_POST
    61917U,	// LD4Fourv2s
    3998173U,	// LD4Fourv2s_POST
    70109U,	// LD4Fourv4h
    4006365U,	// LD4Fourv4h_POST
    78301U,	// LD4Fourv4s
    3752413U,	// LD4Fourv4s_POST
    86493U,	// LD4Fourv8b
    4022749U,	// LD4Fourv8b_POST
    94685U,	// LD4Fourv8h
    3768797U,	// LD4Fourv8h_POST
    38787U,	// LD4Rv16b
    5023619U,	// LD4Rv16b_POST
    46979U,	// LD4Rv1d
    3983235U,	// LD4Rv1d_POST
    55171U,	// LD4Rv2d
    3991427U,	// LD4Rv2d_POST
    63363U,	// LD4Rv2s
    4261763U,	// LD4Rv2s_POST
    71555U,	// LD4Rv4h
    4532099U,	// LD4Rv4h_POST
    79747U,	// LD4Rv4s
    4278147U,	// LD4Rv4s_POST
    87939U,	// LD4Rv8b
    5072771U,	// LD4Rv8b_POST
    96131U,	// LD4Rv8h
    4556675U,	// LD4Rv8h_POST
    6132189U,	// LD4i16
    6922717U,	// LD4i16_POST
    6140381U,	// LD4i32
    7455197U,	// LD4i32_POST
    6148573U,	// LD4i64
    9560541U,	// LD4i64_POST
    6156765U,	// LD4i8
    6685149U,	// LD4i8_POST
    26485304U,	// LDARB
    26485801U,	// LDARH
    26486665U,	// LDARW
    26486665U,	// LDARX
    553920315U,	// LDAXPW
    553920315U,	// LDAXPX
    26485358U,	// LDAXRB
    26485855U,	// LDAXRH
    26486787U,	// LDAXRW
    26486787U,	// LDAXRX
    553920258U,	// LDNPDi
    553920258U,	// LDNPQi
    553920258U,	// LDNPSi
    553920258U,	// LDNPWi
    553920258U,	// LDNPXi
    553920190U,	// LDPDi
    604276414U,	// LDPDpost
    604276414U,	// LDPDpre
    553920190U,	// LDPQi
    604276414U,	// LDPQpost
    604276414U,	// LDPQpre
    553920974U,	// LDPSWi
    604277198U,	// LDPSWpost
    604277198U,	// LDPSWpre
    553920190U,	// LDPSi
    604276414U,	// LDPSpost
    604276414U,	// LDPSpre
    553920190U,	// LDPWi
    604276414U,	// LDPWpost
    604276414U,	// LDPWpre
    553920190U,	// LDPXi
    604276414U,	// LDPXpost
    604276414U,	// LDPXpre
    1150583359U,	// LDRBBpost
    76841535U,	// LDRBBpre
    26485311U,	// LDRBBroW
    26485311U,	// LDRBBroX
    26485311U,	// LDRBBui
    1150584728U,	// LDRBpost
    76842904U,	// LDRBpre
    26486680U,	// LDRBroW
    26486680U,	// LDRBroX
    26486680U,	// LDRBui
    100935576U,	// LDRDl
    1150584728U,	// LDRDpost
    76842904U,	// LDRDpre
    26486680U,	// LDRDroW
    26486680U,	// LDRDroX
    26486680U,	// LDRDui
    1150583856U,	// LDRHHpost
    76842032U,	// LDRHHpre
    26485808U,	// LDRHHroW
    26485808U,	// LDRHHroX
    26485808U,	// LDRHHui
    1150584728U,	// LDRHpost
    76842904U,	// LDRHpre
    26486680U,	// LDRHroW
    26486680U,	// LDRHroX
    26486680U,	// LDRHui
    100935576U,	// LDRQl
    1150584728U,	// LDRQpost
    76842904U,	// LDRQpre
    26486680U,	// LDRQroW
    26486680U,	// LDRQroX
    26486680U,	// LDRQui
    1150583446U,	// LDRSBWpost
    76841622U,	// LDRSBWpre
    26485398U,	// LDRSBWroW
    26485398U,	// LDRSBWroX
    26485398U,	// LDRSBWui
    1150583446U,	// LDRSBXpost
    76841622U,	// LDRSBXpre
    26485398U,	// LDRSBXroW
    26485398U,	// LDRSBXroX
    26485398U,	// LDRSBXui
    1150583933U,	// LDRSHWpost
    76842109U,	// LDRSHWpre
    26485885U,	// LDRSHWroW
    26485885U,	// LDRSHWroX
    26485885U,	// LDRSHWui
    1150583933U,	// LDRSHXpost
    76842109U,	// LDRSHXpre
    26485885U,	// LDRSHXroW
    26485885U,	// LDRSHXroX
    26485885U,	// LDRSHXui
    100936149U,	// LDRSWl
    1150585301U,	// LDRSWpost
    76843477U,	// LDRSWpre
    26487253U,	// LDRSWroW
    26487253U,	// LDRSWroX
    26487253U,	// LDRSWui
    100935576U,	// LDRSl
    1150584728U,	// LDRSpost
    76842904U,	// LDRSpre
    26486680U,	// LDRSroW
    26486680U,	// LDRSroX
    26486680U,	// LDRSui
    100935576U,	// LDRWl
    1150584728U,	// LDRWpost
    76842904U,	// LDRWpre
    26486680U,	// LDRWroW
    26486680U,	// LDRWroX
    26486680U,	// LDRWui
    100935576U,	// LDRXl
    1150584728U,	// LDRXpost
    76842904U,	// LDRXpre
    26486680U,	// LDRXroW
    26486680U,	// LDRXroX
    26486680U,	// LDRXui
    26485324U,	// LDTRBi
    26485821U,	// LDTRHi
    26485405U,	// LDTRSBWi
    26485405U,	// LDTRSBXi
    26485892U,	// LDTRSHWi
    26485892U,	// LDTRSHXi
    26487260U,	// LDTRSWi
    26486752U,	// LDTRWi
    26486752U,	// LDTRXi
    26485344U,	// LDURBBi
    26486775U,	// LDURBi
    26486775U,	// LDURDi
    26485841U,	// LDURHHi
    26486775U,	// LDURHi
    26486775U,	// LDURQi
    26485413U,	// LDURSBWi
    26485413U,	// LDURSBXi
    26485900U,	// LDURSHWi
    26485900U,	// LDURSHXi
    26487268U,	// LDURSWi
    26486775U,	// LDURSi
    26486775U,	// LDURWi
    26486775U,	// LDURXi
    553920343U,	// LDXPW
    553920343U,	// LDXPX
    26485366U,	// LDXRB
    26485863U,	// LDXRH
    26486794U,	// LDXRW
    26486794U,	// LDXRX
    0U,	// LOADgot
    17049003U,	// LSLVWr
    17049003U,	// LSLVXr
    17049558U,	// LSRVWr
    17049558U,	// LSRVXr
    17048395U,	// MADDWrrr
    17048395U,	// MADDXrrr
    2181050875U,	// MLAv16i8
    2718446075U,	// MLAv2i32
    2718446075U,	// MLAv2i32_indexed
    3255841275U,	// MLAv4i16
    3255841275U,	// MLAv4i16_indexed
    1108619771U,	// MLAv4i32
    1108619771U,	// MLAv4i32_indexed
    1645752827U,	// MLAv8i16
    1645752827U,	// MLAv8i16_indexed
    3793498619U,	// MLAv8i8
    2181052514U,	// MLSv16i8
    2718447714U,	// MLSv2i32
    2718447714U,	// MLSv2i32_indexed
    3255842914U,	// MLSv4i16
    3255842914U,	// MLSv4i16_indexed
    1108621410U,	// MLSv4i32
    1108621410U,	// MLSv4i32_indexed
    1645754466U,	// MLSv8i16
    1645754466U,	// MLSv8i16_indexed
    3793500258U,	// MLSv8i8
    168043698U,	// MOVID
    721425586U,	// MOVIv16b_ns
    168563890U,	// MOVIv2d_ns
    1795691698U,	// MOVIv2i32
    1795691698U,	// MOVIv2s_msl
    1796215986U,	// MOVIv4i16
    1796478130U,	// MOVIv4i32
    1796478130U,	// MOVIv4s_msl
    723260594U,	// MOVIv8b_ns
    1796740274U,	// MOVIv8i16
    84157629U,	// MOVKWi
    84157629U,	// MOVKXi
    1795434146U,	// MOVNWi
    1795434146U,	// MOVNXi
    1795435093U,	// MOVZWi
    1795435093U,	// MOVZXi
    0U,	// MOVaddr
    0U,	// MOVaddrBA
    0U,	// MOVaddrCP
    0U,	// MOVaddrEXT
    0U,	// MOVaddrJT
    0U,	// MOVaddrTLS
    0U,	// MOVi32imm
    0U,	// MOVi64imm
    201599116U,	// MRS
    137179U,	// MSR
    141275U,	// MSRpstate
    17048258U,	// MSUBWrrr
    17048258U,	// MSUBXrrr
    2147489228U,	// MULv16i8
    2684884428U,	// MULv2i32
    2684884428U,	// MULv2i32_indexed
    3222279628U,	// MULv4i16
    3222279628U,	// MULv4i16_indexed
    1075058124U,	// MULv4i32
    1075058124U,	// MULv4i32_indexed
    1612191180U,	// MULv8i16
    1612191180U,	// MULv8i16_indexed
    3759936972U,	// MULv8i8
    1795691679U,	// MVNIv2i32
    1795691679U,	// MVNIv2s_msl
    1796215967U,	// MVNIv4i16
    1796478111U,	// MVNIv4i32
    1796478111U,	// MVNIv4s_msl
    1796740255U,	// MVNIv8i16
    5076U,	// NEGv16i8
    553919444U,	// NEGv1i64
    1074271188U,	// NEGv2i32
    1611404244U,	// NEGv2i64
    2148537300U,	// NEGv4i16
    2685670356U,	// NEGv4i32
    3222803412U,	// NEGv8i16
    3759936468U,	// NEGv8i8
    6353U,	// NOTv16i8
    3759937745U,	// NOTv8i8
    0U,	// ORNWrr
    17049189U,	// ORNWrs
    0U,	// ORNXrr
    17049189U,	// ORNXrs
    2147489381U,	// ORNv16i8
    3759937125U,	// ORNv8i8
    17049548U,	// ORRWri
    0U,	// ORRWrr
    17049548U,	// ORRWrs
    17049548U,	// ORRXri
    0U,	// ORRXrr
    17049548U,	// ORRXrs
    2147489740U,	// ORRv16i8
    84424652U,	// ORRv2i32
    84948940U,	// ORRv4i16
    85211084U,	// ORRv4i32
    85473228U,	// ORRv8i16
    3759937484U,	// ORRv8i8
    2149060822U,	// PMULLv16i8
    228070797U,	// PMULLv1i64
    244846806U,	// PMULLv2i64
    3759674765U,	// PMULLv8i8
    2147489240U,	// PMULv16i8
    3759936984U,	// PMULv8i8
    101070321U,	// PRFMl
    26621425U,	// PRFMroW
    26621425U,	// PRFMroX
    26621425U,	// PRFMui
    26621455U,	// PRFUMi
    537400862U,	// RADDHNv2i64_v2i32
    571748633U,	// RADDHNv2i64_v4i32
    1074796062U,	// RADDHNv4i32_v4i16
    1108881689U,	// RADDHNv4i32_v8i16
    1644179737U,	// RADDHNv8i16_v16i8
    1612453406U,	// RADDHNv8i16_v8i8
    553920698U,	// RBITWr
    553920698U,	// RBITXr
    6330U,	// RBITv16i8
    3759937722U,	// RBITv8i8
    2107559U,	// RET
    0U,	// RET_ReallyLR
    553918951U,	// REV16Wr
    553918951U,	// REV16Xr
    4583U,	// REV16v16i8
    3759935975U,	// REV16v8i8
    553918540U,	// REV32Xr
    4172U,	// REV32v16i8
    2148536396U,	// REV32v4i16
    3222802508U,	// REV32v8i16
    3759935564U,	// REV32v8i8
    4566U,	// REV64v16i8
    1074270678U,	// REV64v2i32
    2148536790U,	// REV64v4i16
    2685669846U,	// REV64v4i32
    3222802902U,	// REV64v8i16
    3759935958U,	// REV64v8i8
    553920805U,	// REVWr
    553920805U,	// REVXr
    17049543U,	// RORVWr
    17049543U,	// RORVXr
    1644179766U,	// RSHRNv16i8_shift
    537400917U,	// RSHRNv2i32_shift
    1074796117U,	// RSHRNv4i16_shift
    571748662U,	// RSHRNv4i32_shift
    1108881718U,	// RSHRNv8i16_shift
    1612453461U,	// RSHRNv8i8_shift
    537400854U,	// RSUBHNv2i64_v2i32
    571748624U,	// RSUBHNv2i64_v4i32
    1074796054U,	// RSUBHNv4i32_v4i16
    1108881680U,	// RSUBHNv4i32_v8i16
    1644179728U,	// RSUBHNv8i16_v16i8
    1612453398U,	// RSUBHNv8i16_v8i8
    2182623330U,	// SABALv16i8_v8i16
    2718708931U,	// SABALv2i32_v2i64
    3256104131U,	// SABALv4i16_v4i32
    1108095074U,	// SABALv4i32_v2i64
    1645490274U,	// SABALv8i16_v4i32
    3793237187U,	// SABALv8i8_v8i16
    2181050862U,	// SABAv16i8
    2718446062U,	// SABAv2i32
    3255841262U,	// SABAv4i16
    1108619758U,	// SABAv4i32
    1645752814U,	// SABAv8i16
    3793498606U,	// SABAv8i8
    2149060764U,	// SABDLv16i8_v8i16
    2685146379U,	// SABDLv2i32_v2i64
    3222541579U,	// SABDLv4i16_v4i32
    1074532508U,	// SABDLv4i32_v2i64
    1611927708U,	// SABDLv8i16_v4i32
    3759674635U,	// SABDLv8i8_v8i16
    2147488538U,	// SABDv16i8
    2684883738U,	// SABDv2i32
    3222278938U,	// SABDv4i16
    1075057434U,	// SABDv4i32
    1612190490U,	// SABDv8i16
    3759936282U,	// SABDv8i8
    35141315U,	// SADALPv16i8_v8i16
    1117533891U,	// SADALPv2i32_v1i64
    2181576387U,	// SADALPv4i16_v2i32
    2718709443U,	// SADALPv4i32_v2i64
    3256104643U,	// SADALPv8i16_v4i32
    3792713411U,	// SADALPv8i8_v4i16
    1578707U,	// SADDLPv16i8_v8i16
    1083971283U,	// SADDLPv2i32_v1i64
    2148013779U,	// SADDLPv4i16_v2i32
    2685146835U,	// SADDLPv4i32_v2i64
    3222542035U,	// SADDLPv8i16_v4i32
    3759150803U,	// SADDLPv8i8_v4i16
    272700U,	// SADDLVv16i8v
    2147756348U,	// SADDLVv4i16v
    2684627260U,	// SADDLVv4i32v
    3221498172U,	// SADDLVv8i16v
    3758369084U,	// SADDLVv8i8v
    2149060780U,	// SADDLv16i8_v8i16
    2685146409U,	// SADDLv2i32_v2i64
    3222541609U,	// SADDLv4i16_v4i32
    1074532524U,	// SADDLv4i32_v2i64
    1611927724U,	// SADDLv8i16_v4i32
    3759674665U,	// SADDLv8i8_v8i16
    1612190133U,	// SADDWv16i8_v8i16
    537663936U,	// SADDWv2i32_v2i64
    1075059136U,	// SADDWv4i16_v4i32
    537661877U,	// SADDWv4i32_v2i64
    1075057077U,	// SADDWv8i16_v4i32
    1612192192U,	// SADDWv8i8_v8i16
    17049656U,	// SBCSWr
    17049656U,	// SBCSXr
    17048293U,	// SBCWr
    17048293U,	// SBCXr
    17049061U,	// SBFMWri
    17049061U,	// SBFMXri
    17048517U,	// SCVTFSWDri
    17048517U,	// SCVTFSWSri
    17048517U,	// SCVTFSXDri
    17048517U,	// SCVTFSXSri
    553919429U,	// SCVTFUWDri
    553919429U,	// SCVTFUWSri
    553919429U,	// SCVTFUXDri
    553919429U,	// SCVTFUXSri
    17048517U,	// SCVTFd
    17048517U,	// SCVTFs
    553919429U,	// SCVTFv1i32
    553919429U,	// SCVTFv1i64
    1074271173U,	// SCVTFv2f32
    1611404229U,	// SCVTFv2f64
    2684883909U,	// SCVTFv2i32_shift
    537662405U,	// SCVTFv2i64_shift
    2685670341U,	// SCVTFv4f32
    1075057605U,	// SCVTFv4i32_shift
    17049904U,	// SDIVWr
    17049904U,	// SDIVXr
    17049904U,	// SDIV_IntWr
    17049904U,	// SDIV_IntXr
    67404510U,	// SHA1Crrr
    553919463U,	// SHA1Hrr
    67405278U,	// SHA1Mrrr
    67405488U,	// SHA1Prrr
    1108619265U,	// SHA1SU0rrr
    2719232056U,	// SHA1SU1rr
    67403864U,	// SHA256H2rrr
    67404790U,	// SHA256Hrrr
    2719232010U,	// SHA256SU0rr
    1108619329U,	// SHA256SU1rrr
    2147488572U,	// SHADDv16i8
    2684883772U,	// SHADDv2i32
    3222278972U,	// SHADDv4i16
    1075057468U,	// SHADDv4i32
    1612190524U,	// SHADDv8i16
    3759936316U,	// SHADDv8i8
    2149060797U,	// SHLLv16i8
    2685146487U,	// SHLLv2i32
    3222541687U,	// SHLLv4i16
    3758887101U,	// SHLLv4i32
    1315005U,	// SHLLv8i16
    538449271U,	// SHLLv8i8
    17048896U,	// SHLd
    2147489088U,	// SHLv16i8_shift
    2684884288U,	// SHLv2i32_shift
    537662784U,	// SHLv2i64_shift
    3222279488U,	// SHLv4i16_shift
    1075057984U,	// SHLv4i32_shift
    1612191040U,	// SHLv8i16_shift
    3759936832U,	// SHLv8i8_shift
    1644179748U,	// SHRNv16i8_shift
    537400901U,	// SHRNv2i32_shift
    1074796101U,	// SHRNv4i16_shift
    571748644U,	// SHRNv4i32_shift
    1108881700U,	// SHRNv8i16_shift
    1612453445U,	// SHRNv8i8_shift
    2147488435U,	// SHSUBv16i8
    2684883635U,	// SHSUBv2i32
    3222278835U,	// SHSUBv4i16
    1075057331U,	// SHSUBv4i32
    1612190387U,	// SHSUBv8i16
    3759936179U,	// SHSUBv8i8
    67404954U,	// SLId
    2181051546U,	// SLIv16i8_shift
    2718446746U,	// SLIv2i32_shift
    571225242U,	// SLIv2i64_shift
    3255841946U,	// SLIv4i16_shift
    1108620442U,	// SLIv4i32_shift
    1645753498U,	// SLIv8i16_shift
    3793499290U,	// SLIv8i8_shift
    17048857U,	// SMADDLrrr
    2147489609U,	// SMAXPv16i8
    2684884809U,	// SMAXPv2i32
    3222280009U,	// SMAXPv4i16
    1075058505U,	// SMAXPv4i32
    1612191561U,	// SMAXPv8i16
    3759937353U,	// SMAXPv8i8
    272787U,	// SMAXVv16i8v
    2147756435U,	// SMAXVv4i16v
    2684627347U,	// SMAXVv4i32v
    3221498259U,	// SMAXVv8i16v
    3758369171U,	// SMAXVv8i8v
    2147490298U,	// SMAXv16i8
    2684885498U,	// SMAXv2i32
    3222280698U,	// SMAXv4i16
    1075059194U,	// SMAXv4i32
    1612192250U,	// SMAXv8i16
    3759938042U,	// SMAXv8i8
    21246U,	// SMC
    2147489551U,	// SMINPv16i8
    2684884751U,	// SMINPv2i32
    3222279951U,	// SMINPv4i16
    1075058447U,	// SMINPv4i32
    1612191503U,	// SMINPv8i16
    3759937295U,	// SMINPv8i8
    272741U,	// SMINVv16i8v
    2147756389U,	// SMINVv4i16v
    2684627301U,	// SMINVv4i32v
    3221498213U,	// SMINVv8i16v
    3758369125U,	// SMINVv8i8v
    2147489324U,	// SMINv16i8
    2684884524U,	// SMINv2i32
    3222279724U,	// SMINv4i16
    1075058220U,	// SMINv4i32
    1612191276U,	// SMINv8i16
    3759937068U,	// SMINv8i8
    2182623356U,	// SMLALv16i8_v8i16
    2718708954U,	// SMLALv2i32_indexed
    2718708954U,	// SMLALv2i32_v2i64
    3256104154U,	// SMLALv4i16_indexed
    3256104154U,	// SMLALv4i16_v4i32
    1108095100U,	// SMLALv4i32_indexed
    1108095100U,	// SMLALv4i32_v2i64
    1645490300U,	// SMLALv8i16_indexed
    1645490300U,	// SMLALv8i16_v4i32
    3793237210U,	// SMLALv8i8_v8i16
    2182623480U,	// SMLSLv16i8_v8i16
    2718709168U,	// SMLSLv2i32_indexed
    2718709168U,	// SMLSLv2i32_v2i64
    3256104368U,	// SMLSLv4i16_indexed
    3256104368U,	// SMLSLv4i16_v4i32
    1108095224U,	// SMLSLv4i32_indexed
    1108095224U,	// SMLSLv4i32_v2i64
    1645490424U,	// SMLSLv8i16_indexed
    1645490424U,	// SMLSLv8i16_v4i32
    3793237424U,	// SMLSLv8i8_v8i16
    272768U,	// SMOVvi16to32
    272768U,	// SMOVvi16to64
    537143680U,	// SMOVvi32to64
    1610885504U,	// SMOVvi8to32
    1610885504U,	// SMOVvi8to64
    17048813U,	// SMSUBLrrr
    17048603U,	// SMULHrr
    2149060830U,	// SMULLv16i8_v8i16
    2685146516U,	// SMULLv2i32_indexed
    2685146516U,	// SMULLv2i32_v2i64
    3222541716U,	// SMULLv4i16_indexed
    3222541716U,	// SMULLv4i16_v4i32
    1074532574U,	// SMULLv4i32_indexed
    1074532574U,	// SMULLv4i32_v2i64
    1611927774U,	// SMULLv8i16_indexed
    1611927774U,	// SMULLv8i16_v4i32
    3759674772U,	// SMULLv8i8_v8i16
    6187U,	// SQABSv16i8
    553920555U,	// SQABSv1i16
    553920555U,	// SQABSv1i32
    553920555U,	// SQABSv1i64
    553920555U,	// SQABSv1i8
    1074272299U,	// SQABSv2i32
    1611405355U,	// SQABSv2i64
    2148538411U,	// SQABSv4i16
    2685671467U,	// SQABSv4i32
    3222804523U,	// SQABSv8i16
    3759937579U,	// SQABSv8i8
    2147488602U,	// SQADDv16i8
    17048410U,	// SQADDv1i16
    17048410U,	// SQADDv1i32
    17048410U,	// SQADDv1i64
    17048410U,	// SQADDv1i8
    2684883802U,	// SQADDv2i32
    537662298U,	// SQADDv2i64
    3222279002U,	// SQADDv4i16
    1075057498U,	// SQADDv4i32
    1612190554U,	// SQADDv8i16
    3759936346U,	// SQADDv8i8
    67405009U,	// SQDMLALi16
    67405009U,	// SQDMLALi32
    67405009U,	// SQDMLALv1i32_indexed
    67405009U,	// SQDMLALv1i64_indexed
    2718708945U,	// SQDMLALv2i32_indexed
    2718708945U,	// SQDMLALv2i32_v2i64
    3256104145U,	// SQDMLALv4i16_indexed
    3256104145U,	// SQDMLALv4i16_v4i32
    1108095090U,	// SQDMLALv4i32_indexed
    1108095090U,	// SQDMLALv4i32_v2i64
    1645490290U,	// SQDMLALv8i16_indexed
    1645490290U,	// SQDMLALv8i16_v4i32
    67405223U,	// SQDMLSLi16
    67405223U,	// SQDMLSLi32
    67405223U,	// SQDMLSLv1i32_indexed
    67405223U,	// SQDMLSLv1i64_indexed
    2718709159U,	// SQDMLSLv2i32_indexed
    2718709159U,	// SQDMLSLv2i32_v2i64
    3256104359U,	// SQDMLSLv4i16_indexed
    3256104359U,	// SQDMLSLv4i16_v4i32
    1108095214U,	// SQDMLSLv4i32_indexed
    1108095214U,	// SQDMLSLv4i32_v2i64
    1645490414U,	// SQDMLSLv8i16_indexed
    1645490414U,	// SQDMLSLv8i16_v4i32
    17048584U,	// SQDMULHv1i16
    17048584U,	// SQDMULHv1i16_indexed
    17048584U,	// SQDMULHv1i32
    17048584U,	// SQDMULHv1i32_indexed
    2684883976U,	// SQDMULHv2i32
    2684883976U,	// SQDMULHv2i32_indexed
    3222279176U,	// SQDMULHv4i16
    3222279176U,	// SQDMULHv4i16_indexed
    1075057672U,	// SQDMULHv4i32
    1075057672U,	// SQDMULHv4i32_indexed
    1612190728U,	// SQDMULHv8i16
    1612190728U,	// SQDMULHv8i16_indexed
    17048964U,	// SQDMULLi16
    17048964U,	// SQDMULLi32
    17048964U,	// SQDMULLv1i32_indexed
    17048964U,	// SQDMULLv1i64_indexed
    2685146500U,	// SQDMULLv2i32_indexed
    2685146500U,	// SQDMULLv2i32_v2i64
    3222541700U,	// SQDMULLv4i16_indexed
    3222541700U,	// SQDMULLv4i16_v4i32
    1074532556U,	// SQDMULLv4i32_indexed
    1074532556U,	// SQDMULLv4i32_v2i64
    1611927756U,	// SQDMULLv8i16_indexed
    1611927756U,	// SQDMULLv8i16_v4i32
    5081U,	// SQNEGv16i8
    553919449U,	// SQNEGv1i16
    553919449U,	// SQNEGv1i32
    553919449U,	// SQNEGv1i64
    553919449U,	// SQNEGv1i8
    1074271193U,	// SQNEGv2i32
    1611404249U,	// SQNEGv2i64
    2148537305U,	// SQNEGv4i16
    2685670361U,	// SQNEGv4i32
    3222803417U,	// SQNEGv8i16
    3759936473U,	// SQNEGv8i8
    17048593U,	// SQRDMULHv1i16
    17048593U,	// SQRDMULHv1i16_indexed
    17048593U,	// SQRDMULHv1i32
    17048593U,	// SQRDMULHv1i32_indexed
    2684883985U,	// SQRDMULHv2i32
    2684883985U,	// SQRDMULHv2i32_indexed
    3222279185U,	// SQRDMULHv4i16
    3222279185U,	// SQRDMULHv4i16_indexed
    1075057681U,	// SQRDMULHv4i32
    1075057681U,	// SQRDMULHv4i32_indexed
    1612190737U,	// SQRDMULHv8i16
    1612190737U,	// SQRDMULHv8i16_indexed
    2147489100U,	// SQRSHLv16i8
    17048908U,	// SQRSHLv1i16
    17048908U,	// SQRSHLv1i32
    17048908U,	// SQRSHLv1i64
    17048908U,	// SQRSHLv1i8
    2684884300U,	// SQRSHLv2i32
    537662796U,	// SQRSHLv2i64
    3222279500U,	// SQRSHLv4i16
    1075057996U,	// SQRSHLv4i32
    1612191052U,	// SQRSHLv8i16
    3759936844U,	// SQRSHLv8i8
    17049171U,	// SQRSHRNb
    17049171U,	// SQRSHRNh
    17049171U,	// SQRSHRNs
    1644179764U,	// SQRSHRNv16i8_shift
    537400915U,	// SQRSHRNv2i32_shift
    1074796115U,	// SQRSHRNv4i16_shift
    571748660U,	// SQRSHRNv4i32_shift
    1108881716U,	// SQRSHRNv8i16_shift
    1612453459U,	// SQRSHRNv8i8_shift
    17049232U,	// SQRSHRUNb
    17049232U,	// SQRSHRUNh
    17049232U,	// SQRSHRUNs
    1644179824U,	// SQRSHRUNv16i8_shift
    537400976U,	// SQRSHRUNv2i32_shift
    1074796176U,	// SQRSHRUNv4i16_shift
    571748720U,	// SQRSHRUNv4i32_shift
    1108881776U,	// SQRSHRUNv8i16_shift
    1612453520U,	// SQRSHRUNv8i8_shift
    17049847U,	// SQSHLUb
    17049847U,	// SQSHLUd
    17049847U,	// SQSHLUh
    17049847U,	// SQSHLUs
    2147490039U,	// SQSHLUv16i8_shift
    2684885239U,	// SQSHLUv2i32_shift
    537663735U,	// SQSHLUv2i64_shift
    3222280439U,	// SQSHLUv4i16_shift
    1075058935U,	// SQSHLUv4i32_shift
    1612191991U,	// SQSHLUv8i16_shift
    3759937783U,	// SQSHLUv8i8_shift
    17048894U,	// SQSHLb
    17048894U,	// SQSHLd
    17048894U,	// SQSHLh
    17048894U,	// SQSHLs
    2147489086U,	// SQSHLv16i8
    2147489086U,	// SQSHLv16i8_shift
    17048894U,	// SQSHLv1i16
    17048894U,	// SQSHLv1i32
    17048894U,	// SQSHLv1i64
    17048894U,	// SQSHLv1i8
    2684884286U,	// SQSHLv2i32
    2684884286U,	// SQSHLv2i32_shift
    537662782U,	// SQSHLv2i64
    537662782U,	// SQSHLv2i64_shift
    3222279486U,	// SQSHLv4i16
    3222279486U,	// SQSHLv4i16_shift
    1075057982U,	// SQSHLv4i32
    1075057982U,	// SQSHLv4i32_shift
    1612191038U,	// SQSHLv8i16
    1612191038U,	// SQSHLv8i16_shift
    3759936830U,	// SQSHLv8i8
    3759936830U,	// SQSHLv8i8_shift
    17049155U,	// SQSHRNb
    17049155U,	// SQSHRNh
    17049155U,	// SQSHRNs
    1644179746U,	// SQSHRNv16i8_shift
    537400899U,	// SQSHRNv2i32_shift
    1074796099U,	// SQSHRNv4i16_shift
    571748642U,	// SQSHRNv4i32_shift
    1108881698U,	// SQSHRNv8i16_shift
    1612453443U,	// SQSHRNv8i8_shift
    17049223U,	// SQSHRUNb
    17049223U,	// SQSHRUNh
    17049223U,	// SQSHRUNs
    1644179814U,	// SQSHRUNv16i8_shift
    537400967U,	// SQSHRUNv2i32_shift
    1074796167U,	// SQSHRUNv4i16_shift
    571748710U,	// SQSHRUNv4i32_shift
    1108881766U,	// SQSHRUNv8i16_shift
    1612453511U,	// SQSHRUNv8i8_shift
    2147488464U,	// SQSUBv16i8
    17048272U,	// SQSUBv1i16
    17048272U,	// SQSUBv1i32
    17048272U,	// SQSUBv1i64
    17048272U,	// SQSUBv1i8
    2684883664U,	// SQSUBv2i32
    537662160U,	// SQSUBv2i64
    3222278864U,	// SQSUBv4i16
    1075057360U,	// SQSUBv4i32
    1612190416U,	// SQSUBv8i16
    3759936208U,	// SQSUBv8i8
    3254792534U,	// SQXTNv16i8
    553920121U,	// SQXTNv1i16
    553920121U,	// SQXTNv1i32
    553920121U,	// SQXTNv1i8
    1611142777U,	// SQXTNv2i32
    2685408889U,	// SQXTNv4i16
    1645490518U,	// SQXTNv4i32
    2719494486U,	// SQXTNv8i16
    3223066233U,	// SQXTNv8i8
    3254792571U,	// SQXTUNv16i8
    553920154U,	// SQXTUNv1i16
    553920154U,	// SQXTUNv1i32
    553920154U,	// SQXTUNv1i8
    1611142810U,	// SQXTUNv2i32
    2685408922U,	// SQXTUNv4i16
    1645490555U,	// SQXTUNv4i32
    2719494523U,	// SQXTUNv8i16
    3223066266U,	// SQXTUNv8i8
    2147488556U,	// SRHADDv16i8
    2684883756U,	// SRHADDv2i32
    3222278956U,	// SRHADDv4i16
    1075057452U,	// SRHADDv4i32
    1612190508U,	// SRHADDv8i16
    3759936300U,	// SRHADDv8i8
    67404965U,	// SRId
    2181051557U,	// SRIv16i8_shift
    2718446757U,	// SRIv2i32_shift
    571225253U,	// SRIv2i64_shift
    3255841957U,	// SRIv4i16_shift
    1108620453U,	// SRIv4i32_shift
    1645753509U,	// SRIv8i16_shift
    3793499301U,	// SRIv8i8_shift
    2147489116U,	// SRSHLv16i8
    17048924U,	// SRSHLv1i64
    2684884316U,	// SRSHLv2i32
    537662812U,	// SRSHLv2i64
    3222279516U,	// SRSHLv4i16
    1075058012U,	// SRSHLv4i32
    1612191068U,	// SRSHLv8i16
    3759936860U,	// SRSHLv8i8
    17049501U,	// SRSHRd
    2147489693U,	// SRSHRv16i8_shift
    2684884893U,	// SRSHRv2i32_shift
    537663389U,	// SRSHRv2i64_shift
    3222280093U,	// SRSHRv4i16_shift
    1075058589U,	// SRSHRv4i32_shift
    1612191645U,	// SRSHRv8i16_shift
    3759937437U,	// SRSHRv8i8_shift
    67404288U,	// SRSRAd
    2181050880U,	// SRSRAv16i8_shift
    2718446080U,	// SRSRAv2i32_shift
    571224576U,	// SRSRAv2i64_shift
    3255841280U,	// SRSRAv4i16_shift
    1108619776U,	// SRSRAv4i32_shift
    1645752832U,	// SRSRAv8i16_shift
    3793498624U,	// SRSRAv8i8_shift
    2149060796U,	// SSHLLv16i8_shift
    2685146486U,	// SSHLLv2i32_shift
    3222541686U,	// SSHLLv4i16_shift
    1074532540U,	// SSHLLv4i32_shift
    1611927740U,	// SSHLLv8i16_shift
    3759674742U,	// SSHLLv8i8_shift
    2147489130U,	// SSHLv16i8
    17048938U,	// SSHLv1i64
    2684884330U,	// SSHLv2i32
    537662826U,	// SSHLv2i64
    3222279530U,	// SSHLv4i16
    1075058026U,	// SSHLv4i32
    1612191082U,	// SSHLv8i16
    3759936874U,	// SSHLv8i8
    17049515U,	// SSHRd
    2147489707U,	// SSHRv16i8_shift
    2684884907U,	// SSHRv2i32_shift
    537663403U,	// SSHRv2i64_shift
    3222280107U,	// SSHRv4i16_shift
    1075058603U,	// SSHRv4i32_shift
    1612191659U,	// SSHRv8i16_shift
    3759937451U,	// SSHRv8i8_shift
    67404302U,	// SSRAd
    2181050894U,	// SSRAv16i8_shift
    2718446094U,	// SSRAv2i32_shift
    571224590U,	// SSRAv2i64_shift
    3255841294U,	// SSRAv4i16_shift
    1108619790U,	// SSRAv4i32_shift
    1645752846U,	// SSRAv8i16_shift
    3793498638U,	// SSRAv8i8_shift
    2149060748U,	// SSUBLv16i8_v8i16
    2685146365U,	// SSUBLv2i32_v2i64
    3222541565U,	// SSUBLv4i16_v4i32
    1074532492U,	// SSUBLv4i32_v2i64
    1611927692U,	// SSUBLv8i16_v4i32
    3759674621U,	// SSUBLv8i8_v8i16
    1612190117U,	// SSUBWv16i8_v8i16
    537663913U,	// SSUBWv2i32_v2i64
    1075059113U,	// SSUBWv4i16_v4i32
    537661861U,	// SSUBWv4i32_v2i64
    1075057061U,	// SSUBWv8i16_v4i32
    1612192169U,	// SSUBWv8i8_v8i16
    36915U,	// ST1Fourv16b
    3711027U,	// ST1Fourv16b_POST
    45107U,	// ST1Fourv1d
    3981363U,	// ST1Fourv1d_POST
    53299U,	// ST1Fourv2d
    3727411U,	// ST1Fourv2d_POST
    61491U,	// ST1Fourv2s
    3997747U,	// ST1Fourv2s_POST
    69683U,	// ST1Fourv4h
    4005939U,	// ST1Fourv4h_POST
    77875U,	// ST1Fourv4s
    3751987U,	// ST1Fourv4s_POST
    86067U,	// ST1Fourv8b
    4022323U,	// ST1Fourv8b_POST
    94259U,	// ST1Fourv8h
    3768371U,	// ST1Fourv8h_POST
    36915U,	// ST1Onev16b
    4235315U,	// ST1Onev16b_POST
    45107U,	// ST1Onev1d
    4505651U,	// ST1Onev1d_POST
    53299U,	// ST1Onev2d
    4251699U,	// ST1Onev2d_POST
    61491U,	// ST1Onev2s
    4522035U,	// ST1Onev2s_POST
    69683U,	// ST1Onev4h
    4530227U,	// ST1Onev4h_POST
    77875U,	// ST1Onev4s
    4276275U,	// ST1Onev4s_POST
    86067U,	// ST1Onev8b
    4546611U,	// ST1Onev8b_POST
    94259U,	// ST1Onev8h
    4292659U,	// ST1Onev8h_POST
    36915U,	// ST1Threev16b
    5546035U,	// ST1Threev16b_POST
    45107U,	// ST1Threev1d
    5816371U,	// ST1Threev1d_POST
    53299U,	// ST1Threev2d
    5562419U,	// ST1Threev2d_POST
    61491U,	// ST1Threev2s
    5832755U,	// ST1Threev2s_POST
    69683U,	// ST1Threev4h
    5840947U,	// ST1Threev4h_POST
    77875U,	// ST1Threev4s
    5586995U,	// ST1Threev4s_POST
    86067U,	// ST1Threev8b
    5857331U,	// ST1Threev8b_POST
    94259U,	// ST1Threev8h
    5603379U,	// ST1Threev8h_POST
    36915U,	// ST1Twov16b
    3973171U,	// ST1Twov16b_POST
    45107U,	// ST1Twov1d
    4243507U,	// ST1Twov1d_POST
    53299U,	// ST1Twov2d
    3989555U,	// ST1Twov2d_POST
    61491U,	// ST1Twov2s
    4259891U,	// ST1Twov2s_POST
    69683U,	// ST1Twov4h
    4268083U,	// ST1Twov4h_POST
    77875U,	// ST1Twov4s
    4014131U,	// ST1Twov4s_POST
    86067U,	// ST1Twov8b
    4284467U,	// ST1Twov8b_POST
    94259U,	// ST1Twov8h
    4030515U,	// ST1Twov8h_POST
    147507U,	// ST1i16
    262246451U,	// ST1i16_POST
    151603U,	// ST1i32
    279031859U,	// ST1i32_POST
    155699U,	// ST1i64
    295817267U,	// ST1i64_POST
    159795U,	// ST1i8
    312602675U,	// ST1i8_POST
    37280U,	// ST2Twov16b
    3973536U,	// ST2Twov16b_POST
    53664U,	// ST2Twov2d
    3989920U,	// ST2Twov2d_POST
    61856U,	// ST2Twov2s
    4260256U,	// ST2Twov2s_POST
    70048U,	// ST2Twov4h
    4268448U,	// ST2Twov4h_POST
    78240U,	// ST2Twov4s
    4014496U,	// ST2Twov4s_POST
    86432U,	// ST2Twov8b
    4284832U,	// ST2Twov8b_POST
    94624U,	// ST2Twov8h
    4030880U,	// ST2Twov8h_POST
    147872U,	// ST2i16
    279024032U,	// ST2i16_POST
    151968U,	// ST2i32
    295809440U,	// ST2i32_POST
    156064U,	// ST2i64
    329372064U,	// ST2i64_POST
    160160U,	// ST2i8
    262271392U,	// ST2i8_POST
    37329U,	// ST3Threev16b
    5546449U,	// ST3Threev16b_POST
    53713U,	// ST3Threev2d
    5562833U,	// ST3Threev2d_POST
    61905U,	// ST3Threev2s
    5833169U,	// ST3Threev2s_POST
    70097U,	// ST3Threev4h
    5841361U,	// ST3Threev4h_POST
    78289U,	// ST3Threev4s
    5587409U,	// ST3Threev4s_POST
    86481U,	// ST3Threev8b
    5857745U,	// ST3Threev8b_POST
    94673U,	// ST3Threev8h
    5603793U,	// ST3Threev8h_POST
    147921U,	// ST3i16
    346132945U,	// ST3i16_POST
    152017U,	// ST3i32
    362918353U,	// ST3i32_POST
    156113U,	// ST3i64
    379703761U,	// ST3i64_POST
    160209U,	// ST3i8
    396489169U,	// ST3i8_POST
    37346U,	// ST4Fourv16b
    3711458U,	// ST4Fourv16b_POST
    53730U,	// ST4Fourv2d
    3727842U,	// ST4Fourv2d_POST
    61922U,	// ST4Fourv2s
    3998178U,	// ST4Fourv2s_POST
    70114U,	// ST4Fourv4h
    4006370U,	// ST4Fourv4h_POST
    78306U,	// ST4Fourv4s
    3752418U,	// ST4Fourv4s_POST
    86498U,	// ST4Fourv8b
    4022754U,	// ST4Fourv8b_POST
    94690U,	// ST4Fourv8h
    3768802U,	// ST4Fourv8h_POST
    147938U,	// ST4i16
    295801314U,	// ST4i16_POST
    152034U,	// ST4i32
    329363938U,	// ST4i32_POST
    156130U,	// ST4i64
    413258210U,	// ST4i64_POST
    160226U,	// ST4i8
    279048674U,	// ST4i8_POST
    26485317U,	// STLRB
    26485814U,	// STLRH
    26486716U,	// STLRW
    26486716U,	// STLRX
    17049437U,	// STLXPW
    17049437U,	// STLXPX
    553919101U,	// STLXRB
    553919598U,	// STLXRH
    553920528U,	// STLXRW
    553920528U,	// STLXRX
    553920285U,	// STNPDi
    553920285U,	// STNPQi
    553920285U,	// STNPSi
    553920285U,	// STNPWi
    553920285U,	// STNPXi
    553920305U,	// STPDi
    604276529U,	// STPDpost
    604276529U,	// STPDpre
    553920305U,	// STPQi
    604276529U,	// STPQpost
    604276529U,	// STPQpre
    553920305U,	// STPSi
    604276529U,	// STPSpost
    604276529U,	// STPSpre
    553920305U,	// STPWi
    604276529U,	// STPWpost
    604276529U,	// STPWpre
    553920305U,	// STPXi
    604276529U,	// STPXpost
    604276529U,	// STPXpre
    1150583379U,	// STRBBpost
    76841555U,	// STRBBpre
    26485331U,	// STRBBroW
    26485331U,	// STRBBroX
    26485331U,	// STRBBui
    1150584806U,	// STRBpost
    76842982U,	// STRBpre
    26486758U,	// STRBroW
    26486758U,	// STRBroX
    26486758U,	// STRBui
    1150584806U,	// STRDpost
    76842982U,	// STRDpre
    26486758U,	// STRDroW
    26486758U,	// STRDroX
    26486758U,	// STRDui
    1150583876U,	// STRHHpost
    76842052U,	// STRHHpre
    26485828U,	// STRHHroW
    26485828U,	// STRHHroX
    26485828U,	// STRHHui
    1150584806U,	// STRHpost
    76842982U,	// STRHpre
    26486758U,	// STRHroW
    26486758U,	// STRHroX
    26486758U,	// STRHui
    1150584806U,	// STRQpost
    76842982U,	// STRQpre
    26486758U,	// STRQroW
    26486758U,	// STRQroX
    26486758U,	// STRQui
    1150584806U,	// STRSpost
    76842982U,	// STRSpre
    26486758U,	// STRSroW
    26486758U,	// STRSroX
    26486758U,	// STRSui
    1150584806U,	// STRWpost
    76842982U,	// STRWpre
    26486758U,	// STRWroW
    26486758U,	// STRWroX
    26486758U,	// STRWui
    1150584806U,	// STRXpost
    76842982U,	// STRXpre
    26486758U,	// STRXroW
    26486758U,	// STRXroX
    26486758U,	// STRXui
    26485337U,	// STTRBi
    26485834U,	// STTRHi
    26486763U,	// STTRWi
    26486763U,	// STTRXi
    26485351U,	// STURBBi
    26486781U,	// STURBi
    26486781U,	// STURDi
    26485848U,	// STURHHi
    26486781U,	// STURHi
    26486781U,	// STURQi
    26486781U,	// STURSi
    26486781U,	// STURWi
    26486781U,	// STURXi
    17049444U,	// STXPW
    17049444U,	// STXPX
    553919109U,	// STXRB
    553919606U,	// STXRH
    553920535U,	// STXRW
    553920535U,	// STXRX
    537400855U,	// SUBHNv2i64_v2i32
    571748625U,	// SUBHNv2i64_v4i32
    1074796055U,	// SUBHNv4i32_v4i16
    1108881681U,	// SUBHNv4i32_v8i16
    1644179729U,	// SUBHNv8i16_v16i8
    1612453399U,	// SUBHNv8i16_v8i8
    17049650U,	// SUBSWri
    0U,	// SUBSWrr
    17049650U,	// SUBSWrs
    17049650U,	// SUBSWrx
    17049650U,	// SUBSXri
    0U,	// SUBSXrr
    17049650U,	// SUBSXrs
    17049650U,	// SUBSXrx
    17049650U,	// SUBSXrx64
    17048238U,	// SUBWri
    0U,	// SUBWrr
    17048238U,	// SUBWrs
    17048238U,	// SUBWrx
    17048238U,	// SUBXri
    0U,	// SUBXrr
    17048238U,	// SUBXrs
    17048238U,	// SUBXrx
    17048238U,	// SUBXrx64
    2147488430U,	// SUBv16i8
    17048238U,	// SUBv1i64
    2684883630U,	// SUBv2i32
    537662126U,	// SUBv2i64
    3222278830U,	// SUBv4i16
    1075057326U,	// SUBv4i32
    1612190382U,	// SUBv8i16
    3759936174U,	// SUBv8i8
    33567585U,	// SUQADDv16i8
    604275553U,	// SUQADDv1i16
    604275553U,	// SUQADDv1i32
    604275553U,	// SUQADDv1i64
    604275553U,	// SUQADDv1i8
    1107833697U,	// SUQADDv2i32
    1644966753U,	// SUQADDv2i64
    2182099809U,	// SUQADDv4i16
    2719232865U,	// SUQADDv4i32
    3256365921U,	// SUQADDv8i16
    3793498977U,	// SUQADDv8i8
    21263U,	// SVC
    17049022U,	// SYSLxt
    419702938U,	// SYSxt
    436212968U,	// TBLv16i8Four
    436212968U,	// TBLv16i8One
    436212968U,	// TBLv16i8Three
    436212968U,	// TBLv16i8Two
    4196144360U,	// TBLv8i8Four
    4196144360U,	// TBLv8i8One
    4196144360U,	// TBLv8i8Three
    4196144360U,	// TBLv8i8Two
    17050183U,	// TBNZW
    17050183U,	// TBNZX
    452999686U,	// TBXv16i8Four
    452999686U,	// TBXv16i8One
    452999686U,	// TBXv16i8Three
    452999686U,	// TBXv16i8Two
    4212931078U,	// TBXv8i8Four
    4212931078U,	// TBXv8i8One
    4212931078U,	// TBXv8i8Three
    4212931078U,	// TBXv8i8Two
    17050167U,	// TBZW
    17050167U,	// TBZX
    0U,	// TCRETURNdi
    0U,	// TCRETURNri
    2107995U,	// TLSDESCCALL
    0U,	// TLSDESC_BLR
    2147487770U,	// TRN1v16i8
    2684882970U,	// TRN1v2i32
    537661466U,	// TRN1v2i64
    3222278170U,	// TRN1v4i16
    1075056666U,	// TRN1v4i32
    1612189722U,	// TRN1v8i16
    3759935514U,	// TRN1v8i8
    2147488072U,	// TRN2v16i8
    2684883272U,	// TRN2v2i32
    537661768U,	// TRN2v2i64
    3222278472U,	// TRN2v4i16
    1075056968U,	// TRN2v4i32
    1612190024U,	// TRN2v8i16
    3759935816U,	// TRN2v8i8
    2182623338U,	// UABALv16i8_v8i16
    2718708938U,	// UABALv2i32_v2i64
    3256104138U,	// UABALv4i16_v4i32
    1108095082U,	// UABALv4i32_v2i64
    1645490282U,	// UABALv8i16_v4i32
    3793237194U,	// UABALv8i8_v8i16
    2181050868U,	// UABAv16i8
    2718446068U,	// UABAv2i32
    3255841268U,	// UABAv4i16
    1108619764U,	// UABAv4i32
    1645752820U,	// UABAv8i16
    3793498612U,	// UABAv8i8
    2149060772U,	// UABDLv16i8_v8i16
    2685146386U,	// UABDLv2i32_v2i64
    3222541586U,	// UABDLv4i16_v4i32
    1074532516U,	// UABDLv4i32_v2i64
    1611927716U,	// UABDLv8i16_v4i32
    3759674642U,	// UABDLv8i8_v8i16
    2147488544U,	// UABDv16i8
    2684883744U,	// UABDv2i32
    3222278944U,	// UABDv4i16
    1075057440U,	// UABDv4i32
    1612190496U,	// UABDv8i16
    3759936288U,	// UABDv8i8
    35141323U,	// UADALPv16i8_v8i16
    1117533899U,	// UADALPv2i32_v1i64
    2181576395U,	// UADALPv4i16_v2i32
    2718709451U,	// UADALPv4i32_v2i64
    3256104651U,	// UADALPv8i16_v4i32
    3792713419U,	// UADALPv8i8_v4i16
    1578715U,	// UADDLPv16i8_v8i16
    1083971291U,	// UADDLPv2i32_v1i64
    2148013787U,	// UADDLPv4i16_v2i32
    2685146843U,	// UADDLPv4i32_v2i64
    3222542043U,	// UADDLPv8i16_v4i32
    3759150811U,	// UADDLPv8i8_v4i16
    272708U,	// UADDLVv16i8v
    2147756356U,	// UADDLVv4i16v
    2684627268U,	// UADDLVv4i32v
    3221498180U,	// UADDLVv8i16v
    3758369092U,	// UADDLVv8i8v
    2149060788U,	// UADDLv16i8_v8i16
    2685146416U,	// UADDLv2i32_v2i64
    3222541616U,	// UADDLv4i16_v4i32
    1074532532U,	// UADDLv4i32_v2i64
    1611927732U,	// UADDLv8i16_v4i32
    3759674672U,	// UADDLv8i8_v8i16
    1612190141U,	// UADDWv16i8_v8i16
    537663943U,	// UADDWv2i32_v2i64
    1075059143U,	// UADDWv4i16_v4i32
    537661885U,	// UADDWv4i32_v2i64
    1075057085U,	// UADDWv8i16_v4i32
    1612192199U,	// UADDWv8i8_v8i16
    17049067U,	// UBFMWri
    17049067U,	// UBFMXri
    17048524U,	// UCVTFSWDri
    17048524U,	// UCVTFSWSri
    17048524U,	// UCVTFSXDri
    17048524U,	// UCVTFSXSri
    553919436U,	// UCVTFUWDri
    553919436U,	// UCVTFUWSri
    553919436U,	// UCVTFUXDri
    553919436U,	// UCVTFUXSri
    17048524U,	// UCVTFd
    17048524U,	// UCVTFs
    553919436U,	// UCVTFv1i32
    553919436U,	// UCVTFv1i64
    1074271180U,	// UCVTFv2f32
    1611404236U,	// UCVTFv2f64
    2684883916U,	// UCVTFv2i32_shift
    537662412U,	// UCVTFv2i64_shift
    2685670348U,	// UCVTFv4f32
    1075057612U,	// UCVTFv4i32_shift
    17049910U,	// UDIVWr
    17049910U,	// UDIVXr
    17049910U,	// UDIV_IntWr
    17049910U,	// UDIV_IntXr
    2147488579U,	// UHADDv16i8
    2684883779U,	// UHADDv2i32
    3222278979U,	// UHADDv4i16
    1075057475U,	// UHADDv4i32
    1612190531U,	// UHADDv8i16
    3759936323U,	// UHADDv8i8
    2147488442U,	// UHSUBv16i8
    2684883642U,	// UHSUBv2i32
    3222278842U,	// UHSUBv4i16
    1075057338U,	// UHSUBv4i32
    1612190394U,	// UHSUBv8i16
    3759936186U,	// UHSUBv8i8
    17048865U,	// UMADDLrrr
    2147489616U,	// UMAXPv16i8
    2684884816U,	// UMAXPv2i32
    3222280016U,	// UMAXPv4i16
    1075058512U,	// UMAXPv4i32
    1612191568U,	// UMAXPv8i16
    3759937360U,	// UMAXPv8i8
    272794U,	// UMAXVv16i8v
    2147756442U,	// UMAXVv4i16v
    2684627354U,	// UMAXVv4i32v
    3221498266U,	// UMAXVv8i16v
    3758369178U,	// UMAXVv8i8v
    2147490304U,	// UMAXv16i8
    2684885504U,	// UMAXv2i32
    3222280704U,	// UMAXv4i16
    1075059200U,	// UMAXv4i32
    1612192256U,	// UMAXv8i16
    3759938048U,	// UMAXv8i8
    2147489558U,	// UMINPv16i8
    2684884758U,	// UMINPv2i32
    3222279958U,	// UMINPv4i16
    1075058454U,	// UMINPv4i32
    1612191510U,	// UMINPv8i16
    3759937302U,	// UMINPv8i8
    272748U,	// UMINVv16i8v
    2147756396U,	// UMINVv4i16v
    2684627308U,	// UMINVv4i32v
    3221498220U,	// UMINVv8i16v
    3758369132U,	// UMINVv8i8v
    2147489330U,	// UMINv16i8
    2684884530U,	// UMINv2i32
    3222279730U,	// UMINv4i16
    1075058226U,	// UMINv4i32
    1612191282U,	// UMINv8i16
    3759937074U,	// UMINv8i8
    2182623364U,	// UMLALv16i8_v8i16
    2718708961U,	// UMLALv2i32_indexed
    2718708961U,	// UMLALv2i32_v2i64
    3256104161U,	// UMLALv4i16_indexed
    3256104161U,	// UMLALv4i16_v4i32
    1108095108U,	// UMLALv4i32_indexed
    1108095108U,	// UMLALv4i32_v2i64
    1645490308U,	// UMLALv8i16_indexed
    1645490308U,	// UMLALv8i16_v4i32
    3793237217U,	// UMLALv8i8_v8i16
    2182623488U,	// UMLSLv16i8_v8i16
    2718709175U,	// UMLSLv2i32_indexed
    2718709175U,	// UMLSLv2i32_v2i64
    3256104375U,	// UMLSLv4i16_indexed
    3256104375U,	// UMLSLv4i16_v4i32
    1108095232U,	// UMLSLv4i32_indexed
    1108095232U,	// UMLSLv4i32_v2i64
    1645490432U,	// UMLSLv8i16_indexed
    1645490432U,	// UMLSLv8i16_v4i32
    3793237431U,	// UMLSLv8i8_v8i16
    272774U,	// UMOVvi16
    537143686U,	// UMOVvi32
    1074014598U,	// UMOVvi64
    1610885510U,	// UMOVvi8
    17048821U,	// UMSUBLrrr
    17048610U,	// UMULHrr
    2149060838U,	// UMULLv16i8_v8i16
    2685146523U,	// UMULLv2i32_indexed
    2685146523U,	// UMULLv2i32_v2i64
    3222541723U,	// UMULLv4i16_indexed
    3222541723U,	// UMULLv4i16_v4i32
    1074532582U,	// UMULLv4i32_indexed
    1074532582U,	// UMULLv4i32_v2i64
    1611927782U,	// UMULLv8i16_indexed
    1611927782U,	// UMULLv8i16_v4i32
    3759674779U,	// UMULLv8i8_v8i16
    2147488610U,	// UQADDv16i8
    17048418U,	// UQADDv1i16
    17048418U,	// UQADDv1i32
    17048418U,	// UQADDv1i64
    17048418U,	// UQADDv1i8
    2684883810U,	// UQADDv2i32
    537662306U,	// UQADDv2i64
    3222279010U,	// UQADDv4i16
    1075057506U,	// UQADDv4i32
    1612190562U,	// UQADDv8i16
    3759936354U,	// UQADDv8i8
    2147489108U,	// UQRSHLv16i8
    17048916U,	// UQRSHLv1i16
    17048916U,	// UQRSHLv1i32
    17048916U,	// UQRSHLv1i64
    17048916U,	// UQRSHLv1i8
    2684884308U,	// UQRSHLv2i32
    537662804U,	// UQRSHLv2i64
    3222279508U,	// UQRSHLv4i16
    1075058004U,	// UQRSHLv4i32
    1612191060U,	// UQRSHLv8i16
    3759936852U,	// UQRSHLv8i8
    17049180U,	// UQRSHRNb
    17049180U,	// UQRSHRNh
    17049180U,	// UQRSHRNs
    1644179774U,	// UQRSHRNv16i8_shift
    537400924U,	// UQRSHRNv2i32_shift
    1074796124U,	// UQRSHRNv4i16_shift
    571748670U,	// UQRSHRNv4i32_shift
    1108881726U,	// UQRSHRNv8i16_shift
    1612453468U,	// UQRSHRNv8i8_shift
    17048901U,	// UQSHLb
    17048901U,	// UQSHLd
    17048901U,	// UQSHLh
    17048901U,	// UQSHLs
    2147489093U,	// UQSHLv16i8
    2147489093U,	// UQSHLv16i8_shift
    17048901U,	// UQSHLv1i16
    17048901U,	// UQSHLv1i32
    17048901U,	// UQSHLv1i64
    17048901U,	// UQSHLv1i8
    2684884293U,	// UQSHLv2i32
    2684884293U,	// UQSHLv2i32_shift
    537662789U,	// UQSHLv2i64
    537662789U,	// UQSHLv2i64_shift
    3222279493U,	// UQSHLv4i16
    3222279493U,	// UQSHLv4i16_shift
    1075057989U,	// UQSHLv4i32
    1075057989U,	// UQSHLv4i32_shift
    1612191045U,	// UQSHLv8i16
    1612191045U,	// UQSHLv8i16_shift
    3759936837U,	// UQSHLv8i8
    3759936837U,	// UQSHLv8i8_shift
    17049163U,	// UQSHRNb
    17049163U,	// UQSHRNh
    17049163U,	// UQSHRNs
    1644179755U,	// UQSHRNv16i8_shift
    537400907U,	// UQSHRNv2i32_shift
    1074796107U,	// UQSHRNv4i16_shift
    571748651U,	// UQSHRNv4i32_shift
    1108881707U,	// UQSHRNv8i16_shift
    1612453451U,	// UQSHRNv8i8_shift
    2147488471U,	// UQSUBv16i8
    17048279U,	// UQSUBv1i16
    17048279U,	// UQSUBv1i32
    17048279U,	// UQSUBv1i64
    17048279U,	// UQSUBv1i8
    2684883671U,	// UQSUBv2i32
    537662167U,	// UQSUBv2i64
    3222278871U,	// UQSUBv4i16
    1075057367U,	// UQSUBv4i32
    1612190423U,	// UQSUBv8i16
    3759936215U,	// UQSUBv8i8
    3254792542U,	// UQXTNv16i8
    553920128U,	// UQXTNv1i16
    553920128U,	// UQXTNv1i32
    553920128U,	// UQXTNv1i8
    1611142784U,	// UQXTNv2i32
    2685408896U,	// UQXTNv4i16
    1645490526U,	// UQXTNv4i32
    2719494494U,	// UQXTNv8i16
    3223066240U,	// UQXTNv8i8
    1074271121U,	// URECPEv2i32
    2685670289U,	// URECPEv4i32
    2147488564U,	// URHADDv16i8
    2684883764U,	// URHADDv2i32
    3222278964U,	// URHADDv4i16
    1075057460U,	// URHADDv4i32
    1612190516U,	// URHADDv8i16
    3759936308U,	// URHADDv8i8
    2147489123U,	// URSHLv16i8
    17048931U,	// URSHLv1i64
    2684884323U,	// URSHLv2i32
    537662819U,	// URSHLv2i64
    3222279523U,	// URSHLv4i16
    1075058019U,	// URSHLv4i32
    1612191075U,	// URSHLv8i16
    3759936867U,	// URSHLv8i8
    17049508U,	// URSHRd
    2147489700U,	// URSHRv16i8_shift
    2684884900U,	// URSHRv2i32_shift
    537663396U,	// URSHRv2i64_shift
    3222280100U,	// URSHRv4i16_shift
    1075058596U,	// URSHRv4i32_shift
    1612191652U,	// URSHRv8i16_shift
    3759937444U,	// URSHRv8i8_shift
    1074271159U,	// URSQRTEv2i32
    2685670327U,	// URSQRTEv4i32
    67404295U,	// URSRAd
    2181050887U,	// URSRAv16i8_shift
    2718446087U,	// URSRAv2i32_shift
    571224583U,	// URSRAv2i64_shift
    3255841287U,	// URSRAv4i16_shift
    1108619783U,	// URSRAv4i32_shift
    1645752839U,	// URSRAv8i16_shift
    3793498631U,	// URSRAv8i8_shift
    2149060804U,	// USHLLv16i8_shift
    2685146493U,	// USHLLv2i32_shift
    3222541693U,	// USHLLv4i16_shift
    1074532548U,	// USHLLv4i32_shift
    1611927748U,	// USHLLv8i16_shift
    3759674749U,	// USHLLv8i8_shift
    2147489136U,	// USHLv16i8
    17048944U,	// USHLv1i64
    2684884336U,	// USHLv2i32
    537662832U,	// USHLv2i64
    3222279536U,	// USHLv4i16
    1075058032U,	// USHLv4i32
    1612191088U,	// USHLv8i16
    3759936880U,	// USHLv8i8
    17049521U,	// USHRd
    2147489713U,	// USHRv16i8_shift
    2684884913U,	// USHRv2i32_shift
    537663409U,	// USHRv2i64_shift
    3222280113U,	// USHRv4i16_shift
    1075058609U,	// USHRv4i32_shift
    1612191665U,	// USHRv8i16_shift
    3759937457U,	// USHRv8i8_shift
    33567577U,	// USQADDv16i8
    604275545U,	// USQADDv1i16
    604275545U,	// USQADDv1i32
    604275545U,	// USQADDv1i64
    604275545U,	// USQADDv1i8
    1107833689U,	// USQADDv2i32
    1644966745U,	// USQADDv2i64
    2182099801U,	// USQADDv4i16
    2719232857U,	// USQADDv4i32
    3256365913U,	// USQADDv8i16
    3793498969U,	// USQADDv8i8
    67404308U,	// USRAd
    2181050900U,	// USRAv16i8_shift
    2718446100U,	// USRAv2i32_shift
    571224596U,	// USRAv2i64_shift
    3255841300U,	// USRAv4i16_shift
    1108619796U,	// USRAv4i32_shift
    1645752852U,	// USRAv8i16_shift
    3793498644U,	// USRAv8i8_shift
    2149060756U,	// USUBLv16i8_v8i16
    2685146372U,	// USUBLv2i32_v2i64
    3222541572U,	// USUBLv4i16_v4i32
    1074532500U,	// USUBLv4i32_v2i64
    1611927700U,	// USUBLv8i16_v4i32
    3759674628U,	// USUBLv8i8_v8i16
    1612190125U,	// USUBWv16i8_v8i16
    537663920U,	// USUBWv2i32_v2i64
    1075059120U,	// USUBWv4i16_v4i32
    537661869U,	// USUBWv4i32_v2i64
    1075057069U,	// USUBWv8i16_v4i32
    1612192176U,	// USUBWv8i8_v8i16
    2147487782U,	// UZP1v16i8
    2684882982U,	// UZP1v2i32
    537661478U,	// UZP1v2i64
    3222278182U,	// UZP1v4i16
    1075056678U,	// UZP1v4i32
    1612189734U,	// UZP1v8i16
    3759935526U,	// UZP1v8i8
    2147488147U,	// UZP2v16i8
    2684883347U,	// UZP2v2i32
    537661843U,	// UZP2v2i64
    3222278547U,	// UZP2v4i16
    1075057043U,	// UZP2v4i32
    1612190099U,	// UZP2v8i16
    3759935891U,	// UZP2v8i8
    3254792536U,	// XTNv16i8
    1611142779U,	// XTNv2i32
    2685408891U,	// XTNv4i16
    1645490520U,	// XTNv4i32
    2719494488U,	// XTNv8i16
    3223066235U,	// XTNv8i8
    2147487776U,	// ZIP1v16i8
    2684882976U,	// ZIP1v2i32
    537661472U,	// ZIP1v2i64
    3222278176U,	// ZIP1v4i16
    1075056672U,	// ZIP1v4i32
    1612189728U,	// ZIP1v8i16
    3759935520U,	// ZIP1v8i8
    2147488141U,	// ZIP2v16i8
    2684883341U,	// ZIP2v2i32
    537661837U,	// ZIP2v2i64
    3222278541U,	// ZIP2v4i16
    1075057037U,	// ZIP2v4i32
    1612190093U,	// ZIP2v8i16
    3759935885U,	// ZIP2v8i8
    0U
  };

  static const uint32_t OpInfo2[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    0U,	// DBG_VALUE
    0U,	// REG_SEQUENCE
    0U,	// COPY
    0U,	// BUNDLE
    0U,	// LIFETIME_START
    0U,	// LIFETIME_END
    0U,	// STACKMAP
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    0U,	// STATEPOINT
    0U,	// FRAME_ALLOC
    0U,	// ABSv16i8
    0U,	// ABSv1i64
    0U,	// ABSv2i32
    0U,	// ABSv2i64
    0U,	// ABSv4i16
    0U,	// ABSv4i32
    0U,	// ABSv8i16
    0U,	// ABSv8i8
    1U,	// ADCSWr
    1U,	// ADCSXr
    1U,	// ADCWr
    1U,	// ADCXr
    265U,	// ADDHNv2i64_v2i32
    273U,	// ADDHNv2i64_v4i32
    521U,	// ADDHNv4i32_v4i16
    529U,	// ADDHNv4i32_v8i16
    785U,	// ADDHNv8i16_v16i8
    777U,	// ADDHNv8i16_v8i8
    1033U,	// ADDPv16i8
    1289U,	// ADDPv2i32
    265U,	// ADDPv2i64
    0U,	// ADDPv2i64p
    1545U,	// ADDPv4i16
    521U,	// ADDPv4i32
    777U,	// ADDPv8i16
    1801U,	// ADDPv8i8
    25U,	// ADDSWri
    0U,	// ADDSWrr
    33U,	// ADDSWrs
    41U,	// ADDSWrx
    25U,	// ADDSXri
    0U,	// ADDSXrr
    33U,	// ADDSXrs
    41U,	// ADDSXrx
    2049U,	// ADDSXrx64
    0U,	// ADDVv16i8v
    0U,	// ADDVv4i16v
    0U,	// ADDVv4i32v
    0U,	// ADDVv8i16v
    0U,	// ADDVv8i8v
    25U,	// ADDWri
    0U,	// ADDWrr
    33U,	// ADDWrs
    41U,	// ADDWrx
    25U,	// ADDXri
    0U,	// ADDXrr
    33U,	// ADDXrs
    41U,	// ADDXrx
    2049U,	// ADDXrx64
    1033U,	// ADDv16i8
    1U,	// ADDv1i64
    1289U,	// ADDv2i32
    265U,	// ADDv2i64
    1545U,	// ADDv4i16
    521U,	// ADDv4i32
    777U,	// ADDv8i16
    1801U,	// ADDv8i8
    0U,	// ADJCALLSTACKDOWN
    0U,	// ADJCALLSTACKUP
    0U,	// ADR
    0U,	// ADRP
    0U,	// AESDrr
    0U,	// AESErr
    0U,	// AESIMCrr
    0U,	// AESMCrr
    49U,	// ANDSWri
    0U,	// ANDSWrr
    33U,	// ANDSWrs
    57U,	// ANDSXri
    0U,	// ANDSXrr
    33U,	// ANDSXrs
    49U,	// ANDWri
    0U,	// ANDWrr
    33U,	// ANDWrs
    57U,	// ANDXri
    0U,	// ANDXrr
    33U,	// ANDXrs
    1033U,	// ANDv16i8
    1801U,	// ANDv8i8
    1U,	// ASRVWr
    1U,	// ASRVXr
    0U,	// B
    2369U,	// BFMWri
    2369U,	// BFMXri
    0U,	// BICSWrr
    33U,	// BICSWrs
    0U,	// BICSXrr
    33U,	// BICSXrs
    0U,	// BICWrr
    33U,	// BICWrs
    0U,	// BICXrr
    33U,	// BICXrs
    1033U,	// BICv16i8
    0U,	// BICv2i32
    0U,	// BICv4i16
    0U,	// BICv4i32
    0U,	// BICv8i16
    1801U,	// BICv8i8
    1033U,	// BIFv16i8
    1801U,	// BIFv8i8
    1041U,	// BITv16i8
    1809U,	// BITv8i8
    0U,	// BL
    0U,	// BLR
    0U,	// BR
    0U,	// BRK
    1041U,	// BSLv16i8
    1809U,	// BSLv8i8
    0U,	// Bcc
    0U,	// CBNZW
    0U,	// CBNZX
    0U,	// CBZW
    0U,	// CBZX
    10497U,	// CCMNWi
    10497U,	// CCMNWr
    10497U,	// CCMNXi
    10497U,	// CCMNXr
    10497U,	// CCMPWi
    10497U,	// CCMPWr
    10497U,	// CCMPXi
    10497U,	// CCMPXr
    0U,	// CLREX
    0U,	// CLSWr
    0U,	// CLSXr
    0U,	// CLSv16i8
    0U,	// CLSv2i32
    0U,	// CLSv4i16
    0U,	// CLSv4i32
    0U,	// CLSv8i16
    0U,	// CLSv8i8
    0U,	// CLZWr
    0U,	// CLZXr
    0U,	// CLZv16i8
    0U,	// CLZv2i32
    0U,	// CLZv4i16
    0U,	// CLZv4i32
    0U,	// CLZv8i16
    0U,	// CLZv8i8
    1033U,	// CMEQv16i8
    2U,	// CMEQv16i8rz
    1U,	// CMEQv1i64
    2U,	// CMEQv1i64rz
    1289U,	// CMEQv2i32
    2U,	// CMEQv2i32rz
    265U,	// CMEQv2i64
    2U,	// CMEQv2i64rz
    1545U,	// CMEQv4i16
    2U,	// CMEQv4i16rz
    521U,	// CMEQv4i32
    2U,	// CMEQv4i32rz
    777U,	// CMEQv8i16
    2U,	// CMEQv8i16rz
    1801U,	// CMEQv8i8
    2U,	// CMEQv8i8rz
    1033U,	// CMGEv16i8
    2U,	// CMGEv16i8rz
    1U,	// CMGEv1i64
    2U,	// CMGEv1i64rz
    1289U,	// CMGEv2i32
    2U,	// CMGEv2i32rz
    265U,	// CMGEv2i64
    2U,	// CMGEv2i64rz
    1545U,	// CMGEv4i16
    2U,	// CMGEv4i16rz
    521U,	// CMGEv4i32
    2U,	// CMGEv4i32rz
    777U,	// CMGEv8i16
    2U,	// CMGEv8i16rz
    1801U,	// CMGEv8i8
    2U,	// CMGEv8i8rz
    1033U,	// CMGTv16i8
    2U,	// CMGTv16i8rz
    1U,	// CMGTv1i64
    2U,	// CMGTv1i64rz
    1289U,	// CMGTv2i32
    2U,	// CMGTv2i32rz
    265U,	// CMGTv2i64
    2U,	// CMGTv2i64rz
    1545U,	// CMGTv4i16
    2U,	// CMGTv4i16rz
    521U,	// CMGTv4i32
    2U,	// CMGTv4i32rz
    777U,	// CMGTv8i16
    2U,	// CMGTv8i16rz
    1801U,	// CMGTv8i8
    2U,	// CMGTv8i8rz
    1033U,	// CMHIv16i8
    1U,	// CMHIv1i64
    1289U,	// CMHIv2i32
    265U,	// CMHIv2i64
    1545U,	// CMHIv4i16
    521U,	// CMHIv4i32
    777U,	// CMHIv8i16
    1801U,	// CMHIv8i8
    1033U,	// CMHSv16i8
    1U,	// CMHSv1i64
    1289U,	// CMHSv2i32
    265U,	// CMHSv2i64
    1545U,	// CMHSv4i16
    521U,	// CMHSv4i32
    777U,	// CMHSv8i16
    1801U,	// CMHSv8i8
    2U,	// CMLEv16i8rz
    2U,	// CMLEv1i64rz
    2U,	// CMLEv2i32rz
    2U,	// CMLEv2i64rz
    2U,	// CMLEv4i16rz
    2U,	// CMLEv4i32rz
    2U,	// CMLEv8i16rz
    2U,	// CMLEv8i8rz
    2U,	// CMLTv16i8rz
    2U,	// CMLTv1i64rz
    2U,	// CMLTv2i32rz
    2U,	// CMLTv2i64rz
    2U,	// CMLTv4i16rz
    2U,	// CMLTv4i32rz
    2U,	// CMLTv8i16rz
    2U,	// CMLTv8i8rz
    1033U,	// CMTSTv16i8
    1U,	// CMTSTv1i64
    1289U,	// CMTSTv2i32
    265U,	// CMTSTv2i64
    1545U,	// CMTSTv4i16
    521U,	// CMTSTv4i32
    777U,	// CMTSTv8i16
    1801U,	// CMTSTv8i8
    0U,	// CNTv16i8
    0U,	// CNTv8i8
    75U,	// CPYi16
    75U,	// CPYi32
    75U,	// CPYi64
    75U,	// CPYi8
    1U,	// CRC32Brr
    1U,	// CRC32CBrr
    1U,	// CRC32CHrr
    1U,	// CRC32CWrr
    1U,	// CRC32CXrr
    1U,	// CRC32Hrr
    1U,	// CRC32Wrr
    1U,	// CRC32Xrr
    10497U,	// CSELWr
    10497U,	// CSELXr
    10497U,	// CSINCWr
    10497U,	// CSINCXr
    10497U,	// CSINVWr
    10497U,	// CSINVXr
    10497U,	// CSNEGWr
    10497U,	// CSNEGXr
    0U,	// DCPS1
    0U,	// DCPS2
    0U,	// DCPS3
    0U,	// DMB
    0U,	// DRPS
    0U,	// DSB
    0U,	// DUPv16i8gpr
    75U,	// DUPv16i8lane
    0U,	// DUPv2i32gpr
    75U,	// DUPv2i32lane
    0U,	// DUPv2i64gpr
    75U,	// DUPv2i64lane
    0U,	// DUPv4i16gpr
    75U,	// DUPv4i16lane
    0U,	// DUPv4i32gpr
    75U,	// DUPv4i32lane
    0U,	// DUPv8i16gpr
    75U,	// DUPv8i16lane
    0U,	// DUPv8i8gpr
    75U,	// DUPv8i8lane
    0U,	// EONWrr
    33U,	// EONWrs
    0U,	// EONXrr
    33U,	// EONXrs
    49U,	// EORWri
    0U,	// EORWrr
    33U,	// EORWrs
    57U,	// EORXri
    0U,	// EORXrr
    33U,	// EORXrs
    1033U,	// EORv16i8
    1801U,	// EORv8i8
    0U,	// ERET
    18689U,	// EXTRWrri
    18689U,	// EXTRXrri
    2569U,	// EXTv16i8
    2825U,	// EXTv8i8
    0U,	// F128CSEL
    1U,	// FABD32
    1U,	// FABD64
    1289U,	// FABDv2f32
    265U,	// FABDv2f64
    521U,	// FABDv4f32
    0U,	// FABSDr
    0U,	// FABSSr
    0U,	// FABSv2f32
    0U,	// FABSv2f64
    0U,	// FABSv4f32
    1U,	// FACGE32
    1U,	// FACGE64
    1289U,	// FACGEv2f32
    265U,	// FACGEv2f64
    521U,	// FACGEv4f32
    1U,	// FACGT32
    1U,	// FACGT64
    1289U,	// FACGTv2f32
    265U,	// FACGTv2f64
    521U,	// FACGTv4f32
    1U,	// FADDDrr
    1289U,	// FADDPv2f32
    265U,	// FADDPv2f64
    0U,	// FADDPv2i32p
    0U,	// FADDPv2i64p
    521U,	// FADDPv4f32
    1U,	// FADDSrr
    1289U,	// FADDv2f32
    265U,	// FADDv2f64
    521U,	// FADDv4f32
    10497U,	// FCCMPDrr
    10497U,	// FCCMPEDrr
    10497U,	// FCCMPESrr
    10497U,	// FCCMPSrr
    1U,	// FCMEQ32
    1U,	// FCMEQ64
    3U,	// FCMEQv1i32rz
    3U,	// FCMEQv1i64rz
    1289U,	// FCMEQv2f32
    265U,	// FCMEQv2f64
    3U,	// FCMEQv2i32rz
    3U,	// FCMEQv2i64rz
    521U,	// FCMEQv4f32
    3U,	// FCMEQv4i32rz
    1U,	// FCMGE32
    1U,	// FCMGE64
    3U,	// FCMGEv1i32rz
    3U,	// FCMGEv1i64rz
    1289U,	// FCMGEv2f32
    265U,	// FCMGEv2f64
    3U,	// FCMGEv2i32rz
    3U,	// FCMGEv2i64rz
    521U,	// FCMGEv4f32
    3U,	// FCMGEv4i32rz
    1U,	// FCMGT32
    1U,	// FCMGT64
    3U,	// FCMGTv1i32rz
    3U,	// FCMGTv1i64rz
    1289U,	// FCMGTv2f32
    265U,	// FCMGTv2f64
    3U,	// FCMGTv2i32rz
    3U,	// FCMGTv2i64rz
    521U,	// FCMGTv4f32
    3U,	// FCMGTv4i32rz
    3U,	// FCMLEv1i32rz
    3U,	// FCMLEv1i64rz
    3U,	// FCMLEv2i32rz
    3U,	// FCMLEv2i64rz
    3U,	// FCMLEv4i32rz
    3U,	// FCMLTv1i32rz
    3U,	// FCMLTv1i64rz
    3U,	// FCMLTv2i32rz
    3U,	// FCMLTv2i64rz
    3U,	// FCMLTv4i32rz
    0U,	// FCMPDri
    0U,	// FCMPDrr
    0U,	// FCMPEDri
    0U,	// FCMPEDrr
    0U,	// FCMPESri
    0U,	// FCMPESrr
    0U,	// FCMPSri
    0U,	// FCMPSrr
    10497U,	// FCSELDrrr
    10497U,	// FCSELSrrr
    0U,	// FCVTASUWDr
    0U,	// FCVTASUWSr
    0U,	// FCVTASUXDr
    0U,	// FCVTASUXSr
    0U,	// FCVTASv1i32
    0U,	// FCVTASv1i64
    0U,	// FCVTASv2f32
    0U,	// FCVTASv2f64
    0U,	// FCVTASv4f32
    0U,	// FCVTAUUWDr
    0U,	// FCVTAUUWSr
    0U,	// FCVTAUUXDr
    0U,	// FCVTAUUXSr
    0U,	// FCVTAUv1i32
    0U,	// FCVTAUv1i64
    0U,	// FCVTAUv2f32
    0U,	// FCVTAUv2f64
    0U,	// FCVTAUv4f32
    0U,	// FCVTDHr
    0U,	// FCVTDSr
    0U,	// FCVTHDr
    0U,	// FCVTHSr
    0U,	// FCVTLv2i32
    0U,	// FCVTLv4i16
    0U,	// FCVTLv4i32
    0U,	// FCVTLv8i16
    0U,	// FCVTMSUWDr
    0U,	// FCVTMSUWSr
    0U,	// FCVTMSUXDr
    0U,	// FCVTMSUXSr
    0U,	// FCVTMSv1i32
    0U,	// FCVTMSv1i64
    0U,	// FCVTMSv2f32
    0U,	// FCVTMSv2f64
    0U,	// FCVTMSv4f32
    0U,	// FCVTMUUWDr
    0U,	// FCVTMUUWSr
    0U,	// FCVTMUUXDr
    0U,	// FCVTMUUXSr
    0U,	// FCVTMUv1i32
    0U,	// FCVTMUv1i64
    0U,	// FCVTMUv2f32
    0U,	// FCVTMUv2f64
    0U,	// FCVTMUv4f32
    0U,	// FCVTNSUWDr
    0U,	// FCVTNSUWSr
    0U,	// FCVTNSUXDr
    0U,	// FCVTNSUXSr
    0U,	// FCVTNSv1i32
    0U,	// FCVTNSv1i64
    0U,	// FCVTNSv2f32
    0U,	// FCVTNSv2f64
    0U,	// FCVTNSv4f32
    0U,	// FCVTNUUWDr
    0U,	// FCVTNUUWSr
    0U,	// FCVTNUUXDr
    0U,	// FCVTNUUXSr
    0U,	// FCVTNUv1i32
    0U,	// FCVTNUv1i64
    0U,	// FCVTNUv2f32
    0U,	// FCVTNUv2f64
    0U,	// FCVTNUv4f32
    0U,	// FCVTNv2i32
    0U,	// FCVTNv4i16
    0U,	// FCVTNv4i32
    0U,	// FCVTNv8i16
    0U,	// FCVTPSUWDr
    0U,	// FCVTPSUWSr
    0U,	// FCVTPSUXDr
    0U,	// FCVTPSUXSr
    0U,	// FCVTPSv1i32
    0U,	// FCVTPSv1i64
    0U,	// FCVTPSv2f32
    0U,	// FCVTPSv2f64
    0U,	// FCVTPSv4f32
    0U,	// FCVTPUUWDr
    0U,	// FCVTPUUWSr
    0U,	// FCVTPUUXDr
    0U,	// FCVTPUUXSr
    0U,	// FCVTPUv1i32
    0U,	// FCVTPUv1i64
    0U,	// FCVTPUv2f32
    0U,	// FCVTPUv2f64
    0U,	// FCVTPUv4f32
    0U,	// FCVTSDr
    0U,	// FCVTSHr
    0U,	// FCVTXNv1i64
    0U,	// FCVTXNv2f32
    0U,	// FCVTXNv4f32
    1U,	// FCVTZSSWDri
    1U,	// FCVTZSSWSri
    1U,	// FCVTZSSXDri
    1U,	// FCVTZSSXSri
    0U,	// FCVTZSUWDr
    0U,	// FCVTZSUWSr
    0U,	// FCVTZSUXDr
    0U,	// FCVTZSUXSr
    1U,	// FCVTZS_IntSWDri
    1U,	// FCVTZS_IntSWSri
    1U,	// FCVTZS_IntSXDri
    1U,	// FCVTZS_IntSXSri
    0U,	// FCVTZS_IntUWDr
    0U,	// FCVTZS_IntUWSr
    0U,	// FCVTZS_IntUXDr
    0U,	// FCVTZS_IntUXSr
    0U,	// FCVTZS_Intv2f32
    0U,	// FCVTZS_Intv2f64
    0U,	// FCVTZS_Intv4f32
    1U,	// FCVTZSd
    1U,	// FCVTZSs
    0U,	// FCVTZSv1i32
    0U,	// FCVTZSv1i64
    0U,	// FCVTZSv2f32
    0U,	// FCVTZSv2f64
    1U,	// FCVTZSv2i32_shift
    1U,	// FCVTZSv2i64_shift
    0U,	// FCVTZSv4f32
    1U,	// FCVTZSv4i32_shift
    1U,	// FCVTZUSWDri
    1U,	// FCVTZUSWSri
    1U,	// FCVTZUSXDri
    1U,	// FCVTZUSXSri
    0U,	// FCVTZUUWDr
    0U,	// FCVTZUUWSr
    0U,	// FCVTZUUXDr
    0U,	// FCVTZUUXSr
    1U,	// FCVTZU_IntSWDri
    1U,	// FCVTZU_IntSWSri
    1U,	// FCVTZU_IntSXDri
    1U,	// FCVTZU_IntSXSri
    0U,	// FCVTZU_IntUWDr
    0U,	// FCVTZU_IntUWSr
    0U,	// FCVTZU_IntUXDr
    0U,	// FCVTZU_IntUXSr
    0U,	// FCVTZU_Intv2f32
    0U,	// FCVTZU_Intv2f64
    0U,	// FCVTZU_Intv4f32
    1U,	// FCVTZUd
    1U,	// FCVTZUs
    0U,	// FCVTZUv1i32
    0U,	// FCVTZUv1i64
    0U,	// FCVTZUv2f32
    0U,	// FCVTZUv2f64
    1U,	// FCVTZUv2i32_shift
    1U,	// FCVTZUv2i64_shift
    0U,	// FCVTZUv4f32
    1U,	// FCVTZUv4i32_shift
    1U,	// FDIVDrr
    1U,	// FDIVSrr
    1289U,	// FDIVv2f32
    265U,	// FDIVv2f64
    521U,	// FDIVv4f32
    18689U,	// FMADDDrrr
    18689U,	// FMADDSrrr
    1U,	// FMAXDrr
    1U,	// FMAXNMDrr
    1289U,	// FMAXNMPv2f32
    265U,	// FMAXNMPv2f64
    0U,	// FMAXNMPv2i32p
    0U,	// FMAXNMPv2i64p
    521U,	// FMAXNMPv4f32
    1U,	// FMAXNMSrr
    0U,	// FMAXNMVv4i32v
    1289U,	// FMAXNMv2f32
    265U,	// FMAXNMv2f64
    521U,	// FMAXNMv4f32
    1289U,	// FMAXPv2f32
    265U,	// FMAXPv2f64
    0U,	// FMAXPv2i32p
    0U,	// FMAXPv2i64p
    521U,	// FMAXPv4f32
    1U,	// FMAXSrr
    0U,	// FMAXVv4i32v
    1289U,	// FMAXv2f32
    265U,	// FMAXv2f64
    521U,	// FMAXv4f32
    1U,	// FMINDrr
    1U,	// FMINNMDrr
    1289U,	// FMINNMPv2f32
    265U,	// FMINNMPv2f64
    0U,	// FMINNMPv2i32p
    0U,	// FMINNMPv2i64p
    521U,	// FMINNMPv4f32
    1U,	// FMINNMSrr
    0U,	// FMINNMVv4i32v
    1289U,	// FMINNMv2f32
    265U,	// FMINNMv2f64
    521U,	// FMINNMv4f32
    1289U,	// FMINPv2f32
    265U,	// FMINPv2f64
    0U,	// FMINPv2i32p
    0U,	// FMINPv2i64p
    521U,	// FMINPv4f32
    1U,	// FMINSrr
    0U,	// FMINVv4i32v
    1289U,	// FMINv2f32
    265U,	// FMINv2f64
    521U,	// FMINv4f32
    27665U,	// FMLAv1i32_indexed
    27921U,	// FMLAv1i64_indexed
    1297U,	// FMLAv2f32
    273U,	// FMLAv2f64
    27665U,	// FMLAv2i32_indexed
    27921U,	// FMLAv2i64_indexed
    529U,	// FMLAv4f32
    27665U,	// FMLAv4i32_indexed
    27665U,	// FMLSv1i32_indexed
    27921U,	// FMLSv1i64_indexed
    1297U,	// FMLSv2f32
    273U,	// FMLSv2f64
    27665U,	// FMLSv2i32_indexed
    27921U,	// FMLSv2i64_indexed
    529U,	// FMLSv4f32
    27665U,	// FMLSv4i32_indexed
    75U,	// FMOVDXHighr
    0U,	// FMOVDXr
    0U,	// FMOVDi
    0U,	// FMOVDr
    0U,	// FMOVSWr
    0U,	// FMOVSi
    0U,	// FMOVSr
    0U,	// FMOVWSr
    0U,	// FMOVXDHighr
    0U,	// FMOVXDr
    0U,	// FMOVv2f32_ns
    0U,	// FMOVv2f64_ns
    0U,	// FMOVv4f32_ns
    18689U,	// FMSUBDrrr
    18689U,	// FMSUBSrrr
    1U,	// FMULDrr
    1U,	// FMULSrr
    1U,	// FMULX32
    1U,	// FMULX64
    35849U,	// FMULXv1i32_indexed
    36105U,	// FMULXv1i64_indexed
    1289U,	// FMULXv2f32
    265U,	// FMULXv2f64
    35849U,	// FMULXv2i32_indexed
    36105U,	// FMULXv2i64_indexed
    521U,	// FMULXv4f32
    35849U,	// FMULXv4i32_indexed
    35849U,	// FMULv1i32_indexed
    36105U,	// FMULv1i64_indexed
    1289U,	// FMULv2f32
    265U,	// FMULv2f64
    35849U,	// FMULv2i32_indexed
    36105U,	// FMULv2i64_indexed
    521U,	// FMULv4f32
    35849U,	// FMULv4i32_indexed
    0U,	// FNEGDr
    0U,	// FNEGSr
    0U,	// FNEGv2f32
    0U,	// FNEGv2f64
    0U,	// FNEGv4f32
    18689U,	// FNMADDDrrr
    18689U,	// FNMADDSrrr
    18689U,	// FNMSUBDrrr
    18689U,	// FNMSUBSrrr
    1U,	// FNMULDrr
    1U,	// FNMULSrr
    0U,	// FRECPEv1i32
    0U,	// FRECPEv1i64
    0U,	// FRECPEv2f32
    0U,	// FRECPEv2f64
    0U,	// FRECPEv4f32
    1U,	// FRECPS32
    1U,	// FRECPS64
    1289U,	// FRECPSv2f32
    265U,	// FRECPSv2f64
    521U,	// FRECPSv4f32
    0U,	// FRECPXv1i32
    0U,	// FRECPXv1i64
    0U,	// FRINTADr
    0U,	// FRINTASr
    0U,	// FRINTAv2f32
    0U,	// FRINTAv2f64
    0U,	// FRINTAv4f32
    0U,	// FRINTIDr
    0U,	// FRINTISr
    0U,	// FRINTIv2f32
    0U,	// FRINTIv2f64
    0U,	// FRINTIv4f32
    0U,	// FRINTMDr
    0U,	// FRINTMSr
    0U,	// FRINTMv2f32
    0U,	// FRINTMv2f64
    0U,	// FRINTMv4f32
    0U,	// FRINTNDr
    0U,	// FRINTNSr
    0U,	// FRINTNv2f32
    0U,	// FRINTNv2f64
    0U,	// FRINTNv4f32
    0U,	// FRINTPDr
    0U,	// FRINTPSr
    0U,	// FRINTPv2f32
    0U,	// FRINTPv2f64
    0U,	// FRINTPv4f32
    0U,	// FRINTXDr
    0U,	// FRINTXSr
    0U,	// FRINTXv2f32
    0U,	// FRINTXv2f64
    0U,	// FRINTXv4f32
    0U,	// FRINTZDr
    0U,	// FRINTZSr
    0U,	// FRINTZv2f32
    0U,	// FRINTZv2f64
    0U,	// FRINTZv4f32
    0U,	// FRSQRTEv1i32
    0U,	// FRSQRTEv1i64
    0U,	// FRSQRTEv2f32
    0U,	// FRSQRTEv2f64
    0U,	// FRSQRTEv4f32
    1U,	// FRSQRTS32
    1U,	// FRSQRTS64
    1289U,	// FRSQRTSv2f32
    265U,	// FRSQRTSv2f64
    521U,	// FRSQRTSv4f32
    0U,	// FSQRTDr
    0U,	// FSQRTSr
    0U,	// FSQRTv2f32
    0U,	// FSQRTv2f64
    0U,	// FSQRTv4f32
    1U,	// FSUBDrr
    1U,	// FSUBSrr
    1289U,	// FSUBv2f32
    265U,	// FSUBv2f64
    521U,	// FSUBv4f32
    0U,	// HINT
    0U,	// HLT
    0U,	// HVC
    0U,	// INSvi16gpr
    83U,	// INSvi16lane
    0U,	// INSvi32gpr
    83U,	// INSvi32lane
    0U,	// INSvi64gpr
    83U,	// INSvi64lane
    0U,	// INSvi8gpr
    83U,	// INSvi8lane
    0U,	// ISB
    0U,	// LD1Fourv16b
    0U,	// LD1Fourv16b_POST
    0U,	// LD1Fourv1d
    0U,	// LD1Fourv1d_POST
    0U,	// LD1Fourv2d
    0U,	// LD1Fourv2d_POST
    0U,	// LD1Fourv2s
    0U,	// LD1Fourv2s_POST
    0U,	// LD1Fourv4h
    0U,	// LD1Fourv4h_POST
    0U,	// LD1Fourv4s
    0U,	// LD1Fourv4s_POST
    0U,	// LD1Fourv8b
    0U,	// LD1Fourv8b_POST
    0U,	// LD1Fourv8h
    0U,	// LD1Fourv8h_POST
    0U,	// LD1Onev16b
    0U,	// LD1Onev16b_POST
    0U,	// LD1Onev1d
    0U,	// LD1Onev1d_POST
    0U,	// LD1Onev2d
    0U,	// LD1Onev2d_POST
    0U,	// LD1Onev2s
    0U,	// LD1Onev2s_POST
    0U,	// LD1Onev4h
    0U,	// LD1Onev4h_POST
    0U,	// LD1Onev4s
    0U,	// LD1Onev4s_POST
    0U,	// LD1Onev8b
    0U,	// LD1Onev8b_POST
    0U,	// LD1Onev8h
    0U,	// LD1Onev8h_POST
    0U,	// LD1Rv16b
    0U,	// LD1Rv16b_POST
    0U,	// LD1Rv1d
    0U,	// LD1Rv1d_POST
    0U,	// LD1Rv2d
    0U,	// LD1Rv2d_POST
    0U,	// LD1Rv2s
    0U,	// LD1Rv2s_POST
    0U,	// LD1Rv4h
    0U,	// LD1Rv4h_POST
    0U,	// LD1Rv4s
    0U,	// LD1Rv4s_POST
    0U,	// LD1Rv8b
    0U,	// LD1Rv8b_POST
    0U,	// LD1Rv8h
    0U,	// LD1Rv8h_POST
    0U,	// LD1Threev16b
    0U,	// LD1Threev16b_POST
    0U,	// LD1Threev1d
    0U,	// LD1Threev1d_POST
    0U,	// LD1Threev2d
    0U,	// LD1Threev2d_POST
    0U,	// LD1Threev2s
    0U,	// LD1Threev2s_POST
    0U,	// LD1Threev4h
    0U,	// LD1Threev4h_POST
    0U,	// LD1Threev4s
    0U,	// LD1Threev4s_POST
    0U,	// LD1Threev8b
    0U,	// LD1Threev8b_POST
    0U,	// LD1Threev8h
    0U,	// LD1Threev8h_POST
    0U,	// LD1Twov16b
    0U,	// LD1Twov16b_POST
    0U,	// LD1Twov1d
    0U,	// LD1Twov1d_POST
    0U,	// LD1Twov2d
    0U,	// LD1Twov2d_POST
    0U,	// LD1Twov2s
    0U,	// LD1Twov2s_POST
    0U,	// LD1Twov4h
    0U,	// LD1Twov4h_POST
    0U,	// LD1Twov4s
    0U,	// LD1Twov4s_POST
    0U,	// LD1Twov8b
    0U,	// LD1Twov8b_POST
    0U,	// LD1Twov8h
    0U,	// LD1Twov8h_POST
    0U,	// LD1i16
    0U,	// LD1i16_POST
    0U,	// LD1i32
    0U,	// LD1i32_POST
    0U,	// LD1i64
    0U,	// LD1i64_POST
    0U,	// LD1i8
    0U,	// LD1i8_POST
    0U,	// LD2Rv16b
    0U,	// LD2Rv16b_POST
    0U,	// LD2Rv1d
    0U,	// LD2Rv1d_POST
    0U,	// LD2Rv2d
    0U,	// LD2Rv2d_POST
    0U,	// LD2Rv2s
    0U,	// LD2Rv2s_POST
    0U,	// LD2Rv4h
    0U,	// LD2Rv4h_POST
    0U,	// LD2Rv4s
    0U,	// LD2Rv4s_POST
    0U,	// LD2Rv8b
    0U,	// LD2Rv8b_POST
    0U,	// LD2Rv8h
    0U,	// LD2Rv8h_POST
    0U,	// LD2Twov16b
    0U,	// LD2Twov16b_POST
    0U,	// LD2Twov2d
    0U,	// LD2Twov2d_POST
    0U,	// LD2Twov2s
    0U,	// LD2Twov2s_POST
    0U,	// LD2Twov4h
    0U,	// LD2Twov4h_POST
    0U,	// LD2Twov4s
    0U,	// LD2Twov4s_POST
    0U,	// LD2Twov8b
    0U,	// LD2Twov8b_POST
    0U,	// LD2Twov8h
    0U,	// LD2Twov8h_POST
    0U,	// LD2i16
    0U,	// LD2i16_POST
    0U,	// LD2i32
    0U,	// LD2i32_POST
    0U,	// LD2i64
    0U,	// LD2i64_POST
    0U,	// LD2i8
    0U,	// LD2i8_POST
    0U,	// LD3Rv16b
    0U,	// LD3Rv16b_POST
    0U,	// LD3Rv1d
    0U,	// LD3Rv1d_POST
    0U,	// LD3Rv2d
    0U,	// LD3Rv2d_POST
    0U,	// LD3Rv2s
    0U,	// LD3Rv2s_POST
    0U,	// LD3Rv4h
    0U,	// LD3Rv4h_POST
    0U,	// LD3Rv4s
    0U,	// LD3Rv4s_POST
    0U,	// LD3Rv8b
    0U,	// LD3Rv8b_POST
    0U,	// LD3Rv8h
    0U,	// LD3Rv8h_POST
    0U,	// LD3Threev16b
    0U,	// LD3Threev16b_POST
    0U,	// LD3Threev2d
    0U,	// LD3Threev2d_POST
    0U,	// LD3Threev2s
    0U,	// LD3Threev2s_POST
    0U,	// LD3Threev4h
    0U,	// LD3Threev4h_POST
    0U,	// LD3Threev4s
    0U,	// LD3Threev4s_POST
    0U,	// LD3Threev8b
    0U,	// LD3Threev8b_POST
    0U,	// LD3Threev8h
    0U,	// LD3Threev8h_POST
    0U,	// LD3i16
    0U,	// LD3i16_POST
    0U,	// LD3i32
    0U,	// LD3i32_POST
    0U,	// LD3i64
    0U,	// LD3i64_POST
    0U,	// LD3i8
    0U,	// LD3i8_POST
    0U,	// LD4Fourv16b
    0U,	// LD4Fourv16b_POST
    0U,	// LD4Fourv2d
    0U,	// LD4Fourv2d_POST
    0U,	// LD4Fourv2s
    0U,	// LD4Fourv2s_POST
    0U,	// LD4Fourv4h
    0U,	// LD4Fourv4h_POST
    0U,	// LD4Fourv4s
    0U,	// LD4Fourv4s_POST
    0U,	// LD4Fourv8b
    0U,	// LD4Fourv8b_POST
    0U,	// LD4Fourv8h
    0U,	// LD4Fourv8h_POST
    0U,	// LD4Rv16b
    0U,	// LD4Rv16b_POST
    0U,	// LD4Rv1d
    0U,	// LD4Rv1d_POST
    0U,	// LD4Rv2d
    0U,	// LD4Rv2d_POST
    0U,	// LD4Rv2s
    0U,	// LD4Rv2s_POST
    0U,	// LD4Rv4h
    0U,	// LD4Rv4h_POST
    0U,	// LD4Rv4s
    0U,	// LD4Rv4s_POST
    0U,	// LD4Rv8b
    0U,	// LD4Rv8b_POST
    0U,	// LD4Rv8h
    0U,	// LD4Rv8h_POST
    0U,	// LD4i16
    0U,	// LD4i16_POST
    0U,	// LD4i32
    0U,	// LD4i32_POST
    0U,	// LD4i64
    0U,	// LD4i64_POST
    0U,	// LD4i8
    0U,	// LD4i8_POST
    4U,	// LDARB
    4U,	// LDARH
    4U,	// LDARW
    4U,	// LDARX
    3588U,	// LDAXPW
    3588U,	// LDAXPX
    4U,	// LDAXRB
    4U,	// LDAXRH
    4U,	// LDAXRW
    4U,	// LDAXRX
    43268U,	// LDNPDi
    51460U,	// LDNPQi
    59652U,	// LDNPSi
    59652U,	// LDNPWi
    43268U,	// LDNPXi
    43268U,	// LDPDi
    69444U,	// LDPDpost
    330052U,	// LDPDpre
    51460U,	// LDPQi
    77636U,	// LDPQpost
    338244U,	// LDPQpre
    59652U,	// LDPSWi
    85828U,	// LDPSWpost
    346436U,	// LDPSWpre
    59652U,	// LDPSi
    85828U,	// LDPSpost
    346436U,	// LDPSpre
    59652U,	// LDPWi
    85828U,	// LDPWpost
    346436U,	// LDPWpre
    43268U,	// LDPXi
    69444U,	// LDPXpost
    330052U,	// LDPXpre
    4U,	// LDRBBpost
    4161U,	// LDRBBpre
    92417U,	// LDRBBroW
    100609U,	// LDRBBroX
    89U,	// LDRBBui
    4U,	// LDRBpost
    4161U,	// LDRBpre
    92417U,	// LDRBroW
    100609U,	// LDRBroX
    89U,	// LDRBui
    0U,	// LDRDl
    4U,	// LDRDpost
    4161U,	// LDRDpre
    108801U,	// LDRDroW
    116993U,	// LDRDroX
    97U,	// LDRDui
    4U,	// LDRHHpost
    4161U,	// LDRHHpre
    125185U,	// LDRHHroW
    133377U,	// LDRHHroX
    105U,	// LDRHHui
    4U,	// LDRHpost
    4161U,	// LDRHpre
    125185U,	// LDRHroW
    133377U,	// LDRHroX
    105U,	// LDRHui
    0U,	// LDRQl
    4U,	// LDRQpost
    4161U,	// LDRQpre
    141569U,	// LDRQroW
    149761U,	// LDRQroX
    113U,	// LDRQui
    4U,	// LDRSBWpost
    4161U,	// LDRSBWpre
    92417U,	// LDRSBWroW
    100609U,	// LDRSBWroX
    89U,	// LDRSBWui
    4U,	// LDRSBXpost
    4161U,	// LDRSBXpre
    92417U,	// LDRSBXroW
    100609U,	// LDRSBXroX
    89U,	// LDRSBXui
    4U,	// LDRSHWpost
    4161U,	// LDRSHWpre
    125185U,	// LDRSHWroW
    133377U,	// LDRSHWroX
    105U,	// LDRSHWui
    4U,	// LDRSHXpost
    4161U,	// LDRSHXpre
    125185U,	// LDRSHXroW
    133377U,	// LDRSHXroX
    105U,	// LDRSHXui
    0U,	// LDRSWl
    4U,	// LDRSWpost
    4161U,	// LDRSWpre
    157953U,	// LDRSWroW
    166145U,	// LDRSWroX
    121U,	// LDRSWui
    0U,	// LDRSl
    4U,	// LDRSpost
    4161U,	// LDRSpre
    157953U,	// LDRSroW
    166145U,	// LDRSroX
    121U,	// LDRSui
    0U,	// LDRWl
    4U,	// LDRWpost
    4161U,	// LDRWpre
    157953U,	// LDRWroW
    166145U,	// LDRWroX
    121U,	// LDRWui
    0U,	// LDRXl
    4U,	// LDRXpost
    4161U,	// LDRXpre
    108801U,	// LDRXroW
    116993U,	// LDRXroX
    97U,	// LDRXui
    3585U,	// LDTRBi
    3585U,	// LDTRHi
    3585U,	// LDTRSBWi
    3585U,	// LDTRSBXi
    3585U,	// LDTRSHWi
    3585U,	// LDTRSHXi
    3585U,	// LDTRSWi
    3585U,	// LDTRWi
    3585U,	// LDTRXi
    3585U,	// LDURBBi
    3585U,	// LDURBi
    3585U,	// LDURDi
    3585U,	// LDURHHi
    3585U,	// LDURHi
    3585U,	// LDURQi
    3585U,	// LDURSBWi
    3585U,	// LDURSBXi
    3585U,	// LDURSHWi
    3585U,	// LDURSHXi
    3585U,	// LDURSWi
    3585U,	// LDURSi
    3585U,	// LDURWi
    3585U,	// LDURXi
    3588U,	// LDXPW
    3588U,	// LDXPX
    4U,	// LDXRB
    4U,	// LDXRH
    4U,	// LDXRW
    4U,	// LDXRX
    0U,	// LOADgot
    1U,	// LSLVWr
    1U,	// LSLVXr
    1U,	// LSRVWr
    1U,	// LSRVXr
    18689U,	// MADDWrrr
    18689U,	// MADDXrrr
    1041U,	// MLAv16i8
    1297U,	// MLAv2i32
    27665U,	// MLAv2i32_indexed
    1553U,	// MLAv4i16
    28945U,	// MLAv4i16_indexed
    529U,	// MLAv4i32
    27665U,	// MLAv4i32_indexed
    785U,	// MLAv8i16
    28945U,	// MLAv8i16_indexed
    1809U,	// MLAv8i8
    1041U,	// MLSv16i8
    1297U,	// MLSv2i32
    27665U,	// MLSv2i32_indexed
    1553U,	// MLSv4i16
    28945U,	// MLSv4i16_indexed
    529U,	// MLSv4i32
    27665U,	// MLSv4i32_indexed
    785U,	// MLSv8i16
    28945U,	// MLSv8i16_indexed
    1809U,	// MLSv8i8
    0U,	// MOVID
    0U,	// MOVIv16b_ns
    0U,	// MOVIv2d_ns
    4U,	// MOVIv2i32
    4U,	// MOVIv2s_msl
    4U,	// MOVIv4i16
    4U,	// MOVIv4i32
    4U,	// MOVIv4s_msl
    0U,	// MOVIv8b_ns
    4U,	// MOVIv8i16
    0U,	// MOVKWi
    0U,	// MOVKXi
    4U,	// MOVNWi
    4U,	// MOVNXi
    4U,	// MOVZWi
    4U,	// MOVZXi
    0U,	// MOVaddr
    0U,	// MOVaddrBA
    0U,	// MOVaddrCP
    0U,	// MOVaddrEXT
    0U,	// MOVaddrJT
    0U,	// MOVaddrTLS
    0U,	// MOVi32imm
    0U,	// MOVi64imm
    0U,	// MRS
    0U,	// MSR
    0U,	// MSRpstate
    18689U,	// MSUBWrrr
    18689U,	// MSUBXrrr
    1033U,	// MULv16i8
    1289U,	// MULv2i32
    35849U,	// MULv2i32_indexed
    1545U,	// MULv4i16
    37129U,	// MULv4i16_indexed
    521U,	// MULv4i32
    35849U,	// MULv4i32_indexed
    777U,	// MULv8i16
    37129U,	// MULv8i16_indexed
    1801U,	// MULv8i8
    4U,	// MVNIv2i32
    4U,	// MVNIv2s_msl
    4U,	// MVNIv4i16
    4U,	// MVNIv4i32
    4U,	// MVNIv4s_msl
    4U,	// MVNIv8i16
    0U,	// NEGv16i8
    0U,	// NEGv1i64
    0U,	// NEGv2i32
    0U,	// NEGv2i64
    0U,	// NEGv4i16
    0U,	// NEGv4i32
    0U,	// NEGv8i16
    0U,	// NEGv8i8
    0U,	// NOTv16i8
    0U,	// NOTv8i8
    0U,	// ORNWrr
    33U,	// ORNWrs
    0U,	// ORNXrr
    33U,	// ORNXrs
    1033U,	// ORNv16i8
    1801U,	// ORNv8i8
    49U,	// ORRWri
    0U,	// ORRWrr
    33U,	// ORRWrs
    57U,	// ORRXri
    0U,	// ORRXrr
    33U,	// ORRXrs
    1033U,	// ORRv16i8
    0U,	// ORRv2i32
    0U,	// ORRv4i16
    0U,	// ORRv4i32
    0U,	// ORRv8i16
    1801U,	// ORRv8i8
    1033U,	// PMULLv16i8
    0U,	// PMULLv1i64
    0U,	// PMULLv2i64
    1801U,	// PMULLv8i8
    1033U,	// PMULv16i8
    1801U,	// PMULv8i8
    0U,	// PRFMl
    108801U,	// PRFMroW
    116993U,	// PRFMroX
    97U,	// PRFMui
    3585U,	// PRFUMi
    265U,	// RADDHNv2i64_v2i32
    273U,	// RADDHNv2i64_v4i32
    521U,	// RADDHNv4i32_v4i16
    529U,	// RADDHNv4i32_v8i16
    785U,	// RADDHNv8i16_v16i8
    777U,	// RADDHNv8i16_v8i8
    0U,	// RBITWr
    0U,	// RBITXr
    0U,	// RBITv16i8
    0U,	// RBITv8i8
    0U,	// RET
    0U,	// RET_ReallyLR
    0U,	// REV16Wr
    0U,	// REV16Xr
    0U,	// REV16v16i8
    0U,	// REV16v8i8
    0U,	// REV32Xr
    0U,	// REV32v16i8
    0U,	// REV32v4i16
    0U,	// REV32v8i16
    0U,	// REV32v8i8
    0U,	// REV64v16i8
    0U,	// REV64v2i32
    0U,	// REV64v4i16
    0U,	// REV64v4i32
    0U,	// REV64v8i16
    0U,	// REV64v8i8
    0U,	// REVWr
    0U,	// REVXr
    1U,	// RORVWr
    1U,	// RORVXr
    65U,	// RSHRNv16i8_shift
    1U,	// RSHRNv2i32_shift
    1U,	// RSHRNv4i16_shift
    65U,	// RSHRNv4i32_shift
    65U,	// RSHRNv8i16_shift
    1U,	// RSHRNv8i8_shift
    265U,	// RSUBHNv2i64_v2i32
    273U,	// RSUBHNv2i64_v4i32
    521U,	// RSUBHNv4i32_v4i16
    529U,	// RSUBHNv4i32_v8i16
    785U,	// RSUBHNv8i16_v16i8
    777U,	// RSUBHNv8i16_v8i8
    1041U,	// SABALv16i8_v8i16
    1297U,	// SABALv2i32_v2i64
    1553U,	// SABALv4i16_v4i32
    529U,	// SABALv4i32_v2i64
    785U,	// SABALv8i16_v4i32
    1809U,	// SABALv8i8_v8i16
    1041U,	// SABAv16i8
    1297U,	// SABAv2i32
    1553U,	// SABAv4i16
    529U,	// SABAv4i32
    785U,	// SABAv8i16
    1809U,	// SABAv8i8
    1033U,	// SABDLv16i8_v8i16
    1289U,	// SABDLv2i32_v2i64
    1545U,	// SABDLv4i16_v4i32
    521U,	// SABDLv4i32_v2i64
    777U,	// SABDLv8i16_v4i32
    1801U,	// SABDLv8i8_v8i16
    1033U,	// SABDv16i8
    1289U,	// SABDv2i32
    1545U,	// SABDv4i16
    521U,	// SABDv4i32
    777U,	// SABDv8i16
    1801U,	// SABDv8i8
    0U,	// SADALPv16i8_v8i16
    0U,	// SADALPv2i32_v1i64
    0U,	// SADALPv4i16_v2i32
    0U,	// SADALPv4i32_v2i64
    0U,	// SADALPv8i16_v4i32
    0U,	// SADALPv8i8_v4i16
    0U,	// SADDLPv16i8_v8i16
    0U,	// SADDLPv2i32_v1i64
    0U,	// SADDLPv4i16_v2i32
    0U,	// SADDLPv4i32_v2i64
    0U,	// SADDLPv8i16_v4i32
    0U,	// SADDLPv8i8_v4i16
    0U,	// SADDLVv16i8v
    0U,	// SADDLVv4i16v
    0U,	// SADDLVv4i32v
    0U,	// SADDLVv8i16v
    0U,	// SADDLVv8i8v
    1033U,	// SADDLv16i8_v8i16
    1289U,	// SADDLv2i32_v2i64
    1545U,	// SADDLv4i16_v4i32
    521U,	// SADDLv4i32_v2i64
    777U,	// SADDLv8i16_v4i32
    1801U,	// SADDLv8i8_v8i16
    1033U,	// SADDWv16i8_v8i16
    1289U,	// SADDWv2i32_v2i64
    1545U,	// SADDWv4i16_v4i32
    521U,	// SADDWv4i32_v2i64
    777U,	// SADDWv8i16_v4i32
    1801U,	// SADDWv8i8_v8i16
    1U,	// SBCSWr
    1U,	// SBCSXr
    1U,	// SBCWr
    1U,	// SBCXr
    18689U,	// SBFMWri
    18689U,	// SBFMXri
    1U,	// SCVTFSWDri
    1U,	// SCVTFSWSri
    1U,	// SCVTFSXDri
    1U,	// SCVTFSXSri
    0U,	// SCVTFUWDri
    0U,	// SCVTFUWSri
    0U,	// SCVTFUXDri
    0U,	// SCVTFUXSri
    1U,	// SCVTFd
    1U,	// SCVTFs
    0U,	// SCVTFv1i32
    0U,	// SCVTFv1i64
    0U,	// SCVTFv2f32
    0U,	// SCVTFv2f64
    1U,	// SCVTFv2i32_shift
    1U,	// SCVTFv2i64_shift
    0U,	// SCVTFv4f32
    1U,	// SCVTFv4i32_shift
    1U,	// SDIVWr
    1U,	// SDIVXr
    1U,	// SDIV_IntWr
    1U,	// SDIV_IntXr
    529U,	// SHA1Crrr
    0U,	// SHA1Hrr
    529U,	// SHA1Mrrr
    529U,	// SHA1Prrr
    529U,	// SHA1SU0rrr
    0U,	// SHA1SU1rr
    529U,	// SHA256H2rrr
    529U,	// SHA256Hrrr
    0U,	// SHA256SU0rr
    529U,	// SHA256SU1rrr
    1033U,	// SHADDv16i8
    1289U,	// SHADDv2i32
    1545U,	// SHADDv4i16
    521U,	// SHADDv4i32
    777U,	// SHADDv8i16
    1801U,	// SHADDv8i8
    4U,	// SHLLv16i8
    4U,	// SHLLv2i32
    4U,	// SHLLv4i16
    4U,	// SHLLv4i32
    5U,	// SHLLv8i16
    5U,	// SHLLv8i8
    1U,	// SHLd
    1U,	// SHLv16i8_shift
    1U,	// SHLv2i32_shift
    1U,	// SHLv2i64_shift
    1U,	// SHLv4i16_shift
    1U,	// SHLv4i32_shift
    1U,	// SHLv8i16_shift
    1U,	// SHLv8i8_shift
    65U,	// SHRNv16i8_shift
    1U,	// SHRNv2i32_shift
    1U,	// SHRNv4i16_shift
    65U,	// SHRNv4i32_shift
    65U,	// SHRNv8i16_shift
    1U,	// SHRNv8i8_shift
    1033U,	// SHSUBv16i8
    1289U,	// SHSUBv2i32
    1545U,	// SHSUBv4i16
    521U,	// SHSUBv4i32
    777U,	// SHSUBv8i16
    1801U,	// SHSUBv8i8
    65U,	// SLId
    65U,	// SLIv16i8_shift
    65U,	// SLIv2i32_shift
    65U,	// SLIv2i64_shift
    65U,	// SLIv4i16_shift
    65U,	// SLIv4i32_shift
    65U,	// SLIv8i16_shift
    65U,	// SLIv8i8_shift
    18689U,	// SMADDLrrr
    1033U,	// SMAXPv16i8
    1289U,	// SMAXPv2i32
    1545U,	// SMAXPv4i16
    521U,	// SMAXPv4i32
    777U,	// SMAXPv8i16
    1801U,	// SMAXPv8i8
    0U,	// SMAXVv16i8v
    0U,	// SMAXVv4i16v
    0U,	// SMAXVv4i32v
    0U,	// SMAXVv8i16v
    0U,	// SMAXVv8i8v
    1033U,	// SMAXv16i8
    1289U,	// SMAXv2i32
    1545U,	// SMAXv4i16
    521U,	// SMAXv4i32
    777U,	// SMAXv8i16
    1801U,	// SMAXv8i8
    0U,	// SMC
    1033U,	// SMINPv16i8
    1289U,	// SMINPv2i32
    1545U,	// SMINPv4i16
    521U,	// SMINPv4i32
    777U,	// SMINPv8i16
    1801U,	// SMINPv8i8
    0U,	// SMINVv16i8v
    0U,	// SMINVv4i16v
    0U,	// SMINVv4i32v
    0U,	// SMINVv8i16v
    0U,	// SMINVv8i8v
    1033U,	// SMINv16i8
    1289U,	// SMINv2i32
    1545U,	// SMINv4i16
    521U,	// SMINv4i32
    777U,	// SMINv8i16
    1801U,	// SMINv8i8
    1041U,	// SMLALv16i8_v8i16
    27665U,	// SMLALv2i32_indexed
    1297U,	// SMLALv2i32_v2i64
    28945U,	// SMLALv4i16_indexed
    1553U,	// SMLALv4i16_v4i32
    27665U,	// SMLALv4i32_indexed
    529U,	// SMLALv4i32_v2i64
    28945U,	// SMLALv8i16_indexed
    785U,	// SMLALv8i16_v4i32
    1809U,	// SMLALv8i8_v8i16
    1041U,	// SMLSLv16i8_v8i16
    27665U,	// SMLSLv2i32_indexed
    1297U,	// SMLSLv2i32_v2i64
    28945U,	// SMLSLv4i16_indexed
    1553U,	// SMLSLv4i16_v4i32
    27665U,	// SMLSLv4i32_indexed
    529U,	// SMLSLv4i32_v2i64
    28945U,	// SMLSLv8i16_indexed
    785U,	// SMLSLv8i16_v4i32
    1809U,	// SMLSLv8i8_v8i16
    75U,	// SMOVvi16to32
    75U,	// SMOVvi16to64
    75U,	// SMOVvi32to64
    75U,	// SMOVvi8to32
    75U,	// SMOVvi8to64
    18689U,	// SMSUBLrrr
    1U,	// SMULHrr
    1033U,	// SMULLv16i8_v8i16
    35849U,	// SMULLv2i32_indexed
    1289U,	// SMULLv2i32_v2i64
    37129U,	// SMULLv4i16_indexed
    1545U,	// SMULLv4i16_v4i32
    35849U,	// SMULLv4i32_indexed
    521U,	// SMULLv4i32_v2i64
    37129U,	// SMULLv8i16_indexed
    777U,	// SMULLv8i16_v4i32
    1801U,	// SMULLv8i8_v8i16
    0U,	// SQABSv16i8
    0U,	// SQABSv1i16
    0U,	// SQABSv1i32
    0U,	// SQABSv1i64
    0U,	// SQABSv1i8
    0U,	// SQABSv2i32
    0U,	// SQABSv2i64
    0U,	// SQABSv4i16
    0U,	// SQABSv4i32
    0U,	// SQABSv8i16
    0U,	// SQABSv8i8
    1033U,	// SQADDv16i8
    1U,	// SQADDv1i16
    1U,	// SQADDv1i32
    1U,	// SQADDv1i64
    1U,	// SQADDv1i8
    1289U,	// SQADDv2i32
    265U,	// SQADDv2i64
    1545U,	// SQADDv4i16
    521U,	// SQADDv4i32
    777U,	// SQADDv8i16
    1801U,	// SQADDv8i8
    65U,	// SQDMLALi16
    65U,	// SQDMLALi32
    28945U,	// SQDMLALv1i32_indexed
    27665U,	// SQDMLALv1i64_indexed
    27665U,	// SQDMLALv2i32_indexed
    1297U,	// SQDMLALv2i32_v2i64
    28945U,	// SQDMLALv4i16_indexed
    1553U,	// SQDMLALv4i16_v4i32
    27665U,	// SQDMLALv4i32_indexed
    529U,	// SQDMLALv4i32_v2i64
    28945U,	// SQDMLALv8i16_indexed
    785U,	// SQDMLALv8i16_v4i32
    65U,	// SQDMLSLi16
    65U,	// SQDMLSLi32
    28945U,	// SQDMLSLv1i32_indexed
    27665U,	// SQDMLSLv1i64_indexed
    27665U,	// SQDMLSLv2i32_indexed
    1297U,	// SQDMLSLv2i32_v2i64
    28945U,	// SQDMLSLv4i16_indexed
    1553U,	// SQDMLSLv4i16_v4i32
    27665U,	// SQDMLSLv4i32_indexed
    529U,	// SQDMLSLv4i32_v2i64
    28945U,	// SQDMLSLv8i16_indexed
    785U,	// SQDMLSLv8i16_v4i32
    1U,	// SQDMULHv1i16
    37129U,	// SQDMULHv1i16_indexed
    1U,	// SQDMULHv1i32
    35849U,	// SQDMULHv1i32_indexed
    1289U,	// SQDMULHv2i32
    35849U,	// SQDMULHv2i32_indexed
    1545U,	// SQDMULHv4i16
    37129U,	// SQDMULHv4i16_indexed
    521U,	// SQDMULHv4i32
    35849U,	// SQDMULHv4i32_indexed
    777U,	// SQDMULHv8i16
    37129U,	// SQDMULHv8i16_indexed
    1U,	// SQDMULLi16
    1U,	// SQDMULLi32
    37129U,	// SQDMULLv1i32_indexed
    35849U,	// SQDMULLv1i64_indexed
    35849U,	// SQDMULLv2i32_indexed
    1289U,	// SQDMULLv2i32_v2i64
    37129U,	// SQDMULLv4i16_indexed
    1545U,	// SQDMULLv4i16_v4i32
    35849U,	// SQDMULLv4i32_indexed
    521U,	// SQDMULLv4i32_v2i64
    37129U,	// SQDMULLv8i16_indexed
    777U,	// SQDMULLv8i16_v4i32
    0U,	// SQNEGv16i8
    0U,	// SQNEGv1i16
    0U,	// SQNEGv1i32
    0U,	// SQNEGv1i64
    0U,	// SQNEGv1i8
    0U,	// SQNEGv2i32
    0U,	// SQNEGv2i64
    0U,	// SQNEGv4i16
    0U,	// SQNEGv4i32
    0U,	// SQNEGv8i16
    0U,	// SQNEGv8i8
    1U,	// SQRDMULHv1i16
    37129U,	// SQRDMULHv1i16_indexed
    1U,	// SQRDMULHv1i32
    35849U,	// SQRDMULHv1i32_indexed
    1289U,	// SQRDMULHv2i32
    35849U,	// SQRDMULHv2i32_indexed
    1545U,	// SQRDMULHv4i16
    37129U,	// SQRDMULHv4i16_indexed
    521U,	// SQRDMULHv4i32
    35849U,	// SQRDMULHv4i32_indexed
    777U,	// SQRDMULHv8i16
    37129U,	// SQRDMULHv8i16_indexed
    1033U,	// SQRSHLv16i8
    1U,	// SQRSHLv1i16
    1U,	// SQRSHLv1i32
    1U,	// SQRSHLv1i64
    1U,	// SQRSHLv1i8
    1289U,	// SQRSHLv2i32
    265U,	// SQRSHLv2i64
    1545U,	// SQRSHLv4i16
    521U,	// SQRSHLv4i32
    777U,	// SQRSHLv8i16
    1801U,	// SQRSHLv8i8
    1U,	// SQRSHRNb
    1U,	// SQRSHRNh
    1U,	// SQRSHRNs
    65U,	// SQRSHRNv16i8_shift
    1U,	// SQRSHRNv2i32_shift
    1U,	// SQRSHRNv4i16_shift
    65U,	// SQRSHRNv4i32_shift
    65U,	// SQRSHRNv8i16_shift
    1U,	// SQRSHRNv8i8_shift
    1U,	// SQRSHRUNb
    1U,	// SQRSHRUNh
    1U,	// SQRSHRUNs
    65U,	// SQRSHRUNv16i8_shift
    1U,	// SQRSHRUNv2i32_shift
    1U,	// SQRSHRUNv4i16_shift
    65U,	// SQRSHRUNv4i32_shift
    65U,	// SQRSHRUNv8i16_shift
    1U,	// SQRSHRUNv8i8_shift
    1U,	// SQSHLUb
    1U,	// SQSHLUd
    1U,	// SQSHLUh
    1U,	// SQSHLUs
    1U,	// SQSHLUv16i8_shift
    1U,	// SQSHLUv2i32_shift
    1U,	// SQSHLUv2i64_shift
    1U,	// SQSHLUv4i16_shift
    1U,	// SQSHLUv4i32_shift
    1U,	// SQSHLUv8i16_shift
    1U,	// SQSHLUv8i8_shift
    1U,	// SQSHLb
    1U,	// SQSHLd
    1U,	// SQSHLh
    1U,	// SQSHLs
    1033U,	// SQSHLv16i8
    1U,	// SQSHLv16i8_shift
    1U,	// SQSHLv1i16
    1U,	// SQSHLv1i32
    1U,	// SQSHLv1i64
    1U,	// SQSHLv1i8
    1289U,	// SQSHLv2i32
    1U,	// SQSHLv2i32_shift
    265U,	// SQSHLv2i64
    1U,	// SQSHLv2i64_shift
    1545U,	// SQSHLv4i16
    1U,	// SQSHLv4i16_shift
    521U,	// SQSHLv4i32
    1U,	// SQSHLv4i32_shift
    777U,	// SQSHLv8i16
    1U,	// SQSHLv8i16_shift
    1801U,	// SQSHLv8i8
    1U,	// SQSHLv8i8_shift
    1U,	// SQSHRNb
    1U,	// SQSHRNh
    1U,	// SQSHRNs
    65U,	// SQSHRNv16i8_shift
    1U,	// SQSHRNv2i32_shift
    1U,	// SQSHRNv4i16_shift
    65U,	// SQSHRNv4i32_shift
    65U,	// SQSHRNv8i16_shift
    1U,	// SQSHRNv8i8_shift
    1U,	// SQSHRUNb
    1U,	// SQSHRUNh
    1U,	// SQSHRUNs
    65U,	// SQSHRUNv16i8_shift
    1U,	// SQSHRUNv2i32_shift
    1U,	// SQSHRUNv4i16_shift
    65U,	// SQSHRUNv4i32_shift
    65U,	// SQSHRUNv8i16_shift
    1U,	// SQSHRUNv8i8_shift
    1033U,	// SQSUBv16i8
    1U,	// SQSUBv1i16
    1U,	// SQSUBv1i32
    1U,	// SQSUBv1i64
    1U,	// SQSUBv1i8
    1289U,	// SQSUBv2i32
    265U,	// SQSUBv2i64
    1545U,	// SQSUBv4i16
    521U,	// SQSUBv4i32
    777U,	// SQSUBv8i16
    1801U,	// SQSUBv8i8
    0U,	// SQXTNv16i8
    0U,	// SQXTNv1i16
    0U,	// SQXTNv1i32
    0U,	// SQXTNv1i8
    0U,	// SQXTNv2i32
    0U,	// SQXTNv4i16
    0U,	// SQXTNv4i32
    0U,	// SQXTNv8i16
    0U,	// SQXTNv8i8
    0U,	// SQXTUNv16i8
    0U,	// SQXTUNv1i16
    0U,	// SQXTUNv1i32
    0U,	// SQXTUNv1i8
    0U,	// SQXTUNv2i32
    0U,	// SQXTUNv4i16
    0U,	// SQXTUNv4i32
    0U,	// SQXTUNv8i16
    0U,	// SQXTUNv8i8
    1033U,	// SRHADDv16i8
    1289U,	// SRHADDv2i32
    1545U,	// SRHADDv4i16
    521U,	// SRHADDv4i32
    777U,	// SRHADDv8i16
    1801U,	// SRHADDv8i8
    65U,	// SRId
    65U,	// SRIv16i8_shift
    65U,	// SRIv2i32_shift
    65U,	// SRIv2i64_shift
    65U,	// SRIv4i16_shift
    65U,	// SRIv4i32_shift
    65U,	// SRIv8i16_shift
    65U,	// SRIv8i8_shift
    1033U,	// SRSHLv16i8
    1U,	// SRSHLv1i64
    1289U,	// SRSHLv2i32
    265U,	// SRSHLv2i64
    1545U,	// SRSHLv4i16
    521U,	// SRSHLv4i32
    777U,	// SRSHLv8i16
    1801U,	// SRSHLv8i8
    1U,	// SRSHRd
    1U,	// SRSHRv16i8_shift
    1U,	// SRSHRv2i32_shift
    1U,	// SRSHRv2i64_shift
    1U,	// SRSHRv4i16_shift
    1U,	// SRSHRv4i32_shift
    1U,	// SRSHRv8i16_shift
    1U,	// SRSHRv8i8_shift
    65U,	// SRSRAd
    65U,	// SRSRAv16i8_shift
    65U,	// SRSRAv2i32_shift
    65U,	// SRSRAv2i64_shift
    65U,	// SRSRAv4i16_shift
    65U,	// SRSRAv4i32_shift
    65U,	// SRSRAv8i16_shift
    65U,	// SRSRAv8i8_shift
    1U,	// SSHLLv16i8_shift
    1U,	// SSHLLv2i32_shift
    1U,	// SSHLLv4i16_shift
    1U,	// SSHLLv4i32_shift
    1U,	// SSHLLv8i16_shift
    1U,	// SSHLLv8i8_shift
    1033U,	// SSHLv16i8
    1U,	// SSHLv1i64
    1289U,	// SSHLv2i32
    265U,	// SSHLv2i64
    1545U,	// SSHLv4i16
    521U,	// SSHLv4i32
    777U,	// SSHLv8i16
    1801U,	// SSHLv8i8
    1U,	// SSHRd
    1U,	// SSHRv16i8_shift
    1U,	// SSHRv2i32_shift
    1U,	// SSHRv2i64_shift
    1U,	// SSHRv4i16_shift
    1U,	// SSHRv4i32_shift
    1U,	// SSHRv8i16_shift
    1U,	// SSHRv8i8_shift
    65U,	// SSRAd
    65U,	// SSRAv16i8_shift
    65U,	// SSRAv2i32_shift
    65U,	// SSRAv2i64_shift
    65U,	// SSRAv4i16_shift
    65U,	// SSRAv4i32_shift
    65U,	// SSRAv8i16_shift
    65U,	// SSRAv8i8_shift
    1033U,	// SSUBLv16i8_v8i16
    1289U,	// SSUBLv2i32_v2i64
    1545U,	// SSUBLv4i16_v4i32
    521U,	// SSUBLv4i32_v2i64
    777U,	// SSUBLv8i16_v4i32
    1801U,	// SSUBLv8i8_v8i16
    1033U,	// SSUBWv16i8_v8i16
    1289U,	// SSUBWv2i32_v2i64
    1545U,	// SSUBWv4i16_v4i32
    521U,	// SSUBWv4i32_v2i64
    777U,	// SSUBWv8i16_v4i32
    1801U,	// SSUBWv8i8_v8i16
    0U,	// ST1Fourv16b
    0U,	// ST1Fourv16b_POST
    0U,	// ST1Fourv1d
    0U,	// ST1Fourv1d_POST
    0U,	// ST1Fourv2d
    0U,	// ST1Fourv2d_POST
    0U,	// ST1Fourv2s
    0U,	// ST1Fourv2s_POST
    0U,	// ST1Fourv4h
    0U,	// ST1Fourv4h_POST
    0U,	// ST1Fourv4s
    0U,	// ST1Fourv4s_POST
    0U,	// ST1Fourv8b
    0U,	// ST1Fourv8b_POST
    0U,	// ST1Fourv8h
    0U,	// ST1Fourv8h_POST
    0U,	// ST1Onev16b
    0U,	// ST1Onev16b_POST
    0U,	// ST1Onev1d
    0U,	// ST1Onev1d_POST
    0U,	// ST1Onev2d
    0U,	// ST1Onev2d_POST
    0U,	// ST1Onev2s
    0U,	// ST1Onev2s_POST
    0U,	// ST1Onev4h
    0U,	// ST1Onev4h_POST
    0U,	// ST1Onev4s
    0U,	// ST1Onev4s_POST
    0U,	// ST1Onev8b
    0U,	// ST1Onev8b_POST
    0U,	// ST1Onev8h
    0U,	// ST1Onev8h_POST
    0U,	// ST1Threev16b
    0U,	// ST1Threev16b_POST
    0U,	// ST1Threev1d
    0U,	// ST1Threev1d_POST
    0U,	// ST1Threev2d
    0U,	// ST1Threev2d_POST
    0U,	// ST1Threev2s
    0U,	// ST1Threev2s_POST
    0U,	// ST1Threev4h
    0U,	// ST1Threev4h_POST
    0U,	// ST1Threev4s
    0U,	// ST1Threev4s_POST
    0U,	// ST1Threev8b
    0U,	// ST1Threev8b_POST
    0U,	// ST1Threev8h
    0U,	// ST1Threev8h_POST
    0U,	// ST1Twov16b
    0U,	// ST1Twov16b_POST
    0U,	// ST1Twov1d
    0U,	// ST1Twov1d_POST
    0U,	// ST1Twov2d
    0U,	// ST1Twov2d_POST
    0U,	// ST1Twov2s
    0U,	// ST1Twov2s_POST
    0U,	// ST1Twov4h
    0U,	// ST1Twov4h_POST
    0U,	// ST1Twov4s
    0U,	// ST1Twov4s_POST
    0U,	// ST1Twov8b
    0U,	// ST1Twov8b_POST
    0U,	// ST1Twov8h
    0U,	// ST1Twov8h_POST
    0U,	// ST1i16
    0U,	// ST1i16_POST
    0U,	// ST1i32
    0U,	// ST1i32_POST
    0U,	// ST1i64
    0U,	// ST1i64_POST
    0U,	// ST1i8
    0U,	// ST1i8_POST
    0U,	// ST2Twov16b
    0U,	// ST2Twov16b_POST
    0U,	// ST2Twov2d
    0U,	// ST2Twov2d_POST
    0U,	// ST2Twov2s
    0U,	// ST2Twov2s_POST
    0U,	// ST2Twov4h
    0U,	// ST2Twov4h_POST
    0U,	// ST2Twov4s
    0U,	// ST2Twov4s_POST
    0U,	// ST2Twov8b
    0U,	// ST2Twov8b_POST
    0U,	// ST2Twov8h
    0U,	// ST2Twov8h_POST
    0U,	// ST2i16
    0U,	// ST2i16_POST
    0U,	// ST2i32
    0U,	// ST2i32_POST
    0U,	// ST2i64
    0U,	// ST2i64_POST
    0U,	// ST2i8
    0U,	// ST2i8_POST
    0U,	// ST3Threev16b
    0U,	// ST3Threev16b_POST
    0U,	// ST3Threev2d
    0U,	// ST3Threev2d_POST
    0U,	// ST3Threev2s
    0U,	// ST3Threev2s_POST
    0U,	// ST3Threev4h
    0U,	// ST3Threev4h_POST
    0U,	// ST3Threev4s
    0U,	// ST3Threev4s_POST
    0U,	// ST3Threev8b
    0U,	// ST3Threev8b_POST
    0U,	// ST3Threev8h
    0U,	// ST3Threev8h_POST
    0U,	// ST3i16
    0U,	// ST3i16_POST
    0U,	// ST3i32
    0U,	// ST3i32_POST
    0U,	// ST3i64
    0U,	// ST3i64_POST
    0U,	// ST3i8
    0U,	// ST3i8_POST
    0U,	// ST4Fourv16b
    0U,	// ST4Fourv16b_POST
    0U,	// ST4Fourv2d
    0U,	// ST4Fourv2d_POST
    0U,	// ST4Fourv2s
    0U,	// ST4Fourv2s_POST
    0U,	// ST4Fourv4h
    0U,	// ST4Fourv4h_POST
    0U,	// ST4Fourv4s
    0U,	// ST4Fourv4s_POST
    0U,	// ST4Fourv8b
    0U,	// ST4Fourv8b_POST
    0U,	// ST4Fourv8h
    0U,	// ST4Fourv8h_POST
    0U,	// ST4i16
    0U,	// ST4i16_POST
    0U,	// ST4i32
    0U,	// ST4i32_POST
    0U,	// ST4i64
    0U,	// ST4i64_POST
    0U,	// ST4i8
    0U,	// ST4i8_POST
    4U,	// STLRB
    4U,	// STLRH
    4U,	// STLRW
    4U,	// STLRX
    4609U,	// STLXPW
    4609U,	// STLXPX
    3588U,	// STLXRB
    3588U,	// STLXRH
    3588U,	// STLXRW
    3588U,	// STLXRX
    43268U,	// STNPDi
    51460U,	// STNPQi
    59652U,	// STNPSi
    59652U,	// STNPWi
    43268U,	// STNPXi
    43268U,	// STPDi
    69444U,	// STPDpost
    330052U,	// STPDpre
    51460U,	// STPQi
    77636U,	// STPQpost
    338244U,	// STPQpre
    59652U,	// STPSi
    85828U,	// STPSpost
    346436U,	// STPSpre
    59652U,	// STPWi
    85828U,	// STPWpost
    346436U,	// STPWpre
    43268U,	// STPXi
    69444U,	// STPXpost
    330052U,	// STPXpre
    4U,	// STRBBpost
    4161U,	// STRBBpre
    92417U,	// STRBBroW
    100609U,	// STRBBroX
    89U,	// STRBBui
    4U,	// STRBpost
    4161U,	// STRBpre
    92417U,	// STRBroW
    100609U,	// STRBroX
    89U,	// STRBui
    4U,	// STRDpost
    4161U,	// STRDpre
    108801U,	// STRDroW
    116993U,	// STRDroX
    97U,	// STRDui
    4U,	// STRHHpost
    4161U,	// STRHHpre
    125185U,	// STRHHroW
    133377U,	// STRHHroX
    105U,	// STRHHui
    4U,	// STRHpost
    4161U,	// STRHpre
    125185U,	// STRHroW
    133377U,	// STRHroX
    105U,	// STRHui
    4U,	// STRQpost
    4161U,	// STRQpre
    141569U,	// STRQroW
    149761U,	// STRQroX
    113U,	// STRQui
    4U,	// STRSpost
    4161U,	// STRSpre
    157953U,	// STRSroW
    166145U,	// STRSroX
    121U,	// STRSui
    4U,	// STRWpost
    4161U,	// STRWpre
    157953U,	// STRWroW
    166145U,	// STRWroX
    121U,	// STRWui
    4U,	// STRXpost
    4161U,	// STRXpre
    108801U,	// STRXroW
    116993U,	// STRXroX
    97U,	// STRXui
    3585U,	// STTRBi
    3585U,	// STTRHi
    3585U,	// STTRWi
    3585U,	// STTRXi
    3585U,	// STURBBi
    3585U,	// STURBi
    3585U,	// STURDi
    3585U,	// STURHHi
    3585U,	// STURHi
    3585U,	// STURQi
    3585U,	// STURSi
    3585U,	// STURWi
    3585U,	// STURXi
    4609U,	// STXPW
    4609U,	// STXPX
    3588U,	// STXRB
    3588U,	// STXRH
    3588U,	// STXRW
    3588U,	// STXRX
    265U,	// SUBHNv2i64_v2i32
    273U,	// SUBHNv2i64_v4i32
    521U,	// SUBHNv4i32_v4i16
    529U,	// SUBHNv4i32_v8i16
    785U,	// SUBHNv8i16_v16i8
    777U,	// SUBHNv8i16_v8i8
    25U,	// SUBSWri
    0U,	// SUBSWrr
    33U,	// SUBSWrs
    41U,	// SUBSWrx
    25U,	// SUBSXri
    0U,	// SUBSXrr
    33U,	// SUBSXrs
    41U,	// SUBSXrx
    2049U,	// SUBSXrx64
    25U,	// SUBWri
    0U,	// SUBWrr
    33U,	// SUBWrs
    41U,	// SUBWrx
    25U,	// SUBXri
    0U,	// SUBXrr
    33U,	// SUBXrs
    41U,	// SUBXrx
    2049U,	// SUBXrx64
    1033U,	// SUBv16i8
    1U,	// SUBv1i64
    1289U,	// SUBv2i32
    265U,	// SUBv2i64
    1545U,	// SUBv4i16
    521U,	// SUBv4i32
    777U,	// SUBv8i16
    1801U,	// SUBv8i8
    0U,	// SUQADDv16i8
    0U,	// SUQADDv1i16
    0U,	// SUQADDv1i32
    0U,	// SUQADDv1i64
    0U,	// SUQADDv1i8
    0U,	// SUQADDv2i32
    0U,	// SUQADDv2i64
    0U,	// SUQADDv4i16
    0U,	// SUQADDv4i32
    0U,	// SUQADDv8i16
    0U,	// SUQADDv8i8
    0U,	// SVC
    129U,	// SYSLxt
    0U,	// SYSxt
    0U,	// TBLv16i8Four
    0U,	// TBLv16i8One
    0U,	// TBLv16i8Three
    0U,	// TBLv16i8Two
    0U,	// TBLv8i8Four
    0U,	// TBLv8i8One
    0U,	// TBLv8i8Three
    0U,	// TBLv8i8Two
    137U,	// TBNZW
    137U,	// TBNZX
    0U,	// TBXv16i8Four
    0U,	// TBXv16i8One
    0U,	// TBXv16i8Three
    0U,	// TBXv16i8Two
    0U,	// TBXv8i8Four
    0U,	// TBXv8i8One
    0U,	// TBXv8i8Three
    0U,	// TBXv8i8Two
    137U,	// TBZW
    137U,	// TBZX
    0U,	// TCRETURNdi
    0U,	// TCRETURNri
    0U,	// TLSDESCCALL
    0U,	// TLSDESC_BLR
    1033U,	// TRN1v16i8
    1289U,	// TRN1v2i32
    265U,	// TRN1v2i64
    1545U,	// TRN1v4i16
    521U,	// TRN1v4i32
    777U,	// TRN1v8i16
    1801U,	// TRN1v8i8
    1033U,	// TRN2v16i8
    1289U,	// TRN2v2i32
    265U,	// TRN2v2i64
    1545U,	// TRN2v4i16
    521U,	// TRN2v4i32
    777U,	// TRN2v8i16
    1801U,	// TRN2v8i8
    1041U,	// UABALv16i8_v8i16
    1297U,	// UABALv2i32_v2i64
    1553U,	// UABALv4i16_v4i32
    529U,	// UABALv4i32_v2i64
    785U,	// UABALv8i16_v4i32
    1809U,	// UABALv8i8_v8i16
    1041U,	// UABAv16i8
    1297U,	// UABAv2i32
    1553U,	// UABAv4i16
    529U,	// UABAv4i32
    785U,	// UABAv8i16
    1809U,	// UABAv8i8
    1033U,	// UABDLv16i8_v8i16
    1289U,	// UABDLv2i32_v2i64
    1545U,	// UABDLv4i16_v4i32
    521U,	// UABDLv4i32_v2i64
    777U,	// UABDLv8i16_v4i32
    1801U,	// UABDLv8i8_v8i16
    1033U,	// UABDv16i8
    1289U,	// UABDv2i32
    1545U,	// UABDv4i16
    521U,	// UABDv4i32
    777U,	// UABDv8i16
    1801U,	// UABDv8i8
    0U,	// UADALPv16i8_v8i16
    0U,	// UADALPv2i32_v1i64
    0U,	// UADALPv4i16_v2i32
    0U,	// UADALPv4i32_v2i64
    0U,	// UADALPv8i16_v4i32
    0U,	// UADALPv8i8_v4i16
    0U,	// UADDLPv16i8_v8i16
    0U,	// UADDLPv2i32_v1i64
    0U,	// UADDLPv4i16_v2i32
    0U,	// UADDLPv4i32_v2i64
    0U,	// UADDLPv8i16_v4i32
    0U,	// UADDLPv8i8_v4i16
    0U,	// UADDLVv16i8v
    0U,	// UADDLVv4i16v
    0U,	// UADDLVv4i32v
    0U,	// UADDLVv8i16v
    0U,	// UADDLVv8i8v
    1033U,	// UADDLv16i8_v8i16
    1289U,	// UADDLv2i32_v2i64
    1545U,	// UADDLv4i16_v4i32
    521U,	// UADDLv4i32_v2i64
    777U,	// UADDLv8i16_v4i32
    1801U,	// UADDLv8i8_v8i16
    1033U,	// UADDWv16i8_v8i16
    1289U,	// UADDWv2i32_v2i64
    1545U,	// UADDWv4i16_v4i32
    521U,	// UADDWv4i32_v2i64
    777U,	// UADDWv8i16_v4i32
    1801U,	// UADDWv8i8_v8i16
    18689U,	// UBFMWri
    18689U,	// UBFMXri
    1U,	// UCVTFSWDri
    1U,	// UCVTFSWSri
    1U,	// UCVTFSXDri
    1U,	// UCVTFSXSri
    0U,	// UCVTFUWDri
    0U,	// UCVTFUWSri
    0U,	// UCVTFUXDri
    0U,	// UCVTFUXSri
    1U,	// UCVTFd
    1U,	// UCVTFs
    0U,	// UCVTFv1i32
    0U,	// UCVTFv1i64
    0U,	// UCVTFv2f32
    0U,	// UCVTFv2f64
    1U,	// UCVTFv2i32_shift
    1U,	// UCVTFv2i64_shift
    0U,	// UCVTFv4f32
    1U,	// UCVTFv4i32_shift
    1U,	// UDIVWr
    1U,	// UDIVXr
    1U,	// UDIV_IntWr
    1U,	// UDIV_IntXr
    1033U,	// UHADDv16i8
    1289U,	// UHADDv2i32
    1545U,	// UHADDv4i16
    521U,	// UHADDv4i32
    777U,	// UHADDv8i16
    1801U,	// UHADDv8i8
    1033U,	// UHSUBv16i8
    1289U,	// UHSUBv2i32
    1545U,	// UHSUBv4i16
    521U,	// UHSUBv4i32
    777U,	// UHSUBv8i16
    1801U,	// UHSUBv8i8
    18689U,	// UMADDLrrr
    1033U,	// UMAXPv16i8
    1289U,	// UMAXPv2i32
    1545U,	// UMAXPv4i16
    521U,	// UMAXPv4i32
    777U,	// UMAXPv8i16
    1801U,	// UMAXPv8i8
    0U,	// UMAXVv16i8v
    0U,	// UMAXVv4i16v
    0U,	// UMAXVv4i32v
    0U,	// UMAXVv8i16v
    0U,	// UMAXVv8i8v
    1033U,	// UMAXv16i8
    1289U,	// UMAXv2i32
    1545U,	// UMAXv4i16
    521U,	// UMAXv4i32
    777U,	// UMAXv8i16
    1801U,	// UMAXv8i8
    1033U,	// UMINPv16i8
    1289U,	// UMINPv2i32
    1545U,	// UMINPv4i16
    521U,	// UMINPv4i32
    777U,	// UMINPv8i16
    1801U,	// UMINPv8i8
    0U,	// UMINVv16i8v
    0U,	// UMINVv4i16v
    0U,	// UMINVv4i32v
    0U,	// UMINVv8i16v
    0U,	// UMINVv8i8v
    1033U,	// UMINv16i8
    1289U,	// UMINv2i32
    1545U,	// UMINv4i16
    521U,	// UMINv4i32
    777U,	// UMINv8i16
    1801U,	// UMINv8i8
    1041U,	// UMLALv16i8_v8i16
    27665U,	// UMLALv2i32_indexed
    1297U,	// UMLALv2i32_v2i64
    28945U,	// UMLALv4i16_indexed
    1553U,	// UMLALv4i16_v4i32
    27665U,	// UMLALv4i32_indexed
    529U,	// UMLALv4i32_v2i64
    28945U,	// UMLALv8i16_indexed
    785U,	// UMLALv8i16_v4i32
    1809U,	// UMLALv8i8_v8i16
    1041U,	// UMLSLv16i8_v8i16
    27665U,	// UMLSLv2i32_indexed
    1297U,	// UMLSLv2i32_v2i64
    28945U,	// UMLSLv4i16_indexed
    1553U,	// UMLSLv4i16_v4i32
    27665U,	// UMLSLv4i32_indexed
    529U,	// UMLSLv4i32_v2i64
    28945U,	// UMLSLv8i16_indexed
    785U,	// UMLSLv8i16_v4i32
    1809U,	// UMLSLv8i8_v8i16
    75U,	// UMOVvi16
    75U,	// UMOVvi32
    75U,	// UMOVvi64
    75U,	// UMOVvi8
    18689U,	// UMSUBLrrr
    1U,	// UMULHrr
    1033U,	// UMULLv16i8_v8i16
    35849U,	// UMULLv2i32_indexed
    1289U,	// UMULLv2i32_v2i64
    37129U,	// UMULLv4i16_indexed
    1545U,	// UMULLv4i16_v4i32
    35849U,	// UMULLv4i32_indexed
    521U,	// UMULLv4i32_v2i64
    37129U,	// UMULLv8i16_indexed
    777U,	// UMULLv8i16_v4i32
    1801U,	// UMULLv8i8_v8i16
    1033U,	// UQADDv16i8
    1U,	// UQADDv1i16
    1U,	// UQADDv1i32
    1U,	// UQADDv1i64
    1U,	// UQADDv1i8
    1289U,	// UQADDv2i32
    265U,	// UQADDv2i64
    1545U,	// UQADDv4i16
    521U,	// UQADDv4i32
    777U,	// UQADDv8i16
    1801U,	// UQADDv8i8
    1033U,	// UQRSHLv16i8
    1U,	// UQRSHLv1i16
    1U,	// UQRSHLv1i32
    1U,	// UQRSHLv1i64
    1U,	// UQRSHLv1i8
    1289U,	// UQRSHLv2i32
    265U,	// UQRSHLv2i64
    1545U,	// UQRSHLv4i16
    521U,	// UQRSHLv4i32
    777U,	// UQRSHLv8i16
    1801U,	// UQRSHLv8i8
    1U,	// UQRSHRNb
    1U,	// UQRSHRNh
    1U,	// UQRSHRNs
    65U,	// UQRSHRNv16i8_shift
    1U,	// UQRSHRNv2i32_shift
    1U,	// UQRSHRNv4i16_shift
    65U,	// UQRSHRNv4i32_shift
    65U,	// UQRSHRNv8i16_shift
    1U,	// UQRSHRNv8i8_shift
    1U,	// UQSHLb
    1U,	// UQSHLd
    1U,	// UQSHLh
    1U,	// UQSHLs
    1033U,	// UQSHLv16i8
    1U,	// UQSHLv16i8_shift
    1U,	// UQSHLv1i16
    1U,	// UQSHLv1i32
    1U,	// UQSHLv1i64
    1U,	// UQSHLv1i8
    1289U,	// UQSHLv2i32
    1U,	// UQSHLv2i32_shift
    265U,	// UQSHLv2i64
    1U,	// UQSHLv2i64_shift
    1545U,	// UQSHLv4i16
    1U,	// UQSHLv4i16_shift
    521U,	// UQSHLv4i32
    1U,	// UQSHLv4i32_shift
    777U,	// UQSHLv8i16
    1U,	// UQSHLv8i16_shift
    1801U,	// UQSHLv8i8
    1U,	// UQSHLv8i8_shift
    1U,	// UQSHRNb
    1U,	// UQSHRNh
    1U,	// UQSHRNs
    65U,	// UQSHRNv16i8_shift
    1U,	// UQSHRNv2i32_shift
    1U,	// UQSHRNv4i16_shift
    65U,	// UQSHRNv4i32_shift
    65U,	// UQSHRNv8i16_shift
    1U,	// UQSHRNv8i8_shift
    1033U,	// UQSUBv16i8
    1U,	// UQSUBv1i16
    1U,	// UQSUBv1i32
    1U,	// UQSUBv1i64
    1U,	// UQSUBv1i8
    1289U,	// UQSUBv2i32
    265U,	// UQSUBv2i64
    1545U,	// UQSUBv4i16
    521U,	// UQSUBv4i32
    777U,	// UQSUBv8i16
    1801U,	// UQSUBv8i8
    0U,	// UQXTNv16i8
    0U,	// UQXTNv1i16
    0U,	// UQXTNv1i32
    0U,	// UQXTNv1i8
    0U,	// UQXTNv2i32
    0U,	// UQXTNv4i16
    0U,	// UQXTNv4i32
    0U,	// UQXTNv8i16
    0U,	// UQXTNv8i8
    0U,	// URECPEv2i32
    0U,	// URECPEv4i32
    1033U,	// URHADDv16i8
    1289U,	// URHADDv2i32
    1545U,	// URHADDv4i16
    521U,	// URHADDv4i32
    777U,	// URHADDv8i16
    1801U,	// URHADDv8i8
    1033U,	// URSHLv16i8
    1U,	// URSHLv1i64
    1289U,	// URSHLv2i32
    265U,	// URSHLv2i64
    1545U,	// URSHLv4i16
    521U,	// URSHLv4i32
    777U,	// URSHLv8i16
    1801U,	// URSHLv8i8
    1U,	// URSHRd
    1U,	// URSHRv16i8_shift
    1U,	// URSHRv2i32_shift
    1U,	// URSHRv2i64_shift
    1U,	// URSHRv4i16_shift
    1U,	// URSHRv4i32_shift
    1U,	// URSHRv8i16_shift
    1U,	// URSHRv8i8_shift
    0U,	// URSQRTEv2i32
    0U,	// URSQRTEv4i32
    65U,	// URSRAd
    65U,	// URSRAv16i8_shift
    65U,	// URSRAv2i32_shift
    65U,	// URSRAv2i64_shift
    65U,	// URSRAv4i16_shift
    65U,	// URSRAv4i32_shift
    65U,	// URSRAv8i16_shift
    65U,	// URSRAv8i8_shift
    1U,	// USHLLv16i8_shift
    1U,	// USHLLv2i32_shift
    1U,	// USHLLv4i16_shift
    1U,	// USHLLv4i32_shift
    1U,	// USHLLv8i16_shift
    1U,	// USHLLv8i8_shift
    1033U,	// USHLv16i8
    1U,	// USHLv1i64
    1289U,	// USHLv2i32
    265U,	// USHLv2i64
    1545U,	// USHLv4i16
    521U,	// USHLv4i32
    777U,	// USHLv8i16
    1801U,	// USHLv8i8
    1U,	// USHRd
    1U,	// USHRv16i8_shift
    1U,	// USHRv2i32_shift
    1U,	// USHRv2i64_shift
    1U,	// USHRv4i16_shift
    1U,	// USHRv4i32_shift
    1U,	// USHRv8i16_shift
    1U,	// USHRv8i8_shift
    0U,	// USQADDv16i8
    0U,	// USQADDv1i16
    0U,	// USQADDv1i32
    0U,	// USQADDv1i64
    0U,	// USQADDv1i8
    0U,	// USQADDv2i32
    0U,	// USQADDv2i64
    0U,	// USQADDv4i16
    0U,	// USQADDv4i32
    0U,	// USQADDv8i16
    0U,	// USQADDv8i8
    65U,	// USRAd
    65U,	// USRAv16i8_shift
    65U,	// USRAv2i32_shift
    65U,	// USRAv2i64_shift
    65U,	// USRAv4i16_shift
    65U,	// USRAv4i32_shift
    65U,	// USRAv8i16_shift
    65U,	// USRAv8i8_shift
    1033U,	// USUBLv16i8_v8i16
    1289U,	// USUBLv2i32_v2i64
    1545U,	// USUBLv4i16_v4i32
    521U,	// USUBLv4i32_v2i64
    777U,	// USUBLv8i16_v4i32
    1801U,	// USUBLv8i8_v8i16
    1033U,	// USUBWv16i8_v8i16
    1289U,	// USUBWv2i32_v2i64
    1545U,	// USUBWv4i16_v4i32
    521U,	// USUBWv4i32_v2i64
    777U,	// USUBWv8i16_v4i32
    1801U,	// USUBWv8i8_v8i16
    1033U,	// UZP1v16i8
    1289U,	// UZP1v2i32
    265U,	// UZP1v2i64
    1545U,	// UZP1v4i16
    521U,	// UZP1v4i32
    777U,	// UZP1v8i16
    1801U,	// UZP1v8i8
    1033U,	// UZP2v16i8
    1289U,	// UZP2v2i32
    265U,	// UZP2v2i64
    1545U,	// UZP2v4i16
    521U,	// UZP2v4i32
    777U,	// UZP2v8i16
    1801U,	// UZP2v8i8
    0U,	// XTNv16i8
    0U,	// XTNv2i32
    0U,	// XTNv4i16
    0U,	// XTNv4i32
    0U,	// XTNv8i16
    0U,	// XTNv8i8
    1033U,	// ZIP1v16i8
    1289U,	// ZIP1v2i32
    265U,	// ZIP1v2i64
    1545U,	// ZIP1v4i16
    521U,	// ZIP1v4i32
    777U,	// ZIP1v8i16
    1801U,	// ZIP1v8i8
    1033U,	// ZIP2v16i8
    1289U,	// ZIP2v2i32
    265U,	// ZIP2v2i64
    1545U,	// ZIP2v4i16
    521U,	// ZIP2v4i32
    777U,	// ZIP2v8i16
    1801U,	// ZIP2v8i8
    0U
  };

#ifndef CAPSTONE_DIET
  static const char AsmStrs[] = {
  /* 0 */ 's', 'h', 'a', '1', 's', 'u', '0', 9, 0,
  /* 9 */ 's', 'h', 'a', '2', '5', '6', 's', 'u', '0', 9, 0,
  /* 20 */ 'l', 'd', '1', 9, 0,
  /* 25 */ 't', 'r', 'n', '1', 9, 0,
  /* 31 */ 'z', 'i', 'p', '1', 9, 0,
  /* 37 */ 'u', 'z', 'p', '1', 9, 0,
  /* 43 */ 'd', 'c', 'p', 's', '1', 9, 0,
  /* 50 */ 's', 't', '1', 9, 0,
  /* 55 */ 's', 'h', 'a', '1', 's', 'u', '1', 9, 0,
  /* 64 */ 's', 'h', 'a', '2', '5', '6', 's', 'u', '1', 9, 0,
  /* 75 */ 'r', 'e', 'v', '3', '2', 9, 0,
  /* 82 */ 'l', 'd', '2', 9, 0,
  /* 87 */ 's', 'h', 'a', '2', '5', '6', 'h', '2', 9, 0,
  /* 97 */ 's', 'a', 'b', 'a', 'l', '2', 9, 0,
  /* 105 */ 'u', 'a', 'b', 'a', 'l', '2', 9, 0,
  /* 113 */ 's', 'q', 'd', 'm', 'l', 'a', 'l', '2', 9, 0,
  /* 123 */ 's', 'm', 'l', 'a', 'l', '2', 9, 0,
  /* 131 */ 'u', 'm', 'l', 'a', 'l', '2', 9, 0,
  /* 139 */ 's', 's', 'u', 'b', 'l', '2', 9, 0,
  /* 147 */ 'u', 's', 'u', 'b', 'l', '2', 9, 0,
  /* 155 */ 's', 'a', 'b', 'd', 'l', '2', 9, 0,
  /* 163 */ 'u', 'a', 'b', 'd', 'l', '2', 9, 0,
  /* 171 */ 's', 'a', 'd', 'd', 'l', '2', 9, 0,
  /* 179 */ 'u', 'a', 'd', 'd', 'l', '2', 9, 0,
  /* 187 */ 's', 's', 'h', 'l', 'l', '2', 9, 0,
  /* 195 */ 'u', 's', 'h', 'l', 'l', '2', 9, 0,
  /* 203 */ 's', 'q', 'd', 'm', 'u', 'l', 'l', '2', 9, 0,
  /* 213 */ 'p', 'm', 'u', 'l', 'l', '2', 9, 0,
  /* 221 */ 's', 'm', 'u', 'l', 'l', '2', 9, 0,
  /* 229 */ 'u', 'm', 'u', 'l', 'l', '2', 9, 0,
  /* 237 */ 's', 'q', 'd', 'm', 'l', 's', 'l', '2', 9, 0,
  /* 247 */ 's', 'm', 'l', 's', 'l', '2', 9, 0,
  /* 255 */ 'u', 'm', 'l', 's', 'l', '2', 9, 0,
  /* 263 */ 'f', 'c', 'v', 't', 'l', '2', 9, 0,
  /* 271 */ 'r', 's', 'u', 'b', 'h', 'n', '2', 9, 0,
  /* 280 */ 'r', 'a', 'd', 'd', 'h', 'n', '2', 9, 0,
  /* 289 */ 's', 'q', 's', 'h', 'r', 'n', '2', 9, 0,
  /* 298 */ 'u', 'q', 's', 'h', 'r', 'n', '2', 9, 0,
  /* 307 */ 's', 'q', 'r', 's', 'h', 'r', 'n', '2', 9, 0,
  /* 317 */ 'u', 'q', 'r', 's', 'h', 'r', 'n', '2', 9, 0,
  /* 327 */ 't', 'r', 'n', '2', 9, 0,
  /* 333 */ 'f', 'c', 'v', 't', 'n', '2', 9, 0,
  /* 341 */ 's', 'q', 'x', 't', 'n', '2', 9, 0,
  /* 349 */ 'u', 'q', 'x', 't', 'n', '2', 9, 0,
  /* 357 */ 's', 'q', 's', 'h', 'r', 'u', 'n', '2', 9, 0,
  /* 367 */ 's', 'q', 'r', 's', 'h', 'r', 'u', 'n', '2', 9, 0,
  /* 378 */ 's', 'q', 'x', 't', 'u', 'n', '2', 9, 0,
  /* 387 */ 'f', 'c', 'v', 't', 'x', 'n', '2', 9, 0,
  /* 396 */ 'z', 'i', 'p', '2', 9, 0,
  /* 402 */ 'u', 'z', 'p', '2', 9, 0,
  /* 408 */ 'd', 'c', 'p', 's', '2', 9, 0,
  /* 415 */ 's', 't', '2', 9, 0,
  /* 420 */ 's', 's', 'u', 'b', 'w', '2', 9, 0,
  /* 428 */ 'u', 's', 'u', 'b', 'w', '2', 9, 0,
  /* 436 */ 's', 'a', 'd', 'd', 'w', '2', 9, 0,
  /* 444 */ 'u', 'a', 'd', 'd', 'w', '2', 9, 0,
  /* 452 */ 'l', 'd', '3', 9, 0,
  /* 457 */ 'd', 'c', 'p', 's', '3', 9, 0,
  /* 464 */ 's', 't', '3', 9, 0,
  /* 469 */ 'r', 'e', 'v', '6', '4', 9, 0,
  /* 476 */ 'l', 'd', '4', 9, 0,
  /* 481 */ 's', 't', '4', 9, 0,
  /* 486 */ 'r', 'e', 'v', '1', '6', 9, 0,
  /* 493 */ 's', 'a', 'b', 'a', 9, 0,
  /* 499 */ 'u', 'a', 'b', 'a', 9, 0,
  /* 505 */ 'f', 'm', 'l', 'a', 9, 0,
  /* 511 */ 's', 'r', 's', 'r', 'a', 9, 0,
  /* 518 */ 'u', 'r', 's', 'r', 'a', 9, 0,
  /* 525 */ 's', 's', 'r', 'a', 9, 0,
  /* 531 */ 'u', 's', 'r', 'a', 9, 0,
  /* 537 */ 'f', 'r', 'i', 'n', 't', 'a', 9, 0,
  /* 545 */ 'c', 'r', 'c', '3', '2', 'b', 9, 0,
  /* 553 */ 'c', 'r', 'c', '3', '2', 'c', 'b', 9, 0,
  /* 562 */ 'd', 'm', 'b', 9, 0,
  /* 567 */ 'l', 'd', 'a', 'r', 'b', 9, 0,
  /* 574 */ 'l', 'd', 'r', 'b', 9, 0,
  /* 580 */ 's', 't', 'l', 'r', 'b', 9, 0,
  /* 587 */ 'l', 'd', 't', 'r', 'b', 9, 0,
  /* 594 */ 's', 't', 'r', 'b', 9, 0,
  /* 600 */ 's', 't', 't', 'r', 'b', 9, 0,
  /* 607 */ 'l', 'd', 'u', 'r', 'b', 9, 0,
  /* 614 */ 's', 't', 'u', 'r', 'b', 9, 0,
  /* 621 */ 'l', 'd', 'a', 'x', 'r', 'b', 9, 0,
  /* 629 */ 'l', 'd', 'x', 'r', 'b', 9, 0,
  /* 636 */ 's', 't', 'l', 'x', 'r', 'b', 9, 0,
  /* 644 */ 's', 't', 'x', 'r', 'b', 9, 0,
  /* 651 */ 'd', 's', 'b', 9, 0,
  /* 656 */ 'i', 's', 'b', 9, 0,
  /* 661 */ 'l', 'd', 'r', 's', 'b', 9, 0,
  /* 668 */ 'l', 'd', 't', 'r', 's', 'b', 9, 0,
  /* 676 */ 'l', 'd', 'u', 'r', 's', 'b', 9, 0,
  /* 684 */ 'f', 's', 'u', 'b', 9, 0,
  /* 690 */ 's', 'h', 's', 'u', 'b', 9, 0,
  /* 697 */ 'u', 'h', 's', 'u', 'b', 9, 0,
  /* 704 */ 'f', 'm', 's', 'u', 'b', 9, 0,
  /* 711 */ 'f', 'n', 'm', 's', 'u', 'b', 9, 0,
  /* 719 */ 's', 'q', 's', 'u', 'b', 9, 0,
  /* 726 */ 'u', 'q', 's', 'u', 'b', 9, 0,
  /* 733 */ 's', 'h', 'a', '1', 'c', 9, 0,
  /* 740 */ 's', 'b', 'c', 9, 0,
  /* 745 */ 'a', 'd', 'c', 9, 0,
  /* 750 */ 'b', 'i', 'c', 9, 0,
  /* 755 */ 'a', 'e', 's', 'i', 'm', 'c', 9, 0,
  /* 763 */ 'a', 'e', 's', 'm', 'c', 9, 0,
  /* 770 */ 'c', 's', 'i', 'n', 'c', 9, 0,
  /* 777 */ 'h', 'v', 'c', 9, 0,
  /* 782 */ 's', 'v', 'c', 9, 0,
  /* 787 */ 'f', 'a', 'b', 'd', 9, 0,
  /* 793 */ 's', 'a', 'b', 'd', 9, 0,
  /* 799 */ 'u', 'a', 'b', 'd', 9, 0,
  /* 805 */ 'f', 'a', 'd', 'd', 9, 0,
  /* 811 */ 's', 'r', 'h', 'a', 'd', 'd', 9, 0,
  /* 819 */ 'u', 'r', 'h', 'a', 'd', 'd', 9, 0,
  /* 827 */ 's', 'h', 'a', 'd', 'd', 9, 0,
  /* 834 */ 'u', 'h', 'a', 'd', 'd', 9, 0,
  /* 841 */ 'f', 'm', 'a', 'd', 'd', 9, 0,
  /* 848 */ 'f', 'n', 'm', 'a', 'd', 'd', 9, 0,
  /* 856 */ 'u', 's', 'q', 'a', 'd', 'd', 9, 0,
  /* 864 */ 's', 'u', 'q', 'a', 'd', 'd', 9, 0,
  /* 872 */ 'a', 'n', 'd', 9, 0,
  /* 877 */ 'a', 'e', 's', 'd', 9, 0,
  /* 883 */ 'f', 'a', 'c', 'g', 'e', 9, 0,
  /* 890 */ 'f', 'c', 'm', 'g', 'e', 9, 0,
  /* 897 */ 'f', 'c', 'm', 'l', 'e', 9, 0,
  /* 904 */ 'f', 'r', 'e', 'c', 'p', 'e', 9, 0,
  /* 912 */ 'u', 'r', 'e', 'c', 'p', 'e', 9, 0,
  /* 920 */ 'f', 'c', 'c', 'm', 'p', 'e', 9, 0,
  /* 928 */ 'f', 'c', 'm', 'p', 'e', 9, 0,
  /* 935 */ 'a', 'e', 's', 'e', 9, 0,
  /* 941 */ 'f', 'r', 's', 'q', 'r', 't', 'e', 9, 0,
  /* 950 */ 'u', 'r', 's', 'q', 'r', 't', 'e', 9, 0,
  /* 959 */ 'b', 'i', 'f', 9, 0,
  /* 964 */ 's', 'c', 'v', 't', 'f', 9, 0,
  /* 971 */ 'u', 'c', 'v', 't', 'f', 9, 0,
  /* 978 */ 'f', 'n', 'e', 'g', 9, 0,
  /* 984 */ 's', 'q', 'n', 'e', 'g', 9, 0,
  /* 991 */ 'c', 's', 'n', 'e', 'g', 9, 0,
  /* 998 */ 's', 'h', 'a', '1', 'h', 9, 0,
  /* 1005 */ 'c', 'r', 'c', '3', '2', 'h', 9, 0,
  /* 1013 */ 's', 'h', 'a', '2', '5', '6', 'h', 9, 0,
  /* 1022 */ 'c', 'r', 'c', '3', '2', 'c', 'h', 9, 0,
  /* 1031 */ 's', 'q', 'd', 'm', 'u', 'l', 'h', 9, 0,
  /* 1040 */ 's', 'q', 'r', 'd', 'm', 'u', 'l', 'h', 9, 0,
  /* 1050 */ 's', 'm', 'u', 'l', 'h', 9, 0,
  /* 1057 */ 'u', 'm', 'u', 'l', 'h', 9, 0,
  /* 1064 */ 'l', 'd', 'a', 'r', 'h', 9, 0,
  /* 1071 */ 'l', 'd', 'r', 'h', 9, 0,
  /* 1077 */ 's', 't', 'l', 'r', 'h', 9, 0,
  /* 1084 */ 'l', 'd', 't', 'r', 'h', 9, 0,
  /* 1091 */ 's', 't', 'r', 'h', 9, 0,
  /* 1097 */ 's', 't', 't', 'r', 'h', 9, 0,
  /* 1104 */ 'l', 'd', 'u', 'r', 'h', 9, 0,
  /* 1111 */ 's', 't', 'u', 'r', 'h', 9, 0,
  /* 1118 */ 'l', 'd', 'a', 'x', 'r', 'h', 9, 0,
  /* 1126 */ 'l', 'd', 'x', 'r', 'h', 9, 0,
  /* 1133 */ 's', 't', 'l', 'x', 'r', 'h', 9, 0,
  /* 1141 */ 's', 't', 'x', 'r', 'h', 9, 0,
  /* 1148 */ 'l', 'd', 'r', 's', 'h', 9, 0,
  /* 1155 */ 'l', 'd', 't', 'r', 's', 'h', 9, 0,
  /* 1163 */ 'l', 'd', 'u', 'r', 's', 'h', 9, 0,
  /* 1171 */ 'c', 'm', 'h', 'i', 9, 0,
  /* 1177 */ 's', 'l', 'i', 9, 0,
  /* 1182 */ 'm', 'v', 'n', 'i', 9, 0,
  /* 1188 */ 's', 'r', 'i', 9, 0,
  /* 1193 */ 'f', 'r', 'i', 'n', 't', 'i', 9, 0,
  /* 1201 */ 'm', 'o', 'v', 'i', 9, 0,
  /* 1207 */ 'b', 'r', 'k', 9, 0,
  /* 1212 */ 'm', 'o', 'v', 'k', 9, 0,
  /* 1218 */ 's', 'a', 'b', 'a', 'l', 9, 0,
  /* 1225 */ 'u', 'a', 'b', 'a', 'l', 9, 0,
  /* 1232 */ 's', 'q', 'd', 'm', 'l', 'a', 'l', 9, 0,
  /* 1241 */ 's', 'm', 'l', 'a', 'l', 9, 0,
  /* 1248 */ 'u', 'm', 'l', 'a', 'l', 9, 0,
  /* 1255 */ 't', 'b', 'l', 9, 0,
  /* 1260 */ 's', 'm', 's', 'u', 'b', 'l', 9, 0,
  /* 1268 */ 'u', 'm', 's', 'u', 'b', 'l', 9, 0,
  /* 1276 */ 's', 's', 'u', 'b', 'l', 9, 0,
  /* 1283 */ 'u', 's', 'u', 'b', 'l', 9, 0,
  /* 1290 */ 's', 'a', 'b', 'd', 'l', 9, 0,
  /* 1297 */ 'u', 'a', 'b', 'd', 'l', 9, 0,
  /* 1304 */ 's', 'm', 'a', 'd', 'd', 'l', 9, 0,
  /* 1312 */ 'u', 'm', 'a', 'd', 'd', 'l', 9, 0,
  /* 1320 */ 's', 'a', 'd', 'd', 'l', 9, 0,
  /* 1327 */ 'u', 'a', 'd', 'd', 'l', 9, 0,
  /* 1334 */ 'f', 'c', 's', 'e', 'l', 9, 0,
  /* 1341 */ 's', 'q', 's', 'h', 'l', 9, 0,
  /* 1348 */ 'u', 'q', 's', 'h', 'l', 9, 0,
  /* 1355 */ 's', 'q', 'r', 's', 'h', 'l', 9, 0,
  /* 1363 */ 'u', 'q', 'r', 's', 'h', 'l', 9, 0,
  /* 1371 */ 's', 'r', 's', 'h', 'l', 9, 0,
  /* 1378 */ 'u', 'r', 's', 'h', 'l', 9, 0,
  /* 1385 */ 's', 's', 'h', 'l', 9, 0,
  /* 1391 */ 'u', 's', 'h', 'l', 9, 0,
  /* 1397 */ 's', 's', 'h', 'l', 'l', 9, 0,
  /* 1404 */ 'u', 's', 'h', 'l', 'l', 9, 0,
  /* 1411 */ 's', 'q', 'd', 'm', 'u', 'l', 'l', 9, 0,
  /* 1420 */ 'p', 'm', 'u', 'l', 'l', 9, 0,
  /* 1427 */ 's', 'm', 'u', 'l', 'l', 9, 0,
  /* 1434 */ 'u', 'm', 'u', 'l', 'l', 9, 0,
  /* 1441 */ 'b', 's', 'l', 9, 0,
  /* 1446 */ 's', 'q', 'd', 'm', 'l', 's', 'l', 9, 0,
  /* 1455 */ 's', 'm', 'l', 's', 'l', 9, 0,
  /* 1462 */ 'u', 'm', 'l', 's', 'l', 9, 0,
  /* 1469 */ 's', 'y', 's', 'l', 9, 0,
  /* 1475 */ 'f', 'c', 'v', 't', 'l', 9, 0,
  /* 1482 */ 'f', 'm', 'u', 'l', 9, 0,
  /* 1488 */ 'f', 'n', 'm', 'u', 'l', 9, 0,
  /* 1495 */ 'p', 'm', 'u', 'l', 9, 0,
  /* 1501 */ 's', 'h', 'a', '1', 'm', 9, 0,
  /* 1508 */ 's', 'b', 'f', 'm', 9, 0,
  /* 1514 */ 'u', 'b', 'f', 'm', 9, 0,
  /* 1520 */ 'p', 'r', 'f', 'm', 9, 0,
  /* 1526 */ 'f', 'm', 'i', 'n', 'n', 'm', 9, 0,
  /* 1534 */ 'f', 'm', 'a', 'x', 'n', 'm', 9, 0,
  /* 1542 */ 'f', 'r', 'i', 'n', 't', 'm', 9, 0,
  /* 1550 */ 'p', 'r', 'f', 'u', 'm', 9, 0,
  /* 1557 */ 'r', 's', 'u', 'b', 'h', 'n', 9, 0,
  /* 1565 */ 'r', 'a', 'd', 'd', 'h', 'n', 9, 0,
  /* 1573 */ 'f', 'm', 'i', 'n', 9, 0,
  /* 1579 */ 's', 'm', 'i', 'n', 9, 0,
  /* 1585 */ 'u', 'm', 'i', 'n', 9, 0,
  /* 1591 */ 'c', 'c', 'm', 'n', 9, 0,
  /* 1597 */ 'e', 'o', 'n', 9, 0,
  /* 1602 */ 's', 'q', 's', 'h', 'r', 'n', 9, 0,
  /* 1610 */ 'u', 'q', 's', 'h', 'r', 'n', 9, 0,
  /* 1618 */ 's', 'q', 'r', 's', 'h', 'r', 'n', 9, 0,
  /* 1627 */ 'u', 'q', 'r', 's', 'h', 'r', 'n', 9, 0,
  /* 1636 */ 'o', 'r', 'n', 9, 0,
  /* 1641 */ 'f', 'r', 'i', 'n', 't', 'n', 9, 0,
  /* 1649 */ 'f', 'c', 'v', 't', 'n', 9, 0,
  /* 1656 */ 's', 'q', 'x', 't', 'n', 9, 0,
  /* 1663 */ 'u', 'q', 'x', 't', 'n', 9, 0,
  /* 1670 */ 's', 'q', 's', 'h', 'r', 'u', 'n', 9, 0,
  /* 1679 */ 's', 'q', 'r', 's', 'h', 'r', 'u', 'n', 9, 0,
  /* 1689 */ 's', 'q', 'x', 't', 'u', 'n', 9, 0,
  /* 1697 */ 'm', 'o', 'v', 'n', 9, 0,
  /* 1703 */ 'f', 'c', 'v', 't', 'x', 'n', 9, 0,
  /* 1711 */ 's', 'h', 'a', '1', 'p', 9, 0,
  /* 1718 */ 'f', 'a', 'd', 'd', 'p', 9, 0,
  /* 1725 */ 'l', 'd', 'p', 9, 0,
  /* 1730 */ 's', 'a', 'd', 'a', 'l', 'p', 9, 0,
  /* 1738 */ 'u', 'a', 'd', 'a', 'l', 'p', 9, 0,
  /* 1746 */ 's', 'a', 'd', 'd', 'l', 'p', 9, 0,
  /* 1754 */ 'u', 'a', 'd', 'd', 'l', 'p', 9, 0,
  /* 1762 */ 'f', 'c', 'c', 'm', 'p', 9, 0,
  /* 1769 */ 'f', 'c', 'm', 'p', 9, 0,
  /* 1775 */ 'f', 'm', 'i', 'n', 'n', 'm', 'p', 9, 0,
  /* 1784 */ 'f', 'm', 'a', 'x', 'n', 'm', 'p', 9, 0,
  /* 1793 */ 'l', 'd', 'n', 'p', 9, 0,
  /* 1799 */ 'f', 'm', 'i', 'n', 'p', 9, 0,
  /* 1806 */ 's', 'm', 'i', 'n', 'p', 9, 0,
  /* 1813 */ 'u', 'm', 'i', 'n', 'p', 9, 0,
  /* 1820 */ 's', 't', 'n', 'p', 9, 0,
  /* 1826 */ 'a', 'd', 'r', 'p', 9, 0,
  /* 1832 */ 'f', 'r', 'i', 'n', 't', 'p', 9, 0,
  /* 1840 */ 's', 't', 'p', 9, 0,
  /* 1845 */ 'd', 'u', 'p', 9, 0,
  /* 1850 */ 'l', 'd', 'a', 'x', 'p', 9, 0,
  /* 1857 */ 'f', 'm', 'a', 'x', 'p', 9, 0,
  /* 1864 */ 's', 'm', 'a', 'x', 'p', 9, 0,
  /* 1871 */ 'u', 'm', 'a', 'x', 'p', 9, 0,
  /* 1878 */ 'l', 'd', 'x', 'p', 9, 0,
  /* 1884 */ 's', 't', 'l', 'x', 'p', 9, 0,
  /* 1891 */ 's', 't', 'x', 'p', 9, 0,
  /* 1897 */ 'f', 'c', 'm', 'e', 'q', 9, 0,
  /* 1904 */ 'l', 'd', '1', 'r', 9, 0,
  /* 1910 */ 'l', 'd', '2', 'r', 9, 0,
  /* 1916 */ 'l', 'd', '3', 'r', 9, 0,
  /* 1922 */ 'l', 'd', '4', 'r', 9, 0,
  /* 1928 */ 'l', 'd', 'a', 'r', 9, 0,
  /* 1934 */ 'b', 'r', 9, 0,
  /* 1938 */ 'a', 'd', 'r', 9, 0,
  /* 1943 */ 'l', 'd', 'r', 9, 0,
  /* 1948 */ 's', 'r', 's', 'h', 'r', 9, 0,
  /* 1955 */ 'u', 'r', 's', 'h', 'r', 9, 0,
  /* 1962 */ 's', 's', 'h', 'r', 9, 0,
  /* 1968 */ 'u', 's', 'h', 'r', 9, 0,
  /* 1974 */ 'b', 'l', 'r', 9, 0,
  /* 1979 */ 's', 't', 'l', 'r', 9, 0,
  /* 1985 */ 'e', 'o', 'r', 9, 0,
  /* 1990 */ 'r', 'o', 'r', 9, 0,
  /* 1995 */ 'o', 'r', 'r', 9, 0,
  /* 2000 */ 'a', 's', 'r', 9, 0,
  /* 2005 */ 'l', 's', 'r', 9, 0,
  /* 2010 */ 'm', 's', 'r', 9, 0,
  /* 2015 */ 'l', 'd', 't', 'r', 9, 0,
  /* 2021 */ 's', 't', 'r', 9, 0,
  /* 2026 */ 's', 't', 't', 'r', 9, 0,
  /* 2032 */ 'e', 'x', 't', 'r', 9, 0,
  /* 2038 */ 'l', 'd', 'u', 'r', 9, 0,
  /* 2044 */ 's', 't', 'u', 'r', 9, 0,
  /* 2050 */ 'l', 'd', 'a', 'x', 'r', 9, 0,
  /* 2057 */ 'l', 'd', 'x', 'r', 9, 0,
  /* 2063 */ 's', 't', 'l', 'x', 'r', 9, 0,
  /* 2070 */ 's', 't', 'x', 'r', 9, 0,
  /* 2076 */ 'f', 'c', 'v', 't', 'a', 's', 9, 0,
  /* 2084 */ 'f', 'a', 'b', 's', 9, 0,
  /* 2090 */ 's', 'q', 'a', 'b', 's', 9, 0,
  /* 2097 */ 's', 'u', 'b', 's', 9, 0,
  /* 2103 */ 's', 'b', 'c', 's', 9, 0,
  /* 2109 */ 'a', 'd', 'c', 's', 9, 0,
  /* 2115 */ 'b', 'i', 'c', 's', 9, 0,
  /* 2121 */ 'a', 'd', 'd', 's', 9, 0,
  /* 2127 */ 'a', 'n', 'd', 's', 9, 0,
  /* 2133 */ 'c', 'm', 'h', 's', 9, 0,
  /* 2139 */ 'c', 'l', 's', 9, 0,
  /* 2144 */ 'f', 'm', 'l', 's', 9, 0,
  /* 2150 */ 'f', 'c', 'v', 't', 'm', 's', 9, 0,
  /* 2158 */ 'i', 'n', 's', 9, 0,
  /* 2163 */ 'f', 'c', 'v', 't', 'n', 's', 9, 0,
  /* 2171 */ 'f', 'r', 'e', 'c', 'p', 's', 9, 0,
  /* 2179 */ 'f', 'c', 'v', 't', 'p', 's', 9, 0,
  /* 2187 */ 'm', 'r', 's', 9, 0,
  /* 2192 */ 'f', 'r', 's', 'q', 'r', 't', 's', 9, 0,
  /* 2201 */ 's', 'y', 's', 9, 0,
  /* 2206 */ 'f', 'c', 'v', 't', 'z', 's', 9, 0,
  /* 2214 */ 'r', 'e', 't', 9, 0,
  /* 2219 */ 'f', 'a', 'c', 'g', 't', 9, 0,
  /* 2226 */ 'f', 'c', 'm', 'g', 't', 9, 0,
  /* 2233 */ 'r', 'b', 'i', 't', 9, 0,
  /* 2239 */ 'h', 'l', 't', 9, 0,
  /* 2244 */ 'f', 'c', 'm', 'l', 't', 9, 0,
  /* 2251 */ 'c', 'n', 't', 9, 0,
  /* 2256 */ 'n', 'o', 't', 9, 0,
  /* 2261 */ 'f', 's', 'q', 'r', 't', 9, 0,
  /* 2268 */ 'c', 'm', 't', 's', 't', 9, 0,
  /* 2275 */ 'f', 'c', 'v', 't', 9, 0,
  /* 2281 */ 'e', 'x', 't', 9, 0,
  /* 2286 */ 'f', 'c', 'v', 't', 'a', 'u', 9, 0,
  /* 2294 */ 's', 'q', 's', 'h', 'l', 'u', 9, 0,
  /* 2302 */ 'f', 'c', 'v', 't', 'm', 'u', 9, 0,
  /* 2310 */ 'f', 'c', 'v', 't', 'n', 'u', 9, 0,
  /* 2318 */ 'f', 'c', 'v', 't', 'p', 'u', 9, 0,
  /* 2326 */ 'f', 'c', 'v', 't', 'z', 'u', 9, 0,
  /* 2334 */ 'a', 'd', 'd', 'v', 9, 0,
  /* 2340 */ 'r', 'e', 'v', 9, 0,
  /* 2345 */ 'f', 'd', 'i', 'v', 9, 0,
  /* 2351 */ 's', 'd', 'i', 'v', 9, 0,
  /* 2357 */ 'u', 'd', 'i', 'v', 9, 0,
  /* 2363 */ 's', 'a', 'd', 'd', 'l', 'v', 9, 0,
  /* 2371 */ 'u', 'a', 'd', 'd', 'l', 'v', 9, 0,
  /* 2379 */ 'f', 'm', 'i', 'n', 'n', 'm', 'v', 9, 0,
  /* 2388 */ 'f', 'm', 'a', 'x', 'n', 'm', 'v', 9, 0,
  /* 2397 */ 'f', 'm', 'i', 'n', 'v', 9, 0,
  /* 2404 */ 's', 'm', 'i', 'n', 'v', 9, 0,
  /* 2411 */ 'u', 'm', 'i', 'n', 'v', 9, 0,
  /* 2418 */ 'c', 's', 'i', 'n', 'v', 9, 0,
  /* 2425 */ 'f', 'm', 'o', 'v', 9, 0,
  /* 2431 */ 's', 'm', 'o', 'v', 9, 0,
  /* 2437 */ 'u', 'm', 'o', 'v', 9, 0,
  /* 2443 */ 'f', 'm', 'a', 'x', 'v', 9, 0,
  /* 2450 */ 's', 'm', 'a', 'x', 'v', 9, 0,
  /* 2457 */ 'u', 'm', 'a', 'x', 'v', 9, 0,
  /* 2464 */ 'c', 'r', 'c', '3', '2', 'w', 9, 0,
  /* 2472 */ 's', 's', 'u', 'b', 'w', 9, 0,
  /* 2479 */ 'u', 's', 'u', 'b', 'w', 9, 0,
  /* 2486 */ 'c', 'r', 'c', '3', '2', 'c', 'w', 9, 0,
  /* 2495 */ 's', 'a', 'd', 'd', 'w', 9, 0,
  /* 2502 */ 'u', 'a', 'd', 'd', 'w', 9, 0,
  /* 2509 */ 'l', 'd', 'p', 's', 'w', 9, 0,
  /* 2516 */ 'l', 'd', 'r', 's', 'w', 9, 0,
  /* 2523 */ 'l', 'd', 't', 'r', 's', 'w', 9, 0,
  /* 2531 */ 'l', 'd', 'u', 'r', 's', 'w', 9, 0,
  /* 2539 */ 'c', 'r', 'c', '3', '2', 'x', 9, 0,
  /* 2547 */ 'f', 'm', 'a', 'x', 9, 0,
  /* 2553 */ 's', 'm', 'a', 'x', 9, 0,
  /* 2559 */ 'u', 'm', 'a', 'x', 9, 0,
  /* 2565 */ 't', 'b', 'x', 9, 0,
  /* 2570 */ 'c', 'r', 'c', '3', '2', 'c', 'x', 9, 0,
  /* 2579 */ 'c', 'l', 'r', 'e', 'x', 9, 0,
  /* 2586 */ 'f', 'm', 'u', 'l', 'x', 9, 0,
  /* 2593 */ 'f', 'r', 'e', 'c', 'p', 'x', 9, 0,
  /* 2601 */ 'f', 'r', 'i', 'n', 't', 'x', 9, 0,
  /* 2609 */ 'c', 'b', 'z', 9, 0,
  /* 2614 */ 't', 'b', 'z', 9, 0,
  /* 2619 */ 'c', 'l', 'z', 9, 0,
  /* 2624 */ 'c', 'b', 'n', 'z', 9, 0,
  /* 2630 */ 't', 'b', 'n', 'z', 9, 0,
  /* 2636 */ 'f', 'r', 'i', 'n', 't', 'z', 9, 0,
  /* 2644 */ 'm', 'o', 'v', 'z', 9, 0,
  /* 2650 */ '.', 't', 'l', 's', 'd', 'e', 's', 'c', 'c', 'a', 'l', 'l', 32, 0,
  /* 2664 */ 'h', 'i', 'n', 't', 32, 0,
  /* 2670 */ 'b', '.', 0,
  /* 2673 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'E', 'N', 'D', 0,
  /* 2686 */ 'B', 'U', 'N', 'D', 'L', 'E', 0,
  /* 2693 */ 'D', 'B', 'G', '_', 'V', 'A', 'L', 'U', 'E', 0,
  /* 2703 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'S', 'T', 'A', 'R', 'T', 0,
  /* 2718 */ 'd', 'r', 'p', 's', 0,
  /* 2723 */ 'e', 'r', 'e', 't', 0,
  };
#endif

  // Emit the opcode for the instruction.
  uint64_t Bits1 = OpInfo[MCInst_getOpcode(MI)];
  uint64_t Bits2 = OpInfo2[MCInst_getOpcode(MI)];
  uint64_t Bits = (Bits2 << 32) | Bits1;
  // assert(Bits != 0 && "Cannot print this instruction.");
#ifndef CAPSTONE_DIET
  SStream_concat0(O, AsmStrs+(Bits & 4095)-1);
#endif


  // Fragment 0 encoded into 6 bits for 40 unique commands.
  //printf("Frag-0: %"PRIu64"\n", (Bits >> 12) & 63);
  switch ((Bits >> 12) & 63) {
  default:   // unreachable.
  case 0:
    // DBG_VALUE, BUNDLE, LIFETIME_START, LIFETIME_END, DRPS, ERET
    return;
    break;
  case 1:
    // ABSv16i8, ABSv2i32, ABSv2i64, ABSv4i16, ABSv4i32, ABSv8i16, ABSv8i8, A...
    printVRegOperand(MI, 0, O); 
    break;
  case 2:
    // ABSv1i64, ADCSWr, ADCSXr, ADCWr, ADCXr, ADDPv2i64p, ADDSWri, ADDSWrs, ...
    printOperand(MI, 0, O); 
    break;
  case 3:
    // ADDHNv2i64_v4i32, ADDHNv4i32_v8i16, ADDHNv8i16_v16i8, AESDrr, AESErr, ...
    printVRegOperand(MI, 1, O); 
    break;
  case 4:
    // B, BL
    printAlignedLabel(MI, 0, O); 
    return;
    break;
  case 5:
    // BRK, DCPS1, DCPS2, DCPS3, HINT, HLT, HVC, SMC, SVC
    printHexImm(MI, 0, O); 
    return;
    break;
  case 6:
    // Bcc
    printCondCode(MI, 0, O); 
    SStream_concat0(O, "\t"); 
    printAlignedLabel(MI, 1, O); 
    return;
    break;
  case 7:
    // DMB, DSB, ISB
    printBarrierOption(MI, 0, O); 
    return;
    break;
  case 8:
    // FMLAv1i32_indexed, FMLAv1i64_indexed, FMLSv1i32_indexed, FMLSv1i64_ind...
    printOperand(MI, 1, O); 
    break;
  case 9:
    // LD1Fourv16b, LD1Onev16b, LD1Rv16b, LD1Threev16b, LD1Twov16b, LD2Rv16b,...
    printTypedVectorList(MI, 0, O, 16, 'b', MRI); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 1, O); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 10:
    // LD1Fourv16b_POST, LD1Onev16b_POST, LD1Rv16b_POST, LD1Threev16b_POST, L...
    printTypedVectorList(MI, 1, O, 16, 'b', MRI); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 2, O); 
    SStream_concat0(O, "], "); 
    set_mem_access(MI, false);
    break;
  case 11:
    // LD1Fourv1d, LD1Onev1d, LD1Rv1d, LD1Threev1d, LD1Twov1d, LD2Rv1d, LD3Rv...
    printTypedVectorList(MI, 0, O, 1, 'd', MRI); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 1, O); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 12:
    // LD1Fourv1d_POST, LD1Onev1d_POST, LD1Rv1d_POST, LD1Threev1d_POST, LD1Tw...
    printTypedVectorList(MI, 1, O, 1, 'd', MRI); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 2, O); 
    SStream_concat0(O, "], "); 
    set_mem_access(MI, false);
    break;
  case 13:
    // LD1Fourv2d, LD1Onev2d, LD1Rv2d, LD1Threev2d, LD1Twov2d, LD2Rv2d, LD2Tw...
    printTypedVectorList(MI, 0, O, 2, 'd', MRI); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 1, O); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 14:
    // LD1Fourv2d_POST, LD1Onev2d_POST, LD1Rv2d_POST, LD1Threev2d_POST, LD1Tw...
    printTypedVectorList(MI, 1, O, 2, 'd', MRI); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 2, O); 
    SStream_concat0(O, "], "); 
    set_mem_access(MI, false);
    break;
  case 15:
    // LD1Fourv2s, LD1Onev2s, LD1Rv2s, LD1Threev2s, LD1Twov2s, LD2Rv2s, LD2Tw...
    printTypedVectorList(MI, 0, O, 2, 's', MRI); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 1, O); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 16:
    // LD1Fourv2s_POST, LD1Onev2s_POST, LD1Rv2s_POST, LD1Threev2s_POST, LD1Tw...
    printTypedVectorList(MI, 1, O, 2, 's', MRI); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 2, O); 
    SStream_concat0(O, "], "); 
    set_mem_access(MI, false);
    break;
  case 17:
    // LD1Fourv4h, LD1Onev4h, LD1Rv4h, LD1Threev4h, LD1Twov4h, LD2Rv4h, LD2Tw...
    printTypedVectorList(MI, 0, O, 4, 'h', MRI); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 1, O); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 18:
    // LD1Fourv4h_POST, LD1Onev4h_POST, LD1Rv4h_POST, LD1Threev4h_POST, LD1Tw...
    printTypedVectorList(MI, 1, O, 4, 'h', MRI); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 2, O); 
    SStream_concat0(O, "], "); 
    set_mem_access(MI, false);
    break;
  case 19:
    // LD1Fourv4s, LD1Onev4s, LD1Rv4s, LD1Threev4s, LD1Twov4s, LD2Rv4s, LD2Tw...
    printTypedVectorList(MI, 0, O, 4, 's', MRI); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 1, O); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 20:
    // LD1Fourv4s_POST, LD1Onev4s_POST, LD1Rv4s_POST, LD1Threev4s_POST, LD1Tw...
    printTypedVectorList(MI, 1, O, 4, 's', MRI); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 2, O); 
    SStream_concat0(O, "], "); 
    set_mem_access(MI, false);
    break;
  case 21:
    // LD1Fourv8b, LD1Onev8b, LD1Rv8b, LD1Threev8b, LD1Twov8b, LD2Rv8b, LD2Tw...
    printTypedVectorList(MI, 0, O, 8, 'b', MRI); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 1, O); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 22:
    // LD1Fourv8b_POST, LD1Onev8b_POST, LD1Rv8b_POST, LD1Threev8b_POST, LD1Tw...
    printTypedVectorList(MI, 1, O, 8, 'b', MRI); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 2, O); 
    SStream_concat0(O, "], "); 
    set_mem_access(MI, false);
    break;
  case 23:
    // LD1Fourv8h, LD1Onev8h, LD1Rv8h, LD1Threev8h, LD1Twov8h, LD2Rv8h, LD2Tw...
    printTypedVectorList(MI, 0, O, 8, 'h', MRI); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 1, O); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 24:
    // LD1Fourv8h_POST, LD1Onev8h_POST, LD1Rv8h_POST, LD1Threev8h_POST, LD1Tw...
    printTypedVectorList(MI, 1, O, 8, 'h', MRI); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 2, O); 
    SStream_concat0(O, "], "); 
    set_mem_access(MI, false);
    break;
  case 25:
    // LD1i16, LD2i16, LD3i16, LD4i16, ST1i16_POST, ST2i16_POST, ST3i16_POST,...
    printTypedVectorList(MI, 1, O, 0, 'h', MRI); 
    printVectorIndex(MI, 2, O); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 3, O); 
    break;
  case 26:
    // LD1i16_POST, LD2i16_POST, LD3i16_POST, LD4i16_POST
    printTypedVectorList(MI, 2, O, 0, 'h', MRI); 
    printVectorIndex(MI, 3, O); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 4, O); 
    SStream_concat0(O, "], "); 
    set_mem_access(MI, false);
    break;
  case 27:
    // LD1i32, LD2i32, LD3i32, LD4i32, ST1i32_POST, ST2i32_POST, ST3i32_POST,...
    printTypedVectorList(MI, 1, O, 0, 's', MRI); 
    printVectorIndex(MI, 2, O); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 3, O); 
    break;
  case 28:
    // LD1i32_POST, LD2i32_POST, LD3i32_POST, LD4i32_POST
    printTypedVectorList(MI, 2, O, 0, 's', MRI); 
    printVectorIndex(MI, 3, O); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 4, O); 
    SStream_concat0(O, "], "); 
    set_mem_access(MI, false);
    break;
  case 29:
    // LD1i64, LD2i64, LD3i64, LD4i64, ST1i64_POST, ST2i64_POST, ST3i64_POST,...
    printTypedVectorList(MI, 1, O, 0, 'd', MRI); 
    printVectorIndex(MI, 2, O); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 3, O); 
    break;
  case 30:
    // LD1i64_POST, LD2i64_POST, LD3i64_POST, LD4i64_POST
    printTypedVectorList(MI, 2, O, 0, 'd', MRI); 
    printVectorIndex(MI, 3, O); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 4, O); 
    SStream_concat0(O, "], "); 
    set_mem_access(MI, false);
    break;
  case 31:
    // LD1i8, LD2i8, LD3i8, LD4i8, ST1i8_POST, ST2i8_POST, ST3i8_POST, ST4i8_...
    printTypedVectorList(MI, 1, O, 0, 'b', MRI); 
    printVectorIndex(MI, 2, O); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 3, O); 
    break;
  case 32:
    // LD1i8_POST, LD2i8_POST, LD3i8_POST, LD4i8_POST
    printTypedVectorList(MI, 2, O, 0, 'b', MRI); 
    printVectorIndex(MI, 3, O); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 4, O); 
    SStream_concat0(O, "], "); 
    set_mem_access(MI, false);
    break;
  case 33:
    // MSR
    printMSRSystemRegister(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 34:
    // MSRpstate
    printSystemPStateField(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 35:
    // PRFMl, PRFMroW, PRFMroX, PRFMui, PRFUMi
    printPrefetchOp(MI, 0, O); 
    break;
  case 36:
    // ST1i16, ST2i16, ST3i16, ST4i16
    printTypedVectorList(MI, 0, O, 0, 'h', MRI); 
    printVectorIndex(MI, 1, O); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 2, O); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 37:
    // ST1i32, ST2i32, ST3i32, ST4i32
    printTypedVectorList(MI, 0, O, 0, 's', MRI); 
    printVectorIndex(MI, 1, O); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 2, O); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 38:
    // ST1i64, ST2i64, ST3i64, ST4i64
    printTypedVectorList(MI, 0, O, 0, 'd', MRI); 
    printVectorIndex(MI, 1, O); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 2, O); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 39:
    // ST1i8, ST2i8, ST3i8, ST4i8
    printTypedVectorList(MI, 0, O, 0, 'b', MRI); 
    printVectorIndex(MI, 1, O); 
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 2, O); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  }


  // Fragment 1 encoded into 6 bits for 41 unique commands.
  //printf("Frag-1: %"PRIu64"\n", (Bits >> 18) & 63);
  switch ((Bits >> 18) & 63) {
  default:   // unreachable.
  case 0:
    // ABSv16i8, ADDHNv8i16_v16i8, ADDPv16i8, ADDv16i8, AESDrr, AESErr, AESIM...
    SStream_concat0(O, ".16b, "); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_16B);
    break;
  case 1:
    // ABSv1i64, ADCSWr, ADCSXr, ADCWr, ADCXr, ADDPv2i64p, ADDSWri, ADDSWrs, ...
    SStream_concat0(O, ", "); 
    break;
  case 2:
    // ABSv2i32, ADDHNv2i64_v2i32, ADDPv2i32, ADDv2i32, BICv2i32, CLSv2i32, C...
    SStream_concat0(O, ".2s, "); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_2S);
    break;
  case 3:
    // ABSv2i64, ADDPv2i64, ADDv2i64, CMEQv2i64, CMEQv2i64rz, CMGEv2i64, CMGE...
    SStream_concat0(O, ".2d, "); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_2D);
    break;
  case 4:
    // ABSv4i16, ADDHNv4i32_v4i16, ADDPv4i16, ADDv4i16, BICv4i16, CLSv4i16, C...
    SStream_concat0(O, ".4h, "); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_4H);
    break;
  case 5:
    // ABSv4i32, ADDHNv2i64_v4i32, ADDPv4i32, ADDv4i32, BICv4i32, CLSv4i32, C...
    SStream_concat0(O, ".4s, "); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_4S);
    break;
  case 6:
    // ABSv8i16, ADDHNv4i32_v8i16, ADDPv8i16, ADDv8i16, BICv8i16, CLSv8i16, C...
    SStream_concat0(O, ".8h, "); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_8H);
    break;
  case 7:
    // ABSv8i8, ADDHNv8i16_v8i8, ADDPv8i8, ADDv8i8, ANDv8i8, BICv8i8, BIFv8i8...
    SStream_concat0(O, ".8b, "); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_8B);
    break;
  case 8:
    // BLR, BR, CLREX, RET, TLSDESCCALL
    return;
    break;
  case 9:
    // FCMPDri, FCMPEDri, FCMPESri, FCMPSri
    SStream_concat0(O, ", #0.0");
	arm64_op_addFP(MI, 0.0);
    return;
    break;
  case 10:
    // FMOVXDHighr, INSvi64gpr, INSvi64lane
    SStream_concat0(O, ".d");
	arm64_op_addVectorElementSizeSpecifier(MI, ARM64_VESS_D);
    printVectorIndex(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 11:
    // INSvi16gpr, INSvi16lane
    SStream_concat0(O, ".h");
	arm64_op_addVectorElementSizeSpecifier(MI, ARM64_VESS_H);
    printVectorIndex(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 12:
    // INSvi32gpr, INSvi32lane
    SStream_concat0(O, ".s");
	arm64_op_addVectorElementSizeSpecifier(MI, ARM64_VESS_S);
    printVectorIndex(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 13:
    // INSvi8gpr, INSvi8lane
    SStream_concat0(O, ".b");
	arm64_op_addVectorElementSizeSpecifier(MI, ARM64_VESS_B);
    printVectorIndex(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 14:
    // LD1Fourv16b_POST, LD1Fourv2d_POST, LD1Fourv4s_POST, LD1Fourv8h_POST, L...
    printPostIncOperand2(MI, 3, O, 64); 
    return;
    break;
  case 15:
    // LD1Fourv1d_POST, LD1Fourv2s_POST, LD1Fourv4h_POST, LD1Fourv8b_POST, LD...
    printPostIncOperand2(MI, 3, O, 32); 
    return;
    break;
  case 16:
    // LD1Onev16b_POST, LD1Onev2d_POST, LD1Onev4s_POST, LD1Onev8h_POST, LD1Tw...
    printPostIncOperand2(MI, 3, O, 16); 
    return;
    break;
  case 17:
    // LD1Onev1d_POST, LD1Onev2s_POST, LD1Onev4h_POST, LD1Onev8b_POST, LD1Rv1...
    printPostIncOperand2(MI, 3, O, 8); 
    return;
    break;
  case 18:
    // LD1Rv16b_POST, LD1Rv8b_POST
    printPostIncOperand2(MI, 3, O, 1); 
    return;
    break;
  case 19:
    // LD1Rv2s_POST, LD1Rv4s_POST, LD2Rv4h_POST, LD2Rv8h_POST, LD4Rv16b_POST,...
    printPostIncOperand2(MI, 3, O, 4); 
    return;
    break;
  case 20:
    // LD1Rv4h_POST, LD1Rv8h_POST, LD2Rv16b_POST, LD2Rv8b_POST
    printPostIncOperand2(MI, 3, O, 2); 
    return;
    break;
  case 21:
    // LD1Threev16b_POST, LD1Threev2d_POST, LD1Threev4s_POST, LD1Threev8h_POS...
    printPostIncOperand2(MI, 3, O, 48); 
    return;
    break;
  case 22:
    // LD1Threev1d_POST, LD1Threev2s_POST, LD1Threev4h_POST, LD1Threev8b_POST...
    printPostIncOperand2(MI, 3, O, 24); 
    return;
    break;
  case 23:
    // LD1i16, LD1i32, LD1i64, LD1i8, LD2i16, LD2i32, LD2i64, LD2i8, LD3i16, ...
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 24:
    // LD1i16_POST, LD2i8_POST
    printPostIncOperand2(MI, 5, O, 2); 
    return;
    break;
  case 25:
    // LD1i32_POST, LD2i16_POST, LD4i8_POST
    printPostIncOperand2(MI, 5, O, 4); 
    return;
    break;
  case 26:
    // LD1i64_POST, LD2i32_POST, LD4i16_POST
    printPostIncOperand2(MI, 5, O, 8); 
    return;
    break;
  case 27:
    // LD1i8_POST
    printPostIncOperand2(MI, 5, O, 1); 
    return;
    break;
  case 28:
    // LD2i64_POST, LD4i32_POST
    printPostIncOperand2(MI, 5, O, 16); 
    return;
    break;
  case 29:
    // LD3Rv16b_POST, LD3Rv8b_POST
    printPostIncOperand2(MI, 3, O, 3); 
    return;
    break;
  case 30:
    // LD3Rv2s_POST, LD3Rv4s_POST
    printPostIncOperand2(MI, 3, O, 12); 
    return;
    break;
  case 31:
    // LD3Rv4h_POST, LD3Rv8h_POST
    printPostIncOperand2(MI, 3, O, 6); 
    return;
    break;
  case 32:
    // LD3i16_POST
    printPostIncOperand2(MI, 5, O, 6); 
    return;
    break;
  case 33:
    // LD3i32_POST
    printPostIncOperand2(MI, 5, O, 12); 
    return;
    break;
  case 34:
    // LD3i64_POST
    printPostIncOperand2(MI, 5, O, 24); 
    return;
    break;
  case 35:
    // LD3i8_POST
    printPostIncOperand2(MI, 5, O, 3); 
    return;
    break;
  case 36:
    // LD4i64_POST
    printPostIncOperand2(MI, 5, O, 32); 
    return;
    break;
  case 37:
    // LDARB, LDARH, LDARW, LDARX, LDAXRB, LDAXRH, LDAXRW, LDAXRX, LDRBBpost,...
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    break;
  case 38:
    // PMULLv1i64, PMULLv2i64
    SStream_concat0(O, ".1q, ");
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_1Q);
    printVRegOperand(MI, 1, O); 
    break;
  case 39:
    // SADALPv2i32_v1i64, SADDLPv2i32_v1i64, UADALPv2i32_v1i64, UADDLPv2i32_v...
    SStream_concat0(O, ".1d, ");
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_1D);
    break;
  case 40:
    // ST1i16_POST, ST1i32_POST, ST1i64_POST, ST1i8_POST, ST2i16_POST, ST2i32...
    SStream_concat0(O, "], "); 
    set_mem_access(MI, false);
    break;
  }


  // Fragment 2 encoded into 5 bits for 28 unique commands.
  //printf("Frag-2: %"PRIu64"\n", (Bits >> 24) & 31);
  switch ((Bits >> 24) & 31) {
  default:   // unreachable.
  case 0:
    // ABSv16i8, ABSv2i32, ABSv2i64, ABSv4i16, ABSv4i32, ABSv8i16, ABSv8i8, A...
    printVRegOperand(MI, 1, O); 
    break;
  case 1:
    // ABSv1i64, ADCSWr, ADCSXr, ADCWr, ADCXr, ADDSWri, ADDSWrs, ADDSWrx, ADD...
    printOperand(MI, 1, O); 
    break;
  case 2:
    // ADDHNv2i64_v4i32, ADDHNv4i32_v8i16, ADDHNv8i16_v16i8, AESDrr, AESErr, ...
    printVRegOperand(MI, 2, O); 
    break;
  case 3:
    // ADRP
    printAdrpLabel(MI, 1, O); 
    return;
    break;
  case 4:
    // BFMWri, BFMXri, FMLAv1i32_indexed, FMLAv1i64_indexed, FMLSv1i32_indexe...
    printOperand(MI, 2, O); 
    break;
  case 5:
    // BICv2i32, BICv4i16, BICv4i32, BICv8i16, MOVKWi, MOVKXi, ORRv2i32, ORRv...
    printHexImm(MI, 2, O); 
    printShifter(MI, 3, O); 
    return;
    break;
  case 6:
    // CBNZW, CBNZX, CBZW, CBZX, LDRDl, LDRQl, LDRSWl, LDRSl, LDRWl, LDRXl, P...
    printAlignedLabel(MI, 1, O); 
    return;
    break;
  case 7:
    // FMOVDi, FMOVSi, FMOVv2f32_ns, FMOVv2f64_ns, FMOVv4f32_ns
    printFPImmOperand(MI, 1, O); 
    return;
    break;
  case 8:
    // INSvi16gpr, INSvi32gpr, INSvi64gpr, INSvi8gpr
    printOperand(MI, 3, O); 
    return;
    break;
  case 9:
    // INSvi16lane, INSvi32lane, INSvi64lane, INSvi8lane
    printVRegOperand(MI, 3, O); 
    break;
  case 10:
    // MOVID, MOVIv2d_ns
    printSIMDType10Operand(MI, 1, O); 
    return;
    break;
  case 11:
    // MOVIv16b_ns, MOVIv2i32, MOVIv2s_msl, MOVIv4i16, MOVIv4i32, MOVIv4s_msl...
    printHexImm(MI, 1, O); 
    break;
  case 12:
    // MRS
    printMRSSystemRegister(MI, 1, O); 
    return;
    break;
  case 13:
    // PMULLv1i64
    SStream_concat0(O, ".1d, "); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_1D);
    printVRegOperand(MI, 2, O); 
    SStream_concat0(O, ".1d"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_1D);
    return;
    break;
  case 14:
    // PMULLv2i64
    SStream_concat0(O, ".2d, "); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_2D);
    printVRegOperand(MI, 2, O); 
    SStream_concat0(O, ".2d"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_2D);
    return;
    break;
  case 15:
    // ST1i16_POST, ST2i8_POST
    printPostIncOperand2(MI, 4, O, 2); 
    return;
    break;
  case 16:
    // ST1i32_POST, ST2i16_POST, ST4i8_POST
    printPostIncOperand2(MI, 4, O, 4); 
    return;
    break;
  case 17:
    // ST1i64_POST, ST2i32_POST, ST4i16_POST
    printPostIncOperand2(MI, 4, O, 8); 
    return;
    break;
  case 18:
    // ST1i8_POST
    printPostIncOperand2(MI, 4, O, 1); 
    return;
    break;
  case 19:
    // ST2i64_POST, ST4i32_POST
    printPostIncOperand2(MI, 4, O, 16); 
    return;
    break;
  case 20:
    // ST3i16_POST
    printPostIncOperand2(MI, 4, O, 6); 
    return;
    break;
  case 21:
    // ST3i32_POST
    printPostIncOperand2(MI, 4, O, 12); 
    return;
    break;
  case 22:
    // ST3i64_POST
    printPostIncOperand2(MI, 4, O, 24); 
    return;
    break;
  case 23:
    // ST3i8_POST
    printPostIncOperand2(MI, 4, O, 3); 
    return;
    break;
  case 24:
    // ST4i64_POST
    printPostIncOperand2(MI, 4, O, 32); 
    return;
    break;
  case 25:
    // SYSxt
    printSysCROperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printSysCROperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 4, O); 
    return;
    break;
  case 26:
    // TBLv16i8Four, TBLv16i8One, TBLv16i8Three, TBLv16i8Two, TBLv8i8Four, TB...
    printTypedVectorList(MI, 1, O, 16, 'b', MRI); 
    SStream_concat0(O, ", "); 
    printVRegOperand(MI, 2, O); 
    break;
  case 27:
    // TBXv16i8Four, TBXv16i8One, TBXv16i8Three, TBXv16i8Two, TBXv8i8Four, TB...
    printTypedVectorList(MI, 2, O, 16, 'b', MRI); 
    SStream_concat0(O, ", "); 
    printVRegOperand(MI, 3, O); 
    break;
  }


  // Fragment 3 encoded into 6 bits for 42 unique commands.
  //printf("Frag-3: %"PRIu64"\n", (Bits >> 29) & 63);
  switch ((Bits >> 29) & 63) {
  default:   // unreachable.
  case 0:
    // ABSv16i8, ADDVv16i8v, AESDrr, AESErr, AESIMCrr, AESMCrr, CLSv16i8, CLZ...
    SStream_concat0(O, ".16b"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_16B);
    return;
    break;
  case 1:
    // ABSv1i64, ADR, CLSWr, CLSXr, CLZWr, CLZXr, DUPv16i8gpr, DUPv2i32gpr, D...
    return;
    break;
  case 2:
    // ABSv2i32, CLSv2i32, CLZv2i32, FABSv2f32, FADDPv2i32p, FCVTASv2f32, FCV...
    SStream_concat0(O, ".2s"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_2S);
    return;
    break;
  case 3:
    // ABSv2i64, ADDPv2i64p, FABSv2f64, FADDPv2i64p, FCVTASv2f64, FCVTAUv2f64...
    SStream_concat0(O, ".2d"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_2D);
    return;
    break;
  case 4:
    // ABSv4i16, ADDVv4i16v, CLSv4i16, CLZv4i16, FCVTLv4i16, NEGv4i16, REV32v...
    SStream_concat0(O, ".4h"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_4H);
    return;
    break;
  case 5:
    // ABSv4i32, ADDVv4i32v, CLSv4i32, CLZv4i32, FABSv4f32, FCVTASv4f32, FCVT...
    SStream_concat0(O, ".4s"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_4S);
    return;
    break;
  case 6:
    // ABSv8i16, ADDVv8i16v, CLSv8i16, CLZv8i16, FCVTLv8i16, NEGv8i16, REV32v...
    SStream_concat0(O, ".8h"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_8H);
    return;
    break;
  case 7:
    // ABSv8i8, ADDVv8i8v, CLSv8i8, CLZv8i8, CNTv8i8, NEGv8i8, NOTv8i8, RBITv...
    SStream_concat0(O, ".8b"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_8B);
    return;
    break;
  case 8:
    // ADCSWr, ADCSXr, ADCWr, ADCXr, ADDSWri, ADDSWrs, ADDSWrx, ADDSXri, ADDS...
    SStream_concat0(O, ", "); 
    break;
  case 9:
    // ADDHNv2i64_v2i32, ADDHNv2i64_v4i32, ADDPv2i64, ADDv2i64, CMEQv2i64, CM...
    SStream_concat0(O, ".2d, "); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_2D);
    break;
  case 10:
    // ADDHNv4i32_v4i16, ADDHNv4i32_v8i16, ADDPv4i32, ADDv4i32, CMEQv4i32, CM...
    SStream_concat0(O, ".4s, "); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_4S);
    break;
  case 11:
    // ADDHNv8i16_v16i8, ADDHNv8i16_v8i8, ADDPv8i16, ADDv8i16, CMEQv8i16, CMG...
    SStream_concat0(O, ".8h, "); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_8H);
    break;
  case 12:
    // ADDPv16i8, ADDv16i8, ANDv16i8, BICv16i8, BIFv16i8, BITv16i8, BSLv16i8,...
    SStream_concat0(O, ".16b, "); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_16B);
    break;
  case 13:
    // ADDPv2i32, ADDv2i32, CMEQv2i32, CMGEv2i32, CMGTv2i32, CMHIv2i32, CMHSv...
    SStream_concat0(O, ".2s, "); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_2S);
    break;
  case 14:
    // ADDPv4i16, ADDv4i16, CMEQv4i16, CMGEv4i16, CMGTv4i16, CMHIv4i16, CMHSv...
    SStream_concat0(O, ".4h, "); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_4H);
    break;
  case 15:
    // ADDPv8i8, ADDv8i8, ANDv8i8, BICv8i8, BIFv8i8, BITv8i8, BSLv8i8, CMEQv8...
    SStream_concat0(O, ".8b, "); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_8B);
    break;
  case 16:
    // CMEQv16i8rz, CMGEv16i8rz, CMGTv16i8rz, CMLEv16i8rz, CMLTv16i8rz
    SStream_concat0(O, ".16b, #0"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_16B);
	arm64_op_addFP(MI, 0.0);
    return;
    break;
  case 17:
    // CMEQv1i64rz, CMGEv1i64rz, CMGTv1i64rz, CMLEv1i64rz, CMLTv1i64rz
    SStream_concat0(O, ", #0"); 
	arm64_op_addImm(MI, 0);
    return;
    break;
  case 18:
    // CMEQv2i32rz, CMGEv2i32rz, CMGTv2i32rz, CMLEv2i32rz, CMLTv2i32rz
    SStream_concat0(O, ".2s, #0"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_2S);
	arm64_op_addImm(MI, 0);
    return;
    break;
  case 19:
    // CMEQv2i64rz, CMGEv2i64rz, CMGTv2i64rz, CMLEv2i64rz, CMLTv2i64rz
    SStream_concat0(O, ".2d, #0"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_2D);
	arm64_op_addImm(MI, 0);
    return;
    break;
  case 20:
    // CMEQv4i16rz, CMGEv4i16rz, CMGTv4i16rz, CMLEv4i16rz, CMLTv4i16rz
    SStream_concat0(O, ".4h, #0"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_4H);
	arm64_op_addImm(MI, 0);
    return;
    break;
  case 21:
    // CMEQv4i32rz, CMGEv4i32rz, CMGTv4i32rz, CMLEv4i32rz, CMLTv4i32rz
    SStream_concat0(O, ".4s, #0"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_4S);
	arm64_op_addImm(MI, 0);
    return;
    break;
  case 22:
    // CMEQv8i16rz, CMGEv8i16rz, CMGTv8i16rz, CMLEv8i16rz, CMLTv8i16rz
    SStream_concat0(O, ".8h, #0"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_8H);
	arm64_op_addImm(MI, 0);
    return;
    break;
  case 23:
    // CMEQv8i8rz, CMGEv8i8rz, CMGTv8i8rz, CMLEv8i8rz, CMLTv8i8rz
    SStream_concat0(O, ".8b, #0"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_8B);
	arm64_op_addImm(MI, 0);
    return;
    break;
  case 24:
    // CPYi16, DUPv4i16lane, DUPv8i16lane, INSvi16lane, SMOVvi16to32, SMOVvi1...
    SStream_concat0(O, ".h"); 
	arm64_op_addVectorElementSizeSpecifier(MI, ARM64_VESS_H);
    break;
  case 25:
    // CPYi32, DUPv2i32lane, DUPv4i32lane, INSvi32lane, SMOVvi32to64, UMOVvi3...
    SStream_concat0(O, ".s"); 
	arm64_op_addVectorElementSizeSpecifier(MI, ARM64_VESS_S);
    break;
  case 26:
    // CPYi64, DUPv2i64lane, FMOVDXHighr, INSvi64lane, UMOVvi64
    SStream_concat0(O, ".d"); 
	arm64_op_addVectorElementSizeSpecifier(MI, ARM64_VESS_D);
    break;
  case 27:
    // CPYi8, DUPv16i8lane, DUPv8i8lane, INSvi8lane, SMOVvi8to32, SMOVvi8to64...
    SStream_concat0(O, ".b"); 
	arm64_op_addVectorElementSizeSpecifier(MI, ARM64_VESS_B);
    break;
  case 28:
    // FCMEQv1i32rz, FCMEQv1i64rz, FCMGEv1i32rz, FCMGEv1i64rz, FCMGTv1i32rz, ...
    SStream_concat0(O, ", #0.0"); 
	arm64_op_addFP(MI, 0.0);
    return;
    break;
  case 29:
    // FCMEQv2i32rz, FCMGEv2i32rz, FCMGTv2i32rz, FCMLEv2i32rz, FCMLTv2i32rz
    SStream_concat0(O, ".2s, #0.0"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_2S);
	arm64_op_addFP(MI, 0.0);
    return;
    break;
  case 30:
    // FCMEQv2i64rz, FCMGEv2i64rz, FCMGTv2i64rz, FCMLEv2i64rz, FCMLTv2i64rz
    SStream_concat0(O, ".2d, #0.0"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_2D);
	arm64_op_addFP(MI, 0.0);
    return;
    break;
  case 31:
    // FCMEQv4i32rz, FCMGEv4i32rz, FCMGTv4i32rz, FCMLEv4i32rz, FCMLTv4i32rz
    SStream_concat0(O, ".4s, #0.0"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_4S);
	arm64_op_addFP(MI, 0.0);
    return;
    break;
  case 32:
    // LDARB, LDARH, LDARW, LDARX, LDAXRB, LDAXRH, LDAXRW, LDAXRX, LDXRB, LDX...
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 33:
    // LDAXPW, LDAXPX, LDNPDi, LDNPQi, LDNPSi, LDNPWi, LDNPXi, LDPDi, LDPDpos...
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    break;
  case 34:
    // LDRBBpost, LDRBpost, LDRDpost, LDRHHpost, LDRHpost, LDRQpost, LDRSBWpo...
    SStream_concat0(O, "], "); 
    set_mem_access(MI, false);
    printOperand(MI, 3, O); 
    return;
    break;
  case 35:
    // MOVIv2i32, MOVIv2s_msl, MOVIv4i16, MOVIv4i32, MOVIv4s_msl, MOVIv8i16, ...
    printShifter(MI, 2, O); 
    return;
    break;
  case 36:
    // SHLLv16i8
    SStream_concat0(O, ".16b, #8"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_16B);
	arm64_op_addImm(MI, 8);
    return;
    break;
  case 37:
    // SHLLv2i32
    SStream_concat0(O, ".2s, #32"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_2S);
	arm64_op_addImm(MI, 32);
    return;
    break;
  case 38:
    // SHLLv4i16
    SStream_concat0(O, ".4h, #16"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_4H);
	arm64_op_addImm(MI, 16);
    return;
    break;
  case 39:
    // SHLLv4i32
    SStream_concat0(O, ".4s, #32"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_4S);
	arm64_op_addImm(MI, 32);
    return;
    break;
  case 40:
    // SHLLv8i16
    SStream_concat0(O, ".8h, #16"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_8H);
	arm64_op_addImm(MI, 16);
    return;
    break;
  case 41:
    // SHLLv8i8
    SStream_concat0(O, ".8b, #8"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_8B);
	arm64_op_addImm(MI, 8);
    return;
    break;
  }


  // Fragment 4 encoded into 5 bits for 18 unique commands.
  //printf("Frag-4: %"PRIu64"\n", (Bits >> 35) & 31);
  switch ((Bits >> 35) & 31) {
  default:   // unreachable.
  case 0:
    // ADCSWr, ADCSXr, ADCWr, ADCXr, ADDSXrx64, ADDXrx64, ADDv1i64, ASRVWr, A...
    printOperand(MI, 2, O); 
    break;
  case 1:
    // ADDHNv2i64_v2i32, ADDHNv4i32_v4i16, ADDHNv8i16_v8i8, ADDPv16i8, ADDPv2...
    printVRegOperand(MI, 2, O); 
    break;
  case 2:
    // ADDHNv2i64_v4i32, ADDHNv4i32_v8i16, ADDHNv8i16_v16i8, BITv16i8, BITv8i...
    printVRegOperand(MI, 3, O); 
    break;
  case 3:
    // ADDSWri, ADDSXri, ADDWri, ADDXri, SUBSWri, SUBSXri, SUBWri, SUBXri
    printAddSubImm(MI, 2, O); 
    return;
    break;
  case 4:
    // ADDSWrs, ADDSXrs, ADDWrs, ADDXrs, ANDSWrs, ANDSXrs, ANDWrs, ANDXrs, BI...
    printShiftedRegister(MI, 2, O); 
    return;
    break;
  case 5:
    // ADDSWrx, ADDSXrx, ADDWrx, ADDXrx, SUBSWrx, SUBSXrx, SUBWrx, SUBXrx
    printExtendedRegister(MI, 2, O); 
    return;
    break;
  case 6:
    // ANDSWri, ANDWri, EORWri, ORRWri
    printLogicalImm32(MI, 2, O); 
    return;
    break;
  case 7:
    // ANDSXri, ANDXri, EORXri, ORRXri
    printLogicalImm64(MI, 2, O); 
    return;
    break;
  case 8:
    // BFMWri, BFMXri, LDPDpost, LDPDpre, LDPQpost, LDPQpre, LDPSWpost, LDPSW...
    printOperand(MI, 3, O); 
    break;
  case 9:
    // CPYi16, CPYi32, CPYi64, CPYi8, DUPv16i8lane, DUPv2i32lane, DUPv2i64lan...
    printVectorIndex(MI, 2, O); 
    return;
    break;
  case 10:
    // INSvi16lane, INSvi32lane, INSvi64lane, INSvi8lane
    printVectorIndex(MI, 4, O); 
    return;
    break;
  case 11:
    // LDRBBui, LDRBui, LDRSBWui, LDRSBXui, STRBBui, STRBui
    printUImm12Offset2(MI, 2, O, 1); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 12:
    // LDRDui, LDRXui, PRFMui, STRDui, STRXui
    printUImm12Offset2(MI, 2, O, 8); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 13:
    // LDRHHui, LDRHui, LDRSHWui, LDRSHXui, STRHHui, STRHui
    printUImm12Offset2(MI, 2, O, 2); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 14:
    // LDRQui, STRQui
    printUImm12Offset2(MI, 2, O, 16); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 15:
    // LDRSWui, LDRSui, LDRWui, STRSui, STRWui
    printUImm12Offset2(MI, 2, O, 4); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 16:
    // SYSLxt
    printSysCROperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printSysCROperand(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 4, O); 
    return;
    break;
  case 17:
    // TBNZW, TBNZX, TBZW, TBZX
    printAlignedLabel(MI, 2, O); 
    return;
    break;
  }


  // Fragment 5 encoded into 5 bits for 19 unique commands.
  //printf("Frag-5: %"PRIu64"\n", (Bits >> 40) & 31);
  switch ((Bits >> 40) & 31) {
  default:   // unreachable.
  case 0:
    // ADCSWr, ADCSXr, ADCWr, ADCXr, ADDv1i64, ASRVWr, ASRVXr, CMEQv1i64, CMG...
    return;
    break;
  case 1:
    // ADDHNv2i64_v2i32, ADDHNv2i64_v4i32, ADDPv2i64, ADDv2i64, CMEQv2i64, CM...
    SStream_concat0(O, ".2d"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_2D);
    return;
    break;
  case 2:
    // ADDHNv4i32_v4i16, ADDHNv4i32_v8i16, ADDPv4i32, ADDv4i32, CMEQv4i32, CM...
    SStream_concat0(O, ".4s"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_4S);
    return;
    break;
  case 3:
    // ADDHNv8i16_v16i8, ADDHNv8i16_v8i8, ADDPv8i16, ADDv8i16, CMEQv8i16, CMG...
    SStream_concat0(O, ".8h"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_8H);
    return;
    break;
  case 4:
    // ADDPv16i8, ADDv16i8, ANDv16i8, BICv16i8, BIFv16i8, BITv16i8, BSLv16i8,...
    SStream_concat0(O, ".16b"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_16B);
    return;
    break;
  case 5:
    // ADDPv2i32, ADDv2i32, CMEQv2i32, CMGEv2i32, CMGTv2i32, CMHIv2i32, CMHSv...
    SStream_concat0(O, ".2s"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_2S);
    return;
    break;
  case 6:
    // ADDPv4i16, ADDv4i16, CMEQv4i16, CMGEv4i16, CMGTv4i16, CMHIv4i16, CMHSv...
    SStream_concat0(O, ".4h"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_4H);
    return;
    break;
  case 7:
    // ADDPv8i8, ADDv8i8, ANDv8i8, BICv8i8, BIFv8i8, BITv8i8, BSLv8i8, CMEQv8...
    SStream_concat0(O, ".8b"); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_8B);
    return;
    break;
  case 8:
    // ADDSXrx64, ADDXrx64, SUBSXrx64, SUBXrx64
    printArithExtend(MI, 3, O); 
    return;
    break;
  case 9:
    // BFMWri, BFMXri, CCMNWi, CCMNWr, CCMNXi, CCMNXr, CCMPWi, CCMPWr, CCMPXi...
    SStream_concat0(O, ", "); 
    break;
  case 10:
    // EXTv16i8
    SStream_concat0(O, ".16b, "); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_16B);
    printOperand(MI, 3, O); 
    return;
    break;
  case 11:
    // EXTv8i8
    SStream_concat0(O, ".8b, "); 
	arm64_op_addVectorArrSpecifier(MI, ARM64_VAS_8B);
    printOperand(MI, 3, O); 
    return;
    break;
  case 12:
    // FMLAv1i32_indexed, FMLAv2i32_indexed, FMLAv4i32_indexed, FMLSv1i32_ind...
    SStream_concat0(O, ".s"); 
	arm64_op_addVectorElementSizeSpecifier(MI, ARM64_VESS_S);
    break;
  case 13:
    // FMLAv1i64_indexed, FMLAv2i64_indexed, FMLSv1i64_indexed, FMLSv2i64_ind...
    SStream_concat0(O, ".d"); 
	arm64_op_addVectorElementSizeSpecifier(MI, ARM64_VESS_D);
    break;
  case 14:
    // LDAXPW, LDAXPX, LDTRBi, LDTRHi, LDTRSBWi, LDTRSBXi, LDTRSHWi, LDTRSHXi...
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 15:
    // LDPDpost, LDPQpost, LDPSWpost, LDPSpost, LDPWpost, LDPXpost, STPDpost,...
    SStream_concat0(O, "], "); 
    set_mem_access(MI, false);
    break;
  case 16:
    // LDRBBpre, LDRBpre, LDRDpre, LDRHHpre, LDRHpre, LDRQpre, LDRSBWpre, LDR...
    SStream_concat0(O, "]!"); 
    set_mem_access(MI, false);
    return;
    break;
  case 17:
    // MLAv4i16_indexed, MLAv8i16_indexed, MLSv4i16_indexed, MLSv8i16_indexed...
    SStream_concat0(O, ".h"); 
	arm64_op_addVectorElementSizeSpecifier(MI, ARM64_VESS_H);
    break;
  case 18:
    // STLXPW, STLXPX, STXPW, STXPX
    SStream_concat0(O, ", ["); 
    set_mem_access(MI, true);
    printOperand(MI, 3, O); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  }


  // Fragment 6 encoded into 5 bits for 21 unique commands.
  //printf("Frag-6: %"PRIu64"\n", (Bits >> 45) & 31);
  switch ((Bits >> 45) & 31) {
  default:   // unreachable.
  case 0:
    // BFMWri, BFMXri
    printOperand(MI, 4, O); 
    return;
    break;
  case 1:
    // CCMNWi, CCMNWr, CCMNXi, CCMNXr, CCMPWi, CCMPWr, CCMPXi, CCMPXr, CSELWr...
    printCondCode(MI, 3, O); 
    return;
    break;
  case 2:
    // EXTRWrri, EXTRXrri, FMADDDrrr, FMADDSrrr, FMSUBDrrr, FMSUBSrrr, FNMADD...
    printOperand(MI, 3, O); 
    return;
    break;
  case 3:
    // FMLAv1i32_indexed, FMLAv1i64_indexed, FMLAv2i32_indexed, FMLAv2i64_ind...
    printVectorIndex(MI, 4, O); 
    return;
    break;
  case 4:
    // FMULXv1i32_indexed, FMULXv1i64_indexed, FMULXv2i32_indexed, FMULXv2i64...
    printVectorIndex(MI, 3, O); 
    return;
    break;
  case 5:
    // LDNPDi, LDNPXi, LDPDi, LDPXi, STNPDi, STNPXi, STPDi, STPXi
    printImmScale(MI, 3, O, 8); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 6:
    // LDNPQi, LDPQi, STNPQi, STPQi
    printImmScale(MI, 3, O, 16); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 7:
    // LDNPSi, LDNPWi, LDPSWi, LDPSi, LDPWi, STNPSi, STNPWi, STPSi, STPWi
    printImmScale(MI, 3, O, 4); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 8:
    // LDPDpost, LDPDpre, LDPXpost, LDPXpre, STPDpost, STPDpre, STPXpost, STP...
    printImmScale(MI, 4, O, 8); 
    break;
  case 9:
    // LDPQpost, LDPQpre, STPQpost, STPQpre
    printImmScale(MI, 4, O, 16); 
    break;
  case 10:
    // LDPSWpost, LDPSWpre, LDPSpost, LDPSpre, LDPWpost, LDPWpre, STPSpost, S...
    printImmScale(MI, 4, O, 4); 
    break;
  case 11:
    // LDRBBroW, LDRBroW, LDRSBWroW, LDRSBXroW, STRBBroW, STRBroW
    printMemExtend(MI, 3, O, 'w', 8); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 12:
    // LDRBBroX, LDRBroX, LDRSBWroX, LDRSBXroX, STRBBroX, STRBroX
    printMemExtend(MI, 3, O, 'x', 8); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 13:
    // LDRDroW, LDRXroW, PRFMroW, STRDroW, STRXroW
    printMemExtend(MI, 3, O, 'w', 64); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 14:
    // LDRDroX, LDRXroX, PRFMroX, STRDroX, STRXroX
    printMemExtend(MI, 3, O, 'x', 64); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 15:
    // LDRHHroW, LDRHroW, LDRSHWroW, LDRSHXroW, STRHHroW, STRHroW
    printMemExtend(MI, 3, O, 'w', 16); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 16:
    // LDRHHroX, LDRHroX, LDRSHWroX, LDRSHXroX, STRHHroX, STRHroX
    printMemExtend(MI, 3, O, 'x', 16); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 17:
    // LDRQroW, STRQroW
    printMemExtend(MI, 3, O, 'w', 128); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 18:
    // LDRQroX, STRQroX
    printMemExtend(MI, 3, O, 'x', 128); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 19:
    // LDRSWroW, LDRSroW, LDRWroW, STRSroW, STRWroW
    printMemExtend(MI, 3, O, 'w', 32); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  case 20:
    // LDRSWroX, LDRSroX, LDRWroX, STRSroX, STRWroX
    printMemExtend(MI, 3, O, 'x', 32); 
    SStream_concat0(O, "]"); 
    set_mem_access(MI, false);
    return;
    break;
  }


  // Fragment 7 encoded into 1 bits for 2 unique commands.
  //printf("Frag-7: %"PRIu64"\n", (Bits >> 50) & 1);
  if ((Bits >> 50) & 1) {
    // LDPDpre, LDPQpre, LDPSWpre, LDPSpre, LDPWpre, LDPXpre, STPDpre, STPQpr...
    SStream_concat0(O, "]!"); 
    set_mem_access(MI, false);
    return;
  } else {
    // LDPDpost, LDPQpost, LDPSWpost, LDPSpost, LDPWpost, LDPXpost, STPDpost,...
    return;
  }
}


/// getRegisterName - This method is automatically generated by tblgen
/// from the register set description.  This returns the assembler name
/// for the specified register.
static const char *getRegisterName(unsigned RegNo, int AltIdx)
{
  // assert(RegNo && RegNo < 420 && "Invalid register number!");

#ifndef CAPSTONE_DIET
  static const char AsmStrsNoRegAltName[] = {
  /* 0 */ 'D', '7', '_', 'D', '8', '_', 'D', '9', '_', 'D', '1', '0', 0,
  /* 13 */ 'Q', '7', '_', 'Q', '8', '_', 'Q', '9', '_', 'Q', '1', '0', 0,
  /* 26 */ 'b', '1', '0', 0,
  /* 30 */ 'd', '1', '0', 0,
  /* 34 */ 'h', '1', '0', 0,
  /* 38 */ 'q', '1', '0', 0,
  /* 42 */ 's', '1', '0', 0,
  /* 46 */ 'w', '1', '0', 0,
  /* 50 */ 'x', '1', '0', 0,
  /* 54 */ 'D', '1', '7', '_', 'D', '1', '8', '_', 'D', '1', '9', '_', 'D', '2', '0', 0,
  /* 70 */ 'Q', '1', '7', '_', 'Q', '1', '8', '_', 'Q', '1', '9', '_', 'Q', '2', '0', 0,
  /* 86 */ 'b', '2', '0', 0,
  /* 90 */ 'd', '2', '0', 0,
  /* 94 */ 'h', '2', '0', 0,
  /* 98 */ 'q', '2', '0', 0,
  /* 102 */ 's', '2', '0', 0,
  /* 106 */ 'w', '2', '0', 0,
  /* 110 */ 'x', '2', '0', 0,
  /* 114 */ 'D', '2', '7', '_', 'D', '2', '8', '_', 'D', '2', '9', '_', 'D', '3', '0', 0,
  /* 130 */ 'Q', '2', '7', '_', 'Q', '2', '8', '_', 'Q', '2', '9', '_', 'Q', '3', '0', 0,
  /* 146 */ 'b', '3', '0', 0,
  /* 150 */ 'd', '3', '0', 0,
  /* 154 */ 'h', '3', '0', 0,
  /* 158 */ 'q', '3', '0', 0,
  /* 162 */ 's', '3', '0', 0,
  /* 166 */ 'w', '3', '0', 0,
  /* 170 */ 'x', '3', '0', 0,
  /* 174 */ 'D', '2', '9', '_', 'D', '3', '0', '_', 'D', '3', '1', '_', 'D', '0', 0,
  /* 189 */ 'Q', '2', '9', '_', 'Q', '3', '0', '_', 'Q', '3', '1', '_', 'Q', '0', 0,
  /* 204 */ 'b', '0', 0,
  /* 207 */ 'd', '0', 0,
  /* 210 */ 'h', '0', 0,
  /* 213 */ 'q', '0', 0,
  /* 216 */ 's', '0', 0,
  /* 219 */ 'w', '0', 0,
  /* 222 */ 'x', '0', 0,
  /* 225 */ 'D', '8', '_', 'D', '9', '_', 'D', '1', '0', '_', 'D', '1', '1', 0,
  /* 239 */ 'Q', '8', '_', 'Q', '9', '_', 'Q', '1', '0', '_', 'Q', '1', '1', 0,
  /* 253 */ 'b', '1', '1', 0,
  /* 257 */ 'd', '1', '1', 0,
  /* 261 */ 'h', '1', '1', 0,
  /* 265 */ 'q', '1', '1', 0,
  /* 269 */ 's', '1', '1', 0,
  /* 273 */ 'w', '1', '1', 0,
  /* 277 */ 'x', '1', '1', 0,
  /* 281 */ 'D', '1', '8', '_', 'D', '1', '9', '_', 'D', '2', '0', '_', 'D', '2', '1', 0,
  /* 297 */ 'Q', '1', '8', '_', 'Q', '1', '9', '_', 'Q', '2', '0', '_', 'Q', '2', '1', 0,
  /* 313 */ 'b', '2', '1', 0,
  /* 317 */ 'd', '2', '1', 0,
  /* 321 */ 'h', '2', '1', 0,
  /* 325 */ 'q', '2', '1', 0,
  /* 329 */ 's', '2', '1', 0,
  /* 333 */ 'w', '2', '1', 0,
  /* 337 */ 'x', '2', '1', 0,
  /* 341 */ 'D', '2', '8', '_', 'D', '2', '9', '_', 'D', '3', '0', '_', 'D', '3', '1', 0,
  /* 357 */ 'Q', '2', '8', '_', 'Q', '2', '9', '_', 'Q', '3', '0', '_', 'Q', '3', '1', 0,
  /* 373 */ 'b', '3', '1', 0,
  /* 377 */ 'd', '3', '1', 0,
  /* 381 */ 'h', '3', '1', 0,
  /* 385 */ 'q', '3', '1', 0,
  /* 389 */ 's', '3', '1', 0,
  /* 393 */ 'D', '3', '0', '_', 'D', '3', '1', '_', 'D', '0', '_', 'D', '1', 0,
  /* 407 */ 'Q', '3', '0', '_', 'Q', '3', '1', '_', 'Q', '0', '_', 'Q', '1', 0,
  /* 421 */ 'b', '1', 0,
  /* 424 */ 'd', '1', 0,
  /* 427 */ 'h', '1', 0,
  /* 430 */ 'q', '1', 0,
  /* 433 */ 's', '1', 0,
  /* 436 */ 'w', '1', 0,
  /* 439 */ 'x', '1', 0,
  /* 442 */ 'D', '9', '_', 'D', '1', '0', '_', 'D', '1', '1', '_', 'D', '1', '2', 0,
  /* 457 */ 'Q', '9', '_', 'Q', '1', '0', '_', 'Q', '1', '1', '_', 'Q', '1', '2', 0,
  /* 472 */ 'b', '1', '2', 0,
  /* 476 */ 'd', '1', '2', 0,
  /* 480 */ 'h', '1', '2', 0,
  /* 484 */ 'q', '1', '2', 0,
  /* 488 */ 's', '1', '2', 0,
  /* 492 */ 'w', '1', '2', 0,
  /* 496 */ 'x', '1', '2', 0,
  /* 500 */ 'D', '1', '9', '_', 'D', '2', '0', '_', 'D', '2', '1', '_', 'D', '2', '2', 0,
  /* 516 */ 'Q', '1', '9', '_', 'Q', '2', '0', '_', 'Q', '2', '1', '_', 'Q', '2', '2', 0,
  /* 532 */ 'b', '2', '2', 0,
  /* 536 */ 'd', '2', '2', 0,
  /* 540 */ 'h', '2', '2', 0,
  /* 544 */ 'q', '2', '2', 0,
  /* 548 */ 's', '2', '2', 0,
  /* 552 */ 'w', '2', '2', 0,
  /* 556 */ 'x', '2', '2', 0,
  /* 560 */ 'D', '3', '1', '_', 'D', '0', '_', 'D', '1', '_', 'D', '2', 0,
  /* 573 */ 'Q', '3', '1', '_', 'Q', '0', '_', 'Q', '1', '_', 'Q', '2', 0,
  /* 586 */ 'b', '2', 0,
  /* 589 */ 'd', '2', 0,
  /* 592 */ 'h', '2', 0,
  /* 595 */ 'q', '2', 0,
  /* 598 */ 's', '2', 0,
  /* 601 */ 'w', '2', 0,
  /* 604 */ 'x', '2', 0,
  /* 607 */ 'D', '1', '0', '_', 'D', '1', '1', '_', 'D', '1', '2', '_', 'D', '1', '3', 0,
  /* 623 */ 'Q', '1', '0', '_', 'Q', '1', '1', '_', 'Q', '1', '2', '_', 'Q', '1', '3', 0,
  /* 639 */ 'b', '1', '3', 0,
  /* 643 */ 'd', '1', '3', 0,
  /* 647 */ 'h', '1', '3', 0,
  /* 651 */ 'q', '1', '3', 0,
  /* 655 */ 's', '1', '3', 0,
  /* 659 */ 'w', '1', '3', 0,
  /* 663 */ 'x', '1', '3', 0,
  /* 667 */ 'D', '2', '0', '_', 'D', '2', '1', '_', 'D', '2', '2', '_', 'D', '2', '3', 0,
  /* 683 */ 'Q', '2', '0', '_', 'Q', '2', '1', '_', 'Q', '2', '2', '_', 'Q', '2', '3', 0,
  /* 699 */ 'b', '2', '3', 0,
  /* 703 */ 'd', '2', '3', 0,
  /* 707 */ 'h', '2', '3', 0,
  /* 711 */ 'q', '2', '3', 0,
  /* 715 */ 's', '2', '3', 0,
  /* 719 */ 'w', '2', '3', 0,
  /* 723 */ 'x', '2', '3', 0,
  /* 727 */ 'D', '0', '_', 'D', '1', '_', 'D', '2', '_', 'D', '3', 0,
  /* 739 */ 'Q', '0', '_', 'Q', '1', '_', 'Q', '2', '_', 'Q', '3', 0,
  /* 751 */ 'b', '3', 0,
  /* 754 */ 'd', '3', 0,
  /* 757 */ 'h', '3', 0,
  /* 760 */ 'q', '3', 0,
  /* 763 */ 's', '3', 0,
  /* 766 */ 'w', '3', 0,
  /* 769 */ 'x', '3', 0,
  /* 772 */ 'D', '1', '1', '_', 'D', '1', '2', '_', 'D', '1', '3', '_', 'D', '1', '4', 0,
  /* 788 */ 'Q', '1', '1', '_', 'Q', '1', '2', '_', 'Q', '1', '3', '_', 'Q', '1', '4', 0,
  /* 804 */ 'b', '1', '4', 0,
  /* 808 */ 'd', '1', '4', 0,
  /* 812 */ 'h', '1', '4', 0,
  /* 816 */ 'q', '1', '4', 0,
  /* 820 */ 's', '1', '4', 0,
  /* 824 */ 'w', '1', '4', 0,
  /* 828 */ 'x', '1', '4', 0,
  /* 832 */ 'D', '2', '1', '_', 'D', '2', '2', '_', 'D', '2', '3', '_', 'D', '2', '4', 0,
  /* 848 */ 'Q', '2', '1', '_', 'Q', '2', '2', '_', 'Q', '2', '3', '_', 'Q', '2', '4', 0,
  /* 864 */ 'b', '2', '4', 0,
  /* 868 */ 'd', '2', '4', 0,
  /* 872 */ 'h', '2', '4', 0,
  /* 876 */ 'q', '2', '4', 0,
  /* 880 */ 's', '2', '4', 0,
  /* 884 */ 'w', '2', '4', 0,
  /* 888 */ 'x', '2', '4', 0,
  /* 892 */ 'D', '1', '_', 'D', '2', '_', 'D', '3', '_', 'D', '4', 0,
  /* 904 */ 'Q', '1', '_', 'Q', '2', '_', 'Q', '3', '_', 'Q', '4', 0,
  /* 916 */ 'b', '4', 0,
  /* 919 */ 'd', '4', 0,
  /* 922 */ 'h', '4', 0,
  /* 925 */ 'q', '4', 0,
  /* 928 */ 's', '4', 0,
  /* 931 */ 'w', '4', 0,
  /* 934 */ 'x', '4', 0,
  /* 937 */ 'D', '1', '2', '_', 'D', '1', '3', '_', 'D', '1', '4', '_', 'D', '1', '5', 0,
  /* 953 */ 'Q', '1', '2', '_', 'Q', '1', '3', '_', 'Q', '1', '4', '_', 'Q', '1', '5', 0,
  /* 969 */ 'b', '1', '5', 0,
  /* 973 */ 'd', '1', '5', 0,
  /* 977 */ 'h', '1', '5', 0,
  /* 981 */ 'q', '1', '5', 0,
  /* 985 */ 's', '1', '5', 0,
  /* 989 */ 'w', '1', '5', 0,
  /* 993 */ 'x', '1', '5', 0,
  /* 997 */ 'D', '2', '2', '_', 'D', '2', '3', '_', 'D', '2', '4', '_', 'D', '2', '5', 0,
  /* 1013 */ 'Q', '2', '2', '_', 'Q', '2', '3', '_', 'Q', '2', '4', '_', 'Q', '2', '5', 0,
  /* 1029 */ 'b', '2', '5', 0,
  /* 1033 */ 'd', '2', '5', 0,
  /* 1037 */ 'h', '2', '5', 0,
  /* 1041 */ 'q', '2', '5', 0,
  /* 1045 */ 's', '2', '5', 0,
  /* 1049 */ 'w', '2', '5', 0,
  /* 1053 */ 'x', '2', '5', 0,
  /* 1057 */ 'D', '2', '_', 'D', '3', '_', 'D', '4', '_', 'D', '5', 0,
  /* 1069 */ 'Q', '2', '_', 'Q', '3', '_', 'Q', '4', '_', 'Q', '5', 0,
  /* 1081 */ 'b', '5', 0,
  /* 1084 */ 'd', '5', 0,
  /* 1087 */ 'h', '5', 0,
  /* 1090 */ 'q', '5', 0,
  /* 1093 */ 's', '5', 0,
  /* 1096 */ 'w', '5', 0,
  /* 1099 */ 'x', '5', 0,
  /* 1102 */ 'D', '1', '3', '_', 'D', '1', '4', '_', 'D', '1', '5', '_', 'D', '1', '6', 0,
  /* 1118 */ 'Q', '1', '3', '_', 'Q', '1', '4', '_', 'Q', '1', '5', '_', 'Q', '1', '6', 0,
  /* 1134 */ 'b', '1', '6', 0,
  /* 1138 */ 'd', '1', '6', 0,
  /* 1142 */ 'h', '1', '6', 0,
  /* 1146 */ 'q', '1', '6', 0,
  /* 1150 */ 's', '1', '6', 0,
  /* 1154 */ 'w', '1', '6', 0,
  /* 1158 */ 'x', '1', '6', 0,
  /* 1162 */ 'D', '2', '3', '_', 'D', '2', '4', '_', 'D', '2', '5', '_', 'D', '2', '6', 0,
  /* 1178 */ 'Q', '2', '3', '_', 'Q', '2', '4', '_', 'Q', '2', '5', '_', 'Q', '2', '6', 0,
  /* 1194 */ 'b', '2', '6', 0,
  /* 1198 */ 'd', '2', '6', 0,
  /* 1202 */ 'h', '2', '6', 0,
  /* 1206 */ 'q', '2', '6', 0,
  /* 1210 */ 's', '2', '6', 0,
  /* 1214 */ 'w', '2', '6', 0,
  /* 1218 */ 'x', '2', '6', 0,
  /* 1222 */ 'D', '3', '_', 'D', '4', '_', 'D', '5', '_', 'D', '6', 0,
  /* 1234 */ 'Q', '3', '_', 'Q', '4', '_', 'Q', '5', '_', 'Q', '6', 0,
  /* 1246 */ 'b', '6', 0,
  /* 1249 */ 'd', '6', 0,
  /* 1252 */ 'h', '6', 0,
  /* 1255 */ 'q', '6', 0,
  /* 1258 */ 's', '6', 0,
  /* 1261 */ 'w', '6', 0,
  /* 1264 */ 'x', '6', 0,
  /* 1267 */ 'D', '1', '4', '_', 'D', '1', '5', '_', 'D', '1', '6', '_', 'D', '1', '7', 0,
  /* 1283 */ 'Q', '1', '4', '_', 'Q', '1', '5', '_', 'Q', '1', '6', '_', 'Q', '1', '7', 0,
  /* 1299 */ 'b', '1', '7', 0,
  /* 1303 */ 'd', '1', '7', 0,
  /* 1307 */ 'h', '1', '7', 0,
  /* 1311 */ 'q', '1', '7', 0,
  /* 1315 */ 's', '1', '7', 0,
  /* 1319 */ 'w', '1', '7', 0,
  /* 1323 */ 'x', '1', '7', 0,
  /* 1327 */ 'D', '2', '4', '_', 'D', '2', '5', '_', 'D', '2', '6', '_', 'D', '2', '7', 0,
  /* 1343 */ 'Q', '2', '4', '_', 'Q', '2', '5', '_', 'Q', '2', '6', '_', 'Q', '2', '7', 0,
  /* 1359 */ 'b', '2', '7', 0,
  /* 1363 */ 'd', '2', '7', 0,
  /* 1367 */ 'h', '2', '7', 0,
  /* 1371 */ 'q', '2', '7', 0,
  /* 1375 */ 's', '2', '7', 0,
  /* 1379 */ 'w', '2', '7', 0,
  /* 1383 */ 'x', '2', '7', 0,
  /* 1387 */ 'D', '4', '_', 'D', '5', '_', 'D', '6', '_', 'D', '7', 0,
  /* 1399 */ 'Q', '4', '_', 'Q', '5', '_', 'Q', '6', '_', 'Q', '7', 0,
  /* 1411 */ 'b', '7', 0,
  /* 1414 */ 'd', '7', 0,
  /* 1417 */ 'h', '7', 0,
  /* 1420 */ 'q', '7', 0,
  /* 1423 */ 's', '7', 0,
  /* 1426 */ 'w', '7', 0,
  /* 1429 */ 'x', '7', 0,
  /* 1432 */ 'D', '1', '5', '_', 'D', '1', '6', '_', 'D', '1', '7', '_', 'D', '1', '8', 0,
  /* 1448 */ 'Q', '1', '5', '_', 'Q', '1', '6', '_', 'Q', '1', '7', '_', 'Q', '1', '8', 0,
  /* 1464 */ 'b', '1', '8', 0,
  /* 1468 */ 'd', '1', '8', 0,
  /* 1472 */ 'h', '1', '8', 0,
  /* 1476 */ 'q', '1', '8', 0,
  /* 1480 */ 's', '1', '8', 0,
  /* 1484 */ 'w', '1', '8', 0,
  /* 1488 */ 'x', '1', '8', 0,
  /* 1492 */ 'D', '2', '5', '_', 'D', '2', '6', '_', 'D', '2', '7', '_', 'D', '2', '8', 0,
  /* 1508 */ 'Q', '2', '5', '_', 'Q', '2', '6', '_', 'Q', '2', '7', '_', 'Q', '2', '8', 0,
  /* 1524 */ 'b', '2', '8', 0,
  /* 1528 */ 'd', '2', '8', 0,
  /* 1532 */ 'h', '2', '8', 0,
  /* 1536 */ 'q', '2', '8', 0,
  /* 1540 */ 's', '2', '8', 0,
  /* 1544 */ 'w', '2', '8', 0,
  /* 1548 */ 'x', '2', '8', 0,
  /* 1552 */ 'D', '5', '_', 'D', '6', '_', 'D', '7', '_', 'D', '8', 0,
  /* 1564 */ 'Q', '5', '_', 'Q', '6', '_', 'Q', '7', '_', 'Q', '8', 0,
  /* 1576 */ 'b', '8', 0,
  /* 1579 */ 'd', '8', 0,
  /* 1582 */ 'h', '8', 0,
  /* 1585 */ 'q', '8', 0,
  /* 1588 */ 's', '8', 0,
  /* 1591 */ 'w', '8', 0,
  /* 1594 */ 'x', '8', 0,
  /* 1597 */ 'D', '1', '6', '_', 'D', '1', '7', '_', 'D', '1', '8', '_', 'D', '1', '9', 0,
  /* 1613 */ 'Q', '1', '6', '_', 'Q', '1', '7', '_', 'Q', '1', '8', '_', 'Q', '1', '9', 0,
  /* 1629 */ 'b', '1', '9', 0,
  /* 1633 */ 'd', '1', '9', 0,
  /* 1637 */ 'h', '1', '9', 0,
  /* 1641 */ 'q', '1', '9', 0,
  /* 1645 */ 's', '1', '9', 0,
  /* 1649 */ 'w', '1', '9', 0,
  /* 1653 */ 'x', '1', '9', 0,
  /* 1657 */ 'D', '2', '6', '_', 'D', '2', '7', '_', 'D', '2', '8', '_', 'D', '2', '9', 0,
  /* 1673 */ 'Q', '2', '6', '_', 'Q', '2', '7', '_', 'Q', '2', '8', '_', 'Q', '2', '9', 0,
  /* 1689 */ 'b', '2', '9', 0,
  /* 1693 */ 'd', '2', '9', 0,
  /* 1697 */ 'h', '2', '9', 0,
  /* 1701 */ 'q', '2', '9', 0,
  /* 1705 */ 's', '2', '9', 0,
  /* 1709 */ 'w', '2', '9', 0,
  /* 1713 */ 'x', '2', '9', 0,
  /* 1717 */ 'D', '6', '_', 'D', '7', '_', 'D', '8', '_', 'D', '9', 0,
  /* 1729 */ 'Q', '6', '_', 'Q', '7', '_', 'Q', '8', '_', 'Q', '9', 0,
  /* 1741 */ 'b', '9', 0,
  /* 1744 */ 'd', '9', 0,
  /* 1747 */ 'h', '9', 0,
  /* 1750 */ 'q', '9', 0,
  /* 1753 */ 's', '9', 0,
  /* 1756 */ 'w', '9', 0,
  /* 1759 */ 'x', '9', 0,
  /* 1762 */ 'w', 's', 'p', 0,
  /* 1766 */ 'w', 'z', 'r', 0,
  /* 1770 */ 'x', 'z', 'r', 0,
  /* 1774 */ 'n', 'z', 'c', 'v', 0,
  };

  static const uint16_t RegAsmOffsetNoRegAltName[] = {
    1713, 170, 1774, 1763, 1762, 1766, 1770, 204, 421, 586, 751, 916, 1081, 1246, 
    1411, 1576, 1741, 26, 253, 472, 639, 804, 969, 1134, 1299, 1464, 1629, 86, 
    313, 532, 699, 864, 1029, 1194, 1359, 1524, 1689, 146, 373, 207, 424, 589, 
    754, 919, 1084, 1249, 1414, 1579, 1744, 30, 257, 476, 643, 808, 973, 1138, 
    1303, 1468, 1633, 90, 317, 536, 703, 868, 1033, 1198, 1363, 1528, 1693, 150, 
    377, 210, 427, 592, 757, 922, 1087, 1252, 1417, 1582, 1747, 34, 261, 480, 
    647, 812, 977, 1142, 1307, 1472, 1637, 94, 321, 540, 707, 872, 1037, 1202, 
    1367, 1532, 1697, 154, 381, 213, 430, 595, 760, 925, 1090, 1255, 1420, 1585, 
    1750, 38, 265, 484, 651, 816, 981, 1146, 1311, 1476, 1641, 98, 325, 544, 
    711, 876, 1041, 1206, 1371, 1536, 1701, 158, 385, 216, 433, 598, 763, 928, 
    1093, 1258, 1423, 1588, 1753, 42, 269, 488, 655, 820, 985, 1150, 1315, 1480, 
    1645, 102, 329, 548, 715, 880, 1045, 1210, 1375, 1540, 1705, 162, 389, 219, 
    436, 601, 766, 931, 1096, 1261, 1426, 1591, 1756, 46, 273, 492, 659, 824, 
    989, 1154, 1319, 1484, 1649, 106, 333, 552, 719, 884, 1049, 1214, 1379, 1544, 
    1709, 166, 222, 439, 604, 769, 934, 1099, 1264, 1429, 1594, 1759, 50, 277, 
    496, 663, 828, 993, 1158, 1323, 1488, 1653, 110, 337, 556, 723, 888, 1053, 
    1218, 1383, 1548, 401, 567, 733, 898, 1063, 1228, 1393, 1558, 1723, 6, 231, 
    449, 615, 780, 945, 1110, 1275, 1440, 1605, 62, 289, 508, 675, 840, 1005, 
    1170, 1335, 1500, 1665, 122, 349, 182, 727, 892, 1057, 1222, 1387, 1552, 1717, 
    0, 225, 442, 607, 772, 937, 1102, 1267, 1432, 1597, 54, 281, 500, 667, 
    832, 997, 1162, 1327, 1492, 1657, 114, 341, 174, 393, 560, 564, 730, 895, 
    1060, 1225, 1390, 1555, 1720, 3, 228, 445, 611, 776, 941, 1106, 1271, 1436, 
    1601, 58, 285, 504, 671, 836, 1001, 1166, 1331, 1496, 1661, 118, 345, 178, 
    397, 415, 580, 745, 910, 1075, 1240, 1405, 1570, 1735, 19, 245, 464, 631, 
    796, 961, 1126, 1291, 1456, 1621, 78, 305, 524, 691, 856, 1021, 1186, 1351, 
    1516, 1681, 138, 365, 197, 739, 904, 1069, 1234, 1399, 1564, 1729, 13, 239, 
    457, 623, 788, 953, 1118, 1283, 1448, 1613, 70, 297, 516, 683, 848, 1013, 
    1178, 1343, 1508, 1673, 130, 357, 189, 407, 573, 577, 742, 907, 1072, 1237, 
    1402, 1567, 1732, 16, 242, 460, 627, 792, 957, 1122, 1287, 1452, 1617, 74, 
    301, 520, 687, 852, 1017, 1182, 1347, 1512, 1677, 134, 361, 193, 411, 
  };

  static const char AsmStrsvreg[] = {
  /* 0 */ 'v', '1', '0', 0,
  /* 4 */ 'v', '2', '0', 0,
  /* 8 */ 'v', '3', '0', 0,
  /* 12 */ 'v', '0', 0,
  /* 15 */ 'v', '1', '1', 0,
  /* 19 */ 'v', '2', '1', 0,
  /* 23 */ 'v', '3', '1', 0,
  /* 27 */ 'v', '1', 0,
  /* 30 */ 'v', '1', '2', 0,
  /* 34 */ 'v', '2', '2', 0,
  /* 38 */ 'v', '2', 0,
  /* 41 */ 'v', '1', '3', 0,
  /* 45 */ 'v', '2', '3', 0,
  /* 49 */ 'v', '3', 0,
  /* 52 */ 'v', '1', '4', 0,
  /* 56 */ 'v', '2', '4', 0,
  /* 60 */ 'v', '4', 0,
  /* 63 */ 'v', '1', '5', 0,
  /* 67 */ 'v', '2', '5', 0,
  /* 71 */ 'v', '5', 0,
  /* 74 */ 'v', '1', '6', 0,
  /* 78 */ 'v', '2', '6', 0,
  /* 82 */ 'v', '6', 0,
  /* 85 */ 'v', '1', '7', 0,
  /* 89 */ 'v', '2', '7', 0,
  /* 93 */ 'v', '7', 0,
  /* 96 */ 'v', '1', '8', 0,
  /* 100 */ 'v', '2', '8', 0,
  /* 104 */ 'v', '8', 0,
  /* 107 */ 'v', '1', '9', 0,
  /* 111 */ 'v', '2', '9', 0,
  /* 115 */ 'v', '9', 0,
  };

  static const uint16_t RegAsmOffsetvreg[] = {
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 12, 27, 38, 
    49, 60, 71, 82, 93, 104, 115, 0, 15, 30, 41, 52, 63, 74, 
    85, 96, 107, 4, 19, 34, 45, 56, 67, 78, 89, 100, 111, 8, 
    23, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 12, 27, 38, 49, 60, 71, 82, 93, 104, 
    115, 0, 15, 30, 41, 52, 63, 74, 85, 96, 107, 4, 19, 34, 
    45, 56, 67, 78, 89, 100, 111, 8, 23, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 
    3, 3, 3, 12, 27, 38, 49, 60, 71, 82, 93, 104, 115, 0, 
    15, 30, 41, 52, 63, 74, 85, 96, 107, 4, 19, 34, 45, 56, 
    67, 78, 89, 100, 111, 8, 23, 12, 27, 38, 49, 60, 71, 82, 
    93, 104, 115, 0, 15, 30, 41, 52, 63, 74, 85, 96, 107, 4, 
    19, 34, 45, 56, 67, 78, 89, 100, 111, 8, 23, 12, 27, 38, 
    49, 60, 71, 82, 93, 104, 115, 0, 15, 30, 41, 52, 63, 74, 
    85, 96, 107, 4, 19, 34, 45, 56, 67, 78, 89, 100, 111, 8, 
    23, 12, 27, 38, 49, 60, 71, 82, 93, 104, 115, 0, 15, 30, 
    41, 52, 63, 74, 85, 96, 107, 4, 19, 34, 45, 56, 67, 78, 
    89, 100, 111, 8, 23, 12, 27, 38, 49, 60, 71, 82, 93, 104, 
    115, 0, 15, 30, 41, 52, 63, 74, 85, 96, 107, 4, 19, 34, 
    45, 56, 67, 78, 89, 100, 111, 8, 23, 12, 27, 38, 49, 60, 
    71, 82, 93, 104, 115, 0, 15, 30, 41, 52, 63, 74, 85, 96, 
    107, 4, 19, 34, 45, 56, 67, 78, 89, 100, 111, 8, 23, 
  };

  const uint16_t *RegAsmOffset;
  const char *AsmStrs;

  switch(AltIdx) {
  default: // llvm_unreachable("Invalid register alt name index!");
  case AArch64_NoRegAltName:
    AsmStrs = AsmStrsNoRegAltName;
    RegAsmOffset = RegAsmOffsetNoRegAltName;
    break;
  case AArch64_vreg:
    AsmStrs = AsmStrsvreg;
    RegAsmOffset = RegAsmOffsetvreg;
    break;
  }
  //int i;
  //for (i = 0; i < sizeof(RegAsmOffsetNoRegAltName)/2; i++)
  //     printf("%s = %u\n", AsmStrsNoRegAltName+RegAsmOffsetNoRegAltName[i], i + 1);
  //printf("*************************\n");
  //for (i = 0; i < sizeof(RegAsmOffsetvreg)/2; i++)
  //     printf("%s = %u\n", AsmStrsvreg+RegAsmOffsetvreg[i], i + 1);
  //printf("-------------------------\n");
  return AsmStrs+RegAsmOffset[RegNo-1];
#else
  return NULL;
#endif
}

#ifdef PRINT_ALIAS_INSTR
#undef PRINT_ALIAS_INSTR

static void printCustomAliasOperand(MCInst *MI, unsigned OpIdx,
  unsigned PrintMethodIdx, SStream *OS, MCRegisterInfo *MRI)
{
  // printf(">>>> Method: %u, opIdx: %x\n", PrintMethodIdx, OpIdx);
  switch (PrintMethodIdx) {
  default:
    // llvm_unreachable("Unknown PrintMethod kind");
    break;
  case 0:
    printAddSubImm(MI, OpIdx, OS);
    break;
  case 1:
    printShifter(MI, OpIdx, OS);
    break;
  case 2:
    printArithExtend(MI, OpIdx, OS);
    break;
  case 3:
    printLogicalImm32(MI, OpIdx, OS);
    break;
  case 4:
    printLogicalImm64(MI, OpIdx, OS);
    break;
  case 5:
    printVRegOperand(MI, OpIdx, OS);
    break;
  case 6:
    printHexImm(MI, OpIdx, OS);
    break;
  case 7:
    printInverseCondCode(MI, OpIdx, OS);
    break;
  case 8:
    printVectorIndex(MI, OpIdx, OS);
    break;
  case 9:
    printTypedVectorList(MI, OpIdx, OS, 16, 'b', MRI);
    break;
  case 10:
    printTypedVectorList(MI, OpIdx, OS, 1, 'd', MRI);
    break;
  case 11:
    printTypedVectorList(MI, OpIdx, OS, 2, 'd', MRI);
    break;
  case 12:
    printTypedVectorList(MI, OpIdx, OS, 2, 's', MRI);
    break;
  case 13:
    printTypedVectorList(MI, OpIdx, OS, 4, 'h', MRI);
    break;
  case 14:
    printTypedVectorList(MI, OpIdx, OS, 4, 's', MRI);
    break;
  case 15:
    printTypedVectorList(MI, OpIdx, OS, 8, 'b', MRI);
    break;
  case 16:
    printTypedVectorList(MI, OpIdx, OS, 8, 'h', MRI);
    break;
  case 17:
    printTypedVectorList(MI, OpIdx, OS, 0, 'h', MRI);
    break;
  case 18:
    printTypedVectorList(MI, OpIdx, OS, 0, 's', MRI);
    break;
  case 19:
    printTypedVectorList(MI, OpIdx, OS, 0, 'd', MRI);
    break;
  case 20:
    printTypedVectorList(MI, OpIdx, OS, 0, 'b', MRI);
    break;
  case 21:
    printPrefetchOp(MI, OpIdx, OS);
    break;
  case 22:
    printSysCROperand(MI, OpIdx, OS);
    break;
  }
}

static bool AArch64InstPrinterValidateMCOperand(
       MCOperand *MCOp, unsigned PredicateIndex)
{
  switch (PredicateIndex) {
  default:
    // llvm_unreachable("Unknown MCOperandPredicate kind");
  case 1: {
    return (MCOperand_isImm(MCOp) &&
           MCOperand_getImm(MCOp) != ARM64_CC_AL &&
           MCOperand_getImm(MCOp) != ARM64_CC_NV);
    }
  }
}

static char *printAliasInstr(MCInst *MI, SStream *OS, void *info)
{
  #define GETREGCLASS_CONTAIN(_class, _reg) MCRegisterClass_contains(MCRegisterInfo_getRegClass(MRI, _class), MCOperand_getReg(MCInst_getOperand(MI, _reg)))
  const char *AsmString;
  char *tmp, *AsmMnem, *AsmOps, *c;
  int OpIdx, PrintMethodIdx;
  MCRegisterInfo *MRI = (MCRegisterInfo *)info;
  switch (MCInst_getOpcode(MI)) {
  default: return NULL;
  case AArch64_ADDSWri:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_WZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32spRegClassID, 1)) {
      // (ADDSWri WZR, GPR32sp:$src, addsub_shifted_imm32:$imm)
      AsmString = "cmn $\x02, $\xFF\x03\x01";
      break;
    }
    return NULL;
  case AArch64_ADDSWrs:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_WZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (ADDSWrs WZR, GPR32:$src1, GPR32:$src2, 0)
      AsmString = "cmn $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_WZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2)) {
      // (ADDSWrs WZR, GPR32:$src1, GPR32:$src2, arith_shift32:$sh)
      AsmString = "cmn $\x02, $\x03$\xFF\x04\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (ADDSWrs GPR32:$dst, GPR32:$src1, GPR32:$src2, 0)
      AsmString = "adds $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_ADDSWrx:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_WZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32sponlyRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 16) {
      // (ADDSWrx WZR, GPR32sponly:$src1, GPR32:$src2, 16)
      AsmString = "cmn $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_WZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2)) {
      // (ADDSWrx WZR, GPR32sp:$src1, GPR32:$src2, arith_extend:$sh)
      AsmString = "cmn $\x02, $\x03$\xFF\x04\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32sponlyRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 16) {
      // (ADDSWrx GPR32:$dst, GPR32sponly:$src1, GPR32:$src2, 16)
      AsmString = "adds $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_ADDSXri:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_XZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1)) {
      // (ADDSXri XZR, GPR64sp:$src, addsub_shifted_imm64:$imm)
      AsmString = "cmn $\x02, $\xFF\x03\x01";
      break;
    }
    return NULL;
  case AArch64_ADDSXrs:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_XZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (ADDSXrs XZR, GPR64:$src1, GPR64:$src2, 0)
      AsmString = "cmn $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_XZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2)) {
      // (ADDSXrs XZR, GPR64:$src1, GPR64:$src2, arith_shift64:$sh)
      AsmString = "cmn $\x02, $\x03$\xFF\x04\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (ADDSXrs GPR64:$dst, GPR64:$src1, GPR64:$src2, 0)
      AsmString = "adds $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_ADDSXrx:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_XZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2)) {
      // (ADDSXrx XZR, GPR64sp:$src1, GPR32:$src2, arith_extend:$sh)
      AsmString = "cmn $\x02, $\x03$\xFF\x04\x03";
      break;
    }
    return NULL;
  case AArch64_ADDSXrx64:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_XZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64sponlyRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 24) {
      // (ADDSXrx64 XZR, GPR64sponly:$src1, GPR64:$src2, 24)
      AsmString = "cmn $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_XZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2)) {
      // (ADDSXrx64 XZR, GPR64sp:$src1, GPR64:$src2, arith_extendlsl64:$sh)
      AsmString = "cmn $\x02, $\x03$\xFF\x04\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64sponlyRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 24) {
      // (ADDSXrx64 GPR64:$dst, GPR64sponly:$src1, GPR64:$src2, 24)
      AsmString = "adds $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_ADDWri:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32sponlyRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (ADDWri GPR32sponly:$dst, GPR32sp:$src, 0, 0)
      AsmString = "mov $\x01, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32sponlyRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (ADDWri GPR32sp:$dst, GPR32sponly:$src, 0, 0)
      AsmString = "mov $\x01, $\x02";
      break;
    }
    return NULL;
  case AArch64_ADDWrs:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (ADDWrs GPR32:$dst, GPR32:$src1, GPR32:$src2, 0)
      AsmString = "add $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_ADDWrx:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32sponlyRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 16) {
      // (ADDWrx GPR32sponly:$dst, GPR32sp:$src1, GPR32:$src2, 16)
      AsmString = "add $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32sponlyRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 16) {
      // (ADDWrx GPR32sp:$dst, GPR32sponly:$src1, GPR32:$src2, 16)
      AsmString = "add $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_ADDXri:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64sponlyRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (ADDXri GPR64sponly:$dst, GPR64sp:$src, 0, 0)
      AsmString = "mov $\x01, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64sponlyRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (ADDXri GPR64sp:$dst, GPR64sponly:$src, 0, 0)
      AsmString = "mov $\x01, $\x02";
      break;
    }
    return NULL;
  case AArch64_ADDXrs:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (ADDXrs GPR64:$dst, GPR64:$src1, GPR64:$src2, 0)
      AsmString = "add $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_ADDXrx64:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64sponlyRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 24) {
      // (ADDXrx64 GPR64sponly:$dst, GPR64sp:$src1, GPR64:$src2, 24)
      AsmString = "add $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64sponlyRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 24) {
      // (ADDXrx64 GPR64sp:$dst, GPR64sponly:$src1, GPR64:$src2, 24)
      AsmString = "add $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_ANDSWri:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_WZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1)) {
      // (ANDSWri WZR, GPR32:$src1, logical_imm32:$src2)
      AsmString = "tst $\x02, $\xFF\x03\x04";
      break;
    }
    return NULL;
  case AArch64_ANDSWrs:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_WZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (ANDSWrs WZR, GPR32:$src1, GPR32:$src2, 0)
      AsmString = "tst $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_WZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2)) {
      // (ANDSWrs WZR, GPR32:$src1, GPR32:$src2, logical_shift32:$sh)
      AsmString = "tst $\x02, $\x03$\xFF\x04\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (ANDSWrs GPR32:$dst, GPR32:$src1, GPR32:$src2, 0)
      AsmString = "ands $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_ANDSXri:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_XZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1)) {
      // (ANDSXri XZR, GPR64:$src1, logical_imm64:$src2)
      AsmString = "tst $\x02, $\xFF\x03\x05";
      break;
    }
    return NULL;
  case AArch64_ANDSXrs:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_XZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (ANDSXrs XZR, GPR64:$src1, GPR64:$src2, 0)
      AsmString = "tst $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_XZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2)) {
      // (ANDSXrs XZR, GPR64:$src1, GPR64:$src2, logical_shift64:$sh)
      AsmString = "tst $\x02, $\x03$\xFF\x04\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (ANDSXrs GPR64:$dst, GPR64:$src1, GPR64:$src2, 0)
      AsmString = "ands $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_ANDWrs:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (ANDWrs GPR32:$dst, GPR32:$src1, GPR32:$src2, 0)
      AsmString = "and $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_ANDXrs:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (ANDXrs GPR64:$dst, GPR64:$src1, GPR64:$src2, 0)
      AsmString = "and $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_BICSWrs:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (BICSWrs GPR32:$dst, GPR32:$src1, GPR32:$src2, 0)
      AsmString = "bics $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_BICSXrs:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (BICSXrs GPR64:$dst, GPR64:$src1, GPR64:$src2, 0)
      AsmString = "bics $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_BICWrs:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (BICWrs GPR32:$dst, GPR32:$src1, GPR32:$src2, 0)
      AsmString = "bic $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_BICXrs:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (BICXrs GPR64:$dst, GPR64:$src1, GPR64:$src2, 0)
      AsmString = "bic $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_BICv2i32:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (BICv2i32 V64:$Vd, imm0_255:$imm, 0)
      AsmString = "bic $\xFF\x01\x06.2s, $\xFF\x02\x07";
      break;
    }
    return NULL;
  case AArch64_BICv4i16:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (BICv4i16 V64:$Vd, imm0_255:$imm, 0)
      AsmString = "bic $\xFF\x01\x06.4h, $\xFF\x02\x07";
      break;
    }
    return NULL;
  case AArch64_BICv4i32:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (BICv4i32 V128:$Vd, imm0_255:$imm, 0)
      AsmString = "bic $\xFF\x01\x06.4s, $\xFF\x02\x07";
      break;
    }
    return NULL;
  case AArch64_BICv8i16:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (BICv8i16 V128:$Vd, imm0_255:$imm, 0)
      AsmString = "bic $\xFF\x01\x06.8h, $\xFF\x02\x07";
      break;
    }
    return NULL;
  case AArch64_CLREX:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15) {
      // (CLREX 15)
      AsmString = "clrex";
      break;
    }
    return NULL;
  case AArch64_CSINCWr:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == AArch64_WZR &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_WZR &&
        AArch64InstPrinterValidateMCOperand(MCInst_getOperand(MI, 3), 1)) {
      // (CSINCWr GPR32:$dst, WZR, WZR, inv_ccode:$cc)
      AsmString = "cset $\x01, $\xFF\x04\x08";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1)) &&
        AArch64InstPrinterValidateMCOperand(MCInst_getOperand(MI, 3), 1)) {
      // (CSINCWr GPR32:$dst, GPR32:$src, GPR32:$src, inv_ccode:$cc)
      AsmString = "cinc $\x01, $\x02, $\xFF\x04\x08";
      break;
    }
    return NULL;
  case AArch64_CSINCXr:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == AArch64_XZR &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR &&
        AArch64InstPrinterValidateMCOperand(MCInst_getOperand(MI, 3), 1)) {
      // (CSINCXr GPR64:$dst, XZR, XZR, inv_ccode:$cc)
      AsmString = "cset $\x01, $\xFF\x04\x08";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1)) &&
        AArch64InstPrinterValidateMCOperand(MCInst_getOperand(MI, 3), 1)) {
      // (CSINCXr GPR64:$dst, GPR64:$src, GPR64:$src, inv_ccode:$cc)
      AsmString = "cinc $\x01, $\x02, $\xFF\x04\x08";
      break;
    }
    return NULL;
  case AArch64_CSINVWr:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == AArch64_WZR &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_WZR &&
        AArch64InstPrinterValidateMCOperand(MCInst_getOperand(MI, 3), 1)) {
      // (CSINVWr GPR32:$dst, WZR, WZR, inv_ccode:$cc)
      AsmString = "csetm $\x01, $\xFF\x04\x08";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1)) &&
        AArch64InstPrinterValidateMCOperand(MCInst_getOperand(MI, 3), 1)) {
      // (CSINVWr GPR32:$dst, GPR32:$src, GPR32:$src, inv_ccode:$cc)
      AsmString = "cinv $\x01, $\x02, $\xFF\x04\x08";
      break;
    }
    return NULL;
  case AArch64_CSINVXr:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == AArch64_XZR &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR &&
        AArch64InstPrinterValidateMCOperand(MCInst_getOperand(MI, 3), 1)) {
      // (CSINVXr GPR64:$dst, XZR, XZR, inv_ccode:$cc)
      AsmString = "csetm $\x01, $\xFF\x04\x08";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1)) &&
        AArch64InstPrinterValidateMCOperand(MCInst_getOperand(MI, 3), 1)) {
      // (CSINVXr GPR64:$dst, GPR64:$src, GPR64:$src, inv_ccode:$cc)
      AsmString = "cinv $\x01, $\x02, $\xFF\x04\x08";
      break;
    }
    return NULL;
  case AArch64_CSNEGWr:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1)) &&
        AArch64InstPrinterValidateMCOperand(MCInst_getOperand(MI, 3), 1)) {
      // (CSNEGWr GPR32:$dst, GPR32:$src, GPR32:$src, inv_ccode:$cc)
      AsmString = "cneg $\x01, $\x02, $\xFF\x04\x08";
      break;
    }
    return NULL;
  case AArch64_CSNEGXr:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1)) &&
        AArch64InstPrinterValidateMCOperand(MCInst_getOperand(MI, 3), 1)) {
      // (CSNEGXr GPR64:$dst, GPR64:$src, GPR64:$src, inv_ccode:$cc)
      AsmString = "cneg $\x01, $\x02, $\xFF\x04\x08";
      break;
    }
    return NULL;
  case AArch64_DCPS1:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (DCPS1 0)
      AsmString = "dcps1";
      break;
    }
    return NULL;
  case AArch64_DCPS2:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (DCPS2 0)
      AsmString = "dcps2";
      break;
    }
    return NULL;
  case AArch64_DCPS3:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (DCPS3 0)
      AsmString = "dcps3";
      break;
    }
    return NULL;
  case AArch64_EONWrs:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (EONWrs GPR32:$dst, GPR32:$src1, GPR32:$src2, 0)
      AsmString = "eon $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_EONXrs:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (EONXrs GPR64:$dst, GPR64:$src1, GPR64:$src2, 0)
      AsmString = "eon $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_EORWrs:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (EORWrs GPR32:$dst, GPR32:$src1, GPR32:$src2, 0)
      AsmString = "eor $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_EORXrs:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (EORXrs GPR64:$dst, GPR64:$src1, GPR64:$src2, 0)
      AsmString = "eor $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_EXTRWrri:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1))) {
      // (EXTRWrri GPR32:$dst, GPR32:$src, GPR32:$src, imm0_31:$shift)
      AsmString = "ror $\x01, $\x02, $\x04";
      break;
    }
    return NULL;
  case AArch64_EXTRXrri:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1))) {
      // (EXTRXrri GPR64:$dst, GPR64:$src, GPR64:$src, imm0_63:$shift)
      AsmString = "ror $\x01, $\x02, $\x04";
      break;
    }
    return NULL;
  case AArch64_HINT:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (HINT { 0, 0, 0 })
      AsmString = "nop";
      break;
    }
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 1) {
      // (HINT { 0, 0, 1 })
      AsmString = "yield";
      break;
    }
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2) {
      // (HINT { 0, 1, 0 })
      AsmString = "wfe";
      break;
    }
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 3) {
      // (HINT { 0, 1, 1 })
      AsmString = "wfi";
      break;
    }
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 4) {
      // (HINT { 1, 0, 0 })
      AsmString = "sev";
      break;
    }
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 5) {
      // (HINT { 1, 0, 1 })
      AsmString = "sevl";
      break;
    }
    return NULL;
  case AArch64_INSvi16gpr:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2)) {
      // (INSvi16gpr V128:$dst, VectorIndexH:$idx, GPR32:$src)
      AsmString = "mov	$\xFF\x01\x06.h$\xFF\x02\x09, $\x03";
      break;
    }
    return NULL;
  case AArch64_INSvi16lane:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 2)) {
      // (INSvi16lane V128:$dst, VectorIndexH:$idx, V128:$src, VectorIndexH:$idx2)
      AsmString = "mov	$\xFF\x01\x06.h$\xFF\x02\x09, $\xFF\x03\x06.h$\xFF\x04\x09";
      break;
    }
    return NULL;
  case AArch64_INSvi32gpr:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2)) {
      // (INSvi32gpr V128:$dst, VectorIndexS:$idx, GPR32:$src)
      AsmString = "mov	$\xFF\x01\x06.s$\xFF\x02\x09, $\x03";
      break;
    }
    return NULL;
  case AArch64_INSvi32lane:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 2)) {
      // (INSvi32lane V128:$dst, VectorIndexS:$idx, V128:$src, VectorIndexS:$idx2)
      AsmString = "mov	$\xFF\x01\x06.s$\xFF\x02\x09, $\xFF\x03\x06.s$\xFF\x04\x09";
      break;
    }
    return NULL;
  case AArch64_INSvi64gpr:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2)) {
      // (INSvi64gpr V128:$dst, VectorIndexD:$idx, GPR64:$src)
      AsmString = "mov	$\xFF\x01\x06.d$\xFF\x02\x09, $\x03";
      break;
    }
    return NULL;
  case AArch64_INSvi64lane:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 2)) {
      // (INSvi64lane V128:$dst, VectorIndexD:$idx, V128:$src, VectorIndexD:$idx2)
      AsmString = "mov	$\xFF\x01\x06.d$\xFF\x02\x09, $\xFF\x03\x06.d$\xFF\x04\x09";
      break;
    }
    return NULL;
  case AArch64_INSvi8gpr:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2)) {
      // (INSvi8gpr V128:$dst, VectorIndexB:$idx, GPR32:$src)
      AsmString = "mov	$\xFF\x01\x06.b$\xFF\x02\x09, $\x03";
      break;
    }
    return NULL;
  case AArch64_INSvi8lane:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 2)) {
      // (INSvi8lane V128:$dst, VectorIndexB:$idx, V128:$src, VectorIndexB:$idx2)
      AsmString = "mov	$\xFF\x01\x06.b$\xFF\x02\x09, $\xFF\x03\x06.b$\xFF\x04\x09";
      break;
    }
    return NULL;
  case AArch64_ISB:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15) {
      // (ISB 15)
      AsmString = "isb";
      break;
    }
    return NULL;
  case AArch64_LD1Fourv16b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Fourv16b_POST GPR64sp:$Rn, VecListFour16b:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x0A, [$\x01], #64";
      break;
    }
    return NULL;
  case AArch64_LD1Fourv1d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Fourv1d_POST GPR64sp:$Rn, VecListFour1d:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x0B, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_LD1Fourv2d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Fourv2d_POST GPR64sp:$Rn, VecListFour2d:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x0C, [$\x01], #64";
      break;
    }
    return NULL;
  case AArch64_LD1Fourv2s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Fourv2s_POST GPR64sp:$Rn, VecListFour2s:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x0D, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_LD1Fourv4h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Fourv4h_POST GPR64sp:$Rn, VecListFour4h:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x0E, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_LD1Fourv4s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Fourv4s_POST GPR64sp:$Rn, VecListFour4s:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x0F, [$\x01], #64";
      break;
    }
    return NULL;
  case AArch64_LD1Fourv8b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Fourv8b_POST GPR64sp:$Rn, VecListFour8b:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x10, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_LD1Fourv8h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Fourv8h_POST GPR64sp:$Rn, VecListFour8h:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x11, [$\x01], #64";
      break;
    }
    return NULL;
  case AArch64_LD1Onev16b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Onev16b_POST GPR64sp:$Rn, VecListOne16b:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x0A, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_LD1Onev1d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Onev1d_POST GPR64sp:$Rn, VecListOne1d:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x0B, [$\x01], #8";
      break;
    }
    return NULL;
  case AArch64_LD1Onev2d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Onev2d_POST GPR64sp:$Rn, VecListOne2d:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x0C, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_LD1Onev2s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Onev2s_POST GPR64sp:$Rn, VecListOne2s:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x0D, [$\x01], #8";
      break;
    }
    return NULL;
  case AArch64_LD1Onev4h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Onev4h_POST GPR64sp:$Rn, VecListOne4h:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x0E, [$\x01], #8";
      break;
    }
    return NULL;
  case AArch64_LD1Onev4s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Onev4s_POST GPR64sp:$Rn, VecListOne4s:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x0F, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_LD1Onev8b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Onev8b_POST GPR64sp:$Rn, VecListOne8b:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x10, [$\x01], #8";
      break;
    }
    return NULL;
  case AArch64_LD1Onev8h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Onev8h_POST GPR64sp:$Rn, VecListOne8h:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x11, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_LD1Rv16b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Rv16b_POST GPR64sp:$Rn, VecListOne16b:$Vt, XZR)
      AsmString = "ld1r	$\xFF\x02\x0A, [$\x01], #1";
      break;
    }
    return NULL;
  case AArch64_LD1Rv1d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Rv1d_POST GPR64sp:$Rn, VecListOne1d:$Vt, XZR)
      AsmString = "ld1r	$\xFF\x02\x0B, [$\x01], #8";
      break;
    }
    return NULL;
  case AArch64_LD1Rv2d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Rv2d_POST GPR64sp:$Rn, VecListOne2d:$Vt, XZR)
      AsmString = "ld1r	$\xFF\x02\x0C, [$\x01], #8";
      break;
    }
    return NULL;
  case AArch64_LD1Rv2s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Rv2s_POST GPR64sp:$Rn, VecListOne2s:$Vt, XZR)
      AsmString = "ld1r	$\xFF\x02\x0D, [$\x01], #4";
      break;
    }
    return NULL;
  case AArch64_LD1Rv4h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Rv4h_POST GPR64sp:$Rn, VecListOne4h:$Vt, XZR)
      AsmString = "ld1r	$\xFF\x02\x0E, [$\x01], #2";
      break;
    }
    return NULL;
  case AArch64_LD1Rv4s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Rv4s_POST GPR64sp:$Rn, VecListOne4s:$Vt, XZR)
      AsmString = "ld1r	$\xFF\x02\x0F, [$\x01], #4";
      break;
    }
    return NULL;
  case AArch64_LD1Rv8b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Rv8b_POST GPR64sp:$Rn, VecListOne8b:$Vt, XZR)
      AsmString = "ld1r	$\xFF\x02\x10, [$\x01], #1";
      break;
    }
    return NULL;
  case AArch64_LD1Rv8h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Rv8h_POST GPR64sp:$Rn, VecListOne8h:$Vt, XZR)
      AsmString = "ld1r	$\xFF\x02\x11, [$\x01], #2";
      break;
    }
    return NULL;
  case AArch64_LD1Threev16b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Threev16b_POST GPR64sp:$Rn, VecListThree16b:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x0A, [$\x01], #48";
      break;
    }
    return NULL;
  case AArch64_LD1Threev1d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Threev1d_POST GPR64sp:$Rn, VecListThree1d:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x0B, [$\x01], #24";
      break;
    }
    return NULL;
  case AArch64_LD1Threev2d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Threev2d_POST GPR64sp:$Rn, VecListThree2d:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x0C, [$\x01], #48";
      break;
    }
    return NULL;
  case AArch64_LD1Threev2s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Threev2s_POST GPR64sp:$Rn, VecListThree2s:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x0D, [$\x01], #24";
      break;
    }
    return NULL;
  case AArch64_LD1Threev4h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Threev4h_POST GPR64sp:$Rn, VecListThree4h:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x0E, [$\x01], #24";
      break;
    }
    return NULL;
  case AArch64_LD1Threev4s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Threev4s_POST GPR64sp:$Rn, VecListThree4s:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x0F, [$\x01], #48";
      break;
    }
    return NULL;
  case AArch64_LD1Threev8b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Threev8b_POST GPR64sp:$Rn, VecListThree8b:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x10, [$\x01], #24";
      break;
    }
    return NULL;
  case AArch64_LD1Threev8h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Threev8h_POST GPR64sp:$Rn, VecListThree8h:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x11, [$\x01], #48";
      break;
    }
    return NULL;
  case AArch64_LD1Twov16b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Twov16b_POST GPR64sp:$Rn, VecListTwo16b:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x0A, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_LD1Twov1d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Twov1d_POST GPR64sp:$Rn, VecListTwo1d:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x0B, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_LD1Twov2d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Twov2d_POST GPR64sp:$Rn, VecListTwo2d:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x0C, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_LD1Twov2s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Twov2s_POST GPR64sp:$Rn, VecListTwo2s:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x0D, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_LD1Twov4h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Twov4h_POST GPR64sp:$Rn, VecListTwo4h:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x0E, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_LD1Twov4s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Twov4s_POST GPR64sp:$Rn, VecListTwo4s:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x0F, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_LD1Twov8b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Twov8b_POST GPR64sp:$Rn, VecListTwo8b:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x10, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_LD1Twov8h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD1Twov8h_POST GPR64sp:$Rn, VecListTwo8h:$Vt, XZR)
      AsmString = "ld1	$\xFF\x02\x11, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_LD1i16_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (LD1i16_POST GPR64sp:$Rn, VecListOneh:$Vt, VectorIndexH:$idx, XZR)
      AsmString = "ld1	$\xFF\x02\x12$\xFF\x03\x09, [$\x01], #2";
      break;
    }
    return NULL;
  case AArch64_LD1i32_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (LD1i32_POST GPR64sp:$Rn, VecListOnes:$Vt, VectorIndexS:$idx, XZR)
      AsmString = "ld1	$\xFF\x02\x13$\xFF\x03\x09, [$\x01], #4";
      break;
    }
    return NULL;
  case AArch64_LD1i64_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (LD1i64_POST GPR64sp:$Rn, VecListOned:$Vt, VectorIndexD:$idx, XZR)
      AsmString = "ld1	$\xFF\x02\x14$\xFF\x03\x09, [$\x01], #8";
      break;
    }
    return NULL;
  case AArch64_LD1i8_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (LD1i8_POST GPR64sp:$Rn, VecListOneb:$Vt, VectorIndexB:$idx, XZR)
      AsmString = "ld1	$\xFF\x02\x15$\xFF\x03\x09, [$\x01], #1";
      break;
    }
    return NULL;
  case AArch64_LD2Rv16b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD2Rv16b_POST GPR64sp:$Rn, VecListTwo16b:$Vt, XZR)
      AsmString = "ld2r	$\xFF\x02\x0A, [$\x01], #2";
      break;
    }
    return NULL;
  case AArch64_LD2Rv1d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD2Rv1d_POST GPR64sp:$Rn, VecListTwo1d:$Vt, XZR)
      AsmString = "ld2r	$\xFF\x02\x0B, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_LD2Rv2d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD2Rv2d_POST GPR64sp:$Rn, VecListTwo2d:$Vt, XZR)
      AsmString = "ld2r	$\xFF\x02\x0C, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_LD2Rv2s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD2Rv2s_POST GPR64sp:$Rn, VecListTwo2s:$Vt, XZR)
      AsmString = "ld2r	$\xFF\x02\x0D, [$\x01], #8";
      break;
    }
    return NULL;
  case AArch64_LD2Rv4h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD2Rv4h_POST GPR64sp:$Rn, VecListTwo4h:$Vt, XZR)
      AsmString = "ld2r	$\xFF\x02\x0E, [$\x01], #4";
      break;
    }
    return NULL;
  case AArch64_LD2Rv4s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD2Rv4s_POST GPR64sp:$Rn, VecListTwo4s:$Vt, XZR)
      AsmString = "ld2r	$\xFF\x02\x0F, [$\x01], #8";
      break;
    }
    return NULL;
  case AArch64_LD2Rv8b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD2Rv8b_POST GPR64sp:$Rn, VecListTwo8b:$Vt, XZR)
      AsmString = "ld2r	$\xFF\x02\x10, [$\x01], #2";
      break;
    }
    return NULL;
  case AArch64_LD2Rv8h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD2Rv8h_POST GPR64sp:$Rn, VecListTwo8h:$Vt, XZR)
      AsmString = "ld2r	$\xFF\x02\x11, [$\x01], #4";
      break;
    }
    return NULL;
  case AArch64_LD2Twov16b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD2Twov16b_POST GPR64sp:$Rn, VecListTwo16b:$Vt, XZR)
      AsmString = "ld2	$\xFF\x02\x0A, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_LD2Twov2d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD2Twov2d_POST GPR64sp:$Rn, VecListTwo2d:$Vt, XZR)
      AsmString = "ld2	$\xFF\x02\x0C, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_LD2Twov2s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD2Twov2s_POST GPR64sp:$Rn, VecListTwo2s:$Vt, XZR)
      AsmString = "ld2	$\xFF\x02\x0D, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_LD2Twov4h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD2Twov4h_POST GPR64sp:$Rn, VecListTwo4h:$Vt, XZR)
      AsmString = "ld2	$\xFF\x02\x0E, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_LD2Twov4s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD2Twov4s_POST GPR64sp:$Rn, VecListTwo4s:$Vt, XZR)
      AsmString = "ld2	$\xFF\x02\x0F, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_LD2Twov8b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD2Twov8b_POST GPR64sp:$Rn, VecListTwo8b:$Vt, XZR)
      AsmString = "ld2	$\xFF\x02\x10, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_LD2Twov8h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD2Twov8h_POST GPR64sp:$Rn, VecListTwo8h:$Vt, XZR)
      AsmString = "ld2	$\xFF\x02\x11, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_LD2i16_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (LD2i16_POST GPR64sp:$Rn, VecListTwoh:$Vt, VectorIndexH:$idx, XZR)
      AsmString = "ld2	$\xFF\x02\x12$\xFF\x03\x09, [$\x01], #4";
      break;
    }
    return NULL;
  case AArch64_LD2i32_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (LD2i32_POST GPR64sp:$Rn, VecListTwos:$Vt, VectorIndexS:$idx, XZR)
      AsmString = "ld2	$\xFF\x02\x13$\xFF\x03\x09, [$\x01], #8";
      break;
    }
    return NULL;
  case AArch64_LD2i64_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (LD2i64_POST GPR64sp:$Rn, VecListTwod:$Vt, VectorIndexD:$idx, XZR)
      AsmString = "ld2	$\xFF\x02\x14$\xFF\x03\x09, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_LD2i8_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (LD2i8_POST GPR64sp:$Rn, VecListTwob:$Vt, VectorIndexB:$idx, XZR)
      AsmString = "ld2	$\xFF\x02\x15$\xFF\x03\x09, [$\x01], #2";
      break;
    }
    return NULL;
  case AArch64_LD3Rv16b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD3Rv16b_POST GPR64sp:$Rn, VecListThree16b:$Vt, XZR)
      AsmString = "ld3r	$\xFF\x02\x0A, [$\x01], #3";
      break;
    }
    return NULL;
  case AArch64_LD3Rv1d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD3Rv1d_POST GPR64sp:$Rn, VecListThree1d:$Vt, XZR)
      AsmString = "ld3r	$\xFF\x02\x0B, [$\x01], #24";
      break;
    }
    return NULL;
  case AArch64_LD3Rv2d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD3Rv2d_POST GPR64sp:$Rn, VecListThree2d:$Vt, XZR)
      AsmString = "ld3r	$\xFF\x02\x0C, [$\x01], #24";
      break;
    }
    return NULL;
  case AArch64_LD3Rv2s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD3Rv2s_POST GPR64sp:$Rn, VecListThree2s:$Vt, XZR)
      AsmString = "ld3r	$\xFF\x02\x0D, [$\x01], #12";
      break;
    }
    return NULL;
  case AArch64_LD3Rv4h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD3Rv4h_POST GPR64sp:$Rn, VecListThree4h:$Vt, XZR)
      AsmString = "ld3r	$\xFF\x02\x0E, [$\x01], #6";
      break;
    }
    return NULL;
  case AArch64_LD3Rv4s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD3Rv4s_POST GPR64sp:$Rn, VecListThree4s:$Vt, XZR)
      AsmString = "ld3r	$\xFF\x02\x0F, [$\x01], #12";
      break;
    }
    return NULL;
  case AArch64_LD3Rv8b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD3Rv8b_POST GPR64sp:$Rn, VecListThree8b:$Vt, XZR)
      AsmString = "ld3r	$\xFF\x02\x10, [$\x01], #3";
      break;
    }
    return NULL;
  case AArch64_LD3Rv8h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD3Rv8h_POST GPR64sp:$Rn, VecListThree8h:$Vt, XZR)
      AsmString = "ld3r	$\xFF\x02\x11, [$\x01], #6";
      break;
    }
    return NULL;
  case AArch64_LD3Threev16b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD3Threev16b_POST GPR64sp:$Rn, VecListThree16b:$Vt, XZR)
      AsmString = "ld3	$\xFF\x02\x0A, [$\x01], #48";
      break;
    }
    return NULL;
  case AArch64_LD3Threev2d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD3Threev2d_POST GPR64sp:$Rn, VecListThree2d:$Vt, XZR)
      AsmString = "ld3	$\xFF\x02\x0C, [$\x01], #48";
      break;
    }
    return NULL;
  case AArch64_LD3Threev2s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD3Threev2s_POST GPR64sp:$Rn, VecListThree2s:$Vt, XZR)
      AsmString = "ld3	$\xFF\x02\x0D, [$\x01], #24";
      break;
    }
    return NULL;
  case AArch64_LD3Threev4h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD3Threev4h_POST GPR64sp:$Rn, VecListThree4h:$Vt, XZR)
      AsmString = "ld3	$\xFF\x02\x0E, [$\x01], #24";
      break;
    }
    return NULL;
  case AArch64_LD3Threev4s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD3Threev4s_POST GPR64sp:$Rn, VecListThree4s:$Vt, XZR)
      AsmString = "ld3	$\xFF\x02\x0F, [$\x01], #48";
      break;
    }
    return NULL;
  case AArch64_LD3Threev8b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD3Threev8b_POST GPR64sp:$Rn, VecListThree8b:$Vt, XZR)
      AsmString = "ld3	$\xFF\x02\x10, [$\x01], #24";
      break;
    }
    return NULL;
  case AArch64_LD3Threev8h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD3Threev8h_POST GPR64sp:$Rn, VecListThree8h:$Vt, XZR)
      AsmString = "ld3	$\xFF\x02\x11, [$\x01], #48";
      break;
    }
    return NULL;
  case AArch64_LD3i16_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (LD3i16_POST GPR64sp:$Rn, VecListThreeh:$Vt, VectorIndexH:$idx, XZR)
      AsmString = "ld3	$\xFF\x02\x12$\xFF\x03\x09, [$\x01], #6";
      break;
    }
    return NULL;
  case AArch64_LD3i32_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (LD3i32_POST GPR64sp:$Rn, VecListThrees:$Vt, VectorIndexS:$idx, XZR)
      AsmString = "ld3	$\xFF\x02\x13$\xFF\x03\x09, [$\x01], #12";
      break;
    }
    return NULL;
  case AArch64_LD3i64_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (LD3i64_POST GPR64sp:$Rn, VecListThreed:$Vt, VectorIndexD:$idx, XZR)
      AsmString = "ld3	$\xFF\x02\x14$\xFF\x03\x09, [$\x01], #24";
      break;
    }
    return NULL;
  case AArch64_LD3i8_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (LD3i8_POST GPR64sp:$Rn, VecListThreeb:$Vt, VectorIndexB:$idx, XZR)
      AsmString = "ld3	$\xFF\x02\x15$\xFF\x03\x09, [$\x01], #3";
      break;
    }
    return NULL;
  case AArch64_LD4Fourv16b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD4Fourv16b_POST GPR64sp:$Rn, VecListFour16b:$Vt, XZR)
      AsmString = "ld4	$\xFF\x02\x0A, [$\x01], #64";
      break;
    }
    return NULL;
  case AArch64_LD4Fourv2d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD4Fourv2d_POST GPR64sp:$Rn, VecListFour2d:$Vt, XZR)
      AsmString = "ld4	$\xFF\x02\x0C, [$\x01], #64";
      break;
    }
    return NULL;
  case AArch64_LD4Fourv2s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD4Fourv2s_POST GPR64sp:$Rn, VecListFour2s:$Vt, XZR)
      AsmString = "ld4	$\xFF\x02\x0D, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_LD4Fourv4h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD4Fourv4h_POST GPR64sp:$Rn, VecListFour4h:$Vt, XZR)
      AsmString = "ld4	$\xFF\x02\x0E, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_LD4Fourv4s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD4Fourv4s_POST GPR64sp:$Rn, VecListFour4s:$Vt, XZR)
      AsmString = "ld4	$\xFF\x02\x0F, [$\x01], #64";
      break;
    }
    return NULL;
  case AArch64_LD4Fourv8b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD4Fourv8b_POST GPR64sp:$Rn, VecListFour8b:$Vt, XZR)
      AsmString = "ld4	$\xFF\x02\x10, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_LD4Fourv8h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD4Fourv8h_POST GPR64sp:$Rn, VecListFour8h:$Vt, XZR)
      AsmString = "ld4	$\xFF\x02\x11, [$\x01], #64";
      break;
    }
    return NULL;
  case AArch64_LD4Rv16b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD4Rv16b_POST GPR64sp:$Rn, VecListFour16b:$Vt, XZR)
      AsmString = "ld4r	$\xFF\x02\x0A, [$\x01], #4";
      break;
    }
    return NULL;
  case AArch64_LD4Rv1d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD4Rv1d_POST GPR64sp:$Rn, VecListFour1d:$Vt, XZR)
      AsmString = "ld4r	$\xFF\x02\x0B, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_LD4Rv2d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD4Rv2d_POST GPR64sp:$Rn, VecListFour2d:$Vt, XZR)
      AsmString = "ld4r	$\xFF\x02\x0C, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_LD4Rv2s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD4Rv2s_POST GPR64sp:$Rn, VecListFour2s:$Vt, XZR)
      AsmString = "ld4r	$\xFF\x02\x0D, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_LD4Rv4h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD4Rv4h_POST GPR64sp:$Rn, VecListFour4h:$Vt, XZR)
      AsmString = "ld4r	$\xFF\x02\x0E, [$\x01], #8";
      break;
    }
    return NULL;
  case AArch64_LD4Rv4s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD4Rv4s_POST GPR64sp:$Rn, VecListFour4s:$Vt, XZR)
      AsmString = "ld4r	$\xFF\x02\x0F, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_LD4Rv8b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD4Rv8b_POST GPR64sp:$Rn, VecListFour8b:$Vt, XZR)
      AsmString = "ld4r	$\xFF\x02\x10, [$\x01], #4";
      break;
    }
    return NULL;
  case AArch64_LD4Rv8h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (LD4Rv8h_POST GPR64sp:$Rn, VecListFour8h:$Vt, XZR)
      AsmString = "ld4r	$\xFF\x02\x11, [$\x01], #8";
      break;
    }
    return NULL;
  case AArch64_LD4i16_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (LD4i16_POST GPR64sp:$Rn, VecListFourh:$Vt, VectorIndexH:$idx, XZR)
      AsmString = "ld4	$\xFF\x02\x12$\xFF\x03\x09, [$\x01], #8";
      break;
    }
    return NULL;
  case AArch64_LD4i32_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (LD4i32_POST GPR64sp:$Rn, VecListFours:$Vt, VectorIndexS:$idx, XZR)
      AsmString = "ld4	$\xFF\x02\x13$\xFF\x03\x09, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_LD4i64_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (LD4i64_POST GPR64sp:$Rn, VecListFourd:$Vt, VectorIndexD:$idx, XZR)
      AsmString = "ld4	$\xFF\x02\x14$\xFF\x03\x09, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_LD4i8_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (LD4i8_POST GPR64sp:$Rn, VecListFourb:$Vt, VectorIndexB:$idx, XZR)
      AsmString = "ld4	$\xFF\x02\x15$\xFF\x03\x09, [$\x01], #4";
      break;
    }
    return NULL;
  case AArch64_LDNPDi:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (LDNPDi FPR64:$Rt, FPR64:$Rt2, GPR64sp:$Rn, 0)
      AsmString = "ldnp	$\x01, $\x02, [$\x03]";
      break;
    }
    return NULL;
  case AArch64_LDNPQi:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (LDNPQi FPR128:$Rt, FPR128:$Rt2, GPR64sp:$Rn, 0)
      AsmString = "ldnp	$\x01, $\x02, [$\x03]";
      break;
    }
    return NULL;
  case AArch64_LDNPSi:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (LDNPSi FPR32:$Rt, FPR32:$Rt2, GPR64sp:$Rn, 0)
      AsmString = "ldnp	$\x01, $\x02, [$\x03]";
      break;
    }
    return NULL;
  case AArch64_LDNPWi:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (LDNPWi GPR32:$Rt, GPR32:$Rt2, GPR64sp:$Rn, 0)
      AsmString = "ldnp	$\x01, $\x02, [$\x03]";
      break;
    }
    return NULL;
  case AArch64_LDNPXi:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (LDNPXi GPR64:$Rt, GPR64:$Rt2, GPR64sp:$Rn, 0)
      AsmString = "ldnp	$\x01, $\x02, [$\x03]";
      break;
    }
    return NULL;
  case AArch64_LDPDi:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (LDPDi FPR64:$Rt, FPR64:$Rt2, GPR64sp:$Rn, 0)
      AsmString = "ldp $\x01, $\x02, [$\x03]";
      break;
    }
    return NULL;
  case AArch64_LDPQi:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (LDPQi FPR128:$Rt, FPR128:$Rt2, GPR64sp:$Rn, 0)
      AsmString = "ldp $\x01, $\x02, [$\x03]";
      break;
    }
    return NULL;
  case AArch64_LDPSWi:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (LDPSWi GPR64:$Rt, GPR64:$Rt2, GPR64sp:$Rn, 0)
      AsmString = "ldpsw $\x01, $\x02, [$\x03]";
      break;
    }
    return NULL;
  case AArch64_LDPSi:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (LDPSi FPR32:$Rt, FPR32:$Rt2, GPR64sp:$Rn, 0)
      AsmString = "ldp $\x01, $\x02, [$\x03]";
      break;
    }
    return NULL;
  case AArch64_LDPWi:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (LDPWi GPR32:$Rt, GPR32:$Rt2, GPR64sp:$Rn, 0)
      AsmString = "ldp $\x01, $\x02, [$\x03]";
      break;
    }
    return NULL;
  case AArch64_LDPXi:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (LDPXi GPR64:$Rt, GPR64:$Rt2, GPR64sp:$Rn, 0)
      AsmString = "ldp $\x01, $\x02, [$\x03]";
      break;
    }
    return NULL;
  case AArch64_LDRBBroX:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 0) {
      // (LDRBBroX GPR32:$Rt, GPR64sp:$Rn, GPR64:$Rm, 0, 0)
      AsmString = "ldrb $\x01, [$\x02, $\x03]";
      break;
    }
    return NULL;
  case AArch64_LDRBBui:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDRBBui GPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldrb $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDRBroX:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR8RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 0) {
      // (LDRBroX FPR8:$Rt, GPR64sp:$Rn, GPR64:$Rm, 0, 0)
      AsmString = "ldr $\x01, [$\x02, $\x03]";
      break;
    }
    return NULL;
  case AArch64_LDRBui:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR8RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDRBui FPR8:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldr $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDRDroX:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 0) {
      // (LDRDroX FPR64:$Rt, GPR64sp:$Rn, GPR64:$Rm, 0, 0)
      AsmString = "ldr $\x01, [$\x02, $\x03]";
      break;
    }
    return NULL;
  case AArch64_LDRDui:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDRDui FPR64:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldr $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDRHHroX:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 0) {
      // (LDRHHroX GPR32:$Rt, GPR64sp:$Rn, GPR64:$Rm, 0, 0)
      AsmString = "ldrh $\x01, [$\x02, $\x03]";
      break;
    }
    return NULL;
  case AArch64_LDRHHui:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDRHHui GPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldrh $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDRHroX:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR16RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 0) {
      // (LDRHroX FPR16:$Rt, GPR64sp:$Rn, GPR64:$Rm, 0, 0)
      AsmString = "ldr $\x01, [$\x02, $\x03]";
      break;
    }
    return NULL;
  case AArch64_LDRHui:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR16RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDRHui FPR16:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldr $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDRQroX:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 0) {
      // (LDRQroX FPR128:$Rt, GPR64sp:$Rn, GPR64:$Rm, 0, 0)
      AsmString = "ldr $\x01, [$\x02, $\x03]";
      break;
    }
    return NULL;
  case AArch64_LDRQui:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDRQui FPR128:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldr $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDRSBWroX:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 0) {
      // (LDRSBWroX GPR32:$Rt, GPR64sp:$Rn, GPR64:$Rm, 0, 0)
      AsmString = "ldrsb $\x01, [$\x02, $\x03]";
      break;
    }
    return NULL;
  case AArch64_LDRSBWui:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDRSBWui GPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldrsb $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDRSBXroX:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 0) {
      // (LDRSBXroX GPR64:$Rt, GPR64sp:$Rn, GPR64:$Rm, 0, 0)
      AsmString = "ldrsb $\x01, [$\x02, $\x03]";
      break;
    }
    return NULL;
  case AArch64_LDRSBXui:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDRSBXui GPR64:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldrsb $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDRSHWroX:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 0) {
      // (LDRSHWroX GPR32:$Rt, GPR64sp:$Rn, GPR64:$Rm, 0, 0)
      AsmString = "ldrsh $\x01, [$\x02, $\x03]";
      break;
    }
    return NULL;
  case AArch64_LDRSHWui:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDRSHWui GPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldrsh $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDRSHXroX:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 0) {
      // (LDRSHXroX GPR64:$Rt, GPR64sp:$Rn, GPR64:$Rm, 0, 0)
      AsmString = "ldrsh $\x01, [$\x02, $\x03]";
      break;
    }
    return NULL;
  case AArch64_LDRSHXui:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDRSHXui GPR64:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldrsh $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDRSWroX:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 0) {
      // (LDRSWroX GPR64:$Rt, GPR64sp:$Rn, GPR64:$Rm, 0, 0)
      AsmString = "ldrsw $\x01, [$\x02, $\x03]";
      break;
    }
    return NULL;
  case AArch64_LDRSWui:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDRSWui GPR64:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldrsw $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDRSroX:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 0) {
      // (LDRSroX FPR32:$Rt, GPR64sp:$Rn, GPR64:$Rm, 0, 0)
      AsmString = "ldr $\x01, [$\x02, $\x03]";
      break;
    }
    return NULL;
  case AArch64_LDRSui:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDRSui FPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldr $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDRWroX:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 0) {
      // (LDRWroX GPR32:$Rt, GPR64sp:$Rn, GPR64:$Rm, 0, 0)
      AsmString = "ldr $\x01, [$\x02, $\x03]";
      break;
    }
    return NULL;
  case AArch64_LDRWui:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDRWui GPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldr $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDRXroX:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 0) {
      // (LDRXroX GPR64:$Rt, GPR64sp:$Rn, GPR64:$Rm, 0, 0)
      AsmString = "ldr $\x01, [$\x02, $\x03]";
      break;
    }
    return NULL;
  case AArch64_LDRXui:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDRXui GPR64:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldr $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDTRBi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDTRBi GPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldtrb $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDTRHi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDTRHi GPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldtrh $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDTRSBWi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDTRSBWi GPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldtrsb $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDTRSBXi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDTRSBXi GPR64:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldtrsb $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDTRSHWi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDTRSHWi GPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldtrsh $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDTRSHXi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDTRSHXi GPR64:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldtrsh $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDTRSWi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDTRSWi GPR64:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldtrsw $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDTRWi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDTRWi GPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldtr $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDTRXi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDTRXi GPR64:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldtr $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDURBBi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDURBBi GPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldurb $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDURBi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR8RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDURBi FPR8:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldur $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDURDi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDURDi FPR64:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldur $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDURHHi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDURHHi GPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldurh $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDURHi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR16RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDURHi FPR16:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldur $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDURQi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDURQi FPR128:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldur $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDURSBWi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDURSBWi GPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldursb $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDURSBXi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDURSBXi GPR64:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldursb $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDURSHWi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDURSHWi GPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldursh $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDURSHXi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDURSHXi GPR64:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldursh $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDURSWi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDURSWi GPR64:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldursw $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDURSi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDURSi FPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldur $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDURWi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDURWi GPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldur $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_LDURXi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (LDURXi GPR64:$Rt, GPR64sp:$Rn, 0)
      AsmString = "ldur $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_MADDWrrr:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_WZR) {
      // (MADDWrrr GPR32:$dst, GPR32:$src1, GPR32:$src2, WZR)
      AsmString = "mul $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_MADDXrrr:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (MADDXrrr GPR64:$dst, GPR64:$src1, GPR64:$src2, XZR)
      AsmString = "mul $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_MOVKWi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 16) {
      // (MOVKWi GPR32:$Rd, movk_symbol_g1:$sym, 16)
      AsmString = "movk $\x01, $\x02";
      break;
    }
    return NULL;
  case AArch64_MOVKXi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 48) {
      // (MOVKXi GPR64:$Rd, movk_symbol_g3:$sym, 48)
      AsmString = "movk $\x01, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 32) {
      // (MOVKXi GPR64:$Rd, movk_symbol_g2:$sym, 32)
      AsmString = "movk $\x01, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 16) {
      // (MOVKXi GPR64:$Rd, movk_symbol_g1:$sym, 16)
      AsmString = "movk $\x01, $\x02";
      break;
    }
    return NULL;
  case AArch64_MSUBWrrr:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_WZR) {
      // (MSUBWrrr GPR32:$dst, GPR32:$src1, GPR32:$src2, WZR)
      AsmString = "mneg $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_MSUBXrrr:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (MSUBXrrr GPR64:$dst, GPR64:$src1, GPR64:$src2, XZR)
      AsmString = "mneg $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_NOTv16i8:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1)) {
      // (NOTv16i8 V128:$Vd, V128:$Vn)
      AsmString = "mvn $\xFF\x01\x06.16b, $\xFF\x02\x06.16b";
      break;
    }
    return NULL;
  case AArch64_NOTv8i8:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 1)) {
      // (NOTv8i8 V64:$Vd, V64:$Vn)
      AsmString = "mvn $\xFF\x01\x06.8b, $\xFF\x02\x06.8b";
      break;
    }
    return NULL;
  case AArch64_ORNWrs:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == AArch64_WZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (ORNWrs GPR32:$Wd, WZR, GPR32:$Wm, 0)
      AsmString = "mvn $\x01, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == AArch64_WZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2)) {
      // (ORNWrs GPR32:$Wd, WZR, GPR32:$Wm, logical_shift32:$sh)
      AsmString = "mvn $\x01, $\x03$\xFF\x04\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (ORNWrs GPR32:$dst, GPR32:$src1, GPR32:$src2, 0)
      AsmString = "orn $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_ORNXrs:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == AArch64_XZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (ORNXrs GPR64:$Xd, XZR, GPR64:$Xm, 0)
      AsmString = "mvn $\x01, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == AArch64_XZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2)) {
      // (ORNXrs GPR64:$Xd, XZR, GPR64:$Xm, logical_shift64:$sh)
      AsmString = "mvn $\x01, $\x03$\xFF\x04\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (ORNXrs GPR64:$dst, GPR64:$src1, GPR64:$src2, 0)
      AsmString = "orn $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_ORRWrs:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == AArch64_WZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (ORRWrs GPR32:$dst, WZR, GPR32:$src, 0)
      AsmString = "mov $\x01, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (ORRWrs GPR32:$dst, GPR32:$src1, GPR32:$src2, 0)
      AsmString = "orr $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_ORRXrs:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == AArch64_XZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (ORRXrs GPR64:$dst, XZR, GPR64:$src, 0)
      AsmString = "mov $\x01, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (ORRXrs GPR64:$dst, GPR64:$src1, GPR64:$src2, 0)
      AsmString = "orr $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_ORRv16i8:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1))) {
      // (ORRv16i8 V128:$dst, V128:$src, V128:$src)
      AsmString = "mov	$\xFF\x01\x06.16b, $\xFF\x02\x06.16b";
      break;
    }
    return NULL;
  case AArch64_ORRv2i32:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (ORRv2i32 V64:$Vd, imm0_255:$imm, 0)
      AsmString = "orr $\xFF\x01\x06.2s, $\xFF\x02\x07";
      break;
    }
    return NULL;
  case AArch64_ORRv4i16:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (ORRv4i16 V64:$Vd, imm0_255:$imm, 0)
      AsmString = "orr $\xFF\x01\x06.4h, $\xFF\x02\x07";
      break;
    }
    return NULL;
  case AArch64_ORRv4i32:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (ORRv4i32 V128:$Vd, imm0_255:$imm, 0)
      AsmString = "orr $\xFF\x01\x06.4s, $\xFF\x02\x07";
      break;
    }
    return NULL;
  case AArch64_ORRv8i16:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (ORRv8i16 V128:$Vd, imm0_255:$imm, 0)
      AsmString = "orr $\xFF\x01\x06.8h, $\xFF\x02\x07";
      break;
    }
    return NULL;
  case AArch64_ORRv8i8:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1))) {
      // (ORRv8i8 V64:$dst, V64:$src, V64:$src)
      AsmString = "mov	$\xFF\x01\x06.8b, $\xFF\x02\x06.8b";
      break;
    }
    return NULL;
  case AArch64_PRFMroX:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 0) {
      // (PRFMroX prfop:$Rt, GPR64sp:$Rn, GPR64:$Rm, 0, 0)
      AsmString = "prfm $\xFF\x01\x16, [$\x02, $\x03]";
      break;
    }
    return NULL;
  case AArch64_PRFMui:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (PRFMui prfop:$Rt, GPR64sp:$Rn, 0)
      AsmString = "prfm $\xFF\x01\x16, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_PRFUMi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (PRFUMi prfop:$Rt, GPR64sp:$Rn, 0)
      AsmString = "prfum $\xFF\x01\x16, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_RET:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_LR) {
      // (RET LR)
      AsmString = "ret";
      break;
    }
    return NULL;
  case AArch64_SBCSWr:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == AArch64_WZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2)) {
      // (SBCSWr GPR32:$dst, WZR, GPR32:$src)
      AsmString = "ngcs $\x01, $\x03";
      break;
    }
    return NULL;
  case AArch64_SBCSXr:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == AArch64_XZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2)) {
      // (SBCSXr GPR64:$dst, XZR, GPR64:$src)
      AsmString = "ngcs $\x01, $\x03";
      break;
    }
    return NULL;
  case AArch64_SBCWr:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == AArch64_WZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2)) {
      // (SBCWr GPR32:$dst, WZR, GPR32:$src)
      AsmString = "ngc $\x01, $\x03";
      break;
    }
    return NULL;
  case AArch64_SBCXr:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == AArch64_XZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2)) {
      // (SBCXr GPR64:$dst, XZR, GPR64:$src)
      AsmString = "ngc $\x01, $\x03";
      break;
    }
    return NULL;
  case AArch64_SBFMWri:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 31) {
      // (SBFMWri GPR32:$dst, GPR32:$src, imm0_31:$shift, 31)
      AsmString = "asr $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 7) {
      // (SBFMWri GPR32:$dst, GPR32:$src, 0, 7)
      AsmString = "sxtb $\x01, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 15) {
      // (SBFMWri GPR32:$dst, GPR32:$src, 0, 15)
      AsmString = "sxth $\x01, $\x02";
      break;
    }
    return NULL;
  case AArch64_SBFMXri:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 63) {
      // (SBFMXri GPR64:$dst, GPR64:$src, imm0_63:$shift, 63)
      AsmString = "asr $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 7) {
      // (SBFMXri GPR64:$dst, GPR64:$src, 0, 7)
      AsmString = "sxtb $\x01, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 15) {
      // (SBFMXri GPR64:$dst, GPR64:$src, 0, 15)
      AsmString = "sxth $\x01, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 31) {
      // (SBFMXri GPR64:$dst, GPR64:$src, 0, 31)
      AsmString = "sxtw $\x01, $\x02";
      break;
    }
    return NULL;
  case AArch64_SMADDLrrr:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (SMADDLrrr GPR64:$dst, GPR32:$src1, GPR32:$src2, XZR)
      AsmString = "smull $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_SMSUBLrrr:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (SMSUBLrrr GPR64:$dst, GPR32:$src1, GPR32:$src2, XZR)
      AsmString = "smnegl $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_ST1Fourv16b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Fourv16b_POST GPR64sp:$Rn, VecListFour16b:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x0A, [$\x01], #64";
      break;
    }
    return NULL;
  case AArch64_ST1Fourv1d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Fourv1d_POST GPR64sp:$Rn, VecListFour1d:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x0B, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_ST1Fourv2d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Fourv2d_POST GPR64sp:$Rn, VecListFour2d:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x0C, [$\x01], #64";
      break;
    }
    return NULL;
  case AArch64_ST1Fourv2s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Fourv2s_POST GPR64sp:$Rn, VecListFour2s:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x0D, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_ST1Fourv4h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Fourv4h_POST GPR64sp:$Rn, VecListFour4h:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x0E, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_ST1Fourv4s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Fourv4s_POST GPR64sp:$Rn, VecListFour4s:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x0F, [$\x01], #64";
      break;
    }
    return NULL;
  case AArch64_ST1Fourv8b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Fourv8b_POST GPR64sp:$Rn, VecListFour8b:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x10, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_ST1Fourv8h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Fourv8h_POST GPR64sp:$Rn, VecListFour8h:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x11, [$\x01], #64";
      break;
    }
    return NULL;
  case AArch64_ST1Onev16b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Onev16b_POST GPR64sp:$Rn, VecListOne16b:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x0A, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_ST1Onev1d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Onev1d_POST GPR64sp:$Rn, VecListOne1d:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x0B, [$\x01], #8";
      break;
    }
    return NULL;
  case AArch64_ST1Onev2d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Onev2d_POST GPR64sp:$Rn, VecListOne2d:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x0C, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_ST1Onev2s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Onev2s_POST GPR64sp:$Rn, VecListOne2s:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x0D, [$\x01], #8";
      break;
    }
    return NULL;
  case AArch64_ST1Onev4h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Onev4h_POST GPR64sp:$Rn, VecListOne4h:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x0E, [$\x01], #8";
      break;
    }
    return NULL;
  case AArch64_ST1Onev4s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Onev4s_POST GPR64sp:$Rn, VecListOne4s:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x0F, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_ST1Onev8b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Onev8b_POST GPR64sp:$Rn, VecListOne8b:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x10, [$\x01], #8";
      break;
    }
    return NULL;
  case AArch64_ST1Onev8h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Onev8h_POST GPR64sp:$Rn, VecListOne8h:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x11, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_ST1Threev16b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Threev16b_POST GPR64sp:$Rn, VecListThree16b:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x0A, [$\x01], #48";
      break;
    }
    return NULL;
  case AArch64_ST1Threev1d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Threev1d_POST GPR64sp:$Rn, VecListThree1d:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x0B, [$\x01], #24";
      break;
    }
    return NULL;
  case AArch64_ST1Threev2d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Threev2d_POST GPR64sp:$Rn, VecListThree2d:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x0C, [$\x01], #48";
      break;
    }
    return NULL;
  case AArch64_ST1Threev2s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Threev2s_POST GPR64sp:$Rn, VecListThree2s:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x0D, [$\x01], #24";
      break;
    }
    return NULL;
  case AArch64_ST1Threev4h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Threev4h_POST GPR64sp:$Rn, VecListThree4h:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x0E, [$\x01], #24";
      break;
    }
    return NULL;
  case AArch64_ST1Threev4s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Threev4s_POST GPR64sp:$Rn, VecListThree4s:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x0F, [$\x01], #48";
      break;
    }
    return NULL;
  case AArch64_ST1Threev8b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Threev8b_POST GPR64sp:$Rn, VecListThree8b:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x10, [$\x01], #24";
      break;
    }
    return NULL;
  case AArch64_ST1Threev8h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Threev8h_POST GPR64sp:$Rn, VecListThree8h:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x11, [$\x01], #48";
      break;
    }
    return NULL;
  case AArch64_ST1Twov16b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Twov16b_POST GPR64sp:$Rn, VecListTwo16b:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x0A, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_ST1Twov1d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Twov1d_POST GPR64sp:$Rn, VecListTwo1d:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x0B, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_ST1Twov2d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Twov2d_POST GPR64sp:$Rn, VecListTwo2d:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x0C, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_ST1Twov2s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Twov2s_POST GPR64sp:$Rn, VecListTwo2s:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x0D, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_ST1Twov4h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Twov4h_POST GPR64sp:$Rn, VecListTwo4h:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x0E, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_ST1Twov4s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Twov4s_POST GPR64sp:$Rn, VecListTwo4s:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x0F, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_ST1Twov8b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Twov8b_POST GPR64sp:$Rn, VecListTwo8b:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x10, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_ST1Twov8h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST1Twov8h_POST GPR64sp:$Rn, VecListTwo8h:$Vt, XZR)
      AsmString = "st1	$\xFF\x02\x11, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_ST1i16_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (ST1i16_POST GPR64sp:$Rn, VecListOneh:$Vt, VectorIndexH:$idx, XZR)
      AsmString = "st1	$\xFF\x02\x12$\xFF\x03\x09, [$\x01], #2";
      break;
    }
    return NULL;
  case AArch64_ST1i32_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (ST1i32_POST GPR64sp:$Rn, VecListOnes:$Vt, VectorIndexS:$idx, XZR)
      AsmString = "st1	$\xFF\x02\x13$\xFF\x03\x09, [$\x01], #4";
      break;
    }
    return NULL;
  case AArch64_ST1i64_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (ST1i64_POST GPR64sp:$Rn, VecListOned:$Vt, VectorIndexD:$idx, XZR)
      AsmString = "st1	$\xFF\x02\x14$\xFF\x03\x09, [$\x01], #8";
      break;
    }
    return NULL;
  case AArch64_ST1i8_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (ST1i8_POST GPR64sp:$Rn, VecListOneb:$Vt, VectorIndexB:$idx, XZR)
      AsmString = "st1	$\xFF\x02\x15$\xFF\x03\x09, [$\x01], #1";
      break;
    }
    return NULL;
  case AArch64_ST2Twov16b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST2Twov16b_POST GPR64sp:$Rn, VecListTwo16b:$Vt, XZR)
      AsmString = "st2	$\xFF\x02\x0A, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_ST2Twov2d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST2Twov2d_POST GPR64sp:$Rn, VecListTwo2d:$Vt, XZR)
      AsmString = "st2	$\xFF\x02\x0C, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_ST2Twov2s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST2Twov2s_POST GPR64sp:$Rn, VecListTwo2s:$Vt, XZR)
      AsmString = "st2	$\xFF\x02\x0D, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_ST2Twov4h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST2Twov4h_POST GPR64sp:$Rn, VecListTwo4h:$Vt, XZR)
      AsmString = "st2	$\xFF\x02\x0E, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_ST2Twov4s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST2Twov4s_POST GPR64sp:$Rn, VecListTwo4s:$Vt, XZR)
      AsmString = "st2	$\xFF\x02\x0F, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_ST2Twov8b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST2Twov8b_POST GPR64sp:$Rn, VecListTwo8b:$Vt, XZR)
      AsmString = "st2	$\xFF\x02\x10, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_ST2Twov8h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST2Twov8h_POST GPR64sp:$Rn, VecListTwo8h:$Vt, XZR)
      AsmString = "st2	$\xFF\x02\x11, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_ST2i16_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (ST2i16_POST GPR64sp:$Rn, VecListTwoh:$Vt, VectorIndexH:$idx, XZR)
      AsmString = "st2	$\xFF\x02\x12$\xFF\x03\x09, [$\x01], #4";
      break;
    }
    return NULL;
  case AArch64_ST2i32_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (ST2i32_POST GPR64sp:$Rn, VecListTwos:$Vt, VectorIndexS:$idx, XZR)
      AsmString = "st2	$\xFF\x02\x13$\xFF\x03\x09, [$\x01], #8";
      break;
    }
    return NULL;
  case AArch64_ST2i64_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (ST2i64_POST GPR64sp:$Rn, VecListTwod:$Vt, VectorIndexD:$idx, XZR)
      AsmString = "st2	$\xFF\x02\x14$\xFF\x03\x09, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_ST2i8_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (ST2i8_POST GPR64sp:$Rn, VecListTwob:$Vt, VectorIndexB:$idx, XZR)
      AsmString = "st2	$\xFF\x02\x15$\xFF\x03\x09, [$\x01], #2";
      break;
    }
    return NULL;
  case AArch64_ST3Threev16b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST3Threev16b_POST GPR64sp:$Rn, VecListThree16b:$Vt, XZR)
      AsmString = "st3	$\xFF\x02\x0A, [$\x01], #48";
      break;
    }
    return NULL;
  case AArch64_ST3Threev2d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST3Threev2d_POST GPR64sp:$Rn, VecListThree2d:$Vt, XZR)
      AsmString = "st3	$\xFF\x02\x0C, [$\x01], #48";
      break;
    }
    return NULL;
  case AArch64_ST3Threev2s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST3Threev2s_POST GPR64sp:$Rn, VecListThree2s:$Vt, XZR)
      AsmString = "st3	$\xFF\x02\x0D, [$\x01], #24";
      break;
    }
    return NULL;
  case AArch64_ST3Threev4h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST3Threev4h_POST GPR64sp:$Rn, VecListThree4h:$Vt, XZR)
      AsmString = "st3	$\xFF\x02\x0E, [$\x01], #24";
      break;
    }
    return NULL;
  case AArch64_ST3Threev4s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST3Threev4s_POST GPR64sp:$Rn, VecListThree4s:$Vt, XZR)
      AsmString = "st3	$\xFF\x02\x0F, [$\x01], #48";
      break;
    }
    return NULL;
  case AArch64_ST3Threev8b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST3Threev8b_POST GPR64sp:$Rn, VecListThree8b:$Vt, XZR)
      AsmString = "st3	$\xFF\x02\x10, [$\x01], #24";
      break;
    }
    return NULL;
  case AArch64_ST3Threev8h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST3Threev8h_POST GPR64sp:$Rn, VecListThree8h:$Vt, XZR)
      AsmString = "st3	$\xFF\x02\x11, [$\x01], #48";
      break;
    }
    return NULL;
  case AArch64_ST3i16_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (ST3i16_POST GPR64sp:$Rn, VecListThreeh:$Vt, VectorIndexH:$idx, XZR)
      AsmString = "st3	$\xFF\x02\x12$\xFF\x03\x09, [$\x01], #6";
      break;
    }
    return NULL;
  case AArch64_ST3i32_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (ST3i32_POST GPR64sp:$Rn, VecListThrees:$Vt, VectorIndexS:$idx, XZR)
      AsmString = "st3	$\xFF\x02\x13$\xFF\x03\x09, [$\x01], #12";
      break;
    }
    return NULL;
  case AArch64_ST3i64_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (ST3i64_POST GPR64sp:$Rn, VecListThreed:$Vt, VectorIndexD:$idx, XZR)
      AsmString = "st3	$\xFF\x02\x14$\xFF\x03\x09, [$\x01], #24";
      break;
    }
    return NULL;
  case AArch64_ST3i8_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (ST3i8_POST GPR64sp:$Rn, VecListThreeb:$Vt, VectorIndexB:$idx, XZR)
      AsmString = "st3	$\xFF\x02\x15$\xFF\x03\x09, [$\x01], #3";
      break;
    }
    return NULL;
  case AArch64_ST4Fourv16b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST4Fourv16b_POST GPR64sp:$Rn, VecListFour16b:$Vt, XZR)
      AsmString = "st4	$\xFF\x02\x0A, [$\x01], #64";
      break;
    }
    return NULL;
  case AArch64_ST4Fourv2d_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST4Fourv2d_POST GPR64sp:$Rn, VecListFour2d:$Vt, XZR)
      AsmString = "st4	$\xFF\x02\x0C, [$\x01], #64";
      break;
    }
    return NULL;
  case AArch64_ST4Fourv2s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST4Fourv2s_POST GPR64sp:$Rn, VecListFour2s:$Vt, XZR)
      AsmString = "st4	$\xFF\x02\x0D, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_ST4Fourv4h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST4Fourv4h_POST GPR64sp:$Rn, VecListFour4h:$Vt, XZR)
      AsmString = "st4	$\xFF\x02\x0E, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_ST4Fourv4s_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST4Fourv4s_POST GPR64sp:$Rn, VecListFour4s:$Vt, XZR)
      AsmString = "st4	$\xFF\x02\x0F, [$\x01], #64";
      break;
    }
    return NULL;
  case AArch64_ST4Fourv8b_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_DDDDRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST4Fourv8b_POST GPR64sp:$Rn, VecListFour8b:$Vt, XZR)
      AsmString = "st4	$\xFF\x02\x10, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_ST4Fourv8h_POST:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == AArch64_XZR) {
      // (ST4Fourv8h_POST GPR64sp:$Rn, VecListFour8h:$Vt, XZR)
      AsmString = "st4	$\xFF\x02\x11, [$\x01], #64";
      break;
    }
    return NULL;
  case AArch64_ST4i16_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (ST4i16_POST GPR64sp:$Rn, VecListFourh:$Vt, VectorIndexH:$idx, XZR)
      AsmString = "st4	$\xFF\x02\x12$\xFF\x03\x09, [$\x01], #8";
      break;
    }
    return NULL;
  case AArch64_ST4i32_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (ST4i32_POST GPR64sp:$Rn, VecListFours:$Vt, VectorIndexS:$idx, XZR)
      AsmString = "st4	$\xFF\x02\x13$\xFF\x03\x09, [$\x01], #16";
      break;
    }
    return NULL;
  case AArch64_ST4i64_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (ST4i64_POST GPR64sp:$Rn, VecListFourd:$Vt, VectorIndexD:$idx, XZR)
      AsmString = "st4	$\xFF\x02\x14$\xFF\x03\x09, [$\x01], #32";
      break;
    }
    return NULL;
  case AArch64_ST4i8_POST:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_QQQQRegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (ST4i8_POST GPR64sp:$Rn, VecListFourb:$Vt, VectorIndexB:$idx, XZR)
      AsmString = "st4	$\xFF\x02\x15$\xFF\x03\x09, [$\x01], #4";
      break;
    }
    return NULL;
  case AArch64_STNPDi:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (STNPDi FPR64:$Rt, FPR64:$Rt2, GPR64sp:$Rn, 0)
      AsmString = "stnp	$\x01, $\x02, [$\x03]";
      break;
    }
    return NULL;
  case AArch64_STNPQi:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (STNPQi FPR128:$Rt, FPR128:$Rt2, GPR64sp:$Rn, 0)
      AsmString = "stnp	$\x01, $\x02, [$\x03]";
      break;
    }
    return NULL;
  case AArch64_STNPSi:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (STNPSi FPR32:$Rt, FPR32:$Rt2, GPR64sp:$Rn, 0)
      AsmString = "stnp	$\x01, $\x02, [$\x03]";
      break;
    }
    return NULL;
  case AArch64_STNPWi:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (STNPWi GPR32:$Rt, GPR32:$Rt2, GPR64sp:$Rn, 0)
      AsmString = "stnp	$\x01, $\x02, [$\x03]";
      break;
    }
    return NULL;
  case AArch64_STNPXi:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (STNPXi GPR64:$Rt, GPR64:$Rt2, GPR64sp:$Rn, 0)
      AsmString = "stnp	$\x01, $\x02, [$\x03]";
      break;
    }
    return NULL;
  case AArch64_STPDi:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (STPDi FPR64:$Rt, FPR64:$Rt2, GPR64sp:$Rn, 0)
      AsmString = "stp $\x01, $\x02, [$\x03]";
      break;
    }
    return NULL;
  case AArch64_STPQi:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (STPQi FPR128:$Rt, FPR128:$Rt2, GPR64sp:$Rn, 0)
      AsmString = "stp $\x01, $\x02, [$\x03]";
      break;
    }
    return NULL;
  case AArch64_STPSi:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (STPSi FPR32:$Rt, FPR32:$Rt2, GPR64sp:$Rn, 0)
      AsmString = "stp $\x01, $\x02, [$\x03]";
      break;
    }
    return NULL;
  case AArch64_STPWi:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (STPWi GPR32:$Rt, GPR32:$Rt2, GPR64sp:$Rn, 0)
      AsmString = "stp $\x01, $\x02, [$\x03]";
      break;
    }
    return NULL;
  case AArch64_STPXi:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (STPXi GPR64:$Rt, GPR64:$Rt2, GPR64sp:$Rn, 0)
      AsmString = "stp $\x01, $\x02, [$\x03]";
      break;
    }
    return NULL;
  case AArch64_STRBBroX:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 0) {
      // (STRBBroX GPR32:$Rt, GPR64sp:$Rn, GPR64:$Rm, 0, 0)
      AsmString = "strb $\x01, [$\x02, $\x03]";
      break;
    }
    return NULL;
  case AArch64_STRBBui:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (STRBBui GPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "strb $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_STRBroX:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR8RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 0) {
      // (STRBroX FPR8:$Rt, GPR64sp:$Rn, GPR64:$Rm, 0, 0)
      AsmString = "str $\x01, [$\x02, $\x03]";
      break;
    }
    return NULL;
  case AArch64_STRBui:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR8RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (STRBui FPR8:$Rt, GPR64sp:$Rn, 0)
      AsmString = "str $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_STRDroX:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 0) {
      // (STRDroX FPR64:$Rt, GPR64sp:$Rn, GPR64:$Rm, 0, 0)
      AsmString = "str $\x01, [$\x02, $\x03]";
      break;
    }
    return NULL;
  case AArch64_STRDui:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (STRDui FPR64:$Rt, GPR64sp:$Rn, 0)
      AsmString = "str $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_STRHHroX:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 0) {
      // (STRHHroX GPR32:$Rt, GPR64sp:$Rn, GPR64:$Rm, 0, 0)
      AsmString = "strh $\x01, [$\x02, $\x03]";
      break;
    }
    return NULL;
  case AArch64_STRHHui:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (STRHHui GPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "strh $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_STRHroX:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR16RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 0) {
      // (STRHroX FPR16:$Rt, GPR64sp:$Rn, GPR64:$Rm, 0, 0)
      AsmString = "str $\x01, [$\x02, $\x03]";
      break;
    }
    return NULL;
  case AArch64_STRHui:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR16RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (STRHui FPR16:$Rt, GPR64sp:$Rn, 0)
      AsmString = "str $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_STRQroX:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 0) {
      // (STRQroX FPR128:$Rt, GPR64sp:$Rn, GPR64:$Rm, 0, 0)
      AsmString = "str $\x01, [$\x02, $\x03]";
      break;
    }
    return NULL;
  case AArch64_STRQui:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (STRQui FPR128:$Rt, GPR64sp:$Rn, 0)
      AsmString = "str $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_STRSroX:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 0) {
      // (STRSroX FPR32:$Rt, GPR64sp:$Rn, GPR64:$Rm, 0, 0)
      AsmString = "str $\x01, [$\x02, $\x03]";
      break;
    }
    return NULL;
  case AArch64_STRSui:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (STRSui FPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "str $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_STRWroX:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 0) {
      // (STRWroX GPR32:$Rt, GPR64sp:$Rn, GPR64:$Rm, 0, 0)
      AsmString = "str $\x01, [$\x02, $\x03]";
      break;
    }
    return NULL;
  case AArch64_STRWui:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (STRWui GPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "str $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_STRXroX:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 0) {
      // (STRXroX GPR64:$Rt, GPR64sp:$Rn, GPR64:$Rm, 0, 0)
      AsmString = "str $\x01, [$\x02, $\x03]";
      break;
    }
    return NULL;
  case AArch64_STRXui:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (STRXui GPR64:$Rt, GPR64sp:$Rn, 0)
      AsmString = "str $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_STTRBi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (STTRBi GPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "sttrb $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_STTRHi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (STTRHi GPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "sttrh $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_STTRWi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (STTRWi GPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "sttr $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_STTRXi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (STTRXi GPR64:$Rt, GPR64sp:$Rn, 0)
      AsmString = "sttr $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_STURBBi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (STURBBi GPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "sturb $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_STURBi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR8RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (STURBi FPR8:$Rt, GPR64sp:$Rn, 0)
      AsmString = "stur $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_STURDi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (STURDi FPR64:$Rt, GPR64sp:$Rn, 0)
      AsmString = "stur $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_STURHHi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (STURHHi GPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "sturh $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_STURHi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR16RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (STURHi FPR16:$Rt, GPR64sp:$Rn, 0)
      AsmString = "stur $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_STURQi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (STURQi FPR128:$Rt, GPR64sp:$Rn, 0)
      AsmString = "stur $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_STURSi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (STURSi FPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "stur $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_STURWi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (STURWi GPR32:$Rt, GPR64sp:$Rn, 0)
      AsmString = "stur $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_STURXi:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (STURXi GPR64:$Rt, GPR64sp:$Rn, 0)
      AsmString = "stur $\x01, [$\x02]";
      break;
    }
    return NULL;
  case AArch64_SUBSWri:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_WZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32spRegClassID, 1)) {
      // (SUBSWri WZR, GPR32sp:$src, addsub_shifted_imm32:$imm)
      AsmString = "cmp $\x02, $\xFF\x03\x01";
      break;
    }
    return NULL;
  case AArch64_SUBSWrs:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_WZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (SUBSWrs WZR, GPR32:$src1, GPR32:$src2, 0)
      AsmString = "cmp $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_WZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2)) {
      // (SUBSWrs WZR, GPR32:$src1, GPR32:$src2, arith_shift32:$sh)
      AsmString = "cmp $\x02, $\x03$\xFF\x04\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == AArch64_WZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (SUBSWrs GPR32:$dst, WZR, GPR32:$src, 0)
      AsmString = "negs $\x01, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == AArch64_WZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2)) {
      // (SUBSWrs GPR32:$dst, WZR, GPR32:$src, arith_shift32:$shift)
      AsmString = "negs $\x01, $\x03$\xFF\x04\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (SUBSWrs GPR32:$dst, GPR32:$src1, GPR32:$src2, 0)
      AsmString = "subs $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_SUBSWrx:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_WZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32sponlyRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 16) {
      // (SUBSWrx WZR, GPR32sponly:$src1, GPR32:$src2, 16)
      AsmString = "cmp $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_WZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2)) {
      // (SUBSWrx WZR, GPR32sp:$src1, GPR32:$src2, arith_extend:$sh)
      AsmString = "cmp $\x02, $\x03$\xFF\x04\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32sponlyRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 16) {
      // (SUBSWrx GPR32:$dst, GPR32sponly:$src1, GPR32:$src2, 16)
      AsmString = "subs $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_SUBSXri:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_XZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1)) {
      // (SUBSXri XZR, GPR64sp:$src, addsub_shifted_imm64:$imm)
      AsmString = "cmp $\x02, $\xFF\x03\x01";
      break;
    }
    return NULL;
  case AArch64_SUBSXrs:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_XZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (SUBSXrs XZR, GPR64:$src1, GPR64:$src2, 0)
      AsmString = "cmp $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_XZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2)) {
      // (SUBSXrs XZR, GPR64:$src1, GPR64:$src2, arith_shift64:$sh)
      AsmString = "cmp $\x02, $\x03$\xFF\x04\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == AArch64_XZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (SUBSXrs GPR64:$dst, XZR, GPR64:$src, 0)
      AsmString = "negs $\x01, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == AArch64_XZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2)) {
      // (SUBSXrs GPR64:$dst, XZR, GPR64:$src, arith_shift64:$shift)
      AsmString = "negs $\x01, $\x03$\xFF\x04\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (SUBSXrs GPR64:$dst, GPR64:$src1, GPR64:$src2, 0)
      AsmString = "subs $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_SUBSXrx:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_XZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2)) {
      // (SUBSXrx XZR, GPR64sp:$src1, GPR32:$src2, arith_extend:$sh)
      AsmString = "cmp $\x02, $\x03$\xFF\x04\x03";
      break;
    }
    return NULL;
  case AArch64_SUBSXrx64:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_XZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64sponlyRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 24) {
      // (SUBSXrx64 XZR, GPR64sponly:$src1, GPR64:$src2, 24)
      AsmString = "cmp $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == AArch64_XZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2)) {
      // (SUBSXrx64 XZR, GPR64sp:$src1, GPR64:$src2, arith_extendlsl64:$sh)
      AsmString = "cmp $\x02, $\x03$\xFF\x04\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64sponlyRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 24) {
      // (SUBSXrx64 GPR64:$dst, GPR64sponly:$src1, GPR64:$src2, 24)
      AsmString = "subs $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_SUBWrs:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == AArch64_WZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (SUBWrs GPR32:$dst, WZR, GPR32:$src, 0)
      AsmString = "neg $\x01, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == AArch64_WZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2)) {
      // (SUBWrs GPR32:$dst, WZR, GPR32:$src, arith_shift32:$shift)
      AsmString = "neg $\x01, $\x03$\xFF\x04\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (SUBWrs GPR32:$dst, GPR32:$src1, GPR32:$src2, 0)
      AsmString = "sub $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_SUBWrx:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32sponlyRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 16) {
      // (SUBWrx GPR32sponly:$dst, GPR32sp:$src1, GPR32:$src2, 16)
      AsmString = "sub $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32sponlyRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 16) {
      // (SUBWrx GPR32sp:$dst, GPR32sponly:$src1, GPR32:$src2, 16)
      AsmString = "sub $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_SUBXrs:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == AArch64_XZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (SUBXrs GPR64:$dst, XZR, GPR64:$src, 0)
      AsmString = "neg $\x01, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == AArch64_XZR &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2)) {
      // (SUBXrs GPR64:$dst, XZR, GPR64:$src, arith_shift64:$shift)
      AsmString = "neg $\x01, $\x03$\xFF\x04\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (SUBXrs GPR64:$dst, GPR64:$src1, GPR64:$src2, 0)
      AsmString = "sub $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_SUBXrx64:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64sponlyRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 24) {
      // (SUBXrx64 GPR64sponly:$dst, GPR64sp:$src1, GPR64:$src2, 24)
      AsmString = "sub $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64spRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64sponlyRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 24) {
      // (SUBXrx64 GPR64sp:$dst, GPR64sponly:$src1, GPR64:$src2, 24)
      AsmString = "sub $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_SYSxt:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_getReg(MCInst_getOperand(MI, 4)) == AArch64_XZR) {
      // (SYSxt imm0_7:$op1, sys_cr_op:$Cn, sys_cr_op:$Cm, imm0_7:$op2, XZR)
      AsmString = "sys $\x01, $\xFF\x02\x17, $\xFF\x03\x17, $\x04";
      break;
    }
    return NULL;
  case AArch64_UBFMWri:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 31) {
      // (UBFMWri GPR32:$dst, GPR32:$src, imm0_31:$shift, 31)
      AsmString = "lsr $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 7) {
      // (UBFMWri GPR32:$dst, GPR32:$src, 0, 7)
      AsmString = "uxtb $\x01, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 15) {
      // (UBFMWri GPR32:$dst, GPR32:$src, 0, 15)
      AsmString = "uxth $\x01, $\x02";
      break;
    }
    return NULL;
  case AArch64_UBFMXri:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 63) {
      // (UBFMXri GPR64:$dst, GPR64:$src, imm0_63:$shift, 63)
      AsmString = "lsr $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 7) {
      // (UBFMXri GPR64:$dst, GPR64:$src, 0, 7)
      AsmString = "uxtb $\x01, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 15) {
      // (UBFMXri GPR64:$dst, GPR64:$src, 0, 15)
      AsmString = "uxth $\x01, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 31) {
      // (UBFMXri GPR64:$dst, GPR64:$src, 0, 31)
      AsmString = "uxtw $\x01, $\x02";
      break;
    }
    return NULL;
  case AArch64_UMADDLrrr:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (UMADDLrrr GPR64:$dst, GPR32:$src1, GPR32:$src2, XZR)
      AsmString = "umull $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case AArch64_UMOVvi32:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1)) {
      // (UMOVvi32 GPR32:$dst, V128:$src, VectorIndexS:$idx)
      AsmString = "mov	$\x01, $\xFF\x02\x06.s$\xFF\x03\x09";
      break;
    }
    return NULL;
  case AArch64_UMOVvi64:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_FPR128RegClassID, 1)) {
      // (UMOVvi64 GPR64:$dst, V128:$src, VectorIndexD:$idx)
      AsmString = "mov	$\x01, $\xFF\x02\x06.d$\xFF\x03\x09";
      break;
    }
    return NULL;
  case AArch64_UMSUBLrrr:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(AArch64_GPR32RegClassID, 2) &&
        MCOperand_getReg(MCInst_getOperand(MI, 3)) == AArch64_XZR) {
      // (UMSUBLrrr GPR64:$dst, GPR32:$src1, GPR32:$src2, XZR)
      AsmString = "umnegl $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  }

  tmp = cs_strdup(AsmString);
  AsmMnem = tmp;
  for(AsmOps = tmp; *AsmOps; AsmOps++) {
    if (*AsmOps == ' ' || *AsmOps == '\t') {
      *AsmOps = '\0';
      AsmOps++;
      break;
    }
  }
  SStream_concat0(OS, AsmMnem);
  if (*AsmOps) {
    SStream_concat0(OS, "\t");
    for (c = AsmOps; *c; c++) {
      if (*c == '[') {
        SStream_concat0(OS, "[");
        set_mem_access(MI, true);
      }
      else if (*c == ']') {
        SStream_concat0(OS, "]");
        set_mem_access(MI, false);
      }
      else if (*c == '$') {
        c += 1;
        if (*c == (char)0xff) {
          c += 1;
          OpIdx = *c - 1;
          c += 1;
          PrintMethodIdx = *c - 1;
          printCustomAliasOperand(MI, OpIdx, PrintMethodIdx, OS, MRI);
        } else
          printOperand(MI, *c - 1, OS);
      } else {
        SStream_concat(OS, "%c", *c);
      }
    }
  }
  return tmp;
}

#endif // PRINT_ALIAS_INSTR
