/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Target Instruction Enum Values                                              *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine */
/* By <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2015 */


#ifdef GET_INSTRINFO_ENUM
#undef GET_INSTRINFO_ENUM

enum {
    XCore_PHI	= 0,
    XCore_INLINEASM	= 1,
    XCore_CFI_INSTRUCTION	= 2,
    XCore_EH_LABEL	= 3,
    XCore_GC_LABEL	= 4,
    XCore_KILL	= 5,
    XCore_EXTRACT_SUBREG	= 6,
    XCore_INSERT_SUBREG	= 7,
    XCore_IMPLICIT_DEF	= 8,
    XCore_SUBREG_TO_REG	= 9,
    XCore_COPY_TO_REGCLASS	= 10,
    XCore_DBG_VALUE	= 11,
    XCore_REG_SEQUENCE	= 12,
    XCore_COPY	= 13,
    XCore_BUNDLE	= 14,
    XCore_LIFETIME_START	= 15,
    XCore_LIFETIME_END	= 16,
    XCore_STACKMAP	= 17,
    XCore_PATCHPOINT	= 18,
    XCore_LOAD_STACK_GUARD	= 19,
    XCore_STATEPOINT	= 20,
    XCore_FRAME_ALLOC	= 21,
    XCore_ADD_2rus	= 22,
    XCore_ADD_3r	= 23,
    XCore_ADJCALLSTACKDOWN	= 24,
    XCore_ADJCALLSTACKUP	= 25,
    XCore_ANDNOT_2r	= 26,
    XCore_AND_3r	= 27,
    XCore_ASHR_l2rus	= 28,
    XCore_ASHR_l3r	= 29,
    XCore_BAU_1r	= 30,
    XCore_BITREV_l2r	= 31,
    XCore_BLACP_lu10	= 32,
    XCore_BLACP_u10	= 33,
    XCore_BLAT_lu6	= 34,
    XCore_BLAT_u6	= 35,
    XCore_BLA_1r	= 36,
    XCore_BLRB_lu10	= 37,
    XCore_BLRB_u10	= 38,
    XCore_BLRF_lu10	= 39,
    XCore_BLRF_u10	= 40,
    XCore_BRBF_lru6	= 41,
    XCore_BRBF_ru6	= 42,
    XCore_BRBT_lru6	= 43,
    XCore_BRBT_ru6	= 44,
    XCore_BRBU_lu6	= 45,
    XCore_BRBU_u6	= 46,
    XCore_BRFF_lru6	= 47,
    XCore_BRFF_ru6	= 48,
    XCore_BRFT_lru6	= 49,
    XCore_BRFT_ru6	= 50,
    XCore_BRFU_lu6	= 51,
    XCore_BRFU_u6	= 52,
    XCore_BRU_1r	= 53,
    XCore_BR_JT	= 54,
    XCore_BR_JT32	= 55,
    XCore_BYTEREV_l2r	= 56,
    XCore_CHKCT_2r	= 57,
    XCore_CHKCT_rus	= 58,
    XCore_CLRE_0R	= 59,
    XCore_CLRPT_1R	= 60,
    XCore_CLRSR_branch_lu6	= 61,
    XCore_CLRSR_branch_u6	= 62,
    XCore_CLRSR_lu6	= 63,
    XCore_CLRSR_u6	= 64,
    XCore_CLZ_l2r	= 65,
    XCore_CRC8_l4r	= 66,
    XCore_CRC_l3r	= 67,
    XCore_DCALL_0R	= 68,
    XCore_DENTSP_0R	= 69,
    XCore_DGETREG_1r	= 70,
    XCore_DIVS_l3r	= 71,
    XCore_DIVU_l3r	= 72,
    XCore_DRESTSP_0R	= 73,
    XCore_DRET_0R	= 74,
    XCore_ECALLF_1r	= 75,
    XCore_ECALLT_1r	= 76,
    XCore_EDU_1r	= 77,
    XCore_EEF_2r	= 78,
    XCore_EET_2r	= 79,
    XCore_EEU_1r	= 80,
    XCore_EH_RETURN	= 81,
    XCore_ENDIN_2r	= 82,
    XCore_ENTSP_lu6	= 83,
    XCore_ENTSP_u6	= 84,
    XCore_EQ_2rus	= 85,
    XCore_EQ_3r	= 86,
    XCore_EXTDP_lu6	= 87,
    XCore_EXTDP_u6	= 88,
    XCore_EXTSP_lu6	= 89,
    XCore_EXTSP_u6	= 90,
    XCore_FRAME_TO_ARGS_OFFSET	= 91,
    XCore_FREER_1r	= 92,
    XCore_FREET_0R	= 93,
    XCore_GETD_l2r	= 94,
    XCore_GETED_0R	= 95,
    XCore_GETET_0R	= 96,
    XCore_GETID_0R	= 97,
    XCore_GETKEP_0R	= 98,
    XCore_GETKSP_0R	= 99,
    XCore_GETN_l2r	= 100,
    XCore_GETPS_l2r	= 101,
    XCore_GETR_rus	= 102,
    XCore_GETSR_lu6	= 103,
    XCore_GETSR_u6	= 104,
    XCore_GETST_2r	= 105,
    XCore_GETTS_2r	= 106,
    XCore_INCT_2r	= 107,
    XCore_INITCP_2r	= 108,
    XCore_INITDP_2r	= 109,
    XCore_INITLR_l2r	= 110,
    XCore_INITPC_2r	= 111,
    XCore_INITSP_2r	= 112,
    XCore_INPW_l2rus	= 113,
    XCore_INSHR_2r	= 114,
    XCore_INT_2r	= 115,
    XCore_IN_2r	= 116,
    XCore_Int_MemBarrier	= 117,
    XCore_KCALL_1r	= 118,
    XCore_KCALL_lu6	= 119,
    XCore_KCALL_u6	= 120,
    XCore_KENTSP_lu6	= 121,
    XCore_KENTSP_u6	= 122,
    XCore_KRESTSP_lu6	= 123,
    XCore_KRESTSP_u6	= 124,
    XCore_KRET_0R	= 125,
    XCore_LADD_l5r	= 126,
    XCore_LD16S_3r	= 127,
    XCore_LD8U_3r	= 128,
    XCore_LDA16B_l3r	= 129,
    XCore_LDA16F_l3r	= 130,
    XCore_LDAPB_lu10	= 131,
    XCore_LDAPB_u10	= 132,
    XCore_LDAPF_lu10	= 133,
    XCore_LDAPF_lu10_ba	= 134,
    XCore_LDAPF_u10	= 135,
    XCore_LDAWB_l2rus	= 136,
    XCore_LDAWB_l3r	= 137,
    XCore_LDAWCP_lu6	= 138,
    XCore_LDAWCP_u6	= 139,
    XCore_LDAWDP_lru6	= 140,
    XCore_LDAWDP_ru6	= 141,
    XCore_LDAWFI	= 142,
    XCore_LDAWF_l2rus	= 143,
    XCore_LDAWF_l3r	= 144,
    XCore_LDAWSP_lru6	= 145,
    XCore_LDAWSP_ru6	= 146,
    XCore_LDC_lru6	= 147,
    XCore_LDC_ru6	= 148,
    XCore_LDET_0R	= 149,
    XCore_LDIVU_l5r	= 150,
    XCore_LDSED_0R	= 151,
    XCore_LDSPC_0R	= 152,
    XCore_LDSSR_0R	= 153,
    XCore_LDWCP_lru6	= 154,
    XCore_LDWCP_lu10	= 155,
    XCore_LDWCP_ru6	= 156,
    XCore_LDWCP_u10	= 157,
    XCore_LDWDP_lru6	= 158,
    XCore_LDWDP_ru6	= 159,
    XCore_LDWFI	= 160,
    XCore_LDWSP_lru6	= 161,
    XCore_LDWSP_ru6	= 162,
    XCore_LDW_2rus	= 163,
    XCore_LDW_3r	= 164,
    XCore_LMUL_l6r	= 165,
    XCore_LSS_3r	= 166,
    XCore_LSUB_l5r	= 167,
    XCore_LSU_3r	= 168,
    XCore_MACCS_l4r	= 169,
    XCore_MACCU_l4r	= 170,
    XCore_MJOIN_1r	= 171,
    XCore_MKMSK_2r	= 172,
    XCore_MKMSK_rus	= 173,
    XCore_MSYNC_1r	= 174,
    XCore_MUL_l3r	= 175,
    XCore_NEG	= 176,
    XCore_NOT	= 177,
    XCore_OR_3r	= 178,
    XCore_OUTCT_2r	= 179,
    XCore_OUTCT_rus	= 180,
    XCore_OUTPW_l2rus	= 181,
    XCore_OUTSHR_2r	= 182,
    XCore_OUTT_2r	= 183,
    XCore_OUT_2r	= 184,
    XCore_PEEK_2r	= 185,
    XCore_REMS_l3r	= 186,
    XCore_REMU_l3r	= 187,
    XCore_RETSP_lu6	= 188,
    XCore_RETSP_u6	= 189,
    XCore_SELECT_CC	= 190,
    XCore_SETCLK_l2r	= 191,
    XCore_SETCP_1r	= 192,
    XCore_SETC_l2r	= 193,
    XCore_SETC_lru6	= 194,
    XCore_SETC_ru6	= 195,
    XCore_SETDP_1r	= 196,
    XCore_SETD_2r	= 197,
    XCore_SETEV_1r	= 198,
    XCore_SETKEP_0R	= 199,
    XCore_SETN_l2r	= 200,
    XCore_SETPSC_2r	= 201,
    XCore_SETPS_l2r	= 202,
    XCore_SETPT_2r	= 203,
    XCore_SETRDY_l2r	= 204,
    XCore_SETSP_1r	= 205,
    XCore_SETSR_branch_lu6	= 206,
    XCore_SETSR_branch_u6	= 207,
    XCore_SETSR_lu6	= 208,
    XCore_SETSR_u6	= 209,
    XCore_SETTW_l2r	= 210,
    XCore_SETV_1r	= 211,
    XCore_SEXT_2r	= 212,
    XCore_SEXT_rus	= 213,
    XCore_SHL_2rus	= 214,
    XCore_SHL_3r	= 215,
    XCore_SHR_2rus	= 216,
    XCore_SHR_3r	= 217,
    XCore_SSYNC_0r	= 218,
    XCore_ST16_l3r	= 219,
    XCore_ST8_l3r	= 220,
    XCore_STET_0R	= 221,
    XCore_STSED_0R	= 222,
    XCore_STSPC_0R	= 223,
    XCore_STSSR_0R	= 224,
    XCore_STWDP_lru6	= 225,
    XCore_STWDP_ru6	= 226,
    XCore_STWFI	= 227,
    XCore_STWSP_lru6	= 228,
    XCore_STWSP_ru6	= 229,
    XCore_STW_2rus	= 230,
    XCore_STW_l3r	= 231,
    XCore_SUB_2rus	= 232,
    XCore_SUB_3r	= 233,
    XCore_SYNCR_1r	= 234,
    XCore_TESTCT_2r	= 235,
    XCore_TESTLCL_l2r	= 236,
    XCore_TESTWCT_2r	= 237,
    XCore_TSETMR_2r	= 238,
    XCore_TSETR_3r	= 239,
    XCore_TSTART_1R	= 240,
    XCore_WAITEF_1R	= 241,
    XCore_WAITET_1R	= 242,
    XCore_WAITEU_0R	= 243,
    XCore_XOR_l3r	= 244,
    XCore_ZEXT_2r	= 245,
    XCore_ZEXT_rus	= 246,
    XCore_INSTRUCTION_LIST_END = 247
};

#endif // GET_INSTRINFO_ENUM
