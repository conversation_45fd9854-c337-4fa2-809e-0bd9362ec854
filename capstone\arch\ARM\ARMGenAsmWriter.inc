/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Assembly Writer Source Fragment                                             *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine, http://www.capstone-engine.org */
/* By <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2015 */

/// printInstruction - This method is automatically generated by tablegen
/// from the instruction set description.
static void printInstruction(MCInst *MI, SStream *O, MCRegisterInfo *MRI)
{
  static const uint32_t OpInfo[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    1341U,	// DBG_VALUE
    0U,	// REG_SEQUENCE
    0U,	// COPY
    1334U,	// BUNDLE
    1351U,	// LIFETIME_START
    1321U,	// LIFETIME_END
    0U,	// STACKMAP
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    0U,	// STATEPOINT
    0U,	// FRAME_ALLOC
    0U,	// ABS
    5780U,	// ADCri
    5780U,	// ADCrr
    9876U,	// ADCrsi
    13972U,	// ADCrsr
    0U,	// ADDSri
    0U,	// ADDSrr
    0U,	// ADDSrsi
    0U,	// ADDSrsr
    5841U,	// ADDri
    5841U,	// ADDrr
    9937U,	// ADDrsi
    14033U,	// ADDrsr
    0U,	// ADJCALLSTACKDOWN
    0U,	// ADJCALLSTACKUP
    18818U,	// ADR
    1090671288U,	// AESD
    1090671296U,	// AESE
    1107448485U,	// AESIMC
    1107448495U,	// AESMC
    5894U,	// ANDri
    5894U,	// ANDrr
    9990U,	// ANDrsi
    14086U,	// ANDrsr
    268720U,	// ASRi
    268720U,	// ASRr
    0U,	// B
    0U,	// BCCZi64
    0U,	// BCCi64
    26268U,	// BFC
    30689U,	// BFI
    5793U,	// BICri
    5793U,	// BICrr
    9889U,	// BICrsi
    13985U,	// BICrsr
    414547U,	// BKPT
    414527U,	// BL
    414594U,	// BLX
    1073777598U,	// BLX_pred
    414594U,	// BLXi
    1073776690U,	// BL_pred
    0U,	// BMOVPCB_CALL
    0U,	// BMOVPCRX_CALL
    0U,	// BR_JTadd
    0U,	// BR_JTm
    0U,	// BR_JTr
    414590U,	// BX
    1073776627U,	// BXJ
    0U,	// BX_CALL
    564058U,	// BX_RET
    1073777498U,	// BX_pred
    1073776047U,	// Bcc
    2197858637U,	// CDP
    67809687U,	// CDP2
    2984U,	// CLREX
    19434U,	// CLZ
    18675U,	// CMNri
    18675U,	// CMNzrr
    26867U,	// CMNzrsi
    30963U,	// CMNzrsr
    18775U,	// CMPri
    18775U,	// CMPrr
    26967U,	// CMPrsi
    31063U,	// CMPrsr
    0U,	// CONSTPOOL_ENTRY
    0U,	// COPY_STRUCT_BYVAL_I32
    414531U,	// CPS1p
    1157679622U,	// CPS2p
    83937798U,	// CPS3p
    33706710U,	// CRC32B
    33706718U,	// CRC32CB
    33706787U,	// CRC32CH
    33706863U,	// CRC32CW
    33706779U,	// CRC32H
    33706855U,	// CRC32W
    1073776486U,	// DBG
    54005U,	// DMB
    54010U,	// DSB
    6558U,	// EORri
    6558U,	// EORrr
    10654U,	// EORrsi
    14750U,	// EORrsr
    432735U,	// ERET
    3322694403U,	// FCONSTD
    3322825475U,	// FCONSTS
    33573717U,	// FLDMXDB_UPD
    35614U,	// FLDMXIA
    33573662U,	// FLDMXIA_UPD
    1088010U,	// FMSTAT
    33573725U,	// FSTMXDB_UPD
    35622U,	// FSTMXIA
    33573670U,	// FSTMXIA_UPD
    1073777302U,	// HINT
    414542U,	// HLT
    414468U,	// HVC
    58111U,	// ISB
    117766788U,	// ITasm
    0U,	// Int_eh_sjlj_dispatchsetup
    0U,	// Int_eh_sjlj_longjmp
    0U,	// Int_eh_sjlj_setjmp
    0U,	// Int_eh_sjlj_setjmp_nofp
    17755U,	// LDA
    17836U,	// LDAB
    19350U,	// LDAEX
    18036U,	// LDAEXB
    134235936U,	// LDAEXD
    18373U,	// LDAEXH
    18293U,	// LDAH
    152220465U,	// LDC2L_OFFSET
    1242739505U,	// LDC2L_OPTION
    2316481329U,	// LDC2L_POST
    185774897U,	// LDC2L_PRE
    152220030U,	// LDC2_OFFSET
    1242739070U,	// LDC2_OPTION
    2316480894U,	// LDC2_POST
    185774462U,	// LDC2_PRE
    3271587899U,	// LDCL_OFFSET
    3271587899U,	// LDCL_OPTION
    3271587899U,	// LDCL_POST
    3271587899U,	// LDCL_PRE
    3271587480U,	// LDC_OFFSET
    3271587480U,	// LDC_OPTION
    3271587480U,	// LDC_POST
    3271587480U,	// LDC_PRE
    34143U,	// LDMDA
    33572191U,	// LDMDA_UPD
    34270U,	// LDMDB
    33572318U,	// LDMDB_UPD
    35010U,	// LDMIA
    0U,	// LDMIA_RET
    33573058U,	// LDMIA_UPD
    34289U,	// LDMIB
    33572337U,	// LDMIB_UPD
    281164U,	// LDRBT_POST
    68172U,	// LDRBT_POST_IMM
    68172U,	// LDRBT_POST_REG
    67083U,	// LDRB_POST_IMM
    67083U,	// LDRB_POST_REG
    30219U,	// LDRB_PRE_IMM
    67083U,	// LDRB_PRE_REG
    26123U,	// LDRBi12
    30219U,	// LDRBrs
    67338U,	// LDRD
    42762U,	// LDRD_POST
    42762U,	// LDRD_PRE
    19362U,	// LDREX
    18050U,	// LDREXB
    134235950U,	// LDREXD
    18387U,	// LDREXH
    30624U,	// LDRH
    31343U,	// LDRHTi
    68207U,	// LDRHTr
    67488U,	// LDRH_POST
    67488U,	// LDRH_PRE
    0U,	// LDRLIT_ga_abs
    0U,	// LDRLIT_ga_pcrel
    0U,	// LDRLIT_ga_pcrel_ldr
    30237U,	// LDRSB
    31320U,	// LDRSBTi
    68184U,	// LDRSBTr
    67101U,	// LDRSB_POST
    67101U,	// LDRSB_PRE
    30634U,	// LDRSH
    31355U,	// LDRSHTi
    68219U,	// LDRSHTr
    67498U,	// LDRSH_POST
    67498U,	// LDRSH_PRE
    281243U,	// LDRT_POST
    68251U,	// LDRT_POST_IMM
    68251U,	// LDRT_POST_REG
    67975U,	// LDR_POST_IMM
    67975U,	// LDR_POST_REG
    31111U,	// LDR_PRE_IMM
    67975U,	// LDR_PRE_REG
    27015U,	// LDRcp
    27015U,	// LDRi12
    31111U,	// LDRrs
    0U,	// LEApcrel
    0U,	// LEApcrelJT
    268445U,	// LSLi
    268445U,	// LSLr
    268727U,	// LSRi
    268727U,	// LSRr
    2197858686U,	// MCR
    17478045U,	// MCR2
    2197883302U,	// MCRR
    17478051U,	// MCRR2
    9607U,	// MLA
    0U,	// MLAv5
    31209U,	// MLS
    0U,	// MOVCCi
    0U,	// MOVCCi16
    0U,	// MOVCCi32imm
    0U,	// MOVCCr
    0U,	// MOVCCsi
    0U,	// MOVCCsr
    1350404U,	// MOVPCLR
    0U,	// MOVPCRX
    27345U,	// MOVTi16
    0U,	// MOVTi16_ga_pcrel
    0U,	// MOV_ga_pcrel
    0U,	// MOV_ga_pcrel_ldr
    72452U,	// MOVi
    19225U,	// MOVi16
    0U,	// MOVi16_ga_pcrel
    0U,	// MOVi32imm
    72452U,	// MOVr
    72452U,	// MOVr_TC
    6916U,	// MOVsi
    11012U,	// MOVsr
    0U,	// MOVsra_flag
    0U,	// MOVsrl_flag
    201369257U,	// MRC
    74116U,	// MRC2
    2197882541U,	// MRRC
    17478026U,	// MRRC2
    35339U,	// MRS
    18955U,	// MRSbanked
    1073777163U,	// MRSsys
    2365606332U,	// MSR
    234899900U,	// MSRbanked
    2365606332U,	// MSRi
    6317U,	// MUL
    0U,	// MULv5
    0U,	// MVNCCi
    71991U,	// MVNi
    71991U,	// MVNr
    6455U,	// MVNsi
    10551U,	// MVNsr
    6572U,	// ORRri
    6572U,	// ORRrr
    10668U,	// ORRrsi
    14764U,	// ORRrsr
    0U,	// PICADD
    0U,	// PICLDR
    0U,	// PICLDRB
    0U,	// PICLDRH
    0U,	// PICLDRSB
    0U,	// PICLDRSH
    0U,	// PICSTR
    0U,	// PICSTRB
    0U,	// PICSTRH
    31287U,	// PKHBT
    30250U,	// PKHTB
    78712U,	// PLDWi12
    82808U,	// PLDWrs
    78601U,	// PLDi12
    82697U,	// PLDrs
    78636U,	// PLIi12
    82732U,	// PLIrs
    26345U,	// QADD
    25776U,	// QADD16
    25879U,	// QADD8
    27603U,	// QASX
    26319U,	// QDADD
    26191U,	// QDSUB
    27462U,	// QSAX
    26204U,	// QSUB
    25738U,	// QSUB16
    25840U,	// QSUB8
    19074U,	// RBIT
    19184U,	// REV
    17620U,	// REV16
    18357U,	// REVSH
    414408U,	// RFEDA
    1462984U,	// RFEDA_UPD
    414439U,	// RFEDB
    1463015U,	// RFEDB_UPD
    414415U,	// RFEIA
    1462991U,	// RFEIA_UPD
    414446U,	// RFEIB
    1463022U,	// RFEIB_UPD
    268706U,	// RORi
    268706U,	// RORr
    0U,	// RRX
    334786U,	// RRXi
    0U,	// RSBSri
    0U,	// RSBSrsi
    0U,	// RSBSrsr
    5663U,	// RSBri
    5663U,	// RSBrr
    9759U,	// RSBrsi
    13855U,	// RSBrsr
    5810U,	// RSCri
    5810U,	// RSCrr
    9906U,	// RSCrsi
    14002U,	// RSCrsr
    25783U,	// SADD16
    25885U,	// SADD8
    27608U,	// SASX
    5776U,	// SBCri
    5776U,	// SBCrr
    9872U,	// SBCrsi
    13968U,	// SBCrsr
    31668U,	// SBFX
    27380U,	// SDIV
    26712U,	// SEL
    86798U,	// SETEND
    16928834U,	// SHA1C
    1107447884U,	// SHA1H
    16928866U,	// SHA1M
    16928876U,	// SHA1P
    16928769U,	// SHA1SU0
    1090670619U,	// SHA1SU1
    16928854U,	// SHA256H
    16928821U,	// SHA256H2
    1090670605U,	// SHA256SU0
    16928807U,	// SHA256SU1
    25759U,	// SHADD16
    25864U,	// SHADD8
    27590U,	// SHASX
    27449U,	// SHSAX
    25721U,	// SHSUB16
    25825U,	// SHSUB8
    1073776293U,	// SMC
    30141U,	// SMLABB
    31280U,	// SMLABT
    30398U,	// SMLAD
    31594U,	// SMLADX
    92190U,	// SMLAL
    30148U,	// SMLALBB
    31293U,	// SMLALBT
    30451U,	// SMLALD
    31608U,	// SMLALDX
    30256U,	// SMLALTB
    31415U,	// SMLALTT
    0U,	// SMLALv5
    30243U,	// SMLATB
    31408U,	// SMLATT
    30310U,	// SMLAWB
    31446U,	// SMLAWT
    30484U,	// SMLSD
    31624U,	// SMLSDX
    30462U,	// SMLSLD
    31616U,	// SMLSLDX
    30085U,	// SMMLA
    31095U,	// SMMLAR
    31207U,	// SMMLS
    31156U,	// SMMLSR
    26795U,	// SMMUL
    27030U,	// SMMULR
    26308U,	// SMUAD
    27505U,	// SMUADX
    26060U,	// SMULBB
    27205U,	// SMULBT
    10370U,	// SMULL
    0U,	// SMULLv5
    26168U,	// SMULTB
    27327U,	// SMULTT
    26221U,	// SMULWB
    27357U,	// SMULWT
    26394U,	// SMUSD
    27535U,	// SMUSDX
    0U,	// SPACE
    414658U,	// SRSDA
    414610U,	// SRSDA_UPD
    414680U,	// SRSDB
    414634U,	// SRSDB_UPD
    414669U,	// SRSIA
    414622U,	// SRSIA_UPD
    414691U,	// SRSIB
    414646U,	// SRSIB_UPD
    31270U,	// SSAT
    25797U,	// SSAT16
    27467U,	// SSAX
    25745U,	// SSUB16
    25846U,	// SSUB8
    152220472U,	// STC2L_OFFSET
    1242739512U,	// STC2L_OPTION
    2316481336U,	// STC2L_POST
    185774904U,	// STC2L_PRE
    152220049U,	// STC2_OFFSET
    1242739089U,	// STC2_OPTION
    2316480913U,	// STC2_POST
    185774481U,	// STC2_PRE
    3271587904U,	// STCL_OFFSET
    3271587904U,	// STCL_OPTION
    3271587904U,	// STCL_POST
    3271587904U,	// STCL_PRE
    3271587510U,	// STC_OFFSET
    3271587510U,	// STC_OPTION
    3271587510U,	// STC_POST
    3271587510U,	// STC_PRE
    18599U,	// STL
    17917U,	// STLB
    27548U,	// STLEX
    26235U,	// STLEXB
    26407U,	// STLEXD
    26572U,	// STLEXH
    18314U,	// STLH
    34149U,	// STMDA
    33572197U,	// STMDA_UPD
    34277U,	// STMDB
    33572325U,	// STMDB_UPD
    35014U,	// STMIA
    33573062U,	// STMIA_UPD
    34295U,	// STMIB
    33572343U,	// STMIB_UPD
    281170U,	// STRBT_POST
    33622610U,	// STRBT_POST_IMM
    33622610U,	// STRBT_POST_REG
    33621520U,	// STRB_POST_IMM
    33621520U,	// STRB_POST_REG
    33584656U,	// STRB_PRE_IMM
    33621520U,	// STRB_PRE_REG
    26128U,	// STRBi12
    0U,	// STRBi_preidx
    0U,	// STRBr_preidx
    30224U,	// STRBrs
    67343U,	// STRD
    33597199U,	// STRD_POST
    33597199U,	// STRD_PRE
    27566U,	// STREX
    26249U,	// STREXB
    26421U,	// STREXD
    26586U,	// STREXH
    30629U,	// STRH
    33585781U,	// STRHTi
    33622645U,	// STRHTr
    33621925U,	// STRH_POST
    33621925U,	// STRH_PRE
    0U,	// STRH_preidx
    281254U,	// STRT_POST
    33622694U,	// STRT_POST_IMM
    33622694U,	// STRT_POST_REG
    33622472U,	// STR_POST_IMM
    33622472U,	// STR_POST_REG
    33585608U,	// STR_PRE_IMM
    33622472U,	// STR_PRE_REG
    27080U,	// STRi12
    0U,	// STRi_preidx
    0U,	// STRr_preidx
    31176U,	// STRrs
    0U,	// SUBS_PC_LR
    0U,	// SUBSri
    0U,	// SUBSrr
    0U,	// SUBSrsi
    0U,	// SUBSrsr
    5713U,	// SUBri
    5713U,	// SUBrr
    9809U,	// SUBrsi
    13905U,	// SUBrsr
    1073776314U,	// SVC
    26981U,	// SWP
    26118U,	// SWPB
    30129U,	// SXTAB
    29787U,	// SXTAB16
    30586U,	// SXTAH
    26181U,	// SXTB
    25707U,	// SXTB16
    26555U,	// SXTH
    0U,	// TAILJMPd
    0U,	// TAILJMPr
    0U,	// TCRETURNdi
    0U,	// TCRETURNri
    18803U,	// TEQri
    18803U,	// TEQrr
    26995U,	// TEQrsi
    31091U,	// TEQrsr
    0U,	// TPsoft
    2376U,	// TRAP
    2376U,	// TRAPNaCl
    19116U,	// TSTri
    19116U,	// TSTrr
    27308U,	// TSTrsi
    31404U,	// TSTrsr
    25790U,	// UADD16
    25891U,	// UADD8
    27613U,	// UASX
    31673U,	// UBFX
    414486U,	// UDF
    27385U,	// UDIV
    25767U,	// UHADD16
    25871U,	// UHADD8
    27596U,	// UHASX
    27455U,	// UHSAX
    25729U,	// UHSUB16
    25832U,	// UHSUB8
    30723U,	// UMAAL
    92196U,	// UMLAL
    0U,	// UMLALv5
    10376U,	// UMULL
    0U,	// UMULLv5
    25775U,	// UQADD16
    25878U,	// UQADD8
    27602U,	// UQASX
    27461U,	// UQSAX
    25737U,	// UQSUB16
    25839U,	// UQSUB8
    25858U,	// USAD8
    29914U,	// USADA8
    31275U,	// USAT
    25804U,	// USAT16
    27472U,	// USAX
    25752U,	// USUB16
    25852U,	// USUB8
    30135U,	// UXTAB
    29795U,	// UXTAB16
    30592U,	// UXTAH
    26186U,	// UXTB
    25714U,	// UXTB16
    26560U,	// UXTH
    18380809U,	// VABALsv2i64
    18511881U,	// VABALsv4i32
    18642953U,	// VABALsv8i16
    18774025U,	// VABALuv2i64
    18905097U,	// VABALuv4i32
    19036169U,	// VABALuv8i16
    18642262U,	// VABAsv16i8
    18380118U,	// VABAsv2i32
    18511190U,	// VABAsv4i16
    18380118U,	// VABAsv4i32
    18511190U,	// VABAsv8i16
    18642262U,	// VABAsv8i8
    19035478U,	// VABAuv16i8
    18773334U,	// VABAuv2i32
    18904406U,	// VABAuv4i16
    18773334U,	// VABAuv4i32
    18904406U,	// VABAuv8i16
    19035478U,	// VABAuv8i8
    35153989U,	// VABDLsv2i64
    35285061U,	// VABDLsv4i32
    35416133U,	// VABDLsv8i16
    35547205U,	// VABDLuv2i64
    35678277U,	// VABDLuv4i32
    35809349U,	// VABDLuv8i16
    2249090762U,	// VABDfd
    2249090762U,	// VABDfq
    35415754U,	// VABDsv16i8
    35153610U,	// VABDsv2i32
    35284682U,	// VABDsv4i16
    35153610U,	// VABDsv4i32
    35284682U,	// VABDsv8i16
    35415754U,	// VABDsv8i8
    35808970U,	// VABDuv16i8
    35546826U,	// VABDuv2i32
    35677898U,	// VABDuv4i16
    35546826U,	// VABDuv4i32
    35677898U,	// VABDuv8i16
    35808970U,	// VABDuv8i8
    2248952280U,	// VABSD
    2249083352U,	// VABSS
    2249083352U,	// VABSfd
    2249083352U,	// VABSfq
    1109150168U,	// VABSv16i8
    1108888024U,	// VABSv2i32
    1109019096U,	// VABSv4i16
    1108888024U,	// VABSv4i32
    1109019096U,	// VABSv8i16
    1109150168U,	// VABSv8i8
    2249090876U,	// VACGEd
    2249090876U,	// VACGEq
    2249091684U,	// VACGTd
    2249091684U,	// VACGTq
    2248959726U,	// VADDD
    35940577U,	// VADDHNv2i32
    36071649U,	// VADDHNv4i16
    36202721U,	// VADDHNv8i8
    35154002U,	// VADDLsv2i64
    35285074U,	// VADDLsv4i32
    35416146U,	// VADDLsv8i16
    35547218U,	// VADDLuv2i64
    35678290U,	// VADDLuv4i32
    35809362U,	// VADDLuv8i16
    2249090798U,	// VADDS
    35154702U,	// VADDWsv2i64
    35285774U,	// VADDWsv4i32
    35416846U,	// VADDWsv8i16
    35547918U,	// VADDWuv2i64
    35678990U,	// VADDWuv4i32
    35810062U,	// VADDWuv8i16
    2249090798U,	// VADDfd
    2249090798U,	// VADDfq
    36333294U,	// VADDv16i8
    35940078U,	// VADDv1i64
    36071150U,	// VADDv2i32
    35940078U,	// VADDv2i64
    36202222U,	// VADDv4i16
    36071150U,	// VADDv4i32
    36202222U,	// VADDv8i16
    36333294U,	// VADDv8i8
    26373U,	// VANDd
    26373U,	// VANDq
    26272U,	// VBICd
    254174880U,	// VBICiv2i32
    254305952U,	// VBICiv4i16
    254174880U,	// VBICiv4i32
    254305952U,	// VBICiv8i16
    26272U,	// VBICq
    30561U,	// VBIFd
    30561U,	// VBIFq
    31367U,	// VBITd
    31367U,	// VBITq
    30868U,	// VBSLd
    30868U,	// VBSLq
    2249091438U,	// VCEQfd
    2249091438U,	// VCEQfq
    36333934U,	// VCEQv16i8
    36071790U,	// VCEQv2i32
    36202862U,	// VCEQv4i16
    36071790U,	// VCEQv4i32
    36202862U,	// VCEQv8i16
    36333934U,	// VCEQv8i8
    3257551214U,	// VCEQzv16i8
    2249083246U,	// VCEQzv2f32
    3257289070U,	// VCEQzv2i32
    2249083246U,	// VCEQzv4f32
    3257420142U,	// VCEQzv4i16
    3257289070U,	// VCEQzv4i32
    3257420142U,	// VCEQzv8i16
    3257551214U,	// VCEQzv8i8
    2249090882U,	// VCGEfd
    2249090882U,	// VCGEfq
    35415874U,	// VCGEsv16i8
    35153730U,	// VCGEsv2i32
    35284802U,	// VCGEsv4i16
    35153730U,	// VCGEsv4i32
    35284802U,	// VCGEsv8i16
    35415874U,	// VCGEsv8i8
    35809090U,	// VCGEuv16i8
    35546946U,	// VCGEuv2i32
    35678018U,	// VCGEuv4i16
    35546946U,	// VCGEuv4i32
    35678018U,	// VCGEuv8i16
    35809090U,	// VCGEuv8i8
    3256633154U,	// VCGEzv16i8
    2249082690U,	// VCGEzv2f32
    3256371010U,	// VCGEzv2i32
    2249082690U,	// VCGEzv4f32
    3256502082U,	// VCGEzv4i16
    3256371010U,	// VCGEzv4i32
    3256502082U,	// VCGEzv8i16
    3256633154U,	// VCGEzv8i8
    2249091690U,	// VCGTfd
    2249091690U,	// VCGTfq
    35416682U,	// VCGTsv16i8
    35154538U,	// VCGTsv2i32
    35285610U,	// VCGTsv4i16
    35154538U,	// VCGTsv4i32
    35285610U,	// VCGTsv8i16
    35416682U,	// VCGTsv8i8
    35809898U,	// VCGTuv16i8
    35547754U,	// VCGTuv2i32
    35678826U,	// VCGTuv4i16
    35547754U,	// VCGTuv4i32
    35678826U,	// VCGTuv8i16
    35809898U,	// VCGTuv8i8
    3256633962U,	// VCGTzv16i8
    2249083498U,	// VCGTzv2f32
    3256371818U,	// VCGTzv2i32
    2249083498U,	// VCGTzv4f32
    3256502890U,	// VCGTzv4i16
    3256371818U,	// VCGTzv4i32
    3256502890U,	// VCGTzv8i16
    3256633962U,	// VCGTzv8i8
    3256633159U,	// VCLEzv16i8
    2249082695U,	// VCLEzv2f32
    3256371015U,	// VCLEzv2i32
    2249082695U,	// VCLEzv4f32
    3256502087U,	// VCLEzv4i16
    3256371015U,	// VCLEzv4i32
    3256502087U,	// VCLEzv8i16
    3256633159U,	// VCLEzv8i8
    1109150178U,	// VCLSv16i8
    1108888034U,	// VCLSv2i32
    1109019106U,	// VCLSv4i16
    1108888034U,	// VCLSv4i32
    1109019106U,	// VCLSv8i16
    1109150178U,	// VCLSv8i8
    3256633996U,	// VCLTzv16i8
    2249083532U,	// VCLTzv2f32
    3256371852U,	// VCLTzv2i32
    2249083532U,	// VCLTzv4f32
    3256502924U,	// VCLTzv4i16
    3256371852U,	// VCLTzv4i32
    3256502924U,	// VCLTzv8i16
    3256633996U,	// VCLTzv8i8
    1110068201U,	// VCLZv16i8
    1109806057U,	// VCLZv2i32
    1109937129U,	// VCLZv4i16
    1109806057U,	// VCLZv4i32
    1109937129U,	// VCLZv8i16
    1110068201U,	// VCLZv8i8
    2248952150U,	// VCMPD
    2248951635U,	// VCMPED
    2249082707U,	// VCMPES
    269256531U,	// VCMPEZD
    269387603U,	// VCMPEZS
    2249083222U,	// VCMPS
    269257046U,	// VCMPZD
    269388118U,	// VCMPZS
    2902673U,	// VCNTd
    2902673U,	// VCNTq
    1107447926U,	// VCVTANSD
    1107447926U,	// VCVTANSQ
    1107447986U,	// VCVTANUD
    1107447986U,	// VCVTANUQ
    1107448234U,	// VCVTASD
    1107447926U,	// VCVTASS
    1107448294U,	// VCVTAUD
    1107447986U,	// VCVTAUS
    3032639U,	// VCVTBDH
    3163711U,	// VCVTBHD
    3294783U,	// VCVTBHS
    3425855U,	// VCVTBSH
    3558092U,	// VCVTDS
    1107447941U,	// VCVTMNSD
    1107447941U,	// VCVTMNSQ
    1107448001U,	// VCVTMNUD
    1107448001U,	// VCVTMNUQ
    1107448249U,	// VCVTMSD
    1107447941U,	// VCVTMSS
    1107448309U,	// VCVTMUD
    1107448001U,	// VCVTMUS
    1107447956U,	// VCVTNNSD
    1107447956U,	// VCVTNNSQ
    1107448016U,	// VCVTNNUD
    1107448016U,	// VCVTNNUQ
    1107448264U,	// VCVTNSD
    1107447956U,	// VCVTNSS
    1107448324U,	// VCVTNUD
    1107448016U,	// VCVTNUS
    1107447971U,	// VCVTPNSD
    1107447971U,	// VCVTPNSQ
    1107448031U,	// VCVTPNUD
    1107448031U,	// VCVTPNUQ
    1107448279U,	// VCVTPSD
    1107447971U,	// VCVTPSS
    1107448339U,	// VCVTPUD
    1107448031U,	// VCVTPUS
    3689164U,	// VCVTSD
    3033798U,	// VCVTTDH
    3164870U,	// VCVTTHD
    3295942U,	// VCVTTHS
    3427014U,	// VCVTTSH
    3427020U,	// VCVTf2h
    289032908U,	// VCVTf2sd
    289032908U,	// VCVTf2sq
    289163980U,	// VCVTf2ud
    289163980U,	// VCVTf2uq
    104491724U,	// VCVTf2xsd
    104491724U,	// VCVTf2xsq
    104622796U,	// VCVTf2xud
    104622796U,	// VCVTf2xuq
    3295948U,	// VCVTh2f
    289295052U,	// VCVTs2fd
    289295052U,	// VCVTs2fq
    289426124U,	// VCVTu2fd
    289426124U,	// VCVTu2fq
    104753868U,	// VCVTxs2fd
    104753868U,	// VCVTxs2fq
    104884940U,	// VCVTxu2fd
    104884940U,	// VCVTxu2fq
    2248960766U,	// VDIVD
    2249091838U,	// VDIVS
    4344159U,	// VDUP16d
    4344159U,	// VDUP16q
    4475231U,	// VDUP32d
    4475231U,	// VDUP32q
    2902367U,	// VDUP8d
    2902367U,	// VDUP8q
    4352351U,	// VDUPLN16d
    4352351U,	// VDUPLN16q
    4483423U,	// VDUPLN32d
    4483423U,	// VDUPLN32q
    2910559U,	// VDUPLN8d
    2910559U,	// VDUPLN8q
    27037U,	// VEORd
    27037U,	// VEORq
    4356836U,	// VEXTd16
    4487908U,	// VEXTd32
    2915044U,	// VEXTd8
    4356836U,	// VEXTq16
    4487908U,	// VEXTq32
    4618980U,	// VEXTq64
    2915044U,	// VEXTq8
    101479830U,	// VFMAD
    101610902U,	// VFMAS
    101610902U,	// VFMAfd
    101610902U,	// VFMAfq
    101480952U,	// VFMSD
    101612024U,	// VFMSS
    101612024U,	// VFMSfd
    101612024U,	// VFMSfq
    101479835U,	// VFNMAD
    101610907U,	// VFNMAS
    101480957U,	// VFNMSD
    101612029U,	// VFNMSS
    4483843U,	// VGETLNi32
    1109027587U,	// VGETLNs16
    1109158659U,	// VGETLNs8
    1109420803U,	// VGETLNu16
    1109551875U,	// VGETLNu8
    35415772U,	// VHADDsv16i8
    35153628U,	// VHADDsv2i32
    35284700U,	// VHADDsv4i16
    35153628U,	// VHADDsv4i32
    35284700U,	// VHADDsv8i16
    35415772U,	// VHADDsv8i8
    35808988U,	// VHADDuv16i8
    35546844U,	// VHADDuv2i32
    35677916U,	// VHADDuv4i16
    35546844U,	// VHADDuv4i32
    35677916U,	// VHADDuv8i16
    35808988U,	// VHADDuv8i8
    35415637U,	// VHSUBsv16i8
    35153493U,	// VHSUBsv2i32
    35284565U,	// VHSUBsv4i16
    35153493U,	// VHSUBsv4i32
    35284565U,	// VHSUBsv8i16
    35415637U,	// VHSUBsv8i8
    35808853U,	// VHSUBuv16i8
    35546709U,	// VHSUBuv2i32
    35677781U,	// VHSUBuv4i16
    35546709U,	// VHSUBuv4i32
    35677781U,	// VHSUBuv8i16
    35808853U,	// VHSUBuv8i8
    2453824494U,	// VLD1DUPd16
    3527570414U,	// VLD1DUPd16wb_fixed
    3527607278U,	// VLD1DUPd16wb_register
    2453955566U,	// VLD1DUPd32
    3527701486U,	// VLD1DUPd32wb_fixed
    3527738350U,	// VLD1DUPd32wb_register
    2452382702U,	// VLD1DUPd8
    3526128622U,	// VLD1DUPd8wb_fixed
    3526165486U,	// VLD1DUPd8wb_register
    2470601710U,	// VLD1DUPq16
    3544347630U,	// VLD1DUPq16wb_fixed
    3544384494U,	// VLD1DUPq16wb_register
    2470732782U,	// VLD1DUPq32
    3544478702U,	// VLD1DUPq32wb_fixed
    3544515566U,	// VLD1DUPq32wb_register
    2469159918U,	// VLD1DUPq8
    3542905838U,	// VLD1DUPq8wb_fixed
    3542942702U,	// VLD1DUPq8wb_register
    4785134U,	// VLD1LNd16
    4813806U,	// VLD1LNd16_UPD
    4916206U,	// VLD1LNd32
    4944878U,	// VLD1LNd32_UPD
    5047278U,	// VLD1LNd8
    5075950U,	// VLD1LNd8_UPD
    4355054U,	// VLD1LNdAsm_16
    4486126U,	// VLD1LNdAsm_32
    2913262U,	// VLD1LNdAsm_8
    4355054U,	// VLD1LNdWB_fixed_Asm_16
    4486126U,	// VLD1LNdWB_fixed_Asm_32
    2913262U,	// VLD1LNdWB_fixed_Asm_8
    4391918U,	// VLD1LNdWB_register_Asm_16
    4522990U,	// VLD1LNdWB_register_Asm_32
    2950126U,	// VLD1LNdWB_register_Asm_8
    0U,	// VLD1LNq16Pseudo
    0U,	// VLD1LNq16Pseudo_UPD
    0U,	// VLD1LNq32Pseudo
    0U,	// VLD1LNq32Pseudo_UPD
    0U,	// VLD1LNq8Pseudo
    0U,	// VLD1LNq8Pseudo_UPD
    2487378926U,	// VLD1d16
    2504156142U,	// VLD1d16Q
    3577902062U,	// VLD1d16Qwb_fixed
    3577938926U,	// VLD1d16Qwb_register
    2520933358U,	// VLD1d16T
    3594679278U,	// VLD1d16Twb_fixed
    3594716142U,	// VLD1d16Twb_register
    3561124846U,	// VLD1d16wb_fixed
    3561161710U,	// VLD1d16wb_register
    2487509998U,	// VLD1d32
    2504287214U,	// VLD1d32Q
    3578033134U,	// VLD1d32Qwb_fixed
    3578069998U,	// VLD1d32Qwb_register
    2521064430U,	// VLD1d32T
    3594810350U,	// VLD1d32Twb_fixed
    3594847214U,	// VLD1d32Twb_register
    3561255918U,	// VLD1d32wb_fixed
    3561292782U,	// VLD1d32wb_register
    2487641070U,	// VLD1d64
    2504418286U,	// VLD1d64Q
    0U,	// VLD1d64QPseudo
    0U,	// VLD1d64QPseudoWB_fixed
    0U,	// VLD1d64QPseudoWB_register
    3578164206U,	// VLD1d64Qwb_fixed
    3578201070U,	// VLD1d64Qwb_register
    2521195502U,	// VLD1d64T
    0U,	// VLD1d64TPseudo
    0U,	// VLD1d64TPseudoWB_fixed
    0U,	// VLD1d64TPseudoWB_register
    3594941422U,	// VLD1d64Twb_fixed
    3594978286U,	// VLD1d64Twb_register
    3561386990U,	// VLD1d64wb_fixed
    3561423854U,	// VLD1d64wb_register
    2485937134U,	// VLD1d8
    2502714350U,	// VLD1d8Q
    3576460270U,	// VLD1d8Qwb_fixed
    3576497134U,	// VLD1d8Qwb_register
    2519491566U,	// VLD1d8T
    3593237486U,	// VLD1d8Twb_fixed
    3593274350U,	// VLD1d8Twb_register
    3559683054U,	// VLD1d8wb_fixed
    3559719918U,	// VLD1d8wb_register
    2537710574U,	// VLD1q16
    3611456494U,	// VLD1q16wb_fixed
    3611493358U,	// VLD1q16wb_register
    2537841646U,	// VLD1q32
    3611587566U,	// VLD1q32wb_fixed
    3611624430U,	// VLD1q32wb_register
    2537972718U,	// VLD1q64
    3611718638U,	// VLD1q64wb_fixed
    3611755502U,	// VLD1q64wb_register
    2536268782U,	// VLD1q8
    3610014702U,	// VLD1q8wb_fixed
    3610051566U,	// VLD1q8wb_register
    2470601754U,	// VLD2DUPd16
    3544347674U,	// VLD2DUPd16wb_fixed
    3544384538U,	// VLD2DUPd16wb_register
    2554487834U,	// VLD2DUPd16x2
    3628233754U,	// VLD2DUPd16x2wb_fixed
    3628270618U,	// VLD2DUPd16x2wb_register
    2470732826U,	// VLD2DUPd32
    3544478746U,	// VLD2DUPd32wb_fixed
    3544515610U,	// VLD2DUPd32wb_register
    2554618906U,	// VLD2DUPd32x2
    3628364826U,	// VLD2DUPd32x2wb_fixed
    3628401690U,	// VLD2DUPd32x2wb_register
    2469159962U,	// VLD2DUPd8
    3542905882U,	// VLD2DUPd8wb_fixed
    3542942746U,	// VLD2DUPd8wb_register
    2553046042U,	// VLD2DUPd8x2
    3626791962U,	// VLD2DUPd8x2wb_fixed
    3626828826U,	// VLD2DUPd8x2wb_register
    4813850U,	// VLD2LNd16
    0U,	// VLD2LNd16Pseudo
    0U,	// VLD2LNd16Pseudo_UPD
    4817946U,	// VLD2LNd16_UPD
    4944922U,	// VLD2LNd32
    0U,	// VLD2LNd32Pseudo
    0U,	// VLD2LNd32Pseudo_UPD
    4949018U,	// VLD2LNd32_UPD
    5075994U,	// VLD2LNd8
    0U,	// VLD2LNd8Pseudo
    0U,	// VLD2LNd8Pseudo_UPD
    5080090U,	// VLD2LNd8_UPD
    4355098U,	// VLD2LNdAsm_16
    4486170U,	// VLD2LNdAsm_32
    2913306U,	// VLD2LNdAsm_8
    4355098U,	// VLD2LNdWB_fixed_Asm_16
    4486170U,	// VLD2LNdWB_fixed_Asm_32
    2913306U,	// VLD2LNdWB_fixed_Asm_8
    4391962U,	// VLD2LNdWB_register_Asm_16
    4523034U,	// VLD2LNdWB_register_Asm_32
    2950170U,	// VLD2LNdWB_register_Asm_8
    4813850U,	// VLD2LNq16
    0U,	// VLD2LNq16Pseudo
    0U,	// VLD2LNq16Pseudo_UPD
    4817946U,	// VLD2LNq16_UPD
    4944922U,	// VLD2LNq32
    0U,	// VLD2LNq32Pseudo
    0U,	// VLD2LNq32Pseudo_UPD
    4949018U,	// VLD2LNq32_UPD
    4355098U,	// VLD2LNqAsm_16
    4486170U,	// VLD2LNqAsm_32
    4355098U,	// VLD2LNqWB_fixed_Asm_16
    4486170U,	// VLD2LNqWB_fixed_Asm_32
    4391962U,	// VLD2LNqWB_register_Asm_16
    4523034U,	// VLD2LNqWB_register_Asm_32
    2571265050U,	// VLD2b16
    3645010970U,	// VLD2b16wb_fixed
    3645047834U,	// VLD2b16wb_register
    2571396122U,	// VLD2b32
    3645142042U,	// VLD2b32wb_fixed
    3645178906U,	// VLD2b32wb_register
    2569823258U,	// VLD2b8
    3643569178U,	// VLD2b8wb_fixed
    3643606042U,	// VLD2b8wb_register
    2537710618U,	// VLD2d16
    3611456538U,	// VLD2d16wb_fixed
    3611493402U,	// VLD2d16wb_register
    2537841690U,	// VLD2d32
    3611587610U,	// VLD2d32wb_fixed
    3611624474U,	// VLD2d32wb_register
    2536268826U,	// VLD2d8
    3610014746U,	// VLD2d8wb_fixed
    3610051610U,	// VLD2d8wb_register
    2504156186U,	// VLD2q16
    0U,	// VLD2q16Pseudo
    0U,	// VLD2q16PseudoWB_fixed
    0U,	// VLD2q16PseudoWB_register
    3577902106U,	// VLD2q16wb_fixed
    3577938970U,	// VLD2q16wb_register
    2504287258U,	// VLD2q32
    0U,	// VLD2q32Pseudo
    0U,	// VLD2q32PseudoWB_fixed
    0U,	// VLD2q32PseudoWB_register
    3578033178U,	// VLD2q32wb_fixed
    3578070042U,	// VLD2q32wb_register
    2502714394U,	// VLD2q8
    0U,	// VLD2q8Pseudo
    0U,	// VLD2q8PseudoWB_fixed
    0U,	// VLD2q8PseudoWB_register
    3576460314U,	// VLD2q8wb_fixed
    3576497178U,	// VLD2q8wb_register
    1078527034U,	// VLD3DUPd16
    0U,	// VLD3DUPd16Pseudo
    0U,	// VLD3DUPd16Pseudo_UPD
    1078555706U,	// VLD3DUPd16_UPD
    1078658106U,	// VLD3DUPd32
    0U,	// VLD3DUPd32Pseudo
    0U,	// VLD3DUPd32Pseudo_UPD
    1078686778U,	// VLD3DUPd32_UPD
    1078789178U,	// VLD3DUPd8
    0U,	// VLD3DUPd8Pseudo
    0U,	// VLD3DUPd8Pseudo_UPD
    1078817850U,	// VLD3DUPd8_UPD
    1514300474U,	// VLD3DUPdAsm_16
    1514431546U,	// VLD3DUPdAsm_32
    1512858682U,	// VLD3DUPdAsm_8
    2588042298U,	// VLD3DUPdWB_fixed_Asm_16
    2588173370U,	// VLD3DUPdWB_fixed_Asm_32
    2586600506U,	// VLD3DUPdWB_fixed_Asm_8
    440562746U,	// VLD3DUPdWB_register_Asm_16
    440693818U,	// VLD3DUPdWB_register_Asm_32
    439120954U,	// VLD3DUPdWB_register_Asm_8
    1078527034U,	// VLD3DUPq16
    1078555706U,	// VLD3DUPq16_UPD
    1078658106U,	// VLD3DUPq32
    1078686778U,	// VLD3DUPq32_UPD
    1078789178U,	// VLD3DUPq8
    1078817850U,	// VLD3DUPq8_UPD
    1531077690U,	// VLD3DUPqAsm_16
    1531208762U,	// VLD3DUPqAsm_32
    1529635898U,	// VLD3DUPqAsm_8
    2604819514U,	// VLD3DUPqWB_fixed_Asm_16
    2604950586U,	// VLD3DUPqWB_fixed_Asm_32
    2603377722U,	// VLD3DUPqWB_fixed_Asm_8
    457339962U,	// VLD3DUPqWB_register_Asm_16
    457471034U,	// VLD3DUPqWB_register_Asm_32
    455898170U,	// VLD3DUPqWB_register_Asm_8
    4817978U,	// VLD3LNd16
    0U,	// VLD3LNd16Pseudo
    0U,	// VLD3LNd16Pseudo_UPD
    4822074U,	// VLD3LNd16_UPD
    4949050U,	// VLD3LNd32
    0U,	// VLD3LNd32Pseudo
    0U,	// VLD3LNd32Pseudo_UPD
    4953146U,	// VLD3LNd32_UPD
    5080122U,	// VLD3LNd8
    0U,	// VLD3LNd8Pseudo
    0U,	// VLD3LNd8Pseudo_UPD
    5084218U,	// VLD3LNd8_UPD
    4355130U,	// VLD3LNdAsm_16
    4486202U,	// VLD3LNdAsm_32
    2913338U,	// VLD3LNdAsm_8
    4355130U,	// VLD3LNdWB_fixed_Asm_16
    4486202U,	// VLD3LNdWB_fixed_Asm_32
    2913338U,	// VLD3LNdWB_fixed_Asm_8
    4391994U,	// VLD3LNdWB_register_Asm_16
    4523066U,	// VLD3LNdWB_register_Asm_32
    2950202U,	// VLD3LNdWB_register_Asm_8
    4817978U,	// VLD3LNq16
    0U,	// VLD3LNq16Pseudo
    0U,	// VLD3LNq16Pseudo_UPD
    4822074U,	// VLD3LNq16_UPD
    4949050U,	// VLD3LNq32
    0U,	// VLD3LNq32Pseudo
    0U,	// VLD3LNq32Pseudo_UPD
    4953146U,	// VLD3LNq32_UPD
    4355130U,	// VLD3LNqAsm_16
    4486202U,	// VLD3LNqAsm_32
    4355130U,	// VLD3LNqWB_fixed_Asm_16
    4486202U,	// VLD3LNqWB_fixed_Asm_32
    4391994U,	// VLD3LNqWB_register_Asm_16
    4523066U,	// VLD3LNqWB_register_Asm_32
    4785210U,	// VLD3d16
    0U,	// VLD3d16Pseudo
    0U,	// VLD3d16Pseudo_UPD
    4813882U,	// VLD3d16_UPD
    4916282U,	// VLD3d32
    0U,	// VLD3d32Pseudo
    0U,	// VLD3d32Pseudo_UPD
    4944954U,	// VLD3d32_UPD
    5047354U,	// VLD3d8
    0U,	// VLD3d8Pseudo
    0U,	// VLD3d8Pseudo_UPD
    5076026U,	// VLD3d8_UPD
    2520933434U,	// VLD3dAsm_16
    2521064506U,	// VLD3dAsm_32
    2519491642U,	// VLD3dAsm_8
    2520933434U,	// VLD3dWB_fixed_Asm_16
    2521064506U,	// VLD3dWB_fixed_Asm_32
    2519491642U,	// VLD3dWB_fixed_Asm_8
    2520937530U,	// VLD3dWB_register_Asm_16
    2521068602U,	// VLD3dWB_register_Asm_32
    2519495738U,	// VLD3dWB_register_Asm_8
    4785210U,	// VLD3q16
    0U,	// VLD3q16Pseudo_UPD
    4813882U,	// VLD3q16_UPD
    0U,	// VLD3q16oddPseudo
    0U,	// VLD3q16oddPseudo_UPD
    4916282U,	// VLD3q32
    0U,	// VLD3q32Pseudo_UPD
    4944954U,	// VLD3q32_UPD
    0U,	// VLD3q32oddPseudo
    0U,	// VLD3q32oddPseudo_UPD
    5047354U,	// VLD3q8
    0U,	// VLD3q8Pseudo_UPD
    5076026U,	// VLD3q8_UPD
    0U,	// VLD3q8oddPseudo
    0U,	// VLD3q8oddPseudo_UPD
    1547854906U,	// VLD3qAsm_16
    1547985978U,	// VLD3qAsm_32
    1546413114U,	// VLD3qAsm_8
    2621596730U,	// VLD3qWB_fixed_Asm_16
    2621727802U,	// VLD3qWB_fixed_Asm_32
    2620154938U,	// VLD3qWB_fixed_Asm_8
    474117178U,	// VLD3qWB_register_Asm_16
    474248250U,	// VLD3qWB_register_Asm_32
    472675386U,	// VLD3qWB_register_Asm_8
    1078502481U,	// VLD4DUPd16
    0U,	// VLD4DUPd16Pseudo
    0U,	// VLD4DUPd16Pseudo_UPD
    1078568017U,	// VLD4DUPd16_UPD
    1078633553U,	// VLD4DUPd32
    0U,	// VLD4DUPd32Pseudo
    0U,	// VLD4DUPd32Pseudo_UPD
    1078699089U,	// VLD4DUPd32_UPD
    1078764625U,	// VLD4DUPd8
    0U,	// VLD4DUPd8Pseudo
    0U,	// VLD4DUPd8Pseudo_UPD
    1078830161U,	// VLD4DUPd8_UPD
    1564632145U,	// VLD4DUPdAsm_16
    1564763217U,	// VLD4DUPdAsm_32
    1563190353U,	// VLD4DUPdAsm_8
    2638373969U,	// VLD4DUPdWB_fixed_Asm_16
    2638505041U,	// VLD4DUPdWB_fixed_Asm_32
    2636932177U,	// VLD4DUPdWB_fixed_Asm_8
    490894417U,	// VLD4DUPdWB_register_Asm_16
    491025489U,	// VLD4DUPdWB_register_Asm_32
    489452625U,	// VLD4DUPdWB_register_Asm_8
    1078502481U,	// VLD4DUPq16
    1078568017U,	// VLD4DUPq16_UPD
    1078633553U,	// VLD4DUPq32
    1078699089U,	// VLD4DUPq32_UPD
    1078764625U,	// VLD4DUPq8
    1078830161U,	// VLD4DUPq8_UPD
    1581409361U,	// VLD4DUPqAsm_16
    1581540433U,	// VLD4DUPqAsm_32
    1579967569U,	// VLD4DUPqAsm_8
    2655151185U,	// VLD4DUPqWB_fixed_Asm_16
    2655282257U,	// VLD4DUPqWB_fixed_Asm_32
    2653709393U,	// VLD4DUPqWB_fixed_Asm_8
    507671633U,	// VLD4DUPqWB_register_Asm_16
    507802705U,	// VLD4DUPqWB_register_Asm_32
    506229841U,	// VLD4DUPqWB_register_Asm_8
    4822097U,	// VLD4LNd16
    0U,	// VLD4LNd16Pseudo
    0U,	// VLD4LNd16Pseudo_UPD
    4830289U,	// VLD4LNd16_UPD
    4953169U,	// VLD4LNd32
    0U,	// VLD4LNd32Pseudo
    0U,	// VLD4LNd32Pseudo_UPD
    4961361U,	// VLD4LNd32_UPD
    5084241U,	// VLD4LNd8
    0U,	// VLD4LNd8Pseudo
    0U,	// VLD4LNd8Pseudo_UPD
    5092433U,	// VLD4LNd8_UPD
    4355153U,	// VLD4LNdAsm_16
    4486225U,	// VLD4LNdAsm_32
    2913361U,	// VLD4LNdAsm_8
    4355153U,	// VLD4LNdWB_fixed_Asm_16
    4486225U,	// VLD4LNdWB_fixed_Asm_32
    2913361U,	// VLD4LNdWB_fixed_Asm_8
    4392017U,	// VLD4LNdWB_register_Asm_16
    4523089U,	// VLD4LNdWB_register_Asm_32
    2950225U,	// VLD4LNdWB_register_Asm_8
    4822097U,	// VLD4LNq16
    0U,	// VLD4LNq16Pseudo
    0U,	// VLD4LNq16Pseudo_UPD
    4830289U,	// VLD4LNq16_UPD
    4953169U,	// VLD4LNq32
    0U,	// VLD4LNq32Pseudo
    0U,	// VLD4LNq32Pseudo_UPD
    4961361U,	// VLD4LNq32_UPD
    4355153U,	// VLD4LNqAsm_16
    4486225U,	// VLD4LNqAsm_32
    4355153U,	// VLD4LNqWB_fixed_Asm_16
    4486225U,	// VLD4LNqWB_fixed_Asm_32
    4392017U,	// VLD4LNqWB_register_Asm_16
    4523089U,	// VLD4LNqWB_register_Asm_32
    4760657U,	// VLD4d16
    0U,	// VLD4d16Pseudo
    0U,	// VLD4d16Pseudo_UPD
    4826193U,	// VLD4d16_UPD
    4891729U,	// VLD4d32
    0U,	// VLD4d32Pseudo
    0U,	// VLD4d32Pseudo_UPD
    4957265U,	// VLD4d32_UPD
    5022801U,	// VLD4d8
    0U,	// VLD4d8Pseudo
    0U,	// VLD4d8Pseudo_UPD
    5088337U,	// VLD4d8_UPD
    2504156241U,	// VLD4dAsm_16
    2504287313U,	// VLD4dAsm_32
    2502714449U,	// VLD4dAsm_8
    2504156241U,	// VLD4dWB_fixed_Asm_16
    2504287313U,	// VLD4dWB_fixed_Asm_32
    2502714449U,	// VLD4dWB_fixed_Asm_8
    2504160337U,	// VLD4dWB_register_Asm_16
    2504291409U,	// VLD4dWB_register_Asm_32
    2502718545U,	// VLD4dWB_register_Asm_8
    4760657U,	// VLD4q16
    0U,	// VLD4q16Pseudo_UPD
    4826193U,	// VLD4q16_UPD
    0U,	// VLD4q16oddPseudo
    0U,	// VLD4q16oddPseudo_UPD
    4891729U,	// VLD4q32
    0U,	// VLD4q32Pseudo_UPD
    4957265U,	// VLD4q32_UPD
    0U,	// VLD4q32oddPseudo
    0U,	// VLD4q32oddPseudo_UPD
    5022801U,	// VLD4q8
    0U,	// VLD4q8Pseudo_UPD
    5088337U,	// VLD4q8_UPD
    0U,	// VLD4q8oddPseudo
    0U,	// VLD4q8oddPseudo_UPD
    1598186577U,	// VLD4qAsm_16
    1598317649U,	// VLD4qAsm_32
    1596744785U,	// VLD4qAsm_8
    2671928401U,	// VLD4qWB_fixed_Asm_16
    2672059473U,	// VLD4qWB_fixed_Asm_32
    2670486609U,	// VLD4qWB_fixed_Asm_8
    524448849U,	// VLD4qWB_register_Asm_16
    524579921U,	// VLD4qWB_register_Asm_32
    523007057U,	// VLD4qWB_register_Asm_8
    33572317U,	// VLDMDDB_UPD
    34161U,	// VLDMDIA
    33572209U,	// VLDMDIA_UPD
    0U,	// VLDMQIA
    33572317U,	// VLDMSDB_UPD
    34161U,	// VLDMSIA
    33572209U,	// VLDMSIA_UPD
    27014U,	// VLDRD
    27014U,	// VLDRS
    33706566U,	// VMAXNMD
    33706258U,	// VMAXNMND
    33706258U,	// VMAXNMNQ
    33706258U,	// VMAXNMS
    2249091892U,	// VMAXfd
    2249091892U,	// VMAXfq
    35416884U,	// VMAXsv16i8
    35154740U,	// VMAXsv2i32
    35285812U,	// VMAXsv4i16
    35154740U,	// VMAXsv4i32
    35285812U,	// VMAXsv8i16
    35416884U,	// VMAXsv8i8
    35810100U,	// VMAXuv16i8
    35547956U,	// VMAXuv2i32
    35679028U,	// VMAXuv4i16
    35547956U,	// VMAXuv4i32
    35679028U,	// VMAXuv8i16
    35810100U,	// VMAXuv8i8
    33706554U,	// VMINNMD
    33706246U,	// VMINNMND
    33706246U,	// VMINNMNQ
    33706246U,	// VMINNMS
    2249091310U,	// VMINfd
    2249091310U,	// VMINfq
    35416302U,	// VMINsv16i8
    35154158U,	// VMINsv2i32
    35285230U,	// VMINsv4i16
    35154158U,	// VMINsv4i32
    35285230U,	// VMINsv8i16
    35416302U,	// VMINsv8i8
    35809518U,	// VMINuv16i8
    35547374U,	// VMINuv2i32
    35678446U,	// VMINuv4i16
    35547374U,	// VMINuv4i32
    35678446U,	// VMINuv8i16
    35809518U,	// VMINuv8i8
    101479825U,	// VMLAD
    18417706U,	// VMLALslsv2i32
    18548778U,	// VMLALslsv4i16
    18810922U,	// VMLALsluv2i32
    18941994U,	// VMLALsluv4i16
    18380842U,	// VMLALsv2i64
    18511914U,	// VMLALsv4i32
    18642986U,	// VMLALsv8i16
    18774058U,	// VMLALuv2i64
    18905130U,	// VMLALuv4i32
    19036202U,	// VMLALuv8i16
    101610897U,	// VMLAS
    101610897U,	// VMLAfd
    101610897U,	// VMLAfq
    101647761U,	// VMLAslfd
    101647761U,	// VMLAslfq
    19334545U,	// VMLAslv2i32
    19465617U,	// VMLAslv4i16
    19334545U,	// VMLAslv4i32
    19465617U,	// VMLAslv8i16
    19559825U,	// VMLAv16i8
    19297681U,	// VMLAv2i32
    19428753U,	// VMLAv4i16
    19297681U,	// VMLAv4i32
    19428753U,	// VMLAv8i16
    19559825U,	// VMLAv8i8
    101480947U,	// VMLSD
    18417825U,	// VMLSLslsv2i32
    18548897U,	// VMLSLslsv4i16
    18811041U,	// VMLSLsluv2i32
    18942113U,	// VMLSLsluv4i16
    18380961U,	// VMLSLsv2i64
    18512033U,	// VMLSLsv4i32
    18643105U,	// VMLSLsv8i16
    18774177U,	// VMLSLuv2i64
    18905249U,	// VMLSLuv4i32
    19036321U,	// VMLSLuv8i16
    101612019U,	// VMLSS
    101612019U,	// VMLSfd
    101612019U,	// VMLSfq
    101648883U,	// VMLSslfd
    101648883U,	// VMLSslfq
    19335667U,	// VMLSslv2i32
    19466739U,	// VMLSslv4i16
    19335667U,	// VMLSslv4i32
    19466739U,	// VMLSslv8i16
    19560947U,	// VMLSv16i8
    19298803U,	// VMLSv2i32
    19429875U,	// VMLSv4i16
    19298803U,	// VMLSv4i32
    19429875U,	// VMLSv8i16
    19560947U,	// VMLSv8i8
    2248952579U,	// VMOVD
    0U,	// VMOVD0
    27395U,	// VMOVDRR
    0U,	// VMOVDcc
    1108887740U,	// VMOVLsv2i64
    1109018812U,	// VMOVLsv4i32
    1109149884U,	// VMOVLsv8i16
    1109280956U,	// VMOVLuv2i64
    1109412028U,	// VMOVLuv4i32
    1109543100U,	// VMOVLuv8i16
    1109674306U,	// VMOVNv2i32
    1109805378U,	// VMOVNv4i16
    1109936450U,	// VMOVNv8i8
    0U,	// VMOVQ0
    27395U,	// VMOVRRD
    31491U,	// VMOVRRS
    19203U,	// VMOVRS
    2249083651U,	// VMOVS
    19203U,	// VMOVSR
    31491U,	// VMOVSRR
    0U,	// VMOVScc
    254429955U,	// VMOVv16i8
    254036739U,	// VMOVv1i64
    3322825475U,	// VMOVv2f32
    254167811U,	// VMOVv2i32
    254036739U,	// VMOVv2i64
    3322825475U,	// VMOVv4f32
    254298883U,	// VMOVv4i16
    254167811U,	// VMOVv4i32
    254298883U,	// VMOVv8i16
    254429955U,	// VMOVv8i8
    3221260810U,	// VMRS
    35338U,	// VMRS_FPEXC
    1073777162U,	// VMRS_FPINST
    2147518986U,	// VMRS_FPINST2
    3221260810U,	// VMRS_FPSID
    35338U,	// VMRS_MVFR0
    1073777162U,	// VMRS_MVFR1
    2147518986U,	// VMRS_MVFR2
    5147067U,	// VMSR
    5278139U,	// VMSR_FPEXC
    5409211U,	// VMSR_FPINST
    5540283U,	// VMSR_FPINST2
    5671355U,	// VMSR_FPSID
    2248960183U,	// VMULD
    33706650U,	// VMULLp64
    5793934U,	// VMULLp8
    35158158U,	// VMULLslsv2i32
    35289230U,	// VMULLslsv4i16
    35551374U,	// VMULLsluv2i32
    35682446U,	// VMULLsluv4i16
    35154062U,	// VMULLsv2i64
    35285134U,	// VMULLsv4i32
    35416206U,	// VMULLsv8i16
    35547278U,	// VMULLuv2i64
    35678350U,	// VMULLuv4i32
    35809422U,	// VMULLuv8i16
    2249091255U,	// VMULS
    2249091255U,	// VMULfd
    2249091255U,	// VMULfq
    5793975U,	// VMULpd
    5793975U,	// VMULpq
    2249095351U,	// VMULslfd
    2249095351U,	// VMULslfq
    36075703U,	// VMULslv2i32
    36206775U,	// VMULslv4i16
    36075703U,	// VMULslv4i32
    36206775U,	// VMULslv8i16
    36333751U,	// VMULv16i8
    36071607U,	// VMULv2i32
    36202679U,	// VMULv4i16
    36071607U,	// VMULv4i32
    36202679U,	// VMULv8i16
    36333751U,	// VMULv8i8
    18742U,	// VMVNd
    18742U,	// VMVNq
    254167350U,	// VMVNv2i32
    254298422U,	// VMVNv4i16
    254167350U,	// VMVNv4i32
    254298422U,	// VMVNv8i16
    2248951664U,	// VNEGD
    2249082736U,	// VNEGS
    2249082736U,	// VNEGf32q
    2249082736U,	// VNEGfd
    1109018480U,	// VNEGs16d
    1109018480U,	// VNEGs16q
    1108887408U,	// VNEGs32d
    1108887408U,	// VNEGs32q
    1109149552U,	// VNEGs8d
    1109149552U,	// VNEGs8q
    101479819U,	// VNMLAD
    101610891U,	// VNMLAS
    101480941U,	// VNMLSD
    101612013U,	// VNMLSS
    2248960177U,	// VNMULD
    2249091249U,	// VNMULS
    26899U,	// VORNd
    26899U,	// VORNq
    27051U,	// VORRd
    254175659U,	// VORRiv2i32
    254306731U,	// VORRiv4i16
    254175659U,	// VORRiv4i32
    254306731U,	// VORRiv8i16
    27051U,	// VORRq
    1092380687U,	// VPADALsv16i8
    1092118543U,	// VPADALsv2i32
    1092249615U,	// VPADALsv4i16
    1092118543U,	// VPADALsv4i32
    1092249615U,	// VPADALsv8i16
    1092380687U,	// VPADALsv8i8
    1092773903U,	// VPADALuv16i8
    1092511759U,	// VPADALuv2i32
    1092642831U,	// VPADALuv4i16
    1092511759U,	// VPADALuv4i32
    1092642831U,	// VPADALuv8i16
    1092773903U,	// VPADALuv8i8
    1109149771U,	// VPADDLsv16i8
    1108887627U,	// VPADDLsv2i32
    1109018699U,	// VPADDLsv4i16
    1108887627U,	// VPADDLsv4i32
    1109018699U,	// VPADDLsv8i16
    1109149771U,	// VPADDLsv8i8
    1109542987U,	// VPADDLuv16i8
    1109280843U,	// VPADDLuv2i32
    1109411915U,	// VPADDLuv4i16
    1109280843U,	// VPADDLuv4i32
    1109411915U,	// VPADDLuv8i16
    1109542987U,	// VPADDLuv8i8
    2249090786U,	// VPADDf
    36202210U,	// VPADDi16
    36071138U,	// VPADDi32
    36333282U,	// VPADDi8
    2249091886U,	// VPMAXf
    35285806U,	// VPMAXs16
    35154734U,	// VPMAXs32
    35416878U,	// VPMAXs8
    35679022U,	// VPMAXu16
    35547950U,	// VPMAXu32
    35810094U,	// VPMAXu8
    2249091304U,	// VPMINf
    35285224U,	// VPMINs16
    35154152U,	// VPMINs32
    35416296U,	// VPMINs8
    35678440U,	// VPMINu16
    35547368U,	// VPMINu32
    35809512U,	// VPMINu8
    1109150162U,	// VQABSv16i8
    1108888018U,	// VQABSv2i32
    1109019090U,	// VQABSv4i16
    1108888018U,	// VQABSv4i32
    1109019090U,	// VQABSv8i16
    1109150162U,	// VQABSv8i8
    35415784U,	// VQADDsv16i8
    39479016U,	// VQADDsv1i64
    35153640U,	// VQADDsv2i32
    39479016U,	// VQADDsv2i64
    35284712U,	// VQADDsv4i16
    35153640U,	// VQADDsv4i32
    35284712U,	// VQADDsv8i16
    35415784U,	// VQADDsv8i8
    35809000U,	// VQADDuv16i8
    39610088U,	// VQADDuv1i64
    35546856U,	// VQADDuv2i32
    39610088U,	// VQADDuv2i64
    35677928U,	// VQADDuv4i16
    35546856U,	// VQADDuv4i32
    35677928U,	// VQADDuv8i16
    35809000U,	// VQADDuv8i8
    18417686U,	// VQDMLALslv2i32
    18548758U,	// VQDMLALslv4i16
    18380822U,	// VQDMLALv2i64
    18511894U,	// VQDMLALv4i32
    18417817U,	// VQDMLSLslv2i32
    18548889U,	// VQDMLSLslv4i16
    18380953U,	// VQDMLSLv2i64
    18512025U,	// VQDMLSLv4i32
    35157903U,	// VQDMULHslv2i32
    35288975U,	// VQDMULHslv4i16
    35157903U,	// VQDMULHslv4i32
    35288975U,	// VQDMULHslv8i16
    35153807U,	// VQDMULHv2i32
    35284879U,	// VQDMULHv4i16
    35153807U,	// VQDMULHv4i32
    35284879U,	// VQDMULHv8i16
    35158138U,	// VQDMULLslv2i32
    35289210U,	// VQDMULLslv4i16
    35154042U,	// VQDMULLv2i64
    35285114U,	// VQDMULLv4i32
    1113213230U,	// VQMOVNsuv2i32
    1108887854U,	// VQMOVNsuv4i16
    1109018926U,	// VQMOVNsuv8i8
    1113213243U,	// VQMOVNsv2i32
    1108887867U,	// VQMOVNsv4i16
    1109018939U,	// VQMOVNsv8i8
    1113344315U,	// VQMOVNuv2i32
    1109281083U,	// VQMOVNuv4i16
    1109412155U,	// VQMOVNuv8i8
    1109149546U,	// VQNEGv16i8
    1108887402U,	// VQNEGv2i32
    1109018474U,	// VQNEGv4i16
    1108887402U,	// VQNEGv4i32
    1109018474U,	// VQNEGv8i16
    1109149546U,	// VQNEGv8i8
    35157911U,	// VQRDMULHslv2i32
    35288983U,	// VQRDMULHslv4i16
    35157911U,	// VQRDMULHslv4i32
    35288983U,	// VQRDMULHslv8i16
    35153815U,	// VQRDMULHv2i32
    35284887U,	// VQRDMULHv4i16
    35153815U,	// VQRDMULHv4i32
    35284887U,	// VQRDMULHv8i16
    35416162U,	// VQRSHLsv16i8
    39479394U,	// VQRSHLsv1i64
    35154018U,	// VQRSHLsv2i32
    39479394U,	// VQRSHLsv2i64
    35285090U,	// VQRSHLsv4i16
    35154018U,	// VQRSHLsv4i32
    35285090U,	// VQRSHLsv8i16
    35416162U,	// VQRSHLsv8i8
    35809378U,	// VQRSHLuv16i8
    39610466U,	// VQRSHLuv1i64
    35547234U,	// VQRSHLuv2i32
    39610466U,	// VQRSHLuv2i64
    35678306U,	// VQRSHLuv4i16
    35547234U,	// VQRSHLuv4i32
    35678306U,	// VQRSHLuv8i16
    35809378U,	// VQRSHLuv8i8
    39479550U,	// VQRSHRNsv2i32
    35154174U,	// VQRSHRNsv4i16
    35285246U,	// VQRSHRNsv8i8
    39610622U,	// VQRSHRNuv2i32
    35547390U,	// VQRSHRNuv4i16
    35678462U,	// VQRSHRNuv8i8
    39479589U,	// VQRSHRUNv2i32
    35154213U,	// VQRSHRUNv4i16
    35285285U,	// VQRSHRUNv8i8
    35416156U,	// VQSHLsiv16i8
    39479388U,	// VQSHLsiv1i64
    35154012U,	// VQSHLsiv2i32
    39479388U,	// VQSHLsiv2i64
    35285084U,	// VQSHLsiv4i16
    35154012U,	// VQSHLsiv4i32
    35285084U,	// VQSHLsiv8i16
    35416156U,	// VQSHLsiv8i8
    35416809U,	// VQSHLsuv16i8
    39480041U,	// VQSHLsuv1i64
    35154665U,	// VQSHLsuv2i32
    39480041U,	// VQSHLsuv2i64
    35285737U,	// VQSHLsuv4i16
    35154665U,	// VQSHLsuv4i32
    35285737U,	// VQSHLsuv8i16
    35416809U,	// VQSHLsuv8i8
    35416156U,	// VQSHLsv16i8
    39479388U,	// VQSHLsv1i64
    35154012U,	// VQSHLsv2i32
    39479388U,	// VQSHLsv2i64
    35285084U,	// VQSHLsv4i16
    35154012U,	// VQSHLsv4i32
    35285084U,	// VQSHLsv8i16
    35416156U,	// VQSHLsv8i8
    35809372U,	// VQSHLuiv16i8
    39610460U,	// VQSHLuiv1i64
    35547228U,	// VQSHLuiv2i32
    39610460U,	// VQSHLuiv2i64
    35678300U,	// VQSHLuiv4i16
    35547228U,	// VQSHLuiv4i32
    35678300U,	// VQSHLuiv8i16
    35809372U,	// VQSHLuiv8i8
    35809372U,	// VQSHLuv16i8
    39610460U,	// VQSHLuv1i64
    35547228U,	// VQSHLuv2i32
    39610460U,	// VQSHLuv2i64
    35678300U,	// VQSHLuv4i16
    35547228U,	// VQSHLuv4i32
    35678300U,	// VQSHLuv8i16
    35809372U,	// VQSHLuv8i8
    39479543U,	// VQSHRNsv2i32
    35154167U,	// VQSHRNsv4i16
    35285239U,	// VQSHRNsv8i8
    39610615U,	// VQSHRNuv2i32
    35547383U,	// VQSHRNuv4i16
    35678455U,	// VQSHRNuv8i8
    39479581U,	// VQSHRUNv2i32
    35154205U,	// VQSHRUNv4i16
    35285277U,	// VQSHRUNv8i8
    35415643U,	// VQSUBsv16i8
    39478875U,	// VQSUBsv1i64
    35153499U,	// VQSUBsv2i32
    39478875U,	// VQSUBsv2i64
    35284571U,	// VQSUBsv4i16
    35153499U,	// VQSUBsv4i32
    35284571U,	// VQSUBsv8i16
    35415643U,	// VQSUBsv8i8
    35808859U,	// VQSUBuv16i8
    39609947U,	// VQSUBuv1i64
    35546715U,	// VQSUBuv2i32
    39609947U,	// VQSUBuv2i64
    35677787U,	// VQSUBuv4i16
    35546715U,	// VQSUBuv4i32
    35677787U,	// VQSUBuv8i16
    35808859U,	// VQSUBuv8i8
    35940569U,	// VRADDHNv2i32
    36071641U,	// VRADDHNv4i16
    36202713U,	// VRADDHNv8i8
    1109280588U,	// VRECPEd
    2249082700U,	// VRECPEfd
    2249082700U,	// VRECPEfq
    1109280588U,	// VRECPEq
    2249091587U,	// VRECPSfd
    2249091587U,	// VRECPSfq
    2901203U,	// VREV16d8
    2901203U,	// VREV16q8
    4342782U,	// VREV32d16
    2900990U,	// VREV32d8
    4342782U,	// VREV32q16
    2900990U,	// VREV32q8
    4342858U,	// VREV64d16
    4473930U,	// VREV64d32
    2901066U,	// VREV64d8
    4342858U,	// VREV64q16
    4473930U,	// VREV64q32
    2901066U,	// VREV64q8
    35415765U,	// VRHADDsv16i8
    35153621U,	// VRHADDsv2i32
    35284693U,	// VRHADDsv4i16
    35153621U,	// VRHADDsv4i32
    35284693U,	// VRHADDsv8i16
    35415765U,	// VRHADDsv8i8
    35808981U,	// VRHADDuv16i8
    35546837U,	// VRHADDuv2i32
    35677909U,	// VRHADDuv4i16
    35546837U,	// VRHADDuv4i32
    35677909U,	// VRHADDuv8i16
    35808981U,	// VRHADDuv8i8
    1107448354U,	// VRINTAD
    1107448046U,	// VRINTAND
    1107448046U,	// VRINTANQ
    1107448046U,	// VRINTAS
    1107448402U,	// VRINTMD
    1107448094U,	// VRINTMND
    1107448094U,	// VRINTMNQ
    1107448094U,	// VRINTMS
    1107448414U,	// VRINTND
    1107448106U,	// VRINTNND
    1107448106U,	// VRINTNNQ
    1107448106U,	// VRINTNS
    1107448426U,	// VRINTPD
    1107448118U,	// VRINTPND
    1107448118U,	// VRINTPNQ
    1107448118U,	// VRINTPS
    2248952256U,	// VRINTRD
    2249083328U,	// VRINTRS
    2248952802U,	// VRINTXD
    1107448166U,	// VRINTXND
    1107448166U,	// VRINTXNQ
    2249083874U,	// VRINTXS
    2248952814U,	// VRINTZD
    1107448178U,	// VRINTZND
    1107448178U,	// VRINTZNQ
    2249083886U,	// VRINTZS
    35416169U,	// VRSHLsv16i8
    39479401U,	// VRSHLsv1i64
    35154025U,	// VRSHLsv2i32
    39479401U,	// VRSHLsv2i64
    35285097U,	// VRSHLsv4i16
    35154025U,	// VRSHLsv4i32
    35285097U,	// VRSHLsv8i16
    35416169U,	// VRSHLsv8i8
    35809385U,	// VRSHLuv16i8
    39610473U,	// VRSHLuv1i64
    35547241U,	// VRSHLuv2i32
    39610473U,	// VRSHLuv2i64
    35678313U,	// VRSHLuv4i16
    35547241U,	// VRSHLuv4i32
    35678313U,	// VRSHLuv8i16
    35809385U,	// VRSHLuv8i8
    35940614U,	// VRSHRNv2i32
    36071686U,	// VRSHRNv4i16
    36202758U,	// VRSHRNv8i8
    35416459U,	// VRSHRsv16i8
    39479691U,	// VRSHRsv1i64
    35154315U,	// VRSHRsv2i32
    39479691U,	// VRSHRsv2i64
    35285387U,	// VRSHRsv4i16
    35154315U,	// VRSHRsv4i32
    35285387U,	// VRSHRsv8i16
    35416459U,	// VRSHRsv8i8
    35809675U,	// VRSHRuv16i8
    39610763U,	// VRSHRuv1i64
    35547531U,	// VRSHRuv2i32
    39610763U,	// VRSHRuv2i64
    35678603U,	// VRSHRuv4i16
    35547531U,	// VRSHRuv4i32
    35678603U,	// VRSHRuv8i16
    35809675U,	// VRSHRuv8i8
    1109280601U,	// VRSQRTEd
    2249082713U,	// VRSQRTEfd
    2249082713U,	// VRSQRTEfq
    1109280601U,	// VRSQRTEq
    2249091609U,	// VRSQRTSfd
    2249091609U,	// VRSQRTSfq
    18642337U,	// VRSRAsv16i8
    22705569U,	// VRSRAsv1i64
    18380193U,	// VRSRAsv2i32
    22705569U,	// VRSRAsv2i64
    18511265U,	// VRSRAsv4i16
    18380193U,	// VRSRAsv4i32
    18511265U,	// VRSRAsv8i16
    18642337U,	// VRSRAsv8i8
    19035553U,	// VRSRAuv16i8
    22836641U,	// VRSRAuv1i64
    18773409U,	// VRSRAuv2i32
    22836641U,	// VRSRAuv2i64
    18904481U,	// VRSRAuv4i16
    18773409U,	// VRSRAuv4i32
    18904481U,	// VRSRAuv8i16
    19035553U,	// VRSRAuv8i8
    35940554U,	// VRSUBHNv2i32
    36071626U,	// VRSUBHNv4i16
    36202698U,	// VRSUBHNv8i8
    33706614U,	// VSELEQD
    33706306U,	// VSELEQS
    33706542U,	// VSELGED
    33706234U,	// VSELGES
    33706638U,	// VSELGTD
    33706330U,	// VSELGTS
    33706626U,	// VSELVSD
    33706318U,	// VSELVSS
    3225582339U,	// VSETLNi16
    3225713411U,	// VSETLNi32
    3224140547U,	// VSETLNi8
    36202612U,	// VSHLLi16
    36071540U,	// VSHLLi32
    36333684U,	// VSHLLi8
    35154036U,	// VSHLLsv2i64
    35285108U,	// VSHLLsv4i32
    35416180U,	// VSHLLsv8i16
    35547252U,	// VSHLLuv2i64
    35678324U,	// VSHLLuv4i32
    35809396U,	// VSHLLuv8i16
    36333679U,	// VSHLiv16i8
    35940463U,	// VSHLiv1i64
    36071535U,	// VSHLiv2i32
    35940463U,	// VSHLiv2i64
    36202607U,	// VSHLiv4i16
    36071535U,	// VSHLiv4i32
    36202607U,	// VSHLiv8i16
    36333679U,	// VSHLiv8i8
    35416175U,	// VSHLsv16i8
    39479407U,	// VSHLsv1i64
    35154031U,	// VSHLsv2i32
    39479407U,	// VSHLsv2i64
    35285103U,	// VSHLsv4i16
    35154031U,	// VSHLsv4i32
    35285103U,	// VSHLsv8i16
    35416175U,	// VSHLsv8i8
    35809391U,	// VSHLuv16i8
    39610479U,	// VSHLuv1i64
    35547247U,	// VSHLuv2i32
    39610479U,	// VSHLuv2i64
    35678319U,	// VSHLuv4i16
    35547247U,	// VSHLuv4i32
    35678319U,	// VSHLuv8i16
    35809391U,	// VSHLuv8i8
    35940621U,	// VSHRNv2i32
    36071693U,	// VSHRNv4i16
    36202765U,	// VSHRNv8i8
    35416465U,	// VSHRsv16i8
    39479697U,	// VSHRsv1i64
    35154321U,	// VSHRsv2i32
    39479697U,	// VSHRsv2i64
    35285393U,	// VSHRsv4i16
    35154321U,	// VSHRsv4i32
    35285393U,	// VSHRsv8i16
    35416465U,	// VSHRsv8i8
    35809681U,	// VSHRuv16i8
    39610769U,	// VSHRuv1i64
    35547537U,	// VSHRuv2i32
    39610769U,	// VSHRuv2i64
    35678609U,	// VSHRuv4i16
    35547537U,	// VSHRuv4i32
    35678609U,	// VSHRuv8i16
    35809681U,	// VSHRuv8i8
    6187724U,	// VSHTOD
    6318796U,	// VSHTOS
    291654348U,	// VSITOD
    289295052U,	// VSITOS
    2914281U,	// VSLIv16i8
    4618217U,	// VSLIv1i64
    4487145U,	// VSLIv2i32
    4618217U,	// VSLIv2i64
    4356073U,	// VSLIv4i16
    4487145U,	// VSLIv4i32
    4356073U,	// VSLIv8i16
    2914281U,	// VSLIv8i8
    107113164U,	// VSLTOD
    104753868U,	// VSLTOS
    2248952480U,	// VSQRTD
    2249083552U,	// VSQRTS
    18642343U,	// VSRAsv16i8
    22705575U,	// VSRAsv1i64
    18380199U,	// VSRAsv2i32
    22705575U,	// VSRAsv2i64
    18511271U,	// VSRAsv4i16
    18380199U,	// VSRAsv4i32
    18511271U,	// VSRAsv8i16
    18642343U,	// VSRAsv8i8
    19035559U,	// VSRAuv16i8
    22836647U,	// VSRAuv1i64
    18773415U,	// VSRAuv2i32
    22836647U,	// VSRAuv2i64
    18904487U,	// VSRAuv4i16
    18773415U,	// VSRAuv4i32
    18904487U,	// VSRAuv8i16
    19035559U,	// VSRAuv8i8
    2914286U,	// VSRIv16i8
    4618222U,	// VSRIv1i64
    4487150U,	// VSRIv2i32
    4618222U,	// VSRIv2i64
    4356078U,	// VSRIv4i16
    4487150U,	// VSRIv4i32
    4356078U,	// VSRIv8i16
    2914286U,	// VSRIv8i8
    21525497U,	// VST1LNd16
    541631481U,	// VST1LNd16_UPD
    21656569U,	// VST1LNd32
    541762553U,	// VST1LNd32_UPD
    21787641U,	// VST1LNd8
    541893625U,	// VST1LNd8_UPD
    4355065U,	// VST1LNdAsm_16
    4486137U,	// VST1LNdAsm_32
    2913273U,	// VST1LNdAsm_8
    4355065U,	// VST1LNdWB_fixed_Asm_16
    4486137U,	// VST1LNdWB_fixed_Asm_32
    2913273U,	// VST1LNdWB_fixed_Asm_8
    4391929U,	// VST1LNdWB_register_Asm_16
    4523001U,	// VST1LNdWB_register_Asm_32
    2950137U,	// VST1LNdWB_register_Asm_8
    0U,	// VST1LNq16Pseudo
    0U,	// VST1LNq16Pseudo_UPD
    0U,	// VST1LNq32Pseudo
    0U,	// VST1LNq32Pseudo_UPD
    0U,	// VST1LNq8Pseudo
    0U,	// VST1LNq8Pseudo_UPD
    557999097U,	// VST1d16
    574776313U,	// VST1d16Q
    591557625U,	// VST1d16Qwb_fixed
    608371705U,	// VST1d16Qwb_register
    625107961U,	// VST1d16T
    641889273U,	// VST1d16Twb_fixed
    658703353U,	// VST1d16Twb_register
    675443705U,	// VST1d16wb_fixed
    692257785U,	// VST1d16wb_register
    558130169U,	// VST1d32
    574907385U,	// VST1d32Q
    591688697U,	// VST1d32Qwb_fixed
    608502777U,	// VST1d32Qwb_register
    625239033U,	// VST1d32T
    642020345U,	// VST1d32Twb_fixed
    658834425U,	// VST1d32Twb_register
    675574777U,	// VST1d32wb_fixed
    692388857U,	// VST1d32wb_register
    558261241U,	// VST1d64
    575038457U,	// VST1d64Q
    0U,	// VST1d64QPseudo
    0U,	// VST1d64QPseudoWB_fixed
    0U,	// VST1d64QPseudoWB_register
    591819769U,	// VST1d64Qwb_fixed
    608633849U,	// VST1d64Qwb_register
    625370105U,	// VST1d64T
    0U,	// VST1d64TPseudo
    0U,	// VST1d64TPseudoWB_fixed
    0U,	// VST1d64TPseudoWB_register
    642151417U,	// VST1d64Twb_fixed
    658965497U,	// VST1d64Twb_register
    675705849U,	// VST1d64wb_fixed
    692519929U,	// VST1d64wb_register
    556557305U,	// VST1d8
    573334521U,	// VST1d8Q
    590115833U,	// VST1d8Qwb_fixed
    606929913U,	// VST1d8Qwb_register
    623666169U,	// VST1d8T
    640447481U,	// VST1d8Twb_fixed
    657261561U,	// VST1d8Twb_register
    674001913U,	// VST1d8wb_fixed
    690815993U,	// VST1d8wb_register
    708994041U,	// VST1q16
    725775353U,	// VST1q16wb_fixed
    742589433U,	// VST1q16wb_register
    709125113U,	// VST1q32
    725906425U,	// VST1q32wb_fixed
    742720505U,	// VST1q32wb_register
    709256185U,	// VST1q64
    726037497U,	// VST1q64wb_fixed
    742851577U,	// VST1q64wb_register
    707552249U,	// VST1q8
    724333561U,	// VST1q8wb_fixed
    741147641U,	// VST1q8wb_register
    21562421U,	// VST2LNd16
    0U,	// VST2LNd16Pseudo
    0U,	// VST2LNd16Pseudo_UPD
    541684789U,	// VST2LNd16_UPD
    21693493U,	// VST2LNd32
    0U,	// VST2LNd32Pseudo
    0U,	// VST2LNd32Pseudo_UPD
    541815861U,	// VST2LNd32_UPD
    21824565U,	// VST2LNd8
    0U,	// VST2LNd8Pseudo
    0U,	// VST2LNd8Pseudo_UPD
    541946933U,	// VST2LNd8_UPD
    4355125U,	// VST2LNdAsm_16
    4486197U,	// VST2LNdAsm_32
    2913333U,	// VST2LNdAsm_8
    4355125U,	// VST2LNdWB_fixed_Asm_16
    4486197U,	// VST2LNdWB_fixed_Asm_32
    2913333U,	// VST2LNdWB_fixed_Asm_8
    4391989U,	// VST2LNdWB_register_Asm_16
    4523061U,	// VST2LNdWB_register_Asm_32
    2950197U,	// VST2LNdWB_register_Asm_8
    21562421U,	// VST2LNq16
    0U,	// VST2LNq16Pseudo
    0U,	// VST2LNq16Pseudo_UPD
    541684789U,	// VST2LNq16_UPD
    21693493U,	// VST2LNq32
    0U,	// VST2LNq32Pseudo
    0U,	// VST2LNq32Pseudo_UPD
    541815861U,	// VST2LNq32_UPD
    4355125U,	// VST2LNqAsm_16
    4486197U,	// VST2LNqAsm_32
    4355125U,	// VST2LNqWB_fixed_Asm_16
    4486197U,	// VST2LNqWB_fixed_Asm_32
    4391989U,	// VST2LNqWB_register_Asm_16
    4523061U,	// VST2LNqWB_register_Asm_32
    759325749U,	// VST2b16
    776107061U,	// VST2b16wb_fixed
    792921141U,	// VST2b16wb_register
    759456821U,	// VST2b32
    776238133U,	// VST2b32wb_fixed
    793052213U,	// VST2b32wb_register
    757883957U,	// VST2b8
    774665269U,	// VST2b8wb_fixed
    791479349U,	// VST2b8wb_register
    708994101U,	// VST2d16
    725775413U,	// VST2d16wb_fixed
    742589493U,	// VST2d16wb_register
    709125173U,	// VST2d32
    725906485U,	// VST2d32wb_fixed
    742720565U,	// VST2d32wb_register
    707552309U,	// VST2d8
    724333621U,	// VST2d8wb_fixed
    741147701U,	// VST2d8wb_register
    574776373U,	// VST2q16
    0U,	// VST2q16Pseudo
    0U,	// VST2q16PseudoWB_fixed
    0U,	// VST2q16PseudoWB_register
    591557685U,	// VST2q16wb_fixed
    608371765U,	// VST2q16wb_register
    574907445U,	// VST2q32
    0U,	// VST2q32Pseudo
    0U,	// VST2q32PseudoWB_fixed
    0U,	// VST2q32PseudoWB_register
    591688757U,	// VST2q32wb_fixed
    608502837U,	// VST2q32wb_register
    573334581U,	// VST2q8
    0U,	// VST2q8Pseudo
    0U,	// VST2q8PseudoWB_fixed
    0U,	// VST2q8PseudoWB_register
    590115893U,	// VST2q8wb_fixed
    606929973U,	// VST2q8wb_register
    21537861U,	// VST3LNd16
    0U,	// VST3LNd16Pseudo
    0U,	// VST3LNd16Pseudo_UPD
    541697093U,	// VST3LNd16_UPD
    21668933U,	// VST3LNd32
    0U,	// VST3LNd32Pseudo
    0U,	// VST3LNd32Pseudo_UPD
    541828165U,	// VST3LNd32_UPD
    21800005U,	// VST3LNd8
    0U,	// VST3LNd8Pseudo
    0U,	// VST3LNd8Pseudo_UPD
    541959237U,	// VST3LNd8_UPD
    4355141U,	// VST3LNdAsm_16
    4486213U,	// VST3LNdAsm_32
    2913349U,	// VST3LNdAsm_8
    4355141U,	// VST3LNdWB_fixed_Asm_16
    4486213U,	// VST3LNdWB_fixed_Asm_32
    2913349U,	// VST3LNdWB_fixed_Asm_8
    4392005U,	// VST3LNdWB_register_Asm_16
    4523077U,	// VST3LNdWB_register_Asm_32
    2950213U,	// VST3LNdWB_register_Asm_8
    21537861U,	// VST3LNq16
    0U,	// VST3LNq16Pseudo
    0U,	// VST3LNq16Pseudo_UPD
    541697093U,	// VST3LNq16_UPD
    21668933U,	// VST3LNq32
    0U,	// VST3LNq32Pseudo
    0U,	// VST3LNq32Pseudo_UPD
    541828165U,	// VST3LNq32_UPD
    4355141U,	// VST3LNqAsm_16
    4486213U,	// VST3LNqAsm_32
    4355141U,	// VST3LNqWB_fixed_Asm_16
    4486213U,	// VST3LNqWB_fixed_Asm_32
    4392005U,	// VST3LNqWB_register_Asm_16
    4523077U,	// VST3LNqWB_register_Asm_32
    21562437U,	// VST3d16
    0U,	// VST3d16Pseudo
    0U,	// VST3d16Pseudo_UPD
    541684805U,	// VST3d16_UPD
    21693509U,	// VST3d32
    0U,	// VST3d32Pseudo
    0U,	// VST3d32Pseudo_UPD
    541815877U,	// VST3d32_UPD
    21824581U,	// VST3d8
    0U,	// VST3d8Pseudo
    0U,	// VST3d8Pseudo_UPD
    541946949U,	// VST3d8_UPD
    2520933445U,	// VST3dAsm_16
    2521064517U,	// VST3dAsm_32
    2519491653U,	// VST3dAsm_8
    2520933445U,	// VST3dWB_fixed_Asm_16
    2521064517U,	// VST3dWB_fixed_Asm_32
    2519491653U,	// VST3dWB_fixed_Asm_8
    2520937541U,	// VST3dWB_register_Asm_16
    2521068613U,	// VST3dWB_register_Asm_32
    2519495749U,	// VST3dWB_register_Asm_8
    21562437U,	// VST3q16
    0U,	// VST3q16Pseudo_UPD
    541684805U,	// VST3q16_UPD
    0U,	// VST3q16oddPseudo
    0U,	// VST3q16oddPseudo_UPD
    21693509U,	// VST3q32
    0U,	// VST3q32Pseudo_UPD
    541815877U,	// VST3q32_UPD
    0U,	// VST3q32oddPseudo
    0U,	// VST3q32oddPseudo_UPD
    21824581U,	// VST3q8
    0U,	// VST3q8Pseudo_UPD
    541946949U,	// VST3q8_UPD
    0U,	// VST3q8oddPseudo
    0U,	// VST3q8oddPseudo_UPD
    1547854917U,	// VST3qAsm_16
    1547985989U,	// VST3qAsm_32
    1546413125U,	// VST3qAsm_8
    2621596741U,	// VST3qWB_fixed_Asm_16
    2621727813U,	// VST3qWB_fixed_Asm_32
    2620154949U,	// VST3qWB_fixed_Asm_8
    474117189U,	// VST3qWB_register_Asm_16
    474248261U,	// VST3qWB_register_Asm_32
    472675397U,	// VST3qWB_register_Asm_8
    21591126U,	// VST4LNd16
    0U,	// VST4LNd16Pseudo
    0U,	// VST4LNd16Pseudo_UPD
    541688918U,	// VST4LNd16_UPD
    21722198U,	// VST4LNd32
    0U,	// VST4LNd32Pseudo
    0U,	// VST4LNd32Pseudo_UPD
    541819990U,	// VST4LNd32_UPD
    21853270U,	// VST4LNd8
    0U,	// VST4LNd8Pseudo
    0U,	// VST4LNd8Pseudo_UPD
    541951062U,	// VST4LNd8_UPD
    4355158U,	// VST4LNdAsm_16
    4486230U,	// VST4LNdAsm_32
    2913366U,	// VST4LNdAsm_8
    4355158U,	// VST4LNdWB_fixed_Asm_16
    4486230U,	// VST4LNdWB_fixed_Asm_32
    2913366U,	// VST4LNdWB_fixed_Asm_8
    4392022U,	// VST4LNdWB_register_Asm_16
    4523094U,	// VST4LNdWB_register_Asm_32
    2950230U,	// VST4LNdWB_register_Asm_8
    21591126U,	// VST4LNq16
    0U,	// VST4LNq16Pseudo
    0U,	// VST4LNq16Pseudo_UPD
    541688918U,	// VST4LNq16_UPD
    21722198U,	// VST4LNq32
    0U,	// VST4LNq32Pseudo
    0U,	// VST4LNq32Pseudo_UPD
    541819990U,	// VST4LNq32_UPD
    4355158U,	// VST4LNqAsm_16
    4486230U,	// VST4LNqAsm_32
    4355158U,	// VST4LNqWB_fixed_Asm_16
    4486230U,	// VST4LNqWB_fixed_Asm_32
    4392022U,	// VST4LNqWB_register_Asm_16
    4523094U,	// VST4LNqWB_register_Asm_32
    21537878U,	// VST4d16
    0U,	// VST4d16Pseudo
    0U,	// VST4d16Pseudo_UPD
    541697110U,	// VST4d16_UPD
    21668950U,	// VST4d32
    0U,	// VST4d32Pseudo
    0U,	// VST4d32Pseudo_UPD
    541828182U,	// VST4d32_UPD
    21800022U,	// VST4d8
    0U,	// VST4d8Pseudo
    0U,	// VST4d8Pseudo_UPD
    541959254U,	// VST4d8_UPD
    2504156246U,	// VST4dAsm_16
    2504287318U,	// VST4dAsm_32
    2502714454U,	// VST4dAsm_8
    2504156246U,	// VST4dWB_fixed_Asm_16
    2504287318U,	// VST4dWB_fixed_Asm_32
    2502714454U,	// VST4dWB_fixed_Asm_8
    2504160342U,	// VST4dWB_register_Asm_16
    2504291414U,	// VST4dWB_register_Asm_32
    2502718550U,	// VST4dWB_register_Asm_8
    21537878U,	// VST4q16
    0U,	// VST4q16Pseudo_UPD
    541697110U,	// VST4q16_UPD
    0U,	// VST4q16oddPseudo
    0U,	// VST4q16oddPseudo_UPD
    21668950U,	// VST4q32
    0U,	// VST4q32Pseudo_UPD
    541828182U,	// VST4q32_UPD
    0U,	// VST4q32oddPseudo
    0U,	// VST4q32oddPseudo_UPD
    21800022U,	// VST4q8
    0U,	// VST4q8Pseudo_UPD
    541959254U,	// VST4q8_UPD
    0U,	// VST4q8oddPseudo
    0U,	// VST4q8oddPseudo_UPD
    1598186582U,	// VST4qAsm_16
    1598317654U,	// VST4qAsm_32
    1596744790U,	// VST4qAsm_8
    2671928406U,	// VST4qWB_fixed_Asm_16
    2672059478U,	// VST4qWB_fixed_Asm_32
    2670486614U,	// VST4qWB_fixed_Asm_8
    524448854U,	// VST4qWB_register_Asm_16
    524579926U,	// VST4qWB_register_Asm_32
    523007062U,	// VST4qWB_register_Asm_8
    33572324U,	// VSTMDDB_UPD
    34168U,	// VSTMDIA
    33572216U,	// VSTMDIA_UPD
    0U,	// VSTMQIA
    33572324U,	// VSTMSDB_UPD
    34168U,	// VSTMSIA
    33572216U,	// VSTMSIA_UPD
    27079U,	// VSTRD
    27079U,	// VSTRS
    2248959585U,	// VSUBD
    35940562U,	// VSUBHNv2i32
    36071634U,	// VSUBHNv4i16
    36202706U,	// VSUBHNv8i8
    35153973U,	// VSUBLsv2i64
    35285045U,	// VSUBLsv4i32
    35416117U,	// VSUBLsv8i16
    35547189U,	// VSUBLuv2i64
    35678261U,	// VSUBLuv4i32
    35809333U,	// VSUBLuv8i16
    2249090657U,	// VSUBS
    35154696U,	// VSUBWsv2i64
    35285768U,	// VSUBWsv4i32
    35416840U,	// VSUBWsv8i16
    35547912U,	// VSUBWuv2i64
    35678984U,	// VSUBWuv4i32
    35810056U,	// VSUBWuv8i16
    2249090657U,	// VSUBfd
    2249090657U,	// VSUBfq
    36333153U,	// VSUBv16i8
    35939937U,	// VSUBv1i64
    36071009U,	// VSUBv2i32
    35939937U,	// VSUBv2i64
    36202081U,	// VSUBv4i16
    36071009U,	// VSUBv4i32
    36202081U,	// VSUBv8i16
    36333153U,	// VSUBv8i8
    31076U,	// VSWPd
    31076U,	// VSWPq
    2910256U,	// VTBL1
    2910256U,	// VTBL2
    2910256U,	// VTBL3
    0U,	// VTBL3Pseudo
    2910256U,	// VTBL4
    0U,	// VTBL4Pseudo
    2915173U,	// VTBX1
    2915173U,	// VTBX2
    2915173U,	// VTBX3
    0U,	// VTBX3Pseudo
    2915173U,	// VTBX4
    0U,	// VTBX4Pseudo
    6580940U,	// VTOSHD
    6712012U,	// VTOSHS
    292047308U,	// VTOSIRD
    289032652U,	// VTOSIRS
    292047564U,	// VTOSIZD
    289032908U,	// VTOSIZS
    107506380U,	// VTOSLD
    104491724U,	// VTOSLS
    6974156U,	// VTOUHD
    7105228U,	// VTOUHS
    292440524U,	// VTOUIRD
    289163724U,	// VTOUIRS
    292440780U,	// VTOUIZD
    289163980U,	// VTOUIZS
    107899596U,	// VTOULD
    104622796U,	// VTOULS
    4356376U,	// VTRNd16
    4487448U,	// VTRNd32
    2914584U,	// VTRNd8
    4356376U,	// VTRNq16
    4487448U,	// VTRNq32
    2914584U,	// VTRNq8
    2910891U,	// VTSTv16i8
    4483755U,	// VTSTv2i32
    4352683U,	// VTSTv4i16
    4483755U,	// VTSTv4i32
    4352683U,	// VTSTv8i16
    2910891U,	// VTSTv8i8
    7367372U,	// VUHTOD
    7498444U,	// VUHTOS
    292833996U,	// VUITOD
    289426124U,	// VUITOS
    108292812U,	// VULTOD
    104884940U,	// VULTOS
    4356457U,	// VUZPd16
    2914665U,	// VUZPd8
    4356457U,	// VUZPq16
    4487529U,	// VUZPq32
    2914665U,	// VUZPq8
    4356433U,	// VZIPd16
    2914641U,	// VZIPd8
    4356433U,	// VZIPq16
    4487505U,	// VZIPq32
    2914641U,	// VZIPq8
    0U,	// WIN__CHKSTK
    34143U,	// sysLDMDA
    33572191U,	// sysLDMDA_UPD
    34270U,	// sysLDMDB
    33572318U,	// sysLDMDB_UPD
    35010U,	// sysLDMIA
    33573058U,	// sysLDMIA_UPD
    34289U,	// sysLDMIB
    33572337U,	// sysLDMIB_UPD
    34149U,	// sysSTMDA
    33572197U,	// sysSTMDA_UPD
    34277U,	// sysSTMDB
    33572325U,	// sysSTMDB_UPD
    35014U,	// sysSTMIA
    33573062U,	// sysSTMIA_UPD
    34295U,	// sysSTMIB
    33572343U,	// sysSTMIB_UPD
    0U,	// t2ABS
    5780U,	// t2ADCri
    7739028U,	// t2ADCrr
    7743124U,	// t2ADCrs
    0U,	// t2ADDSri
    0U,	// t2ADDSrr
    0U,	// t2ADDSrs
    7739089U,	// t2ADDri
    27407U,	// t2ADDri12
    7739089U,	// t2ADDrr
    7743185U,	// t2ADDrs
    7752066U,	// t2ADR
    5894U,	// t2ANDri
    7739142U,	// t2ANDrr
    7743238U,	// t2ANDrs
    7739824U,	// t2ASRri
    7739824U,	// t2ASRrr
    1081509295U,	// t2B
    26268U,	// t2BFC
    30689U,	// t2BFI
    5793U,	// t2BICri
    7739041U,	// t2BICrr
    7743137U,	// t2BICrs
    0U,	// t2BR_JT
    1073776627U,	// t2BXJ
    1081509295U,	// t2Bcc
    2197858637U,	// t2CDP
    2197857311U,	// t2CDP2
    433064U,	// t2CLREX
    19434U,	// t2CLZ
    7751923U,	// t2CMNri
    7751923U,	// t2CMNzrr
    7760115U,	// t2CMNzrs
    7752023U,	// t2CMPri
    7752023U,	// t2CMPrr
    7760215U,	// t2CMPrs
    414531U,	// t2CPS1p
    1165412870U,	// t2CPS2p
    83937798U,	// t2CPS3p
    33706710U,	// t2CRC32B
    33706718U,	// t2CRC32CB
    33706787U,	// t2CRC32CH
    33706863U,	// t2CRC32CW
    33706779U,	// t2CRC32H
    33706855U,	// t2CRC32W
    1073776486U,	// t2DBG
    431091U,	// t2DCPS1
    431151U,	// t2DCPS2
    431167U,	// t2DCPS3
    805340674U,	// t2DMB
    805340693U,	// t2DSB
    6558U,	// t2EORri
    7739806U,	// t2EORrr
    7743902U,	// t2EORrs
    1081510550U,	// t2HINT
    414553U,	// t2HVC
    822117913U,	// t2ISB
    117504644U,	// t2IT
    0U,	// t2Int_eh_sjlj_setjmp
    0U,	// t2Int_eh_sjlj_setjmp_nofp
    17755U,	// t2LDA
    17836U,	// t2LDAB
    19350U,	// t2LDAEX
    18036U,	// t2LDAEXB
    26400U,	// t2LDAEXD
    18373U,	// t2LDAEXH
    18293U,	// t2LDAH
    3271587831U,	// t2LDC2L_OFFSET
    3271587831U,	// t2LDC2L_OPTION
    3271587831U,	// t2LDC2L_POST
    3271587831U,	// t2LDC2L_PRE
    3271586821U,	// t2LDC2_OFFSET
    3271586821U,	// t2LDC2_OPTION
    3271586821U,	// t2LDC2_POST
    3271586821U,	// t2LDC2_PRE
    3271587899U,	// t2LDCL_OFFSET
    3271587899U,	// t2LDCL_OPTION
    3271587899U,	// t2LDCL_POST
    3271587899U,	// t2LDCL_PRE
    3271587480U,	// t2LDC_OFFSET
    3271587480U,	// t2LDC_OPTION
    3271587480U,	// t2LDC_POST
    3271587480U,	// t2LDC_PRE
    34270U,	// t2LDMDB
    33572318U,	// t2LDMDB_UPD
    7768258U,	// t2LDMIA
    0U,	// t2LDMIA_RET
    41306306U,	// t2LDMIA_UPD
    27212U,	// t2LDRBT
    30219U,	// t2LDRB_POST
    30219U,	// t2LDRB_PRE
    7759371U,	// t2LDRBi12
    26123U,	// t2LDRBi8
    7751179U,	// t2LDRBpci
    280075U,	// t2LDRBpcrel
    7763467U,	// t2LDRBs
    67338U,	// t2LDRD_POST
    67338U,	// t2LDRD_PRE
    30474U,	// t2LDRDi8
    27554U,	// t2LDREX
    18050U,	// t2LDREXB
    26414U,	// t2LDREXD
    18387U,	// t2LDREXH
    27247U,	// t2LDRHT
    30624U,	// t2LDRH_POST
    30624U,	// t2LDRH_PRE
    7759776U,	// t2LDRHi12
    26528U,	// t2LDRHi8
    7751584U,	// t2LDRHpci
    280480U,	// t2LDRHpcrel
    7763872U,	// t2LDRHs
    27224U,	// t2LDRSBT
    30237U,	// t2LDRSB_POST
    30237U,	// t2LDRSB_PRE
    7759389U,	// t2LDRSBi12
    26141U,	// t2LDRSBi8
    7751197U,	// t2LDRSBpci
    280093U,	// t2LDRSBpcrel
    7763485U,	// t2LDRSBs
    27259U,	// t2LDRSHT
    30634U,	// t2LDRSH_POST
    30634U,	// t2LDRSH_PRE
    7759786U,	// t2LDRSHi12
    26538U,	// t2LDRSHi8
    7751594U,	// t2LDRSHpci
    280490U,	// t2LDRSHpcrel
    7763882U,	// t2LDRSHs
    27291U,	// t2LDRT
    31111U,	// t2LDR_POST
    31111U,	// t2LDR_PRE
    7760263U,	// t2LDRi12
    27015U,	// t2LDRi8
    7752071U,	// t2LDRpci
    0U,	// t2LDRpci_pic
    280967U,	// t2LDRpcrel
    7764359U,	// t2LDRs
    0U,	// t2LEApcrel
    0U,	// t2LEApcrelJT
    7739549U,	// t2LSLri
    7739549U,	// t2LSLrr
    7739831U,	// t2LSRri
    7739831U,	// t2LSRrr
    2197858686U,	// t2MCR
    2197857316U,	// t2MCR2
    2197883302U,	// t2MCRR
    2197881897U,	// t2MCRR2
    30087U,	// t2MLA
    31209U,	// t2MLS
    0U,	// t2MOVCCasr
    0U,	// t2MOVCCi
    0U,	// t2MOVCCi16
    0U,	// t2MOVCCi32imm
    0U,	// t2MOVCClsl
    0U,	// t2MOVCClsr
    0U,	// t2MOVCCr
    0U,	// t2MOVCCror
    289313U,	// t2MOVSsi
    293409U,	// t2MOVSsr
    27345U,	// t2MOVTi16
    0U,	// t2MOVTi16_ga_pcrel
    0U,	// t2MOV_ga_pcrel
    7805700U,	// t2MOVi
    19225U,	// t2MOVi16
    0U,	// t2MOVi16_ga_pcrel
    0U,	// t2MOVi32imm
    7805700U,	// t2MOVr
    289540U,	// t2MOVsi
    293636U,	// t2MOVsr
    7752207U,	// t2MOVsra_flag
    7752212U,	// t2MOVsrl_flag
    201369257U,	// t2MRC
    201368586U,	// t2MRC2
    2197882541U,	// t2MRRC
    2197881871U,	// t2MRRC2
    35339U,	// t2MRS_AR
    18955U,	// t2MRS_M
    18955U,	// t2MRSbanked
    1073777163U,	// t2MRSsys_AR
    2365606332U,	// t2MSR_AR
    2365606332U,	// t2MSR_M
    234899900U,	// t2MSRbanked
    26797U,	// t2MUL
    0U,	// t2MVNCCi
    71991U,	// t2MVNi
    7805239U,	// t2MVNr
    7739703U,	// t2MVNs
    6420U,	// t2ORNri
    6420U,	// t2ORNrr
    10516U,	// t2ORNrs
    6572U,	// t2ORRri
    7739820U,	// t2ORRrr
    7743916U,	// t2ORRrs
    31287U,	// t2PKHBT
    30250U,	// t2PKHTB
    838880020U,	// t2PLDWi12
    855657236U,	// t2PLDWi8
    872442644U,	// t2PLDWs
    838878970U,	// t2PLDi12
    855656186U,	// t2PLDi8
    889227002U,	// t2PLDpci
    872441594U,	// t2PLDs
    838879205U,	// t2PLIi12
    855656421U,	// t2PLIi8
    889227237U,	// t2PLIpci
    872441829U,	// t2PLIs
    26345U,	// t2QADD
    25776U,	// t2QADD16
    25879U,	// t2QADD8
    27603U,	// t2QASX
    26319U,	// t2QDADD
    26191U,	// t2QDSUB
    27462U,	// t2QSAX
    26204U,	// t2QSUB
    25738U,	// t2QSUB16
    25840U,	// t2QSUB8
    19074U,	// t2RBIT
    7752432U,	// t2REV
    7750868U,	// t2REV16
    7751605U,	// t2REVSH
    1073776087U,	// t2RFEDB
    2147517911U,	// t2RFEDBW
    1073775979U,	// t2RFEIA
    2147517803U,	// t2RFEIAW
    7739810U,	// t2RORri
    7739810U,	// t2RORrr
    72642U,	// t2RRX
    0U,	// t2RSBSri
    0U,	// t2RSBSrs
    7738911U,	// t2RSBri
    5663U,	// t2RSBrr
    9759U,	// t2RSBrs
    25783U,	// t2SADD16
    25885U,	// t2SADD8
    27608U,	// t2SASX
    5776U,	// t2SBCri
    7739024U,	// t2SBCrr
    7743120U,	// t2SBCrs
    31668U,	// t2SBFX
    27380U,	// t2SDIV
    26712U,	// t2SEL
    25759U,	// t2SHADD16
    25864U,	// t2SHADD8
    27590U,	// t2SHASX
    27449U,	// t2SHSAX
    25721U,	// t2SHSUB16
    25825U,	// t2SHSUB8
    1073776293U,	// t2SMC
    30141U,	// t2SMLABB
    31280U,	// t2SMLABT
    30398U,	// t2SMLAD
    31594U,	// t2SMLADX
    43038U,	// t2SMLAL
    30148U,	// t2SMLALBB
    31293U,	// t2SMLALBT
    30451U,	// t2SMLALD
    31608U,	// t2SMLALDX
    30256U,	// t2SMLALTB
    31415U,	// t2SMLALTT
    30243U,	// t2SMLATB
    31408U,	// t2SMLATT
    30310U,	// t2SMLAWB
    31446U,	// t2SMLAWT
    30484U,	// t2SMLSD
    31624U,	// t2SMLSDX
    30462U,	// t2SMLSLD
    31616U,	// t2SMLSLDX
    30085U,	// t2SMMLA
    31095U,	// t2SMMLAR
    31207U,	// t2SMMLS
    31156U,	// t2SMMLSR
    26795U,	// t2SMMUL
    27030U,	// t2SMMULR
    26308U,	// t2SMUAD
    27505U,	// t2SMUADX
    26060U,	// t2SMULBB
    27205U,	// t2SMULBT
    30850U,	// t2SMULL
    26168U,	// t2SMULTB
    27327U,	// t2SMULTT
    26221U,	// t2SMULWB
    27357U,	// t2SMULWT
    26394U,	// t2SMUSD
    27535U,	// t2SMUSDX
    7898603U,	// t2SRSDB
    8029675U,	// t2SRSDB_UPD
    7898495U,	// t2SRSIA
    8029567U,	// t2SRSIA_UPD
    31270U,	// t2SSAT
    25797U,	// t2SSAT16
    27467U,	// t2SSAX
    25745U,	// t2SSUB16
    25846U,	// t2SSUB8
    3271587837U,	// t2STC2L_OFFSET
    3271587837U,	// t2STC2L_OPTION
    3271587837U,	// t2STC2L_POST
    3271587837U,	// t2STC2L_PRE
    3271586837U,	// t2STC2_OFFSET
    3271586837U,	// t2STC2_OPTION
    3271586837U,	// t2STC2_POST
    3271586837U,	// t2STC2_PRE
    3271587904U,	// t2STCL_OFFSET
    3271587904U,	// t2STCL_OPTION
    3271587904U,	// t2STCL_POST
    3271587904U,	// t2STCL_PRE
    3271587510U,	// t2STC_OFFSET
    3271587510U,	// t2STC_OPTION
    3271587510U,	// t2STC_POST
    3271587510U,	// t2STC_PRE
    18599U,	// t2STL
    17917U,	// t2STLB
    27548U,	// t2STLEX
    26235U,	// t2STLEXB
    30503U,	// t2STLEXD
    26572U,	// t2STLEXH
    18314U,	// t2STLH
    34277U,	// t2STMDB
    33572325U,	// t2STMDB_UPD
    7768262U,	// t2STMIA
    41306310U,	// t2STMIA_UPD
    27218U,	// t2STRBT
    33584656U,	// t2STRB_POST
    33584656U,	// t2STRB_PRE
    0U,	// t2STRB_preidx
    7759376U,	// t2STRBi12
    26128U,	// t2STRBi8
    7763472U,	// t2STRBs
    33621775U,	// t2STRD_POST
    33621775U,	// t2STRD_PRE
    30479U,	// t2STRDi8
    31662U,	// t2STREX
    26249U,	// t2STREXB
    30517U,	// t2STREXD
    26586U,	// t2STREXH
    27253U,	// t2STRHT
    33585061U,	// t2STRH_POST
    33585061U,	// t2STRH_PRE
    0U,	// t2STRH_preidx
    7759781U,	// t2STRHi12
    26533U,	// t2STRHi8
    7763877U,	// t2STRHs
    27302U,	// t2STRT
    33585608U,	// t2STR_POST
    33585608U,	// t2STR_PRE
    0U,	// t2STR_preidx
    7760328U,	// t2STRi12
    27080U,	// t2STRi8
    7764424U,	// t2STRs
    8161757U,	// t2SUBS_PC_LR
    0U,	// t2SUBSri
    0U,	// t2SUBSrr
    0U,	// t2SUBSrs
    7738961U,	// t2SUBri
    27401U,	// t2SUBri12
    7738961U,	// t2SUBrr
    7743057U,	// t2SUBrs
    30129U,	// t2SXTAB
    29787U,	// t2SXTAB16
    30586U,	// t2SXTAH
    7759429U,	// t2SXTB
    25707U,	// t2SXTB16
    7759803U,	// t2SXTH
    905987539U,	// t2TBB
    0U,	// t2TBB_JT
    922765190U,	// t2TBH
    0U,	// t2TBH_JT
    7752051U,	// t2TEQri
    7752051U,	// t2TEQrr
    7760243U,	// t2TEQrs
    7752364U,	// t2TSTri
    7752364U,	// t2TSTrr
    7760556U,	// t2TSTrs
    25790U,	// t2UADD16
    25891U,	// t2UADD8
    27613U,	// t2UASX
    31673U,	// t2UBFX
    414560U,	// t2UDF
    27385U,	// t2UDIV
    25767U,	// t2UHADD16
    25871U,	// t2UHADD8
    27596U,	// t2UHASX
    27455U,	// t2UHSAX
    25729U,	// t2UHSUB16
    25832U,	// t2UHSUB8
    30723U,	// t2UMAAL
    43044U,	// t2UMLAL
    30856U,	// t2UMULL
    25775U,	// t2UQADD16
    25878U,	// t2UQADD8
    27602U,	// t2UQASX
    27461U,	// t2UQSAX
    25737U,	// t2UQSUB16
    25839U,	// t2UQSUB8
    25858U,	// t2USAD8
    29914U,	// t2USADA8
    31275U,	// t2USAT
    25804U,	// t2USAT16
    27472U,	// t2USAX
    25752U,	// t2USUB16
    25852U,	// t2USUB8
    30135U,	// t2UXTAB
    29795U,	// t2UXTAB16
    30592U,	// t2UXTAH
    7759434U,	// t2UXTB
    25714U,	// t2UXTB16
    7759808U,	// t2UXTH
    947898004U,	// tADC
    0U,	// tADDframe
    26321U,	// tADDhirr
    25151185U,	// tADDi3
    947898065U,	// tADDi8
    26321U,	// tADDrSP
    26321U,	// tADDrSPi
    25151185U,	// tADDrr
    26321U,	// tADDspi
    26321U,	// tADDspr
    0U,	// tADJCALLSTACKDOWN
    0U,	// tADJCALLSTACKUP
    18818U,	// tADR
    947898118U,	// tAND
    25151920U,	// tASRri
    947898800U,	// tASRrr
    1073776047U,	// tB
    947898017U,	// tBIC
    414547U,	// tBKPT
    1090558002U,	// tBL
    1090558910U,	// tBLXi
    1090558910U,	// tBLXr
    0U,	// tBRIND
    0U,	// tBR_JTr
    1073777498U,	// tBX
    0U,	// tBX_CALL
    0U,	// tBX_RET
    0U,	// tBX_RET_vararg
    1073776047U,	// tBcc
    0U,	// tBfar
    1107448716U,	// tCBNZ
    1107448711U,	// tCBZ
    18675U,	// tCMNz
    18775U,	// tCMPhir
    18775U,	// tCMPi8
    18775U,	// tCMPr
    1157941766U,	// tCPS
    947898782U,	// tEOR
    1073777302U,	// tHINT
    414542U,	// tHLT
    0U,	// tInt_eh_sjlj_longjmp
    0U,	// tInt_eh_sjlj_setjmp
    35010U,	// tLDMIA
    0U,	// tLDMIA_UPD
    26123U,	// tLDRBi
    26123U,	// tLDRBr
    26528U,	// tLDRHi
    26528U,	// tLDRHr
    0U,	// tLDRLIT_ga_abs
    0U,	// tLDRLIT_ga_pcrel
    26141U,	// tLDRSB
    26538U,	// tLDRSH
    27015U,	// tLDRi
    18823U,	// tLDRpci
    0U,	// tLDRpci_pic
    27015U,	// tLDRr
    27015U,	// tLDRspi
    0U,	// tLEApcrel
    0U,	// tLEApcrelJT
    25151645U,	// tLSLri
    947898525U,	// tLSLrr
    25151927U,	// tLSRri
    947898807U,	// tLSRrr
    0U,	// tMOVCCr_pseudo
    1107448648U,	// tMOVSr
    293718788U,	// tMOVi8
    19204U,	// tMOVr
    25151661U,	// tMUL
    293718327U,	// tMVN
    947898796U,	// tORR
    0U,	// tPICADD
    956340571U,	// tPOP
    0U,	// tPOP_RET
    956340144U,	// tPUSH
    19184U,	// tREV
    17620U,	// tREV16
    18357U,	// tREVSH
    947898786U,	// tROR
    276940319U,	// tRSB
    947898000U,	// tSBC
    86798U,	// tSETEND
    33573062U,	// tSTMIA_UPD
    26128U,	// tSTRBi
    26128U,	// tSTRBr
    26533U,	// tSTRHi
    26533U,	// tSTRHr
    27080U,	// tSTRi
    27080U,	// tSTRr
    27080U,	// tSTRspi
    25151057U,	// tSUBi3
    947897937U,	// tSUBi8
    25151057U,	// tSUBrr
    26193U,	// tSUBspi
    1073776314U,	// tSVC
    17989U,	// tSXTB
    18363U,	// tSXTH
    0U,	// tTAILJMPd
    0U,	// tTAILJMPdND
    0U,	// tTAILJMPr
    0U,	// tTPsoft
    2376U,	// tTRAP
    19116U,	// tTST
    414486U,	// tUDF
    17994U,	// tUXTB
    18368U,	// tUXTH
    0U
  };

  static const uint32_t OpInfo2[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    0U,	// DBG_VALUE
    0U,	// REG_SEQUENCE
    0U,	// COPY
    0U,	// BUNDLE
    0U,	// LIFETIME_START
    0U,	// LIFETIME_END
    0U,	// STACKMAP
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    0U,	// STATEPOINT
    0U,	// FRAME_ALLOC
    0U,	// ABS
    0U,	// ADCri
    16384U,	// ADCrr
    32768U,	// ADCrsi
    0U,	// ADCrsr
    0U,	// ADDSri
    0U,	// ADDSrr
    0U,	// ADDSrsi
    0U,	// ADDSrsr
    0U,	// ADDri
    16384U,	// ADDrr
    32768U,	// ADDrsi
    0U,	// ADDrsr
    0U,	// ADJCALLSTACKDOWN
    0U,	// ADJCALLSTACKUP
    8U,	// ADR
    0U,	// AESD
    0U,	// AESE
    0U,	// AESIMC
    0U,	// AESMC
    0U,	// ANDri
    16384U,	// ANDrr
    32768U,	// ANDrsi
    0U,	// ANDrsr
    16384U,	// ASRi
    16384U,	// ASRr
    0U,	// B
    0U,	// BCCZi64
    0U,	// BCCi64
    16U,	// BFC
    49176U,	// BFI
    0U,	// BICri
    16384U,	// BICrr
    32768U,	// BICrsi
    0U,	// BICrsr
    0U,	// BKPT
    0U,	// BL
    0U,	// BLX
    0U,	// BLX_pred
    0U,	// BLXi
    0U,	// BL_pred
    0U,	// BMOVPCB_CALL
    0U,	// BMOVPCRX_CALL
    0U,	// BR_JTadd
    0U,	// BR_JTm
    0U,	// BR_JTr
    0U,	// BX
    0U,	// BXJ
    0U,	// BX_CALL
    0U,	// BX_RET
    0U,	// BX_pred
    0U,	// Bcc
    544U,	// CDP
    0U,	// CDP2
    0U,	// CLREX
    1024U,	// CLZ
    40U,	// CMNri
    1024U,	// CMNzrr
    48U,	// CMNzrsi
    56U,	// CMNzrsr
    40U,	// CMPri
    1024U,	// CMPrr
    48U,	// CMPrsi
    56U,	// CMPrsr
    0U,	// CONSTPOOL_ENTRY
    0U,	// COPY_STRUCT_BYVAL_I32
    0U,	// CPS1p
    0U,	// CPS2p
    1048U,	// CPS3p
    1048U,	// CRC32B
    1048U,	// CRC32CB
    1048U,	// CRC32CH
    1048U,	// CRC32CW
    1048U,	// CRC32H
    1048U,	// CRC32W
    0U,	// DBG
    0U,	// DMB
    0U,	// DSB
    0U,	// EORri
    16384U,	// EORrr
    32768U,	// EORrsi
    0U,	// EORrsr
    0U,	// ERET
    0U,	// FCONSTD
    0U,	// FCONSTS
    65U,	// FLDMXDB_UPD
    1096U,	// FLDMXIA
    65U,	// FLDMXIA_UPD
    0U,	// FMSTAT
    65U,	// FSTMXDB_UPD
    1096U,	// FSTMXIA
    65U,	// FSTMXIA_UPD
    0U,	// HINT
    0U,	// HLT
    0U,	// HVC
    0U,	// ISB
    0U,	// ITasm
    0U,	// Int_eh_sjlj_dispatchsetup
    0U,	// Int_eh_sjlj_longjmp
    0U,	// Int_eh_sjlj_setjmp
    0U,	// Int_eh_sjlj_setjmp_nofp
    80U,	// LDA
    80U,	// LDAB
    80U,	// LDAEX
    80U,	// LDAEXB
    0U,	// LDAEXD
    80U,	// LDAEXH
    80U,	// LDAH
    0U,	// LDC2L_OFFSET
    1U,	// LDC2L_OPTION
    1U,	// LDC2L_POST
    0U,	// LDC2L_PRE
    0U,	// LDC2_OFFSET
    1U,	// LDC2_OPTION
    1U,	// LDC2_POST
    0U,	// LDC2_PRE
    89U,	// LDCL_OFFSET
    65633U,	// LDCL_OPTION
    82017U,	// LDCL_POST
    105U,	// LDCL_PRE
    89U,	// LDC_OFFSET
    65633U,	// LDC_OPTION
    82017U,	// LDC_POST
    105U,	// LDC_PRE
    1096U,	// LDMDA
    65U,	// LDMDA_UPD
    1096U,	// LDMDB
    65U,	// LDMDB_UPD
    1096U,	// LDMIA
    0U,	// LDMIA_RET
    65U,	// LDMIA_UPD
    1096U,	// LDMIB
    65U,	// LDMIB_UPD
    80U,	// LDRBT_POST
    98400U,	// LDRBT_POST_IMM
    98400U,	// LDRBT_POST_REG
    98400U,	// LDRB_POST_IMM
    98400U,	// LDRB_POST_REG
    112U,	// LDRB_PRE_IMM
    120U,	// LDRB_PRE_REG
    128U,	// LDRBi12
    136U,	// LDRBrs
    114688U,	// LDRD
    1179648U,	// LDRD_POST
    147456U,	// LDRD_PRE
    80U,	// LDREX
    80U,	// LDREXB
    0U,	// LDREXD
    80U,	// LDREXH
    144U,	// LDRH
    163936U,	// LDRHTi
    180320U,	// LDRHTr
    196704U,	// LDRH_POST
    152U,	// LDRH_PRE
    0U,	// LDRLIT_ga_abs
    0U,	// LDRLIT_ga_pcrel
    0U,	// LDRLIT_ga_pcrel_ldr
    144U,	// LDRSB
    163936U,	// LDRSBTi
    180320U,	// LDRSBTr
    196704U,	// LDRSB_POST
    152U,	// LDRSB_PRE
    144U,	// LDRSH
    163936U,	// LDRSHTi
    180320U,	// LDRSHTr
    196704U,	// LDRSH_POST
    152U,	// LDRSH_PRE
    80U,	// LDRT_POST
    98400U,	// LDRT_POST_IMM
    98400U,	// LDRT_POST_REG
    98400U,	// LDR_POST_IMM
    98400U,	// LDR_POST_REG
    112U,	// LDR_PRE_IMM
    120U,	// LDR_PRE_REG
    128U,	// LDRcp
    128U,	// LDRi12
    136U,	// LDRrs
    0U,	// LEApcrel
    0U,	// LEApcrelJT
    16384U,	// LSLi
    16384U,	// LSLr
    16384U,	// LSRi
    16384U,	// LSRr
    2311712U,	// MCR
    160U,	// MCR2
    3360288U,	// MCRR
    229544U,	// MCRR2
    17842176U,	// MLA
    0U,	// MLAv5
    17842176U,	// MLS
    0U,	// MOVCCi
    0U,	// MOVCCi16
    0U,	// MOVCCi32imm
    0U,	// MOVCCr
    0U,	// MOVCCsi
    0U,	// MOVCCsr
    0U,	// MOVPCLR
    0U,	// MOVPCRX
    1048U,	// MOVTi16
    0U,	// MOVTi16_ga_pcrel
    0U,	// MOV_ga_pcrel
    0U,	// MOV_ga_pcrel_ldr
    40U,	// MOVi
    1024U,	// MOVi16
    0U,	// MOVi16_ga_pcrel
    0U,	// MOVi32imm
    1024U,	// MOVr
    1024U,	// MOVr_TC
    48U,	// MOVsi
    56U,	// MOVsr
    0U,	// MOVsra_flag
    0U,	// MOVsrl_flag
    0U,	// MRC
    0U,	// MRC2
    3360288U,	// MRRC
    229544U,	// MRRC2
    2U,	// MRS
    176U,	// MRSbanked
    2U,	// MRSsys
    64U,	// MSR
    0U,	// MSRbanked
    2U,	// MSRi
    16384U,	// MUL
    0U,	// MULv5
    0U,	// MVNCCi
    40U,	// MVNi
    1024U,	// MVNr
    48U,	// MVNsi
    56U,	// MVNsr
    0U,	// ORRri
    16384U,	// ORRrr
    32768U,	// ORRrsi
    0U,	// ORRrsr
    0U,	// PICADD
    0U,	// PICLDR
    0U,	// PICLDRB
    0U,	// PICLDRH
    0U,	// PICLDRSB
    0U,	// PICLDRSH
    0U,	// PICSTR
    0U,	// PICSTRB
    0U,	// PICSTRH
    4210688U,	// PKHBT
    5259264U,	// PKHTB
    0U,	// PLDWi12
    0U,	// PLDWrs
    0U,	// PLDi12
    0U,	// PLDrs
    0U,	// PLIi12
    0U,	// PLIrs
    16384U,	// QADD
    16384U,	// QADD16
    16384U,	// QADD8
    16384U,	// QASX
    16384U,	// QDADD
    16384U,	// QDSUB
    16384U,	// QSAX
    16384U,	// QSUB
    16384U,	// QSUB16
    16384U,	// QSUB8
    1024U,	// RBIT
    1024U,	// REV
    1024U,	// REV16
    1024U,	// REVSH
    0U,	// RFEDA
    0U,	// RFEDA_UPD
    0U,	// RFEDB
    0U,	// RFEDB_UPD
    0U,	// RFEIA
    0U,	// RFEIA_UPD
    0U,	// RFEIB
    0U,	// RFEIB_UPD
    16384U,	// RORi
    16384U,	// RORr
    0U,	// RRX
    1024U,	// RRXi
    0U,	// RSBSri
    0U,	// RSBSrsi
    0U,	// RSBSrsr
    0U,	// RSBri
    16384U,	// RSBrr
    32768U,	// RSBrsi
    0U,	// RSBrsr
    0U,	// RSCri
    16384U,	// RSCrr
    32768U,	// RSCrsi
    0U,	// RSCrsr
    16384U,	// SADD16
    16384U,	// SADD8
    16384U,	// SASX
    0U,	// SBCri
    16384U,	// SBCrr
    32768U,	// SBCrsi
    0U,	// SBCrsr
    34619392U,	// SBFX
    16384U,	// SDIV
    16384U,	// SEL
    0U,	// SETEND
    1192U,	// SHA1C
    0U,	// SHA1H
    1192U,	// SHA1M
    1192U,	// SHA1P
    1192U,	// SHA1SU0
    0U,	// SHA1SU1
    1192U,	// SHA256H
    1192U,	// SHA256H2
    0U,	// SHA256SU0
    1192U,	// SHA256SU1
    16384U,	// SHADD16
    16384U,	// SHADD8
    16384U,	// SHASX
    16384U,	// SHSAX
    16384U,	// SHSUB16
    16384U,	// SHSUB8
    0U,	// SMC
    17842176U,	// SMLABB
    17842176U,	// SMLABT
    17842176U,	// SMLAD
    17842176U,	// SMLADX
    0U,	// SMLAL
    17842176U,	// SMLALBB
    17842176U,	// SMLALBT
    17842176U,	// SMLALD
    17842176U,	// SMLALDX
    17842176U,	// SMLALTB
    17842176U,	// SMLALTT
    0U,	// SMLALv5
    17842176U,	// SMLATB
    17842176U,	// SMLATT
    17842176U,	// SMLAWB
    17842176U,	// SMLAWT
    17842176U,	// SMLSD
    17842176U,	// SMLSDX
    17842176U,	// SMLSLD
    17842176U,	// SMLSLDX
    17842176U,	// SMMLA
    17842176U,	// SMMLAR
    17842176U,	// SMMLS
    17842176U,	// SMMLSR
    16384U,	// SMMUL
    16384U,	// SMMULR
    16384U,	// SMUAD
    16384U,	// SMUADX
    16384U,	// SMULBB
    16384U,	// SMULBT
    17842176U,	// SMULL
    0U,	// SMULLv5
    16384U,	// SMULTB
    16384U,	// SMULTT
    16384U,	// SMULWB
    16384U,	// SMULWT
    16384U,	// SMUSD
    16384U,	// SMUSDX
    0U,	// SPACE
    0U,	// SRSDA
    0U,	// SRSDA_UPD
    0U,	// SRSDB
    0U,	// SRSDB_UPD
    0U,	// SRSIA
    0U,	// SRSIA_UPD
    0U,	// SRSIB
    0U,	// SRSIB_UPD
    2232U,	// SSAT
    1208U,	// SSAT16
    16384U,	// SSAX
    16384U,	// SSUB16
    16384U,	// SSUB8
    0U,	// STC2L_OFFSET
    1U,	// STC2L_OPTION
    1U,	// STC2L_POST
    0U,	// STC2L_PRE
    0U,	// STC2_OFFSET
    1U,	// STC2_OPTION
    1U,	// STC2_POST
    0U,	// STC2_PRE
    89U,	// STCL_OFFSET
    65633U,	// STCL_OPTION
    82017U,	// STCL_POST
    105U,	// STCL_PRE
    89U,	// STC_OFFSET
    65633U,	// STC_OPTION
    82017U,	// STC_POST
    105U,	// STC_PRE
    80U,	// STL
    80U,	// STLB
    245760U,	// STLEX
    245760U,	// STLEXB
    192U,	// STLEXD
    245760U,	// STLEXH
    80U,	// STLH
    1096U,	// STMDA
    65U,	// STMDA_UPD
    1096U,	// STMDB
    65U,	// STMDB_UPD
    1096U,	// STMIA
    65U,	// STMIA_UPD
    1096U,	// STMIB
    65U,	// STMIB_UPD
    80U,	// STRBT_POST
    98400U,	// STRBT_POST_IMM
    98400U,	// STRBT_POST_REG
    98400U,	// STRB_POST_IMM
    98400U,	// STRB_POST_REG
    112U,	// STRB_PRE_IMM
    120U,	// STRB_PRE_REG
    128U,	// STRBi12
    0U,	// STRBi_preidx
    0U,	// STRBr_preidx
    136U,	// STRBrs
    114688U,	// STRD
    1179672U,	// STRD_POST
    147480U,	// STRD_PRE
    245760U,	// STREX
    245760U,	// STREXB
    192U,	// STREXD
    245760U,	// STREXH
    144U,	// STRH
    163936U,	// STRHTi
    180320U,	// STRHTr
    196704U,	// STRH_POST
    152U,	// STRH_PRE
    0U,	// STRH_preidx
    80U,	// STRT_POST
    98400U,	// STRT_POST_IMM
    98400U,	// STRT_POST_REG
    98400U,	// STR_POST_IMM
    98400U,	// STR_POST_REG
    112U,	// STR_PRE_IMM
    120U,	// STR_PRE_REG
    128U,	// STRi12
    0U,	// STRi_preidx
    0U,	// STRr_preidx
    136U,	// STRrs
    0U,	// SUBS_PC_LR
    0U,	// SUBSri
    0U,	// SUBSrr
    0U,	// SUBSrsi
    0U,	// SUBSrsr
    0U,	// SUBri
    16384U,	// SUBrr
    32768U,	// SUBrsi
    0U,	// SUBrsr
    0U,	// SVC
    245760U,	// SWP
    245760U,	// SWPB
    6307840U,	// SXTAB
    6307840U,	// SXTAB16
    6307840U,	// SXTAH
    2560U,	// SXTB
    2560U,	// SXTB16
    2560U,	// SXTH
    0U,	// TAILJMPd
    0U,	// TAILJMPr
    0U,	// TCRETURNdi
    0U,	// TCRETURNri
    40U,	// TEQri
    1024U,	// TEQrr
    48U,	// TEQrsi
    56U,	// TEQrsr
    0U,	// TPsoft
    0U,	// TRAP
    0U,	// TRAPNaCl
    40U,	// TSTri
    1024U,	// TSTrr
    48U,	// TSTrsi
    56U,	// TSTrsr
    16384U,	// UADD16
    16384U,	// UADD8
    16384U,	// UASX
    34619392U,	// UBFX
    0U,	// UDF
    16384U,	// UDIV
    16384U,	// UHADD16
    16384U,	// UHADD8
    16384U,	// UHASX
    16384U,	// UHSAX
    16384U,	// UHSUB16
    16384U,	// UHSUB8
    17842176U,	// UMAAL
    0U,	// UMLAL
    0U,	// UMLALv5
    17842176U,	// UMULL
    0U,	// UMULLv5
    16384U,	// UQADD16
    16384U,	// UQADD8
    16384U,	// UQASX
    16384U,	// UQSAX
    16384U,	// UQSUB16
    16384U,	// UQSUB8
    16384U,	// USAD8
    17842176U,	// USADA8
    7356416U,	// USAT
    16384U,	// USAT16
    16384U,	// USAX
    16384U,	// USUB16
    16384U,	// USUB8
    6307840U,	// UXTAB
    6307840U,	// UXTAB16
    6307840U,	// UXTAH
    2560U,	// UXTB
    2560U,	// UXTB16
    2560U,	// UXTH
    1192U,	// VABALsv2i64
    1192U,	// VABALsv4i32
    1192U,	// VABALsv8i16
    1192U,	// VABALuv2i64
    1192U,	// VABALuv4i32
    1192U,	// VABALuv8i16
    1192U,	// VABAsv16i8
    1192U,	// VABAsv2i32
    1192U,	// VABAsv4i16
    1192U,	// VABAsv4i32
    1192U,	// VABAsv8i16
    1192U,	// VABAsv8i8
    1192U,	// VABAuv16i8
    1192U,	// VABAuv2i32
    1192U,	// VABAuv4i16
    1192U,	// VABAuv4i32
    1192U,	// VABAuv8i16
    1192U,	// VABAuv8i8
    1048U,	// VABDLsv2i64
    1048U,	// VABDLsv4i32
    1048U,	// VABDLsv8i16
    1048U,	// VABDLuv2i64
    1048U,	// VABDLuv4i32
    1048U,	// VABDLuv8i16
    263712U,	// VABDfd
    263712U,	// VABDfq
    1048U,	// VABDsv16i8
    1048U,	// VABDsv2i32
    1048U,	// VABDsv4i16
    1048U,	// VABDsv4i32
    1048U,	// VABDsv8i16
    1048U,	// VABDsv8i8
    1048U,	// VABDuv16i8
    1048U,	// VABDuv2i32
    1048U,	// VABDuv4i16
    1048U,	// VABDuv4i32
    1048U,	// VABDuv8i16
    1048U,	// VABDuv8i8
    64U,	// VABSD
    64U,	// VABSS
    64U,	// VABSfd
    64U,	// VABSfq
    0U,	// VABSv16i8
    0U,	// VABSv2i32
    0U,	// VABSv4i16
    0U,	// VABSv4i32
    0U,	// VABSv8i16
    0U,	// VABSv8i8
    263712U,	// VACGEd
    263712U,	// VACGEq
    263712U,	// VACGTd
    263712U,	// VACGTq
    263712U,	// VADDD
    1048U,	// VADDHNv2i32
    1048U,	// VADDHNv4i16
    1048U,	// VADDHNv8i8
    1048U,	// VADDLsv2i64
    1048U,	// VADDLsv4i32
    1048U,	// VADDLsv8i16
    1048U,	// VADDLuv2i64
    1048U,	// VADDLuv4i32
    1048U,	// VADDLuv8i16
    263712U,	// VADDS
    1048U,	// VADDWsv2i64
    1048U,	// VADDWsv4i32
    1048U,	// VADDWsv8i16
    1048U,	// VADDWuv2i64
    1048U,	// VADDWuv4i32
    1048U,	// VADDWuv8i16
    263712U,	// VADDfd
    263712U,	// VADDfq
    1048U,	// VADDv16i8
    1048U,	// VADDv1i64
    1048U,	// VADDv2i32
    1048U,	// VADDv2i64
    1048U,	// VADDv4i16
    1048U,	// VADDv4i32
    1048U,	// VADDv8i16
    1048U,	// VADDv8i8
    16384U,	// VANDd
    16384U,	// VANDq
    16384U,	// VBICd
    0U,	// VBICiv2i32
    0U,	// VBICiv4i16
    0U,	// VBICiv4i32
    0U,	// VBICiv8i16
    16384U,	// VBICq
    278552U,	// VBIFd
    278552U,	// VBIFq
    278552U,	// VBITd
    278552U,	// VBITq
    278552U,	// VBSLd
    278552U,	// VBSLq
    263712U,	// VCEQfd
    263712U,	// VCEQfq
    1048U,	// VCEQv16i8
    1048U,	// VCEQv2i32
    1048U,	// VCEQv4i16
    1048U,	// VCEQv4i32
    1048U,	// VCEQv8i16
    1048U,	// VCEQv8i8
    2U,	// VCEQzv16i8
    200U,	// VCEQzv2f32
    2U,	// VCEQzv2i32
    200U,	// VCEQzv4f32
    2U,	// VCEQzv4i16
    2U,	// VCEQzv4i32
    2U,	// VCEQzv8i16
    2U,	// VCEQzv8i8
    263712U,	// VCGEfd
    263712U,	// VCGEfq
    1048U,	// VCGEsv16i8
    1048U,	// VCGEsv2i32
    1048U,	// VCGEsv4i16
    1048U,	// VCGEsv4i32
    1048U,	// VCGEsv8i16
    1048U,	// VCGEsv8i8
    1048U,	// VCGEuv16i8
    1048U,	// VCGEuv2i32
    1048U,	// VCGEuv4i16
    1048U,	// VCGEuv4i32
    1048U,	// VCGEuv8i16
    1048U,	// VCGEuv8i8
    2U,	// VCGEzv16i8
    200U,	// VCGEzv2f32
    2U,	// VCGEzv2i32
    200U,	// VCGEzv4f32
    2U,	// VCGEzv4i16
    2U,	// VCGEzv4i32
    2U,	// VCGEzv8i16
    2U,	// VCGEzv8i8
    263712U,	// VCGTfd
    263712U,	// VCGTfq
    1048U,	// VCGTsv16i8
    1048U,	// VCGTsv2i32
    1048U,	// VCGTsv4i16
    1048U,	// VCGTsv4i32
    1048U,	// VCGTsv8i16
    1048U,	// VCGTsv8i8
    1048U,	// VCGTuv16i8
    1048U,	// VCGTuv2i32
    1048U,	// VCGTuv4i16
    1048U,	// VCGTuv4i32
    1048U,	// VCGTuv8i16
    1048U,	// VCGTuv8i8
    2U,	// VCGTzv16i8
    200U,	// VCGTzv2f32
    2U,	// VCGTzv2i32
    200U,	// VCGTzv4f32
    2U,	// VCGTzv4i16
    2U,	// VCGTzv4i32
    2U,	// VCGTzv8i16
    2U,	// VCGTzv8i8
    2U,	// VCLEzv16i8
    200U,	// VCLEzv2f32
    2U,	// VCLEzv2i32
    200U,	// VCLEzv4f32
    2U,	// VCLEzv4i16
    2U,	// VCLEzv4i32
    2U,	// VCLEzv8i16
    2U,	// VCLEzv8i8
    0U,	// VCLSv16i8
    0U,	// VCLSv2i32
    0U,	// VCLSv4i16
    0U,	// VCLSv4i32
    0U,	// VCLSv8i16
    0U,	// VCLSv8i8
    2U,	// VCLTzv16i8
    200U,	// VCLTzv2f32
    2U,	// VCLTzv2i32
    200U,	// VCLTzv4f32
    2U,	// VCLTzv4i16
    2U,	// VCLTzv4i32
    2U,	// VCLTzv8i16
    2U,	// VCLTzv8i8
    0U,	// VCLZv16i8
    0U,	// VCLZv2i32
    0U,	// VCLZv4i16
    0U,	// VCLZv4i32
    0U,	// VCLZv8i16
    0U,	// VCLZv8i8
    64U,	// VCMPD
    64U,	// VCMPED
    64U,	// VCMPES
    0U,	// VCMPEZD
    0U,	// VCMPEZS
    64U,	// VCMPS
    0U,	// VCMPZD
    0U,	// VCMPZS
    1024U,	// VCNTd
    1024U,	// VCNTq
    0U,	// VCVTANSD
    0U,	// VCVTANSQ
    0U,	// VCVTANUD
    0U,	// VCVTANUQ
    0U,	// VCVTASD
    0U,	// VCVTASS
    0U,	// VCVTAUD
    0U,	// VCVTAUS
    0U,	// VCVTBDH
    0U,	// VCVTBHD
    0U,	// VCVTBHS
    0U,	// VCVTBSH
    0U,	// VCVTDS
    0U,	// VCVTMNSD
    0U,	// VCVTMNSQ
    0U,	// VCVTMNUD
    0U,	// VCVTMNUQ
    0U,	// VCVTMSD
    0U,	// VCVTMSS
    0U,	// VCVTMUD
    0U,	// VCVTMUS
    0U,	// VCVTNNSD
    0U,	// VCVTNNSQ
    0U,	// VCVTNNUD
    0U,	// VCVTNNUQ
    0U,	// VCVTNSD
    0U,	// VCVTNSS
    0U,	// VCVTNUD
    0U,	// VCVTNUS
    0U,	// VCVTPNSD
    0U,	// VCVTPNSQ
    0U,	// VCVTPNUD
    0U,	// VCVTPNUQ
    0U,	// VCVTPSD
    0U,	// VCVTPSS
    0U,	// VCVTPUD
    0U,	// VCVTPUS
    0U,	// VCVTSD
    0U,	// VCVTTDH
    0U,	// VCVTTHD
    0U,	// VCVTTHS
    0U,	// VCVTTSH
    0U,	// VCVTf2h
    0U,	// VCVTf2sd
    0U,	// VCVTf2sq
    0U,	// VCVTf2ud
    0U,	// VCVTf2uq
    67U,	// VCVTf2xsd
    67U,	// VCVTf2xsq
    67U,	// VCVTf2xud
    67U,	// VCVTf2xuq
    0U,	// VCVTh2f
    0U,	// VCVTs2fd
    0U,	// VCVTs2fq
    0U,	// VCVTu2fd
    0U,	// VCVTu2fq
    67U,	// VCVTxs2fd
    67U,	// VCVTxs2fq
    67U,	// VCVTxu2fd
    67U,	// VCVTxu2fq
    263712U,	// VDIVD
    263712U,	// VDIVS
    1024U,	// VDUP16d
    1024U,	// VDUP16q
    1024U,	// VDUP32d
    1024U,	// VDUP32q
    1024U,	// VDUP8d
    1024U,	// VDUP8q
    3072U,	// VDUPLN16d
    3072U,	// VDUPLN16q
    3072U,	// VDUPLN32d
    3072U,	// VDUPLN32q
    3072U,	// VDUPLN8d
    3072U,	// VDUPLN8q
    16384U,	// VEORd
    16384U,	// VEORq
    17842176U,	// VEXTd16
    17842176U,	// VEXTd32
    17842176U,	// VEXTd8
    17842176U,	// VEXTq16
    17842176U,	// VEXTq32
    17842176U,	// VEXTq64
    17842176U,	// VEXTq8
    265763U,	// VFMAD
    265763U,	// VFMAS
    265763U,	// VFMAfd
    265763U,	// VFMAfq
    265763U,	// VFMSD
    265763U,	// VFMSS
    265763U,	// VFMSfd
    265763U,	// VFMSfq
    265763U,	// VFNMAD
    265763U,	// VFNMAS
    265763U,	// VFNMSD
    265763U,	// VFNMSS
    3072U,	// VGETLNi32
    3U,	// VGETLNs16
    3U,	// VGETLNs8
    3U,	// VGETLNu16
    3U,	// VGETLNu8
    1048U,	// VHADDsv16i8
    1048U,	// VHADDsv2i32
    1048U,	// VHADDsv4i16
    1048U,	// VHADDsv4i32
    1048U,	// VHADDsv8i16
    1048U,	// VHADDsv8i8
    1048U,	// VHADDuv16i8
    1048U,	// VHADDuv2i32
    1048U,	// VHADDuv4i16
    1048U,	// VHADDuv4i32
    1048U,	// VHADDuv8i16
    1048U,	// VHADDuv8i8
    1048U,	// VHSUBsv16i8
    1048U,	// VHSUBsv2i32
    1048U,	// VHSUBsv4i16
    1048U,	// VHSUBsv4i32
    1048U,	// VHSUBsv8i16
    1048U,	// VHSUBsv8i8
    1048U,	// VHSUBuv16i8
    1048U,	// VHSUBuv2i32
    1048U,	// VHSUBuv4i16
    1048U,	// VHSUBuv4i32
    1048U,	// VHSUBuv8i16
    1048U,	// VHSUBuv8i8
    67U,	// VLD1DUPd16
    211U,	// VLD1DUPd16wb_fixed
    4131U,	// VLD1DUPd16wb_register
    67U,	// VLD1DUPd32
    211U,	// VLD1DUPd32wb_fixed
    4131U,	// VLD1DUPd32wb_register
    67U,	// VLD1DUPd8
    211U,	// VLD1DUPd8wb_fixed
    4131U,	// VLD1DUPd8wb_register
    67U,	// VLD1DUPq16
    211U,	// VLD1DUPq16wb_fixed
    4131U,	// VLD1DUPq16wb_register
    67U,	// VLD1DUPq32
    211U,	// VLD1DUPq32wb_fixed
    4131U,	// VLD1DUPq32wb_register
    67U,	// VLD1DUPq8
    211U,	// VLD1DUPq8wb_fixed
    4131U,	// VLD1DUPq8wb_register
    299740U,	// VLD1LNd16
    316132U,	// VLD1LNd16_UPD
    299740U,	// VLD1LNd32
    316132U,	// VLD1LNd32_UPD
    299740U,	// VLD1LNd8
    316132U,	// VLD1LNd8_UPD
    1256U,	// VLD1LNdAsm_16
    1256U,	// VLD1LNdAsm_32
    1256U,	// VLD1LNdAsm_8
    5352U,	// VLD1LNdWB_fixed_Asm_16
    5352U,	// VLD1LNdWB_fixed_Asm_32
    5352U,	// VLD1LNdWB_fixed_Asm_8
    327912U,	// VLD1LNdWB_register_Asm_16
    327912U,	// VLD1LNdWB_register_Asm_32
    327912U,	// VLD1LNdWB_register_Asm_8
    0U,	// VLD1LNq16Pseudo
    0U,	// VLD1LNq16Pseudo_UPD
    0U,	// VLD1LNq32Pseudo
    0U,	// VLD1LNq32Pseudo_UPD
    0U,	// VLD1LNq8Pseudo
    0U,	// VLD1LNq8Pseudo_UPD
    67U,	// VLD1d16
    67U,	// VLD1d16Q
    211U,	// VLD1d16Qwb_fixed
    4131U,	// VLD1d16Qwb_register
    67U,	// VLD1d16T
    211U,	// VLD1d16Twb_fixed
    4131U,	// VLD1d16Twb_register
    211U,	// VLD1d16wb_fixed
    4131U,	// VLD1d16wb_register
    67U,	// VLD1d32
    67U,	// VLD1d32Q
    211U,	// VLD1d32Qwb_fixed
    4131U,	// VLD1d32Qwb_register
    67U,	// VLD1d32T
    211U,	// VLD1d32Twb_fixed
    4131U,	// VLD1d32Twb_register
    211U,	// VLD1d32wb_fixed
    4131U,	// VLD1d32wb_register
    67U,	// VLD1d64
    67U,	// VLD1d64Q
    0U,	// VLD1d64QPseudo
    0U,	// VLD1d64QPseudoWB_fixed
    0U,	// VLD1d64QPseudoWB_register
    211U,	// VLD1d64Qwb_fixed
    4131U,	// VLD1d64Qwb_register
    67U,	// VLD1d64T
    0U,	// VLD1d64TPseudo
    0U,	// VLD1d64TPseudoWB_fixed
    0U,	// VLD1d64TPseudoWB_register
    211U,	// VLD1d64Twb_fixed
    4131U,	// VLD1d64Twb_register
    211U,	// VLD1d64wb_fixed
    4131U,	// VLD1d64wb_register
    67U,	// VLD1d8
    67U,	// VLD1d8Q
    211U,	// VLD1d8Qwb_fixed
    4131U,	// VLD1d8Qwb_register
    67U,	// VLD1d8T
    211U,	// VLD1d8Twb_fixed
    4131U,	// VLD1d8Twb_register
    211U,	// VLD1d8wb_fixed
    4131U,	// VLD1d8wb_register
    67U,	// VLD1q16
    211U,	// VLD1q16wb_fixed
    4131U,	// VLD1q16wb_register
    67U,	// VLD1q32
    211U,	// VLD1q32wb_fixed
    4131U,	// VLD1q32wb_register
    67U,	// VLD1q64
    211U,	// VLD1q64wb_fixed
    4131U,	// VLD1q64wb_register
    67U,	// VLD1q8
    211U,	// VLD1q8wb_fixed
    4131U,	// VLD1q8wb_register
    67U,	// VLD2DUPd16
    211U,	// VLD2DUPd16wb_fixed
    4131U,	// VLD2DUPd16wb_register
    67U,	// VLD2DUPd16x2
    211U,	// VLD2DUPd16x2wb_fixed
    4131U,	// VLD2DUPd16x2wb_register
    67U,	// VLD2DUPd32
    211U,	// VLD2DUPd32wb_fixed
    4131U,	// VLD2DUPd32wb_register
    67U,	// VLD2DUPd32x2
    211U,	// VLD2DUPd32x2wb_fixed
    4131U,	// VLD2DUPd32x2wb_register
    67U,	// VLD2DUPd8
    211U,	// VLD2DUPd8wb_fixed
    4131U,	// VLD2DUPd8wb_register
    67U,	// VLD2DUPd8x2
    211U,	// VLD2DUPd8x2wb_fixed
    4131U,	// VLD2DUPd8x2wb_register
    349924U,	// VLD2LNd16
    0U,	// VLD2LNd16Pseudo
    0U,	// VLD2LNd16Pseudo_UPD
    366836U,	// VLD2LNd16_UPD
    349924U,	// VLD2LNd32
    0U,	// VLD2LNd32Pseudo
    0U,	// VLD2LNd32Pseudo_UPD
    366836U,	// VLD2LNd32_UPD
    349924U,	// VLD2LNd8
    0U,	// VLD2LNd8Pseudo
    0U,	// VLD2LNd8Pseudo_UPD
    366836U,	// VLD2LNd8_UPD
    1256U,	// VLD2LNdAsm_16
    1256U,	// VLD2LNdAsm_32
    1256U,	// VLD2LNdAsm_8
    5352U,	// VLD2LNdWB_fixed_Asm_16
    5352U,	// VLD2LNdWB_fixed_Asm_32
    5352U,	// VLD2LNdWB_fixed_Asm_8
    327912U,	// VLD2LNdWB_register_Asm_16
    327912U,	// VLD2LNdWB_register_Asm_32
    327912U,	// VLD2LNdWB_register_Asm_8
    349924U,	// VLD2LNq16
    0U,	// VLD2LNq16Pseudo
    0U,	// VLD2LNq16Pseudo_UPD
    366836U,	// VLD2LNq16_UPD
    349924U,	// VLD2LNq32
    0U,	// VLD2LNq32Pseudo
    0U,	// VLD2LNq32Pseudo_UPD
    366836U,	// VLD2LNq32_UPD
    1256U,	// VLD2LNqAsm_16
    1256U,	// VLD2LNqAsm_32
    5352U,	// VLD2LNqWB_fixed_Asm_16
    5352U,	// VLD2LNqWB_fixed_Asm_32
    327912U,	// VLD2LNqWB_register_Asm_16
    327912U,	// VLD2LNqWB_register_Asm_32
    67U,	// VLD2b16
    211U,	// VLD2b16wb_fixed
    4131U,	// VLD2b16wb_register
    67U,	// VLD2b32
    211U,	// VLD2b32wb_fixed
    4131U,	// VLD2b32wb_register
    67U,	// VLD2b8
    211U,	// VLD2b8wb_fixed
    4131U,	// VLD2b8wb_register
    67U,	// VLD2d16
    211U,	// VLD2d16wb_fixed
    4131U,	// VLD2d16wb_register
    67U,	// VLD2d32
    211U,	// VLD2d32wb_fixed
    4131U,	// VLD2d32wb_register
    67U,	// VLD2d8
    211U,	// VLD2d8wb_fixed
    4131U,	// VLD2d8wb_register
    67U,	// VLD2q16
    0U,	// VLD2q16Pseudo
    0U,	// VLD2q16PseudoWB_fixed
    0U,	// VLD2q16PseudoWB_register
    211U,	// VLD2q16wb_fixed
    4131U,	// VLD2q16wb_register
    67U,	// VLD2q32
    0U,	// VLD2q32Pseudo
    0U,	// VLD2q32PseudoWB_fixed
    0U,	// VLD2q32PseudoWB_register
    211U,	// VLD2q32wb_fixed
    4131U,	// VLD2q32wb_register
    67U,	// VLD2q8
    0U,	// VLD2q8Pseudo
    0U,	// VLD2q8PseudoWB_fixed
    0U,	// VLD2q8PseudoWB_register
    211U,	// VLD2q8wb_fixed
    4131U,	// VLD2q8wb_register
    6908U,	// VLD3DUPd16
    0U,	// VLD3DUPd16Pseudo
    0U,	// VLD3DUPd16Pseudo_UPD
    384252U,	// VLD3DUPd16_UPD
    6908U,	// VLD3DUPd32
    0U,	// VLD3DUPd32Pseudo
    0U,	// VLD3DUPd32Pseudo_UPD
    384252U,	// VLD3DUPd32_UPD
    6908U,	// VLD3DUPd8
    0U,	// VLD3DUPd8Pseudo
    0U,	// VLD3DUPd8Pseudo_UPD
    384252U,	// VLD3DUPd8_UPD
    0U,	// VLD3DUPdAsm_16
    0U,	// VLD3DUPdAsm_32
    0U,	// VLD3DUPdAsm_8
    4U,	// VLD3DUPdWB_fixed_Asm_16
    4U,	// VLD3DUPdWB_fixed_Asm_32
    4U,	// VLD3DUPdWB_fixed_Asm_8
    1192U,	// VLD3DUPdWB_register_Asm_16
    1192U,	// VLD3DUPdWB_register_Asm_32
    1192U,	// VLD3DUPdWB_register_Asm_8
    6908U,	// VLD3DUPq16
    384252U,	// VLD3DUPq16_UPD
    6908U,	// VLD3DUPq32
    384252U,	// VLD3DUPq32_UPD
    6908U,	// VLD3DUPq8
    384252U,	// VLD3DUPq8_UPD
    0U,	// VLD3DUPqAsm_16
    0U,	// VLD3DUPqAsm_32
    0U,	// VLD3DUPqAsm_8
    4U,	// VLD3DUPqWB_fixed_Asm_16
    4U,	// VLD3DUPqWB_fixed_Asm_32
    4U,	// VLD3DUPqWB_fixed_Asm_8
    1192U,	// VLD3DUPqWB_register_Asm_16
    1192U,	// VLD3DUPqWB_register_Asm_32
    1192U,	// VLD3DUPqWB_register_Asm_8
    399604U,	// VLD3LNd16
    0U,	// VLD3LNd16Pseudo
    0U,	// VLD3LNd16Pseudo_UPD
    414468U,	// VLD3LNd16_UPD
    399604U,	// VLD3LNd32
    0U,	// VLD3LNd32Pseudo
    0U,	// VLD3LNd32Pseudo_UPD
    414468U,	// VLD3LNd32_UPD
    399604U,	// VLD3LNd8
    0U,	// VLD3LNd8Pseudo
    0U,	// VLD3LNd8Pseudo_UPD
    414468U,	// VLD3LNd8_UPD
    1256U,	// VLD3LNdAsm_16
    1256U,	// VLD3LNdAsm_32
    1256U,	// VLD3LNdAsm_8
    5352U,	// VLD3LNdWB_fixed_Asm_16
    5352U,	// VLD3LNdWB_fixed_Asm_32
    5352U,	// VLD3LNdWB_fixed_Asm_8
    327912U,	// VLD3LNdWB_register_Asm_16
    327912U,	// VLD3LNdWB_register_Asm_32
    327912U,	// VLD3LNdWB_register_Asm_8
    399604U,	// VLD3LNq16
    0U,	// VLD3LNq16Pseudo
    0U,	// VLD3LNq16Pseudo_UPD
    414468U,	// VLD3LNq16_UPD
    399604U,	// VLD3LNq32
    0U,	// VLD3LNq32Pseudo
    0U,	// VLD3LNq32Pseudo_UPD
    414468U,	// VLD3LNq32_UPD
    1256U,	// VLD3LNqAsm_16
    1256U,	// VLD3LNqAsm_32
    5352U,	// VLD3LNqWB_fixed_Asm_16
    5352U,	// VLD3LNqWB_fixed_Asm_32
    327912U,	// VLD3LNqWB_register_Asm_16
    327912U,	// VLD3LNqWB_register_Asm_32
    58736640U,	// VLD3d16
    0U,	// VLD3d16Pseudo
    0U,	// VLD3d16Pseudo_UPD
    75513856U,	// VLD3d16_UPD
    58736640U,	// VLD3d32
    0U,	// VLD3d32Pseudo
    0U,	// VLD3d32Pseudo_UPD
    75513856U,	// VLD3d32_UPD
    58736640U,	// VLD3d8
    0U,	// VLD3d8Pseudo
    0U,	// VLD3d8Pseudo_UPD
    75513856U,	// VLD3d8_UPD
    67U,	// VLD3dAsm_16
    67U,	// VLD3dAsm_32
    67U,	// VLD3dAsm_8
    211U,	// VLD3dWB_fixed_Asm_16
    211U,	// VLD3dWB_fixed_Asm_32
    211U,	// VLD3dWB_fixed_Asm_8
    265763U,	// VLD3dWB_register_Asm_16
    265763U,	// VLD3dWB_register_Asm_32
    265763U,	// VLD3dWB_register_Asm_8
    58736640U,	// VLD3q16
    0U,	// VLD3q16Pseudo_UPD
    75513856U,	// VLD3q16_UPD
    0U,	// VLD3q16oddPseudo
    0U,	// VLD3q16oddPseudo_UPD
    58736640U,	// VLD3q32
    0U,	// VLD3q32Pseudo_UPD
    75513856U,	// VLD3q32_UPD
    0U,	// VLD3q32oddPseudo
    0U,	// VLD3q32oddPseudo_UPD
    58736640U,	// VLD3q8
    0U,	// VLD3q8Pseudo_UPD
    75513856U,	// VLD3q8_UPD
    0U,	// VLD3q8oddPseudo
    0U,	// VLD3q8oddPseudo_UPD
    0U,	// VLD3qAsm_16
    0U,	// VLD3qAsm_32
    0U,	// VLD3qAsm_8
    4U,	// VLD3qWB_fixed_Asm_16
    4U,	// VLD3qWB_fixed_Asm_32
    4U,	// VLD3qWB_fixed_Asm_8
    1192U,	// VLD3qWB_register_Asm_16
    1192U,	// VLD3qWB_register_Asm_32
    1192U,	// VLD3qWB_register_Asm_8
    269580U,	// VLD4DUPd16
    0U,	// VLD4DUPd16Pseudo
    0U,	// VLD4DUPd16Pseudo_UPD
    7948U,	// VLD4DUPd16_UPD
    269580U,	// VLD4DUPd32
    0U,	// VLD4DUPd32Pseudo
    0U,	// VLD4DUPd32Pseudo_UPD
    7948U,	// VLD4DUPd32_UPD
    269580U,	// VLD4DUPd8
    0U,	// VLD4DUPd8Pseudo
    0U,	// VLD4DUPd8Pseudo_UPD
    7948U,	// VLD4DUPd8_UPD
    0U,	// VLD4DUPdAsm_16
    0U,	// VLD4DUPdAsm_32
    0U,	// VLD4DUPdAsm_8
    4U,	// VLD4DUPdWB_fixed_Asm_16
    4U,	// VLD4DUPdWB_fixed_Asm_32
    4U,	// VLD4DUPdWB_fixed_Asm_8
    1192U,	// VLD4DUPdWB_register_Asm_16
    1192U,	// VLD4DUPdWB_register_Asm_32
    1192U,	// VLD4DUPdWB_register_Asm_8
    269580U,	// VLD4DUPq16
    7948U,	// VLD4DUPq16_UPD
    269580U,	// VLD4DUPq32
    7948U,	// VLD4DUPq32_UPD
    269580U,	// VLD4DUPq8
    7948U,	// VLD4DUPq8_UPD
    0U,	// VLD4DUPqAsm_16
    0U,	// VLD4DUPqAsm_32
    0U,	// VLD4DUPqAsm_8
    4U,	// VLD4DUPqWB_fixed_Asm_16
    4U,	// VLD4DUPqWB_fixed_Asm_32
    4U,	// VLD4DUPqWB_fixed_Asm_8
    1192U,	// VLD4DUPqWB_register_Asm_16
    1192U,	// VLD4DUPqWB_register_Asm_32
    1192U,	// VLD4DUPqWB_register_Asm_8
    93607684U,	// VLD4LNd16
    0U,	// VLD4LNd16Pseudo
    0U,	// VLD4LNd16Pseudo_UPD
    276U,	// VLD4LNd16_UPD
    93607684U,	// VLD4LNd32
    0U,	// VLD4LNd32Pseudo
    0U,	// VLD4LNd32Pseudo_UPD
    276U,	// VLD4LNd32_UPD
    93607684U,	// VLD4LNd8
    0U,	// VLD4LNd8Pseudo
    0U,	// VLD4LNd8Pseudo_UPD
    276U,	// VLD4LNd8_UPD
    1256U,	// VLD4LNdAsm_16
    1256U,	// VLD4LNdAsm_32
    1256U,	// VLD4LNdAsm_8
    5352U,	// VLD4LNdWB_fixed_Asm_16
    5352U,	// VLD4LNdWB_fixed_Asm_32
    5352U,	// VLD4LNdWB_fixed_Asm_8
    327912U,	// VLD4LNdWB_register_Asm_16
    327912U,	// VLD4LNdWB_register_Asm_32
    327912U,	// VLD4LNdWB_register_Asm_8
    93607684U,	// VLD4LNq16
    0U,	// VLD4LNq16Pseudo
    0U,	// VLD4LNq16Pseudo_UPD
    276U,	// VLD4LNq16_UPD
    93607684U,	// VLD4LNq32
    0U,	// VLD4LNq32Pseudo
    0U,	// VLD4LNq32Pseudo_UPD
    276U,	// VLD4LNq32_UPD
    1256U,	// VLD4LNqAsm_16
    1256U,	// VLD4LNqAsm_32
    5352U,	// VLD4LNqWB_fixed_Asm_16
    5352U,	// VLD4LNqWB_fixed_Asm_32
    327912U,	// VLD4LNqWB_register_Asm_16
    327912U,	// VLD4LNqWB_register_Asm_32
    286277632U,	// VLD4d16
    0U,	// VLD4d16Pseudo
    0U,	// VLD4d16Pseudo_UPD
    823148544U,	// VLD4d16_UPD
    286277632U,	// VLD4d32
    0U,	// VLD4d32Pseudo
    0U,	// VLD4d32Pseudo_UPD
    823148544U,	// VLD4d32_UPD
    286277632U,	// VLD4d8
    0U,	// VLD4d8Pseudo
    0U,	// VLD4d8Pseudo_UPD
    823148544U,	// VLD4d8_UPD
    67U,	// VLD4dAsm_16
    67U,	// VLD4dAsm_32
    67U,	// VLD4dAsm_8
    211U,	// VLD4dWB_fixed_Asm_16
    211U,	// VLD4dWB_fixed_Asm_32
    211U,	// VLD4dWB_fixed_Asm_8
    265763U,	// VLD4dWB_register_Asm_16
    265763U,	// VLD4dWB_register_Asm_32
    265763U,	// VLD4dWB_register_Asm_8
    286277632U,	// VLD4q16
    0U,	// VLD4q16Pseudo_UPD
    823148544U,	// VLD4q16_UPD
    0U,	// VLD4q16oddPseudo
    0U,	// VLD4q16oddPseudo_UPD
    286277632U,	// VLD4q32
    0U,	// VLD4q32Pseudo_UPD
    823148544U,	// VLD4q32_UPD
    0U,	// VLD4q32oddPseudo
    0U,	// VLD4q32oddPseudo_UPD
    286277632U,	// VLD4q8
    0U,	// VLD4q8Pseudo_UPD
    823148544U,	// VLD4q8_UPD
    0U,	// VLD4q8oddPseudo
    0U,	// VLD4q8oddPseudo_UPD
    0U,	// VLD4qAsm_16
    0U,	// VLD4qAsm_32
    0U,	// VLD4qAsm_8
    4U,	// VLD4qWB_fixed_Asm_16
    4U,	// VLD4qWB_fixed_Asm_32
    4U,	// VLD4qWB_fixed_Asm_8
    1192U,	// VLD4qWB_register_Asm_16
    1192U,	// VLD4qWB_register_Asm_32
    1192U,	// VLD4qWB_register_Asm_8
    65U,	// VLDMDDB_UPD
    1096U,	// VLDMDIA
    65U,	// VLDMDIA_UPD
    0U,	// VLDMQIA
    65U,	// VLDMSDB_UPD
    1096U,	// VLDMSIA
    65U,	// VLDMSIA_UPD
    280U,	// VLDRD
    280U,	// VLDRS
    1048U,	// VMAXNMD
    1048U,	// VMAXNMND
    1048U,	// VMAXNMNQ
    1048U,	// VMAXNMS
    263712U,	// VMAXfd
    263712U,	// VMAXfq
    1048U,	// VMAXsv16i8
    1048U,	// VMAXsv2i32
    1048U,	// VMAXsv4i16
    1048U,	// VMAXsv4i32
    1048U,	// VMAXsv8i16
    1048U,	// VMAXsv8i8
    1048U,	// VMAXuv16i8
    1048U,	// VMAXuv2i32
    1048U,	// VMAXuv4i16
    1048U,	// VMAXuv4i32
    1048U,	// VMAXuv8i16
    1048U,	// VMAXuv8i8
    1048U,	// VMINNMD
    1048U,	// VMINNMND
    1048U,	// VMINNMNQ
    1048U,	// VMINNMS
    263712U,	// VMINfd
    263712U,	// VMINfq
    1048U,	// VMINsv16i8
    1048U,	// VMINsv2i32
    1048U,	// VMINsv4i16
    1048U,	// VMINsv4i32
    1048U,	// VMINsv8i16
    1048U,	// VMINsv8i8
    1048U,	// VMINuv16i8
    1048U,	// VMINuv2i32
    1048U,	// VMINuv4i16
    1048U,	// VMINuv4i32
    1048U,	// VMINuv8i16
    1048U,	// VMINuv8i8
    265763U,	// VMLAD
    8360U,	// VMLALslsv2i32
    8360U,	// VMLALslsv4i16
    8360U,	// VMLALsluv2i32
    8360U,	// VMLALsluv4i16
    1192U,	// VMLALsv2i64
    1192U,	// VMLALsv4i32
    1192U,	// VMLALsv8i16
    1192U,	// VMLALuv2i64
    1192U,	// VMLALuv4i32
    1192U,	// VMLALuv8i16
    265763U,	// VMLAS
    265763U,	// VMLAfd
    265763U,	// VMLAfq
    429603U,	// VMLAslfd
    429603U,	// VMLAslfq
    8360U,	// VMLAslv2i32
    8360U,	// VMLAslv4i16
    8360U,	// VMLAslv4i32
    8360U,	// VMLAslv8i16
    1192U,	// VMLAv16i8
    1192U,	// VMLAv2i32
    1192U,	// VMLAv4i16
    1192U,	// VMLAv4i32
    1192U,	// VMLAv8i16
    1192U,	// VMLAv8i8
    265763U,	// VMLSD
    8360U,	// VMLSLslsv2i32
    8360U,	// VMLSLslsv4i16
    8360U,	// VMLSLsluv2i32
    8360U,	// VMLSLsluv4i16
    1192U,	// VMLSLsv2i64
    1192U,	// VMLSLsv4i32
    1192U,	// VMLSLsv8i16
    1192U,	// VMLSLuv2i64
    1192U,	// VMLSLuv4i32
    1192U,	// VMLSLuv8i16
    265763U,	// VMLSS
    265763U,	// VMLSfd
    265763U,	// VMLSfq
    429603U,	// VMLSslfd
    429603U,	// VMLSslfq
    8360U,	// VMLSslv2i32
    8360U,	// VMLSslv4i16
    8360U,	// VMLSslv4i32
    8360U,	// VMLSslv8i16
    1192U,	// VMLSv16i8
    1192U,	// VMLSv2i32
    1192U,	// VMLSv4i16
    1192U,	// VMLSv4i32
    1192U,	// VMLSv8i16
    1192U,	// VMLSv8i8
    64U,	// VMOVD
    0U,	// VMOVD0
    16384U,	// VMOVDRR
    0U,	// VMOVDcc
    0U,	// VMOVLsv2i64
    0U,	// VMOVLsv4i32
    0U,	// VMOVLsv8i16
    0U,	// VMOVLuv2i64
    0U,	// VMOVLuv4i32
    0U,	// VMOVLuv8i16
    0U,	// VMOVNv2i32
    0U,	// VMOVNv4i16
    0U,	// VMOVNv8i8
    0U,	// VMOVQ0
    16384U,	// VMOVRRD
    17842176U,	// VMOVRRS
    1024U,	// VMOVRS
    64U,	// VMOVS
    1024U,	// VMOVSR
    17842176U,	// VMOVSRR
    0U,	// VMOVScc
    0U,	// VMOVv16i8
    0U,	// VMOVv1i64
    0U,	// VMOVv2f32
    0U,	// VMOVv2i32
    0U,	// VMOVv2i64
    0U,	// VMOVv4f32
    0U,	// VMOVv4i16
    0U,	// VMOVv4i32
    0U,	// VMOVv8i16
    0U,	// VMOVv8i8
    4U,	// VMRS
    5U,	// VMRS_FPEXC
    5U,	// VMRS_FPINST
    5U,	// VMRS_FPINST2
    5U,	// VMRS_FPSID
    6U,	// VMRS_MVFR0
    6U,	// VMRS_MVFR1
    6U,	// VMRS_MVFR2
    0U,	// VMSR
    0U,	// VMSR_FPEXC
    0U,	// VMSR_FPINST
    0U,	// VMSR_FPINST2
    0U,	// VMSR_FPSID
    263712U,	// VMULD
    1048U,	// VMULLp64
    0U,	// VMULLp8
    8728U,	// VMULLslsv2i32
    8728U,	// VMULLslsv4i16
    8728U,	// VMULLsluv2i32
    8728U,	// VMULLsluv4i16
    1048U,	// VMULLsv2i64
    1048U,	// VMULLsv4i32
    1048U,	// VMULLsv8i16
    1048U,	// VMULLuv2i64
    1048U,	// VMULLuv4i32
    1048U,	// VMULLuv8i16
    263712U,	// VMULS
    263712U,	// VMULfd
    263712U,	// VMULfq
    0U,	// VMULpd
    0U,	// VMULpq
    443936U,	// VMULslfd
    443936U,	// VMULslfq
    8728U,	// VMULslv2i32
    8728U,	// VMULslv4i16
    8728U,	// VMULslv4i32
    8728U,	// VMULslv8i16
    1048U,	// VMULv16i8
    1048U,	// VMULv2i32
    1048U,	// VMULv4i16
    1048U,	// VMULv4i32
    1048U,	// VMULv8i16
    1048U,	// VMULv8i8
    1024U,	// VMVNd
    1024U,	// VMVNq
    0U,	// VMVNv2i32
    0U,	// VMVNv4i16
    0U,	// VMVNv4i32
    0U,	// VMVNv8i16
    64U,	// VNEGD
    64U,	// VNEGS
    64U,	// VNEGf32q
    64U,	// VNEGfd
    0U,	// VNEGs16d
    0U,	// VNEGs16q
    0U,	// VNEGs32d
    0U,	// VNEGs32q
    0U,	// VNEGs8d
    0U,	// VNEGs8q
    265763U,	// VNMLAD
    265763U,	// VNMLAS
    265763U,	// VNMLSD
    265763U,	// VNMLSS
    263712U,	// VNMULD
    263712U,	// VNMULS
    16384U,	// VORNd
    16384U,	// VORNq
    16384U,	// VORRd
    0U,	// VORRiv2i32
    0U,	// VORRiv4i16
    0U,	// VORRiv4i32
    0U,	// VORRiv8i16
    16384U,	// VORRq
    0U,	// VPADALsv16i8
    0U,	// VPADALsv2i32
    0U,	// VPADALsv4i16
    0U,	// VPADALsv4i32
    0U,	// VPADALsv8i16
    0U,	// VPADALsv8i8
    0U,	// VPADALuv16i8
    0U,	// VPADALuv2i32
    0U,	// VPADALuv4i16
    0U,	// VPADALuv4i32
    0U,	// VPADALuv8i16
    0U,	// VPADALuv8i8
    0U,	// VPADDLsv16i8
    0U,	// VPADDLsv2i32
    0U,	// VPADDLsv4i16
    0U,	// VPADDLsv4i32
    0U,	// VPADDLsv8i16
    0U,	// VPADDLsv8i8
    0U,	// VPADDLuv16i8
    0U,	// VPADDLuv2i32
    0U,	// VPADDLuv4i16
    0U,	// VPADDLuv4i32
    0U,	// VPADDLuv8i16
    0U,	// VPADDLuv8i8
    263712U,	// VPADDf
    1048U,	// VPADDi16
    1048U,	// VPADDi32
    1048U,	// VPADDi8
    263712U,	// VPMAXf
    1048U,	// VPMAXs16
    1048U,	// VPMAXs32
    1048U,	// VPMAXs8
    1048U,	// VPMAXu16
    1048U,	// VPMAXu32
    1048U,	// VPMAXu8
    263712U,	// VPMINf
    1048U,	// VPMINs16
    1048U,	// VPMINs32
    1048U,	// VPMINs8
    1048U,	// VPMINu16
    1048U,	// VPMINu32
    1048U,	// VPMINu8
    0U,	// VQABSv16i8
    0U,	// VQABSv2i32
    0U,	// VQABSv4i16
    0U,	// VQABSv4i32
    0U,	// VQABSv8i16
    0U,	// VQABSv8i8
    1048U,	// VQADDsv16i8
    1048U,	// VQADDsv1i64
    1048U,	// VQADDsv2i32
    1048U,	// VQADDsv2i64
    1048U,	// VQADDsv4i16
    1048U,	// VQADDsv4i32
    1048U,	// VQADDsv8i16
    1048U,	// VQADDsv8i8
    1048U,	// VQADDuv16i8
    1048U,	// VQADDuv1i64
    1048U,	// VQADDuv2i32
    1048U,	// VQADDuv2i64
    1048U,	// VQADDuv4i16
    1048U,	// VQADDuv4i32
    1048U,	// VQADDuv8i16
    1048U,	// VQADDuv8i8
    8360U,	// VQDMLALslv2i32
    8360U,	// VQDMLALslv4i16
    1192U,	// VQDMLALv2i64
    1192U,	// VQDMLALv4i32
    8360U,	// VQDMLSLslv2i32
    8360U,	// VQDMLSLslv4i16
    1192U,	// VQDMLSLv2i64
    1192U,	// VQDMLSLv4i32
    8728U,	// VQDMULHslv2i32
    8728U,	// VQDMULHslv4i16
    8728U,	// VQDMULHslv4i32
    8728U,	// VQDMULHslv8i16
    1048U,	// VQDMULHv2i32
    1048U,	// VQDMULHv4i16
    1048U,	// VQDMULHv4i32
    1048U,	// VQDMULHv8i16
    8728U,	// VQDMULLslv2i32
    8728U,	// VQDMULLslv4i16
    1048U,	// VQDMULLv2i64
    1048U,	// VQDMULLv4i32
    0U,	// VQMOVNsuv2i32
    0U,	// VQMOVNsuv4i16
    0U,	// VQMOVNsuv8i8
    0U,	// VQMOVNsv2i32
    0U,	// VQMOVNsv4i16
    0U,	// VQMOVNsv8i8
    0U,	// VQMOVNuv2i32
    0U,	// VQMOVNuv4i16
    0U,	// VQMOVNuv8i8
    0U,	// VQNEGv16i8
    0U,	// VQNEGv2i32
    0U,	// VQNEGv4i16
    0U,	// VQNEGv4i32
    0U,	// VQNEGv8i16
    0U,	// VQNEGv8i8
    8728U,	// VQRDMULHslv2i32
    8728U,	// VQRDMULHslv4i16
    8728U,	// VQRDMULHslv4i32
    8728U,	// VQRDMULHslv8i16
    1048U,	// VQRDMULHv2i32
    1048U,	// VQRDMULHv4i16
    1048U,	// VQRDMULHv4i32
    1048U,	// VQRDMULHv8i16
    1048U,	// VQRSHLsv16i8
    1048U,	// VQRSHLsv1i64
    1048U,	// VQRSHLsv2i32
    1048U,	// VQRSHLsv2i64
    1048U,	// VQRSHLsv4i16
    1048U,	// VQRSHLsv4i32
    1048U,	// VQRSHLsv8i16
    1048U,	// VQRSHLsv8i8
    1048U,	// VQRSHLuv16i8
    1048U,	// VQRSHLuv1i64
    1048U,	// VQRSHLuv2i32
    1048U,	// VQRSHLuv2i64
    1048U,	// VQRSHLuv4i16
    1048U,	// VQRSHLuv4i32
    1048U,	// VQRSHLuv8i16
    1048U,	// VQRSHLuv8i8
    1048U,	// VQRSHRNsv2i32
    1048U,	// VQRSHRNsv4i16
    1048U,	// VQRSHRNsv8i8
    1048U,	// VQRSHRNuv2i32
    1048U,	// VQRSHRNuv4i16
    1048U,	// VQRSHRNuv8i8
    1048U,	// VQRSHRUNv2i32
    1048U,	// VQRSHRUNv4i16
    1048U,	// VQRSHRUNv8i8
    1048U,	// VQSHLsiv16i8
    1048U,	// VQSHLsiv1i64
    1048U,	// VQSHLsiv2i32
    1048U,	// VQSHLsiv2i64
    1048U,	// VQSHLsiv4i16
    1048U,	// VQSHLsiv4i32
    1048U,	// VQSHLsiv8i16
    1048U,	// VQSHLsiv8i8
    1048U,	// VQSHLsuv16i8
    1048U,	// VQSHLsuv1i64
    1048U,	// VQSHLsuv2i32
    1048U,	// VQSHLsuv2i64
    1048U,	// VQSHLsuv4i16
    1048U,	// VQSHLsuv4i32
    1048U,	// VQSHLsuv8i16
    1048U,	// VQSHLsuv8i8
    1048U,	// VQSHLsv16i8
    1048U,	// VQSHLsv1i64
    1048U,	// VQSHLsv2i32
    1048U,	// VQSHLsv2i64
    1048U,	// VQSHLsv4i16
    1048U,	// VQSHLsv4i32
    1048U,	// VQSHLsv8i16
    1048U,	// VQSHLsv8i8
    1048U,	// VQSHLuiv16i8
    1048U,	// VQSHLuiv1i64
    1048U,	// VQSHLuiv2i32
    1048U,	// VQSHLuiv2i64
    1048U,	// VQSHLuiv4i16
    1048U,	// VQSHLuiv4i32
    1048U,	// VQSHLuiv8i16
    1048U,	// VQSHLuiv8i8
    1048U,	// VQSHLuv16i8
    1048U,	// VQSHLuv1i64
    1048U,	// VQSHLuv2i32
    1048U,	// VQSHLuv2i64
    1048U,	// VQSHLuv4i16
    1048U,	// VQSHLuv4i32
    1048U,	// VQSHLuv8i16
    1048U,	// VQSHLuv8i8
    1048U,	// VQSHRNsv2i32
    1048U,	// VQSHRNsv4i16
    1048U,	// VQSHRNsv8i8
    1048U,	// VQSHRNuv2i32
    1048U,	// VQSHRNuv4i16
    1048U,	// VQSHRNuv8i8
    1048U,	// VQSHRUNv2i32
    1048U,	// VQSHRUNv4i16
    1048U,	// VQSHRUNv8i8
    1048U,	// VQSUBsv16i8
    1048U,	// VQSUBsv1i64
    1048U,	// VQSUBsv2i32
    1048U,	// VQSUBsv2i64
    1048U,	// VQSUBsv4i16
    1048U,	// VQSUBsv4i32
    1048U,	// VQSUBsv8i16
    1048U,	// VQSUBsv8i8
    1048U,	// VQSUBuv16i8
    1048U,	// VQSUBuv1i64
    1048U,	// VQSUBuv2i32
    1048U,	// VQSUBuv2i64
    1048U,	// VQSUBuv4i16
    1048U,	// VQSUBuv4i32
    1048U,	// VQSUBuv8i16
    1048U,	// VQSUBuv8i8
    1048U,	// VRADDHNv2i32
    1048U,	// VRADDHNv4i16
    1048U,	// VRADDHNv8i8
    0U,	// VRECPEd
    64U,	// VRECPEfd
    64U,	// VRECPEfq
    0U,	// VRECPEq
    263712U,	// VRECPSfd
    263712U,	// VRECPSfq
    1024U,	// VREV16d8
    1024U,	// VREV16q8
    1024U,	// VREV32d16
    1024U,	// VREV32d8
    1024U,	// VREV32q16
    1024U,	// VREV32q8
    1024U,	// VREV64d16
    1024U,	// VREV64d32
    1024U,	// VREV64d8
    1024U,	// VREV64q16
    1024U,	// VREV64q32
    1024U,	// VREV64q8
    1048U,	// VRHADDsv16i8
    1048U,	// VRHADDsv2i32
    1048U,	// VRHADDsv4i16
    1048U,	// VRHADDsv4i32
    1048U,	// VRHADDsv8i16
    1048U,	// VRHADDsv8i8
    1048U,	// VRHADDuv16i8
    1048U,	// VRHADDuv2i32
    1048U,	// VRHADDuv4i16
    1048U,	// VRHADDuv4i32
    1048U,	// VRHADDuv8i16
    1048U,	// VRHADDuv8i8
    0U,	// VRINTAD
    0U,	// VRINTAND
    0U,	// VRINTANQ
    0U,	// VRINTAS
    0U,	// VRINTMD
    0U,	// VRINTMND
    0U,	// VRINTMNQ
    0U,	// VRINTMS
    0U,	// VRINTND
    0U,	// VRINTNND
    0U,	// VRINTNNQ
    0U,	// VRINTNS
    0U,	// VRINTPD
    0U,	// VRINTPND
    0U,	// VRINTPNQ
    0U,	// VRINTPS
    64U,	// VRINTRD
    64U,	// VRINTRS
    64U,	// VRINTXD
    0U,	// VRINTXND
    0U,	// VRINTXNQ
    64U,	// VRINTXS
    64U,	// VRINTZD
    0U,	// VRINTZND
    0U,	// VRINTZNQ
    64U,	// VRINTZS
    1048U,	// VRSHLsv16i8
    1048U,	// VRSHLsv1i64
    1048U,	// VRSHLsv2i32
    1048U,	// VRSHLsv2i64
    1048U,	// VRSHLsv4i16
    1048U,	// VRSHLsv4i32
    1048U,	// VRSHLsv8i16
    1048U,	// VRSHLsv8i8
    1048U,	// VRSHLuv16i8
    1048U,	// VRSHLuv1i64
    1048U,	// VRSHLuv2i32
    1048U,	// VRSHLuv2i64
    1048U,	// VRSHLuv4i16
    1048U,	// VRSHLuv4i32
    1048U,	// VRSHLuv8i16
    1048U,	// VRSHLuv8i8
    1048U,	// VRSHRNv2i32
    1048U,	// VRSHRNv4i16
    1048U,	// VRSHRNv8i8
    1048U,	// VRSHRsv16i8
    1048U,	// VRSHRsv1i64
    1048U,	// VRSHRsv2i32
    1048U,	// VRSHRsv2i64
    1048U,	// VRSHRsv4i16
    1048U,	// VRSHRsv4i32
    1048U,	// VRSHRsv8i16
    1048U,	// VRSHRsv8i8
    1048U,	// VRSHRuv16i8
    1048U,	// VRSHRuv1i64
    1048U,	// VRSHRuv2i32
    1048U,	// VRSHRuv2i64
    1048U,	// VRSHRuv4i16
    1048U,	// VRSHRuv4i32
    1048U,	// VRSHRuv8i16
    1048U,	// VRSHRuv8i8
    0U,	// VRSQRTEd
    64U,	// VRSQRTEfd
    64U,	// VRSQRTEfq
    0U,	// VRSQRTEq
    263712U,	// VRSQRTSfd
    263712U,	// VRSQRTSfq
    1192U,	// VRSRAsv16i8
    1192U,	// VRSRAsv1i64
    1192U,	// VRSRAsv2i32
    1192U,	// VRSRAsv2i64
    1192U,	// VRSRAsv4i16
    1192U,	// VRSRAsv4i32
    1192U,	// VRSRAsv8i16
    1192U,	// VRSRAsv8i8
    1192U,	// VRSRAuv16i8
    1192U,	// VRSRAuv1i64
    1192U,	// VRSRAuv2i32
    1192U,	// VRSRAuv2i64
    1192U,	// VRSRAuv4i16
    1192U,	// VRSRAuv4i32
    1192U,	// VRSRAuv8i16
    1192U,	// VRSRAuv8i8
    1048U,	// VRSUBHNv2i32
    1048U,	// VRSUBHNv4i16
    1048U,	// VRSUBHNv8i8
    1048U,	// VSELEQD
    1048U,	// VSELEQS
    1048U,	// VSELGED
    1048U,	// VSELGES
    1048U,	// VSELGTD
    1048U,	// VSELGTS
    1048U,	// VSELVSD
    1048U,	// VSELVSS
    6U,	// VSETLNi16
    6U,	// VSETLNi32
    6U,	// VSETLNi8
    1048U,	// VSHLLi16
    1048U,	// VSHLLi32
    1048U,	// VSHLLi8
    1048U,	// VSHLLsv2i64
    1048U,	// VSHLLsv4i32
    1048U,	// VSHLLsv8i16
    1048U,	// VSHLLuv2i64
    1048U,	// VSHLLuv4i32
    1048U,	// VSHLLuv8i16
    1048U,	// VSHLiv16i8
    1048U,	// VSHLiv1i64
    1048U,	// VSHLiv2i32
    1048U,	// VSHLiv2i64
    1048U,	// VSHLiv4i16
    1048U,	// VSHLiv4i32
    1048U,	// VSHLiv8i16
    1048U,	// VSHLiv8i8
    1048U,	// VSHLsv16i8
    1048U,	// VSHLsv1i64
    1048U,	// VSHLsv2i32
    1048U,	// VSHLsv2i64
    1048U,	// VSHLsv4i16
    1048U,	// VSHLsv4i32
    1048U,	// VSHLsv8i16
    1048U,	// VSHLsv8i8
    1048U,	// VSHLuv16i8
    1048U,	// VSHLuv1i64
    1048U,	// VSHLuv2i32
    1048U,	// VSHLuv2i64
    1048U,	// VSHLuv4i16
    1048U,	// VSHLuv4i32
    1048U,	// VSHLuv8i16
    1048U,	// VSHLuv8i8
    1048U,	// VSHRNv2i32
    1048U,	// VSHRNv4i16
    1048U,	// VSHRNv8i8
    1048U,	// VSHRsv16i8
    1048U,	// VSHRsv1i64
    1048U,	// VSHRsv2i32
    1048U,	// VSHRsv2i64
    1048U,	// VSHRsv4i16
    1048U,	// VSHRsv4i32
    1048U,	// VSHRsv8i16
    1048U,	// VSHRsv8i8
    1048U,	// VSHRuv16i8
    1048U,	// VSHRuv1i64
    1048U,	// VSHRuv2i32
    1048U,	// VSHRuv2i64
    1048U,	// VSHRuv4i16
    1048U,	// VSHRuv4i32
    1048U,	// VSHRuv8i16
    1048U,	// VSHRuv8i8
    0U,	// VSHTOD
    0U,	// VSHTOS
    0U,	// VSITOD
    0U,	// VSITOS
    278552U,	// VSLIv16i8
    278552U,	// VSLIv1i64
    278552U,	// VSLIv2i32
    278552U,	// VSLIv2i64
    278552U,	// VSLIv4i16
    278552U,	// VSLIv4i32
    278552U,	// VSLIv8i16
    278552U,	// VSLIv8i8
    7U,	// VSLTOD
    7U,	// VSLTOS
    64U,	// VSQRTD
    64U,	// VSQRTS
    1192U,	// VSRAsv16i8
    1192U,	// VSRAsv1i64
    1192U,	// VSRAsv2i32
    1192U,	// VSRAsv2i64
    1192U,	// VSRAsv4i16
    1192U,	// VSRAsv4i32
    1192U,	// VSRAsv8i16
    1192U,	// VSRAsv8i8
    1192U,	// VSRAuv16i8
    1192U,	// VSRAuv1i64
    1192U,	// VSRAuv2i32
    1192U,	// VSRAuv2i64
    1192U,	// VSRAuv4i16
    1192U,	// VSRAuv4i32
    1192U,	// VSRAuv8i16
    1192U,	// VSRAuv8i8
    278552U,	// VSRIv16i8
    278552U,	// VSRIv1i64
    278552U,	// VSRIv2i32
    278552U,	// VSRIv2i64
    278552U,	// VSRIv4i16
    278552U,	// VSRIv4i32
    278552U,	// VSRIv8i16
    278552U,	// VSRIv8i8
    292U,	// VST1LNd16
    10785580U,	// VST1LNd16_UPD
    292U,	// VST1LNd32
    10785580U,	// VST1LNd32_UPD
    292U,	// VST1LNd8
    10785580U,	// VST1LNd8_UPD
    1256U,	// VST1LNdAsm_16
    1256U,	// VST1LNdAsm_32
    1256U,	// VST1LNdAsm_8
    5352U,	// VST1LNdWB_fixed_Asm_16
    5352U,	// VST1LNdWB_fixed_Asm_32
    5352U,	// VST1LNdWB_fixed_Asm_8
    327912U,	// VST1LNdWB_register_Asm_16
    327912U,	// VST1LNdWB_register_Asm_32
    327912U,	// VST1LNdWB_register_Asm_8
    0U,	// VST1LNq16Pseudo
    0U,	// VST1LNq16Pseudo_UPD
    0U,	// VST1LNq32Pseudo
    0U,	// VST1LNq32Pseudo_UPD
    0U,	// VST1LNq8Pseudo
    0U,	// VST1LNq8Pseudo_UPD
    0U,	// VST1d16
    0U,	// VST1d16Q
    0U,	// VST1d16Qwb_fixed
    0U,	// VST1d16Qwb_register
    0U,	// VST1d16T
    0U,	// VST1d16Twb_fixed
    0U,	// VST1d16Twb_register
    0U,	// VST1d16wb_fixed
    0U,	// VST1d16wb_register
    0U,	// VST1d32
    0U,	// VST1d32Q
    0U,	// VST1d32Qwb_fixed
    0U,	// VST1d32Qwb_register
    0U,	// VST1d32T
    0U,	// VST1d32Twb_fixed
    0U,	// VST1d32Twb_register
    0U,	// VST1d32wb_fixed
    0U,	// VST1d32wb_register
    0U,	// VST1d64
    0U,	// VST1d64Q
    0U,	// VST1d64QPseudo
    0U,	// VST1d64QPseudoWB_fixed
    0U,	// VST1d64QPseudoWB_register
    0U,	// VST1d64Qwb_fixed
    0U,	// VST1d64Qwb_register
    0U,	// VST1d64T
    0U,	// VST1d64TPseudo
    0U,	// VST1d64TPseudoWB_fixed
    0U,	// VST1d64TPseudoWB_register
    0U,	// VST1d64Twb_fixed
    0U,	// VST1d64Twb_register
    0U,	// VST1d64wb_fixed
    0U,	// VST1d64wb_register
    0U,	// VST1d8
    0U,	// VST1d8Q
    0U,	// VST1d8Qwb_fixed
    0U,	// VST1d8Qwb_register
    0U,	// VST1d8T
    0U,	// VST1d8Twb_fixed
    0U,	// VST1d8Twb_register
    0U,	// VST1d8wb_fixed
    0U,	// VST1d8wb_register
    0U,	// VST1q16
    0U,	// VST1q16wb_fixed
    0U,	// VST1q16wb_register
    0U,	// VST1q32
    0U,	// VST1q32wb_fixed
    0U,	// VST1q32wb_register
    0U,	// VST1q64
    0U,	// VST1q64wb_fixed
    0U,	// VST1q64wb_register
    0U,	// VST1q8
    0U,	// VST1q8wb_fixed
    0U,	// VST1q8wb_register
    110384860U,	// VST2LNd16
    0U,	// VST2LNd16Pseudo
    0U,	// VST2LNd16Pseudo_UPD
    464612U,	// VST2LNd16_UPD
    110384860U,	// VST2LNd32
    0U,	// VST2LNd32Pseudo
    0U,	// VST2LNd32Pseudo_UPD
    464612U,	// VST2LNd32_UPD
    110384860U,	// VST2LNd8
    0U,	// VST2LNd8Pseudo
    0U,	// VST2LNd8Pseudo_UPD
    464612U,	// VST2LNd8_UPD
    1256U,	// VST2LNdAsm_16
    1256U,	// VST2LNdAsm_32
    1256U,	// VST2LNdAsm_8
    5352U,	// VST2LNdWB_fixed_Asm_16
    5352U,	// VST2LNdWB_fixed_Asm_32
    5352U,	// VST2LNdWB_fixed_Asm_8
    327912U,	// VST2LNdWB_register_Asm_16
    327912U,	// VST2LNdWB_register_Asm_32
    327912U,	// VST2LNdWB_register_Asm_8
    110384860U,	// VST2LNq16
    0U,	// VST2LNq16Pseudo
    0U,	// VST2LNq16Pseudo_UPD
    464612U,	// VST2LNq16_UPD
    110384860U,	// VST2LNq32
    0U,	// VST2LNq32Pseudo
    0U,	// VST2LNq32Pseudo_UPD
    464612U,	// VST2LNq32_UPD
    1256U,	// VST2LNqAsm_16
    1256U,	// VST2LNqAsm_32
    5352U,	// VST2LNqWB_fixed_Asm_16
    5352U,	// VST2LNqWB_fixed_Asm_32
    327912U,	// VST2LNqWB_register_Asm_16
    327912U,	// VST2LNqWB_register_Asm_32
    0U,	// VST2b16
    0U,	// VST2b16wb_fixed
    0U,	// VST2b16wb_register
    0U,	// VST2b32
    0U,	// VST2b32wb_fixed
    0U,	// VST2b32wb_register
    0U,	// VST2b8
    0U,	// VST2b8wb_fixed
    0U,	// VST2b8wb_register
    0U,	// VST2d16
    0U,	// VST2d16wb_fixed
    0U,	// VST2d16wb_register
    0U,	// VST2d32
    0U,	// VST2d32wb_fixed
    0U,	// VST2d32wb_register
    0U,	// VST2d8
    0U,	// VST2d8wb_fixed
    0U,	// VST2d8wb_register
    0U,	// VST2q16
    0U,	// VST2q16Pseudo
    0U,	// VST2q16PseudoWB_fixed
    0U,	// VST2q16PseudoWB_register
    0U,	// VST2q16wb_fixed
    0U,	// VST2q16wb_register
    0U,	// VST2q32
    0U,	// VST2q32Pseudo
    0U,	// VST2q32PseudoWB_fixed
    0U,	// VST2q32PseudoWB_register
    0U,	// VST2q32wb_fixed
    0U,	// VST2q32wb_register
    0U,	// VST2q8
    0U,	// VST2q8Pseudo
    0U,	// VST2q8PseudoWB_fixed
    0U,	// VST2q8PseudoWB_register
    0U,	// VST2q8wb_fixed
    0U,	// VST2q8wb_register
    127162156U,	// VST3LNd16
    0U,	// VST3LNd16Pseudo
    0U,	// VST3LNd16Pseudo_UPD
    308U,	// VST3LNd16_UPD
    127162156U,	// VST3LNd32
    0U,	// VST3LNd32Pseudo
    0U,	// VST3LNd32Pseudo_UPD
    308U,	// VST3LNd32_UPD
    127162156U,	// VST3LNd8
    0U,	// VST3LNd8Pseudo
    0U,	// VST3LNd8Pseudo_UPD
    308U,	// VST3LNd8_UPD
    1256U,	// VST3LNdAsm_16
    1256U,	// VST3LNdAsm_32
    1256U,	// VST3LNdAsm_8
    5352U,	// VST3LNdWB_fixed_Asm_16
    5352U,	// VST3LNdWB_fixed_Asm_32
    5352U,	// VST3LNdWB_fixed_Asm_8
    327912U,	// VST3LNdWB_register_Asm_16
    327912U,	// VST3LNdWB_register_Asm_32
    327912U,	// VST3LNdWB_register_Asm_8
    127162156U,	// VST3LNq16
    0U,	// VST3LNq16Pseudo
    0U,	// VST3LNq16Pseudo_UPD
    308U,	// VST3LNq16_UPD
    127162156U,	// VST3LNq32
    0U,	// VST3LNq32Pseudo
    0U,	// VST3LNq32Pseudo_UPD
    308U,	// VST3LNq32_UPD
    1256U,	// VST3LNqAsm_16
    1256U,	// VST3LNqAsm_32
    5352U,	// VST3LNqWB_fixed_Asm_16
    5352U,	// VST3LNqWB_fixed_Asm_32
    327912U,	// VST3LNqWB_register_Asm_16
    327912U,	// VST3LNqWB_register_Asm_32
    142934184U,	// VST3d16
    0U,	// VST3d16Pseudo
    0U,	// VST3d16Pseudo_UPD
    9528U,	// VST3d16_UPD
    142934184U,	// VST3d32
    0U,	// VST3d32Pseudo
    0U,	// VST3d32Pseudo_UPD
    9528U,	// VST3d32_UPD
    142934184U,	// VST3d8
    0U,	// VST3d8Pseudo
    0U,	// VST3d8Pseudo_UPD
    9528U,	// VST3d8_UPD
    67U,	// VST3dAsm_16
    67U,	// VST3dAsm_32
    67U,	// VST3dAsm_8
    211U,	// VST3dWB_fixed_Asm_16
    211U,	// VST3dWB_fixed_Asm_32
    211U,	// VST3dWB_fixed_Asm_8
    265763U,	// VST3dWB_register_Asm_16
    265763U,	// VST3dWB_register_Asm_32
    265763U,	// VST3dWB_register_Asm_8
    142934184U,	// VST3q16
    0U,	// VST3q16Pseudo_UPD
    9528U,	// VST3q16_UPD
    0U,	// VST3q16oddPseudo
    0U,	// VST3q16oddPseudo_UPD
    142934184U,	// VST3q32
    0U,	// VST3q32Pseudo_UPD
    9528U,	// VST3q32_UPD
    0U,	// VST3q32oddPseudo
    0U,	// VST3q32oddPseudo_UPD
    142934184U,	// VST3q8
    0U,	// VST3q8Pseudo_UPD
    9528U,	// VST3q8_UPD
    0U,	// VST3q8oddPseudo
    0U,	// VST3q8oddPseudo_UPD
    0U,	// VST3qAsm_16
    0U,	// VST3qAsm_32
    0U,	// VST3qAsm_8
    4U,	// VST3qWB_fixed_Asm_16
    4U,	// VST3qWB_fixed_Asm_32
    4U,	// VST3qWB_fixed_Asm_8
    1192U,	// VST3qWB_register_Asm_16
    1192U,	// VST3qWB_register_Asm_32
    1192U,	// VST3qWB_register_Asm_8
    160716516U,	// VST4LNd16
    0U,	// VST4LNd16Pseudo
    0U,	// VST4LNd16Pseudo_UPD
    9972U,	// VST4LNd16_UPD
    160716516U,	// VST4LNd32
    0U,	// VST4LNd32Pseudo
    0U,	// VST4LNd32Pseudo_UPD
    9972U,	// VST4LNd32_UPD
    160716516U,	// VST4LNd8
    0U,	// VST4LNd8Pseudo
    0U,	// VST4LNd8Pseudo_UPD
    9972U,	// VST4LNd8_UPD
    1256U,	// VST4LNdAsm_16
    1256U,	// VST4LNdAsm_32
    1256U,	// VST4LNdAsm_8
    5352U,	// VST4LNdWB_fixed_Asm_16
    5352U,	// VST4LNdWB_fixed_Asm_32
    5352U,	// VST4LNdWB_fixed_Asm_8
    327912U,	// VST4LNdWB_register_Asm_16
    327912U,	// VST4LNdWB_register_Asm_32
    327912U,	// VST4LNdWB_register_Asm_8
    160716516U,	// VST4LNq16
    0U,	// VST4LNq16Pseudo
    0U,	// VST4LNq16Pseudo_UPD
    9972U,	// VST4LNq16_UPD
    160716516U,	// VST4LNq32
    0U,	// VST4LNq32Pseudo
    0U,	// VST4LNq32Pseudo_UPD
    9972U,	// VST4LNq32_UPD
    1256U,	// VST4LNqAsm_16
    1256U,	// VST4LNqAsm_32
    5352U,	// VST4LNqWB_fixed_Asm_16
    5352U,	// VST4LNqWB_fixed_Asm_32
    327912U,	// VST4LNqWB_register_Asm_16
    327912U,	// VST4LNqWB_register_Asm_32
    169148584U,	// VST4d16
    0U,	// VST4d16Pseudo
    0U,	// VST4d16Pseudo_UPD
    475448U,	// VST4d16_UPD
    169148584U,	// VST4d32
    0U,	// VST4d32Pseudo
    0U,	// VST4d32Pseudo_UPD
    475448U,	// VST4d32_UPD
    169148584U,	// VST4d8
    0U,	// VST4d8Pseudo
    0U,	// VST4d8Pseudo_UPD
    475448U,	// VST4d8_UPD
    67U,	// VST4dAsm_16
    67U,	// VST4dAsm_32
    67U,	// VST4dAsm_8
    211U,	// VST4dWB_fixed_Asm_16
    211U,	// VST4dWB_fixed_Asm_32
    211U,	// VST4dWB_fixed_Asm_8
    265763U,	// VST4dWB_register_Asm_16
    265763U,	// VST4dWB_register_Asm_32
    265763U,	// VST4dWB_register_Asm_8
    169148584U,	// VST4q16
    0U,	// VST4q16Pseudo_UPD
    475448U,	// VST4q16_UPD
    0U,	// VST4q16oddPseudo
    0U,	// VST4q16oddPseudo_UPD
    169148584U,	// VST4q32
    0U,	// VST4q32Pseudo_UPD
    475448U,	// VST4q32_UPD
    0U,	// VST4q32oddPseudo
    0U,	// VST4q32oddPseudo_UPD
    169148584U,	// VST4q8
    0U,	// VST4q8Pseudo_UPD
    475448U,	// VST4q8_UPD
    0U,	// VST4q8oddPseudo
    0U,	// VST4q8oddPseudo_UPD
    0U,	// VST4qAsm_16
    0U,	// VST4qAsm_32
    0U,	// VST4qAsm_8
    4U,	// VST4qWB_fixed_Asm_16
    4U,	// VST4qWB_fixed_Asm_32
    4U,	// VST4qWB_fixed_Asm_8
    1192U,	// VST4qWB_register_Asm_16
    1192U,	// VST4qWB_register_Asm_32
    1192U,	// VST4qWB_register_Asm_8
    65U,	// VSTMDDB_UPD
    1096U,	// VSTMDIA
    65U,	// VSTMDIA_UPD
    0U,	// VSTMQIA
    65U,	// VSTMSDB_UPD
    1096U,	// VSTMSIA
    65U,	// VSTMSIA_UPD
    280U,	// VSTRD
    280U,	// VSTRS
    263712U,	// VSUBD
    1048U,	// VSUBHNv2i32
    1048U,	// VSUBHNv4i16
    1048U,	// VSUBHNv8i8
    1048U,	// VSUBLsv2i64
    1048U,	// VSUBLsv4i32
    1048U,	// VSUBLsv8i16
    1048U,	// VSUBLuv2i64
    1048U,	// VSUBLuv4i32
    1048U,	// VSUBLuv8i16
    263712U,	// VSUBS
    1048U,	// VSUBWsv2i64
    1048U,	// VSUBWsv4i32
    1048U,	// VSUBWsv8i16
    1048U,	// VSUBWuv2i64
    1048U,	// VSUBWuv4i32
    1048U,	// VSUBWuv8i16
    263712U,	// VSUBfd
    263712U,	// VSUBfq
    1048U,	// VSUBv16i8
    1048U,	// VSUBv1i64
    1048U,	// VSUBv2i32
    1048U,	// VSUBv2i64
    1048U,	// VSUBv4i16
    1048U,	// VSUBv4i32
    1048U,	// VSUBv8i16
    1048U,	// VSUBv8i8
    1024U,	// VSWPd
    1024U,	// VSWPq
    320U,	// VTBL1
    328U,	// VTBL2
    336U,	// VTBL3
    0U,	// VTBL3Pseudo
    344U,	// VTBL4
    0U,	// VTBL4Pseudo
    352U,	// VTBX1
    360U,	// VTBX2
    368U,	// VTBX3
    0U,	// VTBX3Pseudo
    376U,	// VTBX4
    0U,	// VTBX4Pseudo
    0U,	// VTOSHD
    0U,	// VTOSHS
    0U,	// VTOSIRD
    0U,	// VTOSIRS
    0U,	// VTOSIZD
    0U,	// VTOSIZS
    7U,	// VTOSLD
    7U,	// VTOSLS
    0U,	// VTOUHD
    0U,	// VTOUHS
    0U,	// VTOUIRD
    0U,	// VTOUIRS
    0U,	// VTOUIZD
    0U,	// VTOUIZS
    7U,	// VTOULD
    7U,	// VTOULS
    1024U,	// VTRNd16
    1024U,	// VTRNd32
    1024U,	// VTRNd8
    1024U,	// VTRNq16
    1024U,	// VTRNq32
    1024U,	// VTRNq8
    16384U,	// VTSTv16i8
    16384U,	// VTSTv2i32
    16384U,	// VTSTv4i16
    16384U,	// VTSTv4i32
    16384U,	// VTSTv8i16
    16384U,	// VTSTv8i8
    0U,	// VUHTOD
    0U,	// VUHTOS
    0U,	// VUITOD
    0U,	// VUITOS
    7U,	// VULTOD
    7U,	// VULTOS
    1024U,	// VUZPd16
    1024U,	// VUZPd8
    1024U,	// VUZPq16
    1024U,	// VUZPq32
    1024U,	// VUZPq8
    1024U,	// VZIPd16
    1024U,	// VZIPd8
    1024U,	// VZIPq16
    1024U,	// VZIPq32
    1024U,	// VZIPq8
    0U,	// WIN__CHKSTK
    10312U,	// sysLDMDA
    385U,	// sysLDMDA_UPD
    10312U,	// sysLDMDB
    385U,	// sysLDMDB_UPD
    10312U,	// sysLDMIA
    385U,	// sysLDMIA_UPD
    10312U,	// sysLDMIB
    385U,	// sysLDMIB_UPD
    10312U,	// sysSTMDA
    385U,	// sysSTMDA_UPD
    10312U,	// sysSTMDB
    385U,	// sysSTMDB_UPD
    10312U,	// sysSTMIA
    385U,	// sysSTMIA_UPD
    10312U,	// sysSTMIB
    385U,	// sysSTMIB_UPD
    0U,	// t2ABS
    16384U,	// t2ADCri
    16384U,	// t2ADCrr
    491520U,	// t2ADCrs
    0U,	// t2ADDSri
    0U,	// t2ADDSrr
    0U,	// t2ADDSrs
    16384U,	// t2ADDri
    16384U,	// t2ADDri12
    16384U,	// t2ADDrr
    491520U,	// t2ADDrs
    8U,	// t2ADR
    16384U,	// t2ANDri
    16384U,	// t2ANDrr
    491520U,	// t2ANDrs
    507904U,	// t2ASRri
    16384U,	// t2ASRrr
    0U,	// t2B
    16U,	// t2BFC
    49176U,	// t2BFI
    16384U,	// t2BICri
    16384U,	// t2BICrr
    491520U,	// t2BICrs
    0U,	// t2BR_JT
    0U,	// t2BXJ
    0U,	// t2Bcc
    544U,	// t2CDP
    544U,	// t2CDP2
    0U,	// t2CLREX
    1024U,	// t2CLZ
    1024U,	// t2CMNri
    1024U,	// t2CMNzrr
    392U,	// t2CMNzrs
    1024U,	// t2CMPri
    1024U,	// t2CMPrr
    392U,	// t2CMPrs
    0U,	// t2CPS1p
    0U,	// t2CPS2p
    1048U,	// t2CPS3p
    1048U,	// t2CRC32B
    1048U,	// t2CRC32CB
    1048U,	// t2CRC32CH
    1048U,	// t2CRC32CW
    1048U,	// t2CRC32H
    1048U,	// t2CRC32W
    0U,	// t2DBG
    0U,	// t2DCPS1
    0U,	// t2DCPS2
    0U,	// t2DCPS3
    0U,	// t2DMB
    0U,	// t2DSB
    16384U,	// t2EORri
    16384U,	// t2EORrr
    491520U,	// t2EORrs
    0U,	// t2HINT
    0U,	// t2HVC
    0U,	// t2ISB
    0U,	// t2IT
    0U,	// t2Int_eh_sjlj_setjmp
    0U,	// t2Int_eh_sjlj_setjmp_nofp
    80U,	// t2LDA
    80U,	// t2LDAB
    80U,	// t2LDAEX
    80U,	// t2LDAEXB
    245760U,	// t2LDAEXD
    80U,	// t2LDAEXH
    80U,	// t2LDAH
    89U,	// t2LDC2L_OFFSET
    65633U,	// t2LDC2L_OPTION
    82017U,	// t2LDC2L_POST
    105U,	// t2LDC2L_PRE
    89U,	// t2LDC2_OFFSET
    65633U,	// t2LDC2_OPTION
    82017U,	// t2LDC2_POST
    105U,	// t2LDC2_PRE
    89U,	// t2LDCL_OFFSET
    65633U,	// t2LDCL_OPTION
    82017U,	// t2LDCL_POST
    105U,	// t2LDCL_PRE
    89U,	// t2LDC_OFFSET
    65633U,	// t2LDC_OPTION
    82017U,	// t2LDC_POST
    105U,	// t2LDC_PRE
    1096U,	// t2LDMDB
    65U,	// t2LDMDB_UPD
    1096U,	// t2LDMIA
    0U,	// t2LDMIA_RET
    65U,	// t2LDMIA_UPD
    400U,	// t2LDRBT
    10848U,	// t2LDRB_POST
    408U,	// t2LDRB_PRE
    128U,	// t2LDRBi12
    400U,	// t2LDRBi8
    416U,	// t2LDRBpci
    1024U,	// t2LDRBpcrel
    424U,	// t2LDRBs
    11665408U,	// t2LDRD_POST
    524288U,	// t2LDRD_PRE
    540672U,	// t2LDRDi8
    432U,	// t2LDREX
    80U,	// t2LDREXB
    245760U,	// t2LDREXD
    80U,	// t2LDREXH
    400U,	// t2LDRHT
    10848U,	// t2LDRH_POST
    408U,	// t2LDRH_PRE
    128U,	// t2LDRHi12
    400U,	// t2LDRHi8
    416U,	// t2LDRHpci
    1024U,	// t2LDRHpcrel
    424U,	// t2LDRHs
    400U,	// t2LDRSBT
    10848U,	// t2LDRSB_POST
    408U,	// t2LDRSB_PRE
    128U,	// t2LDRSBi12
    400U,	// t2LDRSBi8
    416U,	// t2LDRSBpci
    1024U,	// t2LDRSBpcrel
    424U,	// t2LDRSBs
    400U,	// t2LDRSHT
    10848U,	// t2LDRSH_POST
    408U,	// t2LDRSH_PRE
    128U,	// t2LDRSHi12
    400U,	// t2LDRSHi8
    416U,	// t2LDRSHpci
    1024U,	// t2LDRSHpcrel
    424U,	// t2LDRSHs
    400U,	// t2LDRT
    10848U,	// t2LDR_POST
    408U,	// t2LDR_PRE
    128U,	// t2LDRi12
    400U,	// t2LDRi8
    416U,	// t2LDRpci
    0U,	// t2LDRpci_pic
    1024U,	// t2LDRpcrel
    424U,	// t2LDRs
    0U,	// t2LEApcrel
    0U,	// t2LEApcrelJT
    16384U,	// t2LSLri
    16384U,	// t2LSLrr
    507904U,	// t2LSRri
    16384U,	// t2LSRrr
    2311712U,	// t2MCR
    2311712U,	// t2MCR2
    3360288U,	// t2MCRR
    3360288U,	// t2MCRR2
    17842176U,	// t2MLA
    17842176U,	// t2MLS
    0U,	// t2MOVCCasr
    0U,	// t2MOVCCi
    0U,	// t2MOVCCi16
    0U,	// t2MOVCCi32imm
    0U,	// t2MOVCClsl
    0U,	// t2MOVCClsr
    0U,	// t2MOVCCr
    0U,	// t2MOVCCror
    392U,	// t2MOVSsi
    56U,	// t2MOVSsr
    1048U,	// t2MOVTi16
    0U,	// t2MOVTi16_ga_pcrel
    0U,	// t2MOV_ga_pcrel
    1024U,	// t2MOVi
    1024U,	// t2MOVi16
    0U,	// t2MOVi16_ga_pcrel
    0U,	// t2MOVi32imm
    1024U,	// t2MOVr
    392U,	// t2MOVsi
    56U,	// t2MOVsr
    11264U,	// t2MOVsra_flag
    11264U,	// t2MOVsrl_flag
    0U,	// t2MRC
    0U,	// t2MRC2
    3360288U,	// t2MRRC
    3360288U,	// t2MRRC2
    2U,	// t2MRS_AR
    440U,	// t2MRS_M
    176U,	// t2MRSbanked
    2U,	// t2MRSsys_AR
    64U,	// t2MSR_AR
    64U,	// t2MSR_M
    0U,	// t2MSRbanked
    16384U,	// t2MUL
    0U,	// t2MVNCCi
    1024U,	// t2MVNi
    1024U,	// t2MVNr
    392U,	// t2MVNs
    16384U,	// t2ORNri
    16384U,	// t2ORNrr
    491520U,	// t2ORNrs
    16384U,	// t2ORRri
    16384U,	// t2ORRrr
    491520U,	// t2ORRrs
    4210688U,	// t2PKHBT
    5259264U,	// t2PKHTB
    0U,	// t2PLDWi12
    0U,	// t2PLDWi8
    0U,	// t2PLDWs
    0U,	// t2PLDi12
    0U,	// t2PLDi8
    0U,	// t2PLDpci
    0U,	// t2PLDs
    0U,	// t2PLIi12
    0U,	// t2PLIi8
    0U,	// t2PLIpci
    0U,	// t2PLIs
    16384U,	// t2QADD
    16384U,	// t2QADD16
    16384U,	// t2QADD8
    16384U,	// t2QASX
    16384U,	// t2QDADD
    16384U,	// t2QDSUB
    16384U,	// t2QSAX
    16384U,	// t2QSUB
    16384U,	// t2QSUB16
    16384U,	// t2QSUB8
    1024U,	// t2RBIT
    1024U,	// t2REV
    1024U,	// t2REV16
    1024U,	// t2REVSH
    0U,	// t2RFEDB
    4U,	// t2RFEDBW
    0U,	// t2RFEIA
    4U,	// t2RFEIAW
    16384U,	// t2RORri
    16384U,	// t2RORrr
    1024U,	// t2RRX
    0U,	// t2RSBSri
    0U,	// t2RSBSrs
    16384U,	// t2RSBri
    16384U,	// t2RSBrr
    491520U,	// t2RSBrs
    16384U,	// t2SADD16
    16384U,	// t2SADD8
    16384U,	// t2SASX
    16384U,	// t2SBCri
    16384U,	// t2SBCrr
    491520U,	// t2SBCrs
    34619392U,	// t2SBFX
    16384U,	// t2SDIV
    16384U,	// t2SEL
    16384U,	// t2SHADD16
    16384U,	// t2SHADD8
    16384U,	// t2SHASX
    16384U,	// t2SHSAX
    16384U,	// t2SHSUB16
    16384U,	// t2SHSUB8
    0U,	// t2SMC
    17842176U,	// t2SMLABB
    17842176U,	// t2SMLABT
    17842176U,	// t2SMLAD
    17842176U,	// t2SMLADX
    17842176U,	// t2SMLAL
    17842176U,	// t2SMLALBB
    17842176U,	// t2SMLALBT
    17842176U,	// t2SMLALD
    17842176U,	// t2SMLALDX
    17842176U,	// t2SMLALTB
    17842176U,	// t2SMLALTT
    17842176U,	// t2SMLATB
    17842176U,	// t2SMLATT
    17842176U,	// t2SMLAWB
    17842176U,	// t2SMLAWT
    17842176U,	// t2SMLSD
    17842176U,	// t2SMLSDX
    17842176U,	// t2SMLSLD
    185876480U,	// t2SMLSLDX
    17842176U,	// t2SMMLA
    17842176U,	// t2SMMLAR
    17842176U,	// t2SMMLS
    17842176U,	// t2SMMLSR
    16384U,	// t2SMMUL
    16384U,	// t2SMMULR
    16384U,	// t2SMUAD
    16384U,	// t2SMUADX
    16384U,	// t2SMULBB
    16384U,	// t2SMULBT
    17842176U,	// t2SMULL
    16384U,	// t2SMULTB
    16384U,	// t2SMULTT
    16384U,	// t2SMULWB
    16384U,	// t2SMULWT
    16384U,	// t2SMUSD
    16384U,	// t2SMUSDX
    0U,	// t2SRSDB
    0U,	// t2SRSDB_UPD
    0U,	// t2SRSIA
    0U,	// t2SRSIA_UPD
    2232U,	// t2SSAT
    1208U,	// t2SSAT16
    16384U,	// t2SSAX
    16384U,	// t2SSUB16
    16384U,	// t2SSUB8
    89U,	// t2STC2L_OFFSET
    65633U,	// t2STC2L_OPTION
    82017U,	// t2STC2L_POST
    105U,	// t2STC2L_PRE
    89U,	// t2STC2_OFFSET
    65633U,	// t2STC2_OPTION
    82017U,	// t2STC2_POST
    105U,	// t2STC2_PRE
    89U,	// t2STCL_OFFSET
    65633U,	// t2STCL_OPTION
    82017U,	// t2STCL_POST
    105U,	// t2STCL_PRE
    89U,	// t2STC_OFFSET
    65633U,	// t2STC_OPTION
    82017U,	// t2STC_POST
    105U,	// t2STC_PRE
    80U,	// t2STL
    80U,	// t2STLB
    245760U,	// t2STLEX
    245760U,	// t2STLEXB
    202391552U,	// t2STLEXD
    245760U,	// t2STLEXH
    80U,	// t2STLH
    1096U,	// t2STMDB
    65U,	// t2STMDB_UPD
    1096U,	// t2STMIA
    65U,	// t2STMIA_UPD
    400U,	// t2STRBT
    10848U,	// t2STRB_POST
    408U,	// t2STRB_PRE
    0U,	// t2STRB_preidx
    128U,	// t2STRBi12
    400U,	// t2STRBi8
    424U,	// t2STRBs
    11665432U,	// t2STRD_POST
    524312U,	// t2STRD_PRE
    540672U,	// t2STRDi8
    557056U,	// t2STREX
    245760U,	// t2STREXB
    202391552U,	// t2STREXD
    245760U,	// t2STREXH
    400U,	// t2STRHT
    10848U,	// t2STRH_POST
    408U,	// t2STRH_PRE
    0U,	// t2STRH_preidx
    128U,	// t2STRHi12
    400U,	// t2STRHi8
    424U,	// t2STRHs
    400U,	// t2STRT
    10848U,	// t2STR_POST
    408U,	// t2STR_PRE
    0U,	// t2STR_preidx
    128U,	// t2STRi12
    400U,	// t2STRi8
    424U,	// t2STRs
    0U,	// t2SUBS_PC_LR
    0U,	// t2SUBSri
    0U,	// t2SUBSrr
    0U,	// t2SUBSrs
    16384U,	// t2SUBri
    16384U,	// t2SUBri12
    16384U,	// t2SUBrr
    491520U,	// t2SUBrs
    6307840U,	// t2SXTAB
    6307840U,	// t2SXTAB16
    6307840U,	// t2SXTAH
    2560U,	// t2SXTB
    2560U,	// t2SXTB16
    2560U,	// t2SXTH
    0U,	// t2TBB
    0U,	// t2TBB_JT
    0U,	// t2TBH
    0U,	// t2TBH_JT
    1024U,	// t2TEQri
    1024U,	// t2TEQrr
    392U,	// t2TEQrs
    1024U,	// t2TSTri
    1024U,	// t2TSTrr
    392U,	// t2TSTrs
    16384U,	// t2UADD16
    16384U,	// t2UADD8
    16384U,	// t2UASX
    34619392U,	// t2UBFX
    0U,	// t2UDF
    16384U,	// t2UDIV
    16384U,	// t2UHADD16
    16384U,	// t2UHADD8
    16384U,	// t2UHASX
    16384U,	// t2UHSAX
    16384U,	// t2UHSUB16
    16384U,	// t2UHSUB8
    17842176U,	// t2UMAAL
    17842176U,	// t2UMLAL
    17842176U,	// t2UMULL
    16384U,	// t2UQADD16
    16384U,	// t2UQADD8
    16384U,	// t2UQASX
    16384U,	// t2UQSAX
    16384U,	// t2UQSUB16
    16384U,	// t2UQSUB8
    16384U,	// t2USAD8
    17842176U,	// t2USADA8
    7356416U,	// t2USAT
    16384U,	// t2USAT16
    16384U,	// t2USAX
    16384U,	// t2USUB16
    16384U,	// t2USUB8
    6307840U,	// t2UXTAB
    6307840U,	// t2UXTAB16
    6307840U,	// t2UXTAH
    2560U,	// t2UXTB
    2560U,	// t2UXTB16
    2560U,	// t2UXTH
    0U,	// tADC
    0U,	// tADDframe
    1048U,	// tADDhirr
    1192U,	// tADDi3
    0U,	// tADDi8
    16384U,	// tADDrSP
    573440U,	// tADDrSPi
    1192U,	// tADDrr
    448U,	// tADDspi
    1048U,	// tADDspr
    0U,	// tADJCALLSTACKDOWN
    0U,	// tADJCALLSTACKUP
    456U,	// tADR
    0U,	// tAND
    464U,	// tASRri
    0U,	// tASRrr
    0U,	// tB
    0U,	// tBIC
    0U,	// tBKPT
    0U,	// tBL
    0U,	// tBLXi
    0U,	// tBLXr
    0U,	// tBRIND
    0U,	// tBR_JTr
    0U,	// tBX
    0U,	// tBX_CALL
    0U,	// tBX_RET
    0U,	// tBX_RET_vararg
    0U,	// tBcc
    0U,	// tBfar
    0U,	// tCBNZ
    0U,	// tCBZ
    1024U,	// tCMNz
    1024U,	// tCMPhir
    1024U,	// tCMPi8
    1024U,	// tCMPr
    0U,	// tCPS
    0U,	// tEOR
    0U,	// tHINT
    0U,	// tHLT
    0U,	// tInt_eh_sjlj_longjmp
    0U,	// tInt_eh_sjlj_setjmp
    1096U,	// tLDMIA
    0U,	// tLDMIA_UPD
    472U,	// tLDRBi
    480U,	// tLDRBr
    488U,	// tLDRHi
    480U,	// tLDRHr
    0U,	// tLDRLIT_ga_abs
    0U,	// tLDRLIT_ga_pcrel
    480U,	// tLDRSB
    480U,	// tLDRSH
    496U,	// tLDRi
    416U,	// tLDRpci
    0U,	// tLDRpci_pic
    480U,	// tLDRr
    504U,	// tLDRspi
    0U,	// tLEApcrel
    0U,	// tLEApcrelJT
    1192U,	// tLSLri
    0U,	// tLSLrr
    464U,	// tLSRri
    0U,	// tLSRrr
    0U,	// tMOVCCr_pseudo
    0U,	// tMOVSr
    0U,	// tMOVi8
    1024U,	// tMOVr
    1192U,	// tMUL
    0U,	// tMVN
    0U,	// tORR
    0U,	// tPICADD
    0U,	// tPOP
    0U,	// tPOP_RET
    0U,	// tPUSH
    1024U,	// tREV
    1024U,	// tREV16
    1024U,	// tREVSH
    0U,	// tROR
    0U,	// tRSB
    0U,	// tSBC
    0U,	// tSETEND
    65U,	// tSTMIA_UPD
    472U,	// tSTRBi
    480U,	// tSTRBr
    488U,	// tSTRHi
    480U,	// tSTRHr
    496U,	// tSTRi
    480U,	// tSTRr
    504U,	// tSTRspi
    1192U,	// tSUBi3
    0U,	// tSUBi8
    1192U,	// tSUBrr
    448U,	// tSUBspi
    0U,	// tSVC
    1024U,	// tSXTB
    1024U,	// tSXTH
    0U,	// tTAILJMPd
    0U,	// tTAILJMPdND
    0U,	// tTAILJMPr
    0U,	// tTPsoft
    0U,	// tTRAP
    1024U,	// tTST
    0U,	// tUDF
    1024U,	// tUXTB
    1024U,	// tUXTH
    0U
  };

#ifndef CAPSTONE_DIET
  static const char AsmStrs[] = {
  /* 0 */ 's', 'h', 'a', '1', 's', 'u', '0', '.', '3', '2', 9, 0,
  /* 12 */ 's', 'h', 'a', '2', '5', '6', 's', 'u', '0', '.', '3', '2', 9, 0,
  /* 26 */ 's', 'h', 'a', '1', 's', 'u', '1', '.', '3', '2', 9, 0,
  /* 38 */ 's', 'h', 'a', '2', '5', '6', 's', 'u', '1', '.', '3', '2', 9, 0,
  /* 52 */ 's', 'h', 'a', '2', '5', '6', 'h', '2', '.', '3', '2', 9, 0,
  /* 65 */ 's', 'h', 'a', '1', 'c', '.', '3', '2', 9, 0,
  /* 75 */ 's', 'h', 'a', '1', 'h', '.', '3', '2', 9, 0,
  /* 85 */ 's', 'h', 'a', '2', '5', '6', 'h', '.', '3', '2', 9, 0,
  /* 97 */ 's', 'h', 'a', '1', 'm', '.', '3', '2', 9, 0,
  /* 107 */ 's', 'h', 'a', '1', 'p', '.', '3', '2', 9, 0,
  /* 117 */ 'v', 'c', 'v', 't', 'a', '.', 's', '3', '2', '.', 'f', '3', '2', 9, 0,
  /* 132 */ 'v', 'c', 'v', 't', 'm', '.', 's', '3', '2', '.', 'f', '3', '2', 9, 0,
  /* 147 */ 'v', 'c', 'v', 't', 'n', '.', 's', '3', '2', '.', 'f', '3', '2', 9, 0,
  /* 162 */ 'v', 'c', 'v', 't', 'p', '.', 's', '3', '2', '.', 'f', '3', '2', 9, 0,
  /* 177 */ 'v', 'c', 'v', 't', 'a', '.', 'u', '3', '2', '.', 'f', '3', '2', 9, 0,
  /* 192 */ 'v', 'c', 'v', 't', 'm', '.', 'u', '3', '2', '.', 'f', '3', '2', 9, 0,
  /* 207 */ 'v', 'c', 'v', 't', 'n', '.', 'u', '3', '2', '.', 'f', '3', '2', 9, 0,
  /* 222 */ 'v', 'c', 'v', 't', 'p', '.', 'u', '3', '2', '.', 'f', '3', '2', 9, 0,
  /* 237 */ 'v', 'r', 'i', 'n', 't', 'a', '.', 'f', '3', '2', 9, 0,
  /* 249 */ 'v', 's', 'e', 'l', 'g', 'e', '.', 'f', '3', '2', 9, 0,
  /* 261 */ 'v', 'm', 'i', 'n', 'n', 'm', '.', 'f', '3', '2', 9, 0,
  /* 273 */ 'v', 'm', 'a', 'x', 'n', 'm', '.', 'f', '3', '2', 9, 0,
  /* 285 */ 'v', 'r', 'i', 'n', 't', 'm', '.', 'f', '3', '2', 9, 0,
  /* 297 */ 'v', 'r', 'i', 'n', 't', 'n', '.', 'f', '3', '2', 9, 0,
  /* 309 */ 'v', 'r', 'i', 'n', 't', 'p', '.', 'f', '3', '2', 9, 0,
  /* 321 */ 'v', 's', 'e', 'l', 'e', 'q', '.', 'f', '3', '2', 9, 0,
  /* 333 */ 'v', 's', 'e', 'l', 'v', 's', '.', 'f', '3', '2', 9, 0,
  /* 345 */ 'v', 's', 'e', 'l', 'g', 't', '.', 'f', '3', '2', 9, 0,
  /* 357 */ 'v', 'r', 'i', 'n', 't', 'x', '.', 'f', '3', '2', 9, 0,
  /* 369 */ 'v', 'r', 'i', 'n', 't', 'z', '.', 'f', '3', '2', 9, 0,
  /* 381 */ 'l', 'd', 'c', '2', 9, 0,
  /* 387 */ 'm', 'r', 'c', '2', 9, 0,
  /* 393 */ 'm', 'r', 'r', 'c', '2', 9, 0,
  /* 400 */ 's', 't', 'c', '2', 9, 0,
  /* 406 */ 'c', 'd', 'p', '2', 9, 0,
  /* 412 */ 'm', 'c', 'r', '2', 9, 0,
  /* 418 */ 'm', 'c', 'r', 'r', '2', 9, 0,
  /* 425 */ 'v', 'c', 'v', 't', 'a', '.', 's', '3', '2', '.', 'f', '6', '4', 9, 0,
  /* 440 */ 'v', 'c', 'v', 't', 'm', '.', 's', '3', '2', '.', 'f', '6', '4', 9, 0,
  /* 455 */ 'v', 'c', 'v', 't', 'n', '.', 's', '3', '2', '.', 'f', '6', '4', 9, 0,
  /* 470 */ 'v', 'c', 'v', 't', 'p', '.', 's', '3', '2', '.', 'f', '6', '4', 9, 0,
  /* 485 */ 'v', 'c', 'v', 't', 'a', '.', 'u', '3', '2', '.', 'f', '6', '4', 9, 0,
  /* 500 */ 'v', 'c', 'v', 't', 'm', '.', 'u', '3', '2', '.', 'f', '6', '4', 9, 0,
  /* 515 */ 'v', 'c', 'v', 't', 'n', '.', 'u', '3', '2', '.', 'f', '6', '4', 9, 0,
  /* 530 */ 'v', 'c', 'v', 't', 'p', '.', 'u', '3', '2', '.', 'f', '6', '4', 9, 0,
  /* 545 */ 'v', 'r', 'i', 'n', 't', 'a', '.', 'f', '6', '4', 9, 0,
  /* 557 */ 'v', 's', 'e', 'l', 'g', 'e', '.', 'f', '6', '4', 9, 0,
  /* 569 */ 'v', 'm', 'i', 'n', 'n', 'm', '.', 'f', '6', '4', 9, 0,
  /* 581 */ 'v', 'm', 'a', 'x', 'n', 'm', '.', 'f', '6', '4', 9, 0,
  /* 593 */ 'v', 'r', 'i', 'n', 't', 'm', '.', 'f', '6', '4', 9, 0,
  /* 605 */ 'v', 'r', 'i', 'n', 't', 'n', '.', 'f', '6', '4', 9, 0,
  /* 617 */ 'v', 'r', 'i', 'n', 't', 'p', '.', 'f', '6', '4', 9, 0,
  /* 629 */ 'v', 's', 'e', 'l', 'e', 'q', '.', 'f', '6', '4', 9, 0,
  /* 641 */ 'v', 's', 'e', 'l', 'v', 's', '.', 'f', '6', '4', 9, 0,
  /* 653 */ 'v', 's', 'e', 'l', 'g', 't', '.', 'f', '6', '4', 9, 0,
  /* 665 */ 'v', 'm', 'u', 'l', 'l', '.', 'p', '6', '4', 9, 0,
  /* 676 */ 'a', 'e', 's', 'i', 'm', 'c', '.', '8', 9, 0,
  /* 686 */ 'a', 'e', 's', 'm', 'c', '.', '8', 9, 0,
  /* 695 */ 'a', 'e', 's', 'd', '.', '8', 9, 0,
  /* 703 */ 'a', 'e', 's', 'e', '.', '8', 9, 0,
  /* 711 */ 'r', 'f', 'e', 'd', 'a', 9, 0,
  /* 718 */ 'r', 'f', 'e', 'i', 'a', 9, 0,
  /* 725 */ 'c', 'r', 'c', '3', '2', 'b', 9, 0,
  /* 733 */ 'c', 'r', 'c', '3', '2', 'c', 'b', 9, 0,
  /* 742 */ 'r', 'f', 'e', 'd', 'b', 9, 0,
  /* 749 */ 'r', 'f', 'e', 'i', 'b', 9, 0,
  /* 756 */ 'd', 'm', 'b', 9, 0,
  /* 761 */ 'd', 's', 'b', 9, 0,
  /* 766 */ 'i', 's', 'b', 9, 0,
  /* 771 */ 'h', 'v', 'c', 9, 0,
  /* 776 */ 'p', 'l', 'd', 9, 0,
  /* 781 */ 's', 'e', 't', 'e', 'n', 'd', 9, 0,
  /* 789 */ 'u', 'd', 'f', 9, 0,
  /* 794 */ 'c', 'r', 'c', '3', '2', 'h', 9, 0,
  /* 802 */ 'c', 'r', 'c', '3', '2', 'c', 'h', 9, 0,
  /* 811 */ 'p', 'l', 'i', 9, 0,
  /* 816 */ 'l', 'd', 'c', '2', 'l', 9, 0,
  /* 823 */ 's', 't', 'c', '2', 'l', 9, 0,
  /* 830 */ 'b', 'l', 9, 0,
  /* 834 */ 'c', 'p', 's', 9, 0,
  /* 839 */ 'm', 'o', 'v', 's', 9, 0,
  /* 845 */ 'h', 'l', 't', 9, 0,
  /* 850 */ 'b', 'k', 'p', 't', 9, 0,
  /* 856 */ 'h', 'v', 'c', '.', 'w', 9, 0,
  /* 863 */ 'u', 'd', 'f', '.', 'w', 9, 0,
  /* 870 */ 'c', 'r', 'c', '3', '2', 'w', 9, 0,
  /* 878 */ 'c', 'r', 'c', '3', '2', 'c', 'w', 9, 0,
  /* 887 */ 'p', 'l', 'd', 'w', 9, 0,
  /* 893 */ 'b', 'x', 9, 0,
  /* 897 */ 'b', 'l', 'x', 9, 0,
  /* 902 */ 'c', 'b', 'z', 9, 0,
  /* 907 */ 'c', 'b', 'n', 'z', 9, 0,
  /* 913 */ 's', 'r', 's', 'd', 'a', 9, 's', 'p', '!', ',', 32, 0,
  /* 925 */ 's', 'r', 's', 'i', 'a', 9, 's', 'p', '!', ',', 32, 0,
  /* 937 */ 's', 'r', 's', 'd', 'b', 9, 's', 'p', '!', ',', 32, 0,
  /* 949 */ 's', 'r', 's', 'i', 'b', 9, 's', 'p', '!', ',', 32, 0,
  /* 961 */ 's', 'r', 's', 'd', 'a', 9, 's', 'p', ',', 32, 0,
  /* 972 */ 's', 'r', 's', 'i', 'a', 9, 's', 'p', ',', 32, 0,
  /* 983 */ 's', 'r', 's', 'd', 'b', 9, 's', 'p', ',', 32, 0,
  /* 994 */ 's', 'r', 's', 'i', 'b', 9, 's', 'p', ',', 32, 0,
  /* 1005 */ 'v', 'l', 'd', '1', 0,
  /* 1010 */ 'd', 'c', 'p', 's', '1', 0,
  /* 1016 */ 'v', 's', 't', '1', 0,
  /* 1021 */ 'v', 'r', 'e', 'v', '3', '2', 0,
  /* 1028 */ 'l', 'd', 'c', '2', 0,
  /* 1033 */ 'm', 'r', 'c', '2', 0,
  /* 1038 */ 'm', 'r', 'r', 'c', '2', 0,
  /* 1044 */ 's', 't', 'c', '2', 0,
  /* 1049 */ 'v', 'l', 'd', '2', 0,
  /* 1054 */ 'c', 'd', 'p', '2', 0,
  /* 1059 */ 'm', 'c', 'r', '2', 0,
  /* 1064 */ 'm', 'c', 'r', 'r', '2', 0,
  /* 1070 */ 'd', 'c', 'p', 's', '2', 0,
  /* 1076 */ 'v', 's', 't', '2', 0,
  /* 1081 */ 'v', 'l', 'd', '3', 0,
  /* 1086 */ 'd', 'c', 'p', 's', '3', 0,
  /* 1092 */ 'v', 's', 't', '3', 0,
  /* 1097 */ 'v', 'r', 'e', 'v', '6', '4', 0,
  /* 1104 */ 'v', 'l', 'd', '4', 0,
  /* 1109 */ 'v', 's', 't', '4', 0,
  /* 1114 */ 's', 'x', 't', 'a', 'b', '1', '6', 0,
  /* 1122 */ 'u', 'x', 't', 'a', 'b', '1', '6', 0,
  /* 1130 */ 's', 'x', 't', 'b', '1', '6', 0,
  /* 1137 */ 'u', 'x', 't', 'b', '1', '6', 0,
  /* 1144 */ 's', 'h', 's', 'u', 'b', '1', '6', 0,
  /* 1152 */ 'u', 'h', 's', 'u', 'b', '1', '6', 0,
  /* 1160 */ 'u', 'q', 's', 'u', 'b', '1', '6', 0,
  /* 1168 */ 's', 's', 'u', 'b', '1', '6', 0,
  /* 1175 */ 'u', 's', 'u', 'b', '1', '6', 0,
  /* 1182 */ 's', 'h', 'a', 'd', 'd', '1', '6', 0,
  /* 1190 */ 'u', 'h', 'a', 'd', 'd', '1', '6', 0,
  /* 1198 */ 'u', 'q', 'a', 'd', 'd', '1', '6', 0,
  /* 1206 */ 's', 'a', 'd', 'd', '1', '6', 0,
  /* 1213 */ 'u', 'a', 'd', 'd', '1', '6', 0,
  /* 1220 */ 's', 's', 'a', 't', '1', '6', 0,
  /* 1227 */ 'u', 's', 'a', 't', '1', '6', 0,
  /* 1234 */ 'v', 'r', 'e', 'v', '1', '6', 0,
  /* 1241 */ 'u', 's', 'a', 'd', 'a', '8', 0,
  /* 1248 */ 's', 'h', 's', 'u', 'b', '8', 0,
  /* 1255 */ 'u', 'h', 's', 'u', 'b', '8', 0,
  /* 1262 */ 'u', 'q', 's', 'u', 'b', '8', 0,
  /* 1269 */ 's', 's', 'u', 'b', '8', 0,
  /* 1275 */ 'u', 's', 'u', 'b', '8', 0,
  /* 1281 */ 'u', 's', 'a', 'd', '8', 0,
  /* 1287 */ 's', 'h', 'a', 'd', 'd', '8', 0,
  /* 1294 */ 'u', 'h', 'a', 'd', 'd', '8', 0,
  /* 1301 */ 'u', 'q', 'a', 'd', 'd', '8', 0,
  /* 1308 */ 's', 'a', 'd', 'd', '8', 0,
  /* 1314 */ 'u', 'a', 'd', 'd', '8', 0,
  /* 1320 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'E', 'N', 'D', 0,
  /* 1333 */ 'B', 'U', 'N', 'D', 'L', 'E', 0,
  /* 1340 */ 'D', 'B', 'G', '_', 'V', 'A', 'L', 'U', 'E', 0,
  /* 1350 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'S', 'T', 'A', 'R', 'T', 0,
  /* 1365 */ 'v', 'a', 'b', 'a', 0,
  /* 1370 */ 'l', 'd', 'a', 0,
  /* 1374 */ 'l', 'd', 'm', 'd', 'a', 0,
  /* 1380 */ 's', 't', 'm', 'd', 'a', 0,
  /* 1386 */ 'r', 'f', 'e', 'i', 'a', 0,
  /* 1392 */ 'v', 'l', 'd', 'm', 'i', 'a', 0,
  /* 1399 */ 'v', 's', 't', 'm', 'i', 'a', 0,
  /* 1406 */ 's', 'r', 's', 'i', 'a', 0,
  /* 1412 */ 's', 'm', 'm', 'l', 'a', 0,
  /* 1418 */ 'v', 'n', 'm', 'l', 'a', 0,
  /* 1424 */ 'v', 'm', 'l', 'a', 0,
  /* 1429 */ 'v', 'f', 'm', 'a', 0,
  /* 1434 */ 'v', 'f', 'n', 'm', 'a', 0,
  /* 1440 */ 'v', 'r', 's', 'r', 'a', 0,
  /* 1446 */ 'v', 's', 'r', 'a', 0,
  /* 1451 */ 'l', 'd', 'a', 'b', 0,
  /* 1456 */ 's', 'x', 't', 'a', 'b', 0,
  /* 1462 */ 'u', 'x', 't', 'a', 'b', 0,
  /* 1468 */ 's', 'm', 'l', 'a', 'b', 'b', 0,
  /* 1475 */ 's', 'm', 'l', 'a', 'l', 'b', 'b', 0,
  /* 1483 */ 's', 'm', 'u', 'l', 'b', 'b', 0,
  /* 1490 */ 't', 'b', 'b', 0,
  /* 1494 */ 'r', 'f', 'e', 'd', 'b', 0,
  /* 1500 */ 'v', 'l', 'd', 'm', 'd', 'b', 0,
  /* 1507 */ 'v', 's', 't', 'm', 'd', 'b', 0,
  /* 1514 */ 's', 'r', 's', 'd', 'b', 0,
  /* 1520 */ 'l', 'd', 'm', 'i', 'b', 0,
  /* 1526 */ 's', 't', 'm', 'i', 'b', 0,
  /* 1532 */ 's', 't', 'l', 'b', 0,
  /* 1537 */ 'd', 'm', 'b', 0,
  /* 1541 */ 's', 'w', 'p', 'b', 0,
  /* 1546 */ 'l', 'd', 'r', 'b', 0,
  /* 1551 */ 's', 't', 'r', 'b', 0,
  /* 1556 */ 'd', 's', 'b', 0,
  /* 1560 */ 'i', 's', 'b', 0,
  /* 1564 */ 'l', 'd', 'r', 's', 'b', 0,
  /* 1570 */ 's', 'm', 'l', 'a', 't', 'b', 0,
  /* 1577 */ 'p', 'k', 'h', 't', 'b', 0,
  /* 1583 */ 's', 'm', 'l', 'a', 'l', 't', 'b', 0,
  /* 1591 */ 's', 'm', 'u', 'l', 't', 'b', 0,
  /* 1598 */ 'v', 'c', 'v', 't', 'b', 0,
  /* 1604 */ 's', 'x', 't', 'b', 0,
  /* 1609 */ 'u', 'x', 't', 'b', 0,
  /* 1614 */ 'q', 'd', 's', 'u', 'b', 0,
  /* 1620 */ 'v', 'h', 's', 'u', 'b', 0,
  /* 1626 */ 'v', 'q', 's', 'u', 'b', 0,
  /* 1632 */ 'v', 's', 'u', 'b', 0,
  /* 1637 */ 's', 'm', 'l', 'a', 'w', 'b', 0,
  /* 1644 */ 's', 'm', 'u', 'l', 'w', 'b', 0,
  /* 1651 */ 'l', 'd', 'a', 'e', 'x', 'b', 0,
  /* 1658 */ 's', 't', 'l', 'e', 'x', 'b', 0,
  /* 1665 */ 'l', 'd', 'r', 'e', 'x', 'b', 0,
  /* 1672 */ 's', 't', 'r', 'e', 'x', 'b', 0,
  /* 1679 */ 's', 'b', 'c', 0,
  /* 1683 */ 'a', 'd', 'c', 0,
  /* 1687 */ 'l', 'd', 'c', 0,
  /* 1691 */ 'b', 'f', 'c', 0,
  /* 1695 */ 'v', 'b', 'i', 'c', 0,
  /* 1700 */ 's', 'm', 'c', 0,
  /* 1704 */ 'm', 'r', 'c', 0,
  /* 1708 */ 'm', 'r', 'r', 'c', 0,
  /* 1713 */ 'r', 's', 'c', 0,
  /* 1717 */ 's', 't', 'c', 0,
  /* 1721 */ 's', 'v', 'c', 0,
  /* 1725 */ 's', 'm', 'l', 'a', 'd', 0,
  /* 1731 */ 's', 'm', 'u', 'a', 'd', 0,
  /* 1737 */ 'v', 'a', 'b', 'd', 0,
  /* 1742 */ 'q', 'd', 'a', 'd', 'd', 0,
  /* 1748 */ 'v', 'r', 'h', 'a', 'd', 'd', 0,
  /* 1755 */ 'v', 'h', 'a', 'd', 'd', 0,
  /* 1761 */ 'v', 'p', 'a', 'd', 'd', 0,
  /* 1767 */ 'v', 'q', 'a', 'd', 'd', 0,
  /* 1773 */ 'v', 'a', 'd', 'd', 0,
  /* 1778 */ 's', 'm', 'l', 'a', 'l', 'd', 0,
  /* 1785 */ 'p', 'l', 'd', 0,
  /* 1789 */ 's', 'm', 'l', 's', 'l', 'd', 0,
  /* 1796 */ 'v', 'a', 'n', 'd', 0,
  /* 1801 */ 'l', 'd', 'r', 'd', 0,
  /* 1806 */ 's', 't', 'r', 'd', 0,
  /* 1811 */ 's', 'm', 'l', 's', 'd', 0,
  /* 1817 */ 's', 'm', 'u', 's', 'd', 0,
  /* 1823 */ 'l', 'd', 'a', 'e', 'x', 'd', 0,
  /* 1830 */ 's', 't', 'l', 'e', 'x', 'd', 0,
  /* 1837 */ 'l', 'd', 'r', 'e', 'x', 'd', 0,
  /* 1844 */ 's', 't', 'r', 'e', 'x', 'd', 0,
  /* 1851 */ 'v', 'a', 'c', 'g', 'e', 0,
  /* 1857 */ 'v', 'c', 'g', 'e', 0,
  /* 1862 */ 'v', 'c', 'l', 'e', 0,
  /* 1867 */ 'v', 'r', 'e', 'c', 'p', 'e', 0,
  /* 1874 */ 'v', 'c', 'm', 'p', 'e', 0,
  /* 1880 */ 'v', 'r', 's', 'q', 'r', 't', 'e', 0,
  /* 1888 */ 'v', 'b', 'i', 'f', 0,
  /* 1893 */ 'd', 'b', 'g', 0,
  /* 1897 */ 'v', 'q', 'n', 'e', 'g', 0,
  /* 1903 */ 'v', 'n', 'e', 'g', 0,
  /* 1908 */ 'l', 'd', 'a', 'h', 0,
  /* 1913 */ 's', 'x', 't', 'a', 'h', 0,
  /* 1919 */ 'u', 'x', 't', 'a', 'h', 0,
  /* 1925 */ 't', 'b', 'h', 0,
  /* 1929 */ 's', 't', 'l', 'h', 0,
  /* 1934 */ 'v', 'q', 'd', 'm', 'u', 'l', 'h', 0,
  /* 1942 */ 'v', 'q', 'r', 'd', 'm', 'u', 'l', 'h', 0,
  /* 1951 */ 'l', 'd', 'r', 'h', 0,
  /* 1956 */ 's', 't', 'r', 'h', 0,
  /* 1961 */ 'l', 'd', 'r', 's', 'h', 0,
  /* 1967 */ 'p', 'u', 's', 'h', 0,
  /* 1972 */ 'r', 'e', 'v', 's', 'h', 0,
  /* 1978 */ 's', 'x', 't', 'h', 0,
  /* 1983 */ 'u', 'x', 't', 'h', 0,
  /* 1988 */ 'l', 'd', 'a', 'e', 'x', 'h', 0,
  /* 1995 */ 's', 't', 'l', 'e', 'x', 'h', 0,
  /* 2002 */ 'l', 'd', 'r', 'e', 'x', 'h', 0,
  /* 2009 */ 's', 't', 'r', 'e', 'x', 'h', 0,
  /* 2016 */ 'b', 'f', 'i', 0,
  /* 2020 */ 'p', 'l', 'i', 0,
  /* 2024 */ 'v', 's', 'l', 'i', 0,
  /* 2029 */ 'v', 's', 'r', 'i', 0,
  /* 2034 */ 'b', 'x', 'j', 0,
  /* 2038 */ 'l', 'd', 'c', '2', 'l', 0,
  /* 2044 */ 's', 't', 'c', '2', 'l', 0,
  /* 2050 */ 'u', 'm', 'a', 'a', 'l', 0,
  /* 2056 */ 'v', 'a', 'b', 'a', 'l', 0,
  /* 2062 */ 'v', 'p', 'a', 'd', 'a', 'l', 0,
  /* 2069 */ 'v', 'q', 'd', 'm', 'l', 'a', 'l', 0,
  /* 2077 */ 's', 'm', 'l', 'a', 'l', 0,
  /* 2083 */ 'u', 'm', 'l', 'a', 'l', 0,
  /* 2089 */ 'v', 'm', 'l', 'a', 'l', 0,
  /* 2095 */ 'v', 't', 'b', 'l', 0,
  /* 2100 */ 'v', 's', 'u', 'b', 'l', 0,
  /* 2106 */ 'l', 'd', 'c', 'l', 0,
  /* 2111 */ 's', 't', 'c', 'l', 0,
  /* 2116 */ 'v', 'a', 'b', 'd', 'l', 0,
  /* 2122 */ 'v', 'p', 'a', 'd', 'd', 'l', 0,
  /* 2129 */ 'v', 'a', 'd', 'd', 'l', 0,
  /* 2135 */ 's', 'e', 'l', 0,
  /* 2139 */ 'v', 'q', 's', 'h', 'l', 0,
  /* 2145 */ 'v', 'q', 'r', 's', 'h', 'l', 0,
  /* 2152 */ 'v', 'r', 's', 'h', 'l', 0,
  /* 2158 */ 'v', 's', 'h', 'l', 0,
  /* 2163 */ 'v', 's', 'h', 'l', 'l', 0,
  /* 2169 */ 'v', 'q', 'd', 'm', 'u', 'l', 'l', 0,
  /* 2177 */ 's', 'm', 'u', 'l', 'l', 0,
  /* 2183 */ 'u', 'm', 'u', 'l', 'l', 0,
  /* 2189 */ 'v', 'm', 'u', 'l', 'l', 0,
  /* 2195 */ 'v', 'b', 's', 'l', 0,
  /* 2200 */ 'v', 'q', 'd', 'm', 'l', 's', 'l', 0,
  /* 2208 */ 'v', 'm', 'l', 's', 'l', 0,
  /* 2214 */ 's', 't', 'l', 0,
  /* 2218 */ 's', 'm', 'm', 'u', 'l', 0,
  /* 2224 */ 'v', 'n', 'm', 'u', 'l', 0,
  /* 2230 */ 'v', 'm', 'u', 'l', 0,
  /* 2235 */ 'v', 'm', 'o', 'v', 'l', 0,
  /* 2241 */ 'l', 'd', 'm', 0,
  /* 2245 */ 's', 't', 'm', 0,
  /* 2249 */ 'v', 'r', 's', 'u', 'b', 'h', 'n', 0,
  /* 2257 */ 'v', 's', 'u', 'b', 'h', 'n', 0,
  /* 2264 */ 'v', 'r', 'a', 'd', 'd', 'h', 'n', 0,
  /* 2272 */ 'v', 'a', 'd', 'd', 'h', 'n', 0,
  /* 2279 */ 'v', 'p', 'm', 'i', 'n', 0,
  /* 2285 */ 'v', 'm', 'i', 'n', 0,
  /* 2290 */ 'c', 'm', 'n', 0,
  /* 2294 */ 'v', 'q', 's', 'h', 'r', 'n', 0,
  /* 2301 */ 'v', 'q', 'r', 's', 'h', 'r', 'n', 0,
  /* 2309 */ 'v', 'r', 's', 'h', 'r', 'n', 0,
  /* 2316 */ 'v', 's', 'h', 'r', 'n', 0,
  /* 2322 */ 'v', 'o', 'r', 'n', 0,
  /* 2327 */ 'v', 't', 'r', 'n', 0,
  /* 2332 */ 'v', 'q', 's', 'h', 'r', 'u', 'n', 0,
  /* 2340 */ 'v', 'q', 'r', 's', 'h', 'r', 'u', 'n', 0,
  /* 2349 */ 'v', 'q', 'm', 'o', 'v', 'u', 'n', 0,
  /* 2357 */ 'v', 'm', 'v', 'n', 0,
  /* 2362 */ 'v', 'q', 'm', 'o', 'v', 'n', 0,
  /* 2369 */ 'v', 'm', 'o', 'v', 'n', 0,
  /* 2375 */ 't', 'r', 'a', 'p', 0,
  /* 2380 */ 'c', 'd', 'p', 0,
  /* 2384 */ 'v', 'z', 'i', 'p', 0,
  /* 2389 */ 'v', 'c', 'm', 'p', 0,
  /* 2394 */ 'p', 'o', 'p', 0,
  /* 2398 */ 'v', 'd', 'u', 'p', 0,
  /* 2403 */ 'v', 's', 'w', 'p', 0,
  /* 2408 */ 'v', 'u', 'z', 'p', 0,
  /* 2413 */ 'v', 'c', 'e', 'q', 0,
  /* 2418 */ 't', 'e', 'q', 0,
  /* 2422 */ 's', 'm', 'm', 'l', 'a', 'r', 0,
  /* 2429 */ 'm', 'c', 'r', 0,
  /* 2433 */ 'a', 'd', 'r', 0,
  /* 2437 */ 'v', 'l', 'd', 'r', 0,
  /* 2442 */ 'v', 'r', 's', 'h', 'r', 0,
  /* 2448 */ 'v', 's', 'h', 'r', 0,
  /* 2453 */ 's', 'm', 'm', 'u', 'l', 'r', 0,
  /* 2460 */ 'v', 'e', 'o', 'r', 0,
  /* 2465 */ 'r', 'o', 'r', 0,
  /* 2469 */ 'm', 'c', 'r', 'r', 0,
  /* 2474 */ 'v', 'o', 'r', 'r', 0,
  /* 2479 */ 'a', 's', 'r', 0,
  /* 2483 */ 's', 'm', 'm', 'l', 's', 'r', 0,
  /* 2490 */ 'v', 'm', 's', 'r', 0,
  /* 2495 */ 'v', 'r', 'i', 'n', 't', 'r', 0,
  /* 2502 */ 'v', 's', 't', 'r', 0,
  /* 2507 */ 'v', 'c', 'v', 't', 'r', 0,
  /* 2513 */ 'v', 'q', 'a', 'b', 's', 0,
  /* 2519 */ 'v', 'a', 'b', 's', 0,
  /* 2524 */ 's', 'u', 'b', 's', 0,
  /* 2529 */ 'v', 'c', 'l', 's', 0,
  /* 2534 */ 's', 'm', 'm', 'l', 's', 0,
  /* 2540 */ 'v', 'n', 'm', 'l', 's', 0,
  /* 2546 */ 'v', 'm', 'l', 's', 0,
  /* 2551 */ 'v', 'f', 'm', 's', 0,
  /* 2556 */ 'v', 'f', 'n', 'm', 's', 0,
  /* 2562 */ 'v', 'r', 'e', 'c', 'p', 's', 0,
  /* 2569 */ 'v', 'm', 'r', 's', 0,
  /* 2574 */ 'a', 's', 'r', 's', 0,
  /* 2579 */ 'l', 's', 'r', 's', 0,
  /* 2584 */ 'v', 'r', 's', 'q', 'r', 't', 's', 0,
  /* 2592 */ 'm', 'o', 'v', 's', 0,
  /* 2597 */ 's', 's', 'a', 't', 0,
  /* 2602 */ 'u', 's', 'a', 't', 0,
  /* 2607 */ 's', 'm', 'l', 'a', 'b', 't', 0,
  /* 2614 */ 'p', 'k', 'h', 'b', 't', 0,
  /* 2620 */ 's', 'm', 'l', 'a', 'l', 'b', 't', 0,
  /* 2628 */ 's', 'm', 'u', 'l', 'b', 't', 0,
  /* 2635 */ 'l', 'd', 'r', 'b', 't', 0,
  /* 2641 */ 's', 't', 'r', 'b', 't', 0,
  /* 2647 */ 'l', 'd', 'r', 's', 'b', 't', 0,
  /* 2654 */ 'e', 'r', 'e', 't', 0,
  /* 2659 */ 'v', 'a', 'c', 'g', 't', 0,
  /* 2665 */ 'v', 'c', 'g', 't', 0,
  /* 2670 */ 'l', 'd', 'r', 'h', 't', 0,
  /* 2676 */ 's', 't', 'r', 'h', 't', 0,
  /* 2682 */ 'l', 'd', 'r', 's', 'h', 't', 0,
  /* 2689 */ 'r', 'b', 'i', 't', 0,
  /* 2694 */ 'v', 'b', 'i', 't', 0,
  /* 2699 */ 'v', 'c', 'l', 't', 0,
  /* 2704 */ 'v', 'c', 'n', 't', 0,
  /* 2709 */ 'h', 'i', 'n', 't', 0,
  /* 2714 */ 'l', 'd', 'r', 't', 0,
  /* 2719 */ 'v', 's', 'q', 'r', 't', 0,
  /* 2725 */ 's', 't', 'r', 't', 0,
  /* 2730 */ 'v', 't', 's', 't', 0,
  /* 2735 */ 's', 'm', 'l', 'a', 't', 't', 0,
  /* 2742 */ 's', 'm', 'l', 'a', 'l', 't', 't', 0,
  /* 2750 */ 's', 'm', 'u', 'l', 't', 't', 0,
  /* 2757 */ 'v', 'c', 'v', 't', 't', 0,
  /* 2763 */ 'v', 'c', 'v', 't', 0,
  /* 2768 */ 'm', 'o', 'v', 't', 0,
  /* 2773 */ 's', 'm', 'l', 'a', 'w', 't', 0,
  /* 2780 */ 's', 'm', 'u', 'l', 'w', 't', 0,
  /* 2787 */ 'v', 'e', 'x', 't', 0,
  /* 2792 */ 'v', 'q', 's', 'h', 'l', 'u', 0,
  /* 2799 */ 'r', 'e', 'v', 0,
  /* 2803 */ 's', 'd', 'i', 'v', 0,
  /* 2808 */ 'u', 'd', 'i', 'v', 0,
  /* 2813 */ 'v', 'd', 'i', 'v', 0,
  /* 2818 */ 'v', 'm', 'o', 'v', 0,
  /* 2823 */ 'v', 's', 'u', 'b', 'w', 0,
  /* 2829 */ 'v', 'a', 'd', 'd', 'w', 0,
  /* 2835 */ 'p', 'l', 'd', 'w', 0,
  /* 2840 */ 'm', 'o', 'v', 'w', 0,
  /* 2845 */ 'f', 'l', 'd', 'm', 'i', 'a', 'x', 0,
  /* 2853 */ 'f', 's', 't', 'm', 'i', 'a', 'x', 0,
  /* 2861 */ 'v', 'p', 'm', 'a', 'x', 0,
  /* 2867 */ 'v', 'm', 'a', 'x', 0,
  /* 2872 */ 's', 'h', 's', 'a', 'x', 0,
  /* 2878 */ 'u', 'h', 's', 'a', 'x', 0,
  /* 2884 */ 'u', 'q', 's', 'a', 'x', 0,
  /* 2890 */ 's', 's', 'a', 'x', 0,
  /* 2895 */ 'u', 's', 'a', 'x', 0,
  /* 2900 */ 'f', 'l', 'd', 'm', 'd', 'b', 'x', 0,
  /* 2908 */ 'f', 's', 't', 'm', 'd', 'b', 'x', 0,
  /* 2916 */ 'v', 't', 'b', 'x', 0,
  /* 2921 */ 's', 'm', 'l', 'a', 'd', 'x', 0,
  /* 2928 */ 's', 'm', 'u', 'a', 'd', 'x', 0,
  /* 2935 */ 's', 'm', 'l', 'a', 'l', 'd', 'x', 0,
  /* 2943 */ 's', 'm', 'l', 's', 'l', 'd', 'x', 0,
  /* 2951 */ 's', 'm', 'l', 's', 'd', 'x', 0,
  /* 2958 */ 's', 'm', 'u', 's', 'd', 'x', 0,
  /* 2965 */ 'l', 'd', 'a', 'e', 'x', 0,
  /* 2971 */ 's', 't', 'l', 'e', 'x', 0,
  /* 2977 */ 'l', 'd', 'r', 'e', 'x', 0,
  /* 2983 */ 'c', 'l', 'r', 'e', 'x', 0,
  /* 2989 */ 's', 't', 'r', 'e', 'x', 0,
  /* 2995 */ 's', 'b', 'f', 'x', 0,
  /* 3000 */ 'u', 'b', 'f', 'x', 0,
  /* 3005 */ 'b', 'l', 'x', 0,
  /* 3009 */ 'r', 'r', 'x', 0,
  /* 3013 */ 's', 'h', 'a', 's', 'x', 0,
  /* 3019 */ 'u', 'h', 'a', 's', 'x', 0,
  /* 3025 */ 'u', 'q', 'a', 's', 'x', 0,
  /* 3031 */ 's', 'a', 's', 'x', 0,
  /* 3036 */ 'u', 'a', 's', 'x', 0,
  /* 3041 */ 'v', 'r', 'i', 'n', 't', 'x', 0,
  /* 3048 */ 'v', 'c', 'l', 'z', 0,
  /* 3053 */ 'v', 'r', 'i', 'n', 't', 'z', 0,
  };
#endif

  // printf(">>> opcode: %u\n", MCInst_getOpcode(MI));
  // Emit the opcode for the instruction.
  uint64_t Bits1 = OpInfo[MCInst_getOpcode(MI)];
  uint64_t Bits2 = OpInfo2[MCInst_getOpcode(MI)];
  uint64_t Bits = (Bits2 << 32) | Bits1;
  // assert(Bits != 0 && "Cannot print this instruction.");
#ifndef CAPSTONE_DIET
  SStream_concat0(O, AsmStrs+(Bits & 4095)-1);
#endif


  // Fragment 0 encoded into 5 bits for 29 unique commands.
  //printf("Frag-0: %"PRIu64"\n", (Bits >> 12) & 31);
  switch ((Bits >> 12) & 31) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // DBG_VALUE, BUNDLE, LIFETIME_START, LIFETIME_END, CLREX, TRAP, TRAPNaCl...
    return;
    break;
  case 1:
    // ADCri, ADCrr, ADDri, ADDrr, ANDri, ANDrr, ASRi, ASRr, BICri, BICrr, EO...
    printSBitModifierOperand(MI, 5, O); 
    printPredicateOperand(MI, 3, O); 
    break;
  case 2:
    // ADCrsi, ADDrsi, ANDrsi, BICrsi, EORrsi, MLA, MOVsr, MVNsr, ORRrsi, RSB...
    printSBitModifierOperand(MI, 6, O); 
    printPredicateOperand(MI, 4, O); 
    break;
  case 3:
    // ADCrsr, ADDrsr, ANDrsr, BICrsr, EORrsr, ORRrsr, RSBrsr, RSCrsr, SBCrsr...
    printSBitModifierOperand(MI, 7, O); 
    printPredicateOperand(MI, 5, O); 
    SStream_concat0(O, "\t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printSORegRegOperand(MI, 2, O); 
    return;
    break;
  case 4:
    // ADR, CLZ, CMNri, CMNzrr, CMPri, CMPrr, FCONSTD, FCONSTS, FLDMXDB_UPD, ...
    printPredicateOperand(MI, 2, O); 
    break;
  case 5:
    // AESD, AESE, AESIMC, AESMC, BKPT, BL, BLX, BLXi, BX, CPS1p, CRC32B, CRC...
    printOperand(MI, 0, O); 
    break;
  case 6:
    // BFC, CMNzrsi, CMPrsi, LDRBi12, LDRcp, LDRi12, MOVTi16, QADD, QADD16, Q...
    printPredicateOperand(MI, 3, O); 
    break;
  case 7:
    // BFI, CMNzrsr, CMPrsr, LDCL_OFFSET, LDCL_OPTION, LDCL_POST, LDCL_PRE, L...
    printPredicateOperand(MI, 4, O); 
    break;
  case 8:
    // BLX_pred, BL_pred, BXJ, BX_pred, Bcc, DBG, FLDMXIA, FSTMXIA, HINT, LDM...
    printPredicateOperand(MI, 1, O); 
    break;
  case 9:
    // BX_RET, ERET, FMSTAT, MOVPCLR, t2CLREX, t2DCPS1, t2DCPS2, t2DCPS3, tBL...
    printPredicateOperand(MI, 0, O); 
    break;
  case 10:
    // CDP, LDRD_POST, LDRD_PRE, MCR, MRC, STRD_POST, STRD_PRE, VLD4DUPd16, V...
    printPredicateOperand(MI, 6, O); 
    break;
  case 11:
    // CDP2, LDC2L_OFFSET, LDC2L_OPTION, LDC2L_POST, LDC2L_PRE, LDC2_OFFSET, ...
    printPImmediate(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 12:
    // CPS2p, CPS3p, t2CPS2p, t2CPS3p, tCPS
    printCPSIMod(MI, 0, O); 
    break;
  case 13:
    // DMB, DSB
    printMemBOption(MI, 0, O); 
    return;
    break;
  case 14:
    // ISB
    printInstSyncBOption(MI, 0, O); 
    return;
    break;
  case 15:
    // ITasm, t2IT
    printThumbITMask(MI, 1, O); 
    break;
  case 16:
    // LDRBT_POST_IMM, LDRBT_POST_REG, LDRB_POST_IMM, LDRB_POST_REG, LDRB_PRE...
    printPredicateOperand(MI, 5, O); 
    break;
  case 17:
    // MOVi, MOVr, MOVr_TC, MVNi, MVNr, RRXi, t2MOVi, t2MOVr, t2MVNi, t2MVNr,...
    printSBitModifierOperand(MI, 4, O); 
    printPredicateOperand(MI, 2, O); 
    break;
  case 18:
    // MRC2
    printPImmediate(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printCImmediate(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printCImmediate(MI, 4, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 5, O); 
    return;
    break;
  case 19:
    // PLDWi12, PLDi12, PLIi12
    printAddrModeImm12Operand(MI, 0, O, false); 
    return;
    break;
  case 20:
    // PLDWrs, PLDrs, PLIrs
    printAddrMode2Operand(MI, 0, O); 
    return;
    break;
  case 21:
    // SETEND, tSETEND
    printSetendOperand(MI, 0, O); 
    return;
    break;
  case 22:
    // SMLAL, UMLAL
    printSBitModifierOperand(MI, 8, O); 
    printPredicateOperand(MI, 6, O); 
    SStream_concat0(O, "\t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 3, O); 
    return;
    break;
  case 23:
    // VLD1LNd16_UPD, VLD1LNd32_UPD, VLD1LNd8_UPD, VLD2LNd16, VLD2LNd32, VLD2...
    printPredicateOperand(MI, 7, O); 
    break;
  case 24:
    // VLD2LNd16_UPD, VLD2LNd32_UPD, VLD2LNd8_UPD, VLD2LNq16_UPD, VLD2LNq32_U...
    printPredicateOperand(MI, 9, O); 
    break;
  case 25:
    // VLD3LNd16_UPD, VLD3LNd32_UPD, VLD3LNd8_UPD, VLD3LNq16_UPD, VLD3LNq32_U...
    printPredicateOperand(MI, 11, O); 
    break;
  case 26:
    // VLD4DUPd16_UPD, VLD4DUPd32_UPD, VLD4DUPd8_UPD, VLD4DUPq16_UPD, VLD4DUP...
    printPredicateOperand(MI, 8, O); 
    break;
  case 27:
    // VLD4LNd16_UPD, VLD4LNd32_UPD, VLD4LNd8_UPD, VLD4LNq16_UPD, VLD4LNq32_U...
    printPredicateOperand(MI, 13, O); 
    break;
  case 28:
    // tADC, tADDi3, tADDi8, tADDrr, tAND, tASRri, tASRrr, tBIC, tEOR, tLSLri...
    printSBitModifierOperand(MI, 1, O); 
    break;
  }


  // Fragment 1 encoded into 7 bits for 65 unique commands.
  //printf("Frag-1: %"PRIu64"\n", (Bits >> 17) & 127);
  switch ((Bits >> 17) & 127) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // ADCri, ADCrr, ADCrsi, ADDri, ADDrr, ADDrsi, ADR, ANDri, ANDrr, ANDrsi,...
    SStream_concat0(O, "\t"); 
    break;
  case 1:
    // AESD, AESE, AESIMC, AESMC, CRC32B, CRC32CB, CRC32CH, CRC32CW, CRC32H, ...
    SStream_concat0(O, ", "); 
    break;
  case 2:
    // ASRi, ASRr, ITasm, LDRBT_POST, LDRT_POST, LSLi, LSLr, LSRi, LSRr, RORi...
    SStream_concat0(O, " "); 
    break;
  case 3:
    // BKPT, BL, BLX, BLXi, BX, CPS1p, ERET, HLT, HVC, RFEDA, RFEDB, RFEIA, R...
    return;
    break;
  case 4:
    // BX_RET
    SStream_concat0(O, "\tlr"); 
	ARM_addReg(MI, ARM_REG_LR);
    return;
    break;
  case 5:
    // CDP2, MCR2, MCRR2, MRRC2
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    break;
  case 6:
    // FCONSTD, VABSD, VADDD, VCMPD, VCMPED, VCMPEZD, VCMPZD, VDIVD, VFMAD, V...
    SStream_concat0(O, ".f64\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F64);
    printOperand(MI, 0, O); 
    break;
  case 7:
    // FCONSTS, VABDfd, VABDfq, VABSS, VABSfd, VABSfq, VACGEd, VACGEq, VACGTd...
    SStream_concat0(O, ".f32\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F32);
    printOperand(MI, 0, O); 
    break;
  case 8:
    // FMSTAT
    SStream_concat0(O, "\tAPSR_nzcv, fpscr"); 
	ARM_addReg(MI, ARM_REG_APSR_NZCV);
	ARM_addReg(MI, ARM_REG_FPSCR);
    return;
    break;
  case 9:
    // LDC2L_OFFSET, LDC2L_OPTION, LDC2L_POST, LDC2L_PRE, LDC2_OFFSET, LDC2_O...
    printCImmediate(MI, 1, O); 
    SStream_concat0(O, ", "); 
    break;
  case 10:
    // MOVPCLR
    SStream_concat0(O, "\tpc, lr"); 
	ARM_addReg(MI, ARM_REG_PC);
	ARM_addReg(MI, ARM_REG_LR);
    return;
    break;
  case 11:
    // RFEDA_UPD, RFEDB_UPD, RFEIA_UPD, RFEIB_UPD
    SStream_concat0(O, "!"); 
    return;
    break;
  case 12:
    // VABALsv2i64, VABAsv2i32, VABAsv4i32, VABDLsv2i64, VABDsv2i32, VABDsv4i...
    SStream_concat0(O, ".s32\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_S32);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 13:
    // VABALsv4i32, VABAsv4i16, VABAsv8i16, VABDLsv4i32, VABDsv4i16, VABDsv8i...
    SStream_concat0(O, ".s16\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_S16);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 14:
    // VABALsv8i16, VABAsv16i8, VABAsv8i8, VABDLsv8i16, VABDsv16i8, VABDsv8i8...
    SStream_concat0(O, ".s8\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_S8);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 15:
    // VABALuv2i64, VABAuv2i32, VABAuv4i32, VABDLuv2i64, VABDuv2i32, VABDuv4i...
    SStream_concat0(O, ".u32\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_U32);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 16:
    // VABALuv4i32, VABAuv4i16, VABAuv8i16, VABDLuv4i32, VABDuv4i16, VABDuv8i...
    SStream_concat0(O, ".u16\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_U16);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 17:
    // VABALuv8i16, VABAuv16i8, VABAuv8i8, VABDLuv8i16, VABDuv16i8, VABDuv8i8...
    SStream_concat0(O, ".u8\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_U8);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 18:
    // VADDHNv2i32, VADDv1i64, VADDv2i64, VMOVNv2i32, VMOVv1i64, VMOVv2i64, V...
    SStream_concat0(O, ".i64\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_I64);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 19:
    // VADDHNv4i16, VADDv2i32, VADDv4i32, VBICiv2i32, VBICiv4i32, VCEQv2i32, ...
    SStream_concat0(O, ".i32\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_I32);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 20:
    // VADDHNv8i8, VADDv4i16, VADDv8i16, VBICiv4i16, VBICiv8i16, VCEQv4i16, V...
    SStream_concat0(O, ".i16\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_I16);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 21:
    // VADDv16i8, VADDv8i8, VCEQv16i8, VCEQv8i8, VCEQzv16i8, VCEQzv8i8, VCLZv...
    SStream_concat0(O, ".i8\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_I8);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 22:
    // VCNTd, VCNTq, VDUP8d, VDUP8q, VDUPLN8d, VDUPLN8q, VEXTd8, VEXTq8, VLD1...
    SStream_concat0(O, ".8\t"); 
	ARM_addVectorDataSize(MI, 8);
    break;
  case 23:
    // VCVTBDH, VCVTTDH
    SStream_concat0(O, ".f16.f64\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F16F64);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 24:
    // VCVTBHD, VCVTTHD
    SStream_concat0(O, ".f64.f16\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F64F16);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 25:
    // VCVTBHS, VCVTTHS, VCVTh2f
    SStream_concat0(O, ".f32.f16\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F32F16);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 26:
    // VCVTBSH, VCVTTSH, VCVTf2h
    SStream_concat0(O, ".f16.f32\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F16F32);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 27:
    // VCVTDS
    SStream_concat0(O, ".f64.f32\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F64F32);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 28:
    // VCVTSD
    SStream_concat0(O, ".f32.f64\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F32F64);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 29:
    // VCVTf2sd, VCVTf2sq, VCVTf2xsd, VCVTf2xsq, VTOSIRS, VTOSIZS, VTOSLS
    SStream_concat0(O, ".s32.f32\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_S32F32);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    break;
  case 30:
    // VCVTf2ud, VCVTf2uq, VCVTf2xud, VCVTf2xuq, VTOUIRS, VTOUIZS, VTOULS
    SStream_concat0(O, ".u32.f32\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_U32F32);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    break;
  case 31:
    // VCVTs2fd, VCVTs2fq, VCVTxs2fd, VCVTxs2fq, VSITOS, VSLTOS
    SStream_concat0(O, ".f32.s32\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F32S32);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    break;
  case 32:
    // VCVTu2fd, VCVTu2fq, VCVTxu2fd, VCVTxu2fq, VUITOS, VULTOS
    SStream_concat0(O, ".f32.u32\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F32U32);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    break;
  case 33:
    // VDUP16d, VDUP16q, VDUPLN16d, VDUPLN16q, VEXTd16, VEXTq16, VLD1DUPd16, ...
    SStream_concat0(O, ".16\t"); 
	ARM_addVectorDataSize(MI, 16);
    break;
  case 34:
    // VDUP32d, VDUP32q, VDUPLN32d, VDUPLN32q, VEXTd32, VEXTq32, VGETLNi32, V...
    SStream_concat0(O, ".32\t"); 
	ARM_addVectorDataSize(MI, 32);
    break;
  case 35:
    // VEXTq64, VLD1d64, VLD1d64Q, VLD1d64Qwb_fixed, VLD1d64Qwb_register, VLD...
    SStream_concat0(O, ".64\t"); 
	ARM_addVectorDataSize(MI, 64);
    break;
  case 36:
    // VLD1LNd16, VLD1LNd16_UPD, VLD2LNd16, VLD2LNd16_UPD, VLD2LNq16, VLD2LNq...
    SStream_concat0(O, ".16\t{"); 
	ARM_addVectorDataSize(MI, 16);
    break;
  case 37:
    // VLD1LNd32, VLD1LNd32_UPD, VLD2LNd32, VLD2LNd32_UPD, VLD2LNq32, VLD2LNq...
    SStream_concat0(O, ".32\t{"); 
	ARM_addVectorDataSize(MI, 32);
    break;
  case 38:
    // VLD1LNd8, VLD1LNd8_UPD, VLD2LNd8, VLD2LNd8_UPD, VLD3DUPd8, VLD3DUPd8_U...
    SStream_concat0(O, ".8\t{"); 
	ARM_addVectorDataSize(MI, 8);
    break;
  case 39:
    // VMSR
    SStream_concat0(O, "\tfpscr, "); 
	ARM_addReg(MI, ARM_REG_FPSCR);
    printOperand(MI, 0, O); 
    return;
    break;
  case 40:
    // VMSR_FPEXC
    SStream_concat0(O, "\tfpexc, "); 
	ARM_addReg(MI, ARM_REG_FPEXC);
    printOperand(MI, 0, O); 
    return;
    break;
  case 41:
    // VMSR_FPINST
    SStream_concat0(O, "\tfpinst, "); 
	ARM_addReg(MI, ARM_REG_FPINST);
    printOperand(MI, 0, O); 
    return;
    break;
  case 42:
    // VMSR_FPINST2
    SStream_concat0(O, "\tfpinst2, "); 
	ARM_addReg(MI, ARM_REG_FPINST2);
    printOperand(MI, 0, O); 
    return;
    break;
  case 43:
    // VMSR_FPSID
    SStream_concat0(O, "\tfpsid, "); 
	ARM_addReg(MI, ARM_REG_FPSID);
    printOperand(MI, 0, O); 
    return;
    break;
  case 44:
    // VMULLp8, VMULpd, VMULpq
    SStream_concat0(O, ".p8\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_P8);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    return;
    break;
  case 45:
    // VQADDsv1i64, VQADDsv2i64, VQMOVNsuv2i32, VQMOVNsv2i32, VQRSHLsv1i64, V...
    SStream_concat0(O, ".s64\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_S64);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 46:
    // VQADDuv1i64, VQADDuv2i64, VQMOVNuv2i32, VQRSHLuv1i64, VQRSHLuv2i64, VQ...
    SStream_concat0(O, ".u64\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_U64);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 47:
    // VSHTOD
    SStream_concat0(O, ".f64.s16\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F64S16);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printFBits16(MI, 2, O); 
    return;
    break;
  case 48:
    // VSHTOS
    SStream_concat0(O, ".f32.s16\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F32S16);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printFBits16(MI, 2, O); 
    return;
    break;
  case 49:
    // VSITOD, VSLTOD
    SStream_concat0(O, ".f64.s32\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F64S32);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    break;
  case 50:
    // VTOSHD
    SStream_concat0(O, ".s16.f64\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_S16F64);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printFBits16(MI, 2, O); 
    return;
    break;
  case 51:
    // VTOSHS
    SStream_concat0(O, ".s16.f32\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_S16F32);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printFBits16(MI, 2, O); 
    return;
    break;
  case 52:
    // VTOSIRD, VTOSIZD, VTOSLD
    SStream_concat0(O, ".s32.f64\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_S32F64);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    break;
  case 53:
    // VTOUHD
    SStream_concat0(O, ".u16.f64\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_U16F64);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printFBits16(MI, 2, O); 
    return;
    break;
  case 54:
    // VTOUHS
    SStream_concat0(O, ".u16.f32\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_U16F32);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printFBits16(MI, 2, O); 
    return;
    break;
  case 55:
    // VTOUIRD, VTOUIZD, VTOULD
    SStream_concat0(O, ".u32.f64\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_U32F64);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    break;
  case 56:
    // VUHTOD
    SStream_concat0(O, ".f64.u16\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F64U16);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printFBits16(MI, 2, O); 
    return;
    break;
  case 57:
    // VUHTOS
    SStream_concat0(O, ".f32.u16\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F32U16);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printFBits16(MI, 2, O); 
    return;
    break;
  case 58:
    // VUITOD, VULTOD
    SStream_concat0(O, ".f64.u32\t"); 
	ARM_addVectorDataType(MI, ARM_VECTORDATA_F64U32);
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    break;
  case 59:
    // t2ADCrr, t2ADCrs, t2ADDri, t2ADDrr, t2ADDrs, t2ADR, t2ANDrr, t2ANDrs, ...
    SStream_concat0(O, ".w\t"); 
    break;
  case 60:
    // t2SRSDB, t2SRSIA
    SStream_concat0(O, "\tsp, "); 
	ARM_addReg(MI, ARM_REG_SP);
    printOperand(MI, 0, O); 
    return;
    break;
  case 61:
    // t2SRSDB_UPD, t2SRSIA_UPD
    SStream_concat0(O, "\tsp!, "); 
	ARM_addReg(MI, ARM_REG_SP);
    printOperand(MI, 0, O); 
    return;
    break;
  case 62:
    // t2SUBS_PC_LR
    SStream_concat0(O, "\tpc, lr, "); 
	ARM_addReg(MI, ARM_REG_PC);
	ARM_addReg(MI, ARM_REG_LR);
    printOperand(MI, 0, O); 
    return;
    break;
  case 63:
    // tADC, tADDi3, tADDi8, tADDrr, tAND, tASRri, tASRrr, tBIC, tEOR, tLSLri...
    printPredicateOperand(MI, 4, O); 
    SStream_concat0(O, "\t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 64:
    // tMOVi8, tMVN, tRSB
    printPredicateOperand(MI, 3, O); 
    SStream_concat0(O, "\t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    break;
  }


  // Fragment 2 encoded into 6 bits for 58 unique commands.
  //printf("Frag-2: %"PRIu64"\n", (Bits >> 24) & 63);
  switch ((Bits >> 24) & 63) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // ADCri, ADCrr, ADCrsi, ADDri, ADDrr, ADDrsi, ADR, ANDri, ANDrr, ANDrsi,...
    printOperand(MI, 0, O); 
    break;
  case 1:
    // AESD, AESE, MCR2, MCRR2, MRRC2, SHA1C, SHA1M, SHA1P, SHA1SU0, SHA1SU1,...
    printOperand(MI, 2, O); 
    break;
  case 2:
    // AESIMC, AESMC, CRC32B, CRC32CB, CRC32CH, CRC32CW, CRC32H, CRC32W, FLDM...
    printOperand(MI, 1, O); 
    break;
  case 3:
    // CDP, LDCL_OFFSET, LDCL_OPTION, LDCL_POST, LDCL_PRE, LDC_OFFSET, LDC_OP...
    printPImmediate(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 4:
    // CDP2
    printCImmediate(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printCImmediate(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printCImmediate(MI, 4, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 5, O); 
    return;
    break;
  case 5:
    // CPS2p, CPS3p, t2CPS2p, t2CPS3p, tCPS
    printCPSIFlag(MI, 1, O); 
    break;
  case 6:
    // FCONSTD, FCONSTS, VABDfd, VABDfq, VABSD, VABSS, VABSfd, VABSfq, VACGEd...
    SStream_concat0(O, ", "); 
    break;
  case 7:
    // ITasm, t2IT
    printMandatoryPredicateOperand(MI, 0, O); 
    return;
    break;
  case 8:
    // LDAEXD, LDREXD
    printGPRPairOperand(MI, 0, O, MRI); 
    SStream_concat0(O, ", "); 
    printAddrMode7Operand(MI, 1, O); 
    return;
    break;
  case 9:
    // LDC2L_OFFSET, LDC2_OFFSET, STC2L_OFFSET, STC2_OFFSET
    printAddrMode5Operand(MI, 2, O, false); 
    return;
    break;
  case 10:
    // LDC2L_OPTION, LDC2L_POST, LDC2_OPTION, LDC2_POST, STC2L_OPTION, STC2L_...
    printAddrMode7Operand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 11:
    // LDC2L_PRE, LDC2_PRE, STC2L_PRE, STC2_PRE
    printAddrMode5Operand(MI, 2, O, true); 
    SStream_concat0(O, "!"); 
    return;
    break;
  case 12:
    // MRC, t2MRC, t2MRC2
    printPImmediate(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printCImmediate(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printCImmediate(MI, 4, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 5, O); 
    return;
    break;
  case 13:
    // MSR, MSRi, t2MSR_AR, t2MSR_M
    printMSRMaskOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 14:
    // MSRbanked, t2MSRbanked
    printBankedRegOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 15:
    // VBICiv2i32, VBICiv4i16, VBICiv4i32, VBICiv8i16, VMOVv16i8, VMOVv1i64, ...
    printNEONModImmOperand(MI, 1, O); 
    return;
    break;
  case 16:
    // VCMPEZD, VCMPEZS, VCMPZD, VCMPZS, tRSB
    SStream_concat0(O, ", #0"); 
	op_addImm(MI, 0);
    return;
    break;
  case 17:
    // VCVTf2sd, VCVTf2sq, VCVTf2ud, VCVTf2uq, VCVTs2fd, VCVTs2fq, VCVTu2fd, ...
    return;
    break;
  case 18:
    // VLD1DUPd16, VLD1DUPd16wb_fixed, VLD1DUPd16wb_register, VLD1DUPd32, VLD...
    printVectorListOneAllLanes(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 19:
    // VLD1DUPq16, VLD1DUPq16wb_fixed, VLD1DUPq16wb_register, VLD1DUPq32, VLD...
    printVectorListTwoAllLanes(MI, 0, O, MRI); 
    SStream_concat0(O, ", "); 
    break;
  case 20:
    // VLD1d16, VLD1d16wb_fixed, VLD1d16wb_register, VLD1d32, VLD1d32wb_fixed...
    printVectorListOne(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 21:
    // VLD1d16Q, VLD1d16Qwb_fixed, VLD1d16Qwb_register, VLD1d32Q, VLD1d32Qwb_...
    printVectorListFour(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 22:
    // VLD1d16T, VLD1d16Twb_fixed, VLD1d16Twb_register, VLD1d32T, VLD1d32Twb_...
    printVectorListThree(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 23:
    // VLD1q16, VLD1q16wb_fixed, VLD1q16wb_register, VLD1q32, VLD1q32wb_fixed...
    printVectorListTwo(MI, 0, O, MRI); 
    SStream_concat0(O, ", "); 
    break;
  case 24:
    // VLD2DUPd16x2, VLD2DUPd16x2wb_fixed, VLD2DUPd16x2wb_register, VLD2DUPd3...
    printVectorListTwoSpacedAllLanes(MI, 0, O, MRI); 
    SStream_concat0(O, ", "); 
    break;
  case 25:
    // VLD2b16, VLD2b16wb_fixed, VLD2b16wb_register, VLD2b32, VLD2b32wb_fixed...
    printVectorListTwoSpaced(MI, 0, O, MRI); 
    SStream_concat0(O, ", "); 
    break;
  case 26:
    // VLD3DUPdAsm_16, VLD3DUPdAsm_32, VLD3DUPdAsm_8, VLD3DUPdWB_fixed_Asm_16...
    printVectorListThreeAllLanes(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    break;
  case 27:
    // VLD3DUPqAsm_16, VLD3DUPqAsm_32, VLD3DUPqAsm_8, VLD3DUPqWB_fixed_Asm_16...
    printVectorListThreeSpacedAllLanes(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    break;
  case 28:
    // VLD3qAsm_16, VLD3qAsm_32, VLD3qAsm_8, VLD3qWB_fixed_Asm_16, VLD3qWB_fi...
    printVectorListThreeSpaced(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    break;
  case 29:
    // VLD4DUPdAsm_16, VLD4DUPdAsm_32, VLD4DUPdAsm_8, VLD4DUPdWB_fixed_Asm_16...
    printVectorListFourAllLanes(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    break;
  case 30:
    // VLD4DUPqAsm_16, VLD4DUPqAsm_32, VLD4DUPqAsm_8, VLD4DUPqWB_fixed_Asm_16...
    printVectorListFourSpacedAllLanes(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    break;
  case 31:
    // VLD4qAsm_16, VLD4qAsm_32, VLD4qAsm_8, VLD4qWB_fixed_Asm_16, VLD4qWB_fi...
    printVectorListFourSpaced(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    break;
  case 32:
    // VST1LNd16_UPD, VST1LNd32_UPD, VST1LNd8_UPD, VST2LNd16_UPD, VST2LNd32_U...
    printOperand(MI, 4, O); 
    break;
  case 33:
    // VST1d16, VST1d32, VST1d64, VST1d8
    printVectorListOne(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 0, O); 
    return;
    break;
  case 34:
    // VST1d16Q, VST1d32Q, VST1d64Q, VST1d8Q, VST2q16, VST2q32, VST2q8
    printVectorListFour(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 0, O); 
    return;
    break;
  case 35:
    // VST1d16Qwb_fixed, VST1d32Qwb_fixed, VST1d64Qwb_fixed, VST1d8Qwb_fixed,...
    printVectorListFour(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    SStream_concat0(O, "!"); 
    return;
    break;
  case 36:
    // VST1d16Qwb_register, VST1d32Qwb_register, VST1d64Qwb_register, VST1d8Q...
    printVectorListFour(MI, 4, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 3, O); 
    return;
    break;
  case 37:
    // VST1d16T, VST1d32T, VST1d64T, VST1d8T
    printVectorListThree(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 0, O); 
    return;
    break;
  case 38:
    // VST1d16Twb_fixed, VST1d32Twb_fixed, VST1d64Twb_fixed, VST1d8Twb_fixed
    printVectorListThree(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    SStream_concat0(O, "!"); 
    return;
    break;
  case 39:
    // VST1d16Twb_register, VST1d32Twb_register, VST1d64Twb_register, VST1d8T...
    printVectorListThree(MI, 4, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 3, O); 
    return;
    break;
  case 40:
    // VST1d16wb_fixed, VST1d32wb_fixed, VST1d64wb_fixed, VST1d8wb_fixed
    printVectorListOne(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    SStream_concat0(O, "!"); 
    return;
    break;
  case 41:
    // VST1d16wb_register, VST1d32wb_register, VST1d64wb_register, VST1d8wb_r...
    printVectorListOne(MI, 4, O); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 3, O); 
    return;
    break;
  case 42:
    // VST1q16, VST1q32, VST1q64, VST1q8, VST2d16, VST2d32, VST2d8
    printVectorListTwo(MI, 2, O, MRI); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 0, O); 
    return;
    break;
  case 43:
    // VST1q16wb_fixed, VST1q32wb_fixed, VST1q64wb_fixed, VST1q8wb_fixed, VST...
    printVectorListTwo(MI, 3, O, MRI); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    SStream_concat0(O, "!"); 
    return;
    break;
  case 44:
    // VST1q16wb_register, VST1q32wb_register, VST1q64wb_register, VST1q8wb_r...
    printVectorListTwo(MI, 4, O, MRI); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 3, O); 
    return;
    break;
  case 45:
    // VST2b16, VST2b32, VST2b8
    printVectorListTwoSpaced(MI, 2, O, MRI); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 0, O); 
    return;
    break;
  case 46:
    // VST2b16wb_fixed, VST2b32wb_fixed, VST2b8wb_fixed
    printVectorListTwoSpaced(MI, 3, O, MRI); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    SStream_concat0(O, "!"); 
    return;
    break;
  case 47:
    // VST2b16wb_register, VST2b32wb_register, VST2b8wb_register
    printVectorListTwoSpaced(MI, 4, O, MRI); 
    SStream_concat0(O, ", "); 
    printAddrMode6Operand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 3, O); 
    return;
    break;
  case 48:
    // t2DMB, t2DSB
    printMemBOption(MI, 0, O); 
    return;
    break;
  case 49:
    // t2ISB
    printInstSyncBOption(MI, 0, O); 
    return;
    break;
  case 50:
    // t2PLDWi12, t2PLDi12, t2PLIi12
    printAddrModeImm12Operand(MI, 0, O, false); 
    return;
    break;
  case 51:
    // t2PLDWi8, t2PLDi8, t2PLIi8
    printT2AddrModeImm8Operand(MI, 0, O, false); 
    return;
    break;
  case 52:
    // t2PLDWs, t2PLDs, t2PLIs
    printT2AddrModeSoRegOperand(MI, 0, O); 
    return;
    break;
  case 53:
    // t2PLDpci, t2PLIpci
    printThumbLdrLabelOperand(MI, 0, O); 
    return;
    break;
  case 54:
    // t2TBB
    printAddrModeTBB(MI, 0, O); 
    return;
    break;
  case 55:
    // t2TBH
    printAddrModeTBH(MI, 0, O); 
    return;
    break;
  case 56:
    // tADC, tADDi8, tAND, tASRrr, tBIC, tEOR, tLSLrr, tLSRrr, tORR, tROR, tS...
    printOperand(MI, 3, O); 
    return;
    break;
  case 57:
    // tPOP, tPUSH
    printRegisterList(MI, 2, O); 
    return;
    break;
  }


  // Fragment 3 encoded into 5 bits for 29 unique commands.
  //printf("Frag-3: %"PRIu64"\n", (Bits >> 30) & 31);
  switch ((Bits >> 30) & 31) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // ADCri, ADCrr, ADCrsi, ADDri, ADDrr, ADDrsi, ADR, ANDri, ANDrr, ANDrsi,...
    SStream_concat0(O, ", "); 
    break;
  case 1:
    // AESD, AESE, AESIMC, AESMC, BLX_pred, BL_pred, BXJ, BX_pred, Bcc, CPS2p...
    return;
    break;
  case 2:
    // CDP, MCR, MCRR, MRRC, MSR, VABDfd, VABDfq, VABSD, VABSS, VABSfd, VABSf...
    printOperand(MI, 1, O); 
    break;
  case 3:
    // FCONSTD, FCONSTS, VMOVv2f32, VMOVv4f32
    printFPImmOperand(MI, 1, O); 
    return;
    break;
  case 4:
    // FLDMXDB_UPD, FLDMXIA_UPD, FSTMXDB_UPD, FSTMXIA_UPD, LDMDA_UPD, LDMDB_U...
    SStream_concat0(O, "!, "); 
    printRegisterList(MI, 4, O); 
    break;
  case 5:
    // LDC2L_OPTION, LDC2_OPTION, STC2L_OPTION, STC2_OPTION
    printCoprocOptionImm(MI, 3, O); 
    return;
    break;
  case 6:
    // LDC2L_POST, LDC2_POST, STC2L_POST, STC2_POST
    printPostIdxImm8s4Operand(MI, 3, O); 
    return;
    break;
  case 7:
    // LDCL_OFFSET, LDCL_OPTION, LDCL_POST, LDCL_PRE, LDC_OFFSET, LDC_OPTION,...
    printCImmediate(MI, 1, O); 
    SStream_concat0(O, ", "); 
    break;
  case 8:
    // MRS, t2MRS_AR
    SStream_concat0(O, ", apsr"); 
	ARM_addReg(MI, ARM_REG_APSR);
    return;
    break;
  case 9:
    // MRSsys, t2MRSsys_AR
    SStream_concat0(O, ", spsr"); 
	ARM_addReg(MI, ARM_REG_SPSR);
    return;
    break;
  case 10:
    // MSRi
    printModImmOperand(MI, 1, O); 
    return;
    break;
  case 11:
    // VCEQzv16i8, VCEQzv2i32, VCEQzv4i16, VCEQzv4i32, VCEQzv8i16, VCEQzv8i8,...
    SStream_concat0(O, ", #0"); 
	op_addImm(MI, 0);
    return;
    break;
  case 12:
    // VCVTf2xsd, VCVTf2xsq, VCVTf2xud, VCVTf2xuq, VCVTxs2fd, VCVTxs2fq, VCVT...
    printOperand(MI, 2, O); 
    break;
  case 13:
    // VGETLNs16, VGETLNs8, VGETLNu16, VGETLNu8
    printVectorIndex(MI, 2, O); 
    return;
    break;
  case 14:
    // VLD1DUPd16, VLD1DUPd32, VLD1DUPd8, VLD1DUPq16, VLD1DUPq32, VLD1DUPq8, ...
    printAddrMode6Operand(MI, 1, O); 
    break;
  case 15:
    // VLD1DUPd16wb_fixed, VLD1DUPd16wb_register, VLD1DUPd32wb_fixed, VLD1DUP...
    printAddrMode6Operand(MI, 2, O); 
    break;
  case 16:
    // VLD1LNd16, VLD1LNd16_UPD, VLD1LNd32, VLD1LNd32_UPD, VLD1LNd8, VLD1LNd8...
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    break;
  case 17:
    // VLD3DUPd16, VLD3DUPd16_UPD, VLD3DUPd32, VLD3DUPd32_UPD, VLD3DUPd8, VLD...
    SStream_concat0(O, "[], "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, "[], "); 
    printOperand(MI, 2, O); 
    break;
  case 18:
    // VLD3DUPdWB_fixed_Asm_16, VLD3DUPdWB_fixed_Asm_32, VLD3DUPdWB_fixed_Asm...
    SStream_concat0(O, "!"); 
    return;
    break;
  case 19:
    // VMRS
    SStream_concat0(O, ", fpscr"); 
	ARM_addReg(MI, ARM_REG_FPSCR);
    return;
    break;
  case 20:
    // VMRS_FPEXC
    SStream_concat0(O, ", fpexc"); 
	ARM_addReg(MI, ARM_REG_FPEXC);
    return;
    break;
  case 21:
    // VMRS_FPINST
    SStream_concat0(O, ", fpinst"); 
	ARM_addReg(MI, ARM_REG_FPINST);
    return;
    break;
  case 22:
    // VMRS_FPINST2
    SStream_concat0(O, ", fpinst2"); 
	ARM_addReg(MI, ARM_REG_FPINST2);
    return;
    break;
  case 23:
    // VMRS_FPSID
    SStream_concat0(O, ", fpsid"); 
	ARM_addReg(MI, ARM_REG_FPSID);
    return;
    break;
  case 24:
    // VMRS_MVFR0
    SStream_concat0(O, ", mvfr0"); 
	ARM_addReg(MI, ARM_REG_MVFR0);
    return;
    break;
  case 25:
    // VMRS_MVFR1
    SStream_concat0(O, ", mvfr1"); 
	ARM_addReg(MI, ARM_REG_MVFR1);
    return;
    break;
  case 26:
    // VMRS_MVFR2
    SStream_concat0(O, ", mvfr2"); 
	ARM_addReg(MI, ARM_REG_MVFR2);
    return;
    break;
  case 27:
    // VSETLNi16, VSETLNi32, VSETLNi8
    printVectorIndex(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    return;
    break;
  case 28:
    // VSLTOD, VSLTOS, VTOSLD, VTOSLS, VTOULD, VTOULS, VULTOD, VULTOS
    printFBits32(MI, 2, O); 
    return;
    break;
  }


  // Fragment 4 encoded into 6 bits for 64 unique commands.
  //printf("Frag-4: %"PRIu64"\n", (Bits >> 35) & 63);
  switch ((Bits >> 35) & 63) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // ADCri, ADCrr, ADCrsi, ADDri, ADDrr, ADDrsi, ANDri, ANDrr, ANDrsi, ASRi...
    printOperand(MI, 1, O); 
    break;
  case 1:
    // ADR, t2ADR
    printAdrLabelOperand(MI, 1, O, 0); 
    return;
    break;
  case 2:
    // BFC, t2BFC
    printBitfieldInvMaskImmOperand(MI, 2, O); 
    return;
    break;
  case 3:
    // BFI, CPS3p, CRC32B, CRC32CB, CRC32CH, CRC32CW, CRC32H, CRC32W, MOVTi16...
    printOperand(MI, 2, O); 
    break;
  case 4:
    // CDP, MCR, MCRR, MRRC, VABDfd, VABDfq, VACGEd, VACGEq, VACGTd, VACGTq, ...
    SStream_concat0(O, ", "); 
    break;
  case 5:
    // CMNri, CMPri, MOVi, MVNi, TEQri, TSTri
    printModImmOperand(MI, 1, O); 
    return;
    break;
  case 6:
    // CMNzrsi, CMPrsi, MOVsi, MVNsi, TEQrsi, TSTrsi
    printSORegImmOperand(MI, 1, O); 
    return;
    break;
  case 7:
    // CMNzrsr, CMPrsr, MOVsr, MVNsr, TEQrsr, TSTrsr, t2MOVSsr, t2MOVsr
    printSORegRegOperand(MI, 1, O); 
    return;
    break;
  case 8:
    // FLDMXDB_UPD, FLDMXIA_UPD, FSTMXDB_UPD, FSTMXIA_UPD, LDMDA_UPD, LDMDB_U...
    return;
    break;
  case 9:
    // FLDMXIA, FSTMXIA, LDMDA, LDMDB, LDMIA, LDMIB, STMDA, STMDB, STMIA, STM...
    printRegisterList(MI, 3, O); 
    break;
  case 10:
    // LDA, LDAB, LDAEX, LDAEXB, LDAEXH, LDAH, LDRBT_POST, LDREX, LDREXB, LDR...
    printAddrMode7Operand(MI, 1, O); 
    return;
    break;
  case 11:
    // LDCL_OFFSET, LDC_OFFSET, STCL_OFFSET, STC_OFFSET, t2LDC2L_OFFSET, t2LD...
    printAddrMode5Operand(MI, 2, O, false); 
    return;
    break;
  case 12:
    // LDCL_OPTION, LDCL_POST, LDC_OPTION, LDC_POST, LDRBT_POST_IMM, LDRBT_PO...
    printAddrMode7Operand(MI, 2, O); 
    break;
  case 13:
    // LDCL_PRE, LDC_PRE, STCL_PRE, STC_PRE, t2LDC2L_PRE, t2LDC2_PRE, t2LDCL_...
    printAddrMode5Operand(MI, 2, O, true); 
    SStream_concat0(O, "!"); 
    return;
    break;
  case 14:
    // LDRB_PRE_IMM, LDR_PRE_IMM, STRB_PRE_IMM, STR_PRE_IMM
    printAddrModeImm12Operand(MI, 2, O, true); 
    SStream_concat0(O, "!"); 
    return;
    break;
  case 15:
    // LDRB_PRE_REG, LDR_PRE_REG, STRB_PRE_REG, STR_PRE_REG
    printAddrMode2Operand(MI, 2, O); 
    SStream_concat0(O, "!"); 
    return;
    break;
  case 16:
    // LDRBi12, LDRcp, LDRi12, STRBi12, STRi12, t2LDRBi12, t2LDRHi12, t2LDRSB...
    printAddrModeImm12Operand(MI, 1, O, false); 
    return;
    break;
  case 17:
    // LDRBrs, LDRrs, STRBrs, STRrs
    printAddrMode2Operand(MI, 1, O); 
    return;
    break;
  case 18:
    // LDRH, LDRSB, LDRSH, STRH
    printAddrMode3Operand(MI, 1, O, false); 
    return;
    break;
  case 19:
    // LDRH_PRE, LDRSB_PRE, LDRSH_PRE, STRH_PRE
    printAddrMode3Operand(MI, 2, O, true); 
    SStream_concat0(O, "!"); 
    return;
    break;
  case 20:
    // MCR2
    printCImmediate(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printCImmediate(MI, 4, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 5, O); 
    return;
    break;
  case 21:
    // MCRR2, MRRC2, SHA1C, SHA1M, SHA1P, SHA1SU0, SHA256H, SHA256H2, SHA256S...
    printOperand(MI, 3, O); 
    break;
  case 22:
    // MRSbanked, t2MRSbanked
    printBankedRegOperand(MI, 1, O); 
    return;
    break;
  case 23:
    // SSAT, SSAT16, t2SSAT, t2SSAT16
    printImmPlusOneOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    break;
  case 24:
    // STLEXD, STREXD
    printGPRPairOperand(MI, 1, O, MRI); 
    SStream_concat0(O, ", "); 
    printAddrMode7Operand(MI, 2, O); 
    return;
    break;
  case 25:
    // VCEQzv2f32, VCEQzv4f32, VCGEzv2f32, VCGEzv4f32, VCGTzv2f32, VCGTzv4f32...
    SStream_concat0(O, ", #0"); 
	op_addImm(MI, 0);
    return;
    break;
  case 26:
    // VLD1DUPd16wb_fixed, VLD1DUPd32wb_fixed, VLD1DUPd8wb_fixed, VLD1DUPq16w...
    SStream_concat0(O, "!"); 
    return;
    break;
  case 27:
    // VLD1LNd16, VLD1LNd32, VLD1LNd8, VST2LNd16, VST2LNd32, VST2LNd8, VST2LN...
    printNoHashImmediate(MI, 4, O); 
    break;
  case 28:
    // VLD1LNd16_UPD, VLD1LNd32_UPD, VLD1LNd8_UPD, VLD2LNd16, VLD2LNd32, VLD2...
    printNoHashImmediate(MI, 6, O); 
    break;
  case 29:
    // VLD1LNdAsm_16, VLD1LNdAsm_32, VLD1LNdAsm_8, VLD1LNdWB_fixed_Asm_16, VL...
    printAddrMode6Operand(MI, 2, O); 
    break;
  case 30:
    // VLD2LNd16_UPD, VLD2LNd32_UPD, VLD2LNd8_UPD, VLD2LNq16_UPD, VLD2LNq32_U...
    printNoHashImmediate(MI, 8, O); 
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    break;
  case 31:
    // VLD3DUPd16, VLD3DUPd16_UPD, VLD3DUPd32, VLD3DUPd32_UPD, VLD3DUPd8, VLD...
    SStream_concat0(O, "[]}, "); 
    break;
  case 32:
    // VLD3LNd16_UPD, VLD3LNd32_UPD, VLD3LNd8_UPD, VLD3LNq16_UPD, VLD3LNq32_U...
    printNoHashImmediate(MI, 10, O); 
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    printOperand(MI, 1, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 10, O); 
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    printOperand(MI, 2, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 10, O); 
    break;
  case 33:
    // VLD4DUPd16, VLD4DUPd16_UPD, VLD4DUPd32, VLD4DUPd32_UPD, VLD4DUPd8, VLD...
    SStream_concat0(O, "[], "); 
    printOperand(MI, 3, O); 
    SStream_concat0(O, "[]}, "); 
    break;
  case 34:
    // VLD4LNd16_UPD, VLD4LNd32_UPD, VLD4LNd8_UPD, VLD4LNq16_UPD, VLD4LNq32_U...
    printNoHashImmediate(MI, 12, O); 
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    printOperand(MI, 1, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 12, O); 
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    printOperand(MI, 2, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 12, O); 
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    printOperand(MI, 3, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 12, O); 
    SStream_concat0(O, "]}, "); 
	set_mem_access(MI, false);
    printAddrMode6Operand(MI, 5, O); 
    printAddrMode6OffsetOperand(MI, 7, O); 
    return;
    break;
  case 35:
    // VLDRD, VLDRS, VSTRD, VSTRS
    printAddrMode5Operand(MI, 1, O, false); 
    return;
    break;
  case 36:
    // VST1LNd16, VST1LNd32, VST1LNd8
    printNoHashImmediate(MI, 3, O); 
    SStream_concat0(O, "]}, "); 
	set_mem_access(MI, false);
    printAddrMode6Operand(MI, 0, O); 
    return;
    break;
  case 37:
    // VST1LNd16_UPD, VST1LNd32_UPD, VST1LNd8_UPD, VST3LNd16, VST3LNd32, VST3...
    printNoHashImmediate(MI, 5, O); 
    break;
  case 38:
    // VST3LNd16_UPD, VST3LNd32_UPD, VST3LNd8_UPD, VST3LNq16_UPD, VST3LNq32_U...
    printNoHashImmediate(MI, 7, O); 
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    printOperand(MI, 5, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 7, O); 
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    printOperand(MI, 6, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 7, O); 
    SStream_concat0(O, "]}, "); 
	set_mem_access(MI, false);
    printAddrMode6Operand(MI, 1, O); 
    printAddrMode6OffsetOperand(MI, 3, O); 
    return;
    break;
  case 39:
    // VST3d16_UPD, VST3d32_UPD, VST3d8_UPD, VST3q16_UPD, VST3q32_UPD, VST3q8...
    printOperand(MI, 5, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 6, O); 
    break;
  case 40:
    // VTBL1
    printVectorListOne(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    return;
    break;
  case 41:
    // VTBL2
    printVectorListTwo(MI, 1, O, MRI); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    return;
    break;
  case 42:
    // VTBL3
    printVectorListThree(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    return;
    break;
  case 43:
    // VTBL4
    printVectorListFour(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    return;
    break;
  case 44:
    // VTBX1
    printVectorListOne(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 3, O); 
    return;
    break;
  case 45:
    // VTBX2
    printVectorListTwo(MI, 2, O, MRI); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 3, O); 
    return;
    break;
  case 46:
    // VTBX3
    printVectorListThree(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 3, O); 
    return;
    break;
  case 47:
    // VTBX4
    printVectorListFour(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 3, O); 
    return;
    break;
  case 48:
    // sysLDMDA_UPD, sysLDMDB_UPD, sysLDMIA_UPD, sysLDMIB_UPD, sysSTMDA_UPD, ...
    SStream_concat0(O, " ^"); 
	ARM_addUserMode(MI);
    return;
    break;
  case 49:
    // t2CMNzrs, t2CMPrs, t2MOVSsi, t2MOVsi, t2MVNs, t2TEQrs, t2TSTrs
    printT2SOOperand(MI, 1, O); 
    return;
    break;
  case 50:
    // t2LDRBT, t2LDRBi8, t2LDRHT, t2LDRHi8, t2LDRSBT, t2LDRSBi8, t2LDRSHT, t...
    printT2AddrModeImm8Operand(MI, 1, O, false); 
    return;
    break;
  case 51:
    // t2LDRB_PRE, t2LDRH_PRE, t2LDRSB_PRE, t2LDRSH_PRE, t2LDR_PRE, t2STRB_PR...
    printT2AddrModeImm8Operand(MI, 2, O, true); 
    SStream_concat0(O, "!"); 
    return;
    break;
  case 52:
    // t2LDRBpci, t2LDRHpci, t2LDRSBpci, t2LDRSHpci, t2LDRpci, tLDRpci
    printThumbLdrLabelOperand(MI, 1, O); 
    return;
    break;
  case 53:
    // t2LDRBs, t2LDRHs, t2LDRSBs, t2LDRSHs, t2LDRs, t2STRBs, t2STRHs, t2STRs
    printT2AddrModeSoRegOperand(MI, 1, O); 
    return;
    break;
  case 54:
    // t2LDREX
    printT2AddrModeImm0_1020s4Operand(MI, 1, O); 
    return;
    break;
  case 55:
    // t2MRS_M
    printMSRMaskOperand(MI, 1, O); 
    return;
    break;
  case 56:
    // tADDspi, tSUBspi
    printThumbS4ImmOperand(MI, 2, O); 
    return;
    break;
  case 57:
    // tADR
    printAdrLabelOperand(MI, 1, O, 2); 
    return;
    break;
  case 58:
    // tASRri, tLSRri
    printThumbSRImm(MI, 3, O); 
    return;
    break;
  case 59:
    // tLDRBi, tSTRBi
    printThumbAddrModeImm5S1Operand(MI, 1, O); 
    return;
    break;
  case 60:
    // tLDRBr, tLDRHr, tLDRSB, tLDRSH, tLDRr, tSTRBr, tSTRHr, tSTRr
    printThumbAddrModeRROperand(MI, 1, O); 
    return;
    break;
  case 61:
    // tLDRHi, tSTRHi
    printThumbAddrModeImm5S2Operand(MI, 1, O); 
    return;
    break;
  case 62:
    // tLDRi, tSTRi
    printThumbAddrModeImm5S4Operand(MI, 1, O); 
    return;
    break;
  case 63:
    // tLDRspi, tSTRspi
    printThumbAddrModeSPOperand(MI, 1, O); 
    return;
    break;
  }


  // Fragment 5 encoded into 5 bits for 23 unique commands.
  //printf("Frag-5: %"PRIu64"\n", (Bits >> 41) & 31);
  switch ((Bits >> 41) & 31) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // ADCri, ADCrr, ADCrsi, ADDri, ADDrr, ADDrsi, ANDri, ANDrr, ANDrsi, ASRi...
    SStream_concat0(O, ", "); 
    break;
  case 1:
    // CDP, t2CDP, t2CDP2
    printCImmediate(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printCImmediate(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printCImmediate(MI, 4, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 5, O); 
    return;
    break;
  case 2:
    // CLZ, CMNzrr, CMPrr, CPS3p, CRC32B, CRC32CB, CRC32CH, CRC32CW, CRC32H, ...
    return;
    break;
  case 3:
    // MCR, MCRR, MRRC, VABDfd, VABDfq, VACGEd, VACGEq, VACGTd, VACGTq, VADDD...
    printOperand(MI, 2, O); 
    break;
  case 4:
    // SSAT, t2SSAT
    printShiftImmOperand(MI, 3, O); 
    return;
    break;
  case 5:
    // SXTB, SXTB16, SXTH, UXTB, UXTB16, UXTH, t2SXTB, t2SXTB16, t2SXTH, t2UX...
    printRotImmOperand(MI, 2, O); 
    return;
    break;
  case 6:
    // VDUPLN16d, VDUPLN16q, VDUPLN32d, VDUPLN32q, VDUPLN8d, VDUPLN8q, VGETLN...
    printVectorIndex(MI, 2, O); 
    return;
    break;
  case 7:
    // VFMAD, VFMAS, VFMAfd, VFMAfq, VFMSD, VFMSS, VFMSfd, VFMSfq, VFNMAD, VF...
    printOperand(MI, 3, O); 
    break;
  case 8:
    // VLD1DUPd16wb_register, VLD1DUPd32wb_register, VLD1DUPd8wb_register, VL...
    printOperand(MI, 4, O); 
    return;
    break;
  case 9:
    // VLD1LNd16, VLD1LNd16_UPD, VLD1LNd32, VLD1LNd32_UPD, VLD1LNd8, VLD1LNd8...
    SStream_concat0(O, "]}, "); 
	set_mem_access(MI, false);
    break;
  case 10:
    // VLD1LNdWB_fixed_Asm_16, VLD1LNdWB_fixed_Asm_32, VLD1LNdWB_fixed_Asm_8,...
    SStream_concat0(O, "!"); 
    return;
    break;
  case 11:
    // VLD2LNd16, VLD2LNd32, VLD2LNd8, VLD2LNq16, VLD2LNq32, VLD4LNd16, VLD4L...
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    break;
  case 12:
    // VLD2LNd16_UPD, VLD2LNd32_UPD, VLD2LNd8_UPD, VLD2LNq16_UPD, VLD2LNq32_U...
    printOperand(MI, 1, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 8, O); 
    break;
  case 13:
    // VLD3DUPd16, VLD3DUPd32, VLD3DUPd8, VLD3DUPq16, VLD3DUPq32, VLD3DUPq8
    printAddrMode6Operand(MI, 3, O); 
    return;
    break;
  case 14:
    // VLD3DUPd16_UPD, VLD3DUPd32_UPD, VLD3DUPd8_UPD, VLD3DUPq16_UPD, VLD3DUP...
    printAddrMode6Operand(MI, 4, O); 
    break;
  case 15:
    // VLD4DUPd16_UPD, VLD4DUPd32_UPD, VLD4DUPd8_UPD, VLD4DUPq16_UPD, VLD4DUP...
    printAddrMode6Operand(MI, 5, O); 
    printAddrMode6OffsetOperand(MI, 7, O); 
    return;
    break;
  case 16:
    // VMLALslsv2i32, VMLALslsv4i16, VMLALsluv2i32, VMLALsluv4i16, VMLAslv2i3...
    printVectorIndex(MI, 4, O); 
    return;
    break;
  case 17:
    // VMULLslsv2i32, VMULLslsv4i16, VMULLsluv2i32, VMULLsluv4i16, VMULslv2i3...
    printVectorIndex(MI, 3, O); 
    return;
    break;
  case 18:
    // VST3d16_UPD, VST3d32_UPD, VST3d8_UPD, VST3q16_UPD, VST3q32_UPD, VST3q8...
    SStream_concat0(O, "}, "); 
    printAddrMode6Operand(MI, 1, O); 
    printAddrMode6OffsetOperand(MI, 3, O); 
    return;
    break;
  case 19:
    // VST4LNd16_UPD, VST4LNd32_UPD, VST4LNd8_UPD, VST4LNq16_UPD, VST4LNq32_U...
    printOperand(MI, 5, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 8, O); 
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    printOperand(MI, 6, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 8, O); 
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    printOperand(MI, 7, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 8, O); 
    SStream_concat0(O, "]}, "); 
	set_mem_access(MI, false);
    printAddrMode6Operand(MI, 1, O); 
    printAddrMode6OffsetOperand(MI, 3, O); 
    return;
    break;
  case 20:
    // sysLDMDA, sysLDMDB, sysLDMIA, sysLDMIB, sysSTMDA, sysSTMDB, sysSTMIA, ...
    SStream_concat0(O, " ^"); 
	ARM_addUserMode(MI);
    return;
    break;
  case 21:
    // t2LDRB_POST, t2LDRH_POST, t2LDRSB_POST, t2LDRSH_POST, t2LDR_POST, t2ST...
    printT2AddrModeImm8OffsetOperand(MI, 3, O); 
    return;
    break;
  case 22:
    // t2MOVsra_flag, t2MOVsrl_flag
    SStream_concat0(O, ", #1"); 
	op_addImm(MI, 1);
    return;
    break;
  }


  // Fragment 6 encoded into 6 bits for 36 unique commands.
  //printf("Frag-6: %"PRIu64"\n", (Bits >> 46) & 63);
  switch ((Bits >> 46) & 63) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // ADCri, ADDri, ANDri, BICri, EORri, ORRri, RSBri, RSCri, SBCri, SUBri
    printModImmOperand(MI, 2, O); 
    return;
    break;
  case 1:
    // ADCrr, ADDrr, ANDrr, ASRi, ASRr, BICrr, EORrr, LSLi, LSLr, LSRi, LSRr,...
    printOperand(MI, 2, O); 
    break;
  case 2:
    // ADCrsi, ADDrsi, ANDrsi, BICrsi, EORrsi, ORRrsi, RSBrsi, RSCrsi, SBCrsi...
    printSORegImmOperand(MI, 2, O); 
    return;
    break;
  case 3:
    // BFI, t2BFI
    printBitfieldInvMaskImmOperand(MI, 3, O); 
    return;
    break;
  case 4:
    // LDCL_OPTION, LDC_OPTION, STCL_OPTION, STC_OPTION, t2LDC2L_OPTION, t2LD...
    printCoprocOptionImm(MI, 3, O); 
    return;
    break;
  case 5:
    // LDCL_POST, LDC_POST, STCL_POST, STC_POST, t2LDC2L_POST, t2LDC2_POST, t...
    printPostIdxImm8s4Operand(MI, 3, O); 
    return;
    break;
  case 6:
    // LDRBT_POST_IMM, LDRBT_POST_REG, LDRB_POST_IMM, LDRB_POST_REG, LDRT_POS...
    printAddrMode2OffsetOperand(MI, 3, O); 
    return;
    break;
  case 7:
    // LDRD, STRD
    printAddrMode3Operand(MI, 2, O, false); 
    return;
    break;
  case 8:
    // LDRD_POST, STRD_POST, t2LDRD_POST, t2STRD_POST
    printAddrMode7Operand(MI, 3, O); 
    break;
  case 9:
    // LDRD_PRE, STRD_PRE
    printAddrMode3Operand(MI, 3, O, true); 
    SStream_concat0(O, "!"); 
    return;
    break;
  case 10:
    // LDRHTi, LDRSBTi, LDRSHTi, STRHTi
    printPostIdxImm8Operand(MI, 3, O); 
    return;
    break;
  case 11:
    // LDRHTr, LDRSBTr, LDRSHTr, STRHTr
    printPostIdxRegOperand(MI, 3, O); 
    return;
    break;
  case 12:
    // LDRH_POST, LDRSB_POST, LDRSH_POST, STRH_POST
    printAddrMode3OffsetOperand(MI, 3, O); 
    return;
    break;
  case 13:
    // MCR, MCRR, MRRC, t2MCR, t2MCR2, t2MCRR, t2MCRR2, t2MRRC, t2MRRC2
    SStream_concat0(O, ", "); 
    break;
  case 14:
    // MCRR2, MRRC2
    printCImmediate(MI, 4, O); 
    return;
    break;
  case 15:
    // STLEX, STLEXB, STLEXH, STREX, STREXB, STREXH, SWP, SWPB, t2LDAEXD, t2L...
    printAddrMode7Operand(MI, 2, O); 
    return;
    break;
  case 16:
    // VABDfd, VABDfq, VACGEd, VACGEq, VACGTd, VACGTq, VADDD, VADDS, VADDfd, ...
    return;
    break;
  case 17:
    // VBIFd, VBIFq, VBITd, VBITq, VBSLd, VBSLq, VLD4LNd16, VLD4LNd32, VLD4LN...
    printOperand(MI, 3, O); 
    break;
  case 18:
    // VLD1LNd16, VLD1LNd32, VLD1LNd8, VST1LNd16_UPD, VST1LNd32_UPD, VST1LNd8...
    printAddrMode6Operand(MI, 1, O); 
    break;
  case 19:
    // VLD1LNd16_UPD, VLD1LNd32_UPD, VLD1LNd8_UPD
    printAddrMode6Operand(MI, 2, O); 
    printAddrMode6OffsetOperand(MI, 4, O); 
    return;
    break;
  case 20:
    // VLD1LNdWB_register_Asm_16, VLD1LNdWB_register_Asm_32, VLD1LNdWB_regist...
    printOperand(MI, 4, O); 
    break;
  case 21:
    // VLD2LNd16, VLD2LNd32, VLD2LNd8, VLD2LNq16, VLD2LNq32
    printOperand(MI, 1, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 6, O); 
    SStream_concat0(O, "]}, "); 
	set_mem_access(MI, false);
    printAddrMode6Operand(MI, 2, O); 
    return;
    break;
  case 22:
    // VLD2LNd16_UPD, VLD2LNd32_UPD, VLD2LNd8_UPD, VLD2LNq16_UPD, VLD2LNq32_U...
    SStream_concat0(O, "]}, "); 
	set_mem_access(MI, false);
    printAddrMode6Operand(MI, 3, O); 
    printAddrMode6OffsetOperand(MI, 5, O); 
    return;
    break;
  case 23:
    // VLD3DUPd16_UPD, VLD3DUPd32_UPD, VLD3DUPd8_UPD, VLD3DUPq16_UPD, VLD3DUP...
    printAddrMode6OffsetOperand(MI, 6, O); 
    return;
    break;
  case 24:
    // VLD3LNd16, VLD3LNd32, VLD3LNd8, VLD3LNq16, VLD3LNq32
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    printOperand(MI, 2, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 8, O); 
    SStream_concat0(O, "]}, "); 
	set_mem_access(MI, false);
    printAddrMode6Operand(MI, 3, O); 
    return;
    break;
  case 25:
    // VLD3LNd16_UPD, VLD3LNd32_UPD, VLD3LNd8_UPD, VLD3LNq16_UPD, VLD3LNq32_U...
    printAddrMode6Operand(MI, 4, O); 
    printAddrMode6OffsetOperand(MI, 6, O); 
    return;
    break;
  case 26:
    // VMLAslfd, VMLAslfq, VMLSslfd, VMLSslfq
    printVectorIndex(MI, 4, O); 
    return;
    break;
  case 27:
    // VMULslfd, VMULslfq
    printVectorIndex(MI, 3, O); 
    return;
    break;
  case 28:
    // VST2LNd16_UPD, VST2LNd32_UPD, VST2LNd8_UPD, VST2LNq16_UPD, VST2LNq32_U...
    printOperand(MI, 5, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 6, O); 
    SStream_concat0(O, "]}, "); 
	set_mem_access(MI, false);
    printAddrMode6Operand(MI, 1, O); 
    printAddrMode6OffsetOperand(MI, 3, O); 
    return;
    break;
  case 29:
    // VST4d16_UPD, VST4d32_UPD, VST4d8_UPD, VST4q16_UPD, VST4q32_UPD, VST4q8...
    printOperand(MI, 7, O); 
    SStream_concat0(O, "}, "); 
    printAddrMode6Operand(MI, 1, O); 
    printAddrMode6OffsetOperand(MI, 3, O); 
    return;
    break;
  case 30:
    // t2ADCrs, t2ADDrs, t2ANDrs, t2BICrs, t2EORrs, t2ORNrs, t2ORRrs, t2RSBrs...
    printT2SOOperand(MI, 2, O); 
    return;
    break;
  case 31:
    // t2ASRri, t2LSRri
    printThumbSRImm(MI, 2, O); 
    return;
    break;
  case 32:
    // t2LDRD_PRE, t2STRD_PRE
    printT2AddrModeImm8s4Operand(MI, 3, O, true); 
    SStream_concat0(O, "!"); 
    return;
    break;
  case 33:
    // t2LDRDi8, t2STRDi8
    printT2AddrModeImm8s4Operand(MI, 2, O, false); 
    return;
    break;
  case 34:
    // t2STREX
    printT2AddrModeImm0_1020s4Operand(MI, 2, O); 
    return;
    break;
  case 35:
    // tADDrSPi
    printThumbS4ImmOperand(MI, 2, O); 
    return;
    break;
  }


  // Fragment 7 encoded into 4 bits for 12 unique commands.
  //printf("Frag-7: %"PRIu64"\n", (Bits >> 52) & 15);
  switch ((Bits >> 52) & 15) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // ADCrr, ADDrr, ANDrr, ASRi, ASRr, BICrr, EORrr, LSLi, LSLr, LSRi, LSRr,...
    return;
    break;
  case 1:
    // LDRD_POST, MLA, MLS, SBFX, SMLABB, SMLABT, SMLAD, SMLADX, SMLALBB, SML...
    SStream_concat0(O, ", "); 
    break;
  case 2:
    // MCR, t2MCR, t2MCR2
    printCImmediate(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printCImmediate(MI, 4, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 5, O); 
    return;
    break;
  case 3:
    // MCRR, MRRC, t2MCRR, t2MCRR2, t2MRRC, t2MRRC2
    printOperand(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printCImmediate(MI, 4, O); 
    return;
    break;
  case 4:
    // PKHBT, t2PKHBT
    printPKHLSLShiftImm(MI, 3, O); 
    return;
    break;
  case 5:
    // PKHTB, t2PKHTB
    printPKHASRShiftImm(MI, 3, O); 
    return;
    break;
  case 6:
    // SXTAB, SXTAB16, SXTAH, UXTAB, UXTAB16, UXTAH, t2SXTAB, t2SXTAB16, t2SX...
    printRotImmOperand(MI, 3, O); 
    return;
    break;
  case 7:
    // USAT, t2USAT
    printShiftImmOperand(MI, 3, O); 
    return;
    break;
  case 8:
    // VLD3d16, VLD3d16_UPD, VLD3d32, VLD3d32_UPD, VLD3d8, VLD3d8_UPD, VLD3q1...
    SStream_concat0(O, "}, "); 
    break;
  case 9:
    // VLD4LNd16, VLD4LNd32, VLD4LNd8, VLD4LNq16, VLD4LNq32, VST2LNd16, VST2L...
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    break;
  case 10:
    // VST1LNd16_UPD, VST1LNd32_UPD, VST1LNd8_UPD
    printAddrMode6OffsetOperand(MI, 3, O); 
    return;
    break;
  case 11:
    // t2LDRD_POST, t2STRD_POST
    printT2AddrModeImm8s4OffsetOperand(MI, 4, O); 
    return;
    break;
  }


  // Fragment 8 encoded into 4 bits for 13 unique commands.
  //printf("Frag-8: %"PRIu64"\n", (Bits >> 56) & 15);
  switch ((Bits >> 56) & 15) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // LDRD_POST, STRD_POST
    printAddrMode3OffsetOperand(MI, 4, O); 
    return;
    break;
  case 1:
    // MLA, MLS, SMLABB, SMLABT, SMLAD, SMLADX, SMLALBB, SMLALBT, SMLALD, SML...
    printOperand(MI, 3, O); 
    break;
  case 2:
    // SBFX, UBFX, t2SBFX, t2UBFX
    printImmPlusOneOperand(MI, 3, O); 
    return;
    break;
  case 3:
    // VLD3d16, VLD3d32, VLD3d8, VLD3q16, VLD3q32, VLD3q8
    printAddrMode6Operand(MI, 3, O); 
    return;
    break;
  case 4:
    // VLD3d16_UPD, VLD3d32_UPD, VLD3d8_UPD, VLD3q16_UPD, VLD3q32_UPD, VLD3q8...
    printAddrMode6Operand(MI, 4, O); 
    printAddrMode6OffsetOperand(MI, 6, O); 
    return;
    break;
  case 5:
    // VLD4LNd16, VLD4LNd32, VLD4LNd8, VLD4LNq16, VLD4LNq32
    printNoHashImmediate(MI, 10, O); 
    SStream_concat0(O, "]}, "); 
	set_mem_access(MI, false);
    printAddrMode6Operand(MI, 4, O); 
    return;
    break;
  case 6:
    // VST2LNd16, VST2LNd32, VST2LNd8, VST2LNq16, VST2LNq32
    printNoHashImmediate(MI, 4, O); 
    SStream_concat0(O, "]}, "); 
	set_mem_access(MI, false);
    printAddrMode6Operand(MI, 0, O); 
    return;
    break;
  case 7:
    // VST3LNd16, VST3LNd32, VST3LNd8, VST3LNq16, VST3LNq32
    printNoHashImmediate(MI, 5, O); 
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    printOperand(MI, 4, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 5, O); 
    SStream_concat0(O, "]}, "); 
	set_mem_access(MI, false);
    printAddrMode6Operand(MI, 0, O); 
    return;
    break;
  case 8:
    // VST3d16, VST3d32, VST3d8, VST3q16, VST3q32, VST3q8
    printAddrMode6Operand(MI, 0, O); 
    return;
    break;
  case 9:
    // VST4LNd16, VST4LNd32, VST4LNd8, VST4LNq16, VST4LNq32
    printNoHashImmediate(MI, 6, O); 
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    printOperand(MI, 4, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 6, O); 
    SStream_concat0(O, "], "); 
	set_mem_access(MI, false);
    printOperand(MI, 5, O); 
    SStream_concat0(O, "["); 
	set_mem_access(MI, true);
    printNoHashImmediate(MI, 6, O); 
    SStream_concat0(O, "]}, "); 
	set_mem_access(MI, false);
    printAddrMode6Operand(MI, 0, O); 
    return;
    break;
  case 10:
    // VST4d16, VST4d32, VST4d8, VST4q16, VST4q32, VST4q8
    printOperand(MI, 5, O); 
    SStream_concat0(O, "}, "); 
    printAddrMode6Operand(MI, 0, O); 
    return;
    break;
  case 11:
    // t2SMLSLDX
    printOperand(MI, 2, O); 
    return;
    break;
  case 12:
    // t2STLEXD, t2STREXD
    printAddrMode7Operand(MI, 3, O); 
    return;
    break;
  }


  // Fragment 9 encoded into 1 bits for 2 unique commands.
  //printf("Frag-9: %"PRIu64"\n", (Bits >> 60) & 1);
  if ((Bits >> 60) & 1) {
    // VLD4d16, VLD4d16_UPD, VLD4d32, VLD4d32_UPD, VLD4d8, VLD4d8_UPD, VLD4q1...
    SStream_concat0(O, "}, "); 
  } else {
    // MLA, MLS, SMLABB, SMLABT, SMLAD, SMLADX, SMLALBB, SMLALBT, SMLALD, SML...
    return;
  }


  // Fragment 10 encoded into 1 bits for 2 unique commands.
  //printf("Frag-10: %"PRIu64"\n", (Bits >> 61) & 1);
  if ((Bits >> 61) & 1) {
    // VLD4d16_UPD, VLD4d32_UPD, VLD4d8_UPD, VLD4q16_UPD, VLD4q32_UPD, VLD4q8...
    printAddrMode6Operand(MI, 5, O); 
    printAddrMode6OffsetOperand(MI, 7, O); 
    return;
  } else {
    // VLD4d16, VLD4d32, VLD4d8, VLD4q16, VLD4q32, VLD4q8
    printAddrMode6Operand(MI, 4, O); 
    return;
  }
}


/// getRegisterName - This method is automatically generated by tblgen
/// from the register set description.  This returns the assembler name
/// for the specified register.
static const char *getRegisterName(unsigned RegNo)
{
  // assert(RegNo && RegNo < 289 && "Invalid register number!");

#ifndef CAPSTONE_DIET
  static const char AsmStrs[] = {
  /* 0 */ 'D', '4', '_', 'D', '6', '_', 'D', '8', '_', 'D', '1', '0', 0,
  /* 13 */ 'D', '7', '_', 'D', '8', '_', 'D', '9', '_', 'D', '1', '0', 0,
  /* 26 */ 'Q', '7', '_', 'Q', '8', '_', 'Q', '9', '_', 'Q', '1', '0', 0,
  /* 39 */ 'd', '1', '0', 0,
  /* 43 */ 'q', '1', '0', 0,
  /* 47 */ 's', '1', '0', 0,
  /* 51 */ 'D', '1', '4', '_', 'D', '1', '6', '_', 'D', '1', '8', '_', 'D', '2', '0', 0,
  /* 67 */ 'D', '1', '7', '_', 'D', '1', '8', '_', 'D', '1', '9', '_', 'D', '2', '0', 0,
  /* 83 */ 'd', '2', '0', 0,
  /* 87 */ 's', '2', '0', 0,
  /* 91 */ 'D', '2', '4', '_', 'D', '2', '6', '_', 'D', '2', '8', '_', 'D', '3', '0', 0,
  /* 107 */ 'D', '2', '7', '_', 'D', '2', '8', '_', 'D', '2', '9', '_', 'D', '3', '0', 0,
  /* 123 */ 'd', '3', '0', 0,
  /* 127 */ 's', '3', '0', 0,
  /* 131 */ 'd', '0', 0,
  /* 134 */ 'q', '0', 0,
  /* 137 */ 'm', 'v', 'f', 'r', '0', 0,
  /* 143 */ 's', '0', 0,
  /* 146 */ 'D', '9', '_', 'D', '1', '0', '_', 'D', '1', '1', 0,
  /* 157 */ 'D', '5', '_', 'D', '7', '_', 'D', '9', '_', 'D', '1', '1', 0,
  /* 170 */ 'Q', '8', '_', 'Q', '9', '_', 'Q', '1', '0', '_', 'Q', '1', '1', 0,
  /* 184 */ 'R', '1', '0', '_', 'R', '1', '1', 0,
  /* 192 */ 'd', '1', '1', 0,
  /* 196 */ 'q', '1', '1', 0,
  /* 200 */ 's', '1', '1', 0,
  /* 204 */ 'D', '1', '9', '_', 'D', '2', '0', '_', 'D', '2', '1', 0,
  /* 216 */ 'D', '1', '5', '_', 'D', '1', '7', '_', 'D', '1', '9', '_', 'D', '2', '1', 0,
  /* 232 */ 'd', '2', '1', 0,
  /* 236 */ 's', '2', '1', 0,
  /* 240 */ 'D', '2', '9', '_', 'D', '3', '0', '_', 'D', '3', '1', 0,
  /* 252 */ 'D', '2', '5', '_', 'D', '2', '7', '_', 'D', '2', '9', '_', 'D', '3', '1', 0,
  /* 268 */ 'd', '3', '1', 0,
  /* 272 */ 's', '3', '1', 0,
  /* 276 */ 'Q', '0', '_', 'Q', '1', 0,
  /* 282 */ 'R', '0', '_', 'R', '1', 0,
  /* 288 */ 'd', '1', 0,
  /* 291 */ 'q', '1', 0,
  /* 294 */ 'm', 'v', 'f', 'r', '1', 0,
  /* 300 */ 's', '1', 0,
  /* 303 */ 'D', '6', '_', 'D', '8', '_', 'D', '1', '0', '_', 'D', '1', '2', 0,
  /* 317 */ 'D', '9', '_', 'D', '1', '0', '_', 'D', '1', '1', '_', 'D', '1', '2', 0,
  /* 332 */ 'Q', '9', '_', 'Q', '1', '0', '_', 'Q', '1', '1', '_', 'Q', '1', '2', 0,
  /* 347 */ 'd', '1', '2', 0,
  /* 351 */ 'q', '1', '2', 0,
  /* 355 */ 's', '1', '2', 0,
  /* 359 */ 'D', '1', '6', '_', 'D', '1', '8', '_', 'D', '2', '0', '_', 'D', '2', '2', 0,
  /* 375 */ 'D', '1', '9', '_', 'D', '2', '0', '_', 'D', '2', '1', '_', 'D', '2', '2', 0,
  /* 391 */ 'd', '2', '2', 0,
  /* 395 */ 's', '2', '2', 0,
  /* 399 */ 'D', '0', '_', 'D', '2', 0,
  /* 405 */ 'D', '0', '_', 'D', '1', '_', 'D', '2', 0,
  /* 414 */ 'Q', '1', '_', 'Q', '2', 0,
  /* 420 */ 'd', '2', 0,
  /* 423 */ 'q', '2', 0,
  /* 426 */ 'm', 'v', 'f', 'r', '2', 0,
  /* 432 */ 's', '2', 0,
  /* 435 */ 'f', 'p', 'i', 'n', 's', 't', '2', 0,
  /* 443 */ 'D', '7', '_', 'D', '9', '_', 'D', '1', '1', '_', 'D', '1', '3', 0,
  /* 457 */ 'D', '1', '1', '_', 'D', '1', '2', '_', 'D', '1', '3', 0,
  /* 469 */ 'Q', '1', '0', '_', 'Q', '1', '1', '_', 'Q', '1', '2', '_', 'Q', '1', '3', 0,
  /* 485 */ 'd', '1', '3', 0,
  /* 489 */ 'q', '1', '3', 0,
  /* 493 */ 's', '1', '3', 0,
  /* 497 */ 'D', '1', '7', '_', 'D', '1', '9', '_', 'D', '2', '1', '_', 'D', '2', '3', 0,
  /* 513 */ 'D', '2', '1', '_', 'D', '2', '2', '_', 'D', '2', '3', 0,
  /* 525 */ 'd', '2', '3', 0,
  /* 529 */ 's', '2', '3', 0,
  /* 533 */ 'D', '1', '_', 'D', '3', 0,
  /* 539 */ 'D', '1', '_', 'D', '2', '_', 'D', '3', 0,
  /* 548 */ 'Q', '0', '_', 'Q', '1', '_', 'Q', '2', '_', 'Q', '3', 0,
  /* 560 */ 'R', '2', '_', 'R', '3', 0,
  /* 566 */ 'd', '3', 0,
  /* 569 */ 'q', '3', 0,
  /* 572 */ 'r', '3', 0,
  /* 575 */ 's', '3', 0,
  /* 578 */ 'D', '8', '_', 'D', '1', '0', '_', 'D', '1', '2', '_', 'D', '1', '4', 0,
  /* 593 */ 'D', '1', '1', '_', 'D', '1', '2', '_', 'D', '1', '3', '_', 'D', '1', '4', 0,
  /* 609 */ 'Q', '1', '1', '_', 'Q', '1', '2', '_', 'Q', '1', '3', '_', 'Q', '1', '4', 0,
  /* 625 */ 'd', '1', '4', 0,
  /* 629 */ 'q', '1', '4', 0,
  /* 633 */ 's', '1', '4', 0,
  /* 637 */ 'D', '1', '8', '_', 'D', '2', '0', '_', 'D', '2', '2', '_', 'D', '2', '4', 0,
  /* 653 */ 'D', '2', '1', '_', 'D', '2', '2', '_', 'D', '2', '3', '_', 'D', '2', '4', 0,
  /* 669 */ 'd', '2', '4', 0,
  /* 673 */ 's', '2', '4', 0,
  /* 677 */ 'D', '0', '_', 'D', '2', '_', 'D', '4', 0,
  /* 686 */ 'D', '1', '_', 'D', '2', '_', 'D', '3', '_', 'D', '4', 0,
  /* 698 */ 'Q', '1', '_', 'Q', '2', '_', 'Q', '3', '_', 'Q', '4', 0,
  /* 710 */ 'd', '4', 0,
  /* 713 */ 'q', '4', 0,
  /* 716 */ 'r', '4', 0,
  /* 719 */ 's', '4', 0,
  /* 722 */ 'D', '9', '_', 'D', '1', '1', '_', 'D', '1', '3', '_', 'D', '1', '5', 0,
  /* 737 */ 'D', '1', '3', '_', 'D', '1', '4', '_', 'D', '1', '5', 0,
  /* 749 */ 'Q', '1', '2', '_', 'Q', '1', '3', '_', 'Q', '1', '4', '_', 'Q', '1', '5', 0,
  /* 765 */ 'd', '1', '5', 0,
  /* 769 */ 'q', '1', '5', 0,
  /* 773 */ 's', '1', '5', 0,
  /* 777 */ 'D', '1', '9', '_', 'D', '2', '1', '_', 'D', '2', '3', '_', 'D', '2', '5', 0,
  /* 793 */ 'D', '2', '3', '_', 'D', '2', '4', '_', 'D', '2', '5', 0,
  /* 805 */ 'd', '2', '5', 0,
  /* 809 */ 's', '2', '5', 0,
  /* 813 */ 'D', '1', '_', 'D', '3', '_', 'D', '5', 0,
  /* 822 */ 'D', '3', '_', 'D', '4', '_', 'D', '5', 0,
  /* 831 */ 'Q', '2', '_', 'Q', '3', '_', 'Q', '4', '_', 'Q', '5', 0,
  /* 843 */ 'R', '4', '_', 'R', '5', 0,
  /* 849 */ 'd', '5', 0,
  /* 852 */ 'q', '5', 0,
  /* 855 */ 'r', '5', 0,
  /* 858 */ 's', '5', 0,
  /* 861 */ 'D', '1', '0', '_', 'D', '1', '2', '_', 'D', '1', '4', '_', 'D', '1', '6', 0,
  /* 877 */ 'D', '1', '3', '_', 'D', '1', '4', '_', 'D', '1', '5', '_', 'D', '1', '6', 0,
  /* 893 */ 'd', '1', '6', 0,
  /* 897 */ 's', '1', '6', 0,
  /* 901 */ 'D', '2', '0', '_', 'D', '2', '2', '_', 'D', '2', '4', '_', 'D', '2', '6', 0,
  /* 917 */ 'D', '2', '3', '_', 'D', '2', '4', '_', 'D', '2', '5', '_', 'D', '2', '6', 0,
  /* 933 */ 'd', '2', '6', 0,
  /* 937 */ 's', '2', '6', 0,
  /* 941 */ 'D', '0', '_', 'D', '2', '_', 'D', '4', '_', 'D', '6', 0,
  /* 953 */ 'D', '3', '_', 'D', '4', '_', 'D', '5', '_', 'D', '6', 0,
  /* 965 */ 'Q', '3', '_', 'Q', '4', '_', 'Q', '5', '_', 'Q', '6', 0,
  /* 977 */ 'd', '6', 0,
  /* 980 */ 'q', '6', 0,
  /* 983 */ 'r', '6', 0,
  /* 986 */ 's', '6', 0,
  /* 989 */ 'D', '1', '1', '_', 'D', '1', '3', '_', 'D', '1', '5', '_', 'D', '1', '7', 0,
  /* 1005 */ 'D', '1', '5', '_', 'D', '1', '6', '_', 'D', '1', '7', 0,
  /* 1017 */ 'd', '1', '7', 0,
  /* 1021 */ 's', '1', '7', 0,
  /* 1025 */ 'D', '2', '1', '_', 'D', '2', '3', '_', 'D', '2', '5', '_', 'D', '2', '7', 0,
  /* 1041 */ 'D', '2', '5', '_', 'D', '2', '6', '_', 'D', '2', '7', 0,
  /* 1053 */ 'd', '2', '7', 0,
  /* 1057 */ 's', '2', '7', 0,
  /* 1061 */ 'D', '1', '_', 'D', '3', '_', 'D', '5', '_', 'D', '7', 0,
  /* 1073 */ 'D', '5', '_', 'D', '6', '_', 'D', '7', 0,
  /* 1082 */ 'Q', '4', '_', 'Q', '5', '_', 'Q', '6', '_', 'Q', '7', 0,
  /* 1094 */ 'R', '6', '_', 'R', '7', 0,
  /* 1100 */ 'd', '7', 0,
  /* 1103 */ 'q', '7', 0,
  /* 1106 */ 'r', '7', 0,
  /* 1109 */ 's', '7', 0,
  /* 1112 */ 'D', '1', '2', '_', 'D', '1', '4', '_', 'D', '1', '6', '_', 'D', '1', '8', 0,
  /* 1128 */ 'D', '1', '5', '_', 'D', '1', '6', '_', 'D', '1', '7', '_', 'D', '1', '8', 0,
  /* 1144 */ 'd', '1', '8', 0,
  /* 1148 */ 's', '1', '8', 0,
  /* 1152 */ 'D', '2', '2', '_', 'D', '2', '4', '_', 'D', '2', '6', '_', 'D', '2', '8', 0,
  /* 1168 */ 'D', '2', '5', '_', 'D', '2', '6', '_', 'D', '2', '7', '_', 'D', '2', '8', 0,
  /* 1184 */ 'd', '2', '8', 0,
  /* 1188 */ 's', '2', '8', 0,
  /* 1192 */ 'D', '2', '_', 'D', '4', '_', 'D', '6', '_', 'D', '8', 0,
  /* 1204 */ 'D', '5', '_', 'D', '6', '_', 'D', '7', '_', 'D', '8', 0,
  /* 1216 */ 'Q', '5', '_', 'Q', '6', '_', 'Q', '7', '_', 'Q', '8', 0,
  /* 1228 */ 'd', '8', 0,
  /* 1231 */ 'q', '8', 0,
  /* 1234 */ 'r', '8', 0,
  /* 1237 */ 's', '8', 0,
  /* 1240 */ 'D', '1', '3', '_', 'D', '1', '5', '_', 'D', '1', '7', '_', 'D', '1', '9', 0,
  /* 1256 */ 'D', '1', '7', '_', 'D', '1', '8', '_', 'D', '1', '9', 0,
  /* 1268 */ 'd', '1', '9', 0,
  /* 1272 */ 's', '1', '9', 0,
  /* 1276 */ 'D', '2', '3', '_', 'D', '2', '5', '_', 'D', '2', '7', '_', 'D', '2', '9', 0,
  /* 1292 */ 'D', '2', '7', '_', 'D', '2', '8', '_', 'D', '2', '9', 0,
  /* 1304 */ 'd', '2', '9', 0,
  /* 1308 */ 's', '2', '9', 0,
  /* 1312 */ 'D', '3', '_', 'D', '5', '_', 'D', '7', '_', 'D', '9', 0,
  /* 1324 */ 'D', '7', '_', 'D', '8', '_', 'D', '9', 0,
  /* 1333 */ 'Q', '6', '_', 'Q', '7', '_', 'Q', '8', '_', 'Q', '9', 0,
  /* 1345 */ 'R', '8', '_', 'R', '9', 0,
  /* 1351 */ 'd', '9', 0,
  /* 1354 */ 'q', '9', 0,
  /* 1357 */ 's', '9', 0,
  /* 1360 */ 'R', '1', '2', '_', 'S', 'P', 0,
  /* 1367 */ 's', 'b', 0,
  /* 1370 */ 'p', 'c', 0,
  /* 1373 */ 'f', 'p', 'e', 'x', 'c', 0,
  /* 1379 */ 'f', 'p', 's', 'i', 'd', 0,
  /* 1385 */ 'i', 't', 's', 't', 'a', 't', 'e', 0,
  /* 1393 */ 's', 'l', 0,
  /* 1396 */ 'f', 'p', 0,
  /* 1399 */ 'i', 'p', 0,
  /* 1402 */ 's', 'p', 0,
  /* 1405 */ 'f', 'p', 's', 'c', 'r', 0,
  /* 1411 */ 'l', 'r', 0,
  /* 1414 */ 'a', 'p', 's', 'r', 0,
  /* 1419 */ 'c', 'p', 's', 'r', 0,
  /* 1424 */ 's', 'p', 's', 'r', 0,
  /* 1429 */ 'f', 'p', 'i', 'n', 's', 't', 0,
  /* 1436 */ 'f', 'p', 's', 'c', 'r', '_', 'n', 'z', 'c', 'v', 0,
  /* 1447 */ 'a', 'p', 's', 'r', '_', 'n', 'z', 'c', 'v', 0,
  };

  static const uint16_t RegAsmOffset[] = {
    1414, 1447, 1419, 1373, 1429, 1405, 1436, 1379, 1385, 1411, 1370, 1402, 1424, 131, 
    288, 420, 566, 710, 849, 977, 1100, 1228, 1351, 39, 192, 347, 485, 625, 
    765, 893, 1017, 1144, 1268, 83, 232, 391, 525, 669, 805, 933, 1053, 1184, 
    1304, 123, 268, 435, 137, 294, 426, 134, 291, 423, 569, 713, 852, 980, 
    1103, 1231, 1354, 43, 196, 351, 489, 629, 769, 140, 297, 429, 572, 716, 
    855, 983, 1106, 1234, 1367, 1393, 1396, 1399, 143, 300, 432, 575, 719, 858, 
    986, 1109, 1237, 1357, 47, 200, 355, 493, 633, 773, 897, 1021, 1148, 1272, 
    87, 236, 395, 529, 673, 809, 937, 1057, 1188, 1308, 127, 272, 399, 533, 
    680, 816, 947, 1067, 1198, 1318, 6, 163, 309, 449, 585, 729, 869, 997, 
    1120, 1248, 59, 224, 367, 505, 645, 785, 909, 1033, 1160, 1284, 99, 260, 
    276, 414, 554, 704, 837, 971, 1088, 1222, 1339, 32, 176, 339, 477, 617, 
    757, 548, 698, 831, 965, 1082, 1216, 1333, 26, 170, 332, 469, 609, 749, 
    1360, 282, 560, 843, 1094, 1345, 184, 405, 539, 689, 822, 956, 1073, 1207, 
    1324, 16, 146, 320, 457, 597, 737, 881, 1005, 1132, 1256, 71, 204, 379, 
    513, 657, 793, 921, 1041, 1172, 1292, 111, 240, 677, 813, 944, 1064, 1195, 
    1315, 3, 160, 306, 446, 581, 725, 865, 993, 1116, 1244, 55, 220, 363, 
    501, 641, 781, 905, 1029, 1156, 1280, 95, 256, 941, 1061, 1192, 1312, 0, 
    157, 303, 443, 578, 722, 861, 989, 1112, 1240, 51, 216, 359, 497, 637, 
    777, 901, 1025, 1152, 1276, 91, 252, 408, 692, 959, 1210, 19, 324, 601, 
    885, 1136, 75, 383, 661, 925, 1176, 115, 686, 953, 1204, 13, 317, 593, 
    877, 1128, 67, 375, 653, 917, 1168, 107, 
  };

  //int i;
  //for (i = 0; i < sizeof(RegAsmOffset)/2; i++)
  //     printf("%s = %u\n", AsmStrs+RegAsmOffset[i], i + 1);
  //printf("*************************\n");
  return AsmStrs+RegAsmOffset[RegNo-1];
#else
  return NULL;
#endif
}

// get registers with number only
static const char *getRegisterName2(unsigned RegNo)
{
  // assert(RegNo && RegNo < 289 && "Invalid register number!");

#ifndef CAPSTONE_DIET
  static const char AsmStrs[] = {
  /* 0 */ 'D', '4', '_', 'D', '6', '_', 'D', '8', '_', 'D', '1', '0', 0,
  /* 13 */ 'D', '7', '_', 'D', '8', '_', 'D', '9', '_', 'D', '1', '0', 0,
  /* 26 */ 'Q', '7', '_', 'Q', '8', '_', 'Q', '9', '_', 'Q', '1', '0', 0,
  /* 39 */ 'd', '1', '0', 0,
  /* 43 */ 'q', '1', '0', 0,
  /* 47 */ 'r', '1', '0', 0,
  /* 51 */ 's', '1', '0', 0,
  /* 55 */ 'D', '1', '4', '_', 'D', '1', '6', '_', 'D', '1', '8', '_', 'D', '2', '0', 0,
  /* 71 */ 'D', '1', '7', '_', 'D', '1', '8', '_', 'D', '1', '9', '_', 'D', '2', '0', 0,
  /* 87 */ 'd', '2', '0', 0,
  /* 91 */ 's', '2', '0', 0,
  /* 95 */ 'D', '2', '4', '_', 'D', '2', '6', '_', 'D', '2', '8', '_', 'D', '3', '0', 0,
  /* 111 */ 'D', '2', '7', '_', 'D', '2', '8', '_', 'D', '2', '9', '_', 'D', '3', '0', 0,
  /* 127 */ 'd', '3', '0', 0,
  /* 131 */ 's', '3', '0', 0,
  /* 135 */ 'd', '0', 0,
  /* 138 */ 'q', '0', 0,
  /* 141 */ 'm', 'v', 'f', 'r', '0', 0,
  /* 147 */ 's', '0', 0,
  /* 150 */ 'D', '9', '_', 'D', '1', '0', '_', 'D', '1', '1', 0,
  /* 161 */ 'D', '5', '_', 'D', '7', '_', 'D', '9', '_', 'D', '1', '1', 0,
  /* 174 */ 'Q', '8', '_', 'Q', '9', '_', 'Q', '1', '0', '_', 'Q', '1', '1', 0,
  /* 188 */ 'R', '1', '0', '_', 'R', '1', '1', 0,
  /* 196 */ 'd', '1', '1', 0,
  /* 200 */ 'q', '1', '1', 0,
  /* 204 */ 'r', '1', '1', 0,
  /* 208 */ 's', '1', '1', 0,
  /* 212 */ 'D', '1', '9', '_', 'D', '2', '0', '_', 'D', '2', '1', 0,
  /* 224 */ 'D', '1', '5', '_', 'D', '1', '7', '_', 'D', '1', '9', '_', 'D', '2', '1', 0,
  /* 240 */ 'd', '2', '1', 0,
  /* 244 */ 's', '2', '1', 0,
  /* 248 */ 'D', '2', '9', '_', 'D', '3', '0', '_', 'D', '3', '1', 0,
  /* 260 */ 'D', '2', '5', '_', 'D', '2', '7', '_', 'D', '2', '9', '_', 'D', '3', '1', 0,
  /* 276 */ 'd', '3', '1', 0,
  /* 280 */ 's', '3', '1', 0,
  /* 284 */ 'Q', '0', '_', 'Q', '1', 0,
  /* 290 */ 'R', '0', '_', 'R', '1', 0,
  /* 296 */ 'd', '1', 0,
  /* 299 */ 'q', '1', 0,
  /* 302 */ 'm', 'v', 'f', 'r', '1', 0,
  /* 308 */ 's', '1', 0,
  /* 311 */ 'D', '6', '_', 'D', '8', '_', 'D', '1', '0', '_', 'D', '1', '2', 0,
  /* 325 */ 'D', '9', '_', 'D', '1', '0', '_', 'D', '1', '1', '_', 'D', '1', '2', 0,
  /* 340 */ 'Q', '9', '_', 'Q', '1', '0', '_', 'Q', '1', '1', '_', 'Q', '1', '2', 0,
  /* 355 */ 'd', '1', '2', 0,
  /* 359 */ 'q', '1', '2', 0,
  /* 363 */ 'r', '1', '2', 0,
  /* 367 */ 's', '1', '2', 0,
  /* 371 */ 'D', '1', '6', '_', 'D', '1', '8', '_', 'D', '2', '0', '_', 'D', '2', '2', 0,
  /* 387 */ 'D', '1', '9', '_', 'D', '2', '0', '_', 'D', '2', '1', '_', 'D', '2', '2', 0,
  /* 403 */ 'd', '2', '2', 0,
  /* 407 */ 's', '2', '2', 0,
  /* 411 */ 'D', '0', '_', 'D', '2', 0,
  /* 417 */ 'D', '0', '_', 'D', '1', '_', 'D', '2', 0,
  /* 426 */ 'Q', '1', '_', 'Q', '2', 0,
  /* 432 */ 'd', '2', 0,
  /* 435 */ 'q', '2', 0,
  /* 438 */ 'm', 'v', 'f', 'r', '2', 0,
  /* 444 */ 's', '2', 0,
  /* 447 */ 'f', 'p', 'i', 'n', 's', 't', '2', 0,
  /* 455 */ 'D', '7', '_', 'D', '9', '_', 'D', '1', '1', '_', 'D', '1', '3', 0,
  /* 469 */ 'D', '1', '1', '_', 'D', '1', '2', '_', 'D', '1', '3', 0,
  /* 481 */ 'Q', '1', '0', '_', 'Q', '1', '1', '_', 'Q', '1', '2', '_', 'Q', '1', '3', 0,
  /* 497 */ 'd', '1', '3', 0,
  /* 501 */ 'q', '1', '3', 0,
  /* 505 */ 's', '1', '3', 0,
  /* 509 */ 'D', '1', '7', '_', 'D', '1', '9', '_', 'D', '2', '1', '_', 'D', '2', '3', 0,
  /* 525 */ 'D', '2', '1', '_', 'D', '2', '2', '_', 'D', '2', '3', 0,
  /* 537 */ 'd', '2', '3', 0,
  /* 541 */ 's', '2', '3', 0,
  /* 545 */ 'D', '1', '_', 'D', '3', 0,
  /* 551 */ 'D', '1', '_', 'D', '2', '_', 'D', '3', 0,
  /* 560 */ 'Q', '0', '_', 'Q', '1', '_', 'Q', '2', '_', 'Q', '3', 0,
  /* 572 */ 'R', '2', '_', 'R', '3', 0,
  /* 578 */ 'd', '3', 0,
  /* 581 */ 'q', '3', 0,
  /* 584 */ 'r', '3', 0,
  /* 587 */ 's', '3', 0,
  /* 590 */ 'D', '8', '_', 'D', '1', '0', '_', 'D', '1', '2', '_', 'D', '1', '4', 0,
  /* 605 */ 'D', '1', '1', '_', 'D', '1', '2', '_', 'D', '1', '3', '_', 'D', '1', '4', 0,
  /* 621 */ 'Q', '1', '1', '_', 'Q', '1', '2', '_', 'Q', '1', '3', '_', 'Q', '1', '4', 0,
  /* 637 */ 'd', '1', '4', 0,
  /* 641 */ 'q', '1', '4', 0,
  /* 645 */ 's', '1', '4', 0,
  /* 649 */ 'D', '1', '8', '_', 'D', '2', '0', '_', 'D', '2', '2', '_', 'D', '2', '4', 0,
  /* 665 */ 'D', '2', '1', '_', 'D', '2', '2', '_', 'D', '2', '3', '_', 'D', '2', '4', 0,
  /* 681 */ 'd', '2', '4', 0,
  /* 685 */ 's', '2', '4', 0,
  /* 689 */ 'D', '0', '_', 'D', '2', '_', 'D', '4', 0,
  /* 698 */ 'D', '1', '_', 'D', '2', '_', 'D', '3', '_', 'D', '4', 0,
  /* 710 */ 'Q', '1', '_', 'Q', '2', '_', 'Q', '3', '_', 'Q', '4', 0,
  /* 722 */ 'd', '4', 0,
  /* 725 */ 'q', '4', 0,
  /* 728 */ 'r', '4', 0,
  /* 731 */ 's', '4', 0,
  /* 734 */ 'D', '9', '_', 'D', '1', '1', '_', 'D', '1', '3', '_', 'D', '1', '5', 0,
  /* 749 */ 'D', '1', '3', '_', 'D', '1', '4', '_', 'D', '1', '5', 0,
  /* 761 */ 'Q', '1', '2', '_', 'Q', '1', '3', '_', 'Q', '1', '4', '_', 'Q', '1', '5', 0,
  /* 777 */ 'd', '1', '5', 0,
  /* 781 */ 'q', '1', '5', 0,
  /* 785 */ 's', '1', '5', 0,
  /* 789 */ 'D', '1', '9', '_', 'D', '2', '1', '_', 'D', '2', '3', '_', 'D', '2', '5', 0,
  /* 805 */ 'D', '2', '3', '_', 'D', '2', '4', '_', 'D', '2', '5', 0,
  /* 817 */ 'd', '2', '5', 0,
  /* 821 */ 's', '2', '5', 0,
  /* 825 */ 'D', '1', '_', 'D', '3', '_', 'D', '5', 0,
  /* 834 */ 'D', '3', '_', 'D', '4', '_', 'D', '5', 0,
  /* 843 */ 'Q', '2', '_', 'Q', '3', '_', 'Q', '4', '_', 'Q', '5', 0,
  /* 855 */ 'R', '4', '_', 'R', '5', 0,
  /* 861 */ 'd', '5', 0,
  /* 864 */ 'q', '5', 0,
  /* 867 */ 'r', '5', 0,
  /* 870 */ 's', '5', 0,
  /* 873 */ 'D', '1', '0', '_', 'D', '1', '2', '_', 'D', '1', '4', '_', 'D', '1', '6', 0,
  /* 889 */ 'D', '1', '3', '_', 'D', '1', '4', '_', 'D', '1', '5', '_', 'D', '1', '6', 0,
  /* 905 */ 'd', '1', '6', 0,
  /* 909 */ 's', '1', '6', 0,
  /* 913 */ 'D', '2', '0', '_', 'D', '2', '2', '_', 'D', '2', '4', '_', 'D', '2', '6', 0,
  /* 929 */ 'D', '2', '3', '_', 'D', '2', '4', '_', 'D', '2', '5', '_', 'D', '2', '6', 0,
  /* 945 */ 'd', '2', '6', 0,
  /* 949 */ 's', '2', '6', 0,
  /* 953 */ 'D', '0', '_', 'D', '2', '_', 'D', '4', '_', 'D', '6', 0,
  /* 965 */ 'D', '3', '_', 'D', '4', '_', 'D', '5', '_', 'D', '6', 0,
  /* 977 */ 'Q', '3', '_', 'Q', '4', '_', 'Q', '5', '_', 'Q', '6', 0,
  /* 989 */ 'd', '6', 0,
  /* 992 */ 'q', '6', 0,
  /* 995 */ 'r', '6', 0,
  /* 998 */ 's', '6', 0,
  /* 1001 */ 'D', '1', '1', '_', 'D', '1', '3', '_', 'D', '1', '5', '_', 'D', '1', '7', 0,
  /* 1017 */ 'D', '1', '5', '_', 'D', '1', '6', '_', 'D', '1', '7', 0,
  /* 1029 */ 'd', '1', '7', 0,
  /* 1033 */ 's', '1', '7', 0,
  /* 1037 */ 'D', '2', '1', '_', 'D', '2', '3', '_', 'D', '2', '5', '_', 'D', '2', '7', 0,
  /* 1053 */ 'D', '2', '5', '_', 'D', '2', '6', '_', 'D', '2', '7', 0,
  /* 1065 */ 'd', '2', '7', 0,
  /* 1069 */ 's', '2', '7', 0,
  /* 1073 */ 'D', '1', '_', 'D', '3', '_', 'D', '5', '_', 'D', '7', 0,
  /* 1085 */ 'D', '5', '_', 'D', '6', '_', 'D', '7', 0,
  /* 1094 */ 'Q', '4', '_', 'Q', '5', '_', 'Q', '6', '_', 'Q', '7', 0,
  /* 1106 */ 'R', '6', '_', 'R', '7', 0,
  /* 1112 */ 'd', '7', 0,
  /* 1115 */ 'q', '7', 0,
  /* 1118 */ 'r', '7', 0,
  /* 1121 */ 's', '7', 0,
  /* 1124 */ 'D', '1', '2', '_', 'D', '1', '4', '_', 'D', '1', '6', '_', 'D', '1', '8', 0,
  /* 1140 */ 'D', '1', '5', '_', 'D', '1', '6', '_', 'D', '1', '7', '_', 'D', '1', '8', 0,
  /* 1156 */ 'd', '1', '8', 0,
  /* 1160 */ 's', '1', '8', 0,
  /* 1164 */ 'D', '2', '2', '_', 'D', '2', '4', '_', 'D', '2', '6', '_', 'D', '2', '8', 0,
  /* 1180 */ 'D', '2', '5', '_', 'D', '2', '6', '_', 'D', '2', '7', '_', 'D', '2', '8', 0,
  /* 1196 */ 'd', '2', '8', 0,
  /* 1200 */ 's', '2', '8', 0,
  /* 1204 */ 'D', '2', '_', 'D', '4', '_', 'D', '6', '_', 'D', '8', 0,
  /* 1216 */ 'D', '5', '_', 'D', '6', '_', 'D', '7', '_', 'D', '8', 0,
  /* 1228 */ 'Q', '5', '_', 'Q', '6', '_', 'Q', '7', '_', 'Q', '8', 0,
  /* 1240 */ 'd', '8', 0,
  /* 1243 */ 'q', '8', 0,
  /* 1246 */ 'r', '8', 0,
  /* 1249 */ 's', '8', 0,
  /* 1252 */ 'D', '1', '3', '_', 'D', '1', '5', '_', 'D', '1', '7', '_', 'D', '1', '9', 0,
  /* 1268 */ 'D', '1', '7', '_', 'D', '1', '8', '_', 'D', '1', '9', 0,
  /* 1280 */ 'd', '1', '9', 0,
  /* 1284 */ 's', '1', '9', 0,
  /* 1288 */ 'D', '2', '3', '_', 'D', '2', '5', '_', 'D', '2', '7', '_', 'D', '2', '9', 0,
  /* 1304 */ 'D', '2', '7', '_', 'D', '2', '8', '_', 'D', '2', '9', 0,
  /* 1316 */ 'd', '2', '9', 0,
  /* 1320 */ 's', '2', '9', 0,
  /* 1324 */ 'D', '3', '_', 'D', '5', '_', 'D', '7', '_', 'D', '9', 0,
  /* 1336 */ 'D', '7', '_', 'D', '8', '_', 'D', '9', 0,
  /* 1345 */ 'Q', '6', '_', 'Q', '7', '_', 'Q', '8', '_', 'Q', '9', 0,
  /* 1357 */ 'R', '8', '_', 'R', '9', 0,
  /* 1363 */ 'd', '9', 0,
  /* 1366 */ 'q', '9', 0,
  /* 1369 */ 'r', '9', 0,
  /* 1372 */ 's', '9', 0,
  /* 1375 */ 'R', '1', '2', '_', 'S', 'P', 0,
  /* 1382 */ 'p', 'c', 0,
  /* 1385 */ 'f', 'p', 'e', 'x', 'c', 0,
  /* 1391 */ 'f', 'p', 's', 'i', 'd', 0,
  /* 1397 */ 'i', 't', 's', 't', 'a', 't', 'e', 0,
  /* 1405 */ 's', 'p', 0,
  /* 1408 */ 'f', 'p', 's', 'c', 'r', 0,
  /* 1414 */ 'l', 'r', 0,
  /* 1417 */ 'a', 'p', 's', 'r', 0,
  /* 1422 */ 'c', 'p', 's', 'r', 0,
  /* 1427 */ 's', 'p', 's', 'r', 0,
  /* 1432 */ 'f', 'p', 'i', 'n', 's', 't', 0,
  /* 1439 */ 'f', 'p', 's', 'c', 'r', '_', 'n', 'z', 'c', 'v', 0,
  /* 1450 */ 'a', 'p', 's', 'r', '_', 'n', 'z', 'c', 'v', 0,
  };

  static const uint32_t RegAsmOffset[] = {
    1417, 1450, 1422, 1385, 1432, 1408, 1439, 1391, 1397, 1414, 1382, 1405, 1427, 135, 
    296, 432, 578, 722, 861, 989, 1112, 1240, 1363, 39, 196, 355, 497, 637, 
    777, 905, 1029, 1156, 1280, 87, 240, 403, 537, 681, 817, 945, 1065, 1196, 
    1316, 127, 276, 447, 141, 302, 438, 138, 299, 435, 581, 725, 864, 992, 
    1115, 1243, 1366, 43, 200, 359, 501, 641, 781, 144, 305, 441, 584, 728, 
    867, 995, 1118, 1246, 1369, 47, 204, 363, 147, 308, 444, 587, 731, 870, 
    998, 1121, 1249, 1372, 51, 208, 367, 505, 645, 785, 909, 1033, 1160, 1284, 
    91, 244, 407, 541, 685, 821, 949, 1069, 1200, 1320, 131, 280, 411, 545, 
    692, 828, 959, 1079, 1210, 1330, 6, 167, 317, 461, 597, 741, 881, 1009, 
    1132, 1260, 63, 232, 379, 517, 657, 797, 921, 1045, 1172, 1296, 103, 268, 
    284, 426, 566, 716, 849, 983, 1100, 1234, 1351, 32, 180, 347, 489, 629, 
    769, 560, 710, 843, 977, 1094, 1228, 1345, 26, 174, 340, 481, 621, 761, 
    1375, 290, 572, 855, 1106, 1357, 188, 417, 551, 701, 834, 968, 1085, 1219, 
    1336, 16, 150, 328, 469, 609, 749, 893, 1017, 1144, 1268, 75, 212, 391, 
    525, 669, 805, 933, 1053, 1184, 1304, 115, 248, 689, 825, 956, 1076, 1207, 
    1327, 3, 164, 314, 458, 593, 737, 877, 1005, 1128, 1256, 59, 228, 375, 
    513, 653, 793, 917, 1041, 1168, 1292, 99, 264, 953, 1073, 1204, 1324, 0, 
    161, 311, 455, 590, 734, 873, 1001, 1124, 1252, 55, 224, 371, 509, 649, 
    789, 913, 1037, 1164, 1288, 95, 260, 420, 704, 971, 1222, 19, 332, 613, 
    897, 1148, 79, 395, 673, 937, 1188, 119, 698, 965, 1216, 13, 325, 605, 
    889, 1140, 71, 387, 665, 929, 1180, 111, 
  };

  //int i;
  //for (i = 0; i < sizeof(RegAsmOffset)/4; i++)
  //     printf("%s = %u\n", AsmStrs+RegAsmOffset[i], i + 1);
  //printf("*************************\n");
  return AsmStrs+RegAsmOffset[RegNo-1];
#else
  return NULL;
#endif
}

#ifdef PRINT_ALIAS_INSTR
#undef PRINT_ALIAS_INSTR

static void printCustomAliasOperand(MCInst *MI, unsigned OpIdx,
  unsigned PrintMethodIdx, SStream *OS)
{
  switch (PrintMethodIdx) {
  default:
    // llvm_unreachable("Unknown PrintMethod kind");
    break;
  case 0:
    printPredicateOperand(MI, OpIdx, OS);
    break;
  case 1:
    printSBitModifierOperand(MI, OpIdx, OS);
    break;
  case 2:
    printFPImmOperand(MI, OpIdx, OS);
    break;
  case 3:
    printRegisterList(MI, OpIdx, OS);
    break;
  case 4:
    printPImmediate(MI, OpIdx, OS);
    break;
  case 5:
    printCImmediate(MI, OpIdx, OS);
    break;
  case 6:
    printImmPlusOneOperand(MI, OpIdx, OS);
    break;
  case 7:
    printAddrMode5Operand(MI, OpIdx, OS, false);
    break;
  case 8:
    printNEONModImmOperand(MI, OpIdx, OS);
    break;
  case 9:
    printT2SOOperand(MI, OpIdx, OS);
    break;
  case 10:
    printAdrLabelOperand<0>(MI, OpIdx, OS, 0);
    break;
  case 11:
    printThumbSRImm(MI, OpIdx, OS);
    break;
  case 12:
    printAddrModeImm12Operand(MI, OpIdx, OS, false);
    break;
  case 13:
    printThumbLdrLabelOperand(MI, OpIdx, OS);
    break;
  case 14:
    printT2AddrModeSoRegOperand(MI, OpIdx, OS);
    break;
  case 15:
    printRotImmOperand(MI, OpIdx, OS);
    break;
  case 16:
    printCPSIMod(MI, OpIdx, OS);
    break;
  }
}

static char *printAliasInstr(MCInst *MI, SStream *OS, void *info)
{
  #define GETREGCLASS_CONTAIN(_class, _reg) MCRegisterClass_contains(MCRegisterInfo_getRegClass(MRI, _class), MCOperand_getReg(MCInst_getOperand(MI, _reg)))
  const char *AsmString;
  char *tmp, *AsmMnem, *AsmOps, *c;
  int OpIdx, PrintMethodIdx;
  MCRegisterInfo *MRI = (MCRegisterInfo *)info;
  switch (MCInst_getOpcode(MI)) {
  default: return NULL;
  case ARM_ANDri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (ANDri rGPR:$Rd, rGPR:$Rn, mod_imm_not:$imm, pred:$p, cc_out:$s)
      AsmString = "bic$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (ANDri rGPR:$Rdn, rGPR:$Rdn, mod_imm_not:$imm, pred:$p, cc_out:$s)
      AsmString = "bic$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x03";
      break;
    }
    return NULL;
  case ARM_BICri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (BICri rGPR:$Rd, rGPR:$Rn, mod_imm_not:$imm, pred:$p, cc_out:$s)
      AsmString = "and$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (BICri rGPR:$Rdn, rGPR:$Rdn, mod_imm_not:$imm, pred:$p, cc_out:$s)
      AsmString = "and$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x03";
      break;
    }
    return NULL;
  case ARM_BKPT:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (BKPT 0)
      AsmString = "bkpt";
      break;
    }
    return NULL;
  case ARM_CMNri:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (CMNri rGPR:$Rd, mod_imm_neg:$imm, pred:$p)
      AsmString = "cmp$\xFF\x03\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_CMPri:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (CMPri rGPR:$Rd, mod_imm_neg:$imm, pred:$p)
      AsmString = "cmn$\xFF\x03\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_DMB:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15) {
      // (DMB 15)
      AsmString = "dmb";
      break;
    }
    return NULL;
  case ARM_DSB:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15) {
      // (DSB 15)
      AsmString = "dsb";
      break;
    }
    return NULL;
  case ARM_FCONSTD:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0)) {
      // (FCONSTD DPR:$Dd, vfp_f64imm:$val, pred:$p)
      AsmString = "fconstd$\xFF\x03\x01 $\x01, $\xFF\x02\x03";
      break;
    }
    return NULL;
  case ARM_FCONSTS:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0)) {
      // (FCONSTS SPR:$Sd, vfp_f32imm:$val, pred:$p)
      AsmString = "fconsts$\xFF\x03\x01 $\x01, $\xFF\x02\x03";
      break;
    }
    return NULL;
  case ARM_FMSTAT:
    if (MCInst_getNumOperands(MI) == 2) {
      // (FMSTAT pred:$p)
      AsmString = "fmstat$\xFF\x01\x01";
      break;
    }
    return NULL;
  case ARM_HINT:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (HINT 0, pred:$p)
      AsmString = "nop$\xFF\x02\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 1) {
      // (HINT 1, pred:$p)
      AsmString = "yield$\xFF\x02\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2) {
      // (HINT 2, pred:$p)
      AsmString = "wfe$\xFF\x02\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 3) {
      // (HINT 3, pred:$p)
      AsmString = "wfi$\xFF\x02\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 4) {
      // (HINT 4, pred:$p)
      AsmString = "sev$\xFF\x02\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 5) {
      // (HINT 5, pred:$p)
      AsmString = "sevl$\xFF\x02\x01";
      break;
    }
    return NULL;
  case ARM_ISB:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15) {
      // (ISB 15)
      AsmString = "isb";
      break;
    }
    return NULL;
  case ARM_LDMIA_UPD:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == ARM_SP) {
      // (LDMIA_UPD SP, pred:$p, reglist:$regs)
      AsmString = "pop$\xFF\x02\x01 $\xFF\x04\x04";
      break;
    }
    return NULL;
  case ARM_MCR:
    if (MCInst_getNumOperands(MI) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 5)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 5)) == 0) {
      // (MCR p_imm:$cop, imm0_7:$opc1, GPR:$Rt, c_imm:$CRn, c_imm:$CRm, 0, pred:$p)
      AsmString = "mcr$\xFF\x07\x01 $\xFF\x01\x05, $\x02, $\x03, $\xFF\x04\x06, $\xFF\x05\x06";
      break;
    }
    return NULL;
  case ARM_MCR2:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 5)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 5)) == 0) {
      // (MCR2 p_imm:$cop, imm0_7:$opc1, GPR:$Rt, c_imm:$CRn, c_imm:$CRm, 0)
      AsmString = "mcr2 $\xFF\x01\x05, $\x02, $\x03, $\xFF\x04\x06, $\xFF\x05\x06";
      break;
    }
    return NULL;
  case ARM_MLA:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 2) &&
        MCOperand_isReg(MCInst_getOperand(MI, 3)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 3)) {
      // (MLA GPRnopc:$Rd, GPRnopc:$Rn, GPRnopc:$Rm, GPRnopc:$Ra, pred:$p, cc_out:$s)
      AsmString = "mla$\xFF\x07\x02$\xFF\x05\x01 $\x01, $\x02, $\x03, $\x04";
      break;
    }
    return NULL;
  case ARM_MOVi:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (MOVi rGPR:$Rd, mod_imm_not:$imm, pred:$p, cc_out:$s)
      AsmString = "mvn$\xFF\x05\x02$\xFF\x03\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_MOVi16:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0)) {
      // (MOVi16 GPR:$Rd, imm0_65535_expr:$imm, pred:$p)
      AsmString = "mov$\xFF\x03\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_MRC:
    if (MCInst_getNumOperands(MI) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRwithAPSRRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 5)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 5)) == 0) {
      // (MRC GPRwithAPSR:$Rt, p_imm:$cop, imm0_7:$opc1, c_imm:$CRn, c_imm:$CRm, 0, pred:$p)
      AsmString = "mrc$\xFF\x07\x01 $\xFF\x02\x05, $\x03, $\x01, $\xFF\x04\x06, $\xFF\x05\x06";
      break;
    }
    return NULL;
  case ARM_MRC2:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRwithAPSRRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 5)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 5)) == 0) {
      // (MRC2 GPRwithAPSR:$Rt, p_imm:$cop, imm0_7:$opc1, c_imm:$CRn, c_imm:$CRm, 0)
      AsmString = "mrc2 $\xFF\x02\x05, $\x03, $\x01, $\xFF\x04\x06, $\xFF\x05\x06";
      break;
    }
    return NULL;
  case ARM_MRS:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (MRS GPRnopc:$Rd, pred:$p)
      AsmString = "mrs$\xFF\x02\x01 $\x01, cpsr";
      break;
    }
    return NULL;
  case ARM_MUL:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 2)) {
      // (MUL GPRnopc:$Rd, GPRnopc:$Rn, GPRnopc:$Rm, pred:$p, cc_out:$s)
      AsmString = "mul$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_MVNi:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (MVNi rGPR:$Rd, mod_imm_not:$imm, pred:$p, cc_out:$s)
      AsmString = "mov$\xFF\x05\x02$\xFF\x03\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_RSBri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (RSBri GPR:$Rd, GPR:$Rm, 0, pred:$p, cc_out:$s)
      AsmString = "neg$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_SMLAL:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 2) &&
        MCOperand_isReg(MCInst_getOperand(MI, 3)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 3)) {
      // (SMLAL GPR:$RdLo, GPR:$RdHi, GPR:$Rn, GPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "smlal$\xFF\x07\x02$\xFF\x05\x01 $\x01, $\x02, $\x03, $\x04";
      break;
    }
    return NULL;
  case ARM_SMULL:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 2) &&
        MCOperand_isReg(MCInst_getOperand(MI, 3)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 3)) {
      // (SMULL GPR:$RdLo, GPR:$RdHi, GPR:$Rn, GPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "smull$\xFF\x07\x02$\xFF\x05\x01 $\x01, $\x02, $\x03, $\x04";
      break;
    }
    return NULL;
  case ARM_SRSDA:
    if (MCInst_getNumOperands(MI) == 1) {
      // (SRSDA imm0_31:$mode)
      AsmString = "srsda $\x01";
      break;
    }
    return NULL;
  case ARM_SRSDA_UPD:
    if (MCInst_getNumOperands(MI) == 1) {
      // (SRSDA_UPD imm0_31:$mode)
      AsmString = "srsda $\x01!";
      break;
    }
    return NULL;
  case ARM_SRSDB:
    if (MCInst_getNumOperands(MI) == 1) {
      // (SRSDB imm0_31:$mode)
      AsmString = "srsdb $\x01";
      break;
    }
    return NULL;
  case ARM_SRSDB_UPD:
    if (MCInst_getNumOperands(MI) == 1) {
      // (SRSDB_UPD imm0_31:$mode)
      AsmString = "srsdb $\x01!";
      break;
    }
    return NULL;
  case ARM_SRSIA:
    if (MCInst_getNumOperands(MI) == 1) {
      // (SRSIA imm0_31:$mode)
      AsmString = "srsia $\x01";
      break;
    }
    return NULL;
  case ARM_SRSIA_UPD:
    if (MCInst_getNumOperands(MI) == 1) {
      // (SRSIA_UPD imm0_31:$mode)
      AsmString = "srsia $\x01!";
      break;
    }
    return NULL;
  case ARM_SRSIB:
    if (MCInst_getNumOperands(MI) == 1) {
      // (SRSIB imm0_31:$mode)
      AsmString = "srsib $\x01";
      break;
    }
    return NULL;
  case ARM_SRSIB_UPD:
    if (MCInst_getNumOperands(MI) == 1) {
      // (SRSIB_UPD imm0_31:$mode)
      AsmString = "srsib $\x01!";
      break;
    }
    return NULL;
  case ARM_SSAT:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (SSAT GPRnopc:$Rd, imm1_32:$sat_imm, GPRnopc:$Rn, 0, pred:$p)
      AsmString = "ssat$\xFF\x05\x01 $\x01, $\xFF\x02\x07, $\x03";
      break;
    }
    return NULL;
  case ARM_STMDB_UPD:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == ARM_SP) {
      // (STMDB_UPD SP, pred:$p, reglist:$regs)
      AsmString = "push$\xFF\x02\x01 $\xFF\x04\x04";
      break;
    }
    return NULL;
  case ARM_SUBri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1)) {
      // (SUBri GPR:$Rd, GPR:$Rn, mod_imm_neg:$imm, pred:$p, cc_out:$s)
      AsmString = "add$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (SUBri GPR:$Rd, GPR:$Rd, mod_imm_neg:$imm, pred:$p, cc_out:$s)
      AsmString = "add$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x03";
      break;
    }
    return NULL;
  case ARM_SXTAB:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (SXTAB GPRnopc:$Rd, GPR:$Rn, GPRnopc:$Rm, 0, pred:$p)
      AsmString = "sxtab$\xFF\x05\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_SXTAB16:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (SXTAB16 GPRnopc:$Rd, GPR:$Rn, GPRnopc:$Rm, 0, pred:$p)
      AsmString = "sxtab16$\xFF\x05\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_SXTAH:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (SXTAH GPRnopc:$Rd, GPR:$Rn, GPRnopc:$Rm, 0, pred:$p)
      AsmString = "sxtah$\xFF\x05\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_SXTB:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (SXTB GPRnopc:$Rd, GPRnopc:$Rm, 0, pred:$p)
      AsmString = "sxtb$\xFF\x04\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_SXTB16:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (SXTB16 GPRnopc:$Rd, GPRnopc:$Rm, 0, pred:$p)
      AsmString = "sxtb16$\xFF\x04\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_SXTH:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (SXTH GPRnopc:$Rd, GPRnopc:$Rm, 0, pred:$p)
      AsmString = "sxth$\xFF\x04\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_UMLAL:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 2) &&
        MCOperand_isReg(MCInst_getOperand(MI, 3)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 3)) {
      // (UMLAL GPR:$RdLo, GPR:$RdHi, GPR:$Rn, GPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "umlal$\xFF\x07\x02$\xFF\x05\x01 $\x01, $\x02, $\x03, $\x04";
      break;
    }
    return NULL;
  case ARM_UMULL:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 2) &&
        MCOperand_isReg(MCInst_getOperand(MI, 3)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 3)) {
      // (UMULL GPR:$RdLo, GPR:$RdHi, GPR:$Rn, GPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "umull$\xFF\x07\x02$\xFF\x05\x01 $\x01, $\x02, $\x03, $\x04";
      break;
    }
    return NULL;
  case ARM_USAT:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (USAT GPRnopc:$Rd, imm0_31:$sat_imm, GPRnopc:$Rn, 0, pred:$p)
      AsmString = "usat$\xFF\x05\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_UXTAB:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (UXTAB GPRnopc:$Rd, GPR:$Rn, GPRnopc:$Rm, 0, pred:$p)
      AsmString = "uxtab$\xFF\x05\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_UXTAB16:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (UXTAB16 GPRnopc:$Rd, GPR:$Rn, GPRnopc:$Rm, 0, pred:$p)
      AsmString = "uxtab16$\xFF\x05\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_UXTAH:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (UXTAH GPRnopc:$Rd, GPR:$Rn, GPRnopc:$Rm, 0, pred:$p)
      AsmString = "uxtah$\xFF\x05\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_UXTB:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (UXTB GPRnopc:$Rd, GPRnopc:$Rm, 0, pred:$p)
      AsmString = "uxtb$\xFF\x04\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_UXTB16:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (UXTB16 GPRnopc:$Rd, GPRnopc:$Rm, 0, pred:$p)
      AsmString = "uxtb16$\xFF\x04\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_UXTH:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (UXTH GPRnopc:$Rd, GPRnopc:$Rm, 0, pred:$p)
      AsmString = "uxth$\xFF\x04\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VACGEd:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VACGEd DPR:$Vd, DPR:$Vm, DPR:$Vn, pred:$p)
      AsmString = "vacle$\xFF\x04\x01.f32 $\x01, $\x03, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (VACGEd DPR:$Vd, DPR:$Vm, DPR:$Vd, pred:$p)
      AsmString = "vacle$\xFF\x04\x01.f32 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VACGEq:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VACGEq QPR:$Vd, QPR:$Vm, QPR:$Vn, pred:$p)
      AsmString = "vacle$\xFF\x04\x01.f32 $\x01, $\x03, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (VACGEq QPR:$Vd, QPR:$Vm, QPR:$Vd, pred:$p)
      AsmString = "vacle$\xFF\x04\x01.f32 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VACGTd:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VACGTd DPR:$Vd, DPR:$Vm, DPR:$Vn, pred:$p)
      AsmString = "vaclt$\xFF\x04\x01.f32 $\x01, $\x03, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (VACGTd DPR:$Vd, DPR:$Vm, DPR:$Vd, pred:$p)
      AsmString = "vaclt$\xFF\x04\x01.f32 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VACGTq:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VACGTq QPR:$Vd, QPR:$Vm, QPR:$Vn, pred:$p)
      AsmString = "vaclt$\xFF\x04\x01.f32 $\x01, $\x03, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (VACGTq QPR:$Vd, QPR:$Vm, QPR:$Vd, pred:$p)
      AsmString = "vaclt$\xFF\x04\x01.f32 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VADDD:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VADDD DPR:$Dd, DPR:$Dn, DPR:$Dm, pred:$p)
      AsmString = "faddd$\xFF\x04\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_VADDS:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 2)) {
      // (VADDS SPR:$Sd, SPR:$Sn, SPR:$Sm, pred:$p)
      AsmString = "fadds$\xFF\x04\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_VBICiv2i32:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0)) {
      // (VBICiv2i32 DPR:$Vd, nImmSplatNotI32:$imm, pred:$p)
      AsmString = "vand$\xFF\x03\x01.i32 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VBICiv4i16:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0)) {
      // (VBICiv4i16 DPR:$Vd, nImmSplatNotI16:$imm, pred:$p)
      AsmString = "vand$\xFF\x03\x01.i16 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VBICiv4i32:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0)) {
      // (VBICiv4i32 QPR:$Vd, nImmSplatNotI32:$imm, pred:$p)
      AsmString = "vand$\xFF\x03\x01.i32 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VBICiv8i16:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0)) {
      // (VBICiv8i16 QPR:$Vd, nImmSplatNotI16:$imm, pred:$p)
      AsmString = "vand$\xFF\x03\x01.i16 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGEfd:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGEfd DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01.f32 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGEfq:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGEfq QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01.f32 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGEsv16i8:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGEsv16i8 QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01.s8 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGEsv2i32:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGEsv2i32 DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01.s32 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGEsv4i16:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGEsv4i16 DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01.s16 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGEsv4i32:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGEsv4i32 QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01.s32 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGEsv8i16:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGEsv8i16 QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01.s16 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGEsv8i8:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGEsv8i8 DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01.s8 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGEuv16i8:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGEuv16i8 QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01.u8 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGEuv2i32:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGEuv2i32 DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01.u32 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGEuv4i16:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGEuv4i16 DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01.u16 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGEuv4i32:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGEuv4i32 QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01.u32 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGEuv8i16:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGEuv8i16 QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01.u16 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGEuv8i8:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGEuv8i8 DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vcle$\xFF\x04\x01.u8 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTfd:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGTfd DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01.f32 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTfq:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGTfq QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01.f32 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTsv16i8:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGTsv16i8 QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01.s8 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTsv2i32:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGTsv2i32 DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01.s32 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTsv4i16:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGTsv4i16 DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01.s16 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTsv4i32:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGTsv4i32 QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01.s32 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTsv8i16:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGTsv8i16 QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01.s16 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTsv8i8:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGTsv8i8 DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01.s8 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTuv16i8:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGTuv16i8 QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01.u8 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTuv2i32:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGTuv2i32 DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01.u32 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTuv4i16:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGTuv4i16 DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01.u16 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTuv4i32:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGTuv4i32 QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01.u32 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTuv8i16:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 2)) {
      // (VCGTuv8i16 QPR:$Qd, QPR:$Qm, QPR:$Qn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01.u16 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCGTuv8i8:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VCGTuv8i8 DPR:$Dd, DPR:$Dm, DPR:$Dn, pred:$p)
      AsmString = "vclt$\xFF\x04\x01.u8 $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case ARM_VCMPZD:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0)) {
      // (VCMPZD DPR:$val, pred:$p)
      AsmString = "fcmpzd$\xFF\x02\x01 $\x01";
      break;
    }
    return NULL;
  case ARM_VCMPZS:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0)) {
      // (VCMPZS SPR:$val, pred:$p)
      AsmString = "fcmpzs$\xFF\x02\x01 $\x01";
      break;
    }
    return NULL;
  case ARM_VLDRD:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0)) {
      // (VLDRD DPR:$Dd, addrmode5:$addr, pred:$p)
      AsmString = "vldr$\xFF\x04\x01.64 $\x01, $\xFF\x02\x08";
      break;
    }
    return NULL;
  case ARM_VLDRS:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0)) {
      // (VLDRS SPR:$Sd, addrmode5:$addr, pred:$p)
      AsmString = "vldr$\xFF\x04\x01.32 $\x01, $\xFF\x02\x08";
      break;
    }
    return NULL;
  case ARM_VMOVDRR:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 2)) {
      // (VMOVDRR DPR:$Dn, GPR:$Rt, GPR:$Rt2, pred:$p)
      AsmString = "vmov$\xFF\x04\x01.f64 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_VMOVRRD:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VMOVRRD GPR:$Rt, GPR:$Rt2, DPR:$Dn, pred:$p)
      AsmString = "vmov$\xFF\x04\x01.f64 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_VMOVS:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 1)) {
      // (VMOVS SPR:$Sd, SPR:$Sm, pred:$p)
      AsmString = "vmov$\xFF\x03\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VMVNv2i32:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0)) {
      // (VMVNv2i32 DPR:$Vd, nImmVMOVI32Neg:$imm, pred:$p)
      AsmString = "vmov$\xFF\x03\x01.i32 $\x01, $\xFF\x02\x09";
      break;
    }
    return NULL;
  case ARM_VMVNv4i32:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0)) {
      // (VMVNv4i32 QPR:$Vd, nImmVMOVI32Neg:$imm, pred:$p)
      AsmString = "vmov$\xFF\x03\x01.i32 $\x01, $\xFF\x02\x09";
      break;
    }
    return NULL;
  case ARM_VRINTAD:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VRINTAD DPR:$Dd, DPR:$Dm)
      AsmString = "vrinta.f64.f64	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTAND:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VRINTAND DPR:$Dd, DPR:$Dm)
      AsmString = "vrinta.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTANQ:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1)) {
      // (VRINTANQ QPR:$Qd, QPR:$Qm)
      AsmString = "vrinta.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTAS:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 1)) {
      // (VRINTAS SPR:$Sd, SPR:$Sm)
      AsmString = "vrinta.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTMD:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VRINTMD DPR:$Dd, DPR:$Dm)
      AsmString = "vrintm.f64.f64	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTMND:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VRINTMND DPR:$Dd, DPR:$Dm)
      AsmString = "vrintm.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTMNQ:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1)) {
      // (VRINTMNQ QPR:$Qd, QPR:$Qm)
      AsmString = "vrintm.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTMS:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 1)) {
      // (VRINTMS SPR:$Sd, SPR:$Sm)
      AsmString = "vrintm.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTND:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VRINTND DPR:$Dd, DPR:$Dm)
      AsmString = "vrintn.f64.f64	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTNND:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VRINTNND DPR:$Dd, DPR:$Dm)
      AsmString = "vrintn.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTNNQ:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1)) {
      // (VRINTNNQ QPR:$Qd, QPR:$Qm)
      AsmString = "vrintn.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTNS:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 1)) {
      // (VRINTNS SPR:$Sd, SPR:$Sm)
      AsmString = "vrintn.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTPD:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VRINTPD DPR:$Dd, DPR:$Dm)
      AsmString = "vrintp.f64.f64	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTPND:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VRINTPND DPR:$Dd, DPR:$Dm)
      AsmString = "vrintp.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTPNQ:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1)) {
      // (VRINTPNQ QPR:$Qd, QPR:$Qm)
      AsmString = "vrintp.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTPS:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 1)) {
      // (VRINTPS SPR:$Sd, SPR:$Sm)
      AsmString = "vrintp.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTRD:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VRINTRD DPR:$Dd, DPR:$Dm, pred:$p)
      AsmString = "vrintr$\xFF\x03\x01.f64.f64	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTRS:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 1)) {
      // (VRINTRS SPR:$Sd, SPR:$Sm, pred:$p)
      AsmString = "vrintr$\xFF\x03\x01.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTXD:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VRINTXD DPR:$Dd, DPR:$Dm, pred:$p)
      AsmString = "vrintx$\xFF\x03\x01.f64.f64	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTXND:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VRINTXND DPR:$Dd, DPR:$Dm)
      AsmString = "vrintx.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTXNQ:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1)) {
      // (VRINTXNQ QPR:$Qd, QPR:$Qm)
      AsmString = "vrintx.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTXS:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 1)) {
      // (VRINTXS SPR:$Sd, SPR:$Sm, pred:$p)
      AsmString = "vrintx$\xFF\x03\x01.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTZD:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VRINTZD DPR:$Dd, DPR:$Dm, pred:$p)
      AsmString = "vrintz$\xFF\x03\x01.f64.f64	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTZND:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VRINTZND DPR:$Dd, DPR:$Dm)
      AsmString = "vrintz.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTZNQ:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_QPRRegClassID, 1)) {
      // (VRINTZNQ QPR:$Qd, QPR:$Qm)
      AsmString = "vrintz.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VRINTZS:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 1)) {
      // (VRINTZS SPR:$Sd, SPR:$Sm, pred:$p)
      AsmString = "vrintz$\xFF\x03\x01.f32.f32	$\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VSETLNi32:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 1) {
      // (VSETLNi32 DPR:$Dd, GPR:$Rn, 1, pred:$p)
      AsmString = "fmdhr$\xFF\x04\x01 $\x01, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (VSETLNi32 DPR:$Dd, GPR:$Rn, 0, pred:$p)
      AsmString = "fmdlr$\xFF\x04\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VSQRTD:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1)) {
      // (VSQRTD DPR:$Dd, DPR:$Dm, pred:$p)
      AsmString = "vsqrt$\xFF\x03\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VSQRTS:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 1)) {
      // (VSQRTS SPR:$Sd, SPR:$Sm, pred:$p)
      AsmString = "vsqrt$\xFF\x03\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_VSTRD:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0)) {
      // (VSTRD DPR:$Dd, addrmode5:$addr, pred:$p)
      AsmString = "vstr$\xFF\x04\x01.64 $\x01, $\xFF\x02\x08";
      break;
    }
    return NULL;
  case ARM_VSTRS:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0)) {
      // (VSTRS SPR:$Sd, addrmode5:$addr, pred:$p)
      AsmString = "vstr$\xFF\x04\x01.32 $\x01, $\xFF\x02\x08";
      break;
    }
    return NULL;
  case ARM_VSUBD:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_DPRRegClassID, 2)) {
      // (VSUBD DPR:$Dd, DPR:$Dn, DPR:$Dm, pred:$p)
      AsmString = "fsubd$\xFF\x04\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_VSUBS:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_SPRRegClassID, 2)) {
      // (VSUBS SPR:$Sd, SPR:$Sn, SPR:$Sm, pred:$p)
      AsmString = "fsubs$\xFF\x04\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2ADCrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2ADCrr rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "adc$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2ADCrs:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2ADCrs rGPR:$Rd, rGPR:$Rn, t2_so_reg:$ShiftedRm, pred:$p, cc_out:$s)
      AsmString = "adc$\xFF\x07\x02$\xFF\x05\x01 $\x01, $\x02, $\xFF\x03\x0A";
      break;
    }
    return NULL;
  case ARM_t2ADDri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 1)) {
      // (t2ADDri GPRnopc:$Rd, GPRnopc:$Rn, t2_so_imm:$imm, pred:$p, cc_out:$s)
      AsmString = "add$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (t2ADDri GPRnopc:$Rdn, GPRnopc:$Rdn, t2_so_imm:$imm, pred:$p, cc_out:$s)
      AsmString = "add$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x03";
      break;
    }
    return NULL;
  case ARM_t2ADDri12:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 1)) {
      // (t2ADDri12 GPRnopc:$Rd, GPR:$Rn, imm0_4095:$imm, pred:$p)
      AsmString = "add$\xFF\x04\x01 $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (t2ADDri12 GPRnopc:$Rdn, GPRnopc:$Rdn, imm0_4095:$imm, pred:$p)
      AsmString = "add$\xFF\x04\x01 $\x01, $\x03";
      break;
    }
    return NULL;
  case ARM_t2ADDrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2ADDrr GPRnopc:$Rd, GPRnopc:$Rn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "add$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0)) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2ADDrr GPRnopc:$Rdn, GPRnopc:$Rdn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "add$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x03";
      break;
    }
    return NULL;
  case ARM_t2ADDrs:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 1)) {
      // (t2ADDrs GPRnopc:$Rd, GPRnopc:$Rn, t2_so_reg:$ShiftedRm, pred:$p, cc_out:$s)
      AsmString = "add$\xFF\x07\x02$\xFF\x05\x01 $\x01, $\x02, $\xFF\x03\x0A";
      break;
    }
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (t2ADDrs GPRnopc:$Rdn, GPRnopc:$Rdn, t2_so_reg:$ShiftedRm, pred:$p, cc_out:$s)
      AsmString = "add$\xFF\x07\x02$\xFF\x05\x01 $\x01, $\xFF\x03\x0A";
      break;
    }
    return NULL;
  case ARM_t2ADR:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2ADR rGPR:$Rd, t2adrlabel:$addr, pred:$p)
      AsmString = "adr$\xFF\x03\x01 $\x01, $\xFF\x02\x0B";
      break;
    }
    return NULL;
  case ARM_t2ANDrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2ANDrr rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "and$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2ANDrs:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2ANDrs rGPR:$Rd, rGPR:$Rn, t2_so_reg:$shift, pred:$p, cc_out:$s)
      AsmString = "and$\xFF\x07\x02$\xFF\x05\x01 $\x01, $\x02, $\xFF\x03\x0A";
      break;
    }
    return NULL;
  case ARM_t2ASRri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2ASRri rGPR:$Rd, rGPR:$Rn, imm_sr:$imm, pred:$p, cc_out:$s)
      AsmString = "asr$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x02, $\xFF\x03\x0C";
      break;
    }
    return NULL;
  case ARM_t2ASRrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2ASRrr rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "asr$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2BICrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2BICrr rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "bic$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2BICrs:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2BICrs rGPR:$Rd, rGPR:$Rn, t2_so_reg:$shift, pred:$p, cc_out:$s)
      AsmString = "bic$\xFF\x07\x02$\xFF\x05\x01 $\x01, $\x02, $\xFF\x03\x0A";
      break;
    }
    return NULL;
  case ARM_t2CMNri:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (t2CMNri GPRnopc:$Rn, t2_so_imm:$imm, pred:$p)
      AsmString = "cmn$\xFF\x03\x01 $\x01, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2CMNri rGPR:$Rd, t2_so_imm_neg:$imm, pred:$p)
      AsmString = "cmp$\xFF\x03\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2CMNzrr:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2CMNzrr GPRnopc:$Rn, rGPR:$Rm, pred:$p)
      AsmString = "cmn$\xFF\x03\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2CMNzrs:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (t2CMNzrs GPRnopc:$Rn, t2_so_reg:$shift, pred:$p)
      AsmString = "cmn$\xFF\x04\x01 $\x01, $\xFF\x02\x0A";
      break;
    }
    return NULL;
  case ARM_t2CMPri:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2CMPri rGPR:$Rd, t2_so_imm_neg:$imm, pred:$p)
      AsmString = "cmn$\xFF\x03\x01 $\x01, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (t2CMPri GPRnopc:$Rn, t2_so_imm:$imm, pred:$p)
      AsmString = "cmp$\xFF\x03\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2CMPrs:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (t2CMPrs GPRnopc:$Rn, t2_so_reg:$shift, pred:$p)
      AsmString = "cmp$\xFF\x04\x01 $\x01, $\xFF\x02\x0A";
      break;
    }
    return NULL;
  case ARM_t2DMB:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15) {
      // (t2DMB 15, pred:$p)
      AsmString = "dmb$\xFF\x02\x01";
      break;
    }
    return NULL;
  case ARM_t2DSB:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15) {
      // (t2DSB 15, pred:$p)
      AsmString = "dsb$\xFF\x02\x01";
      break;
    }
    return NULL;
  case ARM_t2EORri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2EORri rGPR:$Rd, rGPR:$Rn, t2_so_imm:$imm, pred:$p, cc_out:$s)
      AsmString = "eor$\xFF\x06\x02$\xFF\x04\x01.w $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2EORrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2EORrr rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "eor$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2EORrs:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2EORrs rGPR:$Rd, rGPR:$Rn, t2_so_reg:$shift, pred:$p, cc_out:$s)
      AsmString = "eor$\xFF\x07\x02$\xFF\x05\x01 $\x01, $\x02, $\xFF\x03\x0A";
      break;
    }
    return NULL;
  case ARM_t2HINT:
    if (MCInst_getNumOperands(MI) == 3) {
      // (t2HINT imm0_239:$imm, pred:$p)
      AsmString = "hint$\xFF\x02\x01 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (t2HINT 0, pred:$p)
      AsmString = "nop$\xFF\x02\x01.w";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 1) {
      // (t2HINT 1, pred:$p)
      AsmString = "yield$\xFF\x02\x01.w";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2) {
      // (t2HINT 2, pred:$p)
      AsmString = "wfe$\xFF\x02\x01.w";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 3) {
      // (t2HINT 3, pred:$p)
      AsmString = "wfi$\xFF\x02\x01.w";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 4) {
      // (t2HINT 4, pred:$p)
      AsmString = "sev$\xFF\x02\x01.w";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 5) {
      // (t2HINT 5, pred:$p)
      AsmString = "sevl$\xFF\x02\x01.w";
      break;
    }
    return NULL;
  case ARM_t2HVC:
    if (MCInst_getNumOperands(MI) == 1) {
      // (t2HVC imm0_65535:$imm16)
      AsmString = "hvc	$\x01";
      break;
    }
    return NULL;
  case ARM_t2ISB:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15) {
      // (t2ISB 15, pred:$p)
      AsmString = "isb$\xFF\x02\x01";
      break;
    }
    return NULL;
  case ARM_t2LDMDB:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0)) {
      // (t2LDMDB GPR:$Rn, pred:$p, reglist:$regs)
      AsmString = "ldmdb$\xFF\x02\x01.w $\x01, $\xFF\x04\x04";
      break;
    }
    return NULL;
  case ARM_t2LDMDB_UPD:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0)) {
      // (t2LDMDB_UPD GPR:$Rn, pred:$p, reglist:$regs)
      AsmString = "ldmdb$\xFF\x02\x01.w $\x01!, $\xFF\x04\x04";
      break;
    }
    return NULL;
  case ARM_t2LDMIA:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0)) {
      // (t2LDMIA GPR:$Rn, pred:$p, reglist:$regs)
      AsmString = "ldm$\xFF\x02\x01 $\x01, $\xFF\x04\x04";
      break;
    }
    return NULL;
  case ARM_t2LDMIA_UPD:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0)) {
      // (t2LDMIA_UPD GPR:$Rn, pred:$p, reglist:$regs)
      AsmString = "ldm$\xFF\x02\x01 $\x01!, $\xFF\x04\x04";
      break;
    }
    return NULL;
  case ARM_t2LDRBi12:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2LDRBi12 rGPR:$Rt, t2addrmode_imm12:$addr, pred:$p)
      AsmString = "ldrb$\xFF\x04\x01 $\x01, $\xFF\x02\x0D";
      break;
    }
    return NULL;
  case ARM_t2LDRBpci:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2LDRBpci rGPR:$Rt, t2ldrlabel:$addr, pred:$p)
      AsmString = "ldrb$\xFF\x03\x01 $\x01, $\xFF\x02\x0E";
      break;
    }
    return NULL;
  case ARM_t2LDRBpcrel:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (t2LDRBpcrel GPRnopc:$Rt, t2ldr_pcrel_imm12:$addr, pred:$p)
      AsmString = "ldrb$\xFF\x03\x01.w $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2LDRBs:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2LDRBs rGPR:$Rt, t2addrmode_so_reg:$addr, pred:$p)
      AsmString = "ldrb$\xFF\x05\x01 $\x01, $\xFF\x02\x0F";
      break;
    }
    return NULL;
  case ARM_t2LDRHi12:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2LDRHi12 rGPR:$Rt, t2addrmode_imm12:$addr, pred:$p)
      AsmString = "ldrh$\xFF\x04\x01 $\x01, $\xFF\x02\x0D";
      break;
    }
    return NULL;
  case ARM_t2LDRHpci:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2LDRHpci rGPR:$Rt, t2ldrlabel:$addr, pred:$p)
      AsmString = "ldrh$\xFF\x03\x01 $\x01, $\xFF\x02\x0E";
      break;
    }
    return NULL;
  case ARM_t2LDRHpcrel:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (t2LDRHpcrel GPRnopc:$Rt, t2ldr_pcrel_imm12:$addr, pred:$p)
      AsmString = "ldrh$\xFF\x03\x01.w $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2LDRHs:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2LDRHs rGPR:$Rt, t2addrmode_so_reg:$addr, pred:$p)
      AsmString = "ldrh$\xFF\x05\x01 $\x01, $\xFF\x02\x0F";
      break;
    }
    return NULL;
  case ARM_t2LDRSBi12:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2LDRSBi12 rGPR:$Rt, t2addrmode_imm12:$addr, pred:$p)
      AsmString = "ldrsb$\xFF\x04\x01 $\x01, $\xFF\x02\x0D";
      break;
    }
    return NULL;
  case ARM_t2LDRSBpci:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2LDRSBpci rGPR:$Rt, t2ldrlabel:$addr, pred:$p)
      AsmString = "ldrsb$\xFF\x03\x01 $\x01, $\xFF\x02\x0E";
      break;
    }
    return NULL;
  case ARM_t2LDRSBpcrel:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (t2LDRSBpcrel GPRnopc:$Rt, t2ldr_pcrel_imm12:$addr, pred:$p)
      AsmString = "ldrsb$\xFF\x03\x01.w $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2LDRSBs:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2LDRSBs rGPR:$Rt, t2addrmode_so_reg:$addr, pred:$p)
      AsmString = "ldrsb$\xFF\x05\x01 $\x01, $\xFF\x02\x0F";
      break;
    }
    return NULL;
  case ARM_t2LDRSHi12:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2LDRSHi12 rGPR:$Rt, t2addrmode_imm12:$addr, pred:$p)
      AsmString = "ldrsh$\xFF\x04\x01 $\x01, $\xFF\x02\x0D";
      break;
    }
    return NULL;
  case ARM_t2LDRSHpci:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2LDRSHpci rGPR:$Rt, t2ldrlabel:$addr, pred:$p)
      AsmString = "ldrsh$\xFF\x03\x01 $\x01, $\xFF\x02\x0E";
      break;
    }
    return NULL;
  case ARM_t2LDRSHpcrel:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (t2LDRSHpcrel GPRnopc:$Rt, t2ldr_pcrel_imm12:$addr, pred:$p)
      AsmString = "ldrsh$\xFF\x03\x01.w $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2LDRSHs:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2LDRSHs rGPR:$Rt, t2addrmode_so_reg:$addr, pred:$p)
      AsmString = "ldrsh$\xFF\x05\x01 $\x01, $\xFF\x02\x0F";
      break;
    }
    return NULL;
  case ARM_t2LDRi12:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0)) {
      // (t2LDRi12 GPR:$Rt, t2addrmode_imm12:$addr, pred:$p)
      AsmString = "ldr$\xFF\x04\x01 $\x01, $\xFF\x02\x0D";
      break;
    }
    return NULL;
  case ARM_t2LDRpci:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (t2LDRpci GPRnopc:$Rt, t2ldrlabel:$addr, pred:$p)
      AsmString = "ldr$\xFF\x03\x01 $\x01, $\xFF\x02\x0E";
      break;
    }
    return NULL;
  case ARM_t2LDRs:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0)) {
      // (t2LDRs GPR:$Rt, t2addrmode_so_reg:$addr, pred:$p)
      AsmString = "ldr$\xFF\x05\x01 $\x01, $\xFF\x02\x0F";
      break;
    }
    return NULL;
  case ARM_t2LSLri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2LSLri rGPR:$Rd, rGPR:$Rn, imm0_31:$imm, pred:$p, cc_out:$s)
      AsmString = "lsl$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2LSLrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2LSLrr rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "lsl$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2LSRri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2LSRri rGPR:$Rd, rGPR:$Rn, imm_sr:$imm, pred:$p, cc_out:$s)
      AsmString = "lsr$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x02, $\xFF\x03\x0C";
      break;
    }
    return NULL;
  case ARM_t2LSRrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2LSRrr rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "lsr$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2MCR:
    if (MCInst_getNumOperands(MI) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 5)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 5)) == 0) {
      // (t2MCR p_imm:$cop, imm0_7:$opc1, GPR:$Rt, c_imm:$CRn, c_imm:$CRm, 0, pred:$p)
      AsmString = "mcr$\xFF\x07\x01 $\xFF\x01\x05, $\x02, $\x03, $\xFF\x04\x06, $\xFF\x05\x06";
      break;
    }
    return NULL;
  case ARM_t2MCR2:
    if (MCInst_getNumOperands(MI) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 5)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 5)) == 0) {
      // (t2MCR2 p_imm:$cop, imm0_7:$opc1, GPR:$Rt, c_imm:$CRn, c_imm:$CRm, 0, pred:$p)
      AsmString = "mcr2$\xFF\x07\x01 $\xFF\x01\x05, $\x02, $\x03, $\xFF\x04\x06, $\xFF\x05\x06";
      break;
    }
    return NULL;
  case ARM_t2MOVi16:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2MOVi16 rGPR:$Rd, imm256_65535_expr:$imm, pred:$p)
      AsmString = "mov$\xFF\x03\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2MRC:
    if (MCInst_getNumOperands(MI) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRwithAPSRRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 5)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 5)) == 0) {
      // (t2MRC GPRwithAPSR:$Rt, p_imm:$cop, imm0_7:$opc1, c_imm:$CRn, c_imm:$CRm, 0, pred:$p)
      AsmString = "mrc$\xFF\x07\x01 $\xFF\x02\x05, $\x03, $\x01, $\xFF\x04\x06, $\xFF\x05\x06";
      break;
    }
    return NULL;
  case ARM_t2MRC2:
    if (MCInst_getNumOperands(MI) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRwithAPSRRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 5)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 5)) == 0) {
      // (t2MRC2 GPRwithAPSR:$Rt, p_imm:$cop, imm0_7:$opc1, c_imm:$CRn, c_imm:$CRm, 0, pred:$p)
      AsmString = "mrc2$\xFF\x07\x01 $\xFF\x02\x05, $\x03, $\x01, $\xFF\x04\x06, $\xFF\x05\x06";
      break;
    }
    return NULL;
  case ARM_t2MRS_AR:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0)) {
      // (t2MRS_AR GPR:$Rd, pred:$p)
      AsmString = "mrs$\xFF\x02\x01 $\x01, cpsr";
      break;
    }
    return NULL;
  case ARM_t2MUL:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (t2MUL rGPR:$Rn, rGPR:$Rm, rGPR:$Rn, pred:$p)
      AsmString = "mul$\xFF\x04\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2MVNi:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2MVNi rGPR:$Rd, t2_so_imm:$imm, pred:$p, cc_out:$s)
      AsmString = "mvn$\xFF\x05\x02$\xFF\x03\x01.w $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2MVNr:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2MVNr rGPR:$Rd, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "mvn$\xFF\x05\x02$\xFF\x03\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2MVNs:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2MVNs rGPR:$Rd, t2_so_reg:$ShiftedRm, pred:$p, cc_out:$s)
      AsmString = "mvn$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\xFF\x02\x0A";
      break;
    }
    return NULL;
  case ARM_t2ORNri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (t2ORNri rGPR:$Rdn, rGPR:$Rdn, t2_so_imm:$imm, pred:$p, cc_out:$s)
      AsmString = "orn$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x03";
      break;
    }
    return NULL;
  case ARM_t2ORNrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0)) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2ORNrr rGPR:$Rdn, rGPR:$Rdn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "orn$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x03";
      break;
    }
    return NULL;
  case ARM_t2ORNrs:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (t2ORNrs rGPR:$Rdn, rGPR:$Rdn, t2_so_reg:$shift, pred:$p, cc_out:$s)
      AsmString = "orn$\xFF\x07\x02$\xFF\x05\x01 $\x01, $\xFF\x03\x0A";
      break;
    }
    return NULL;
  case ARM_t2ORRri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2ORRri rGPR:$Rd, rGPR:$Rn, t2_so_imm:$imm, pred:$p, cc_out:$s)
      AsmString = "orr$\xFF\x06\x02$\xFF\x04\x01.w $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2ORRrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2ORRrr rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "orr$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2ORRrs:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2ORRrs rGPR:$Rd, rGPR:$Rn, t2_so_reg:$shift, pred:$p, cc_out:$s)
      AsmString = "orr$\xFF\x07\x02$\xFF\x05\x01 $\x01, $\x02, $\xFF\x03\x0A";
      break;
    }
    return NULL;
  case ARM_t2PLDpci:
    if (MCInst_getNumOperands(MI) == 3) {
      // (t2PLDpci t2ldr_pcrel_imm12:$addr, pred:$p)
      AsmString = "pld$\xFF\x02\x01 $\x01";
      break;
    }
    return NULL;
  case ARM_t2PLIpci:
    if (MCInst_getNumOperands(MI) == 3) {
      // (t2PLIpci t2ldr_pcrel_imm12:$addr, pred:$p)
      AsmString = "pli$\xFF\x02\x01 $\x01";
      break;
    }
    return NULL;
  case ARM_t2REV:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2REV rGPR:$Rd, rGPR:$Rm, pred:$p)
      AsmString = "rev$\xFF\x03\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2REV16:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2REV16 rGPR:$Rd, rGPR:$Rm, pred:$p)
      AsmString = "rev16$\xFF\x03\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2REVSH:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2REVSH rGPR:$Rd, rGPR:$Rm, pred:$p)
      AsmString = "revsh$\xFF\x03\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2RORri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2RORri rGPR:$Rd, rGPR:$Rn, imm0_31:$imm, pred:$p, cc_out:$s)
      AsmString = "ror$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2RORrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2RORrr rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "ror$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2RSBri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2RSBri rGPR:$Rd, rGPR:$Rn, t2_so_imm:$imm, pred:$p, cc_out:$s)
      AsmString = "rsb$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (t2RSBri rGPR:$Rdn, rGPR:$Rdn, t2_so_imm:$imm, pred:$p, cc_out:$s)
      AsmString = "rsb$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (t2RSBri rGPR:$Rd, rGPR:$Rm, 0, pred:$p, cc_out:$s)
      AsmString = "neg$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2RSBrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0)) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2RSBrr rGPR:$Rdn, rGPR:$Rdn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "rsb$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x03";
      break;
    }
    return NULL;
  case ARM_t2RSBrs:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (t2RSBrs rGPR:$Rdn, rGPR:$Rdn, t2_so_reg:$ShiftedRm, pred:$p, cc_out:$s)
      AsmString = "rsb$\xFF\x07\x02$\xFF\x05\x01 $\x01, $\xFF\x03\x0A";
      break;
    }
    return NULL;
  case ARM_t2SBCrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2SBCrr rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "sbc$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2SBCrs:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2SBCrs rGPR:$Rd, rGPR:$Rn, t2_so_reg:$ShiftedRm, pred:$p, cc_out:$s)
      AsmString = "sbc$\xFF\x07\x02$\xFF\x05\x01 $\x01, $\x02, $\xFF\x03\x0A";
      break;
    }
    return NULL;
  case ARM_t2SRSDB:
    if (MCInst_getNumOperands(MI) == 3) {
      // (t2SRSDB imm0_31:$mode, pred:$p)
      AsmString = "srsdb$\xFF\x02\x01 $\x01";
      break;
    }
    return NULL;
  case ARM_t2SRSDB_UPD:
    if (MCInst_getNumOperands(MI) == 3) {
      // (t2SRSDB_UPD imm0_31:$mode, pred:$p)
      AsmString = "srsdb$\xFF\x02\x01 $\x01!";
      break;
    }
    return NULL;
  case ARM_t2SRSIA:
    if (MCInst_getNumOperands(MI) == 3) {
      // (t2SRSIA imm0_31:$mode, pred:$p)
      AsmString = "srsia$\xFF\x02\x01 $\x01";
      break;
    }
    return NULL;
  case ARM_t2SRSIA_UPD:
    if (MCInst_getNumOperands(MI) == 3) {
      // (t2SRSIA_UPD imm0_31:$mode, pred:$p)
      AsmString = "srsia$\xFF\x02\x01 $\x01!";
      break;
    }
    return NULL;
  case ARM_t2SSAT:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (t2SSAT rGPR:$Rd, imm1_32:$sat_imm, rGPR:$Rn, 0, pred:$p)
      AsmString = "ssat$\xFF\x05\x01 $\x01, $\xFF\x02\x07, $\x03";
      break;
    }
    return NULL;
  case ARM_t2STMDB:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0)) {
      // (t2STMDB GPR:$Rn, pred:$p, reglist:$regs)
      AsmString = "stmdb$\xFF\x02\x01.w $\x01, $\xFF\x04\x04";
      break;
    }
    return NULL;
  case ARM_t2STMDB_UPD:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0)) {
      // (t2STMDB_UPD GPR:$Rn, pred:$p, reglist:$regs)
      AsmString = "stmdb$\xFF\x02\x01.w $\x01!, $\xFF\x04\x04";
      break;
    }
    return NULL;
  case ARM_t2STMIA_UPD:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0)) {
      // (t2STMIA_UPD GPR:$Rn, pred:$p, reglist:$regs)
      AsmString = "stm$\xFF\x02\x01 $\x01!, $\xFF\x04\x04";
      break;
    }
    return NULL;
  case ARM_t2STRBi12:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2STRBi12 rGPR:$Rt, t2addrmode_imm12:$addr, pred:$p)
      AsmString = "strb$\xFF\x04\x01 $\x01, $\xFF\x02\x0D";
      break;
    }
    return NULL;
  case ARM_t2STRBs:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2STRBs rGPR:$Rt, t2addrmode_so_reg:$addr, pred:$p)
      AsmString = "strb$\xFF\x05\x01 $\x01, $\xFF\x02\x0F";
      break;
    }
    return NULL;
  case ARM_t2STRHi12:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2STRHi12 rGPR:$Rt, t2addrmode_imm12:$addr, pred:$p)
      AsmString = "strh$\xFF\x04\x01 $\x01, $\xFF\x02\x0D";
      break;
    }
    return NULL;
  case ARM_t2STRHs:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0)) {
      // (t2STRHs rGPR:$Rt, t2addrmode_so_reg:$addr, pred:$p)
      AsmString = "strh$\xFF\x05\x01 $\x01, $\xFF\x02\x0F";
      break;
    }
    return NULL;
  case ARM_t2STRi12:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0)) {
      // (t2STRi12 GPR:$Rt, t2addrmode_imm12:$addr, pred:$p)
      AsmString = "str$\xFF\x04\x01 $\x01, $\xFF\x02\x0D";
      break;
    }
    return NULL;
  case ARM_t2STRs:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRRegClassID, 0)) {
      // (t2STRs GPR:$Rt, t2addrmode_so_reg:$addr, pred:$p)
      AsmString = "str$\xFF\x05\x01 $\x01, $\xFF\x02\x0F";
      break;
    }
    return NULL;
  case ARM_t2SUBS_PC_LR:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (t2SUBS_PC_LR 0, pred:$p)
      AsmString = "eret$\xFF\x02\x01";
      break;
    }
    return NULL;
  case ARM_t2SUBrr:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2)) {
      // (t2SUBrr GPRnopc:$Rd, GPRnopc:$Rn, rGPR:$Rm, pred:$p, cc_out:$s)
      AsmString = "sub$\xFF\x06\x02$\xFF\x04\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2SUBrs:
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 1)) {
      // (t2SUBrs GPRnopc:$Rd, GPRnopc:$Rn, t2_so_reg:$ShiftedRm, pred:$p, cc_out:$s)
      AsmString = "sub$\xFF\x07\x02$\xFF\x05\x01 $\x01, $\x02, $\xFF\x03\x0A";
      break;
    }
    if (MCInst_getNumOperands(MI) == 7 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (t2SUBrs GPRnopc:$Rdn, GPRnopc:$Rdn, t2_so_reg:$ShiftedRm, pred:$p, cc_out:$s)
      AsmString = "sub$\xFF\x07\x02$\xFF\x05\x01 $\x01, $\xFF\x03\x0A";
      break;
    }
    return NULL;
  case ARM_t2SXTAB:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (t2SXTAB rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, 0, pred:$p)
      AsmString = "sxtab$\xFF\x05\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2SXTAB16:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (t2SXTAB16 rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, 0, pred:$p)
      AsmString = "sxtab16$\xFF\x05\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2SXTAH:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (t2SXTAH rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, 0, pred:$p)
      AsmString = "sxtah$\xFF\x05\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2SXTB:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2SXTB rGPR:$Rd, rGPR:$Rm, rot_imm:$rot, pred:$p)
      AsmString = "sxtb$\xFF\x04\x01 $\x01, $\x02$\xFF\x03\x10";
      break;
    }
    return NULL;
  case ARM_t2SXTB16:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (t2SXTB16 rGPR:$Rd, rGPR:$Rm, 0, pred:$p)
      AsmString = "sxtb16$\xFF\x04\x01 $\x01, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2SXTB16 rGPR:$Rd, rGPR:$Rm, rot_imm:$rot, pred:$p)
      AsmString = "sxtb16$\xFF\x04\x01 $\x01, $\x02$\xFF\x03\x10";
      break;
    }
    return NULL;
  case ARM_t2SXTH:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2SXTH rGPR:$Rd, rGPR:$Rm, rot_imm:$rot, pred:$p)
      AsmString = "sxth$\xFF\x04\x01 $\x01, $\x02$\xFF\x03\x10";
      break;
    }
    return NULL;
  case ARM_t2TEQri:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (t2TEQri GPRnopc:$Rn, t2_so_imm:$imm, pred:$p)
      AsmString = "teq$\xFF\x03\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2TEQrr:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2TEQrr GPRnopc:$Rn, rGPR:$Rm, pred:$p)
      AsmString = "teq$\xFF\x03\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2TEQrs:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (t2TEQrs GPRnopc:$Rn, t2_so_reg:$shift, pred:$p)
      AsmString = "teq$\xFF\x04\x01 $\x01, $\xFF\x02\x0A";
      break;
    }
    return NULL;
  case ARM_t2TSTri:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (t2TSTri GPRnopc:$Rn, t2_so_imm:$imm, pred:$p)
      AsmString = "tst$\xFF\x03\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2TSTrr:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2TSTrr GPRnopc:$Rn, rGPR:$Rm, pred:$p)
      AsmString = "tst$\xFF\x03\x01 $\x01, $\x02";
      break;
    }
    return NULL;
  case ARM_t2TSTrs:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_GPRnopcRegClassID, 0)) {
      // (t2TSTrs GPRnopc:$Rn, t2_so_reg:$shift, pred:$p)
      AsmString = "tst$\xFF\x04\x01 $\x01, $\xFF\x02\x0A";
      break;
    }
    return NULL;
  case ARM_t2USAT:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (t2USAT rGPR:$Rd, imm0_31:$sat_imm, rGPR:$Rn, 0, pred:$p)
      AsmString = "usat$\xFF\x05\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2UXTAB:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (t2UXTAB rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, 0, pred:$p)
      AsmString = "uxtab$\xFF\x05\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2UXTAB16:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (t2UXTAB16 rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, 0, pred:$p)
      AsmString = "uxtab16$\xFF\x05\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2UXTAH:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (t2UXTAH rGPR:$Rd, rGPR:$Rn, rGPR:$Rm, 0, pred:$p)
      AsmString = "uxtah$\xFF\x05\x01 $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case ARM_t2UXTB:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2UXTB rGPR:$Rd, rGPR:$Rm, rot_imm:$rot, pred:$p)
      AsmString = "uxtb$\xFF\x04\x01 $\x01, $\x02$\xFF\x03\x10";
      break;
    }
    return NULL;
  case ARM_t2UXTB16:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (t2UXTB16 rGPR:$Rd, rGPR:$Rm, 0, pred:$p)
      AsmString = "uxtb16$\xFF\x04\x01 $\x01, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2UXTB16 rGPR:$Rd, rGPR:$Rm, rot_imm:$rot, pred:$p)
      AsmString = "uxtb16$\xFF\x04\x01 $\x01, $\x02$\xFF\x03\x10";
      break;
    }
    return NULL;
  case ARM_t2UXTH:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(ARM_rGPRRegClassID, 1)) {
      // (t2UXTH rGPR:$Rd, rGPR:$Rm, rot_imm:$rot, pred:$p)
      AsmString = "uxth$\xFF\x04\x01 $\x01, $\x02$\xFF\x03\x10";
      break;
    }
    return NULL;
  case ARM_tASRri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_tGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (tASRri tGPR:$Rdm, cc_out:$s, tGPR:$Rdm, imm_sr:$imm, pred:$p)
      AsmString = "asr$\xFF\x02\x02$\xFF\x05\x01 $\x01, $\xFF\x04\x0C";
      break;
    }
    return NULL;
  case ARM_tBKPT:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (tBKPT 0)
      AsmString = "bkpt";
      break;
    }
    return NULL;
  case ARM_tHINT:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (tHINT 0, pred:$p)
      AsmString = "nop$\xFF\x02\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 1) {
      // (tHINT 1, pred:$p)
      AsmString = "yield$\xFF\x02\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2) {
      // (tHINT 2, pred:$p)
      AsmString = "wfe$\xFF\x02\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 3) {
      // (tHINT 3, pred:$p)
      AsmString = "wfi$\xFF\x02\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 4) {
      // (tHINT 4, pred:$p)
      AsmString = "sev$\xFF\x02\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 5) {
      // (tHINT 5, pred:$p)
      AsmString = "sevl$\xFF\x02\x01";
      break;
    }
    return NULL;
  case ARM_tLDMIA:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_tGPRRegClassID, 0)) {
      // (tLDMIA tGPR:$Rn, pred:$p, reglist:$regs)
      AsmString = "ldm$\xFF\x02\x01 $\x01!, $\xFF\x04\x04";
      break;
    }
    return NULL;
  case ARM_tLSLri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_tGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (tLSLri tGPR:$Rdm, cc_out:$s, tGPR:$Rdm, imm0_31:$imm, pred:$p)
      AsmString = "lsl$\xFF\x02\x02$\xFF\x05\x01 $\x01, $\x04";
      break;
    }
    return NULL;
  case ARM_tLSRri:
    if (MCInst_getNumOperands(MI) == 6 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_tGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (tLSRri tGPR:$Rdm, cc_out:$s, tGPR:$Rdm, imm_sr:$imm, pred:$p)
      AsmString = "lsr$\xFF\x02\x02$\xFF\x05\x01 $\x01, $\xFF\x04\x0C";
      break;
    }
    return NULL;
  case ARM_tMOVi8:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_tGPRRegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == ARM_CPSR &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 14 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 0) {
      // (tMOVi8 tGPR:$Rdn, CPSR, imm0_255:$imm, 14, 0)
      AsmString = "movs $\x01, $\x03";
      break;
    }
    return NULL;
  case ARM_tMOVr:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == ARM_R8 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == ARM_R8 &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 14 &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (tMOVr R8, R8, 14, 0)
      AsmString = "nop";
      break;
    }
    return NULL;
  case ARM_tMUL:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_tGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_tGPRRegClassID, 2)) {
      // (tMUL tGPR:$Rdm, s_cc_out:$s, tGPR:$Rn, pred:$p)
      AsmString = "mul$\xFF\x02\x02$\xFF\x04\x01 $\x01, $\x03";
      break;
    }
    return NULL;
  case ARM_tRSB:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(ARM_tGPRRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(ARM_tGPRRegClassID, 2)) {
      // (tRSB tGPR:$Rd, s_cc_out:$s, tGPR:$Rm, pred:$p)
      AsmString = "neg$\xFF\x02\x02$\xFF\x04\x01 $\x01, $\x03";
      break;
    }
    return NULL;
  case ARM_tSUBspi:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == ARM_SP) {
      // (tSUBspi SP, t_imm0_508s4_neg:$imm, pred:$p)
      AsmString = "add$\xFF\x03\x01 sp, $\x02";
      break;
    }
    return NULL;
  }

  tmp = cs_strdup(AsmString);
  AsmMnem = tmp;
  for(AsmOps = tmp; *AsmOps; AsmOps++) {
    if (*AsmOps == ' ' || *AsmOps == '\t') {
      *AsmOps = '\0';
      AsmOps++;
      break;
    }
  }

  SStream_concat0(OS, AsmMnem);
  if (*AsmOps) {
    SStream_concat0(OS, "\t");
    for (c = AsmOps; *c; c++) {
      if (*c == '$') {
        c += 1;
        if (*c == (char)0xff) {
          c += 1;
          OpIdx = *c - 1;
          c += 1;
          PrintMethodIdx = *c - 1;
          printCustomAliasOperand(MI, OpIdx, PrintMethodIdx, OS);
        } else
          printOperand(MI, *c - 1, OS);
      } else {
        SStream_concat(OS, "%c", *c);
      }
    }
  }
  return tmp;
}

#endif // PRINT_ALIAS_INSTR
