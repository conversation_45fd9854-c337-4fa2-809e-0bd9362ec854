
// M6800/2 instructions
static const inst_page1 g_m6800_inst_page1_table[256] = {
	// 0x0x, inherent instructions
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_NOP, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_TAP, inh_hid, inh_hid },
	{ M680X_INS_TPA, inh_hid, inh_hid },
	{ M680X_INS_INX, inh_hid, inh_hid },
	{ M680X_INS_DEX, inh_hid, inh_hid },
	{ M680X_INS_CLV, inh_hid, inh_hid },
	{ M680X_INS_SEV, inh_hid, inh_hid },
	{ M680X_INS_CLC, inh_hid, inh_hid },
	{ M680X_INS_SEC, inh_hid, inh_hid },
	{ M680X_INS_CLI, inh_hid, inh_hid },
	{ M680X_INS_SEI, inh_hid, inh_hid },
	// 0x1x, inherent instructions
	{ M680X_INS_SBA, inh_hid, inh_hid },
	{ M680X_INS_CBA, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_TAB, inh_hid, inh_hid },
	{ M680X_INS_TBA, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_DAA, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ABA, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	// 0x2x, relative branch instructions
	{ M680X_INS_BRA, rel8_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_BHI, rel8_hid, inh_hid },
	{ M680X_INS_BLS, rel8_hid, inh_hid },
	{ M680X_INS_BCC, rel8_hid, inh_hid },
	{ M680X_INS_BCS, rel8_hid, inh_hid },
	{ M680X_INS_BNE, rel8_hid, inh_hid },
	{ M680X_INS_BEQ, rel8_hid, inh_hid },
	{ M680X_INS_BVC, rel8_hid, inh_hid },
	{ M680X_INS_BVS, rel8_hid, inh_hid },
	{ M680X_INS_BPL, rel8_hid, inh_hid },
	{ M680X_INS_BMI, rel8_hid, inh_hid },
	{ M680X_INS_BGE, rel8_hid, inh_hid },
	{ M680X_INS_BLT, rel8_hid, inh_hid },
	{ M680X_INS_BGT, rel8_hid, inh_hid },
	{ M680X_INS_BLE, rel8_hid, inh_hid },
	// 0x3x, inherent instructions
	{ M680X_INS_TSX, inh_hid, inh_hid },
	{ M680X_INS_INS, inh_hid, inh_hid },
	{ M680X_INS_PULA, inh_hid, inh_hid },
	{ M680X_INS_PULB, inh_hid, inh_hid },
	{ M680X_INS_DES, inh_hid, inh_hid },
	{ M680X_INS_TXS, inh_hid, inh_hid },
	{ M680X_INS_PSHA, inh_hid, inh_hid },
	{ M680X_INS_PSHB, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_RTS, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_RTI, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_WAI, inh_hid, inh_hid },
	{ M680X_INS_SWI, inh_hid, inh_hid },
	// 0x4x, Register A instructions
	{ M680X_INS_NEGA, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_COMA, inh_hid, inh_hid },
	{ M680X_INS_LSRA, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_RORA, inh_hid, inh_hid },
	{ M680X_INS_ASRA, inh_hid, inh_hid },
	{ M680X_INS_ASLA, inh_hid, inh_hid },
	{ M680X_INS_ROLA, inh_hid, inh_hid },
	{ M680X_INS_DECA, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_INCA, inh_hid, inh_hid },
	{ M680X_INS_TSTA, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_CLRA, inh_hid, inh_hid },
	// 0x5x, Register B instructions
	{ M680X_INS_NEGB, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_COMB, inh_hid, inh_hid },
	{ M680X_INS_LSRB, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_RORB, inh_hid, inh_hid },
	{ M680X_INS_ASRB, inh_hid, inh_hid },
	{ M680X_INS_ASLB, inh_hid, inh_hid },
	{ M680X_INS_ROLB, inh_hid, inh_hid },
	{ M680X_INS_DECB, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_INCB, inh_hid, inh_hid },
	{ M680X_INS_TSTB, inh_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_CLRB, inh_hid, inh_hid },
	// 0x6x, indexed instructions
	{ M680X_INS_NEG, idxX_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_COM, idxX_hid, inh_hid },
	{ M680X_INS_LSR, idxX_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ROR, idxX_hid, inh_hid },
	{ M680X_INS_ASR, idxX_hid, inh_hid },
	{ M680X_INS_ASL, idxX_hid, inh_hid },
	{ M680X_INS_ROL, idxX_hid, inh_hid },
	{ M680X_INS_DEC, idxX_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_INC, idxX_hid, inh_hid },
	{ M680X_INS_TST, idxX_hid, inh_hid },
	{ M680X_INS_JMP, idxX_hid, inh_hid },
	{ M680X_INS_CLR, idxX_hid, inh_hid },
	// 0x7x, extended instructions
	{ M680X_INS_NEG, ext_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_COM, ext_hid, inh_hid },
	{ M680X_INS_LSR, ext_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ROR, ext_hid, inh_hid },
	{ M680X_INS_ASR, ext_hid, inh_hid },
	{ M680X_INS_ASL, ext_hid, inh_hid },
	{ M680X_INS_ROL, ext_hid, inh_hid },
	{ M680X_INS_DEC, ext_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_INC, ext_hid, inh_hid },
	{ M680X_INS_TST, ext_hid, inh_hid },
	{ M680X_INS_JMP, ext_hid, inh_hid },
	{ M680X_INS_CLR, ext_hid, inh_hid },
	// 0x8x, immediate instructions with Register A,X,S
	{ M680X_INS_SUBA, imm8_hid, inh_hid },
	{ M680X_INS_CMPA, imm8_hid, inh_hid },
	{ M680X_INS_SBCA, imm8_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ANDA, imm8_hid, inh_hid },
	{ M680X_INS_BITA, imm8_hid, inh_hid },
	{ M680X_INS_LDAA, imm8_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_EORA, imm8_hid, inh_hid },
	{ M680X_INS_ADCA, imm8_hid, inh_hid },
	{ M680X_INS_ORAA, imm8_hid, inh_hid },
	{ M680X_INS_ADDA, imm8_hid, inh_hid },
	{ M680X_INS_CPX, imm16_hid, inh_hid },
	{ M680X_INS_BSR, rel8_hid, inh_hid },
	{ M680X_INS_LDS, imm16_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	// 0x9x, direct instructions with register A,X,S
	{ M680X_INS_SUBA, dir_hid, inh_hid },
	{ M680X_INS_CMPA, dir_hid, inh_hid },
	{ M680X_INS_SBCA, dir_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ANDA, dir_hid, inh_hid },
	{ M680X_INS_BITA, dir_hid, inh_hid },
	{ M680X_INS_LDAA, dir_hid, inh_hid },
	{ M680X_INS_STAA, dir_hid, inh_hid },
	{ M680X_INS_EORA, dir_hid, inh_hid },
	{ M680X_INS_ADCA, dir_hid, inh_hid },
	{ M680X_INS_ORAA, dir_hid, inh_hid },
	{ M680X_INS_ADDA, dir_hid, inh_hid },
	{ M680X_INS_CPX, dir_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_LDS, dir_hid, inh_hid },
	{ M680X_INS_STS, dir_hid, inh_hid },
	// 0xAx, indexed instructions with Register A,X
	{ M680X_INS_SUBA, idxX_hid, inh_hid },
	{ M680X_INS_CMPA, idxX_hid, inh_hid },
	{ M680X_INS_SBCA, idxX_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ANDA, idxX_hid, inh_hid },
	{ M680X_INS_BITA, idxX_hid, inh_hid },
	{ M680X_INS_LDAA, idxX_hid, inh_hid },
	{ M680X_INS_STAA, idxX_hid, inh_hid },
	{ M680X_INS_EORA, idxX_hid, inh_hid },
	{ M680X_INS_ADCA, idxX_hid, inh_hid },
	{ M680X_INS_ORAA, idxX_hid, inh_hid },
	{ M680X_INS_ADDA, idxX_hid, inh_hid },
	{ M680X_INS_CPX, idxX_hid, inh_hid },
	{ M680X_INS_JSR, idxX_hid, inh_hid },
	{ M680X_INS_LDS, idxX_hid, inh_hid },
	{ M680X_INS_STS, idxX_hid, inh_hid },
	// 0xBx, extended instructions with register A,X,S
	{ M680X_INS_SUBA, ext_hid, inh_hid },
	{ M680X_INS_CMPA, ext_hid, inh_hid },
	{ M680X_INS_SBCA, ext_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ANDA, ext_hid, inh_hid },
	{ M680X_INS_BITA, ext_hid, inh_hid },
	{ M680X_INS_LDAA, ext_hid, inh_hid },
	{ M680X_INS_STAA, ext_hid, inh_hid },
	{ M680X_INS_EORA, ext_hid, inh_hid },
	{ M680X_INS_ADCA, ext_hid, inh_hid },
	{ M680X_INS_ORAA, ext_hid, inh_hid },
	{ M680X_INS_ADDA, ext_hid, inh_hid },
	{ M680X_INS_CPX, ext_hid, inh_hid },
	{ M680X_INS_JSR, ext_hid, inh_hid },
	{ M680X_INS_LDS, ext_hid, inh_hid },
	{ M680X_INS_STS, ext_hid, inh_hid },
	// 0xCx, immediate instructions with register B,X
	{ M680X_INS_SUBB, imm8_hid, inh_hid },
	{ M680X_INS_CMPB, imm8_hid, inh_hid },
	{ M680X_INS_SBCB, imm8_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ANDB, imm8_hid, inh_hid },
	{ M680X_INS_BITB, imm8_hid, inh_hid },
	{ M680X_INS_LDAB, imm8_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_EORB, imm8_hid, inh_hid },
	{ M680X_INS_ADCB, imm8_hid, inh_hid },
	{ M680X_INS_ORAB, imm8_hid, inh_hid },
	{ M680X_INS_ADDB, imm8_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_LDX, imm16_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	// 0xDx direct instructions with register B,X
	{ M680X_INS_SUBB, dir_hid, inh_hid },
	{ M680X_INS_CMPB, dir_hid, inh_hid },
	{ M680X_INS_SBCB, dir_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ANDB, dir_hid, inh_hid },
	{ M680X_INS_BITB, dir_hid, inh_hid },
	{ M680X_INS_LDAB, dir_hid, inh_hid },
	{ M680X_INS_STAB, dir_hid, inh_hid },
	{ M680X_INS_EORB, dir_hid, inh_hid },
	{ M680X_INS_ADCB, dir_hid, inh_hid },
	{ M680X_INS_ORAB, dir_hid, inh_hid },
	{ M680X_INS_ADDB, dir_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_LDX, dir_hid, inh_hid },
	{ M680X_INS_STX, dir_hid, inh_hid },
	// 0xEx, indexed instruction with register B,X
	{ M680X_INS_SUBB, idxX_hid, inh_hid },
	{ M680X_INS_CMPB, idxX_hid, inh_hid },
	{ M680X_INS_SBCB, idxX_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ANDB, idxX_hid, inh_hid },
	{ M680X_INS_BITB, idxX_hid, inh_hid },
	{ M680X_INS_LDAB, idxX_hid, inh_hid },
	{ M680X_INS_STAB, idxX_hid, inh_hid },
	{ M680X_INS_EORB, idxX_hid, inh_hid },
	{ M680X_INS_ADCB, idxX_hid, inh_hid },
	{ M680X_INS_ORAB, idxX_hid, inh_hid },
	{ M680X_INS_ADDB, idxX_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_LDX, idxX_hid, inh_hid },
	{ M680X_INS_STX, idxX_hid, inh_hid },
	// 0xFx, extended instructions with register B,U
	{ M680X_INS_SUBB, ext_hid, inh_hid },
	{ M680X_INS_CMPB, ext_hid, inh_hid },
	{ M680X_INS_SBCB, ext_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ANDB, ext_hid, inh_hid },
	{ M680X_INS_BITB, ext_hid, inh_hid },
	{ M680X_INS_LDAB, ext_hid, inh_hid },
	{ M680X_INS_STAB, ext_hid, inh_hid },
	{ M680X_INS_EORB, ext_hid, inh_hid },
	{ M680X_INS_ADCB, ext_hid, inh_hid },
	{ M680X_INS_ORAB, ext_hid, inh_hid },
	{ M680X_INS_ADDB, ext_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_LDX, ext_hid, inh_hid },
	{ M680X_INS_STX, ext_hid, inh_hid },
};

