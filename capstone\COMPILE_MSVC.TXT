This documentation explains how to compile <PERSON>stone on Windows using
Microsoft Visual Studio version 2010 or newer.

To compile Capstone on *nix, see COMPILE.TXT

To compile Capstone with C<PERSON>ake, see COMPILE_CMAKE.TXT

                        *-*-*-*-*-*

<PERSON><PERSON> requires no prerequisite packages with default configurations, so it is
easy to compile & install. Open the Visual Studio solution "msvc/capstone.sln"
and follow the instructions below.

NOTE: This requires Visual Studio 2010 or newer versions.

If you wish to embed <PERSON>stone in a kernel driver, Visual Studio 2013 or newer
versions, and Windows Driver Kit 8.1 Update 1 or newer versions are required.


(0) Tailor Capstone to your need.

  Out of 9 archtitectures supported by Capstone (Arm, Arm64, M68K, Mips, PPC,
  Sparc, SystemZ, X86 & XCore), if you just need several selected archs, choose
  the ones you want to compile in by opening Visual Studio solution "msvc\capstone.sln",
  then directly editing the projects "capstone_static" & "capstone_dll" for static
  and dynamic libraries, respectively. This must be done before going to the next
  steps.

  In VisualStudio interface, modify the preprocessor definitions via
  "Project Properties" -> "Configuration Properties" -> "C/C++" -> "Preprocessor"
  to customize Capstone library, as followings.

  - CAPSTONE_HAS_ARM: support ARM. Delete this to remove ARM support.
  - CAPSTONE_HAS_ARM64: support ARM64. Delete this to remove ARM64 support.
  - CAPSTONE_HAS_M68K: support M68K. Delete this to remove M68K support.
  - CAPSTONE_HAS_MIPS: support Mips. Delete this to remove Mips support.
  - CAPSTONE_HAS_PPC: support PPC. Delete this to remove PPC support.
  - CAPSTONE_HAS_SPARC: support Sparc. Delete this to remove Sparc support.
  - CAPSTONE_HAS_SYSZ: support SystemZ. Delete this to remove SystemZ support.
  - CAPSTONE_HAS_X86: support X86. Delete this to remove X86 support.
  - CAPSTONE_HAS_XCORE: support XCore. Delete this to remove XCore support.

  By default, all 9 architectures are compiled in.


  Besides, Capstone also allows some more customization via following macros.

  - CAPSTONE_USE_SYS_DYN_MEM: delete this to use your own dynamic memory management.
  - CAPSTONE_DIET_NO: rename this to "CAPSTONE_DIET" to make the binaries more compact.
  - CAPSTONE_X86_REDUCE_NO: rename this to "CAPSTONE_X86_REDUCE" to make X86 binary smaller.
  - CAPSTONE_X86_ATT_DISABLE_NO: rename this to "CAPSTONE_X86_ATT_DISABLE" to disable
    AT&T syntax on x86.

  By default, Capstone use system dynamic memory management, and both DIET and X86_REDUCE
  modes are disable.


  For each option, refer to docs/README for more details.



(1) Compile from source on Windows with Visual Studio

  - Choose the configuration and the platform you want: Release/Debug & Win32/Win64.
  - Build only the libraries, or the libraries along with all the tests.
  - "capstone_static_winkernel" is for compiling Capstone for a driver and
    "test_winkernel" is a test for a driver, and those are excluded from build by
    default. To compile them, open the Configuration Manager through the [Build]
    menu and check "Build" check boxes for those project.



(2) You can make sure the prior steps successfully worked by launching one of the
  testing binary (test*.exe).

  The testing binary for a driver "test_winkernel.sys" is made up of all tests for
  supported architectures configured with the step (0) along side its own tests.
  Below explains a procedure to run the test driver and check test results.

  On the x64 platform, the test signing mode has to be enabled to install the test
  driver. To do it, open the command prompt with the administrator privileges and
  type the following command, and then restart the system to activate the change:

      >bcdedit /set testsigning on

  Test results from the test driver is sent to kernel debug buffer. In order to
  see those results, download DebugView and run it with the administrator
  privileges, then check [Capture Kernel] through the [Capture] menu.

  DebugView: https://technet.microsoft.com/en-us/sysinternals/debugview.aspx

  To install and uninstall the driver, use the 'sc' command. For installing and
  executing test_winkernel.sys, execute the following commands with the
  administrator privileges:

      >sc create test_winkernel type= kernel binPath= <full path to test_winkernel.sys>
      [SC] CreateService SUCCESS

      >sc start test_winkernel
      [SC] StartService FAILED 995:

      The I/O operation has been aborted because of either a thread exit or an application request.

  To uninstall the driver, execute the following commands with the administrator
  privileges:

      >sc delete test_winkernel
      >bcdedit /deletevalue testsigning
