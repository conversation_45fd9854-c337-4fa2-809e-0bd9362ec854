#!/usr/bin/env python

# Capstone Python bindings, by <PERSON><PERSON><PERSON> <<EMAIL>>
from __future__ import print_function
import sys
from capstone import *

all_tests = (
        # arch, mode, syntax, address, hexcode, expected output
        # issue 456 https://github.com/aquynh/capstone/issues/456

        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xfc16, b"\xE8\x35\x64", "call 0x604e"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123fc1b, b"\x66\xE8\x35\x64", "call 0x6054"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x9123fc1b, b"\x66\xE8\x35\x64", "call 0x6054"),

        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xfc26, b"\xE9\x35\x64", "jmp 0x605e"),

        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xfff6, b"\x66\xE9\x35\x64\x93\x53", "jmp 0x53946431"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123fff1, b"\xE9\x35\x64\x93\x53", "jmp 0xe4b7642b"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123fff1, b"\xE9\x35\x64\x93\x53", "jmp 0x64e4b7642b"),

        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xe8\x35\x64\x93\x53", "call 0x5394641c"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xe8\x35\x64", "call 0x641a"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xe9\x35\x64", "jmp 0x641a"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xe9\x35\x64\x93\x53", "jmp 0x5394641c"),

        # AT&T syntax
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_ATT, 0xfc16, b"\xE8\x35\x64", "callw 0x604e"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_ATT, 0x9123fc1b, b"\x66\xE8\x35\x64", "callw 0x6054"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_ATT, 0x9123fc1b, b"\x66\xE8\x35\x64", "callw 0x6054"),

        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_ATT, 0xfc26, b"\xE9\x35\x64", "jmp 0x605e"),

        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_ATT, 0xfff6, b"\x66\xE9\x35\x64\x93\x53", "jmp 0x53946431"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_ATT, 0x9123fff1, b"\xE9\x35\x64\x93\x53", "jmp 0xe4b7642b"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_ATT, 0x649123fff1, b"\xE9\x35\x64\x93\x53", "jmp 0x64e4b7642b"),

        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_ATT, 0xffe1, b"\x66\xe8\x35\x64\x93\x53", "calll 0x5394641c"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_ATT, 0x649123ffe1, b"\x66\xe8\x35\x64", "callw 0x641a"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_ATT, 0x649123ffe1, b"\x66\xe9\x35\x64", "jmp 0x641a"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_ATT, 0xffe1, b"\x66\xe9\x35\x64\x93\x53", "jmp 0x5394641c"),

        # issue 452 https://github.com/aquynh/capstone/issues/452
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x6C", "insb byte ptr es:[di], dx"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x6D", "insw word ptr es:[di], dx"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x6E", "outsb dx, byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x6F", "outsw dx, word ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xA4", "movsb byte ptr es:[di], byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xA5", "movsw word ptr es:[di], word ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xA6", "cmpsb byte ptr [si], byte ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xA7", "cmpsw word ptr [si], word ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xAA", "stosb byte ptr es:[di], al"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xAB", "stosw word ptr es:[di], ax"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xAC", "lodsb al, byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xAD", "lodsw ax, word ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xAE", "scasb al, byte ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xAF", "scasw ax, word ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x6C", "insb byte ptr es:[di], dx"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x6D", "insd dword ptr es:[di], dx"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x6E", "outsb dx, byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x6F", "outsd dx, dword ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xA4", "movsb byte ptr es:[di], byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xA5", "movsd dword ptr es:[di], dword ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xA6", "cmpsb byte ptr [si], byte ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xA7", "cmpsd dword ptr [si], dword ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xAA", "stosb byte ptr es:[di], al"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xAB", "stosd dword ptr es:[di], eax"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xAC", "lodsb al, byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xAD", "lodsd eax, dword ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xAE", "scasb al, byte ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xAF", "scasd eax, dword ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\x6C", "insb byte ptr es:[edi], dx"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\x6D", "insw word ptr es:[edi], dx"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\x6E", "outsb dx, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\x6F", "outsw dx, word ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xA4", "movsb byte ptr es:[edi], byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xA5", "movsw word ptr es:[edi], word ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xA6", "cmpsb byte ptr [esi], byte ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xA7", "cmpsw word ptr [esi], word ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xAA", "stosb byte ptr es:[edi], al"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xAB", "stosw word ptr es:[edi], ax"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xAC", "lodsb al, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xAD", "lodsw ax, word ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xAE", "scasb al, byte ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xAF", "scasw ax, word ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF2\x6C", "repne insb byte ptr es:[di], dx"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF2\x6D", "repne insw word ptr es:[di], dx"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF2\x6E", "repne outsb dx, byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF2\x6F", "repne outsw dx, word ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF2\xA4", "repne movsb byte ptr es:[di], byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF2\xA5", "repne movsw word ptr es:[di], word ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF2\xA6", "repne cmpsb byte ptr [si], byte ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF2\xA7", "repne cmpsw word ptr [si], word ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF2\xAA", "repne stosb byte ptr es:[di], al"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF2\xAB", "repne stosw word ptr es:[di], ax"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF2\xAC", "repne lodsb al, byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF2\xAD", "repne lodsw ax, word ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF2\xAE", "repne scasb al, byte ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF2\xAF", "repne scasw ax, word ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF3\x6C", "rep insb byte ptr es:[di], dx"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF3\x6D", "rep insw word ptr es:[di], dx"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF3\x6E", "rep outsb dx, byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF3\x6F", "rep outsw dx, word ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF3\xA4", "rep movsb byte ptr es:[di], byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF3\xA5", "rep movsw word ptr es:[di], word ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF3\xA6", "repe cmpsb byte ptr [si], byte ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF3\xA7", "repe cmpsw word ptr [si], word ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF3\xAA", "rep stosb byte ptr es:[di], al"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF3\xAB", "rep stosw word ptr es:[di], ax"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF3\xAC", "rep lodsb al, byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF3\xAD", "rep lodsw ax, word ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF3\xAE", "repe scasb al, byte ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\xF3\xAF", "repe scasw ax, word ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\x6C", "insb byte ptr es:[edi], dx"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\x6D", "insd dword ptr es:[edi], dx"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\x6E", "outsb dx, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\x6F", "outsd dx, dword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xA4", "movsb byte ptr es:[edi], byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xA5", "movsd dword ptr es:[edi], dword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xA6", "cmpsb byte ptr [esi], byte ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xA7", "cmpsd dword ptr [esi], dword ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xAA", "stosb byte ptr es:[edi], al"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xAB", "stosd dword ptr es:[edi], eax"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xAC", "lodsb al, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xAD", "lodsd eax, dword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xAE", "scasb al, byte ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xAF", "scasd eax, dword ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF2\x6C", "repne insb byte ptr es:[di], dx"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF2\x6D", "repne insd dword ptr es:[di], dx"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF2\x6E", "repne outsb dx, byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF2\x6F", "repne outsd dx, dword ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF2\xA4", "repne movsb byte ptr es:[di], byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF2\xA5", "repne movsd dword ptr es:[di], dword ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF2\xA6", "repne cmpsb byte ptr [si], byte ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF2\xA7", "repne cmpsd dword ptr [si], dword ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF2\xAA", "repne stosb byte ptr es:[di], al"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF2\xAB", "repne stosd dword ptr es:[di], eax"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF2\xAC", "repne lodsb al, byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF2\xAD", "repne lodsd eax, dword ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF2\xAE", "repne scasb al, byte ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF2\xAF", "repne scasd eax, dword ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF3\x6C", "rep insb byte ptr es:[di], dx"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF3\x6D", "rep insd dword ptr es:[di], dx"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF3\x6E", "rep outsb dx, byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF3\x6F", "rep outsd dx, dword ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF3\xA4", "rep movsb byte ptr es:[di], byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF3\xA5", "rep movsd dword ptr es:[di], dword ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF3\xA6", "repe cmpsb byte ptr [si], byte ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF3\xA7", "repe cmpsd dword ptr [si], dword ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF3\xAA", "rep stosb byte ptr es:[di], al"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF3\xAB", "rep stosd dword ptr es:[di], eax"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF3\xAC", "rep lodsb al, byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF3\xAD", "rep lodsd eax, dword ptr [si]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF3\xAE", "repe scasb al, byte ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\xF3\xAF", "repe scasd eax, dword ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF2\x6C", "repne insb byte ptr es:[edi], dx"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF2\x6D", "repne insw word ptr es:[edi], dx"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF2\x6E", "repne outsb dx, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF2\x6F", "repne outsw dx, word ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF2\xA4", "repne movsb byte ptr es:[edi], byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF2\xA5", "repne movsw word ptr es:[edi], word ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF2\xA6", "repne cmpsb byte ptr [esi], byte ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF2\xA7", "repne cmpsw word ptr [esi], word ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF2\xAA", "repne stosb byte ptr es:[edi], al"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF2\xAB", "repne stosw word ptr es:[edi], ax"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF2\xAC", "repne lodsb al, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF2\xAD", "repne lodsw ax, word ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF2\xAE", "repne scasb al, byte ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF2\xAF", "repne scasw ax, word ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF3\x6C", "rep insb byte ptr es:[edi], dx"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF3\x6D", "rep insw word ptr es:[edi], dx"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF3\x6E", "rep outsb dx, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF3\x6F", "rep outsw dx, word ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF3\xA4", "rep movsb byte ptr es:[edi], byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF3\xA5", "rep movsw word ptr es:[edi], word ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF3\xA6", "repe cmpsb byte ptr [esi], byte ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF3\xA7", "repe cmpsw word ptr [esi], word ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF3\xAA", "rep stosb byte ptr es:[edi], al"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF3\xAB", "rep stosw word ptr es:[edi], ax"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF3\xAC", "rep lodsb al, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF3\xAD", "rep lodsw ax, word ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF3\xAE", "repe scasb al, byte ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x67\xF3\xAF", "repe scasw ax, word ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF2\x6C", "repne insb byte ptr es:[edi], dx"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF2\x6D", "repne insd dword ptr es:[edi], dx"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF2\x6E", "repne outsb dx, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF2\x6F", "repne outsd dx, dword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF2\xA4", "repne movsb byte ptr es:[edi], byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF2\xA5", "repne movsd dword ptr es:[edi], dword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF2\xA6", "repne cmpsb byte ptr [esi], byte ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF2\xA7", "repne cmpsd dword ptr [esi], dword ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF2\xAA", "repne stosb byte ptr es:[edi], al"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF2\xAB", "repne stosd dword ptr es:[edi], eax"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF2\xAC", "repne lodsb al, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF2\xAD", "repne lodsd eax, dword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF2\xAE", "repne scasb al, byte ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF2\xAF", "repne scasd eax, dword ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF3\x6C", "rep insb byte ptr es:[edi], dx"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF3\x6D", "rep insd dword ptr es:[edi], dx"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF3\x6E", "rep outsb dx, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF3\x6F", "rep outsd dx, dword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF3\xA4", "rep movsb byte ptr es:[edi], byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF3\xA5", "rep movsd dword ptr es:[edi], dword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF3\xA6", "repe cmpsb byte ptr [esi], byte ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF3\xA7", "repe cmpsd dword ptr [esi], dword ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF3\xAA", "rep stosb byte ptr es:[edi], al"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF3\xAB", "rep stosd dword ptr es:[edi], eax"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF3\xAC", "rep lodsb al, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF3\xAD", "rep lodsd eax, dword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF3\xAE", "repe scasb al, byte ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x67\xF3\xAF", "repe scasd eax, dword ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x6C", "insb byte ptr es:[edi], dx"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x6D", "insd dword ptr es:[edi], dx"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x6E", "outsb dx, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x6F", "outsd dx, dword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xA4", "movsb byte ptr es:[edi], byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xA5", "movsd dword ptr es:[edi], dword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xA6", "cmpsb byte ptr [esi], byte ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xA7", "cmpsd dword ptr [esi], dword ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xAA", "stosb byte ptr es:[edi], al"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xAB", "stosd dword ptr es:[edi], eax"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xAC", "lodsb al, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xAD", "lodsd eax, dword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xAE", "scasb al, byte ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xAF", "scasd eax, dword ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x6C", "insb byte ptr es:[edi], dx"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x6D", "insw word ptr es:[edi], dx"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x6E", "outsb dx, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x6F", "outsw dx, word ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xA4", "movsb byte ptr es:[edi], byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xA5", "movsw word ptr es:[edi], word ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xA6", "cmpsb byte ptr [esi], byte ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xA7", "cmpsw word ptr [esi], word ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xAA", "stosb byte ptr es:[edi], al"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xAB", "stosw word ptr es:[edi], ax"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xAC", "lodsb al, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xAD", "lodsw ax, word ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xAE", "scasb al, byte ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xAF", "scasw ax, word ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\x6C", "insb byte ptr es:[di], dx"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\x6D", "insd dword ptr es:[di], dx"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\x6E", "outsb dx, byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\x6F", "outsd dx, dword ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xA4", "movsb byte ptr es:[di], byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xA5", "movsd dword ptr es:[di], dword ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xA6", "cmpsb byte ptr [si], byte ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xA7", "cmpsd dword ptr [si], dword ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xAA", "stosb byte ptr es:[di], al"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xAB", "stosd dword ptr es:[di], eax"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xAC", "lodsb al, byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xAD", "lodsd eax, dword ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xAE", "scasb al, byte ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xAF", "scasd eax, dword ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF2\x6C", "repne insb byte ptr es:[edi], dx"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF2\x6D", "repne insd dword ptr es:[edi], dx"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF2\x6E", "repne outsb dx, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF2\x6F", "repne outsd dx, dword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF2\xA4", "repne movsb byte ptr es:[edi], byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF2\xA5", "repne movsd dword ptr es:[edi], dword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF2\xA6", "repne cmpsb byte ptr [esi], byte ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF2\xA7", "repne cmpsd dword ptr [esi], dword ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF2\xAA", "repne stosb byte ptr es:[edi], al"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF2\xAB", "repne stosd dword ptr es:[edi], eax"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF2\xAC", "repne lodsb al, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF2\xAD", "repne lodsd eax, dword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF2\xAE", "repne scasb al, byte ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF2\xAF", "repne scasd eax, dword ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF3\x6C", "rep insb byte ptr es:[edi], dx"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF3\x6D", "rep insd dword ptr es:[edi], dx"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF3\x6E", "rep outsb dx, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF3\x6F", "rep outsd dx, dword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF3\xA4", "rep movsb byte ptr es:[edi], byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF3\xA5", "rep movsd dword ptr es:[edi], dword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF3\xA6", "repe cmpsb byte ptr [esi], byte ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF3\xA7", "repe cmpsd dword ptr [esi], dword ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF3\xAA", "rep stosb byte ptr es:[edi], al"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF3\xAB", "rep stosd dword ptr es:[edi], eax"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF3\xAC", "rep lodsb al, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF3\xAD", "rep lodsd eax, dword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF3\xAE", "repe scasb al, byte ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\xF3\xAF", "repe scasd eax, dword ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\x6C", "insb byte ptr es:[di], dx"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\x6D", "insw word ptr es:[di], dx"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\x6E", "outsb dx, byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\x6F", "outsw dx, word ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xA4", "movsb byte ptr es:[di], byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xA5", "movsw word ptr es:[di], word ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xA6", "cmpsb byte ptr [si], byte ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xA7", "cmpsw word ptr [si], word ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xAA", "stosb byte ptr es:[di], al"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xAB", "stosw word ptr es:[di], ax"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xAC", "lodsb al, byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xAD", "lodsw ax, word ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xAE", "scasb al, byte ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xAF", "scasw ax, word ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF2\x6C", "repne insb byte ptr es:[edi], dx"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF2\x6D", "repne insw word ptr es:[edi], dx"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF2\x6E", "repne outsb dx, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF2\x6F", "repne outsw dx, word ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF2\xA4", "repne movsb byte ptr es:[edi], byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF2\xA5", "repne movsw word ptr es:[edi], word ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF2\xA6", "repne cmpsb byte ptr [esi], byte ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF2\xA7", "repne cmpsw word ptr [esi], word ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF2\xAA", "repne stosb byte ptr es:[edi], al"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF2\xAB", "repne stosw word ptr es:[edi], ax"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF2\xAC", "repne lodsb al, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF2\xAD", "repne lodsw ax, word ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF2\xAE", "repne scasb al, byte ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF2\xAF", "repne scasw ax, word ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF3\x6C", "rep insb byte ptr es:[edi], dx"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF3\x6D", "rep insw word ptr es:[edi], dx"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF3\x6E", "rep outsb dx, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF3\x6F", "rep outsw dx, word ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF3\xA4", "rep movsb byte ptr es:[edi], byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF3\xA5", "rep movsw word ptr es:[edi], word ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF3\xA6", "repe cmpsb byte ptr [esi], byte ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF3\xA7", "repe cmpsw word ptr [esi], word ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF3\xAA", "rep stosb byte ptr es:[edi], al"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF3\xAB", "rep stosw word ptr es:[edi], ax"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF3\xAC", "rep lodsb al, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF3\xAD", "rep lodsw ax, word ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF3\xAE", "repe scasb al, byte ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\xF3\xAF", "repe scasw ax, word ptr es:[edi]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF2\x6C", "repne insb byte ptr es:[di], dx"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF2\x6D", "repne insd dword ptr es:[di], dx"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF2\x6E", "repne outsb dx, byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF2\x6F", "repne outsd dx, dword ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF2\xA4", "repne movsb byte ptr es:[di], byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF2\xA5", "repne movsd dword ptr es:[di], dword ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF2\xA6", "repne cmpsb byte ptr [si], byte ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF2\xA7", "repne cmpsd dword ptr [si], dword ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF2\xAA", "repne stosb byte ptr es:[di], al"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF2\xAB", "repne stosd dword ptr es:[di], eax"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF2\xAC", "repne lodsb al, byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF2\xAD", "repne lodsd eax, dword ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF2\xAE", "repne scasb al, byte ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF2\xAF", "repne scasd eax, dword ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF3\x6C", "rep insb byte ptr es:[di], dx"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF3\x6D", "rep insd dword ptr es:[di], dx"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF3\x6E", "rep outsb dx, byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF3\x6F", "rep outsd dx, dword ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF3\xA4", "rep movsb byte ptr es:[di], byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF3\xA5", "rep movsd dword ptr es:[di], dword ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF3\xA6", "repe cmpsb byte ptr [si], byte ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF3\xA7", "repe cmpsd dword ptr [si], dword ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF3\xAA", "rep stosb byte ptr es:[di], al"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF3\xAB", "rep stosd dword ptr es:[di], eax"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF3\xAC", "rep lodsb al, byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF3\xAD", "rep lodsd eax, dword ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF3\xAE", "repe scasb al, byte ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x67\xF3\xAF", "repe scasd eax, dword ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF2\x6C", "repne insb byte ptr es:[di], dx"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF2\x6D", "repne insw word ptr es:[di], dx"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF2\x6E", "repne outsb dx, byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF2\x6F", "repne outsw dx, word ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF2\xA4", "repne movsb byte ptr es:[di], byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF2\xA5", "repne movsw word ptr es:[di], word ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF2\xA6", "repne cmpsb byte ptr [si], byte ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF2\xA7", "repne cmpsw word ptr [si], word ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF2\xAA", "repne stosb byte ptr es:[di], al"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF2\xAB", "repne stosw word ptr es:[di], ax"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF2\xAC", "repne lodsb al, byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF2\xAD", "repne lodsw ax, word ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF2\xAE", "repne scasb al, byte ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF2\xAF", "repne scasw ax, word ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF3\x6C", "rep insb byte ptr es:[di], dx"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF3\x6D", "rep insw word ptr es:[di], dx"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF3\x6E", "rep outsb dx, byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF3\x6F", "rep outsw dx, word ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF3\xA4", "rep movsb byte ptr es:[di], byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF3\xA5", "rep movsw word ptr es:[di], word ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF3\xA6", "repe cmpsb byte ptr [si], byte ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF3\xA7", "repe cmpsw word ptr [si], word ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF3\xAA", "rep stosb byte ptr es:[di], al"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF3\xAB", "rep stosw word ptr es:[di], ax"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF3\xAC", "rep lodsb al, byte ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF3\xAD", "rep lodsw ax, word ptr [si]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF3\xAE", "repe scasb al, byte ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x67\xF3\xAF", "repe scasw ax, word ptr es:[di]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x6C", "insb byte ptr [rdi], dx"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x6D", "insd dword ptr [rdi], dx"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x6E", "outsb dx, byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x6F", "outsd dx, dword ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xA4", "movsb byte ptr [rdi], byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xA5", "movsd dword ptr [rdi], dword ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xA6", "cmpsb byte ptr [rsi], byte ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xA7", "cmpsd dword ptr [rsi], dword ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xAA", "stosb byte ptr [rdi], al"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xAB", "stosd dword ptr [rdi], eax"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xAC", "lodsb al, byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xAD", "lodsd eax, dword ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xAE", "scasb al, byte ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xAF", "scasd eax, dword ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x6C", "insb byte ptr [rdi], dx"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x6D", "insw word ptr [rdi], dx"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x6E", "outsb dx, byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x6F", "outsw dx, word ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xA4", "movsb byte ptr [rdi], byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xA5", "movsw word ptr [rdi], word ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xA6", "cmpsb byte ptr [rsi], byte ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xA7", "cmpsw word ptr [rsi], word ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xAA", "stosb byte ptr [rdi], al"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xAB", "stosw word ptr [rdi], ax"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xAC", "lodsb al, byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xAD", "lodsw ax, word ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xAE", "scasb al, byte ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xAF", "scasw ax, word ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\x6C", "insb byte ptr [edi], dx"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\x6D", "insd dword ptr [edi], dx"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\x6E", "outsb dx, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\x6F", "outsd dx, dword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xA4", "movsb byte ptr [edi], byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xA5", "movsd dword ptr [edi], dword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xA6", "cmpsb byte ptr [esi], byte ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xA7", "cmpsd dword ptr [esi], dword ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xAA", "stosb byte ptr [edi], al"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xAB", "stosd dword ptr [edi], eax"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xAC", "lodsb al, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xAD", "lodsd eax, dword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xAE", "scasb al, byte ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xAF", "scasd eax, dword ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF2\x6C", "repne insb byte ptr [rdi], dx"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF2\x6D", "repne insd dword ptr [rdi], dx"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF2\x6E", "repne outsb dx, byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF2\x6F", "repne outsd dx, dword ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF2\xA4", "repne movsb byte ptr [rdi], byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF2\xA5", "repne movsd dword ptr [rdi], dword ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF2\xA6", "repne cmpsb byte ptr [rsi], byte ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF2\xA7", "repne cmpsd dword ptr [rsi], dword ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF2\xAA", "repne stosb byte ptr [rdi], al"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF2\xAB", "repne stosd dword ptr [rdi], eax"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF2\xAC", "repne lodsb al, byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF2\xAD", "repne lodsd eax, dword ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF2\xAE", "repne scasb al, byte ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF2\xAF", "repne scasd eax, dword ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF3\x6C", "rep insb byte ptr [rdi], dx"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF3\x6D", "rep insd dword ptr [rdi], dx"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF3\x6E", "rep outsb dx, byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF3\x6F", "rep outsd dx, dword ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF3\xA4", "rep movsb byte ptr [rdi], byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF3\xA5", "rep movsd dword ptr [rdi], dword ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF3\xA6", "repe cmpsb byte ptr [rsi], byte ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF3\xA7", "repe cmpsd dword ptr [rsi], dword ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF3\xAA", "rep stosb byte ptr [rdi], al"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF3\xAB", "rep stosd dword ptr [rdi], eax"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF3\xAC", "rep lodsb al, byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF3\xAD", "rep lodsd eax, dword ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF3\xAE", "repe scasb al, byte ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF3\xAF", "repe scasd eax, dword ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\x6C", "insb byte ptr [edi], dx"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\x6D", "insw word ptr [edi], dx"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\x6E", "outsb dx, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\x6F", "outsw dx, word ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xA4", "movsb byte ptr [edi], byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xA5", "movsw word ptr [edi], word ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xA6", "cmpsb byte ptr [esi], byte ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xA7", "cmpsw word ptr [esi], word ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xAA", "stosb byte ptr [edi], al"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xAB", "stosw word ptr [edi], ax"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xAC", "lodsb al, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xAD", "lodsw ax, word ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xAE", "scasb al, byte ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xAF", "scasw ax, word ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF2\x6C", "repne insb byte ptr [rdi], dx"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF2\x6D", "repne insw word ptr [rdi], dx"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF2\x6E", "repne outsb dx, byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF2\x6F", "repne outsw dx, word ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF2\xA4", "repne movsb byte ptr [rdi], byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF2\xA5", "repne movsw word ptr [rdi], word ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF2\xA6", "repne cmpsb byte ptr [rsi], byte ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF2\xA7", "repne cmpsw word ptr [rsi], word ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF2\xAA", "repne stosb byte ptr [rdi], al"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF2\xAB", "repne stosw word ptr [rdi], ax"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF2\xAC", "repne lodsb al, byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF2\xAD", "repne lodsw ax, word ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF2\xAE", "repne scasb al, byte ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF2\xAF", "repne scasw ax, word ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF3\x6C", "rep insb byte ptr [rdi], dx"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF3\x6D", "rep insw word ptr [rdi], dx"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF3\x6E", "rep outsb dx, byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF3\x6F", "rep outsw dx, word ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF3\xA4", "rep movsb byte ptr [rdi], byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF3\xA5", "rep movsw word ptr [rdi], word ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF3\xA6", "repe cmpsb byte ptr [rsi], byte ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF3\xA7", "repe cmpsw word ptr [rsi], word ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF3\xAA", "rep stosb byte ptr [rdi], al"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF3\xAB", "rep stosw word ptr [rdi], ax"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF3\xAC", "rep lodsb al, byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF3\xAD", "rep lodsw ax, word ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF3\xAE", "repe scasb al, byte ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF3\xAF", "repe scasw ax, word ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF2\x6C", "repne insb byte ptr [edi], dx"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF2\x6D", "repne insd dword ptr [edi], dx"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF2\x6E", "repne outsb dx, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF2\x6F", "repne outsd dx, dword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF2\xA4", "repne movsb byte ptr [edi], byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF2\xA5", "repne movsd dword ptr [edi], dword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF2\xA6", "repne cmpsb byte ptr [esi], byte ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF2\xA7", "repne cmpsd dword ptr [esi], dword ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF2\xAA", "repne stosb byte ptr [edi], al"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF2\xAB", "repne stosd dword ptr [edi], eax"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF2\xAC", "repne lodsb al, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF2\xAD", "repne lodsd eax, dword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF2\xAE", "repne scasb al, byte ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF2\xAF", "repne scasd eax, dword ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF3\x6C", "rep insb byte ptr [edi], dx"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF3\x6D", "rep insd dword ptr [edi], dx"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF3\x6E", "rep outsb dx, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF3\x6F", "rep outsd dx, dword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF3\xA4", "rep movsb byte ptr [edi], byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF3\xA5", "rep movsd dword ptr [edi], dword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF3\xA6", "repe cmpsb byte ptr [esi], byte ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF3\xA7", "repe cmpsd dword ptr [esi], dword ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF3\xAA", "rep stosb byte ptr [edi], al"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF3\xAB", "rep stosd dword ptr [edi], eax"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF3\xAC", "rep lodsb al, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF3\xAD", "rep lodsd eax, dword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF3\xAE", "repe scasb al, byte ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF3\xAF", "repe scasd eax, dword ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF2\x6C", "repne insb byte ptr [edi], dx"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF2\x6D", "repne insw word ptr [edi], dx"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF2\x6E", "repne outsb dx, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF2\x6F", "repne outsw dx, word ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF2\xA4", "repne movsb byte ptr [edi], byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF2\xA5", "repne movsw word ptr [edi], word ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF2\xA6", "repne cmpsb byte ptr [esi], byte ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF2\xA7", "repne cmpsw word ptr [esi], word ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF2\xAA", "repne stosb byte ptr [edi], al"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF2\xAB", "repne stosw word ptr [edi], ax"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF2\xAC", "repne lodsb al, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF2\xAD", "repne lodsw ax, word ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF2\xAE", "repne scasb al, byte ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF2\xAF", "repne scasw ax, word ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF3\x6C", "rep insb byte ptr [edi], dx"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF3\x6D", "rep insw word ptr [edi], dx"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF3\x6E", "rep outsb dx, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF3\x6F", "rep outsw dx, word ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF3\xA4", "rep movsb byte ptr [edi], byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF3\xA5", "rep movsw word ptr [edi], word ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF3\xA6", "repe cmpsb byte ptr [esi], byte ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF3\xA7", "repe cmpsw word ptr [esi], word ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF3\xAA", "rep stosb byte ptr [edi], al"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF3\xAB", "rep stosw word ptr [edi], ax"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF3\xAC", "rep lodsb al, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF3\xAD", "rep lodsw ax, word ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF3\xAE", "repe scasb al, byte ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF3\xAF", "repe scasw ax, word ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x48\xA4", "movsb byte ptr [rdi], byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x48\xA5", "movsq qword ptr [rdi], qword ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x48\xA6", "cmpsb byte ptr [rsi], byte ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x48\xA7", "cmpsq qword ptr [rsi], qword ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x48\xAA", "stosb byte ptr [rdi], al"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x48\xAB", "stosq qword ptr [rdi], rax"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x48\xAC", "lodsb al, byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x48\xAD", "lodsq rax, qword ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x48\xAE", "scasb al, byte ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x48\xAF", "scasq rax, qword ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x48\xA4", "movsb byte ptr [rdi], byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x48\xA5", "movsq qword ptr [rdi], qword ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x48\xA6", "cmpsb byte ptr [rsi], byte ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x48\xA7", "cmpsq qword ptr [rsi], qword ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x48\xAA", "stosb byte ptr [rdi], al"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x48\xAB", "stosq qword ptr [rdi], rax"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x48\xAC", "lodsb al, byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x48\xAD", "lodsq rax, qword ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x48\xAE", "scasb al, byte ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x48\xAF", "scasq rax, qword ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\x48\xA4", "movsb byte ptr [edi], byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\x48\xA5", "movsq qword ptr [edi], qword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\x48\xA6", "cmpsb byte ptr [esi], byte ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\x48\xA7", "cmpsq qword ptr [esi], qword ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\x48\xAA", "stosb byte ptr [edi], al"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\x48\xAB", "stosq qword ptr [edi], rax"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\x48\xAC", "lodsb al, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\x48\xAD", "lodsq rax, qword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\x48\xAE", "scasb al, byte ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\x48\xAF", "scasq rax, qword ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF2\x48\xA4", "repne movsb byte ptr [rdi], byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF2\x48\xA5", "repne movsq qword ptr [rdi], qword ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF2\x48\xA6", "repne cmpsb byte ptr [rsi], byte ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF2\x48\xA7", "repne cmpsq qword ptr [rsi], qword ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF2\x48\xAA", "repne stosb byte ptr [rdi], al"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF2\x48\xAB", "repne stosq qword ptr [rdi], rax"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF2\x48\xAC", "repne lodsb al, byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF2\x48\xAD", "repne lodsq rax, qword ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF2\x48\xAE", "repne scasb al, byte ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF2\x48\xAF", "repne scasq rax, qword ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF3\x48\xA4", "rep movsb byte ptr [rdi], byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF3\x48\xA5", "rep movsq qword ptr [rdi], qword ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF3\x48\xA6", "repe cmpsb byte ptr [rsi], byte ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF3\x48\xA7", "repe cmpsq qword ptr [rsi], qword ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF3\x48\xAA", "rep stosb byte ptr [rdi], al"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF3\x48\xAB", "rep stosq qword ptr [rdi], rax"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF3\x48\xAC", "rep lodsb al, byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF3\x48\xAD", "rep lodsq rax, qword ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF3\x48\xAE", "repe scasb al, byte ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\xF3\x48\xAF", "repe scasq rax, qword ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\x48\xA4", "movsb byte ptr [edi], byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\x48\xA5", "movsq qword ptr [edi], qword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\x48\xA6", "cmpsb byte ptr [esi], byte ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\x48\xA7", "cmpsq qword ptr [esi], qword ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\x48\xAA", "stosb byte ptr [edi], al"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\x48\xAB", "stosq qword ptr [edi], rax"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\x48\xAC", "lodsb al, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\x48\xAD", "lodsq rax, qword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\x48\xAE", "scasb al, byte ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\x48\xAF", "scasq rax, qword ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF2\x48\xA4", "repne movsb byte ptr [rdi], byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF2\x48\xA5", "repne movsq qword ptr [rdi], qword ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF2\x48\xA6", "repne cmpsb byte ptr [rsi], byte ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF2\x48\xA7", "repne cmpsq qword ptr [rsi], qword ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF2\x48\xAA", "repne stosb byte ptr [rdi], al"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF2\x48\xAB", "repne stosq qword ptr [rdi], rax"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF2\x48\xAC", "repne lodsb al, byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF2\x48\xAD", "repne lodsq rax, qword ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF2\x48\xAE", "repne scasb al, byte ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF2\x48\xAF", "repne scasq rax, qword ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF3\x48\xA4", "rep movsb byte ptr [rdi], byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF3\x48\xA5", "rep movsq qword ptr [rdi], qword ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF3\x48\xA6", "repe cmpsb byte ptr [rsi], byte ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF3\x48\xA7", "repe cmpsq qword ptr [rsi], qword ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF3\x48\xAA", "rep stosb byte ptr [rdi], al"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF3\x48\xAB", "rep stosq qword ptr [rdi], rax"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF3\x48\xAC", "rep lodsb al, byte ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF3\x48\xAD", "rep lodsq rax, qword ptr [rsi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF3\x48\xAE", "repe scasb al, byte ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\xF3\x48\xAF", "repe scasq rax, qword ptr [rdi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF2\x48\xA4", "repne movsb byte ptr [edi], byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF2\x48\xA5", "repne movsq qword ptr [edi], qword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF2\x48\xA6", "repne cmpsb byte ptr [esi], byte ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF2\x48\xA7", "repne cmpsq qword ptr [esi], qword ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF2\x48\xAA", "repne stosb byte ptr [edi], al"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF2\x48\xAB", "repne stosq qword ptr [edi], rax"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF2\x48\xAC", "repne lodsb al, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF2\x48\xAD", "repne lodsq rax, qword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF2\x48\xAE", "repne scasb al, byte ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF2\x48\xAF", "repne scasq rax, qword ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF3\x48\xA4", "rep movsb byte ptr [edi], byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF3\x48\xA5", "rep movsq qword ptr [edi], qword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF3\x48\xA6", "repe cmpsb byte ptr [esi], byte ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF3\x48\xA7", "repe cmpsq qword ptr [esi], qword ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF3\x48\xAA", "rep stosb byte ptr [edi], al"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF3\x48\xAB", "rep stosq qword ptr [edi], rax"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF3\x48\xAC", "rep lodsb al, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF3\x48\xAD", "rep lodsq rax, qword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF3\x48\xAE", "repe scasb al, byte ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x67\xF3\x48\xAF", "repe scasq rax, qword ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF2\x48\xA4", "repne movsb byte ptr [edi], byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF2\x48\xA5", "repne movsq qword ptr [edi], qword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF2\x48\xA6", "repne cmpsb byte ptr [esi], byte ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF2\x48\xA7", "repne cmpsq qword ptr [esi], qword ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF2\x48\xAA", "repne stosb byte ptr [edi], al"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF2\x48\xAB", "repne stosq qword ptr [edi], rax"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF2\x48\xAC", "repne lodsb al, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF2\x48\xAD", "repne lodsq rax, qword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF2\x48\xAE", "repne scasb al, byte ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF2\x48\xAF", "repne scasq rax, qword ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF3\x48\xA4", "rep movsb byte ptr [edi], byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF3\x48\xA5", "rep movsq qword ptr [edi], qword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF3\x48\xA6", "repe cmpsb byte ptr [esi], byte ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF3\x48\xA7", "repe cmpsq qword ptr [esi], qword ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF3\x48\xAA", "rep stosb byte ptr [edi], al"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF3\x48\xAB", "rep stosq qword ptr [edi], rax"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF3\x48\xAC", "rep lodsb al, byte ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF3\x48\xAD", "rep lodsq rax, qword ptr [esi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF3\x48\xAE", "repe scasb al, byte ptr [edi]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x67\xF3\x48\xAF", "repe scasq rax, qword ptr [edi]"),

        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x0f\x01\x05\xa0\x90\x04\x08", "sgdt [rip + 0x80490a0]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x0f\x01\x05\xa0\x90\x04\x08", "sgdt [rip + 0x80490a0]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x0f\x01\x05\xa0\x90\x04\x08", "sgdt [0x80490a0]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x0f\x01\x05\xa0\x90\x04\x08", "sgdt [0x80490a0]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x0f\x01\x05", "sgdt [di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x0f\x01\x05", "sgdt [di]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x0f\x01\x0d\xa0\x90\x04\x08", "sidt [rip + 0x80490a0]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x0f\x01\x0d\xa0\x90\x04\x08", "sidt [rip + 0x80490a0]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x0f\x01\x0d\xa0\x90\x04\x08", "sidt [0x80490a0]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x0f\x01\x0d\xa0\x90\x04\x08", "sidt [0x80490a0]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x0f\x01\x0d", "sidt [di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x0f\x01\x0d", "sidt [di]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x0f\x01\x15\xa0\x90\x04\x08", "lgdt [rip + 0x80490a0]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x0f\x01\x15\xa0\x90\x04\x08", "lgdt [rip + 0x80490a0]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x0f\x01\x15\xa0\x90\x04\x08", "lgdt [0x80490a0]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x0f\x01\x15\xa0\x90\x04\x08", "lgdt [0x80490a0]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x0f\x01\x15", "lgdt [di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x0f\x01\x15", "lgdt [di]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x0f\x01\x1d\xa0\x90\x04\x08", "lidt [rip + 0x80490a0]"),
        (CS_ARCH_X86, CS_MODE_64, CS_OPT_SYNTAX_INTEL, 0x649123ffe1, b"\x66\x0f\x01\x1d\xa0\x90\x04\x08", "lidt [rip + 0x80490a0]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x0f\x01\x1d\xa0\x90\x04\x08", "lidt [0x80490a0]"),
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0x9123ffe1, b"\x66\x0f\x01\x1d\xa0\x90\x04\x08", "lidt [0x80490a0]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x0f\x01\x1d", "lidt [di]"),
        (CS_ARCH_X86, CS_MODE_16, CS_OPT_SYNTAX_INTEL, 0xffe1, b"\x66\x0f\x01\x1d", "lidt [di]"),

        # issues 702 https://github.com/aquynh/capstone/issues/702
        (CS_ARCH_X86, CS_MODE_32, CS_OPT_SYNTAX_INTEL, 0, b"\x85\xC8", "test eax, ecx")
)

_python3 = sys.version_info.major == 3


def to_hex(s):
    if _python3:
        return " ".join("0x{0:02x}".format(c) for c in s)  # <-- Python 3 is OK
    else:
        return " ".join("0x{0:02x}".format(ord(c)) for c in s)


def str_syntax(syntax):
    slist = {
        0: "",
        CS_OPT_SYNTAX_INTEL: "intel",
        CS_OPT_SYNTAX_ATT: "att",
    }

    return slist[syntax]


def str_arch_mode(a, m):
    amlist = {
        (CS_ARCH_X86, CS_MODE_16): "X86-16bit",
        (CS_ARCH_X86, CS_MODE_32): "X86-32bit",
        (CS_ARCH_X86, CS_MODE_64): "X86-64bit",
    }

    return amlist[(a, m)]


# ## Test cs_disasm_quick()
def test_regression(verbose):
    for (arch, mode, syntax, address, code, expected_output) in all_tests:
        #print("%s %s: %s = " %(str_arch_mode(arch, mode), str_syntax(syntax), to_hex(code)), end=""),
        output = "%s %s: %s = " %(str_arch_mode(arch, mode), str_syntax(syntax), to_hex(code))
        md = Cs(arch, mode)
        if syntax != 0:
            md.syntax = syntax
        insn = list(md.disasm(code, address))[0]
        output2 = "%s %s" % (insn.mnemonic, insn.op_str)
        if output2 != expected_output:
            print(output, output2)
            print("\t --> ERROR: expected output = %s" %(expected_output))
        elif verbose:
            print(output, output2)


if __name__ == '__main__':
    import sys
    if len(sys.argv) == 2 and sys.argv[1] == "-v":
        test_regression(True)   # quiet
    else:
        test_regression(False)  # verbose
