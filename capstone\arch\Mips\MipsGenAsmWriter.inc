/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Assembly Writer Source Fragment                                             *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine, http://www.capstone-engine.org */
/* By <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2015 */

/// printInstruction - This method is automatically generated by tablegen
/// from the instruction set description.
static void printInstruction(MCInst *MI, SStream *O, const MCRegisterInfo *MRI)
{
  static const uint32_t OpInfo[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    9396U,	// DBG_VALUE
    0U,	// REG_SEQUENCE
    0U,	// COPY
    9389U,	// BUNDLE
    9406U,	// LIFETIME_START
    9376U,	// LIFETIME_END
    0U,	// STACKMAP
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    0U,	// STATEPOINT
    0U,	// FRAME_ALLOC
    21660U,	// ABSQ_S_PH
    18025U,	// ABSQ_S_QB
    24850U,	// ABSQ_S_W
    134237992U,	// ADD
    18294U,	// ADDIUPC
    18294U,	// ADDIUPC_MM
    22527U,	// ADDIUR1SP_MM
    134234410U,	// ADDIUR2_MM
    8683851U,	// ADDIUS5_MM
    546875U,	// ADDIUSP_MM
    134239193U,	// ADDQH_PH
    134239310U,	// ADDQH_R_PH
    134242253U,	// ADDQH_R_W
    134241856U,	// ADDQH_W
    134239267U,	// ADDQ_PH
    134239366U,	// ADDQ_S_PH
    134242558U,	// ADDQ_S_W
    134236055U,	// ADDSC
    134234730U,	// ADDS_A_B
    134236180U,	// ADDS_A_D
    134238138U,	// ADDS_A_H
    134241564U,	// ADDS_A_W
    134235198U,	// ADDS_S_B
    134237269U,	// ADDS_S_D
    134238695U,	// ADDS_S_H
    134242608U,	// ADDS_S_W
    134235413U,	// ADDS_U_B
    134237736U,	// ADDS_U_D
    134238973U,	// ADDS_U_H
    134243026U,	// ADDS_U_W
    134234575U,	// ADDU16_MM
    134235621U,	// ADDUH_QB
    134235729U,	// ADDUH_R_QB
    134239465U,	// ADDU_PH
    134235834U,	// ADDU_QB
    134239410U,	// ADDU_S_PH
    134235775U,	// ADDU_S_QB
    2281718627U,	// ADDVI_B
    2281720348U,	// ADDVI_D
    2281722002U,	// ADDVI_H
    2281725637U,	// ADDVI_W
    134235491U,	// ADDV_B
    134237836U,	// ADDV_D
    134239051U,	// ADDV_H
    134243126U,	// ADDV_W
    134236094U,	// ADDWC
    134234712U,	// ADD_A_B
    134236161U,	// ADD_A_D
    134238120U,	// ADD_A_H
    134241545U,	// ADD_A_W
    134237992U,	// ADD_MM
    134239685U,	// ADDi
    134239685U,	// ADDi_MM
    134241307U,	// ADDiu
    134241307U,	// ADDiu_MM
    134241261U,	// ADDu
    134241261U,	// ADDu_MM
    0U,	// ADJCALLSTACKDOWN
    0U,	// ADJCALLSTACKUP
    134240158U,	// ALIGN
    18286U,	// ALUIPC
    134238014U,	// AND
    835930U,	// AND16_MM
    134238014U,	// AND64
    134234471U,	// ANDI16_MM
    2281718486U,	// ANDI_B
    134238014U,	// AND_MM
    134241389U,	// AND_V
    0U,	// AND_V_D_PSEUDO
    0U,	// AND_V_H_PSEUDO
    0U,	// AND_V_W_PSEUDO
    134239691U,	// ANDi
    134239691U,	// ANDi64
    134239691U,	// ANDi_MM
    134238028U,	// APPEND
    134235092U,	// ASUB_S_B
    134237099U,	// ASUB_S_D
    134238527U,	// ASUB_S_H
    134242388U,	// ASUB_S_W
    134235307U,	// ASUB_U_B
    134237566U,	// ASUB_U_D
    134238815U,	// ASUB_U_H
    134242856U,	// ASUB_U_W
    0U,	// ATOMIC_CMP_SWAP_I16
    0U,	// ATOMIC_CMP_SWAP_I32
    0U,	// ATOMIC_CMP_SWAP_I64
    0U,	// ATOMIC_CMP_SWAP_I8
    0U,	// ATOMIC_LOAD_ADD_I16
    0U,	// ATOMIC_LOAD_ADD_I32
    0U,	// ATOMIC_LOAD_ADD_I64
    0U,	// ATOMIC_LOAD_ADD_I8
    0U,	// ATOMIC_LOAD_AND_I16
    0U,	// ATOMIC_LOAD_AND_I32
    0U,	// ATOMIC_LOAD_AND_I64
    0U,	// ATOMIC_LOAD_AND_I8
    0U,	// ATOMIC_LOAD_NAND_I16
    0U,	// ATOMIC_LOAD_NAND_I32
    0U,	// ATOMIC_LOAD_NAND_I64
    0U,	// ATOMIC_LOAD_NAND_I8
    0U,	// ATOMIC_LOAD_OR_I16
    0U,	// ATOMIC_LOAD_OR_I32
    0U,	// ATOMIC_LOAD_OR_I64
    0U,	// ATOMIC_LOAD_OR_I8
    0U,	// ATOMIC_LOAD_SUB_I16
    0U,	// ATOMIC_LOAD_SUB_I32
    0U,	// ATOMIC_LOAD_SUB_I64
    0U,	// ATOMIC_LOAD_SUB_I8
    0U,	// ATOMIC_LOAD_XOR_I16
    0U,	// ATOMIC_LOAD_XOR_I32
    0U,	// ATOMIC_LOAD_XOR_I64
    0U,	// ATOMIC_LOAD_XOR_I8
    0U,	// ATOMIC_SWAP_I16
    0U,	// ATOMIC_SWAP_I32
    0U,	// ATOMIC_SWAP_I64
    0U,	// ATOMIC_SWAP_I8
    134239795U,	// AUI
    18279U,	// AUIPC
    134235178U,	// AVER_S_B
    134237249U,	// AVER_S_D
    134238665U,	// AVER_S_H
    134242588U,	// AVER_S_W
    134235393U,	// AVER_U_B
    134237716U,	// AVER_U_D
    134238953U,	// AVER_U_H
    134243006U,	// AVER_U_W
    134235120U,	// AVE_S_B
    134237181U,	// AVE_S_D
    134238597U,	// AVE_S_H
    134242470U,	// AVE_S_W
    134235335U,	// AVE_U_B
    134237648U,	// AVE_U_D
    134238885U,	// AVE_U_H
    134242938U,	// AVE_U_W
    23579U,	// AddiuRxImmX16
    1072155U,	// AddiuRxPcImmX16
    285236251U,	// AddiuRxRxImm16
    16800795U,	// AddiuRxRxImmX16
    25189403U,	// AddiuRxRyOffMemX16
    1336343U,	// AddiuSpImm16
    549911U,	// AddiuSpImmX16
    134241261U,	// AdduRxRyRz16
    16797502U,	// AndRxRxRy16
    0U,	// B
    541013U,	// B16_MM
    134241260U,	// BADDu
    546393U,	// BAL
    542494U,	// BALC
    134240157U,	// BALIGN
    0U,	// BAL_BR
    167788585U,	// BBIT0
    167788717U,	// BBIT032
    167788710U,	// BBIT1
    167788726U,	// BBIT132
    542473U,	// BC
    20351U,	// BC0F
    22218U,	// BC0FL
    23455U,	// BC0T
    22347U,	// BC0TL
    25733U,	// BC1EQZ
    20357U,	// BC1F
    22225U,	// BC1FL
    20357U,	// BC1F_MM
    25717U,	// BC1NEZ
    23461U,	// BC1T
    22354U,	// BC1TL
    23461U,	// BC1T_MM
    25741U,	// BC2EQZ
    20363U,	// BC2F
    22232U,	// BC2FL
    25725U,	// BC2NEZ
    23467U,	// BC2T
    22361U,	// BC2TL
    20369U,	// BC3F
    22239U,	// BC3FL
    23473U,	// BC3T
    22368U,	// BC3TL
    2281718555U,	// BCLRI_B
    2281720292U,	// BCLRI_D
    2281721946U,	// BCLRI_H
    2281725581U,	// BCLRI_W
    134235059U,	// BCLR_B
    134237023U,	// BCLR_D
    134238494U,	// BCLR_H
    134242304U,	// BCLR_W
    134240340U,	// BEQ
    134240340U,	// BEQ64
    134236044U,	// BEQC
    134240063U,	// BEQL
    16882U,	// BEQZ16_MM
    18246U,	// BEQZALC
    18394U,	// BEQZC
    18394U,	// BEQZC_MM
    134240340U,	// BEQ_MM
    134235917U,	// BGEC
    134236068U,	// BGEUC
    25500U,	// BGEZ
    25500U,	// BGEZ64
    22115U,	// BGEZAL
    18219U,	// BGEZALC
    22311U,	// BGEZALL
    23424U,	// BGEZALS_MM
    22115U,	// BGEZAL_MM
    18373U,	// BGEZC
    22391U,	// BGEZL
    25500U,	// BGEZ_MM
    25560U,	// BGTZ
    25560U,	// BGTZ64
    18255U,	// BGTZALC
    18401U,	// BGTZC
    22405U,	// BGTZL
    25560U,	// BGTZ_MM
    2298495744U,	// BINSLI_B
    2298497481U,	// BINSLI_D
    2298499135U,	// BINSLI_H
    2298502770U,	// BINSLI_W
    151012243U,	// BINSL_B
    151014033U,	// BINSL_D
    151015601U,	// BINSL_H
    151019280U,	// BINSL_W
    2298495805U,	// BINSRI_B
    2298497526U,	// BINSRI_D
    2298499180U,	// BINSRI_H
    2298502815U,	// BINSRI_W
    151012291U,	// BINSR_B
    151014289U,	// BINSR_D
    151015726U,	// BINSR_H
    151019570U,	// BINSR_W
    23733U,	// BITREV
    22477U,	// BITSWAP
    25506U,	// BLEZ
    25506U,	// BLEZ64
    18228U,	// BLEZALC
    18380U,	// BLEZC
    22398U,	// BLEZL
    25506U,	// BLEZ_MM
    134236062U,	// BLTC
    134236075U,	// BLTUC
    25566U,	// BLTZ
    25566U,	// BLTZ64
    22123U,	// BLTZAL
    18264U,	// BLTZALC
    22320U,	// BLTZALL
    23433U,	// BLTZALS_MM
    22123U,	// BLTZAL_MM
    18408U,	// BLTZC
    22412U,	// BLTZL
    25566U,	// BLTZ_MM
    2298495860U,	// BMNZI_B
    151018662U,	// BMNZ_V
    2298495852U,	// BMZI_B
    151018648U,	// BMZ_V
    134238058U,	// BNE
    134238058U,	// BNE64
    134235923U,	// BNEC
    2281718494U,	// BNEGI_B
    2281720240U,	// BNEGI_D
    2281721894U,	// BNEGI_H
    2281725529U,	// BNEGI_W
    134234814U,	// BNEG_B
    134236568U,	// BNEG_D
    134238222U,	// BNEG_H
    134241776U,	// BNEG_W
    134239940U,	// BNEL
    16874U,	// BNEZ16_MM
    18237U,	// BNEZALC
    18387U,	// BNEZC
    18387U,	// BNEZC_MM
    134238058U,	// BNE_MM
    134236082U,	// BNVC
    17803U,	// BNZ_B
    20233U,	// BNZ_D
    21363U,	// BNZ_H
    23711U,	// BNZ_V
    25463U,	// BNZ_W
    134236088U,	// BOVC
    540871U,	// BPOSGE32
    0U,	// BPOSGE32_PSEUDO
    22080U,	// BREAK
    65909U,	// BREAK16_MM
    22080U,	// BREAK_MM
    2298495719U,	// BSELI_B
    0U,	// BSEL_D_PSEUDO
    0U,	// BSEL_FD_PSEUDO
    0U,	// BSEL_FW_PSEUDO
    0U,	// BSEL_H_PSEUDO
    151018620U,	// BSEL_V
    0U,	// BSEL_W_PSEUDO
    2281718609U,	// BSETI_B
    2281720330U,	// BSETI_D
    2281721984U,	// BSETI_H
    2281725619U,	// BSETI_W
    134235275U,	// BSET_B
    134237385U,	// BSET_D
    134238783U,	// BSET_H
    134242762U,	// BSET_W
    17797U,	// BZ_B
    20217U,	// BZ_D
    21357U,	// BZ_H
    23698U,	// BZ_V
    25457U,	// BZ_W
    541278U,	// B_MM_Pseudo
    402678723U,	// BeqzRxImm16
    25539U,	// BeqzRxImmX16
    1327710U,	// Bimm16
    541278U,	// BimmX16
    402678696U,	// BnezRxImm16
    25512U,	// BnezRxImmX16
    9368U,	// Break16
    1598417U,	// Bteqz16
    536893428U,	// BteqzT8CmpX16
    536892936U,	// BteqzT8CmpiX16
    536894397U,	// BteqzT8SltX16
    536892966U,	// BteqzT8SltiX16
    536894505U,	// BteqzT8SltiuX16
    536894541U,	// BteqzT8SltuX16
    549841U,	// BteqzX16
    1598390U,	// Btnez16
    671111156U,	// BtnezT8CmpX16
    671110664U,	// BtnezT8CmpiX16
    671112125U,	// BtnezT8SltX16
    671110694U,	// BtnezT8SltiX16
    671112233U,	// BtnezT8SltiuX16
    671112269U,	// BtnezT8SltuX16
    549814U,	// BtnezX16
    0U,	// BuildPairF64
    0U,	// BuildPairF64_64
    85859U,	// CACHE
    85859U,	// CACHE_MM
    85859U,	// CACHE_R6
    19003U,	// CEIL_L_D64
    23031U,	// CEIL_L_S
    20179U,	// CEIL_W_D32
    20179U,	// CEIL_W_D64
    20179U,	// CEIL_W_MM
    23353U,	// CEIL_W_S
    23353U,	// CEIL_W_S_MM
    134234890U,	// CEQI_B
    134236627U,	// CEQI_D
    134238281U,	// CEQI_H
    134241916U,	// CEQI_W
    134235044U,	// CEQ_B
    134236930U,	// CEQ_D
    134238472U,	// CEQ_H
    134242192U,	// CEQ_W
    16444U,	// CFC1
    16444U,	// CFC1_MM
    16968U,	// CFCMSA
    134243407U,	// CINS
    134243363U,	// CINS32
    19639U,	// CLASS_D
    23205U,	// CLASS_S
    134235129U,	// CLEI_S_B
    134237190U,	// CLEI_S_D
    134238606U,	// CLEI_S_H
    134242479U,	// CLEI_S_W
    2281718992U,	// CLEI_U_B
    2281721305U,	// CLEI_U_D
    2281722542U,	// CLEI_U_H
    2281726595U,	// CLEI_U_W
    134235111U,	// CLE_S_B
    134237172U,	// CLE_S_D
    134238588U,	// CLE_S_H
    134242461U,	// CLE_S_W
    134235326U,	// CLE_U_B
    134237639U,	// CLE_U_D
    134238876U,	// CLE_U_H
    134242929U,	// CLE_U_W
    22452U,	// CLO
    22452U,	// CLO_MM
    22452U,	// CLO_R6
    134235149U,	// CLTI_S_B
    134237210U,	// CLTI_S_D
    134238626U,	// CLTI_S_H
    134242499U,	// CLTI_S_W
    2281719012U,	// CLTI_U_B
    2281721325U,	// CLTI_U_D
    2281722562U,	// CLTI_U_H
    2281726615U,	// CLTI_U_W
    134235217U,	// CLT_S_B
    134237288U,	// CLT_S_D
    134238714U,	// CLT_S_H
    134242627U,	// CLT_S_W
    134235444U,	// CLT_U_B
    134237767U,	// CLT_U_D
    134239004U,	// CLT_U_H
    134243057U,	// CLT_U_W
    25534U,	// CLZ
    25534U,	// CLZ_MM
    25534U,	// CLZ_R6
    134235667U,	// CMPGDU_EQ_QB
    134235572U,	// CMPGDU_LE_QB
    134235786U,	// CMPGDU_LT_QB
    134235681U,	// CMPGU_EQ_QB
    134235586U,	// CMPGU_LE_QB
    134235800U,	// CMPGU_LT_QB
    17966U,	// CMPU_EQ_QB
    17871U,	// CMPU_LE_QB
    18085U,	// CMPU_LT_QB
    134236919U,	// CMP_EQ_D
    21548U,	// CMP_EQ_PH
    134240864U,	// CMP_EQ_S
    134236489U,	// CMP_F_D
    134240675U,	// CMP_F_S
    134236333U,	// CMP_LE_D
    21444U,	// CMP_LE_PH
    134240596U,	// CMP_LE_S
    134237410U,	// CMP_LT_D
    21717U,	// CMP_LT_PH
    134240959U,	// CMP_LT_S
    134236507U,	// CMP_SAF_D
    134240685U,	// CMP_SAF_S
    134236946U,	// CMP_SEQ_D
    134240883U,	// CMP_SEQ_S
    134236370U,	// CMP_SLE_D
    134240625U,	// CMP_SLE_S
    134237437U,	// CMP_SLT_D
    134240978U,	// CMP_SLT_S
    134236994U,	// CMP_SUEQ_D
    134240914U,	// CMP_SUEQ_S
    134236418U,	// CMP_SULE_D
    134240656U,	// CMP_SULE_S
    134237485U,	// CMP_SULT_D
    134241009U,	// CMP_SULT_S
    134236876U,	// CMP_SUN_D
    134240837U,	// CMP_SUN_S
    134236974U,	// CMP_UEQ_D
    134240903U,	// CMP_UEQ_S
    134236398U,	// CMP_ULE_D
    134240645U,	// CMP_ULE_S
    134237465U,	// CMP_ULT_D
    134240998U,	// CMP_ULT_S
    134236858U,	// CMP_UN_D
    134240827U,	// CMP_UN_S
    9454U,	// CONSTPOOL_ENTRY
    0U,	// COPY_FD_PSEUDO
    0U,	// COPY_FW_PSEUDO
    2952807544U,	// COPY_S_B
    2952809637U,	// COPY_S_D
    2952811052U,	// COPY_S_H
    2952814987U,	// COPY_S_W
    2952807759U,	// COPY_U_B
    2952810104U,	// COPY_U_D
    2952811319U,	// COPY_U_H
    2952815394U,	// COPY_U_W
    1867863U,	// CTC1
    1867863U,	// CTC1_MM
    16976U,	// CTCMSA
    22833U,	// CVT_D32_S
    23896U,	// CVT_D32_W
    23896U,	// CVT_D32_W_MM
    22087U,	// CVT_D64_L
    22833U,	// CVT_D64_S
    23896U,	// CVT_D64_W
    22833U,	// CVT_D_S_MM
    19024U,	// CVT_L_D64
    19024U,	// CVT_L_D64_MM
    23052U,	// CVT_L_S
    23052U,	// CVT_L_S_MM
    19362U,	// CVT_S_D32
    19362U,	// CVT_S_D32_MM
    19362U,	// CVT_S_D64
    22096U,	// CVT_S_L
    24651U,	// CVT_S_W
    24651U,	// CVT_S_W_MM
    20200U,	// CVT_W_D32
    20200U,	// CVT_W_D64
    20200U,	// CVT_W_MM
    23374U,	// CVT_W_S
    23374U,	// CVT_W_S_MM
    19183U,	// C_EQ_D32
    19183U,	// C_EQ_D64
    23128U,	// C_EQ_S
    18754U,	// C_F_D32
    18754U,	// C_F_D64
    22940U,	// C_F_S
    18597U,	// C_LE_D32
    18597U,	// C_LE_D64
    22860U,	// C_LE_S
    19674U,	// C_LT_D32
    19674U,	// C_LT_D64
    23223U,	// C_LT_S
    18588U,	// C_NGE_D32
    18588U,	// C_NGE_D64
    22851U,	// C_NGE_S
    18623U,	// C_NGLE_D32
    18623U,	// C_NGLE_D64
    22878U,	// C_NGLE_S
    19040U,	// C_NGL_D32
    19040U,	// C_NGL_D64
    23068U,	// C_NGL_S
    19665U,	// C_NGT_D32
    19665U,	// C_NGT_D64
    23214U,	// C_NGT_S
    18633U,	// C_OLE_D32
    18633U,	// C_OLE_D64
    22888U,	// C_OLE_S
    19700U,	// C_OLT_D32
    19700U,	// C_OLT_D64
    23241U,	// C_OLT_S
    19209U,	// C_SEQ_D32
    19209U,	// C_SEQ_D64
    23146U,	// C_SEQ_S
    18824U,	// C_SF_D32
    18824U,	// C_SF_D64
    22986U,	// C_SF_S
    19237U,	// C_UEQ_D32
    19237U,	// C_UEQ_D64
    23166U,	// C_UEQ_S
    18661U,	// C_ULE_D32
    18661U,	// C_ULE_D64
    22908U,	// C_ULE_S
    19728U,	// C_ULT_D32
    19728U,	// C_ULT_D64
    23261U,	// C_ULT_S
    19122U,	// C_UN_D32
    19122U,	// C_UN_D64
    23091U,	// C_UN_S
    22516U,	// CmpRxRy16
    939546120U,	// CmpiRxImm16
    22024U,	// CmpiRxImmX16
    549945U,	// Constant32
    134237991U,	// DADD
    134239684U,	// DADDi
    134241306U,	// DADDiu
    134241267U,	// DADDu
    8689123U,	// DAHI
    134240165U,	// DALIGN
    8689184U,	// DATI
    134239794U,	// DAUI
    22476U,	// DBITSWAP
    22451U,	// DCLO
    22451U,	// DCLO_R6
    25533U,	// DCLZ
    25533U,	// DCLZ_R6
    134241469U,	// DDIV
    134241377U,	// DDIVU
    9480U,	// DERET
    9480U,	// DERET_MM
    134243425U,	// DEXT
    134243400U,	// DEXTM
    134243438U,	// DEXTU
    546247U,	// DI
    134243413U,	// DINS
    134243393U,	// DINSM
    134243431U,	// DINSU
    134241470U,	// DIV
    134241378U,	// DIVU
    134235238U,	// DIV_S_B
    134237331U,	// DIV_S_D
    134238735U,	// DIV_S_H
    134242670U,	// DIV_S_W
    134235453U,	// DIV_U_B
    134237798U,	// DIV_U_D
    134239013U,	// DIV_U_H
    134243088U,	// DIV_U_W
    546247U,	// DI_MM
    134234690U,	// DLSA
    134234690U,	// DLSA_R6
    134234121U,	// DMFC0
    16450U,	// DMFC1
    134234372U,	// DMFC2
    134238036U,	// DMOD
    134241281U,	// DMODU
    134234128U,	// DMTC0
    1867869U,	// DMTC1
    134234379U,	// DMTC2
    134239671U,	// DMUH
    134241299U,	// DMUHU
    134240103U,	// DMUL
    23495U,	// DMULT
    23641U,	// DMULTu
    134241343U,	// DMULU
    134240103U,	// DMUL_R6
    134237239U,	// DOTP_S_D
    134238655U,	// DOTP_S_H
    134242538U,	// DOTP_S_W
    134237706U,	// DOTP_U_D
    134238943U,	// DOTP_U_H
    134242996U,	// DOTP_U_W
    151014368U,	// DPADD_S_D
    151015784U,	// DPADD_S_H
    151019657U,	// DPADD_S_W
    151014835U,	// DPADD_U_D
    151016072U,	// DPADD_U_H
    151020125U,	// DPADD_U_W
    134239524U,	// DPAQX_SA_W_PH
    134239607U,	// DPAQX_S_W_PH
    134241998U,	// DPAQ_SA_L_W
    134239566U,	// DPAQ_S_W_PH
    134239859U,	// DPAU_H_QBL
    134240355U,	// DPAU_H_QBR
    134239645U,	// DPAX_W_PH
    134239514U,	// DPA_W_PH
    22521U,	// DPOP
    134239539U,	// DPSQX_SA_W_PH
    134239621U,	// DPSQX_S_W_PH
    134242011U,	// DPSQ_SA_L_W
    134239594U,	// DPSQ_S_W_PH
    151014335U,	// DPSUB_S_D
    151015763U,	// DPSUB_S_H
    151019624U,	// DPSUB_S_W
    151014802U,	// DPSUB_U_D
    151016051U,	// DPSUB_U_H
    151020092U,	// DPSUB_U_W
    134239871U,	// DPSU_H_QBL
    134240367U,	// DPSU_H_QBR
    134239656U,	// DPSX_W_PH
    134239635U,	// DPS_W_PH
    134240512U,	// DROTR
    134234351U,	// DROTR32
    134241513U,	// DROTRV
    21370U,	// DSBH
    25610U,	// DSDIV
    20275U,	// DSHD
    134240057U,	// DSLL
    134234321U,	// DSLL32
    1073764153U,	// DSLL64_32
    134241475U,	// DSLLV
    134234684U,	// DSRA
    134234303U,	// DSRA32
    134241454U,	// DSRAV
    134240069U,	// DSRL
    134234329U,	// DSRL32
    134241482U,	// DSRLV
    134235901U,	// DSUB
    134241246U,	// DSUBu
    25596U,	// DUDIV
    25611U,	// DivRxRy16
    25597U,	// DivuRxRy16
    9438U,	// EHB
    9438U,	// EHB_MM
    546259U,	// EI
    546259U,	// EI_MM
    9481U,	// ERET
    9481U,	// ERET_MM
    134243426U,	// EXT
    134240324U,	// EXTP
    134240221U,	// EXTPDP
    134241497U,	// EXTPDPV
    134241506U,	// EXTPV
    134242731U,	// EXTRV_RS_W
    134242285U,	// EXTRV_R_W
    134238744U,	// EXTRV_S_H
    134243168U,	// EXTRV_W
    134242720U,	// EXTR_RS_W
    134242264U,	// EXTR_R_W
    134238675U,	// EXTR_S_H
    134242363U,	// EXTR_W
    134243419U,	// EXTS
    134243371U,	// EXTS32
    134243426U,	// EXT_MM
    0U,	// ExtractElementF64
    0U,	// ExtractElementF64_64
    0U,	// FABS_D
    19631U,	// FABS_D32
    19631U,	// FABS_D64
    19631U,	// FABS_MM
    23198U,	// FABS_S
    23198U,	// FABS_S_MM
    0U,	// FABS_W
    134236265U,	// FADD_D
    134236266U,	// FADD_D32
    134236266U,	// FADD_D64
    134236266U,	// FADD_MM
    134240572U,	// FADD_S
    134240572U,	// FADD_S_MM
    134241633U,	// FADD_W
    134236499U,	// FCAF_D
    134241752U,	// FCAF_W
    134236929U,	// FCEQ_D
    134242191U,	// FCEQ_W
    19638U,	// FCLASS_D
    25015U,	// FCLASS_W
    134236343U,	// FCLE_D
    134241675U,	// FCLE_W
    134237420U,	// FCLT_D
    134242770U,	// FCLT_W
    2204821U,	// FCMP_D32
    2204821U,	// FCMP_D32_MM
    2204821U,	// FCMP_D64
    2466965U,	// FCMP_S32
    2466965U,	// FCMP_S32_MM
    134236439U,	// FCNE_D
    134241709U,	// FCNE_W
    134237039U,	// FCOR_D
    134242320U,	// FCOR_W
    134236985U,	// FCUEQ_D
    134242207U,	// FCUEQ_W
    134236409U,	// FCULE_D
    134241691U,	// FCULE_W
    134237476U,	// FCULT_D
    134242786U,	// FCULT_W
    134236455U,	// FCUNE_D
    134241725U,	// FCUNE_W
    134236868U,	// FCUN_D
    134242097U,	// FCUN_W
    134237862U,	// FDIV_D
    134237863U,	// FDIV_D32
    134237863U,	// FDIV_D64
    134237863U,	// FDIV_MM
    134241045U,	// FDIV_S
    134241045U,	// FDIV_S_MM
    134243152U,	// FDIV_W
    134238402U,	// FEXDO_H
    134242113U,	// FEXDO_W
    134236152U,	// FEXP2_D
    0U,	// FEXP2_D_1_PSEUDO
    134241536U,	// FEXP2_W
    0U,	// FEXP2_W_1_PSEUDO
    19064U,	// FEXUPL_D
    24311U,	// FEXUPL_W
    19327U,	// FEXUPR_D
    24608U,	// FEXUPR_W
    19569U,	// FFINT_S_D
    24908U,	// FFINT_S_W
    20048U,	// FFINT_U_D
    25338U,	// FFINT_U_W
    19074U,	// FFQL_D
    24321U,	// FFQL_W
    19337U,	// FFQR_D
    24618U,	// FFQR_W
    17277U,	// FILL_B
    19049U,	// FILL_D
    0U,	// FILL_FD_PSEUDO
    0U,	// FILL_FW_PSEUDO
    20635U,	// FILL_H
    24296U,	// FILL_W
    18415U,	// FLOG2_D
    23799U,	// FLOG2_W
    19013U,	// FLOOR_L_D64
    23041U,	// FLOOR_L_S
    20189U,	// FLOOR_W_D32
    20189U,	// FLOOR_W_D64
    20189U,	// FLOOR_W_MM
    23363U,	// FLOOR_W_S
    23363U,	// FLOOR_W_S_MM
    151013489U,	// FMADD_D
    151018857U,	// FMADD_W
    134236190U,	// FMAX_A_D
    134241574U,	// FMAX_A_W
    134237937U,	// FMAX_D
    134243177U,	// FMAX_W
    134236170U,	// FMIN_A_D
    134241554U,	// FMIN_A_W
    134236842U,	// FMIN_D
    134242089U,	// FMIN_W
    20150U,	// FMOV_D32
    20150U,	// FMOV_D32_MM
    20150U,	// FMOV_D64
    23324U,	// FMOV_S
    23324U,	// FMOV_S_MM
    151013447U,	// FMSUB_D
    151018815U,	// FMSUB_W
    134236826U,	// FMUL_D
    134236827U,	// FMUL_D32
    134236827U,	// FMUL_D64
    134236827U,	// FMUL_MM
    134240805U,	// FMUL_S
    134240805U,	// FMUL_S_MM
    134242073U,	// FMUL_W
    18841U,	// FNEG_D32
    18841U,	// FNEG_D64
    18841U,	// FNEG_MM
    23002U,	// FNEG_S
    23002U,	// FNEG_S_MM
    19175U,	// FRCP_D
    24394U,	// FRCP_W
    19786U,	// FRINT_D
    25084U,	// FRINT_W
    19814U,	// FRSQRT_D
    25112U,	// FRSQRT_W
    134236518U,	// FSAF_D
    134241760U,	// FSAF_W
    134236957U,	// FSEQ_D
    134242199U,	// FSEQ_W
    134236381U,	// FSLE_D
    134241683U,	// FSLE_W
    134237448U,	// FSLT_D
    134242778U,	// FSLT_W
    134236447U,	// FSNE_D
    134241717U,	// FSNE_W
    134237047U,	// FSOR_D
    134242328U,	// FSOR_W
    19805U,	// FSQRT_D
    19806U,	// FSQRT_D32
    19806U,	// FSQRT_D64
    19806U,	// FSQRT_MM
    23301U,	// FSQRT_S
    23301U,	// FSQRT_S_MM
    25103U,	// FSQRT_W
    134236223U,	// FSUB_D
    134236224U,	// FSUB_D32
    134236224U,	// FSUB_D64
    134236224U,	// FSUB_MM
    134240554U,	// FSUB_S
    134240554U,	// FSUB_S_MM
    134241591U,	// FSUB_W
    134237006U,	// FSUEQ_D
    134242216U,	// FSUEQ_W
    134236430U,	// FSULE_D
    134241700U,	// FSULE_W
    134237497U,	// FSULT_D
    134242795U,	// FSULT_W
    134236464U,	// FSUNE_D
    134241734U,	// FSUNE_W
    134236887U,	// FSUN_D
    134242105U,	// FSUN_W
    19580U,	// FTINT_S_D
    24919U,	// FTINT_S_W
    20059U,	// FTINT_U_D
    25349U,	// FTINT_U_W
    134238479U,	// FTQ_H
    134242225U,	// FTQ_W
    19402U,	// FTRUNC_S_D
    24691U,	// FTRUNC_S_W
    19869U,	// FTRUNC_U_D
    25159U,	// FTRUNC_U_W
    1224758783U,	// GotPrologue16
    134237142U,	// HADD_S_D
    134238558U,	// HADD_S_H
    134242431U,	// HADD_S_W
    134237609U,	// HADD_U_D
    134238846U,	// HADD_U_H
    134242899U,	// HADD_U_W
    134237109U,	// HSUB_S_D
    134238537U,	// HSUB_S_H
    134242398U,	// HSUB_S_W
    134237576U,	// HSUB_U_D
    134238825U,	// HSUB_U_H
    134242866U,	// HSUB_U_W
    134235508U,	// ILVEV_B
    134237853U,	// ILVEV_D
    134239068U,	// ILVEV_H
    134243143U,	// ILVEV_W
    134235036U,	// ILVL_B
    134236834U,	// ILVL_D
    134238394U,	// ILVL_H
    134242081U,	// ILVL_W
    134234788U,	// ILVOD_B
    134236307U,	// ILVOD_D
    134238196U,	// ILVOD_H
    134241666U,	// ILVOD_W
    134235084U,	// ILVR_B
    134237082U,	// ILVR_D
    134238519U,	// ILVR_H
    134242371U,	// ILVR_W
    134243408U,	// INS
    44582043U,	// INSERT_B
    0U,	// INSERT_B_VIDX_PSEUDO
    44584275U,	// INSERT_D
    0U,	// INSERT_D_VIDX_PSEUDO
    0U,	// INSERT_FD_PSEUDO
    0U,	// INSERT_FD_VIDX_PSEUDO
    0U,	// INSERT_FW_PSEUDO
    0U,	// INSERT_FW_VIDX_PSEUDO
    44585551U,	// INSERT_H
    0U,	// INSERT_H_VIDX_PSEUDO
    44589573U,	// INSERT_W
    0U,	// INSERT_W_VIDX_PSEUDO
    16801009U,	// INSV
    52970157U,	// INSVE_B
    52971833U,	// INSVE_D
    52973565U,	// INSVE_H
    52977103U,	// INSVE_W
    134243408U,	// INS_MM
    546365U,	// J
    546398U,	// JAL
    22768U,	// JALR
    547056U,	// JALR16_MM
    22768U,	// JALR64
    0U,	// JALR64Pseudo
    0U,	// JALRPseudo
    541104U,	// JALRS16_MM
    23442U,	// JALRS_MM
    17822U,	// JALR_HB
    22768U,	// JALR_MM
    547706U,	// JALS_MM
    549771U,	// JALX
    549771U,	// JALX_MM
    546398U,	// JAL_MM
    18212U,	// JIALC
    18201U,	// JIC
    547052U,	// JR
    541091U,	// JR16_MM
    547052U,	// JR64
    546873U,	// JRADDIUSP
    542610U,	// JRC16_MM
    542103U,	// JR_HB
    542103U,	// JR_HB_R6
    547052U,	// JR_MM
    546365U,	// J_MM
    2905694U,	// Jal16
    3167838U,	// JalB16
    546398U,	// JalOneReg
    22110U,	// JalTwoReg
    9430U,	// JrRa16
    9421U,	// JrcRa16
    549872U,	// JrcRx16
    540673U,	// JumpLinkReg16
    58738087U,	// LB
    58738087U,	// LB64
    58737088U,	// LBU16_MM
    1358979985U,	// LBUX
    58738087U,	// LB_MM
    58743769U,	// LBu
    58743769U,	// LBu64
    58743769U,	// LBu_MM
    58740538U,	// LD
    58736688U,	// LDC1
    58736688U,	// LDC164
    58736688U,	// LDC1_MM
    58736888U,	// LDC2
    58736888U,	// LDC2_R6
    58736947U,	// LDC3
    17103U,	// LDI_B
    18857U,	// LDI_D
    20511U,	// LDI_H
    24146U,	// LDI_W
    58742458U,	// LDL
    18273U,	// LDPC
    58742954U,	// LDR
    1358970992U,	// LDXC1
    1358970992U,	// LDXC164
    58737301U,	// LD_B
    58738820U,	// LD_D
    58740709U,	// LD_H
    58744179U,	// LD_W
    25189403U,	// LEA_ADDiu
    25189402U,	// LEA_ADDiu64
    25189403U,	// LEA_ADDiu_MM
    58741643U,	// LH
    58741643U,	// LH64
    58737111U,	// LHU16_MM
    1358979974U,	// LHX
    58741643U,	// LH_MM
    58743822U,	// LHu
    58743822U,	// LHu64
    58743822U,	// LHu_MM
    16751U,	// LI16_MM
    58742563U,	// LL
    58740537U,	// LLD
    58740537U,	// LLD_R6
    58742563U,	// LL_MM
    58742563U,	// LL_R6
    58736647U,	// LOAD_ACC128
    58736647U,	// LOAD_ACC64
    58736647U,	// LOAD_ACC64DSP
    58742794U,	// LOAD_CCOND_DSP
    0U,	// LONG_BRANCH_ADDiu
    0U,	// LONG_BRANCH_DADDiu
    0U,	// LONG_BRANCH_LUi
    134234691U,	// LSA
    134234691U,	// LSA_R6
    1358971006U,	// LUXC1
    1358971006U,	// LUXC164
    1358971006U,	// LUXC1_MM
    33576504U,	// LUi
    33576504U,	// LUi64
    33576504U,	// LUi_MM
    58745726U,	// LW
    58737118U,	// LW16_MM
    58745726U,	// LW64
    58736740U,	// LWC1
    58736740U,	// LWC1_MM
    58736914U,	// LWC2
    58736914U,	// LWC2_R6
    58736959U,	// LWC3
    58745726U,	// LWGP_MM
    58742637U,	// LWL
    58742637U,	// LWL64
    58742637U,	// LWL_MM
    3522956U,	// LWM16_MM
    3522785U,	// LWM32_MM
    3528595U,	// LWM_MM
    18310U,	// LWPC
    137290U,	// LWP_MM
    58743054U,	// LWR
    58743054U,	// LWR64
    58743054U,	// LWR_MM
    58745726U,	// LWSP_MM
    18303U,	// LWUPC
    58743912U,	// LWU_MM
    1358979991U,	// LWX
    1358971020U,	// LWXC1
    1358971020U,	// LWXC1_MM
    1358977945U,	// LWXS_MM
    58745726U,	// LW_MM
    58743912U,	// LWu
    58738087U,	// LbRxRyOffMemX16
    58743769U,	// LbuRxRyOffMemX16
    58741643U,	// LhRxRyOffMemX16
    58743822U,	// LhuRxRyOffMemX16
    939546111U,	// LiRxImm16
    22005U,	// LiRxImmAlignX16
    22015U,	// LiRxImmX16
    33571334U,	// LoadAddr32Imm
    58737158U,	// LoadAddr32Reg
    33576447U,	// LoadImm32Reg
    22019U,	// LoadImm64Reg
    3695486U,	// LwConstant32
    268460926U,	// LwRxPcTcp16
    25470U,	// LwRxPcTcpX16
    58745726U,	// LwRxRyOffMemX16
    1493197694U,	// LwRxSpImmX16
    20269U,	// MADD
    151013751U,	// MADDF_D
    151017921U,	// MADDF_S
    151015667U,	// MADDR_Q_H
    151019386U,	// MADDR_Q_W
    23546U,	// MADDU
    134241274U,	// MADDU_DSP
    23546U,	// MADDU_MM
    151012706U,	// MADDV_B
    151015051U,	// MADDV_D
    151016266U,	// MADDV_H
    151020341U,	// MADDV_W
    134236274U,	// MADD_D32
    134236274U,	// MADD_D32_MM
    134236274U,	// MADD_D64
    134237997U,	// MADD_DSP
    20269U,	// MADD_MM
    151015637U,	// MADD_Q_H
    151019356U,	// MADD_Q_W
    134240571U,	// MADD_S
    134240571U,	// MADD_S_MM
    134239974U,	// MAQ_SA_W_PHL
    134240436U,	// MAQ_SA_W_PHR
    134240002U,	// MAQ_S_W_PHL
    134240464U,	// MAQ_S_W_PHR
    134236215U,	// MAXA_D
    134240544U,	// MAXA_S
    134235159U,	// MAXI_S_B
    134237220U,	// MAXI_S_D
    134238636U,	// MAXI_S_H
    134242509U,	// MAXI_S_W
    2281719022U,	// MAXI_U_B
    2281721335U,	// MAXI_U_D
    2281722572U,	// MAXI_U_H
    2281726625U,	// MAXI_U_W
    134234740U,	// MAX_A_B
    134236191U,	// MAX_A_D
    134238148U,	// MAX_A_H
    134241575U,	// MAX_A_W
    134237938U,	// MAX_D
    134241111U,	// MAX_S
    134235247U,	// MAX_S_B
    134237340U,	// MAX_S_D
    134238755U,	// MAX_S_H
    134242690U,	// MAX_S_W
    134235462U,	// MAX_U_B
    134237807U,	// MAX_U_D
    134239022U,	// MAX_U_H
    134243097U,	// MAX_U_W
    134234122U,	// MFC0
    16451U,	// MFC1
    16451U,	// MFC1_MM
    134234373U,	// MFC2
    16457U,	// MFHC1_D32
    16457U,	// MFHC1_D64
    16457U,	// MFHC1_MM
    546281U,	// MFHI
    546281U,	// MFHI16_MM
    546281U,	// MFHI64
    21993U,	// MFHI_DSP
    546281U,	// MFHI_MM
    546745U,	// MFLO
    546745U,	// MFLO16_MM
    546745U,	// MFLO64
    22457U,	// MFLO_DSP
    546745U,	// MFLO_MM
    134236200U,	// MINA_D
    134240536U,	// MINA_S
    134235139U,	// MINI_S_B
    134237200U,	// MINI_S_D
    134238616U,	// MINI_S_H
    134242489U,	// MINI_S_W
    2281719002U,	// MINI_U_B
    2281721315U,	// MINI_U_D
    2281722552U,	// MINI_U_H
    2281726605U,	// MINI_U_W
    134234721U,	// MIN_A_B
    134236171U,	// MIN_A_D
    134238129U,	// MIN_A_H
    134241555U,	// MIN_A_W
    134236843U,	// MIN_D
    134240812U,	// MIN_S
    134235169U,	// MIN_S_B
    134237230U,	// MIN_S_D
    134238646U,	// MIN_S_H
    134242529U,	// MIN_S_W
    134235384U,	// MIN_U_B
    134237697U,	// MIN_U_D
    134238934U,	// MIN_U_H
    134242987U,	// MIN_U_W
    0U,	// MIPSeh_return32
    0U,	// MIPSeh_return64
    134238037U,	// MOD
    134235899U,	// MODSUB
    134241282U,	// MODU
    134235102U,	// MOD_S_B
    134237163U,	// MOD_S_D
    134238579U,	// MOD_S_H
    134242452U,	// MOD_S_W
    134235317U,	// MOD_U_B
    134237630U,	// MOD_U_D
    134238867U,	// MOD_U_H
    134242920U,	// MOD_U_W
    20345U,	// MOVE16_MM
    67491813U,	// MOVEP_MM
    23668U,	// MOVE_V
    134236560U,	// MOVF_D32
    134236560U,	// MOVF_D32_MM
    134236560U,	// MOVF_D64
    134238109U,	// MOVF_I
    134238109U,	// MOVF_I64
    134238109U,	// MOVF_I_MM
    134240722U,	// MOVF_S
    134240722U,	// MOVF_S_MM
    134236895U,	// MOVN_I64_D64
    134240173U,	// MOVN_I64_I
    134240173U,	// MOVN_I64_I64
    134240848U,	// MOVN_I64_S
    134236895U,	// MOVN_I_D32
    134236895U,	// MOVN_I_D32_MM
    134236895U,	// MOVN_I_D64
    134240173U,	// MOVN_I_I
    134240173U,	// MOVN_I_I64
    134240173U,	// MOVN_I_MM
    134240848U,	// MOVN_I_S
    134240848U,	// MOVN_I_S_MM
    134237558U,	// MOVT_D32
    134237558U,	// MOVT_D32_MM
    134237558U,	// MOVT_D64
    134241235U,	// MOVT_I
    134241235U,	// MOVT_I64
    134241235U,	// MOVT_I_MM
    134241037U,	// MOVT_S
    134241037U,	// MOVT_S_MM
    134237978U,	// MOVZ_I64_D64
    134243300U,	// MOVZ_I64_I
    134243300U,	// MOVZ_I64_I64
    134241138U,	// MOVZ_I64_S
    134237978U,	// MOVZ_I_D32
    134237978U,	// MOVZ_I_D32_MM
    134237978U,	// MOVZ_I_D64
    134243300U,	// MOVZ_I_I
    134243300U,	// MOVZ_I_I64
    134243300U,	// MOVZ_I_MM
    134241138U,	// MOVZ_I_S
    134241138U,	// MOVZ_I_S_MM
    18179U,	// MSUB
    151013742U,	// MSUBF_D
    151017912U,	// MSUBF_S
    151015656U,	// MSUBR_Q_H
    151019375U,	// MSUBR_Q_W
    23525U,	// MSUBU
    134241253U,	// MSUBU_DSP
    23525U,	// MSUBU_MM
    151012697U,	// MSUBV_B
    151015042U,	// MSUBV_D
    151016257U,	// MSUBV_H
    151020332U,	// MSUBV_W
    134236232U,	// MSUB_D32
    134236232U,	// MSUB_D32_MM
    134236232U,	// MSUB_D64
    134235907U,	// MSUB_DSP
    18179U,	// MSUB_MM
    151015627U,	// MSUB_Q_H
    151019346U,	// MSUB_Q_W
    134240553U,	// MSUB_S
    134240553U,	// MSUB_S_MM
    134234129U,	// MTC0
    1867870U,	// MTC1
    1867870U,	// MTC1_MM
    134234380U,	// MTC2
    1884240U,	// MTHC1_D32
    1884240U,	// MTHC1_D64
    1884240U,	// MTHC1_MM
    546287U,	// MTHI
    546287U,	// MTHI64
    1873391U,	// MTHI_DSP
    546287U,	// MTHI_MM
    1873900U,	// MTHLIP
    546758U,	// MTLO
    546758U,	// MTLO64
    1873862U,	// MTLO_DSP
    546758U,	// MTLO_MM
    540701U,	// MTM0
    540826U,	// MTM1
    540958U,	// MTM2
    540707U,	// MTP0
    540832U,	// MTP1
    540964U,	// MTP2
    134239672U,	// MUH
    134241300U,	// MUHU
    134240104U,	// MUL
    134240015U,	// MULEQ_S_W_PHL
    134240477U,	// MULEQ_S_W_PHR
    134239883U,	// MULEU_S_PH_QBL
    134240379U,	// MULEU_S_PH_QBR
    134239433U,	// MULQ_RS_PH
    134242709U,	// MULQ_RS_W
    134239377U,	// MULQ_S_PH
    134242568U,	// MULQ_S_W
    134238462U,	// MULR_Q_H
    134242181U,	// MULR_Q_W
    134239579U,	// MULSAQ_S_W_PH
    134239554U,	// MULSA_W_PH
    23496U,	// MULT
    134241370U,	// MULTU_DSP
    134241224U,	// MULT_DSP
    23496U,	// MULT_MM
    23642U,	// MULTu
    23642U,	// MULTu_MM
    134241337U,	// MULU
    134235517U,	// MULV_B
    134237870U,	// MULV_D
    134239077U,	// MULV_H
    134243160U,	// MULV_W
    134240104U,	// MUL_MM
    134239250U,	// MUL_PH
    134238431U,	// MUL_Q_H
    134242150U,	// MUL_Q_W
    134240104U,	// MUL_R6
    134239345U,	// MUL_S_PH
    546281U,	// Mfhi16
    546745U,	// Mflo16
    20345U,	// Move32R16
    20345U,	// MoveR3216
    23496U,	// MultRxRy16
    75799496U,	// MultRxRyRz16
    23642U,	// MultuRxRy16
    75799642U,	// MultuRxRyRz16
    17028U,	// NLOC_B
    18521U,	// NLOC_D
    20436U,	// NLOC_H
    23880U,	// NLOC_W
    17036U,	// NLZC_B
    18529U,	// NLZC_D
    20444U,	// NLZC_H
    23888U,	// NLZC_W
    134236282U,	// NMADD_D32
    134236282U,	// NMADD_D32_MM
    134236282U,	// NMADD_D64
    134240570U,	// NMADD_S
    134240570U,	// NMADD_S_MM
    134236240U,	// NMSUB_D32
    134236240U,	// NMSUB_D32_MM
    134236240U,	// NMSUB_D64
    134240552U,	// NMSUB_S
    134240552U,	// NMSUB_S_MM
    0U,	// NOP
    134240502U,	// NOR
    134240502U,	// NOR64
    2281718573U,	// NORI_B
    134240502U,	// NOR_MM
    134241412U,	// NOR_V
    0U,	// NOR_V_D_PSEUDO
    0U,	// NOR_V_H_PSEUDO
    0U,	// NOR_V_W_PSEUDO
    16825U,	// NOT16_MM
    20387U,	// NegRxRy16
    23502U,	// NotRxRy16
    134240503U,	// OR
    836010U,	// OR16_MM
    134240503U,	// OR64
    2281718574U,	// ORI_B
    134240503U,	// OR_MM
    134241413U,	// OR_V
    0U,	// OR_V_D_PSEUDO
    0U,	// OR_V_H_PSEUDO
    0U,	// OR_V_W_PSEUDO
    134239771U,	// ORi
    134239771U,	// ORi64
    134239771U,	// ORi_MM
    16799991U,	// OrRxRxRy16
    134239239U,	// PACKRL_PH
    9442U,	// PAUSE
    9442U,	// PAUSE_MM
    134235499U,	// PCKEV_B
    134237844U,	// PCKEV_D
    134239059U,	// PCKEV_H
    134243134U,	// PCKEV_W
    134234779U,	// PCKOD_B
    134236298U,	// PCKOD_D
    134238187U,	// PCKOD_H
    134241657U,	// PCKOD_W
    17555U,	// PCNT_B
    19778U,	// PCNT_D
    21063U,	// PCNT_H
    25076U,	// PCNT_W
    134239203U,	// PICK_PH
    134235631U,	// PICK_QB
    22522U,	// POP
    22186U,	// PRECEQU_PH_QBL
    16906U,	// PRECEQU_PH_QBLA
    22682U,	// PRECEQU_PH_QBR
    16939U,	// PRECEQU_PH_QBRA
    22260U,	// PRECEQ_W_PHL
    22722U,	// PRECEQ_W_PHR
    22171U,	// PRECEU_PH_QBL
    16890U,	// PRECEU_PH_QBLA
    22667U,	// PRECEU_PH_QBR
    16923U,	// PRECEU_PH_QBRA
    134239155U,	// PRECRQU_S_QB_PH
    134241800U,	// PRECRQ_PH_W
    134239128U,	// PRECRQ_QB_PH
    134241831U,	// PRECRQ_RS_PH_W
    134239142U,	// PRECR_QB_PH
    134241784U,	// PRECR_SRA_PH_W
    134241813U,	// PRECR_SRA_R_PH_W
    85911U,	// PREF
    85911U,	// PREF_MM
    85911U,	// PREF_R6
    134238019U,	// PREPEND
    0U,	// PseudoCMPU_EQ_QB
    0U,	// PseudoCMPU_LE_QB
    0U,	// PseudoCMPU_LT_QB
    0U,	// PseudoCMP_EQ_PH
    0U,	// PseudoCMP_LE_PH
    0U,	// PseudoCMP_LT_PH
    16391U,	// PseudoCVT_D32_W
    16391U,	// PseudoCVT_D64_L
    16391U,	// PseudoCVT_D64_W
    16391U,	// PseudoCVT_S_L
    16391U,	// PseudoCVT_S_W
    0U,	// PseudoDMULT
    0U,	// PseudoDMULTu
    0U,	// PseudoDSDIV
    0U,	// PseudoDUDIV
    0U,	// PseudoIndirectBranch
    0U,	// PseudoIndirectBranch64
    0U,	// PseudoMADD
    0U,	// PseudoMADDU
    0U,	// PseudoMFHI
    0U,	// PseudoMFHI64
    0U,	// PseudoMFLO
    0U,	// PseudoMFLO64
    0U,	// PseudoMSUB
    0U,	// PseudoMSUBU
    0U,	// PseudoMTLOHI
    0U,	// PseudoMTLOHI64
    0U,	// PseudoMTLOHI_DSP
    0U,	// PseudoMULT
    0U,	// PseudoMULTu
    0U,	// PseudoPICK_PH
    0U,	// PseudoPICK_QB
    0U,	// PseudoReturn
    0U,	// PseudoReturn64
    0U,	// PseudoSDIV
    0U,	// PseudoSELECTFP_F_D32
    0U,	// PseudoSELECTFP_F_D64
    0U,	// PseudoSELECTFP_F_I
    0U,	// PseudoSELECTFP_F_I64
    0U,	// PseudoSELECTFP_F_S
    0U,	// PseudoSELECTFP_T_D32
    0U,	// PseudoSELECTFP_T_D64
    0U,	// PseudoSELECTFP_T_I
    0U,	// PseudoSELECTFP_T_I64
    0U,	// PseudoSELECTFP_T_S
    0U,	// PseudoSELECT_D32
    0U,	// PseudoSELECT_D64
    0U,	// PseudoSELECT_I
    0U,	// PseudoSELECT_I64
    0U,	// PseudoSELECT_S
    0U,	// PseudoUDIV
    18155U,	// RADDU_W_QB
    33577003U,	// RDDSP
    22791U,	// RDHWR
    22791U,	// RDHWR64
    22791U,	// RDHWR_MM
    21766U,	// REPLV_PH
    18135U,	// REPLV_QB
    33575925U,	// REPL_PH
    33572353U,	// REPL_QB
    19787U,	// RINT_D
    23293U,	// RINT_S
    134240513U,	// ROTR
    134241514U,	// ROTRV
    134241514U,	// ROTRV_MM
    134240513U,	// ROTR_MM
    18992U,	// ROUND_L_D64
    23020U,	// ROUND_L_S
    20168U,	// ROUND_W_D32
    20168U,	// ROUND_W_D64
    20168U,	// ROUND_W_MM
    23342U,	// ROUND_W_S
    23342U,	// ROUND_W_S_MM
    0U,	// Restore16
    0U,	// RestoreX16
    0U,	// RetRA
    0U,	// RetRA16
    134235208U,	// SAT_S_B
    134237279U,	// SAT_S_D
    2281722353U,	// SAT_S_H
    134242618U,	// SAT_S_W
    134235435U,	// SAT_U_B
    134237758U,	// SAT_U_D
    2281722643U,	// SAT_U_H
    134243048U,	// SAT_U_W
    58738423U,	// SB
    58736980U,	// SB16_MM
    58738423U,	// SB64
    58738423U,	// SB_MM
    3966874U,	// SC
    3968802U,	// SCD
    3968802U,	// SCD_R6
    3966874U,	// SC_MM
    3966874U,	// SC_R6
    58740570U,	// SD
    546774U,	// SDBBP
    65946U,	// SDBBP16_MM
    546774U,	// SDBBP_MM
    546774U,	// SDBBP_R6
    58736694U,	// SDC1
    58736694U,	// SDC164
    58736694U,	// SDC1_MM
    58736894U,	// SDC2
    58736894U,	// SDC2_R6
    58736953U,	// SDC3
    25611U,	// SDIV
    25611U,	// SDIV_MM
    58742463U,	// SDL
    58742959U,	// SDR
    1358970999U,	// SDXC1
    1358970999U,	// SDXC164
    17810U,	// SEB
    17810U,	// SEB64
    17810U,	// SEB_MM
    21382U,	// SEH
    21382U,	// SEH64
    21382U,	// SEH_MM
    134243273U,	// SELEQZ
    134243273U,	// SELEQZ64
    134237968U,	// SELEQZ_D
    134241128U,	// SELEQZ_S
    134243246U,	// SELNEZ
    134243246U,	// SELNEZ64
    134237951U,	// SELNEZ_D
    134241118U,	// SELNEZ_S
    151013977U,	// SEL_D
    151018005U,	// SEL_S
    134240345U,	// SEQ
    134239758U,	// SEQi
    58742195U,	// SH
    58736993U,	// SH16_MM
    58742195U,	// SH64
    2281718455U,	// SHF_B
    2281721863U,	// SHF_H
    2281725417U,	// SHF_W
    22463U,	// SHILO
    23761U,	// SHILOV
    134239484U,	// SHLLV_PH
    134235853U,	// SHLLV_QB
    134239421U,	// SHLLV_S_PH
    134242679U,	// SHLLV_S_W
    134239212U,	// SHLL_PH
    134235640U,	// SHLL_QB
    134239334U,	// SHLL_S_PH
    134242519U,	// SHLL_S_W
    134239474U,	// SHRAV_PH
    134235843U,	// SHRAV_QB
    134239322U,	// SHRAV_R_PH
    134235741U,	// SHRAV_R_QB
    134242274U,	// SHRAV_R_W
    134239119U,	// SHRA_PH
    134235563U,	// SHRA_QB
    134239287U,	// SHRA_R_PH
    134235706U,	// SHRA_R_QB
    134242232U,	// SHRA_R_W
    134239504U,	// SHRLV_PH
    134235873U,	// SHRLV_QB
    134239230U,	// SHRL_PH
    134235658U,	// SHRL_QB
    58742195U,	// SH_MM
    2969584334U,	// SLDI_B
    2969586088U,	// SLDI_D
    2969587742U,	// SLDI_H
    2969591377U,	// SLDI_W
    822100628U,	// SLD_B
    822102147U,	// SLD_D
    822104036U,	// SLD_H
    822107506U,	// SLD_W
    134240058U,	// SLL
    134234494U,	// SLL16_MM
    1610635066U,	// SLL64_32
    1610635066U,	// SLL64_64
    2281718512U,	// SLLI_B
    2281720249U,	// SLLI_D
    2281721903U,	// SLLI_H
    2281725538U,	// SLLI_W
    134241476U,	// SLLV
    134241476U,	// SLLV_MM
    134235013U,	// SLL_B
    134236785U,	// SLL_D
    134238371U,	// SLL_H
    134240058U,	// SLL_MM
    134242032U,	// SLL_W
    134241213U,	// SLT
    134241213U,	// SLT64
    134241213U,	// SLT_MM
    134239782U,	// SLTi
    134239782U,	// SLTi64
    134239782U,	// SLTi_MM
    134241321U,	// SLTiu
    134241321U,	// SLTiu64
    134241321U,	// SLTiu_MM
    134241357U,	// SLTu
    134241357U,	// SLTu64
    134241357U,	// SLTu_MM
    134238063U,	// SNE
    134239703U,	// SNEi
    0U,	// SNZ_B_PSEUDO
    0U,	// SNZ_D_PSEUDO
    0U,	// SNZ_H_PSEUDO
    0U,	// SNZ_V_PSEUDO
    0U,	// SNZ_W_PSEUDO
    2952807239U,	// SPLATI_B
    2952808960U,	// SPLATI_D
    2952810614U,	// SPLATI_H
    2952814249U,	// SPLATI_W
    805323906U,	// SPLAT_B
    805326016U,	// SPLAT_D
    805327414U,	// SPLAT_H
    805331393U,	// SPLAT_W
    134234685U,	// SRA
    2281718470U,	// SRAI_B
    2281720224U,	// SRAI_D
    2281721878U,	// SRAI_H
    2281725513U,	// SRAI_W
    134234898U,	// SRARI_B
    134236635U,	// SRARI_D
    2281721937U,	// SRARI_H
    134241924U,	// SRARI_W
    134235051U,	// SRAR_B
    134237015U,	// SRAR_D
    134238486U,	// SRAR_H
    134242296U,	// SRAR_W
    134241455U,	// SRAV
    134241455U,	// SRAV_MM
    134234749U,	// SRA_B
    134236208U,	// SRA_D
    134238157U,	// SRA_H
    134234685U,	// SRA_MM
    134241584U,	// SRA_W
    134240070U,	// SRL
    134234501U,	// SRL16_MM
    2281718520U,	// SRLI_B
    2281720257U,	// SRLI_D
    2281721911U,	// SRLI_H
    2281725546U,	// SRLI_W
    134234916U,	// SRLRI_B
    134236653U,	// SRLRI_D
    2281721955U,	// SRLRI_H
    134241942U,	// SRLRI_W
    134235067U,	// SRLR_B
    134237031U,	// SRLR_D
    134238502U,	// SRLR_H
    134242312U,	// SRLR_W
    134241483U,	// SRLV
    134241483U,	// SRLV_MM
    134235020U,	// SRL_B
    134236810U,	// SRL_D
    134238378U,	// SRL_H
    134240070U,	// SRL_MM
    134242057U,	// SRL_W
    9463U,	// SSNOP
    9463U,	// SSNOP_MM
    58736647U,	// STORE_ACC128
    58736647U,	// STORE_ACC64
    58736647U,	// STORE_ACC64DSP
    58742810U,	// STORE_CCOND_DSP
    58737829U,	// ST_B
    58740080U,	// ST_D
    58741337U,	// ST_H
    58745378U,	// ST_W
    134235902U,	// SUB
    134239183U,	// SUBQH_PH
    134239298U,	// SUBQH_R_PH
    134242242U,	// SUBQH_R_W
    134241847U,	// SUBQH_W
    134239258U,	// SUBQ_PH
    134239355U,	// SUBQ_S_PH
    134242548U,	// SUBQ_S_W
    134235423U,	// SUBSUS_U_B
    134237746U,	// SUBSUS_U_D
    134238983U,	// SUBSUS_U_H
    134243036U,	// SUBSUS_U_W
    134235226U,	// SUBSUU_S_B
    134237319U,	// SUBSUU_S_D
    134238723U,	// SUBSUU_S_H
    134242658U,	// SUBSUU_S_W
    134235188U,	// SUBS_S_B
    134237259U,	// SUBS_S_D
    134238685U,	// SUBS_S_H
    134242598U,	// SUBS_S_W
    134235403U,	// SUBS_U_B
    134237726U,	// SUBS_U_D
    134238963U,	// SUBS_U_H
    134243016U,	// SUBS_U_W
    134234567U,	// SUBU16_MM
    134235611U,	// SUBUH_QB
    134235717U,	// SUBUH_R_QB
    134239456U,	// SUBU_PH
    134235825U,	// SUBU_QB
    134239399U,	// SUBU_S_PH
    134235764U,	// SUBU_S_QB
    2281718618U,	// SUBVI_B
    2281720339U,	// SUBVI_D
    2281721993U,	// SUBVI_H
    2281725628U,	// SUBVI_W
    134235482U,	// SUBV_B
    134237827U,	// SUBV_D
    134239042U,	// SUBV_H
    134243117U,	// SUBV_W
    134235902U,	// SUB_MM
    134241247U,	// SUBu
    134241247U,	// SUBu_MM
    1358971013U,	// SUXC1
    1358971013U,	// SUXC164
    1358971013U,	// SUXC1_MM
    58745730U,	// SW
    58737124U,	// SW16_MM
    58745730U,	// SW64
    58736746U,	// SWC1
    58736746U,	// SWC1_MM
    58736920U,	// SWC2
    58736920U,	// SWC2_R6
    58736965U,	// SWC3
    58742642U,	// SWL
    58742642U,	// SWL64
    58742642U,	// SWL_MM
    3522963U,	// SWM16_MM
    3522792U,	// SWM32_MM
    3528600U,	// SWM_MM
    137295U,	// SWP_MM
    58743059U,	// SWR
    58743059U,	// SWR64
    58743059U,	// SWR_MM
    58745730U,	// SWSP_MM
    1358971027U,	// SWXC1
    1358971027U,	// SWXC1_MM
    58745730U,	// SW_MM
    549939U,	// SYNC
    153021U,	// SYNCI
    549939U,	// SYNC_MM
    546590U,	// SYSCALL
    546590U,	// SYSCALL_MM
    0U,	// SZ_B_PSEUDO
    0U,	// SZ_D_PSEUDO
    0U,	// SZ_H_PSEUDO
    0U,	// SZ_V_PSEUDO
    0U,	// SZ_W_PSEUDO
    0U,	// Save16
    0U,	// SaveX16
    58738423U,	// SbRxRyOffMemX16
    549866U,	// SebRx16
    549878U,	// SehRx16
    4367299U,	// SelBeqZ
    4367272U,	// SelBneZ
    1828886516U,	// SelTBteqZCmp
    1828886024U,	// SelTBteqZCmpi
    1828887485U,	// SelTBteqZSlt
    1828886054U,	// SelTBteqZSlti
    1828887593U,	// SelTBteqZSltiu
    1828887629U,	// SelTBteqZSltu
    1963104244U,	// SelTBtneZCmp
    1963103752U,	// SelTBtneZCmpi
    1963105213U,	// SelTBtneZSlt
    1963103782U,	// SelTBtneZSlti
    1963105321U,	// SelTBtneZSltiu
    1963105357U,	// SelTBtneZSltu
    58742195U,	// ShRxRyOffMemX16
    134240058U,	// SllX16
    16800964U,	// SllvRxRy16
    92576701U,	// SltCCRxRy16
    23485U,	// SltRxRy16
    92575270U,	// SltiCCRxImmX16
    939546150U,	// SltiRxImm16
    22054U,	// SltiRxImmX16
    92576809U,	// SltiuCCRxImmX16
    939547689U,	// SltiuRxImm16
    23593U,	// SltiuRxImmX16
    92576845U,	// SltuCCRxRy16
    23629U,	// SltuRxRy16
    92576845U,	// SltuRxRyRz16
    134234685U,	// SraX16
    16800943U,	// SravRxRy16
    134240070U,	// SrlX16
    16800971U,	// SrlvRxRy16
    134241247U,	// SubuRxRyRz16
    58745730U,	// SwRxRyOffMemX16
    1493197698U,	// SwRxSpImmX16
    0U,	// TAILCALL
    0U,	// TAILCALL64_R
    0U,	// TAILCALL_R
    134240350U,	// TEQ
    33576468U,	// TEQI
    33576468U,	// TEQI_MM
    134240350U,	// TEQ_MM
    134238046U,	// TGE
    33576401U,	// TGEI
    33578018U,	// TGEIU
    33578018U,	// TGEIU_MM
    33576401U,	// TGEI_MM
    134241288U,	// TGEU
    134241288U,	// TGEU_MM
    134238046U,	// TGE_MM
    9458U,	// TLBP
    9458U,	// TLBP_MM
    9469U,	// TLBR
    9469U,	// TLBR_MM
    9448U,	// TLBWI
    9448U,	// TLBWI_MM
    9474U,	// TLBWR
    9474U,	// TLBWR_MM
    134241218U,	// TLT
    33576492U,	// TLTI
    33578032U,	// TLTIU_MM
    33576492U,	// TLTI_MM
    134241363U,	// TLTU
    134241363U,	// TLTU_MM
    134241218U,	// TLT_MM
    134238068U,	// TNE
    33576413U,	// TNEI
    33576413U,	// TNEI_MM
    134238068U,	// TNE_MM
    0U,	// TRAP
    18981U,	// TRUNC_L_D64
    23009U,	// TRUNC_L_S
    20157U,	// TRUNC_W_D32
    20157U,	// TRUNC_W_D64
    20157U,	// TRUNC_W_MM
    23331U,	// TRUNC_W_S
    23331U,	// TRUNC_W_S_MM
    33578032U,	// TTLTIU
    25597U,	// UDIV
    25597U,	// UDIV_MM
    134241335U,	// V3MULU
    134234135U,	// VMM0
    134241350U,	// VMULU
    151012022U,	// VSHF_B
    151013760U,	// VSHF_D
    151015430U,	// VSHF_H
    151018984U,	// VSHF_W
    9486U,	// WAIT
    547767U,	// WAIT_MM
    33577010U,	// WRDSP
    21376U,	// WSBH
    21376U,	// WSBH_MM
    134240507U,	// XOR
    836009U,	// XOR16_MM
    134240507U,	// XOR64
    2281718581U,	// XORI_B
    134240507U,	// XOR_MM
    134241419U,	// XOR_V
    0U,	// XOR_V_D_PSEUDO
    0U,	// XOR_V_H_PSEUDO
    0U,	// XOR_V_W_PSEUDO
    134239770U,	// XORi
    134239770U,	// XORi64
    134239770U,	// XORi_MM
    16799995U,	// XorRxRxRy16
    0U
  };

  static const uint8_t OpInfo2[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    0U,	// DBG_VALUE
    0U,	// REG_SEQUENCE
    0U,	// COPY
    0U,	// BUNDLE
    0U,	// LIFETIME_START
    0U,	// LIFETIME_END
    0U,	// STACKMAP
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    0U,	// STATEPOINT
    0U,	// FRAME_ALLOC
    0U,	// ABSQ_S_PH
    0U,	// ABSQ_S_QB
    0U,	// ABSQ_S_W
    0U,	// ADD
    0U,	// ADDIUPC
    0U,	// ADDIUPC_MM
    0U,	// ADDIUR1SP_MM
    0U,	// ADDIUR2_MM
    0U,	// ADDIUS5_MM
    0U,	// ADDIUSP_MM
    0U,	// ADDQH_PH
    0U,	// ADDQH_R_PH
    0U,	// ADDQH_R_W
    0U,	// ADDQH_W
    0U,	// ADDQ_PH
    0U,	// ADDQ_S_PH
    0U,	// ADDQ_S_W
    0U,	// ADDSC
    0U,	// ADDS_A_B
    0U,	// ADDS_A_D
    0U,	// ADDS_A_H
    0U,	// ADDS_A_W
    0U,	// ADDS_S_B
    0U,	// ADDS_S_D
    0U,	// ADDS_S_H
    0U,	// ADDS_S_W
    0U,	// ADDS_U_B
    0U,	// ADDS_U_D
    0U,	// ADDS_U_H
    0U,	// ADDS_U_W
    0U,	// ADDU16_MM
    0U,	// ADDUH_QB
    0U,	// ADDUH_R_QB
    0U,	// ADDU_PH
    0U,	// ADDU_QB
    0U,	// ADDU_S_PH
    0U,	// ADDU_S_QB
    0U,	// ADDVI_B
    0U,	// ADDVI_D
    0U,	// ADDVI_H
    0U,	// ADDVI_W
    0U,	// ADDV_B
    0U,	// ADDV_D
    0U,	// ADDV_H
    0U,	// ADDV_W
    0U,	// ADDWC
    0U,	// ADD_A_B
    0U,	// ADD_A_D
    0U,	// ADD_A_H
    0U,	// ADD_A_W
    0U,	// ADD_MM
    0U,	// ADDi
    0U,	// ADDi_MM
    0U,	// ADDiu
    0U,	// ADDiu_MM
    0U,	// ADDu
    0U,	// ADDu_MM
    0U,	// ADJCALLSTACKDOWN
    0U,	// ADJCALLSTACKUP
    4U,	// ALIGN
    0U,	// ALUIPC
    0U,	// AND
    0U,	// AND16_MM
    0U,	// AND64
    0U,	// ANDI16_MM
    0U,	// ANDI_B
    0U,	// AND_MM
    0U,	// AND_V
    0U,	// AND_V_D_PSEUDO
    0U,	// AND_V_H_PSEUDO
    0U,	// AND_V_W_PSEUDO
    1U,	// ANDi
    1U,	// ANDi64
    1U,	// ANDi_MM
    1U,	// APPEND
    0U,	// ASUB_S_B
    0U,	// ASUB_S_D
    0U,	// ASUB_S_H
    0U,	// ASUB_S_W
    0U,	// ASUB_U_B
    0U,	// ASUB_U_D
    0U,	// ASUB_U_H
    0U,	// ASUB_U_W
    0U,	// ATOMIC_CMP_SWAP_I16
    0U,	// ATOMIC_CMP_SWAP_I32
    0U,	// ATOMIC_CMP_SWAP_I64
    0U,	// ATOMIC_CMP_SWAP_I8
    0U,	// ATOMIC_LOAD_ADD_I16
    0U,	// ATOMIC_LOAD_ADD_I32
    0U,	// ATOMIC_LOAD_ADD_I64
    0U,	// ATOMIC_LOAD_ADD_I8
    0U,	// ATOMIC_LOAD_AND_I16
    0U,	// ATOMIC_LOAD_AND_I32
    0U,	// ATOMIC_LOAD_AND_I64
    0U,	// ATOMIC_LOAD_AND_I8
    0U,	// ATOMIC_LOAD_NAND_I16
    0U,	// ATOMIC_LOAD_NAND_I32
    0U,	// ATOMIC_LOAD_NAND_I64
    0U,	// ATOMIC_LOAD_NAND_I8
    0U,	// ATOMIC_LOAD_OR_I16
    0U,	// ATOMIC_LOAD_OR_I32
    0U,	// ATOMIC_LOAD_OR_I64
    0U,	// ATOMIC_LOAD_OR_I8
    0U,	// ATOMIC_LOAD_SUB_I16
    0U,	// ATOMIC_LOAD_SUB_I32
    0U,	// ATOMIC_LOAD_SUB_I64
    0U,	// ATOMIC_LOAD_SUB_I8
    0U,	// ATOMIC_LOAD_XOR_I16
    0U,	// ATOMIC_LOAD_XOR_I32
    0U,	// ATOMIC_LOAD_XOR_I64
    0U,	// ATOMIC_LOAD_XOR_I8
    0U,	// ATOMIC_SWAP_I16
    0U,	// ATOMIC_SWAP_I32
    0U,	// ATOMIC_SWAP_I64
    0U,	// ATOMIC_SWAP_I8
    0U,	// AUI
    0U,	// AUIPC
    0U,	// AVER_S_B
    0U,	// AVER_S_D
    0U,	// AVER_S_H
    0U,	// AVER_S_W
    0U,	// AVER_U_B
    0U,	// AVER_U_D
    0U,	// AVER_U_H
    0U,	// AVER_U_W
    0U,	// AVE_S_B
    0U,	// AVE_S_D
    0U,	// AVE_S_H
    0U,	// AVE_S_W
    0U,	// AVE_U_B
    0U,	// AVE_U_D
    0U,	// AVE_U_H
    0U,	// AVE_U_W
    0U,	// AddiuRxImmX16
    0U,	// AddiuRxPcImmX16
    0U,	// AddiuRxRxImm16
    0U,	// AddiuRxRxImmX16
    0U,	// AddiuRxRyOffMemX16
    0U,	// AddiuSpImm16
    0U,	// AddiuSpImmX16
    0U,	// AdduRxRyRz16
    0U,	// AndRxRxRy16
    0U,	// B
    0U,	// B16_MM
    0U,	// BADDu
    0U,	// BAL
    0U,	// BALC
    1U,	// BALIGN
    0U,	// BAL_BR
    0U,	// BBIT0
    0U,	// BBIT032
    0U,	// BBIT1
    0U,	// BBIT132
    0U,	// BC
    0U,	// BC0F
    0U,	// BC0FL
    0U,	// BC0T
    0U,	// BC0TL
    0U,	// BC1EQZ
    0U,	// BC1F
    0U,	// BC1FL
    0U,	// BC1F_MM
    0U,	// BC1NEZ
    0U,	// BC1T
    0U,	// BC1TL
    0U,	// BC1T_MM
    0U,	// BC2EQZ
    0U,	// BC2F
    0U,	// BC2FL
    0U,	// BC2NEZ
    0U,	// BC2T
    0U,	// BC2TL
    0U,	// BC3F
    0U,	// BC3FL
    0U,	// BC3T
    0U,	// BC3TL
    0U,	// BCLRI_B
    0U,	// BCLRI_D
    0U,	// BCLRI_H
    0U,	// BCLRI_W
    0U,	// BCLR_B
    0U,	// BCLR_D
    0U,	// BCLR_H
    0U,	// BCLR_W
    0U,	// BEQ
    0U,	// BEQ64
    0U,	// BEQC
    0U,	// BEQL
    0U,	// BEQZ16_MM
    0U,	// BEQZALC
    0U,	// BEQZC
    0U,	// BEQZC_MM
    0U,	// BEQ_MM
    0U,	// BGEC
    0U,	// BGEUC
    0U,	// BGEZ
    0U,	// BGEZ64
    0U,	// BGEZAL
    0U,	// BGEZALC
    0U,	// BGEZALL
    0U,	// BGEZALS_MM
    0U,	// BGEZAL_MM
    0U,	// BGEZC
    0U,	// BGEZL
    0U,	// BGEZ_MM
    0U,	// BGTZ
    0U,	// BGTZ64
    0U,	// BGTZALC
    0U,	// BGTZC
    0U,	// BGTZL
    0U,	// BGTZ_MM
    1U,	// BINSLI_B
    1U,	// BINSLI_D
    1U,	// BINSLI_H
    1U,	// BINSLI_W
    2U,	// BINSL_B
    2U,	// BINSL_D
    2U,	// BINSL_H
    2U,	// BINSL_W
    1U,	// BINSRI_B
    1U,	// BINSRI_D
    1U,	// BINSRI_H
    1U,	// BINSRI_W
    2U,	// BINSR_B
    2U,	// BINSR_D
    2U,	// BINSR_H
    2U,	// BINSR_W
    0U,	// BITREV
    0U,	// BITSWAP
    0U,	// BLEZ
    0U,	// BLEZ64
    0U,	// BLEZALC
    0U,	// BLEZC
    0U,	// BLEZL
    0U,	// BLEZ_MM
    0U,	// BLTC
    0U,	// BLTUC
    0U,	// BLTZ
    0U,	// BLTZ64
    0U,	// BLTZAL
    0U,	// BLTZALC
    0U,	// BLTZALL
    0U,	// BLTZALS_MM
    0U,	// BLTZAL_MM
    0U,	// BLTZC
    0U,	// BLTZL
    0U,	// BLTZ_MM
    1U,	// BMNZI_B
    2U,	// BMNZ_V
    1U,	// BMZI_B
    2U,	// BMZ_V
    0U,	// BNE
    0U,	// BNE64
    0U,	// BNEC
    0U,	// BNEGI_B
    0U,	// BNEGI_D
    0U,	// BNEGI_H
    0U,	// BNEGI_W
    0U,	// BNEG_B
    0U,	// BNEG_D
    0U,	// BNEG_H
    0U,	// BNEG_W
    0U,	// BNEL
    0U,	// BNEZ16_MM
    0U,	// BNEZALC
    0U,	// BNEZC
    0U,	// BNEZC_MM
    0U,	// BNE_MM
    0U,	// BNVC
    0U,	// BNZ_B
    0U,	// BNZ_D
    0U,	// BNZ_H
    0U,	// BNZ_V
    0U,	// BNZ_W
    0U,	// BOVC
    0U,	// BPOSGE32
    0U,	// BPOSGE32_PSEUDO
    0U,	// BREAK
    0U,	// BREAK16_MM
    0U,	// BREAK_MM
    1U,	// BSELI_B
    0U,	// BSEL_D_PSEUDO
    0U,	// BSEL_FD_PSEUDO
    0U,	// BSEL_FW_PSEUDO
    0U,	// BSEL_H_PSEUDO
    2U,	// BSEL_V
    0U,	// BSEL_W_PSEUDO
    0U,	// BSETI_B
    0U,	// BSETI_D
    0U,	// BSETI_H
    0U,	// BSETI_W
    0U,	// BSET_B
    0U,	// BSET_D
    0U,	// BSET_H
    0U,	// BSET_W
    0U,	// BZ_B
    0U,	// BZ_D
    0U,	// BZ_H
    0U,	// BZ_V
    0U,	// BZ_W
    0U,	// B_MM_Pseudo
    0U,	// BeqzRxImm16
    0U,	// BeqzRxImmX16
    0U,	// Bimm16
    0U,	// BimmX16
    0U,	// BnezRxImm16
    0U,	// BnezRxImmX16
    0U,	// Break16
    0U,	// Bteqz16
    0U,	// BteqzT8CmpX16
    0U,	// BteqzT8CmpiX16
    0U,	// BteqzT8SltX16
    0U,	// BteqzT8SltiX16
    0U,	// BteqzT8SltiuX16
    0U,	// BteqzT8SltuX16
    0U,	// BteqzX16
    0U,	// Btnez16
    0U,	// BtnezT8CmpX16
    0U,	// BtnezT8CmpiX16
    0U,	// BtnezT8SltX16
    0U,	// BtnezT8SltiX16
    0U,	// BtnezT8SltiuX16
    0U,	// BtnezT8SltuX16
    0U,	// BtnezX16
    0U,	// BuildPairF64
    0U,	// BuildPairF64_64
    0U,	// CACHE
    0U,	// CACHE_MM
    0U,	// CACHE_R6
    0U,	// CEIL_L_D64
    0U,	// CEIL_L_S
    0U,	// CEIL_W_D32
    0U,	// CEIL_W_D64
    0U,	// CEIL_W_MM
    0U,	// CEIL_W_S
    0U,	// CEIL_W_S_MM
    0U,	// CEQI_B
    0U,	// CEQI_D
    0U,	// CEQI_H
    0U,	// CEQI_W
    0U,	// CEQ_B
    0U,	// CEQ_D
    0U,	// CEQ_H
    0U,	// CEQ_W
    0U,	// CFC1
    0U,	// CFC1_MM
    0U,	// CFCMSA
    5U,	// CINS
    5U,	// CINS32
    0U,	// CLASS_D
    0U,	// CLASS_S
    0U,	// CLEI_S_B
    0U,	// CLEI_S_D
    0U,	// CLEI_S_H
    0U,	// CLEI_S_W
    0U,	// CLEI_U_B
    0U,	// CLEI_U_D
    0U,	// CLEI_U_H
    0U,	// CLEI_U_W
    0U,	// CLE_S_B
    0U,	// CLE_S_D
    0U,	// CLE_S_H
    0U,	// CLE_S_W
    0U,	// CLE_U_B
    0U,	// CLE_U_D
    0U,	// CLE_U_H
    0U,	// CLE_U_W
    0U,	// CLO
    0U,	// CLO_MM
    0U,	// CLO_R6
    0U,	// CLTI_S_B
    0U,	// CLTI_S_D
    0U,	// CLTI_S_H
    0U,	// CLTI_S_W
    0U,	// CLTI_U_B
    0U,	// CLTI_U_D
    0U,	// CLTI_U_H
    0U,	// CLTI_U_W
    0U,	// CLT_S_B
    0U,	// CLT_S_D
    0U,	// CLT_S_H
    0U,	// CLT_S_W
    0U,	// CLT_U_B
    0U,	// CLT_U_D
    0U,	// CLT_U_H
    0U,	// CLT_U_W
    0U,	// CLZ
    0U,	// CLZ_MM
    0U,	// CLZ_R6
    0U,	// CMPGDU_EQ_QB
    0U,	// CMPGDU_LE_QB
    0U,	// CMPGDU_LT_QB
    0U,	// CMPGU_EQ_QB
    0U,	// CMPGU_LE_QB
    0U,	// CMPGU_LT_QB
    0U,	// CMPU_EQ_QB
    0U,	// CMPU_LE_QB
    0U,	// CMPU_LT_QB
    0U,	// CMP_EQ_D
    0U,	// CMP_EQ_PH
    0U,	// CMP_EQ_S
    0U,	// CMP_F_D
    0U,	// CMP_F_S
    0U,	// CMP_LE_D
    0U,	// CMP_LE_PH
    0U,	// CMP_LE_S
    0U,	// CMP_LT_D
    0U,	// CMP_LT_PH
    0U,	// CMP_LT_S
    0U,	// CMP_SAF_D
    0U,	// CMP_SAF_S
    0U,	// CMP_SEQ_D
    0U,	// CMP_SEQ_S
    0U,	// CMP_SLE_D
    0U,	// CMP_SLE_S
    0U,	// CMP_SLT_D
    0U,	// CMP_SLT_S
    0U,	// CMP_SUEQ_D
    0U,	// CMP_SUEQ_S
    0U,	// CMP_SULE_D
    0U,	// CMP_SULE_S
    0U,	// CMP_SULT_D
    0U,	// CMP_SULT_S
    0U,	// CMP_SUN_D
    0U,	// CMP_SUN_S
    0U,	// CMP_UEQ_D
    0U,	// CMP_UEQ_S
    0U,	// CMP_ULE_D
    0U,	// CMP_ULE_S
    0U,	// CMP_ULT_D
    0U,	// CMP_ULT_S
    0U,	// CMP_UN_D
    0U,	// CMP_UN_S
    0U,	// CONSTPOOL_ENTRY
    0U,	// COPY_FD_PSEUDO
    0U,	// COPY_FW_PSEUDO
    8U,	// COPY_S_B
    8U,	// COPY_S_D
    8U,	// COPY_S_H
    8U,	// COPY_S_W
    8U,	// COPY_U_B
    8U,	// COPY_U_D
    8U,	// COPY_U_H
    8U,	// COPY_U_W
    0U,	// CTC1
    0U,	// CTC1_MM
    0U,	// CTCMSA
    0U,	// CVT_D32_S
    0U,	// CVT_D32_W
    0U,	// CVT_D32_W_MM
    0U,	// CVT_D64_L
    0U,	// CVT_D64_S
    0U,	// CVT_D64_W
    0U,	// CVT_D_S_MM
    0U,	// CVT_L_D64
    0U,	// CVT_L_D64_MM
    0U,	// CVT_L_S
    0U,	// CVT_L_S_MM
    0U,	// CVT_S_D32
    0U,	// CVT_S_D32_MM
    0U,	// CVT_S_D64
    0U,	// CVT_S_L
    0U,	// CVT_S_W
    0U,	// CVT_S_W_MM
    0U,	// CVT_W_D32
    0U,	// CVT_W_D64
    0U,	// CVT_W_MM
    0U,	// CVT_W_S
    0U,	// CVT_W_S_MM
    0U,	// C_EQ_D32
    0U,	// C_EQ_D64
    0U,	// C_EQ_S
    0U,	// C_F_D32
    0U,	// C_F_D64
    0U,	// C_F_S
    0U,	// C_LE_D32
    0U,	// C_LE_D64
    0U,	// C_LE_S
    0U,	// C_LT_D32
    0U,	// C_LT_D64
    0U,	// C_LT_S
    0U,	// C_NGE_D32
    0U,	// C_NGE_D64
    0U,	// C_NGE_S
    0U,	// C_NGLE_D32
    0U,	// C_NGLE_D64
    0U,	// C_NGLE_S
    0U,	// C_NGL_D32
    0U,	// C_NGL_D64
    0U,	// C_NGL_S
    0U,	// C_NGT_D32
    0U,	// C_NGT_D64
    0U,	// C_NGT_S
    0U,	// C_OLE_D32
    0U,	// C_OLE_D64
    0U,	// C_OLE_S
    0U,	// C_OLT_D32
    0U,	// C_OLT_D64
    0U,	// C_OLT_S
    0U,	// C_SEQ_D32
    0U,	// C_SEQ_D64
    0U,	// C_SEQ_S
    0U,	// C_SF_D32
    0U,	// C_SF_D64
    0U,	// C_SF_S
    0U,	// C_UEQ_D32
    0U,	// C_UEQ_D64
    0U,	// C_UEQ_S
    0U,	// C_ULE_D32
    0U,	// C_ULE_D64
    0U,	// C_ULE_S
    0U,	// C_ULT_D32
    0U,	// C_ULT_D64
    0U,	// C_ULT_S
    0U,	// C_UN_D32
    0U,	// C_UN_D64
    0U,	// C_UN_S
    0U,	// CmpRxRy16
    0U,	// CmpiRxImm16
    0U,	// CmpiRxImmX16
    0U,	// Constant32
    0U,	// DADD
    0U,	// DADDi
    0U,	// DADDiu
    0U,	// DADDu
    0U,	// DAHI
    4U,	// DALIGN
    0U,	// DATI
    0U,	// DAUI
    0U,	// DBITSWAP
    0U,	// DCLO
    0U,	// DCLO_R6
    0U,	// DCLZ
    0U,	// DCLZ_R6
    0U,	// DDIV
    0U,	// DDIVU
    0U,	// DERET
    0U,	// DERET_MM
    21U,	// DEXT
    21U,	// DEXTM
    21U,	// DEXTU
    0U,	// DI
    21U,	// DINS
    21U,	// DINSM
    21U,	// DINSU
    0U,	// DIV
    0U,	// DIVU
    0U,	// DIV_S_B
    0U,	// DIV_S_D
    0U,	// DIV_S_H
    0U,	// DIV_S_W
    0U,	// DIV_U_B
    0U,	// DIV_U_D
    0U,	// DIV_U_H
    0U,	// DIV_U_W
    0U,	// DI_MM
    4U,	// DLSA
    4U,	// DLSA_R6
    1U,	// DMFC0
    0U,	// DMFC1
    1U,	// DMFC2
    0U,	// DMOD
    0U,	// DMODU
    1U,	// DMTC0
    0U,	// DMTC1
    1U,	// DMTC2
    0U,	// DMUH
    0U,	// DMUHU
    0U,	// DMUL
    0U,	// DMULT
    0U,	// DMULTu
    0U,	// DMULU
    0U,	// DMUL_R6
    0U,	// DOTP_S_D
    0U,	// DOTP_S_H
    0U,	// DOTP_S_W
    0U,	// DOTP_U_D
    0U,	// DOTP_U_H
    0U,	// DOTP_U_W
    2U,	// DPADD_S_D
    2U,	// DPADD_S_H
    2U,	// DPADD_S_W
    2U,	// DPADD_U_D
    2U,	// DPADD_U_H
    2U,	// DPADD_U_W
    0U,	// DPAQX_SA_W_PH
    0U,	// DPAQX_S_W_PH
    0U,	// DPAQ_SA_L_W
    0U,	// DPAQ_S_W_PH
    0U,	// DPAU_H_QBL
    0U,	// DPAU_H_QBR
    0U,	// DPAX_W_PH
    0U,	// DPA_W_PH
    0U,	// DPOP
    0U,	// DPSQX_SA_W_PH
    0U,	// DPSQX_S_W_PH
    0U,	// DPSQ_SA_L_W
    0U,	// DPSQ_S_W_PH
    2U,	// DPSUB_S_D
    2U,	// DPSUB_S_H
    2U,	// DPSUB_S_W
    2U,	// DPSUB_U_D
    2U,	// DPSUB_U_H
    2U,	// DPSUB_U_W
    0U,	// DPSU_H_QBL
    0U,	// DPSU_H_QBR
    0U,	// DPSX_W_PH
    0U,	// DPS_W_PH
    1U,	// DROTR
    1U,	// DROTR32
    0U,	// DROTRV
    0U,	// DSBH
    0U,	// DSDIV
    0U,	// DSHD
    1U,	// DSLL
    1U,	// DSLL32
    0U,	// DSLL64_32
    0U,	// DSLLV
    1U,	// DSRA
    1U,	// DSRA32
    0U,	// DSRAV
    1U,	// DSRL
    1U,	// DSRL32
    0U,	// DSRLV
    0U,	// DSUB
    0U,	// DSUBu
    0U,	// DUDIV
    0U,	// DivRxRy16
    0U,	// DivuRxRy16
    0U,	// EHB
    0U,	// EHB_MM
    0U,	// EI
    0U,	// EI_MM
    0U,	// ERET
    0U,	// ERET_MM
    21U,	// EXT
    1U,	// EXTP
    1U,	// EXTPDP
    0U,	// EXTPDPV
    0U,	// EXTPV
    0U,	// EXTRV_RS_W
    0U,	// EXTRV_R_W
    0U,	// EXTRV_S_H
    0U,	// EXTRV_W
    1U,	// EXTR_RS_W
    1U,	// EXTR_R_W
    1U,	// EXTR_S_H
    1U,	// EXTR_W
    5U,	// EXTS
    5U,	// EXTS32
    21U,	// EXT_MM
    0U,	// ExtractElementF64
    0U,	// ExtractElementF64_64
    0U,	// FABS_D
    0U,	// FABS_D32
    0U,	// FABS_D64
    0U,	// FABS_MM
    0U,	// FABS_S
    0U,	// FABS_S_MM
    0U,	// FABS_W
    0U,	// FADD_D
    0U,	// FADD_D32
    0U,	// FADD_D64
    0U,	// FADD_MM
    0U,	// FADD_S
    0U,	// FADD_S_MM
    0U,	// FADD_W
    0U,	// FCAF_D
    0U,	// FCAF_W
    0U,	// FCEQ_D
    0U,	// FCEQ_W
    0U,	// FCLASS_D
    0U,	// FCLASS_W
    0U,	// FCLE_D
    0U,	// FCLE_W
    0U,	// FCLT_D
    0U,	// FCLT_W
    0U,	// FCMP_D32
    0U,	// FCMP_D32_MM
    0U,	// FCMP_D64
    0U,	// FCMP_S32
    0U,	// FCMP_S32_MM
    0U,	// FCNE_D
    0U,	// FCNE_W
    0U,	// FCOR_D
    0U,	// FCOR_W
    0U,	// FCUEQ_D
    0U,	// FCUEQ_W
    0U,	// FCULE_D
    0U,	// FCULE_W
    0U,	// FCULT_D
    0U,	// FCULT_W
    0U,	// FCUNE_D
    0U,	// FCUNE_W
    0U,	// FCUN_D
    0U,	// FCUN_W
    0U,	// FDIV_D
    0U,	// FDIV_D32
    0U,	// FDIV_D64
    0U,	// FDIV_MM
    0U,	// FDIV_S
    0U,	// FDIV_S_MM
    0U,	// FDIV_W
    0U,	// FEXDO_H
    0U,	// FEXDO_W
    0U,	// FEXP2_D
    0U,	// FEXP2_D_1_PSEUDO
    0U,	// FEXP2_W
    0U,	// FEXP2_W_1_PSEUDO
    0U,	// FEXUPL_D
    0U,	// FEXUPL_W
    0U,	// FEXUPR_D
    0U,	// FEXUPR_W
    0U,	// FFINT_S_D
    0U,	// FFINT_S_W
    0U,	// FFINT_U_D
    0U,	// FFINT_U_W
    0U,	// FFQL_D
    0U,	// FFQL_W
    0U,	// FFQR_D
    0U,	// FFQR_W
    0U,	// FILL_B
    0U,	// FILL_D
    0U,	// FILL_FD_PSEUDO
    0U,	// FILL_FW_PSEUDO
    0U,	// FILL_H
    0U,	// FILL_W
    0U,	// FLOG2_D
    0U,	// FLOG2_W
    0U,	// FLOOR_L_D64
    0U,	// FLOOR_L_S
    0U,	// FLOOR_W_D32
    0U,	// FLOOR_W_D64
    0U,	// FLOOR_W_MM
    0U,	// FLOOR_W_S
    0U,	// FLOOR_W_S_MM
    2U,	// FMADD_D
    2U,	// FMADD_W
    0U,	// FMAX_A_D
    0U,	// FMAX_A_W
    0U,	// FMAX_D
    0U,	// FMAX_W
    0U,	// FMIN_A_D
    0U,	// FMIN_A_W
    0U,	// FMIN_D
    0U,	// FMIN_W
    0U,	// FMOV_D32
    0U,	// FMOV_D32_MM
    0U,	// FMOV_D64
    0U,	// FMOV_S
    0U,	// FMOV_S_MM
    2U,	// FMSUB_D
    2U,	// FMSUB_W
    0U,	// FMUL_D
    0U,	// FMUL_D32
    0U,	// FMUL_D64
    0U,	// FMUL_MM
    0U,	// FMUL_S
    0U,	// FMUL_S_MM
    0U,	// FMUL_W
    0U,	// FNEG_D32
    0U,	// FNEG_D64
    0U,	// FNEG_MM
    0U,	// FNEG_S
    0U,	// FNEG_S_MM
    0U,	// FRCP_D
    0U,	// FRCP_W
    0U,	// FRINT_D
    0U,	// FRINT_W
    0U,	// FRSQRT_D
    0U,	// FRSQRT_W
    0U,	// FSAF_D
    0U,	// FSAF_W
    0U,	// FSEQ_D
    0U,	// FSEQ_W
    0U,	// FSLE_D
    0U,	// FSLE_W
    0U,	// FSLT_D
    0U,	// FSLT_W
    0U,	// FSNE_D
    0U,	// FSNE_W
    0U,	// FSOR_D
    0U,	// FSOR_W
    0U,	// FSQRT_D
    0U,	// FSQRT_D32
    0U,	// FSQRT_D64
    0U,	// FSQRT_MM
    0U,	// FSQRT_S
    0U,	// FSQRT_S_MM
    0U,	// FSQRT_W
    0U,	// FSUB_D
    0U,	// FSUB_D32
    0U,	// FSUB_D64
    0U,	// FSUB_MM
    0U,	// FSUB_S
    0U,	// FSUB_S_MM
    0U,	// FSUB_W
    0U,	// FSUEQ_D
    0U,	// FSUEQ_W
    0U,	// FSULE_D
    0U,	// FSULE_W
    0U,	// FSULT_D
    0U,	// FSULT_W
    0U,	// FSUNE_D
    0U,	// FSUNE_W
    0U,	// FSUN_D
    0U,	// FSUN_W
    0U,	// FTINT_S_D
    0U,	// FTINT_S_W
    0U,	// FTINT_U_D
    0U,	// FTINT_U_W
    0U,	// FTQ_H
    0U,	// FTQ_W
    0U,	// FTRUNC_S_D
    0U,	// FTRUNC_S_W
    0U,	// FTRUNC_U_D
    0U,	// FTRUNC_U_W
    0U,	// GotPrologue16
    0U,	// HADD_S_D
    0U,	// HADD_S_H
    0U,	// HADD_S_W
    0U,	// HADD_U_D
    0U,	// HADD_U_H
    0U,	// HADD_U_W
    0U,	// HSUB_S_D
    0U,	// HSUB_S_H
    0U,	// HSUB_S_W
    0U,	// HSUB_U_D
    0U,	// HSUB_U_H
    0U,	// HSUB_U_W
    0U,	// ILVEV_B
    0U,	// ILVEV_D
    0U,	// ILVEV_H
    0U,	// ILVEV_W
    0U,	// ILVL_B
    0U,	// ILVL_D
    0U,	// ILVL_H
    0U,	// ILVL_W
    0U,	// ILVOD_B
    0U,	// ILVOD_D
    0U,	// ILVOD_H
    0U,	// ILVOD_W
    0U,	// ILVR_B
    0U,	// ILVR_D
    0U,	// ILVR_H
    0U,	// ILVR_W
    21U,	// INS
    0U,	// INSERT_B
    0U,	// INSERT_B_VIDX_PSEUDO
    0U,	// INSERT_D
    0U,	// INSERT_D_VIDX_PSEUDO
    0U,	// INSERT_FD_PSEUDO
    0U,	// INSERT_FD_VIDX_PSEUDO
    0U,	// INSERT_FW_PSEUDO
    0U,	// INSERT_FW_VIDX_PSEUDO
    0U,	// INSERT_H
    0U,	// INSERT_H_VIDX_PSEUDO
    0U,	// INSERT_W
    0U,	// INSERT_W_VIDX_PSEUDO
    0U,	// INSV
    0U,	// INSVE_B
    0U,	// INSVE_D
    0U,	// INSVE_H
    0U,	// INSVE_W
    21U,	// INS_MM
    0U,	// J
    0U,	// JAL
    0U,	// JALR
    0U,	// JALR16_MM
    0U,	// JALR64
    0U,	// JALR64Pseudo
    0U,	// JALRPseudo
    0U,	// JALRS16_MM
    0U,	// JALRS_MM
    0U,	// JALR_HB
    0U,	// JALR_MM
    0U,	// JALS_MM
    0U,	// JALX
    0U,	// JALX_MM
    0U,	// JAL_MM
    0U,	// JIALC
    0U,	// JIC
    0U,	// JR
    0U,	// JR16_MM
    0U,	// JR64
    0U,	// JRADDIUSP
    0U,	// JRC16_MM
    0U,	// JR_HB
    0U,	// JR_HB_R6
    0U,	// JR_MM
    0U,	// J_MM
    0U,	// Jal16
    0U,	// JalB16
    0U,	// JalOneReg
    0U,	// JalTwoReg
    0U,	// JrRa16
    0U,	// JrcRa16
    0U,	// JrcRx16
    0U,	// JumpLinkReg16
    0U,	// LB
    0U,	// LB64
    0U,	// LBU16_MM
    0U,	// LBUX
    0U,	// LB_MM
    0U,	// LBu
    0U,	// LBu64
    0U,	// LBu_MM
    0U,	// LD
    0U,	// LDC1
    0U,	// LDC164
    0U,	// LDC1_MM
    0U,	// LDC2
    0U,	// LDC2_R6
    0U,	// LDC3
    0U,	// LDI_B
    0U,	// LDI_D
    0U,	// LDI_H
    0U,	// LDI_W
    0U,	// LDL
    0U,	// LDPC
    0U,	// LDR
    0U,	// LDXC1
    0U,	// LDXC164
    0U,	// LD_B
    0U,	// LD_D
    0U,	// LD_H
    0U,	// LD_W
    0U,	// LEA_ADDiu
    0U,	// LEA_ADDiu64
    0U,	// LEA_ADDiu_MM
    0U,	// LH
    0U,	// LH64
    0U,	// LHU16_MM
    0U,	// LHX
    0U,	// LH_MM
    0U,	// LHu
    0U,	// LHu64
    0U,	// LHu_MM
    0U,	// LI16_MM
    0U,	// LL
    0U,	// LLD
    0U,	// LLD_R6
    0U,	// LL_MM
    0U,	// LL_R6
    0U,	// LOAD_ACC128
    0U,	// LOAD_ACC64
    0U,	// LOAD_ACC64DSP
    0U,	// LOAD_CCOND_DSP
    0U,	// LONG_BRANCH_ADDiu
    0U,	// LONG_BRANCH_DADDiu
    0U,	// LONG_BRANCH_LUi
    4U,	// LSA
    4U,	// LSA_R6
    0U,	// LUXC1
    0U,	// LUXC164
    0U,	// LUXC1_MM
    0U,	// LUi
    0U,	// LUi64
    0U,	// LUi_MM
    0U,	// LW
    0U,	// LW16_MM
    0U,	// LW64
    0U,	// LWC1
    0U,	// LWC1_MM
    0U,	// LWC2
    0U,	// LWC2_R6
    0U,	// LWC3
    0U,	// LWGP_MM
    0U,	// LWL
    0U,	// LWL64
    0U,	// LWL_MM
    0U,	// LWM16_MM
    0U,	// LWM32_MM
    0U,	// LWM_MM
    0U,	// LWPC
    0U,	// LWP_MM
    0U,	// LWR
    0U,	// LWR64
    0U,	// LWR_MM
    0U,	// LWSP_MM
    0U,	// LWUPC
    0U,	// LWU_MM
    0U,	// LWX
    0U,	// LWXC1
    0U,	// LWXC1_MM
    0U,	// LWXS_MM
    0U,	// LW_MM
    0U,	// LWu
    0U,	// LbRxRyOffMemX16
    0U,	// LbuRxRyOffMemX16
    0U,	// LhRxRyOffMemX16
    0U,	// LhuRxRyOffMemX16
    0U,	// LiRxImm16
    0U,	// LiRxImmAlignX16
    0U,	// LiRxImmX16
    0U,	// LoadAddr32Imm
    0U,	// LoadAddr32Reg
    0U,	// LoadImm32Reg
    0U,	// LoadImm64Reg
    0U,	// LwConstant32
    0U,	// LwRxPcTcp16
    0U,	// LwRxPcTcpX16
    0U,	// LwRxRyOffMemX16
    0U,	// LwRxSpImmX16
    0U,	// MADD
    2U,	// MADDF_D
    2U,	// MADDF_S
    2U,	// MADDR_Q_H
    2U,	// MADDR_Q_W
    0U,	// MADDU
    0U,	// MADDU_DSP
    0U,	// MADDU_MM
    2U,	// MADDV_B
    2U,	// MADDV_D
    2U,	// MADDV_H
    2U,	// MADDV_W
    20U,	// MADD_D32
    20U,	// MADD_D32_MM
    20U,	// MADD_D64
    0U,	// MADD_DSP
    0U,	// MADD_MM
    2U,	// MADD_Q_H
    2U,	// MADD_Q_W
    20U,	// MADD_S
    20U,	// MADD_S_MM
    0U,	// MAQ_SA_W_PHL
    0U,	// MAQ_SA_W_PHR
    0U,	// MAQ_S_W_PHL
    0U,	// MAQ_S_W_PHR
    0U,	// MAXA_D
    0U,	// MAXA_S
    0U,	// MAXI_S_B
    0U,	// MAXI_S_D
    0U,	// MAXI_S_H
    0U,	// MAXI_S_W
    0U,	// MAXI_U_B
    0U,	// MAXI_U_D
    0U,	// MAXI_U_H
    0U,	// MAXI_U_W
    0U,	// MAX_A_B
    0U,	// MAX_A_D
    0U,	// MAX_A_H
    0U,	// MAX_A_W
    0U,	// MAX_D
    0U,	// MAX_S
    0U,	// MAX_S_B
    0U,	// MAX_S_D
    0U,	// MAX_S_H
    0U,	// MAX_S_W
    0U,	// MAX_U_B
    0U,	// MAX_U_D
    0U,	// MAX_U_H
    0U,	// MAX_U_W
    1U,	// MFC0
    0U,	// MFC1
    0U,	// MFC1_MM
    1U,	// MFC2
    0U,	// MFHC1_D32
    0U,	// MFHC1_D64
    0U,	// MFHC1_MM
    0U,	// MFHI
    0U,	// MFHI16_MM
    0U,	// MFHI64
    0U,	// MFHI_DSP
    0U,	// MFHI_MM
    0U,	// MFLO
    0U,	// MFLO16_MM
    0U,	// MFLO64
    0U,	// MFLO_DSP
    0U,	// MFLO_MM
    0U,	// MINA_D
    0U,	// MINA_S
    0U,	// MINI_S_B
    0U,	// MINI_S_D
    0U,	// MINI_S_H
    0U,	// MINI_S_W
    0U,	// MINI_U_B
    0U,	// MINI_U_D
    0U,	// MINI_U_H
    0U,	// MINI_U_W
    0U,	// MIN_A_B
    0U,	// MIN_A_D
    0U,	// MIN_A_H
    0U,	// MIN_A_W
    0U,	// MIN_D
    0U,	// MIN_S
    0U,	// MIN_S_B
    0U,	// MIN_S_D
    0U,	// MIN_S_H
    0U,	// MIN_S_W
    0U,	// MIN_U_B
    0U,	// MIN_U_D
    0U,	// MIN_U_H
    0U,	// MIN_U_W
    0U,	// MIPSeh_return32
    0U,	// MIPSeh_return64
    0U,	// MOD
    0U,	// MODSUB
    0U,	// MODU
    0U,	// MOD_S_B
    0U,	// MOD_S_D
    0U,	// MOD_S_H
    0U,	// MOD_S_W
    0U,	// MOD_U_B
    0U,	// MOD_U_D
    0U,	// MOD_U_H
    0U,	// MOD_U_W
    0U,	// MOVE16_MM
    0U,	// MOVEP_MM
    0U,	// MOVE_V
    0U,	// MOVF_D32
    0U,	// MOVF_D32_MM
    0U,	// MOVF_D64
    0U,	// MOVF_I
    0U,	// MOVF_I64
    0U,	// MOVF_I_MM
    0U,	// MOVF_S
    0U,	// MOVF_S_MM
    0U,	// MOVN_I64_D64
    0U,	// MOVN_I64_I
    0U,	// MOVN_I64_I64
    0U,	// MOVN_I64_S
    0U,	// MOVN_I_D32
    0U,	// MOVN_I_D32_MM
    0U,	// MOVN_I_D64
    0U,	// MOVN_I_I
    0U,	// MOVN_I_I64
    0U,	// MOVN_I_MM
    0U,	// MOVN_I_S
    0U,	// MOVN_I_S_MM
    0U,	// MOVT_D32
    0U,	// MOVT_D32_MM
    0U,	// MOVT_D64
    0U,	// MOVT_I
    0U,	// MOVT_I64
    0U,	// MOVT_I_MM
    0U,	// MOVT_S
    0U,	// MOVT_S_MM
    0U,	// MOVZ_I64_D64
    0U,	// MOVZ_I64_I
    0U,	// MOVZ_I64_I64
    0U,	// MOVZ_I64_S
    0U,	// MOVZ_I_D32
    0U,	// MOVZ_I_D32_MM
    0U,	// MOVZ_I_D64
    0U,	// MOVZ_I_I
    0U,	// MOVZ_I_I64
    0U,	// MOVZ_I_MM
    0U,	// MOVZ_I_S
    0U,	// MOVZ_I_S_MM
    0U,	// MSUB
    2U,	// MSUBF_D
    2U,	// MSUBF_S
    2U,	// MSUBR_Q_H
    2U,	// MSUBR_Q_W
    0U,	// MSUBU
    0U,	// MSUBU_DSP
    0U,	// MSUBU_MM
    2U,	// MSUBV_B
    2U,	// MSUBV_D
    2U,	// MSUBV_H
    2U,	// MSUBV_W
    20U,	// MSUB_D32
    20U,	// MSUB_D32_MM
    20U,	// MSUB_D64
    0U,	// MSUB_DSP
    0U,	// MSUB_MM
    2U,	// MSUB_Q_H
    2U,	// MSUB_Q_W
    20U,	// MSUB_S
    20U,	// MSUB_S_MM
    1U,	// MTC0
    0U,	// MTC1
    0U,	// MTC1_MM
    1U,	// MTC2
    0U,	// MTHC1_D32
    0U,	// MTHC1_D64
    0U,	// MTHC1_MM
    0U,	// MTHI
    0U,	// MTHI64
    0U,	// MTHI_DSP
    0U,	// MTHI_MM
    0U,	// MTHLIP
    0U,	// MTLO
    0U,	// MTLO64
    0U,	// MTLO_DSP
    0U,	// MTLO_MM
    0U,	// MTM0
    0U,	// MTM1
    0U,	// MTM2
    0U,	// MTP0
    0U,	// MTP1
    0U,	// MTP2
    0U,	// MUH
    0U,	// MUHU
    0U,	// MUL
    0U,	// MULEQ_S_W_PHL
    0U,	// MULEQ_S_W_PHR
    0U,	// MULEU_S_PH_QBL
    0U,	// MULEU_S_PH_QBR
    0U,	// MULQ_RS_PH
    0U,	// MULQ_RS_W
    0U,	// MULQ_S_PH
    0U,	// MULQ_S_W
    0U,	// MULR_Q_H
    0U,	// MULR_Q_W
    0U,	// MULSAQ_S_W_PH
    0U,	// MULSA_W_PH
    0U,	// MULT
    0U,	// MULTU_DSP
    0U,	// MULT_DSP
    0U,	// MULT_MM
    0U,	// MULTu
    0U,	// MULTu_MM
    0U,	// MULU
    0U,	// MULV_B
    0U,	// MULV_D
    0U,	// MULV_H
    0U,	// MULV_W
    0U,	// MUL_MM
    0U,	// MUL_PH
    0U,	// MUL_Q_H
    0U,	// MUL_Q_W
    0U,	// MUL_R6
    0U,	// MUL_S_PH
    0U,	// Mfhi16
    0U,	// Mflo16
    0U,	// Move32R16
    0U,	// MoveR3216
    0U,	// MultRxRy16
    0U,	// MultRxRyRz16
    0U,	// MultuRxRy16
    0U,	// MultuRxRyRz16
    0U,	// NLOC_B
    0U,	// NLOC_D
    0U,	// NLOC_H
    0U,	// NLOC_W
    0U,	// NLZC_B
    0U,	// NLZC_D
    0U,	// NLZC_H
    0U,	// NLZC_W
    20U,	// NMADD_D32
    20U,	// NMADD_D32_MM
    20U,	// NMADD_D64
    20U,	// NMADD_S
    20U,	// NMADD_S_MM
    20U,	// NMSUB_D32
    20U,	// NMSUB_D32_MM
    20U,	// NMSUB_D64
    20U,	// NMSUB_S
    20U,	// NMSUB_S_MM
    0U,	// NOP
    0U,	// NOR
    0U,	// NOR64
    0U,	// NORI_B
    0U,	// NOR_MM
    0U,	// NOR_V
    0U,	// NOR_V_D_PSEUDO
    0U,	// NOR_V_H_PSEUDO
    0U,	// NOR_V_W_PSEUDO
    0U,	// NOT16_MM
    0U,	// NegRxRy16
    0U,	// NotRxRy16
    0U,	// OR
    0U,	// OR16_MM
    0U,	// OR64
    0U,	// ORI_B
    0U,	// OR_MM
    0U,	// OR_V
    0U,	// OR_V_D_PSEUDO
    0U,	// OR_V_H_PSEUDO
    0U,	// OR_V_W_PSEUDO
    1U,	// ORi
    1U,	// ORi64
    1U,	// ORi_MM
    0U,	// OrRxRxRy16
    0U,	// PACKRL_PH
    0U,	// PAUSE
    0U,	// PAUSE_MM
    0U,	// PCKEV_B
    0U,	// PCKEV_D
    0U,	// PCKEV_H
    0U,	// PCKEV_W
    0U,	// PCKOD_B
    0U,	// PCKOD_D
    0U,	// PCKOD_H
    0U,	// PCKOD_W
    0U,	// PCNT_B
    0U,	// PCNT_D
    0U,	// PCNT_H
    0U,	// PCNT_W
    0U,	// PICK_PH
    0U,	// PICK_QB
    0U,	// POP
    0U,	// PRECEQU_PH_QBL
    0U,	// PRECEQU_PH_QBLA
    0U,	// PRECEQU_PH_QBR
    0U,	// PRECEQU_PH_QBRA
    0U,	// PRECEQ_W_PHL
    0U,	// PRECEQ_W_PHR
    0U,	// PRECEU_PH_QBL
    0U,	// PRECEU_PH_QBLA
    0U,	// PRECEU_PH_QBR
    0U,	// PRECEU_PH_QBRA
    0U,	// PRECRQU_S_QB_PH
    0U,	// PRECRQ_PH_W
    0U,	// PRECRQ_QB_PH
    0U,	// PRECRQ_RS_PH_W
    0U,	// PRECR_QB_PH
    1U,	// PRECR_SRA_PH_W
    1U,	// PRECR_SRA_R_PH_W
    0U,	// PREF
    0U,	// PREF_MM
    0U,	// PREF_R6
    1U,	// PREPEND
    0U,	// PseudoCMPU_EQ_QB
    0U,	// PseudoCMPU_LE_QB
    0U,	// PseudoCMPU_LT_QB
    0U,	// PseudoCMP_EQ_PH
    0U,	// PseudoCMP_LE_PH
    0U,	// PseudoCMP_LT_PH
    0U,	// PseudoCVT_D32_W
    0U,	// PseudoCVT_D64_L
    0U,	// PseudoCVT_D64_W
    0U,	// PseudoCVT_S_L
    0U,	// PseudoCVT_S_W
    0U,	// PseudoDMULT
    0U,	// PseudoDMULTu
    0U,	// PseudoDSDIV
    0U,	// PseudoDUDIV
    0U,	// PseudoIndirectBranch
    0U,	// PseudoIndirectBranch64
    0U,	// PseudoMADD
    0U,	// PseudoMADDU
    0U,	// PseudoMFHI
    0U,	// PseudoMFHI64
    0U,	// PseudoMFLO
    0U,	// PseudoMFLO64
    0U,	// PseudoMSUB
    0U,	// PseudoMSUBU
    0U,	// PseudoMTLOHI
    0U,	// PseudoMTLOHI64
    0U,	// PseudoMTLOHI_DSP
    0U,	// PseudoMULT
    0U,	// PseudoMULTu
    0U,	// PseudoPICK_PH
    0U,	// PseudoPICK_QB
    0U,	// PseudoReturn
    0U,	// PseudoReturn64
    0U,	// PseudoSDIV
    0U,	// PseudoSELECTFP_F_D32
    0U,	// PseudoSELECTFP_F_D64
    0U,	// PseudoSELECTFP_F_I
    0U,	// PseudoSELECTFP_F_I64
    0U,	// PseudoSELECTFP_F_S
    0U,	// PseudoSELECTFP_T_D32
    0U,	// PseudoSELECTFP_T_D64
    0U,	// PseudoSELECTFP_T_I
    0U,	// PseudoSELECTFP_T_I64
    0U,	// PseudoSELECTFP_T_S
    0U,	// PseudoSELECT_D32
    0U,	// PseudoSELECT_D64
    0U,	// PseudoSELECT_I
    0U,	// PseudoSELECT_I64
    0U,	// PseudoSELECT_S
    0U,	// PseudoUDIV
    0U,	// RADDU_W_QB
    0U,	// RDDSP
    0U,	// RDHWR
    0U,	// RDHWR64
    0U,	// RDHWR_MM
    0U,	// REPLV_PH
    0U,	// REPLV_QB
    0U,	// REPL_PH
    0U,	// REPL_QB
    0U,	// RINT_D
    0U,	// RINT_S
    1U,	// ROTR
    0U,	// ROTRV
    0U,	// ROTRV_MM
    1U,	// ROTR_MM
    0U,	// ROUND_L_D64
    0U,	// ROUND_L_S
    0U,	// ROUND_W_D32
    0U,	// ROUND_W_D64
    0U,	// ROUND_W_MM
    0U,	// ROUND_W_S
    0U,	// ROUND_W_S_MM
    0U,	// Restore16
    0U,	// RestoreX16
    0U,	// RetRA
    0U,	// RetRA16
    1U,	// SAT_S_B
    1U,	// SAT_S_D
    0U,	// SAT_S_H
    1U,	// SAT_S_W
    1U,	// SAT_U_B
    1U,	// SAT_U_D
    0U,	// SAT_U_H
    1U,	// SAT_U_W
    0U,	// SB
    0U,	// SB16_MM
    0U,	// SB64
    0U,	// SB_MM
    0U,	// SC
    0U,	// SCD
    0U,	// SCD_R6
    0U,	// SC_MM
    0U,	// SC_R6
    0U,	// SD
    0U,	// SDBBP
    0U,	// SDBBP16_MM
    0U,	// SDBBP_MM
    0U,	// SDBBP_R6
    0U,	// SDC1
    0U,	// SDC164
    0U,	// SDC1_MM
    0U,	// SDC2
    0U,	// SDC2_R6
    0U,	// SDC3
    0U,	// SDIV
    0U,	// SDIV_MM
    0U,	// SDL
    0U,	// SDR
    0U,	// SDXC1
    0U,	// SDXC164
    0U,	// SEB
    0U,	// SEB64
    0U,	// SEB_MM
    0U,	// SEH
    0U,	// SEH64
    0U,	// SEH_MM
    0U,	// SELEQZ
    0U,	// SELEQZ64
    0U,	// SELEQZ_D
    0U,	// SELEQZ_S
    0U,	// SELNEZ
    0U,	// SELNEZ64
    0U,	// SELNEZ_D
    0U,	// SELNEZ_S
    2U,	// SEL_D
    2U,	// SEL_S
    0U,	// SEQ
    0U,	// SEQi
    0U,	// SH
    0U,	// SH16_MM
    0U,	// SH64
    0U,	// SHF_B
    0U,	// SHF_H
    0U,	// SHF_W
    0U,	// SHILO
    0U,	// SHILOV
    0U,	// SHLLV_PH
    0U,	// SHLLV_QB
    0U,	// SHLLV_S_PH
    0U,	// SHLLV_S_W
    1U,	// SHLL_PH
    1U,	// SHLL_QB
    1U,	// SHLL_S_PH
    1U,	// SHLL_S_W
    0U,	// SHRAV_PH
    0U,	// SHRAV_QB
    0U,	// SHRAV_R_PH
    0U,	// SHRAV_R_QB
    0U,	// SHRAV_R_W
    1U,	// SHRA_PH
    1U,	// SHRA_QB
    1U,	// SHRA_R_PH
    1U,	// SHRA_R_QB
    1U,	// SHRA_R_W
    0U,	// SHRLV_PH
    0U,	// SHRLV_QB
    1U,	// SHRL_PH
    1U,	// SHRL_QB
    0U,	// SH_MM
    9U,	// SLDI_B
    9U,	// SLDI_D
    9U,	// SLDI_H
    9U,	// SLDI_W
    10U,	// SLD_B
    10U,	// SLD_D
    10U,	// SLD_H
    10U,	// SLD_W
    1U,	// SLL
    0U,	// SLL16_MM
    0U,	// SLL64_32
    0U,	// SLL64_64
    0U,	// SLLI_B
    0U,	// SLLI_D
    0U,	// SLLI_H
    0U,	// SLLI_W
    0U,	// SLLV
    0U,	// SLLV_MM
    0U,	// SLL_B
    0U,	// SLL_D
    0U,	// SLL_H
    1U,	// SLL_MM
    0U,	// SLL_W
    0U,	// SLT
    0U,	// SLT64
    0U,	// SLT_MM
    0U,	// SLTi
    0U,	// SLTi64
    0U,	// SLTi_MM
    0U,	// SLTiu
    0U,	// SLTiu64
    0U,	// SLTiu_MM
    0U,	// SLTu
    0U,	// SLTu64
    0U,	// SLTu_MM
    0U,	// SNE
    0U,	// SNEi
    0U,	// SNZ_B_PSEUDO
    0U,	// SNZ_D_PSEUDO
    0U,	// SNZ_H_PSEUDO
    0U,	// SNZ_V_PSEUDO
    0U,	// SNZ_W_PSEUDO
    8U,	// SPLATI_B
    8U,	// SPLATI_D
    8U,	// SPLATI_H
    8U,	// SPLATI_W
    8U,	// SPLAT_B
    8U,	// SPLAT_D
    8U,	// SPLAT_H
    8U,	// SPLAT_W
    1U,	// SRA
    0U,	// SRAI_B
    0U,	// SRAI_D
    0U,	// SRAI_H
    0U,	// SRAI_W
    1U,	// SRARI_B
    1U,	// SRARI_D
    0U,	// SRARI_H
    1U,	// SRARI_W
    0U,	// SRAR_B
    0U,	// SRAR_D
    0U,	// SRAR_H
    0U,	// SRAR_W
    0U,	// SRAV
    0U,	// SRAV_MM
    0U,	// SRA_B
    0U,	// SRA_D
    0U,	// SRA_H
    1U,	// SRA_MM
    0U,	// SRA_W
    1U,	// SRL
    0U,	// SRL16_MM
    0U,	// SRLI_B
    0U,	// SRLI_D
    0U,	// SRLI_H
    0U,	// SRLI_W
    1U,	// SRLRI_B
    1U,	// SRLRI_D
    0U,	// SRLRI_H
    1U,	// SRLRI_W
    0U,	// SRLR_B
    0U,	// SRLR_D
    0U,	// SRLR_H
    0U,	// SRLR_W
    0U,	// SRLV
    0U,	// SRLV_MM
    0U,	// SRL_B
    0U,	// SRL_D
    0U,	// SRL_H
    1U,	// SRL_MM
    0U,	// SRL_W
    0U,	// SSNOP
    0U,	// SSNOP_MM
    0U,	// STORE_ACC128
    0U,	// STORE_ACC64
    0U,	// STORE_ACC64DSP
    0U,	// STORE_CCOND_DSP
    0U,	// ST_B
    0U,	// ST_D
    0U,	// ST_H
    0U,	// ST_W
    0U,	// SUB
    0U,	// SUBQH_PH
    0U,	// SUBQH_R_PH
    0U,	// SUBQH_R_W
    0U,	// SUBQH_W
    0U,	// SUBQ_PH
    0U,	// SUBQ_S_PH
    0U,	// SUBQ_S_W
    0U,	// SUBSUS_U_B
    0U,	// SUBSUS_U_D
    0U,	// SUBSUS_U_H
    0U,	// SUBSUS_U_W
    0U,	// SUBSUU_S_B
    0U,	// SUBSUU_S_D
    0U,	// SUBSUU_S_H
    0U,	// SUBSUU_S_W
    0U,	// SUBS_S_B
    0U,	// SUBS_S_D
    0U,	// SUBS_S_H
    0U,	// SUBS_S_W
    0U,	// SUBS_U_B
    0U,	// SUBS_U_D
    0U,	// SUBS_U_H
    0U,	// SUBS_U_W
    0U,	// SUBU16_MM
    0U,	// SUBUH_QB
    0U,	// SUBUH_R_QB
    0U,	// SUBU_PH
    0U,	// SUBU_QB
    0U,	// SUBU_S_PH
    0U,	// SUBU_S_QB
    0U,	// SUBVI_B
    0U,	// SUBVI_D
    0U,	// SUBVI_H
    0U,	// SUBVI_W
    0U,	// SUBV_B
    0U,	// SUBV_D
    0U,	// SUBV_H
    0U,	// SUBV_W
    0U,	// SUB_MM
    0U,	// SUBu
    0U,	// SUBu_MM
    0U,	// SUXC1
    0U,	// SUXC164
    0U,	// SUXC1_MM
    0U,	// SW
    0U,	// SW16_MM
    0U,	// SW64
    0U,	// SWC1
    0U,	// SWC1_MM
    0U,	// SWC2
    0U,	// SWC2_R6
    0U,	// SWC3
    0U,	// SWL
    0U,	// SWL64
    0U,	// SWL_MM
    0U,	// SWM16_MM
    0U,	// SWM32_MM
    0U,	// SWM_MM
    0U,	// SWP_MM
    0U,	// SWR
    0U,	// SWR64
    0U,	// SWR_MM
    0U,	// SWSP_MM
    0U,	// SWXC1
    0U,	// SWXC1_MM
    0U,	// SW_MM
    0U,	// SYNC
    0U,	// SYNCI
    0U,	// SYNC_MM
    0U,	// SYSCALL
    0U,	// SYSCALL_MM
    0U,	// SZ_B_PSEUDO
    0U,	// SZ_D_PSEUDO
    0U,	// SZ_H_PSEUDO
    0U,	// SZ_V_PSEUDO
    0U,	// SZ_W_PSEUDO
    0U,	// Save16
    0U,	// SaveX16
    0U,	// SbRxRyOffMemX16
    0U,	// SebRx16
    0U,	// SehRx16
    0U,	// SelBeqZ
    0U,	// SelBneZ
    0U,	// SelTBteqZCmp
    0U,	// SelTBteqZCmpi
    0U,	// SelTBteqZSlt
    0U,	// SelTBteqZSlti
    0U,	// SelTBteqZSltiu
    0U,	// SelTBteqZSltu
    0U,	// SelTBtneZCmp
    0U,	// SelTBtneZCmpi
    0U,	// SelTBtneZSlt
    0U,	// SelTBtneZSlti
    0U,	// SelTBtneZSltiu
    0U,	// SelTBtneZSltu
    0U,	// ShRxRyOffMemX16
    1U,	// SllX16
    0U,	// SllvRxRy16
    0U,	// SltCCRxRy16
    0U,	// SltRxRy16
    0U,	// SltiCCRxImmX16
    0U,	// SltiRxImm16
    0U,	// SltiRxImmX16
    0U,	// SltiuCCRxImmX16
    0U,	// SltiuRxImm16
    0U,	// SltiuRxImmX16
    0U,	// SltuCCRxRy16
    0U,	// SltuRxRy16
    0U,	// SltuRxRyRz16
    1U,	// SraX16
    0U,	// SravRxRy16
    1U,	// SrlX16
    0U,	// SrlvRxRy16
    0U,	// SubuRxRyRz16
    0U,	// SwRxRyOffMemX16
    0U,	// SwRxSpImmX16
    0U,	// TAILCALL
    0U,	// TAILCALL64_R
    0U,	// TAILCALL_R
    1U,	// TEQ
    0U,	// TEQI
    0U,	// TEQI_MM
    1U,	// TEQ_MM
    1U,	// TGE
    0U,	// TGEI
    0U,	// TGEIU
    0U,	// TGEIU_MM
    0U,	// TGEI_MM
    1U,	// TGEU
    1U,	// TGEU_MM
    1U,	// TGE_MM
    0U,	// TLBP
    0U,	// TLBP_MM
    0U,	// TLBR
    0U,	// TLBR_MM
    0U,	// TLBWI
    0U,	// TLBWI_MM
    0U,	// TLBWR
    0U,	// TLBWR_MM
    1U,	// TLT
    0U,	// TLTI
    0U,	// TLTIU_MM
    0U,	// TLTI_MM
    1U,	// TLTU
    1U,	// TLTU_MM
    1U,	// TLT_MM
    1U,	// TNE
    0U,	// TNEI
    0U,	// TNEI_MM
    1U,	// TNE_MM
    0U,	// TRAP
    0U,	// TRUNC_L_D64
    0U,	// TRUNC_L_S
    0U,	// TRUNC_W_D32
    0U,	// TRUNC_W_D64
    0U,	// TRUNC_W_MM
    0U,	// TRUNC_W_S
    0U,	// TRUNC_W_S_MM
    0U,	// TTLTIU
    0U,	// UDIV
    0U,	// UDIV_MM
    0U,	// V3MULU
    0U,	// VMM0
    0U,	// VMULU
    2U,	// VSHF_B
    2U,	// VSHF_D
    2U,	// VSHF_H
    2U,	// VSHF_W
    0U,	// WAIT
    0U,	// WAIT_MM
    0U,	// WRDSP
    0U,	// WSBH
    0U,	// WSBH_MM
    0U,	// XOR
    0U,	// XOR16_MM
    0U,	// XOR64
    0U,	// XORI_B
    0U,	// XOR_MM
    0U,	// XOR_V
    0U,	// XOR_V_D_PSEUDO
    0U,	// XOR_V_H_PSEUDO
    0U,	// XOR_V_W_PSEUDO
    1U,	// XORi
    1U,	// XORi64
    1U,	// XORi_MM
    0U,	// XorRxRxRy16
    0U
  };

#ifndef CAPSTONE_DIET
  static const char AsmStrs[] = {
  /* 0 */ 'j', 'a', 'l', 'r', 'c', 32, 9, 0,
  /* 8 */ 'd', 'm', 'f', 'c', '0', 9, 0,
  /* 15 */ 'd', 'm', 't', 'c', '0', 9, 0,
  /* 22 */ 'v', 'm', 'm', '0', 9, 0,
  /* 28 */ 'm', 't', 'm', '0', 9, 0,
  /* 34 */ 'm', 't', 'p', '0', 9, 0,
  /* 40 */ 'b', 'b', 'i', 't', '0', 9, 0,
  /* 47 */ 'l', 'd', 'c', '1', 9, 0,
  /* 53 */ 's', 'd', 'c', '1', 9, 0,
  /* 59 */ 'c', 'f', 'c', '1', 9, 0,
  /* 65 */ 'd', 'm', 'f', 'c', '1', 9, 0,
  /* 72 */ 'm', 'f', 'h', 'c', '1', 9, 0,
  /* 79 */ 'm', 't', 'h', 'c', '1', 9, 0,
  /* 86 */ 'c', 't', 'c', '1', 9, 0,
  /* 92 */ 'd', 'm', 't', 'c', '1', 9, 0,
  /* 99 */ 'l', 'w', 'c', '1', 9, 0,
  /* 105 */ 's', 'w', 'c', '1', 9, 0,
  /* 111 */ 'l', 'd', 'x', 'c', '1', 9, 0,
  /* 118 */ 's', 'd', 'x', 'c', '1', 9, 0,
  /* 125 */ 'l', 'u', 'x', 'c', '1', 9, 0,
  /* 132 */ 's', 'u', 'x', 'c', '1', 9, 0,
  /* 139 */ 'l', 'w', 'x', 'c', '1', 9, 0,
  /* 146 */ 's', 'w', 'x', 'c', '1', 9, 0,
  /* 153 */ 'm', 't', 'm', '1', 9, 0,
  /* 159 */ 'm', 't', 'p', '1', 9, 0,
  /* 165 */ 'b', 'b', 'i', 't', '1', 9, 0,
  /* 172 */ 'b', 'b', 'i', 't', '0', '3', '2', 9, 0,
  /* 181 */ 'b', 'b', 'i', 't', '1', '3', '2', 9, 0,
  /* 190 */ 'd', 's', 'r', 'a', '3', '2', 9, 0,
  /* 198 */ 'b', 'p', 'o', 's', 'g', 'e', '3', '2', 9, 0,
  /* 208 */ 'd', 's', 'l', 'l', '3', '2', 9, 0,
  /* 216 */ 'd', 's', 'r', 'l', '3', '2', 9, 0,
  /* 224 */ 'l', 'w', 'm', '3', '2', 9, 0,
  /* 231 */ 's', 'w', 'm', '3', '2', 9, 0,
  /* 238 */ 'd', 'r', 'o', 't', 'r', '3', '2', 9, 0,
  /* 247 */ 'l', 'd', 'c', '2', 9, 0,
  /* 253 */ 's', 'd', 'c', '2', 9, 0,
  /* 259 */ 'd', 'm', 'f', 'c', '2', 9, 0,
  /* 266 */ 'd', 'm', 't', 'c', '2', 9, 0,
  /* 273 */ 'l', 'w', 'c', '2', 9, 0,
  /* 279 */ 's', 'w', 'c', '2', 9, 0,
  /* 285 */ 'm', 't', 'm', '2', 9, 0,
  /* 291 */ 'm', 't', 'p', '2', 9, 0,
  /* 297 */ 'a', 'd', 'd', 'i', 'u', 'r', '2', 9, 0,
  /* 306 */ 'l', 'd', 'c', '3', 9, 0,
  /* 312 */ 's', 'd', 'c', '3', 9, 0,
  /* 318 */ 'l', 'w', 'c', '3', 9, 0,
  /* 324 */ 's', 'w', 'c', '3', 9, 0,
  /* 330 */ 'a', 'd', 'd', 'i', 'u', 's', '5', 9, 0,
  /* 339 */ 's', 'b', '1', '6', 9, 0,
  /* 345 */ 'a', 'n', 'd', '1', '6', 9, 0,
  /* 352 */ 's', 'h', '1', '6', 9, 0,
  /* 358 */ 'a', 'n', 'd', 'i', '1', '6', 9, 0,
  /* 366 */ 'l', 'i', '1', '6', 9, 0,
  /* 372 */ 'b', 'r', 'e', 'a', 'k', '1', '6', 9, 0,
  /* 381 */ 's', 'l', 'l', '1', '6', 9, 0,
  /* 388 */ 's', 'r', 'l', '1', '6', 9, 0,
  /* 395 */ 'l', 'w', 'm', '1', '6', 9, 0,
  /* 402 */ 's', 'w', 'm', '1', '6', 9, 0,
  /* 409 */ 's', 'd', 'b', 'b', 'p', '1', '6', 9, 0,
  /* 418 */ 'j', 'r', '1', '6', 9, 0,
  /* 424 */ 'x', 'o', 'r', '1', '6', 9, 0,
  /* 431 */ 'j', 'a', 'l', 'r', 's', '1', '6', 9, 0,
  /* 440 */ 'n', 'o', 't', '1', '6', 9, 0,
  /* 447 */ 'l', 'b', 'u', '1', '6', 9, 0,
  /* 454 */ 's', 'u', 'b', 'u', '1', '6', 9, 0,
  /* 462 */ 'a', 'd', 'd', 'u', '1', '6', 9, 0,
  /* 470 */ 'l', 'h', 'u', '1', '6', 9, 0,
  /* 477 */ 'l', 'w', '1', '6', 9, 0,
  /* 483 */ 's', 'w', '1', '6', 9, 0,
  /* 489 */ 'b', 'n', 'e', 'z', '1', '6', 9, 0,
  /* 497 */ 'b', 'e', 'q', 'z', '1', '6', 9, 0,
  /* 505 */ 'p', 'r', 'e', 'c', 'e', 'u', '.', 'p', 'h', '.', 'q', 'b', 'l', 'a', 9, 0,
  /* 521 */ 'p', 'r', 'e', 'c', 'e', 'q', 'u', '.', 'p', 'h', '.', 'q', 'b', 'l', 'a', 9, 0,
  /* 538 */ 'p', 'r', 'e', 'c', 'e', 'u', '.', 'p', 'h', '.', 'q', 'b', 'r', 'a', 9, 0,
  /* 554 */ 'p', 'r', 'e', 'c', 'e', 'q', 'u', '.', 'p', 'h', '.', 'q', 'b', 'r', 'a', 9, 0,
  /* 571 */ 'd', 's', 'r', 'a', 9, 0,
  /* 577 */ 'd', 'l', 's', 'a', 9, 0,
  /* 583 */ 'c', 'f', 'c', 'm', 's', 'a', 9, 0,
  /* 591 */ 'c', 't', 'c', 'm', 's', 'a', 9, 0,
  /* 599 */ 'a', 'd', 'd', '_', 'a', '.', 'b', 9, 0,
  /* 608 */ 'm', 'i', 'n', '_', 'a', '.', 'b', 9, 0,
  /* 617 */ 'a', 'd', 'd', 's', '_', 'a', '.', 'b', 9, 0,
  /* 627 */ 'm', 'a', 'x', '_', 'a', '.', 'b', 9, 0,
  /* 636 */ 's', 'r', 'a', '.', 'b', 9, 0,
  /* 643 */ 'n', 'l', 'o', 'c', '.', 'b', 9, 0,
  /* 651 */ 'n', 'l', 'z', 'c', '.', 'b', 9, 0,
  /* 659 */ 's', 'l', 'd', '.', 'b', 9, 0,
  /* 666 */ 'p', 'c', 'k', 'o', 'd', '.', 'b', 9, 0,
  /* 675 */ 'i', 'l', 'v', 'o', 'd', '.', 'b', 9, 0,
  /* 684 */ 'i', 'n', 's', 'v', 'e', '.', 'b', 9, 0,
  /* 693 */ 'v', 's', 'h', 'f', '.', 'b', 9, 0,
  /* 701 */ 'b', 'n', 'e', 'g', '.', 'b', 9, 0,
  /* 709 */ 's', 'r', 'a', 'i', '.', 'b', 9, 0,
  /* 717 */ 's', 'l', 'd', 'i', '.', 'b', 9, 0,
  /* 725 */ 'a', 'n', 'd', 'i', '.', 'b', 9, 0,
  /* 733 */ 'b', 'n', 'e', 'g', 'i', '.', 'b', 9, 0,
  /* 742 */ 'b', 's', 'e', 'l', 'i', '.', 'b', 9, 0,
  /* 751 */ 's', 'l', 'l', 'i', '.', 'b', 9, 0,
  /* 759 */ 's', 'r', 'l', 'i', '.', 'b', 9, 0,
  /* 767 */ 'b', 'i', 'n', 's', 'l', 'i', '.', 'b', 9, 0,
  /* 777 */ 'c', 'e', 'q', 'i', '.', 'b', 9, 0,
  /* 785 */ 's', 'r', 'a', 'r', 'i', '.', 'b', 9, 0,
  /* 794 */ 'b', 'c', 'l', 'r', 'i', '.', 'b', 9, 0,
  /* 803 */ 's', 'r', 'l', 'r', 'i', '.', 'b', 9, 0,
  /* 812 */ 'n', 'o', 'r', 'i', '.', 'b', 9, 0,
  /* 820 */ 'x', 'o', 'r', 'i', '.', 'b', 9, 0,
  /* 828 */ 'b', 'i', 'n', 's', 'r', 'i', '.', 'b', 9, 0,
  /* 838 */ 's', 'p', 'l', 'a', 't', 'i', '.', 'b', 9, 0,
  /* 848 */ 'b', 's', 'e', 't', 'i', '.', 'b', 9, 0,
  /* 857 */ 's', 'u', 'b', 'v', 'i', '.', 'b', 9, 0,
  /* 866 */ 'a', 'd', 'd', 'v', 'i', '.', 'b', 9, 0,
  /* 875 */ 'b', 'm', 'z', 'i', '.', 'b', 9, 0,
  /* 883 */ 'b', 'm', 'n', 'z', 'i', '.', 'b', 9, 0,
  /* 892 */ 'f', 'i', 'l', 'l', '.', 'b', 9, 0,
  /* 900 */ 's', 'l', 'l', '.', 'b', 9, 0,
  /* 907 */ 's', 'r', 'l', '.', 'b', 9, 0,
  /* 914 */ 'b', 'i', 'n', 's', 'l', '.', 'b', 9, 0,
  /* 923 */ 'i', 'l', 'v', 'l', '.', 'b', 9, 0,
  /* 931 */ 'c', 'e', 'q', '.', 'b', 9, 0,
  /* 938 */ 's', 'r', 'a', 'r', '.', 'b', 9, 0,
  /* 946 */ 'b', 'c', 'l', 'r', '.', 'b', 9, 0,
  /* 954 */ 's', 'r', 'l', 'r', '.', 'b', 9, 0,
  /* 962 */ 'b', 'i', 'n', 's', 'r', '.', 'b', 9, 0,
  /* 971 */ 'i', 'l', 'v', 'r', '.', 'b', 9, 0,
  /* 979 */ 'a', 's', 'u', 'b', '_', 's', '.', 'b', 9, 0,
  /* 989 */ 'm', 'o', 'd', '_', 's', '.', 'b', 9, 0,
  /* 998 */ 'c', 'l', 'e', '_', 's', '.', 'b', 9, 0,
  /* 1007 */ 'a', 'v', 'e', '_', 's', '.', 'b', 9, 0,
  /* 1016 */ 'c', 'l', 'e', 'i', '_', 's', '.', 'b', 9, 0,
  /* 1026 */ 'm', 'i', 'n', 'i', '_', 's', '.', 'b', 9, 0,
  /* 1036 */ 'c', 'l', 't', 'i', '_', 's', '.', 'b', 9, 0,
  /* 1046 */ 'm', 'a', 'x', 'i', '_', 's', '.', 'b', 9, 0,
  /* 1056 */ 'm', 'i', 'n', '_', 's', '.', 'b', 9, 0,
  /* 1065 */ 'a', 'v', 'e', 'r', '_', 's', '.', 'b', 9, 0,
  /* 1075 */ 's', 'u', 'b', 's', '_', 's', '.', 'b', 9, 0,
  /* 1085 */ 'a', 'd', 'd', 's', '_', 's', '.', 'b', 9, 0,
  /* 1095 */ 's', 'a', 't', '_', 's', '.', 'b', 9, 0,
  /* 1104 */ 'c', 'l', 't', '_', 's', '.', 'b', 9, 0,
  /* 1113 */ 's', 'u', 'b', 's', 'u', 'u', '_', 's', '.', 'b', 9, 0,
  /* 1125 */ 'd', 'i', 'v', '_', 's', '.', 'b', 9, 0,
  /* 1134 */ 'm', 'a', 'x', '_', 's', '.', 'b', 9, 0,
  /* 1143 */ 'c', 'o', 'p', 'y', '_', 's', '.', 'b', 9, 0,
  /* 1153 */ 's', 'p', 'l', 'a', 't', '.', 'b', 9, 0,
  /* 1162 */ 'b', 's', 'e', 't', '.', 'b', 9, 0,
  /* 1170 */ 'p', 'c', 'n', 't', '.', 'b', 9, 0,
  /* 1178 */ 'i', 'n', 's', 'e', 'r', 't', '.', 'b', 9, 0,
  /* 1188 */ 's', 't', '.', 'b', 9, 0,
  /* 1194 */ 'a', 's', 'u', 'b', '_', 'u', '.', 'b', 9, 0,
  /* 1204 */ 'm', 'o', 'd', '_', 'u', '.', 'b', 9, 0,
  /* 1213 */ 'c', 'l', 'e', '_', 'u', '.', 'b', 9, 0,
  /* 1222 */ 'a', 'v', 'e', '_', 'u', '.', 'b', 9, 0,
  /* 1231 */ 'c', 'l', 'e', 'i', '_', 'u', '.', 'b', 9, 0,
  /* 1241 */ 'm', 'i', 'n', 'i', '_', 'u', '.', 'b', 9, 0,
  /* 1251 */ 'c', 'l', 't', 'i', '_', 'u', '.', 'b', 9, 0,
  /* 1261 */ 'm', 'a', 'x', 'i', '_', 'u', '.', 'b', 9, 0,
  /* 1271 */ 'm', 'i', 'n', '_', 'u', '.', 'b', 9, 0,
  /* 1280 */ 'a', 'v', 'e', 'r', '_', 'u', '.', 'b', 9, 0,
  /* 1290 */ 's', 'u', 'b', 's', '_', 'u', '.', 'b', 9, 0,
  /* 1300 */ 'a', 'd', 'd', 's', '_', 'u', '.', 'b', 9, 0,
  /* 1310 */ 's', 'u', 'b', 's', 'u', 's', '_', 'u', '.', 'b', 9, 0,
  /* 1322 */ 's', 'a', 't', '_', 'u', '.', 'b', 9, 0,
  /* 1331 */ 'c', 'l', 't', '_', 'u', '.', 'b', 9, 0,
  /* 1340 */ 'd', 'i', 'v', '_', 'u', '.', 'b', 9, 0,
  /* 1349 */ 'm', 'a', 'x', '_', 'u', '.', 'b', 9, 0,
  /* 1358 */ 'c', 'o', 'p', 'y', '_', 'u', '.', 'b', 9, 0,
  /* 1368 */ 'm', 's', 'u', 'b', 'v', '.', 'b', 9, 0,
  /* 1377 */ 'm', 'a', 'd', 'd', 'v', '.', 'b', 9, 0,
  /* 1386 */ 'p', 'c', 'k', 'e', 'v', '.', 'b', 9, 0,
  /* 1395 */ 'i', 'l', 'v', 'e', 'v', '.', 'b', 9, 0,
  /* 1404 */ 'm', 'u', 'l', 'v', '.', 'b', 9, 0,
  /* 1412 */ 'b', 'z', '.', 'b', 9, 0,
  /* 1418 */ 'b', 'n', 'z', '.', 'b', 9, 0,
  /* 1425 */ 's', 'e', 'b', 9, 0,
  /* 1430 */ 'j', 'r', '.', 'h', 'b', 9, 0,
  /* 1437 */ 'j', 'a', 'l', 'r', '.', 'h', 'b', 9, 0,
  /* 1446 */ 'l', 'b', 9, 0,
  /* 1450 */ 's', 'h', 'r', 'a', '.', 'q', 'b', 9, 0,
  /* 1459 */ 'c', 'm', 'p', 'g', 'd', 'u', '.', 'l', 'e', '.', 'q', 'b', 9, 0,
  /* 1473 */ 'c', 'm', 'p', 'g', 'u', '.', 'l', 'e', '.', 'q', 'b', 9, 0,
  /* 1486 */ 'c', 'm', 'p', 'u', '.', 'l', 'e', '.', 'q', 'b', 9, 0,
  /* 1498 */ 's', 'u', 'b', 'u', 'h', '.', 'q', 'b', 9, 0,
  /* 1508 */ 'a', 'd', 'd', 'u', 'h', '.', 'q', 'b', 9, 0,
  /* 1518 */ 'p', 'i', 'c', 'k', '.', 'q', 'b', 9, 0,
  /* 1527 */ 's', 'h', 'l', 'l', '.', 'q', 'b', 9, 0,
  /* 1536 */ 'r', 'e', 'p', 'l', '.', 'q', 'b', 9, 0,
  /* 1545 */ 's', 'h', 'r', 'l', '.', 'q', 'b', 9, 0,
  /* 1554 */ 'c', 'm', 'p', 'g', 'd', 'u', '.', 'e', 'q', '.', 'q', 'b', 9, 0,
  /* 1568 */ 'c', 'm', 'p', 'g', 'u', '.', 'e', 'q', '.', 'q', 'b', 9, 0,
  /* 1581 */ 'c', 'm', 'p', 'u', '.', 'e', 'q', '.', 'q', 'b', 9, 0,
  /* 1593 */ 's', 'h', 'r', 'a', '_', 'r', '.', 'q', 'b', 9, 0,
  /* 1604 */ 's', 'u', 'b', 'u', 'h', '_', 'r', '.', 'q', 'b', 9, 0,
  /* 1616 */ 'a', 'd', 'd', 'u', 'h', '_', 'r', '.', 'q', 'b', 9, 0,
  /* 1628 */ 's', 'h', 'r', 'a', 'v', '_', 'r', '.', 'q', 'b', 9, 0,
  /* 1640 */ 'a', 'b', 's', 'q', '_', 's', '.', 'q', 'b', 9, 0,
  /* 1651 */ 's', 'u', 'b', 'u', '_', 's', '.', 'q', 'b', 9, 0,
  /* 1662 */ 'a', 'd', 'd', 'u', '_', 's', '.', 'q', 'b', 9, 0,
  /* 1673 */ 'c', 'm', 'p', 'g', 'd', 'u', '.', 'l', 't', '.', 'q', 'b', 9, 0,
  /* 1687 */ 'c', 'm', 'p', 'g', 'u', '.', 'l', 't', '.', 'q', 'b', 9, 0,
  /* 1700 */ 'c', 'm', 'p', 'u', '.', 'l', 't', '.', 'q', 'b', 9, 0,
  /* 1712 */ 's', 'u', 'b', 'u', '.', 'q', 'b', 9, 0,
  /* 1721 */ 'a', 'd', 'd', 'u', '.', 'q', 'b', 9, 0,
  /* 1730 */ 's', 'h', 'r', 'a', 'v', '.', 'q', 'b', 9, 0,
  /* 1740 */ 's', 'h', 'l', 'l', 'v', '.', 'q', 'b', 9, 0,
  /* 1750 */ 'r', 'e', 'p', 'l', 'v', '.', 'q', 'b', 9, 0,
  /* 1760 */ 's', 'h', 'r', 'l', 'v', '.', 'q', 'b', 9, 0,
  /* 1770 */ 'r', 'a', 'd', 'd', 'u', '.', 'w', '.', 'q', 'b', 9, 0,
  /* 1782 */ 's', 'b', 9, 0,
  /* 1786 */ 'm', 'o', 'd', 's', 'u', 'b', 9, 0,
  /* 1794 */ 'm', 's', 'u', 'b', 9, 0,
  /* 1800 */ 'b', 'c', 9, 0,
  /* 1804 */ 'b', 'g', 'e', 'c', 9, 0,
  /* 1810 */ 'b', 'n', 'e', 'c', 9, 0,
  /* 1816 */ 'j', 'i', 'c', 9, 0,
  /* 1821 */ 'b', 'a', 'l', 'c', 9, 0,
  /* 1827 */ 'j', 'i', 'a', 'l', 'c', 9, 0,
  /* 1834 */ 'b', 'g', 'e', 'z', 'a', 'l', 'c', 9, 0,
  /* 1843 */ 'b', 'l', 'e', 'z', 'a', 'l', 'c', 9, 0,
  /* 1852 */ 'b', 'n', 'e', 'z', 'a', 'l', 'c', 9, 0,
  /* 1861 */ 'b', 'e', 'q', 'z', 'a', 'l', 'c', 9, 0,
  /* 1870 */ 'b', 'g', 't', 'z', 'a', 'l', 'c', 9, 0,
  /* 1879 */ 'b', 'l', 't', 'z', 'a', 'l', 'c', 9, 0,
  /* 1888 */ 'l', 'd', 'p', 'c', 9, 0,
  /* 1894 */ 'a', 'u', 'i', 'p', 'c', 9, 0,
  /* 1901 */ 'a', 'l', 'u', 'i', 'p', 'c', 9, 0,
  /* 1909 */ 'a', 'd', 'd', 'i', 'u', 'p', 'c', 9, 0,
  /* 1918 */ 'l', 'w', 'u', 'p', 'c', 9, 0,
  /* 1925 */ 'l', 'w', 'p', 'c', 9, 0,
  /* 1931 */ 'b', 'e', 'q', 'c', 9, 0,
  /* 1937 */ 'j', 'r', 'c', 9, 0,
  /* 1942 */ 'a', 'd', 'd', 's', 'c', 9, 0,
  /* 1949 */ 'b', 'l', 't', 'c', 9, 0,
  /* 1955 */ 'b', 'g', 'e', 'u', 'c', 9, 0,
  /* 1962 */ 'b', 'l', 't', 'u', 'c', 9, 0,
  /* 1969 */ 'b', 'n', 'v', 'c', 9, 0,
  /* 1975 */ 'b', 'o', 'v', 'c', 9, 0,
  /* 1981 */ 'a', 'd', 'd', 'w', 'c', 9, 0,
  /* 1988 */ 'b', 'g', 'e', 'z', 'c', 9, 0,
  /* 1995 */ 'b', 'l', 'e', 'z', 'c', 9, 0,
  /* 2002 */ 'b', 'n', 'e', 'z', 'c', 9, 0,
  /* 2009 */ 'b', 'e', 'q', 'z', 'c', 9, 0,
  /* 2016 */ 'b', 'g', 't', 'z', 'c', 9, 0,
  /* 2023 */ 'b', 'l', 't', 'z', 'c', 9, 0,
  /* 2030 */ 'f', 'l', 'o', 'g', '2', '.', 'd', 9, 0,
  /* 2039 */ 'f', 'e', 'x', 'p', '2', '.', 'd', 9, 0,
  /* 2048 */ 'a', 'd', 'd', '_', 'a', '.', 'd', 9, 0,
  /* 2057 */ 'f', 'm', 'i', 'n', '_', 'a', '.', 'd', 9, 0,
  /* 2067 */ 'a', 'd', 'd', 's', '_', 'a', '.', 'd', 9, 0,
  /* 2077 */ 'f', 'm', 'a', 'x', '_', 'a', '.', 'd', 9, 0,
  /* 2087 */ 'm', 'i', 'n', 'a', '.', 'd', 9, 0,
  /* 2095 */ 's', 'r', 'a', '.', 'd', 9, 0,
  /* 2102 */ 'm', 'a', 'x', 'a', '.', 'd', 9, 0,
  /* 2110 */ 'f', 's', 'u', 'b', '.', 'd', 9, 0,
  /* 2118 */ 'f', 'm', 's', 'u', 'b', '.', 'd', 9, 0,
  /* 2127 */ 'n', 'm', 's', 'u', 'b', '.', 'd', 9, 0,
  /* 2136 */ 'n', 'l', 'o', 'c', '.', 'd', 9, 0,
  /* 2144 */ 'n', 'l', 'z', 'c', '.', 'd', 9, 0,
  /* 2152 */ 'f', 'a', 'd', 'd', '.', 'd', 9, 0,
  /* 2160 */ 'f', 'm', 'a', 'd', 'd', '.', 'd', 9, 0,
  /* 2169 */ 'n', 'm', 'a', 'd', 'd', '.', 'd', 9, 0,
  /* 2178 */ 's', 'l', 'd', '.', 'd', 9, 0,
  /* 2185 */ 'p', 'c', 'k', 'o', 'd', '.', 'd', 9, 0,
  /* 2194 */ 'i', 'l', 'v', 'o', 'd', '.', 'd', 9, 0,
  /* 2203 */ 'c', '.', 'n', 'g', 'e', '.', 'd', 9, 0,
  /* 2212 */ 'c', '.', 'l', 'e', '.', 'd', 9, 0,
  /* 2220 */ 'c', 'm', 'p', '.', 'l', 'e', '.', 'd', 9, 0,
  /* 2230 */ 'f', 'c', 'l', 'e', '.', 'd', 9, 0,
  /* 2238 */ 'c', '.', 'n', 'g', 'l', 'e', '.', 'd', 9, 0,
  /* 2248 */ 'c', '.', 'o', 'l', 'e', '.', 'd', 9, 0,
  /* 2257 */ 'c', 'm', 'p', '.', 's', 'l', 'e', '.', 'd', 9, 0,
  /* 2268 */ 'f', 's', 'l', 'e', '.', 'd', 9, 0,
  /* 2276 */ 'c', '.', 'u', 'l', 'e', '.', 'd', 9, 0,
  /* 2285 */ 'c', 'm', 'p', '.', 'u', 'l', 'e', '.', 'd', 9, 0,
  /* 2296 */ 'f', 'c', 'u', 'l', 'e', '.', 'd', 9, 0,
  /* 2305 */ 'c', 'm', 'p', '.', 's', 'u', 'l', 'e', '.', 'd', 9, 0,
  /* 2317 */ 'f', 's', 'u', 'l', 'e', '.', 'd', 9, 0,
  /* 2326 */ 'f', 'c', 'n', 'e', '.', 'd', 9, 0,
  /* 2334 */ 'f', 's', 'n', 'e', '.', 'd', 9, 0,
  /* 2342 */ 'f', 'c', 'u', 'n', 'e', '.', 'd', 9, 0,
  /* 2351 */ 'f', 's', 'u', 'n', 'e', '.', 'd', 9, 0,
  /* 2360 */ 'i', 'n', 's', 'v', 'e', '.', 'd', 9, 0,
  /* 2369 */ 'c', '.', 'f', '.', 'd', 9, 0,
  /* 2376 */ 'c', 'm', 'p', '.', 'a', 'f', '.', 'd', 9, 0,
  /* 2386 */ 'f', 'c', 'a', 'f', '.', 'd', 9, 0,
  /* 2394 */ 'c', 'm', 'p', '.', 's', 'a', 'f', '.', 'd', 9, 0,
  /* 2405 */ 'f', 's', 'a', 'f', '.', 'd', 9, 0,
  /* 2413 */ 'm', 's', 'u', 'b', 'f', '.', 'd', 9, 0,
  /* 2422 */ 'm', 'a', 'd', 'd', 'f', '.', 'd', 9, 0,
  /* 2431 */ 'v', 's', 'h', 'f', '.', 'd', 9, 0,
  /* 2439 */ 'c', '.', 's', 'f', '.', 'd', 9, 0,
  /* 2447 */ 'm', 'o', 'v', 'f', '.', 'd', 9, 0,
  /* 2455 */ 'b', 'n', 'e', 'g', '.', 'd', 9, 0,
  /* 2463 */ 's', 'r', 'a', 'i', '.', 'd', 9, 0,
  /* 2471 */ 's', 'l', 'd', 'i', '.', 'd', 9, 0,
  /* 2479 */ 'b', 'n', 'e', 'g', 'i', '.', 'd', 9, 0,
  /* 2488 */ 's', 'l', 'l', 'i', '.', 'd', 9, 0,
  /* 2496 */ 's', 'r', 'l', 'i', '.', 'd', 9, 0,
  /* 2504 */ 'b', 'i', 'n', 's', 'l', 'i', '.', 'd', 9, 0,
  /* 2514 */ 'c', 'e', 'q', 'i', '.', 'd', 9, 0,
  /* 2522 */ 's', 'r', 'a', 'r', 'i', '.', 'd', 9, 0,
  /* 2531 */ 'b', 'c', 'l', 'r', 'i', '.', 'd', 9, 0,
  /* 2540 */ 's', 'r', 'l', 'r', 'i', '.', 'd', 9, 0,
  /* 2549 */ 'b', 'i', 'n', 's', 'r', 'i', '.', 'd', 9, 0,
  /* 2559 */ 's', 'p', 'l', 'a', 't', 'i', '.', 'd', 9, 0,
  /* 2569 */ 'b', 's', 'e', 't', 'i', '.', 'd', 9, 0,
  /* 2578 */ 's', 'u', 'b', 'v', 'i', '.', 'd', 9, 0,
  /* 2587 */ 'a', 'd', 'd', 'v', 'i', '.', 'd', 9, 0,
  /* 2596 */ 't', 'r', 'u', 'n', 'c', '.', 'l', '.', 'd', 9, 0,
  /* 2607 */ 'r', 'o', 'u', 'n', 'd', '.', 'l', '.', 'd', 9, 0,
  /* 2618 */ 'c', 'e', 'i', 'l', '.', 'l', '.', 'd', 9, 0,
  /* 2628 */ 'f', 'l', 'o', 'o', 'r', '.', 'l', '.', 'd', 9, 0,
  /* 2639 */ 'c', 'v', 't', '.', 'l', '.', 'd', 9, 0,
  /* 2648 */ 's', 'e', 'l', '.', 'd', 9, 0,
  /* 2655 */ 'c', '.', 'n', 'g', 'l', '.', 'd', 9, 0,
  /* 2664 */ 'f', 'i', 'l', 'l', '.', 'd', 9, 0,
  /* 2672 */ 's', 'l', 'l', '.', 'd', 9, 0,
  /* 2679 */ 'f', 'e', 'x', 'u', 'p', 'l', '.', 'd', 9, 0,
  /* 2689 */ 'f', 'f', 'q', 'l', '.', 'd', 9, 0,
  /* 2697 */ 's', 'r', 'l', '.', 'd', 9, 0,
  /* 2704 */ 'b', 'i', 'n', 's', 'l', '.', 'd', 9, 0,
  /* 2713 */ 'f', 'm', 'u', 'l', '.', 'd', 9, 0,
  /* 2721 */ 'i', 'l', 'v', 'l', '.', 'd', 9, 0,
  /* 2729 */ 'f', 'm', 'i', 'n', '.', 'd', 9, 0,
  /* 2737 */ 'c', '.', 'u', 'n', '.', 'd', 9, 0,
  /* 2745 */ 'c', 'm', 'p', '.', 'u', 'n', '.', 'd', 9, 0,
  /* 2755 */ 'f', 'c', 'u', 'n', '.', 'd', 9, 0,
  /* 2763 */ 'c', 'm', 'p', '.', 's', 'u', 'n', '.', 'd', 9, 0,
  /* 2774 */ 'f', 's', 'u', 'n', '.', 'd', 9, 0,
  /* 2782 */ 'm', 'o', 'v', 'n', '.', 'd', 9, 0,
  /* 2790 */ 'f', 'r', 'c', 'p', '.', 'd', 9, 0,
  /* 2798 */ 'c', '.', 'e', 'q', '.', 'd', 9, 0,
  /* 2806 */ 'c', 'm', 'p', '.', 'e', 'q', '.', 'd', 9, 0,
  /* 2816 */ 'f', 'c', 'e', 'q', '.', 'd', 9, 0,
  /* 2824 */ 'c', '.', 's', 'e', 'q', '.', 'd', 9, 0,
  /* 2833 */ 'c', 'm', 'p', '.', 's', 'e', 'q', '.', 'd', 9, 0,
  /* 2844 */ 'f', 's', 'e', 'q', '.', 'd', 9, 0,
  /* 2852 */ 'c', '.', 'u', 'e', 'q', '.', 'd', 9, 0,
  /* 2861 */ 'c', 'm', 'p', '.', 'u', 'e', 'q', '.', 'd', 9, 0,
  /* 2872 */ 'f', 'c', 'u', 'e', 'q', '.', 'd', 9, 0,
  /* 2881 */ 'c', 'm', 'p', '.', 's', 'u', 'e', 'q', '.', 'd', 9, 0,
  /* 2893 */ 'f', 's', 'u', 'e', 'q', '.', 'd', 9, 0,
  /* 2902 */ 's', 'r', 'a', 'r', '.', 'd', 9, 0,
  /* 2910 */ 'b', 'c', 'l', 'r', '.', 'd', 9, 0,
  /* 2918 */ 's', 'r', 'l', 'r', '.', 'd', 9, 0,
  /* 2926 */ 'f', 'c', 'o', 'r', '.', 'd', 9, 0,
  /* 2934 */ 'f', 's', 'o', 'r', '.', 'd', 9, 0,
  /* 2942 */ 'f', 'e', 'x', 'u', 'p', 'r', '.', 'd', 9, 0,
  /* 2952 */ 'f', 'f', 'q', 'r', '.', 'd', 9, 0,
  /* 2960 */ 'b', 'i', 'n', 's', 'r', '.', 'd', 9, 0,
  /* 2969 */ 'i', 'l', 'v', 'r', '.', 'd', 9, 0,
  /* 2977 */ 'c', 'v', 't', '.', 's', '.', 'd', 9, 0,
  /* 2986 */ 'a', 's', 'u', 'b', '_', 's', '.', 'd', 9, 0,
  /* 2996 */ 'h', 's', 'u', 'b', '_', 's', '.', 'd', 9, 0,
  /* 3006 */ 'd', 'p', 's', 'u', 'b', '_', 's', '.', 'd', 9, 0,
  /* 3017 */ 'f', 't', 'r', 'u', 'n', 'c', '_', 's', '.', 'd', 9, 0,
  /* 3029 */ 'h', 'a', 'd', 'd', '_', 's', '.', 'd', 9, 0,
  /* 3039 */ 'd', 'p', 'a', 'd', 'd', '_', 's', '.', 'd', 9, 0,
  /* 3050 */ 'm', 'o', 'd', '_', 's', '.', 'd', 9, 0,
  /* 3059 */ 'c', 'l', 'e', '_', 's', '.', 'd', 9, 0,
  /* 3068 */ 'a', 'v', 'e', '_', 's', '.', 'd', 9, 0,
  /* 3077 */ 'c', 'l', 'e', 'i', '_', 's', '.', 'd', 9, 0,
  /* 3087 */ 'm', 'i', 'n', 'i', '_', 's', '.', 'd', 9, 0,
  /* 3097 */ 'c', 'l', 't', 'i', '_', 's', '.', 'd', 9, 0,
  /* 3107 */ 'm', 'a', 'x', 'i', '_', 's', '.', 'd', 9, 0,
  /* 3117 */ 'm', 'i', 'n', '_', 's', '.', 'd', 9, 0,
  /* 3126 */ 'd', 'o', 't', 'p', '_', 's', '.', 'd', 9, 0,
  /* 3136 */ 'a', 'v', 'e', 'r', '_', 's', '.', 'd', 9, 0,
  /* 3146 */ 's', 'u', 'b', 's', '_', 's', '.', 'd', 9, 0,
  /* 3156 */ 'a', 'd', 'd', 's', '_', 's', '.', 'd', 9, 0,
  /* 3166 */ 's', 'a', 't', '_', 's', '.', 'd', 9, 0,
  /* 3175 */ 'c', 'l', 't', '_', 's', '.', 'd', 9, 0,
  /* 3184 */ 'f', 'f', 'i', 'n', 't', '_', 's', '.', 'd', 9, 0,
  /* 3195 */ 'f', 't', 'i', 'n', 't', '_', 's', '.', 'd', 9, 0,
  /* 3206 */ 's', 'u', 'b', 's', 'u', 'u', '_', 's', '.', 'd', 9, 0,
  /* 3218 */ 'd', 'i', 'v', '_', 's', '.', 'd', 9, 0,
  /* 3227 */ 'm', 'a', 'x', '_', 's', '.', 'd', 9, 0,
  /* 3236 */ 'c', 'o', 'p', 'y', '_', 's', '.', 'd', 9, 0,
  /* 3246 */ 'a', 'b', 's', '.', 'd', 9, 0,
  /* 3253 */ 'f', 'c', 'l', 'a', 's', 's', '.', 'd', 9, 0,
  /* 3263 */ 's', 'p', 'l', 'a', 't', '.', 'd', 9, 0,
  /* 3272 */ 'b', 's', 'e', 't', '.', 'd', 9, 0,
  /* 3280 */ 'c', '.', 'n', 'g', 't', '.', 'd', 9, 0,
  /* 3289 */ 'c', '.', 'l', 't', '.', 'd', 9, 0,
  /* 3297 */ 'c', 'm', 'p', '.', 'l', 't', '.', 'd', 9, 0,
  /* 3307 */ 'f', 'c', 'l', 't', '.', 'd', 9, 0,
  /* 3315 */ 'c', '.', 'o', 'l', 't', '.', 'd', 9, 0,
  /* 3324 */ 'c', 'm', 'p', '.', 's', 'l', 't', '.', 'd', 9, 0,
  /* 3335 */ 'f', 's', 'l', 't', '.', 'd', 9, 0,
  /* 3343 */ 'c', '.', 'u', 'l', 't', '.', 'd', 9, 0,
  /* 3352 */ 'c', 'm', 'p', '.', 'u', 'l', 't', '.', 'd', 9, 0,
  /* 3363 */ 'f', 'c', 'u', 'l', 't', '.', 'd', 9, 0,
  /* 3372 */ 'c', 'm', 'p', '.', 's', 'u', 'l', 't', '.', 'd', 9, 0,
  /* 3384 */ 'f', 's', 'u', 'l', 't', '.', 'd', 9, 0,
  /* 3393 */ 'p', 'c', 'n', 't', '.', 'd', 9, 0,
  /* 3401 */ 'f', 'r', 'i', 'n', 't', '.', 'd', 9, 0,
  /* 3410 */ 'i', 'n', 's', 'e', 'r', 't', '.', 'd', 9, 0,
  /* 3420 */ 'f', 's', 'q', 'r', 't', '.', 'd', 9, 0,
  /* 3429 */ 'f', 'r', 's', 'q', 'r', 't', '.', 'd', 9, 0,
  /* 3439 */ 's', 't', '.', 'd', 9, 0,
  /* 3445 */ 'm', 'o', 'v', 't', '.', 'd', 9, 0,
  /* 3453 */ 'a', 's', 'u', 'b', '_', 'u', '.', 'd', 9, 0,
  /* 3463 */ 'h', 's', 'u', 'b', '_', 'u', '.', 'd', 9, 0,
  /* 3473 */ 'd', 'p', 's', 'u', 'b', '_', 'u', '.', 'd', 9, 0,
  /* 3484 */ 'f', 't', 'r', 'u', 'n', 'c', '_', 'u', '.', 'd', 9, 0,
  /* 3496 */ 'h', 'a', 'd', 'd', '_', 'u', '.', 'd', 9, 0,
  /* 3506 */ 'd', 'p', 'a', 'd', 'd', '_', 'u', '.', 'd', 9, 0,
  /* 3517 */ 'm', 'o', 'd', '_', 'u', '.', 'd', 9, 0,
  /* 3526 */ 'c', 'l', 'e', '_', 'u', '.', 'd', 9, 0,
  /* 3535 */ 'a', 'v', 'e', '_', 'u', '.', 'd', 9, 0,
  /* 3544 */ 'c', 'l', 'e', 'i', '_', 'u', '.', 'd', 9, 0,
  /* 3554 */ 'm', 'i', 'n', 'i', '_', 'u', '.', 'd', 9, 0,
  /* 3564 */ 'c', 'l', 't', 'i', '_', 'u', '.', 'd', 9, 0,
  /* 3574 */ 'm', 'a', 'x', 'i', '_', 'u', '.', 'd', 9, 0,
  /* 3584 */ 'm', 'i', 'n', '_', 'u', '.', 'd', 9, 0,
  /* 3593 */ 'd', 'o', 't', 'p', '_', 'u', '.', 'd', 9, 0,
  /* 3603 */ 'a', 'v', 'e', 'r', '_', 'u', '.', 'd', 9, 0,
  /* 3613 */ 's', 'u', 'b', 's', '_', 'u', '.', 'd', 9, 0,
  /* 3623 */ 'a', 'd', 'd', 's', '_', 'u', '.', 'd', 9, 0,
  /* 3633 */ 's', 'u', 'b', 's', 'u', 's', '_', 'u', '.', 'd', 9, 0,
  /* 3645 */ 's', 'a', 't', '_', 'u', '.', 'd', 9, 0,
  /* 3654 */ 'c', 'l', 't', '_', 'u', '.', 'd', 9, 0,
  /* 3663 */ 'f', 'f', 'i', 'n', 't', '_', 'u', '.', 'd', 9, 0,
  /* 3674 */ 'f', 't', 'i', 'n', 't', '_', 'u', '.', 'd', 9, 0,
  /* 3685 */ 'd', 'i', 'v', '_', 'u', '.', 'd', 9, 0,
  /* 3694 */ 'm', 'a', 'x', '_', 'u', '.', 'd', 9, 0,
  /* 3703 */ 'c', 'o', 'p', 'y', '_', 'u', '.', 'd', 9, 0,
  /* 3713 */ 'm', 's', 'u', 'b', 'v', '.', 'd', 9, 0,
  /* 3722 */ 'm', 'a', 'd', 'd', 'v', '.', 'd', 9, 0,
  /* 3731 */ 'p', 'c', 'k', 'e', 'v', '.', 'd', 9, 0,
  /* 3740 */ 'i', 'l', 'v', 'e', 'v', '.', 'd', 9, 0,
  /* 3749 */ 'f', 'd', 'i', 'v', '.', 'd', 9, 0,
  /* 3757 */ 'm', 'u', 'l', 'v', '.', 'd', 9, 0,
  /* 3765 */ 'm', 'o', 'v', '.', 'd', 9, 0,
  /* 3772 */ 't', 'r', 'u', 'n', 'c', '.', 'w', '.', 'd', 9, 0,
  /* 3783 */ 'r', 'o', 'u', 'n', 'd', '.', 'w', '.', 'd', 9, 0,
  /* 3794 */ 'c', 'e', 'i', 'l', '.', 'w', '.', 'd', 9, 0,
  /* 3804 */ 'f', 'l', 'o', 'o', 'r', '.', 'w', '.', 'd', 9, 0,
  /* 3815 */ 'c', 'v', 't', '.', 'w', '.', 'd', 9, 0,
  /* 3824 */ 'f', 'm', 'a', 'x', '.', 'd', 9, 0,
  /* 3832 */ 'b', 'z', '.', 'd', 9, 0,
  /* 3838 */ 's', 'e', 'l', 'n', 'e', 'z', '.', 'd', 9, 0,
  /* 3848 */ 'b', 'n', 'z', '.', 'd', 9, 0,
  /* 3855 */ 's', 'e', 'l', 'e', 'q', 'z', '.', 'd', 9, 0,
  /* 3865 */ 'm', 'o', 'v', 'z', '.', 'd', 9, 0,
  /* 3873 */ 's', 'c', 'd', 9, 0,
  /* 3878 */ 'd', 'a', 'd', 'd', 9, 0,
  /* 3884 */ 'm', 'a', 'd', 'd', 9, 0,
  /* 3890 */ 'd', 's', 'h', 'd', 9, 0,
  /* 3896 */ 'l', 'l', 'd', 9, 0,
  /* 3901 */ 'a', 'n', 'd', 9, 0,
  /* 3906 */ 'p', 'r', 'e', 'p', 'e', 'n', 'd', 9, 0,
  /* 3915 */ 'a', 'p', 'p', 'e', 'n', 'd', 9, 0,
  /* 3923 */ 'd', 'm', 'o', 'd', 9, 0,
  /* 3929 */ 's', 'd', 9, 0,
  /* 3933 */ 't', 'g', 'e', 9, 0,
  /* 3938 */ 'c', 'a', 'c', 'h', 'e', 9, 0,
  /* 3945 */ 'b', 'n', 'e', 9, 0,
  /* 3950 */ 's', 'n', 'e', 9, 0,
  /* 3955 */ 't', 'n', 'e', 9, 0,
  /* 3960 */ 'm', 'o', 'v', 'e', 9, 0,
  /* 3966 */ 'b', 'c', '0', 'f', 9, 0,
  /* 3972 */ 'b', 'c', '1', 'f', 9, 0,
  /* 3978 */ 'b', 'c', '2', 'f', 9, 0,
  /* 3984 */ 'b', 'c', '3', 'f', 9, 0,
  /* 3990 */ 'p', 'r', 'e', 'f', 9, 0,
  /* 3996 */ 'm', 'o', 'v', 'f', 9, 0,
  /* 4002 */ 'n', 'e', 'g', 9, 0,
  /* 4007 */ 'a', 'd', 'd', '_', 'a', '.', 'h', 9, 0,
  /* 4016 */ 'm', 'i', 'n', '_', 'a', '.', 'h', 9, 0,
  /* 4025 */ 'a', 'd', 'd', 's', '_', 'a', '.', 'h', 9, 0,
  /* 4035 */ 'm', 'a', 'x', '_', 'a', '.', 'h', 9, 0,
  /* 4044 */ 's', 'r', 'a', '.', 'h', 9, 0,
  /* 4051 */ 'n', 'l', 'o', 'c', '.', 'h', 9, 0,
  /* 4059 */ 'n', 'l', 'z', 'c', '.', 'h', 9, 0,
  /* 4067 */ 's', 'l', 'd', '.', 'h', 9, 0,
  /* 4074 */ 'p', 'c', 'k', 'o', 'd', '.', 'h', 9, 0,
  /* 4083 */ 'i', 'l', 'v', 'o', 'd', '.', 'h', 9, 0,
  /* 4092 */ 'i', 'n', 's', 'v', 'e', '.', 'h', 9, 0,
  /* 4101 */ 'v', 's', 'h', 'f', '.', 'h', 9, 0,
  /* 4109 */ 'b', 'n', 'e', 'g', '.', 'h', 9, 0,
  /* 4117 */ 's', 'r', 'a', 'i', '.', 'h', 9, 0,
  /* 4125 */ 's', 'l', 'd', 'i', '.', 'h', 9, 0,
  /* 4133 */ 'b', 'n', 'e', 'g', 'i', '.', 'h', 9, 0,
  /* 4142 */ 's', 'l', 'l', 'i', '.', 'h', 9, 0,
  /* 4150 */ 's', 'r', 'l', 'i', '.', 'h', 9, 0,
  /* 4158 */ 'b', 'i', 'n', 's', 'l', 'i', '.', 'h', 9, 0,
  /* 4168 */ 'c', 'e', 'q', 'i', '.', 'h', 9, 0,
  /* 4176 */ 's', 'r', 'a', 'r', 'i', '.', 'h', 9, 0,
  /* 4185 */ 'b', 'c', 'l', 'r', 'i', '.', 'h', 9, 0,
  /* 4194 */ 's', 'r', 'l', 'r', 'i', '.', 'h', 9, 0,
  /* 4203 */ 'b', 'i', 'n', 's', 'r', 'i', '.', 'h', 9, 0,
  /* 4213 */ 's', 'p', 'l', 'a', 't', 'i', '.', 'h', 9, 0,
  /* 4223 */ 'b', 's', 'e', 't', 'i', '.', 'h', 9, 0,
  /* 4232 */ 's', 'u', 'b', 'v', 'i', '.', 'h', 9, 0,
  /* 4241 */ 'a', 'd', 'd', 'v', 'i', '.', 'h', 9, 0,
  /* 4250 */ 'f', 'i', 'l', 'l', '.', 'h', 9, 0,
  /* 4258 */ 's', 'l', 'l', '.', 'h', 9, 0,
  /* 4265 */ 's', 'r', 'l', '.', 'h', 9, 0,
  /* 4272 */ 'b', 'i', 'n', 's', 'l', '.', 'h', 9, 0,
  /* 4281 */ 'i', 'l', 'v', 'l', '.', 'h', 9, 0,
  /* 4289 */ 'f', 'e', 'x', 'd', 'o', '.', 'h', 9, 0,
  /* 4298 */ 'm', 's', 'u', 'b', '_', 'q', '.', 'h', 9, 0,
  /* 4308 */ 'm', 'a', 'd', 'd', '_', 'q', '.', 'h', 9, 0,
  /* 4318 */ 'm', 'u', 'l', '_', 'q', '.', 'h', 9, 0,
  /* 4327 */ 'm', 's', 'u', 'b', 'r', '_', 'q', '.', 'h', 9, 0,
  /* 4338 */ 'm', 'a', 'd', 'd', 'r', '_', 'q', '.', 'h', 9, 0,
  /* 4349 */ 'm', 'u', 'l', 'r', '_', 'q', '.', 'h', 9, 0,
  /* 4359 */ 'c', 'e', 'q', '.', 'h', 9, 0,
  /* 4366 */ 'f', 't', 'q', '.', 'h', 9, 0,
  /* 4373 */ 's', 'r', 'a', 'r', '.', 'h', 9, 0,
  /* 4381 */ 'b', 'c', 'l', 'r', '.', 'h', 9, 0,
  /* 4389 */ 's', 'r', 'l', 'r', '.', 'h', 9, 0,
  /* 4397 */ 'b', 'i', 'n', 's', 'r', '.', 'h', 9, 0,
  /* 4406 */ 'i', 'l', 'v', 'r', '.', 'h', 9, 0,
  /* 4414 */ 'a', 's', 'u', 'b', '_', 's', '.', 'h', 9, 0,
  /* 4424 */ 'h', 's', 'u', 'b', '_', 's', '.', 'h', 9, 0,
  /* 4434 */ 'd', 'p', 's', 'u', 'b', '_', 's', '.', 'h', 9, 0,
  /* 4445 */ 'h', 'a', 'd', 'd', '_', 's', '.', 'h', 9, 0,
  /* 4455 */ 'd', 'p', 'a', 'd', 'd', '_', 's', '.', 'h', 9, 0,
  /* 4466 */ 'm', 'o', 'd', '_', 's', '.', 'h', 9, 0,
  /* 4475 */ 'c', 'l', 'e', '_', 's', '.', 'h', 9, 0,
  /* 4484 */ 'a', 'v', 'e', '_', 's', '.', 'h', 9, 0,
  /* 4493 */ 'c', 'l', 'e', 'i', '_', 's', '.', 'h', 9, 0,
  /* 4503 */ 'm', 'i', 'n', 'i', '_', 's', '.', 'h', 9, 0,
  /* 4513 */ 'c', 'l', 't', 'i', '_', 's', '.', 'h', 9, 0,
  /* 4523 */ 'm', 'a', 'x', 'i', '_', 's', '.', 'h', 9, 0,
  /* 4533 */ 'm', 'i', 'n', '_', 's', '.', 'h', 9, 0,
  /* 4542 */ 'd', 'o', 't', 'p', '_', 's', '.', 'h', 9, 0,
  /* 4552 */ 'a', 'v', 'e', 'r', '_', 's', '.', 'h', 9, 0,
  /* 4562 */ 'e', 'x', 't', 'r', '_', 's', '.', 'h', 9, 0,
  /* 4572 */ 's', 'u', 'b', 's', '_', 's', '.', 'h', 9, 0,
  /* 4582 */ 'a', 'd', 'd', 's', '_', 's', '.', 'h', 9, 0,
  /* 4592 */ 's', 'a', 't', '_', 's', '.', 'h', 9, 0,
  /* 4601 */ 'c', 'l', 't', '_', 's', '.', 'h', 9, 0,
  /* 4610 */ 's', 'u', 'b', 's', 'u', 'u', '_', 's', '.', 'h', 9, 0,
  /* 4622 */ 'd', 'i', 'v', '_', 's', '.', 'h', 9, 0,
  /* 4631 */ 'e', 'x', 't', 'r', 'v', '_', 's', '.', 'h', 9, 0,
  /* 4642 */ 'm', 'a', 'x', '_', 's', '.', 'h', 9, 0,
  /* 4651 */ 'c', 'o', 'p', 'y', '_', 's', '.', 'h', 9, 0,
  /* 4661 */ 's', 'p', 'l', 'a', 't', '.', 'h', 9, 0,
  /* 4670 */ 'b', 's', 'e', 't', '.', 'h', 9, 0,
  /* 4678 */ 'p', 'c', 'n', 't', '.', 'h', 9, 0,
  /* 4686 */ 'i', 'n', 's', 'e', 'r', 't', '.', 'h', 9, 0,
  /* 4696 */ 's', 't', '.', 'h', 9, 0,
  /* 4702 */ 'a', 's', 'u', 'b', '_', 'u', '.', 'h', 9, 0,
  /* 4712 */ 'h', 's', 'u', 'b', '_', 'u', '.', 'h', 9, 0,
  /* 4722 */ 'd', 'p', 's', 'u', 'b', '_', 'u', '.', 'h', 9, 0,
  /* 4733 */ 'h', 'a', 'd', 'd', '_', 'u', '.', 'h', 9, 0,
  /* 4743 */ 'd', 'p', 'a', 'd', 'd', '_', 'u', '.', 'h', 9, 0,
  /* 4754 */ 'm', 'o', 'd', '_', 'u', '.', 'h', 9, 0,
  /* 4763 */ 'c', 'l', 'e', '_', 'u', '.', 'h', 9, 0,
  /* 4772 */ 'a', 'v', 'e', '_', 'u', '.', 'h', 9, 0,
  /* 4781 */ 'c', 'l', 'e', 'i', '_', 'u', '.', 'h', 9, 0,
  /* 4791 */ 'm', 'i', 'n', 'i', '_', 'u', '.', 'h', 9, 0,
  /* 4801 */ 'c', 'l', 't', 'i', '_', 'u', '.', 'h', 9, 0,
  /* 4811 */ 'm', 'a', 'x', 'i', '_', 'u', '.', 'h', 9, 0,
  /* 4821 */ 'm', 'i', 'n', '_', 'u', '.', 'h', 9, 0,
  /* 4830 */ 'd', 'o', 't', 'p', '_', 'u', '.', 'h', 9, 0,
  /* 4840 */ 'a', 'v', 'e', 'r', '_', 'u', '.', 'h', 9, 0,
  /* 4850 */ 's', 'u', 'b', 's', '_', 'u', '.', 'h', 9, 0,
  /* 4860 */ 'a', 'd', 'd', 's', '_', 'u', '.', 'h', 9, 0,
  /* 4870 */ 's', 'u', 'b', 's', 'u', 's', '_', 'u', '.', 'h', 9, 0,
  /* 4882 */ 's', 'a', 't', '_', 'u', '.', 'h', 9, 0,
  /* 4891 */ 'c', 'l', 't', '_', 'u', '.', 'h', 9, 0,
  /* 4900 */ 'd', 'i', 'v', '_', 'u', '.', 'h', 9, 0,
  /* 4909 */ 'm', 'a', 'x', '_', 'u', '.', 'h', 9, 0,
  /* 4918 */ 'c', 'o', 'p', 'y', '_', 'u', '.', 'h', 9, 0,
  /* 4928 */ 'm', 's', 'u', 'b', 'v', '.', 'h', 9, 0,
  /* 4937 */ 'm', 'a', 'd', 'd', 'v', '.', 'h', 9, 0,
  /* 4946 */ 'p', 'c', 'k', 'e', 'v', '.', 'h', 9, 0,
  /* 4955 */ 'i', 'l', 'v', 'e', 'v', '.', 'h', 9, 0,
  /* 4964 */ 'm', 'u', 'l', 'v', '.', 'h', 9, 0,
  /* 4972 */ 'b', 'z', '.', 'h', 9, 0,
  /* 4978 */ 'b', 'n', 'z', '.', 'h', 9, 0,
  /* 4985 */ 'd', 's', 'b', 'h', 9, 0,
  /* 4991 */ 'w', 's', 'b', 'h', 9, 0,
  /* 4997 */ 's', 'e', 'h', 9, 0,
  /* 5002 */ 'l', 'h', 9, 0,
  /* 5006 */ 's', 'h', 'r', 'a', '.', 'p', 'h', 9, 0,
  /* 5015 */ 'p', 'r', 'e', 'c', 'r', 'q', '.', 'q', 'b', '.', 'p', 'h', 9, 0,
  /* 5029 */ 'p', 'r', 'e', 'c', 'r', '.', 'q', 'b', '.', 'p', 'h', 9, 0,
  /* 5042 */ 'p', 'r', 'e', 'c', 'r', 'q', 'u', '_', 's', '.', 'q', 'b', '.', 'p', 'h', 9, 0,
  /* 5059 */ 'c', 'm', 'p', '.', 'l', 'e', '.', 'p', 'h', 9, 0,
  /* 5070 */ 's', 'u', 'b', 'q', 'h', '.', 'p', 'h', 9, 0,
  /* 5080 */ 'a', 'd', 'd', 'q', 'h', '.', 'p', 'h', 9, 0,
  /* 5090 */ 'p', 'i', 'c', 'k', '.', 'p', 'h', 9, 0,
  /* 5099 */ 's', 'h', 'l', 'l', '.', 'p', 'h', 9, 0,
  /* 5108 */ 'r', 'e', 'p', 'l', '.', 'p', 'h', 9, 0,
  /* 5117 */ 's', 'h', 'r', 'l', '.', 'p', 'h', 9, 0,
  /* 5126 */ 'p', 'a', 'c', 'k', 'r', 'l', '.', 'p', 'h', 9, 0,
  /* 5137 */ 'm', 'u', 'l', '.', 'p', 'h', 9, 0,
  /* 5145 */ 's', 'u', 'b', 'q', '.', 'p', 'h', 9, 0,
  /* 5154 */ 'a', 'd', 'd', 'q', '.', 'p', 'h', 9, 0,
  /* 5163 */ 'c', 'm', 'p', '.', 'e', 'q', '.', 'p', 'h', 9, 0,
  /* 5174 */ 's', 'h', 'r', 'a', '_', 'r', '.', 'p', 'h', 9, 0,
  /* 5185 */ 's', 'u', 'b', 'q', 'h', '_', 'r', '.', 'p', 'h', 9, 0,
  /* 5197 */ 'a', 'd', 'd', 'q', 'h', '_', 'r', '.', 'p', 'h', 9, 0,
  /* 5209 */ 's', 'h', 'r', 'a', 'v', '_', 'r', '.', 'p', 'h', 9, 0,
  /* 5221 */ 's', 'h', 'l', 'l', '_', 's', '.', 'p', 'h', 9, 0,
  /* 5232 */ 'm', 'u', 'l', '_', 's', '.', 'p', 'h', 9, 0,
  /* 5242 */ 's', 'u', 'b', 'q', '_', 's', '.', 'p', 'h', 9, 0,
  /* 5253 */ 'a', 'd', 'd', 'q', '_', 's', '.', 'p', 'h', 9, 0,
  /* 5264 */ 'm', 'u', 'l', 'q', '_', 's', '.', 'p', 'h', 9, 0,
  /* 5275 */ 'a', 'b', 's', 'q', '_', 's', '.', 'p', 'h', 9, 0,
  /* 5286 */ 's', 'u', 'b', 'u', '_', 's', '.', 'p', 'h', 9, 0,
  /* 5297 */ 'a', 'd', 'd', 'u', '_', 's', '.', 'p', 'h', 9, 0,
  /* 5308 */ 's', 'h', 'l', 'l', 'v', '_', 's', '.', 'p', 'h', 9, 0,
  /* 5320 */ 'm', 'u', 'l', 'q', '_', 'r', 's', '.', 'p', 'h', 9, 0,
  /* 5332 */ 'c', 'm', 'p', '.', 'l', 't', '.', 'p', 'h', 9, 0,
  /* 5343 */ 's', 'u', 'b', 'u', '.', 'p', 'h', 9, 0,
  /* 5352 */ 'a', 'd', 'd', 'u', '.', 'p', 'h', 9, 0,
  /* 5361 */ 's', 'h', 'r', 'a', 'v', '.', 'p', 'h', 9, 0,
  /* 5371 */ 's', 'h', 'l', 'l', 'v', '.', 'p', 'h', 9, 0,
  /* 5381 */ 'r', 'e', 'p', 'l', 'v', '.', 'p', 'h', 9, 0,
  /* 5391 */ 's', 'h', 'r', 'l', 'v', '.', 'p', 'h', 9, 0,
  /* 5401 */ 'd', 'p', 'a', '.', 'w', '.', 'p', 'h', 9, 0,
  /* 5411 */ 'd', 'p', 'a', 'q', 'x', '_', 's', 'a', '.', 'w', '.', 'p', 'h', 9, 0,
  /* 5426 */ 'd', 'p', 's', 'q', 'x', '_', 's', 'a', '.', 'w', '.', 'p', 'h', 9, 0,
  /* 5441 */ 'm', 'u', 'l', 's', 'a', '.', 'w', '.', 'p', 'h', 9, 0,
  /* 5453 */ 'd', 'p', 'a', 'q', '_', 's', '.', 'w', '.', 'p', 'h', 9, 0,
  /* 5466 */ 'm', 'u', 'l', 's', 'a', 'q', '_', 's', '.', 'w', '.', 'p', 'h', 9, 0,
  /* 5481 */ 'd', 'p', 's', 'q', '_', 's', '.', 'w', '.', 'p', 'h', 9, 0,
  /* 5494 */ 'd', 'p', 'a', 'q', 'x', '_', 's', '.', 'w', '.', 'p', 'h', 9, 0,
  /* 5508 */ 'd', 'p', 's', 'q', 'x', '_', 's', '.', 'w', '.', 'p', 'h', 9, 0,
  /* 5522 */ 'd', 'p', 's', '.', 'w', '.', 'p', 'h', 9, 0,
  /* 5532 */ 'd', 'p', 'a', 'x', '.', 'w', '.', 'p', 'h', 9, 0,
  /* 5543 */ 'd', 'p', 's', 'x', '.', 'w', '.', 'p', 'h', 9, 0,
  /* 5554 */ 's', 'h', 9, 0,
  /* 5558 */ 'd', 'm', 'u', 'h', 9, 0,
  /* 5564 */ 's', 'y', 'n', 'c', 'i', 9, 0,
  /* 5571 */ 'd', 'a', 'd', 'd', 'i', 9, 0,
  /* 5578 */ 'a', 'n', 'd', 'i', 9, 0,
  /* 5584 */ 't', 'g', 'e', 'i', 9, 0,
  /* 5590 */ 's', 'n', 'e', 'i', 9, 0,
  /* 5596 */ 't', 'n', 'e', 'i', 9, 0,
  /* 5602 */ 'd', 'a', 'h', 'i', 9, 0,
  /* 5608 */ 'm', 'f', 'h', 'i', 9, 0,
  /* 5614 */ 'm', 't', 'h', 'i', 9, 0,
  /* 5620 */ '.', 'a', 'l', 'i', 'g', 'n', 32, '2', 10, 9, 'l', 'i', 9, 0,
  /* 5634 */ 'd', 'l', 'i', 9, 0,
  /* 5639 */ 'c', 'm', 'p', 'i', 9, 0,
  /* 5645 */ 's', 'e', 'q', 'i', 9, 0,
  /* 5651 */ 't', 'e', 'q', 'i', 9, 0,
  /* 5657 */ 'x', 'o', 'r', 'i', 9, 0,
  /* 5663 */ 'd', 'a', 't', 'i', 9, 0,
  /* 5669 */ 's', 'l', 't', 'i', 9, 0,
  /* 5675 */ 't', 'l', 't', 'i', 9, 0,
  /* 5681 */ 'd', 'a', 'u', 'i', 9, 0,
  /* 5687 */ 'l', 'u', 'i', 9, 0,
  /* 5692 */ 'j', 9, 0,
  /* 5695 */ 'b', 'r', 'e', 'a', 'k', 9, 0,
  /* 5702 */ 'c', 'v', 't', '.', 'd', '.', 'l', 9, 0,
  /* 5711 */ 'c', 'v', 't', '.', 's', '.', 'l', 9, 0,
  /* 5720 */ 'b', 'a', 'l', 9, 0,
  /* 5725 */ 'j', 'a', 'l', 9, 0,
  /* 5730 */ 'b', 'g', 'e', 'z', 'a', 'l', 9, 0,
  /* 5738 */ 'b', 'l', 't', 'z', 'a', 'l', 9, 0,
  /* 5746 */ 'd', 'p', 'a', 'u', '.', 'h', '.', 'q', 'b', 'l', 9, 0,
  /* 5758 */ 'd', 'p', 's', 'u', '.', 'h', '.', 'q', 'b', 'l', 9, 0,
  /* 5770 */ 'm', 'u', 'l', 'e', 'u', '_', 's', '.', 'p', 'h', '.', 'q', 'b', 'l', 9, 0,
  /* 5786 */ 'p', 'r', 'e', 'c', 'e', 'u', '.', 'p', 'h', '.', 'q', 'b', 'l', 9, 0,
  /* 5801 */ 'p', 'r', 'e', 'c', 'e', 'q', 'u', '.', 'p', 'h', '.', 'q', 'b', 'l', 9, 0,
  /* 5817 */ 'l', 'd', 'l', 9, 0,
  /* 5822 */ 's', 'd', 'l', 9, 0,
  /* 5827 */ 'b', 'n', 'e', 'l', 9, 0,
  /* 5833 */ 'b', 'c', '0', 'f', 'l', 9, 0,
  /* 5840 */ 'b', 'c', '1', 'f', 'l', 9, 0,
  /* 5847 */ 'b', 'c', '2', 'f', 'l', 9, 0,
  /* 5854 */ 'b', 'c', '3', 'f', 'l', 9, 0,
  /* 5861 */ 'm', 'a', 'q', '_', 's', 'a', '.', 'w', '.', 'p', 'h', 'l', 9, 0,
  /* 5875 */ 'p', 'r', 'e', 'c', 'e', 'q', '.', 'w', '.', 'p', 'h', 'l', 9, 0,
  /* 5889 */ 'm', 'a', 'q', '_', 's', '.', 'w', '.', 'p', 'h', 'l', 9, 0,
  /* 5902 */ 'm', 'u', 'l', 'e', 'q', '_', 's', '.', 'w', '.', 'p', 'h', 'l', 9, 0,
  /* 5917 */ 's', 'y', 's', 'c', 'a', 'l', 'l', 9, 0,
  /* 5926 */ 'b', 'g', 'e', 'z', 'a', 'l', 'l', 9, 0,
  /* 5935 */ 'b', 'l', 't', 'z', 'a', 'l', 'l', 9, 0,
  /* 5944 */ 'd', 's', 'l', 'l', 9, 0,
  /* 5950 */ 'b', 'e', 'q', 'l', 9, 0,
  /* 5956 */ 'd', 's', 'r', 'l', 9, 0,
  /* 5962 */ 'b', 'c', '0', 't', 'l', 9, 0,
  /* 5969 */ 'b', 'c', '1', 't', 'l', 9, 0,
  /* 5976 */ 'b', 'c', '2', 't', 'l', 9, 0,
  /* 5983 */ 'b', 'c', '3', 't', 'l', 9, 0,
  /* 5990 */ 'd', 'm', 'u', 'l', 9, 0,
  /* 5996 */ 'l', 'w', 'l', 9, 0,
  /* 6001 */ 's', 'w', 'l', 9, 0,
  /* 6006 */ 'b', 'g', 'e', 'z', 'l', 9, 0,
  /* 6013 */ 'b', 'l', 'e', 'z', 'l', 9, 0,
  /* 6020 */ 'b', 'g', 't', 'z', 'l', 9, 0,
  /* 6027 */ 'b', 'l', 't', 'z', 'l', 9, 0,
  /* 6034 */ 'l', 'w', 'm', 9, 0,
  /* 6039 */ 's', 'w', 'm', 9, 0,
  /* 6044 */ 'b', 'a', 'l', 'i', 'g', 'n', 9, 0,
  /* 6052 */ 'd', 'a', 'l', 'i', 'g', 'n', 9, 0,
  /* 6060 */ 'm', 'o', 'v', 'n', 9, 0,
  /* 6066 */ 'd', 'c', 'l', 'o', 9, 0,
  /* 6072 */ 'm', 'f', 'l', 'o', 9, 0,
  /* 6078 */ 's', 'h', 'i', 'l', 'o', 9, 0,
  /* 6085 */ 'm', 't', 'l', 'o', 9, 0,
  /* 6091 */ 'd', 'b', 'i', 't', 's', 'w', 'a', 'p', 9, 0,
  /* 6101 */ 's', 'd', 'b', 'b', 'p', 9, 0,
  /* 6108 */ 'e', 'x', 't', 'p', 'd', 'p', 9, 0,
  /* 6116 */ 'm', 'o', 'v', 'e', 'p', 9, 0,
  /* 6123 */ 'm', 't', 'h', 'l', 'i', 'p', 9, 0,
  /* 6131 */ 'c', 'm', 'p', 9, 0,
  /* 6136 */ 'd', 'p', 'o', 'p', 9, 0,
  /* 6142 */ 'a', 'd', 'd', 'i', 'u', 'r', '1', 's', 'p', 9, 0,
  /* 6153 */ 'l', 'o', 'a', 'd', '_', 'c', 'c', 'o', 'n', 'd', '_', 'd', 's', 'p', 9, 0,
  /* 6169 */ 's', 't', 'o', 'r', 'e', '_', 'c', 'c', 'o', 'n', 'd', '_', 'd', 's', 'p', 9, 0,
  /* 6186 */ 'r', 'd', 'd', 's', 'p', 9, 0,
  /* 6193 */ 'w', 'r', 'd', 's', 'p', 9, 0,
  /* 6200 */ 'j', 'r', 'a', 'd', 'd', 'i', 'u', 's', 'p', 9, 0,
  /* 6211 */ 'e', 'x', 't', 'p', 9, 0,
  /* 6217 */ 'l', 'w', 'p', 9, 0,
  /* 6222 */ 's', 'w', 'p', 9, 0,
  /* 6227 */ 'b', 'e', 'q', 9, 0,
  /* 6232 */ 's', 'e', 'q', 9, 0,
  /* 6237 */ 't', 'e', 'q', 9, 0,
  /* 6242 */ 'd', 'p', 'a', 'u', '.', 'h', '.', 'q', 'b', 'r', 9, 0,
  /* 6254 */ 'd', 'p', 's', 'u', '.', 'h', '.', 'q', 'b', 'r', 9, 0,
  /* 6266 */ 'm', 'u', 'l', 'e', 'u', '_', 's', '.', 'p', 'h', '.', 'q', 'b', 'r', 9, 0,
  /* 6282 */ 'p', 'r', 'e', 'c', 'e', 'u', '.', 'p', 'h', '.', 'q', 'b', 'r', 9, 0,
  /* 6297 */ 'p', 'r', 'e', 'c', 'e', 'q', 'u', '.', 'p', 'h', '.', 'q', 'b', 'r', 9, 0,
  /* 6313 */ 'l', 'd', 'r', 9, 0,
  /* 6318 */ 's', 'd', 'r', 9, 0,
  /* 6323 */ 'm', 'a', 'q', '_', 's', 'a', '.', 'w', '.', 'p', 'h', 'r', 9, 0,
  /* 6337 */ 'p', 'r', 'e', 'c', 'e', 'q', '.', 'w', '.', 'p', 'h', 'r', 9, 0,
  /* 6351 */ 'm', 'a', 'q', '_', 's', '.', 'w', '.', 'p', 'h', 'r', 9, 0,
  /* 6364 */ 'm', 'u', 'l', 'e', 'q', '_', 's', '.', 'w', '.', 'p', 'h', 'r', 9, 0,
  /* 6379 */ 'j', 'r', 9, 0,
  /* 6383 */ 'j', 'a', 'l', 'r', 9, 0,
  /* 6389 */ 'n', 'o', 'r', 9, 0,
  /* 6394 */ 'x', 'o', 'r', 9, 0,
  /* 6399 */ 'd', 'r', 'o', 't', 'r', 9, 0,
  /* 6406 */ 'r', 'd', 'h', 'w', 'r', 9, 0,
  /* 6413 */ 'l', 'w', 'r', 9, 0,
  /* 6418 */ 's', 'w', 'r', 9, 0,
  /* 6423 */ 'm', 'i', 'n', 'a', '.', 's', 9, 0,
  /* 6431 */ 'm', 'a', 'x', 'a', '.', 's', 9, 0,
  /* 6439 */ 'n', 'm', 's', 'u', 'b', '.', 's', 9, 0,
  /* 6448 */ 'c', 'v', 't', '.', 'd', '.', 's', 9, 0,
  /* 6457 */ 'n', 'm', 'a', 'd', 'd', '.', 's', 9, 0,
  /* 6466 */ 'c', '.', 'n', 'g', 'e', '.', 's', 9, 0,
  /* 6475 */ 'c', '.', 'l', 'e', '.', 's', 9, 0,
  /* 6483 */ 'c', 'm', 'p', '.', 'l', 'e', '.', 's', 9, 0,
  /* 6493 */ 'c', '.', 'n', 'g', 'l', 'e', '.', 's', 9, 0,
  /* 6503 */ 'c', '.', 'o', 'l', 'e', '.', 's', 9, 0,
  /* 6512 */ 'c', 'm', 'p', '.', 's', 'l', 'e', '.', 's', 9, 0,
  /* 6523 */ 'c', '.', 'u', 'l', 'e', '.', 's', 9, 0,
  /* 6532 */ 'c', 'm', 'p', '.', 'u', 'l', 'e', '.', 's', 9, 0,
  /* 6543 */ 'c', 'm', 'p', '.', 's', 'u', 'l', 'e', '.', 's', 9, 0,
  /* 6555 */ 'c', '.', 'f', '.', 's', 9, 0,
  /* 6562 */ 'c', 'm', 'p', '.', 'a', 'f', '.', 's', 9, 0,
  /* 6572 */ 'c', 'm', 'p', '.', 's', 'a', 'f', '.', 's', 9, 0,
  /* 6583 */ 'm', 's', 'u', 'b', 'f', '.', 's', 9, 0,
  /* 6592 */ 'm', 'a', 'd', 'd', 'f', '.', 's', 9, 0,
  /* 6601 */ 'c', '.', 's', 'f', '.', 's', 9, 0,
  /* 6609 */ 'm', 'o', 'v', 'f', '.', 's', 9, 0,
  /* 6617 */ 'n', 'e', 'g', '.', 's', 9, 0,
  /* 6624 */ 't', 'r', 'u', 'n', 'c', '.', 'l', '.', 's', 9, 0,
  /* 6635 */ 'r', 'o', 'u', 'n', 'd', '.', 'l', '.', 's', 9, 0,
  /* 6646 */ 'c', 'e', 'i', 'l', '.', 'l', '.', 's', 9, 0,
  /* 6656 */ 'f', 'l', 'o', 'o', 'r', '.', 'l', '.', 's', 9, 0,
  /* 6667 */ 'c', 'v', 't', '.', 'l', '.', 's', 9, 0,
  /* 6676 */ 's', 'e', 'l', '.', 's', 9, 0,
  /* 6683 */ 'c', '.', 'n', 'g', 'l', '.', 's', 9, 0,
  /* 6692 */ 'm', 'u', 'l', '.', 's', 9, 0,
  /* 6699 */ 'm', 'i', 'n', '.', 's', 9, 0,
  /* 6706 */ 'c', '.', 'u', 'n', '.', 's', 9, 0,
  /* 6714 */ 'c', 'm', 'p', '.', 'u', 'n', '.', 's', 9, 0,
  /* 6724 */ 'c', 'm', 'p', '.', 's', 'u', 'n', '.', 's', 9, 0,
  /* 6735 */ 'm', 'o', 'v', 'n', '.', 's', 9, 0,
  /* 6743 */ 'c', '.', 'e', 'q', '.', 's', 9, 0,
  /* 6751 */ 'c', 'm', 'p', '.', 'e', 'q', '.', 's', 9, 0,
  /* 6761 */ 'c', '.', 's', 'e', 'q', '.', 's', 9, 0,
  /* 6770 */ 'c', 'm', 'p', '.', 's', 'e', 'q', '.', 's', 9, 0,
  /* 6781 */ 'c', '.', 'u', 'e', 'q', '.', 's', 9, 0,
  /* 6790 */ 'c', 'm', 'p', '.', 'u', 'e', 'q', '.', 's', 9, 0,
  /* 6801 */ 'c', 'm', 'p', '.', 's', 'u', 'e', 'q', '.', 's', 9, 0,
  /* 6813 */ 'a', 'b', 's', '.', 's', 9, 0,
  /* 6820 */ 'c', 'l', 'a', 's', 's', '.', 's', 9, 0,
  /* 6829 */ 'c', '.', 'n', 'g', 't', '.', 's', 9, 0,
  /* 6838 */ 'c', '.', 'l', 't', '.', 's', 9, 0,
  /* 6846 */ 'c', 'm', 'p', '.', 'l', 't', '.', 's', 9, 0,
  /* 6856 */ 'c', '.', 'o', 'l', 't', '.', 's', 9, 0,
  /* 6865 */ 'c', 'm', 'p', '.', 's', 'l', 't', '.', 's', 9, 0,
  /* 6876 */ 'c', '.', 'u', 'l', 't', '.', 's', 9, 0,
  /* 6885 */ 'c', 'm', 'p', '.', 'u', 'l', 't', '.', 's', 9, 0,
  /* 6896 */ 'c', 'm', 'p', '.', 's', 'u', 'l', 't', '.', 's', 9, 0,
  /* 6908 */ 'r', 'i', 'n', 't', '.', 's', 9, 0,
  /* 6916 */ 's', 'q', 'r', 't', '.', 's', 9, 0,
  /* 6924 */ 'm', 'o', 'v', 't', '.', 's', 9, 0,
  /* 6932 */ 'd', 'i', 'v', '.', 's', 9, 0,
  /* 6939 */ 'm', 'o', 'v', '.', 's', 9, 0,
  /* 6946 */ 't', 'r', 'u', 'n', 'c', '.', 'w', '.', 's', 9, 0,
  /* 6957 */ 'r', 'o', 'u', 'n', 'd', '.', 'w', '.', 's', 9, 0,
  /* 6968 */ 'c', 'e', 'i', 'l', '.', 'w', '.', 's', 9, 0,
  /* 6978 */ 'f', 'l', 'o', 'o', 'r', '.', 'w', '.', 's', 9, 0,
  /* 6989 */ 'c', 'v', 't', '.', 'w', '.', 's', 9, 0,
  /* 6998 */ 'm', 'a', 'x', '.', 's', 9, 0,
  /* 7005 */ 's', 'e', 'l', 'n', 'e', 'z', '.', 's', 9, 0,
  /* 7015 */ 's', 'e', 'l', 'e', 'q', 'z', '.', 's', 9, 0,
  /* 7025 */ 'm', 'o', 'v', 'z', '.', 's', 9, 0,
  /* 7033 */ 'j', 'a', 'l', 's', 9, 0,
  /* 7039 */ 'b', 'g', 'e', 'z', 'a', 'l', 's', 9, 0,
  /* 7048 */ 'b', 'l', 't', 'z', 'a', 'l', 's', 9, 0,
  /* 7057 */ 'j', 'a', 'l', 'r', 's', 9, 0,
  /* 7064 */ 'l', 'w', 'x', 's', 9, 0,
  /* 7070 */ 'b', 'c', '0', 't', 9, 0,
  /* 7076 */ 'b', 'c', '1', 't', 9, 0,
  /* 7082 */ 'b', 'c', '2', 't', 9, 0,
  /* 7088 */ 'b', 'c', '3', 't', 9, 0,
  /* 7094 */ 'w', 'a', 'i', 't', 9, 0,
  /* 7100 */ 's', 'l', 't', 9, 0,
  /* 7105 */ 't', 'l', 't', 9, 0,
  /* 7110 */ 'd', 'm', 'u', 'l', 't', 9, 0,
  /* 7117 */ 'n', 'o', 't', 9, 0,
  /* 7122 */ 'm', 'o', 'v', 't', 9, 0,
  /* 7128 */ 'l', 'b', 'u', 9, 0,
  /* 7133 */ 'd', 's', 'u', 'b', 'u', 9, 0,
  /* 7140 */ 'm', 's', 'u', 'b', 'u', 9, 0,
  /* 7147 */ 'b', 'a', 'd', 'd', 'u', 9, 0,
  /* 7154 */ 'd', 'a', 'd', 'd', 'u', 9, 0,
  /* 7161 */ 'm', 'a', 'd', 'd', 'u', 9, 0,
  /* 7168 */ 'd', 'm', 'o', 'd', 'u', 9, 0,
  /* 7175 */ 't', 'g', 'e', 'u', 9, 0,
  /* 7181 */ 'l', 'h', 'u', 9, 0,
  /* 7186 */ 'd', 'm', 'u', 'h', 'u', 9, 0,
  /* 7193 */ 'd', 'a', 'd', 'd', 'i', 'u', 9, 0,
  /* 7201 */ 't', 'g', 'e', 'i', 'u', 9, 0,
  /* 7208 */ 's', 'l', 't', 'i', 'u', 9, 0,
  /* 7215 */ 't', 'l', 't', 'i', 'u', 9, 0,
  /* 7222 */ 'v', '3', 'm', 'u', 'l', 'u', 9, 0,
  /* 7230 */ 'd', 'm', 'u', 'l', 'u', 9, 0,
  /* 7237 */ 'v', 'm', 'u', 'l', 'u', 9, 0,
  /* 7244 */ 's', 'l', 't', 'u', 9, 0,
  /* 7250 */ 't', 'l', 't', 'u', 9, 0,
  /* 7256 */ 'd', 'm', 'u', 'l', 't', 'u', 9, 0,
  /* 7264 */ 'd', 'd', 'i', 'v', 'u', 9, 0,
  /* 7271 */ 'l', 'w', 'u', 9, 0,
  /* 7276 */ 'a', 'n', 'd', '.', 'v', 9, 0,
  /* 7283 */ 'm', 'o', 'v', 'e', '.', 'v', 9, 0,
  /* 7291 */ 'b', 's', 'e', 'l', '.', 'v', 9, 0,
  /* 7299 */ 'n', 'o', 'r', '.', 'v', 9, 0,
  /* 7306 */ 'x', 'o', 'r', '.', 'v', 9, 0,
  /* 7313 */ 'b', 'z', '.', 'v', 9, 0,
  /* 7319 */ 'b', 'm', 'z', '.', 'v', 9, 0,
  /* 7326 */ 'b', 'n', 'z', '.', 'v', 9, 0,
  /* 7333 */ 'b', 'm', 'n', 'z', '.', 'v', 9, 0,
  /* 7341 */ 'd', 's', 'r', 'a', 'v', 9, 0,
  /* 7348 */ 'b', 'i', 't', 'r', 'e', 'v', 9, 0,
  /* 7356 */ 'd', 'd', 'i', 'v', 9, 0,
  /* 7362 */ 'd', 's', 'l', 'l', 'v', 9, 0,
  /* 7369 */ 'd', 's', 'r', 'l', 'v', 9, 0,
  /* 7376 */ 's', 'h', 'i', 'l', 'o', 'v', 9, 0,
  /* 7384 */ 'e', 'x', 't', 'p', 'd', 'p', 'v', 9, 0,
  /* 7393 */ 'e', 'x', 't', 'p', 'v', 9, 0,
  /* 7400 */ 'd', 'r', 'o', 't', 'r', 'v', 9, 0,
  /* 7408 */ 'i', 'n', 's', 'v', 9, 0,
  /* 7414 */ 'f', 'l', 'o', 'g', '2', '.', 'w', 9, 0,
  /* 7423 */ 'f', 'e', 'x', 'p', '2', '.', 'w', 9, 0,
  /* 7432 */ 'a', 'd', 'd', '_', 'a', '.', 'w', 9, 0,
  /* 7441 */ 'f', 'm', 'i', 'n', '_', 'a', '.', 'w', 9, 0,
  /* 7451 */ 'a', 'd', 'd', 's', '_', 'a', '.', 'w', 9, 0,
  /* 7461 */ 'f', 'm', 'a', 'x', '_', 'a', '.', 'w', 9, 0,
  /* 7471 */ 's', 'r', 'a', '.', 'w', 9, 0,
  /* 7478 */ 'f', 's', 'u', 'b', '.', 'w', 9, 0,
  /* 7486 */ 'f', 'm', 's', 'u', 'b', '.', 'w', 9, 0,
  /* 7495 */ 'n', 'l', 'o', 'c', '.', 'w', 9, 0,
  /* 7503 */ 'n', 'l', 'z', 'c', '.', 'w', 9, 0,
  /* 7511 */ 'c', 'v', 't', '.', 'd', '.', 'w', 9, 0,
  /* 7520 */ 'f', 'a', 'd', 'd', '.', 'w', 9, 0,
  /* 7528 */ 'f', 'm', 'a', 'd', 'd', '.', 'w', 9, 0,
  /* 7537 */ 's', 'l', 'd', '.', 'w', 9, 0,
  /* 7544 */ 'p', 'c', 'k', 'o', 'd', '.', 'w', 9, 0,
  /* 7553 */ 'i', 'l', 'v', 'o', 'd', '.', 'w', 9, 0,
  /* 7562 */ 'f', 'c', 'l', 'e', '.', 'w', 9, 0,
  /* 7570 */ 'f', 's', 'l', 'e', '.', 'w', 9, 0,
  /* 7578 */ 'f', 'c', 'u', 'l', 'e', '.', 'w', 9, 0,
  /* 7587 */ 'f', 's', 'u', 'l', 'e', '.', 'w', 9, 0,
  /* 7596 */ 'f', 'c', 'n', 'e', '.', 'w', 9, 0,
  /* 7604 */ 'f', 's', 'n', 'e', '.', 'w', 9, 0,
  /* 7612 */ 'f', 'c', 'u', 'n', 'e', '.', 'w', 9, 0,
  /* 7621 */ 'f', 's', 'u', 'n', 'e', '.', 'w', 9, 0,
  /* 7630 */ 'i', 'n', 's', 'v', 'e', '.', 'w', 9, 0,
  /* 7639 */ 'f', 'c', 'a', 'f', '.', 'w', 9, 0,
  /* 7647 */ 'f', 's', 'a', 'f', '.', 'w', 9, 0,
  /* 7655 */ 'v', 's', 'h', 'f', '.', 'w', 9, 0,
  /* 7663 */ 'b', 'n', 'e', 'g', '.', 'w', 9, 0,
  /* 7671 */ 'p', 'r', 'e', 'c', 'r', '_', 's', 'r', 'a', '.', 'p', 'h', '.', 'w', 9, 0,
  /* 7687 */ 'p', 'r', 'e', 'c', 'r', 'q', '.', 'p', 'h', '.', 'w', 9, 0,
  /* 7700 */ 'p', 'r', 'e', 'c', 'r', '_', 's', 'r', 'a', '_', 'r', '.', 'p', 'h', '.', 'w', 9, 0,
  /* 7718 */ 'p', 'r', 'e', 'c', 'r', 'q', '_', 'r', 's', '.', 'p', 'h', '.', 'w', 9, 0,
  /* 7734 */ 's', 'u', 'b', 'q', 'h', '.', 'w', 9, 0,
  /* 7743 */ 'a', 'd', 'd', 'q', 'h', '.', 'w', 9, 0,
  /* 7752 */ 's', 'r', 'a', 'i', '.', 'w', 9, 0,
  /* 7760 */ 's', 'l', 'd', 'i', '.', 'w', 9, 0,
  /* 7768 */ 'b', 'n', 'e', 'g', 'i', '.', 'w', 9, 0,
  /* 7777 */ 's', 'l', 'l', 'i', '.', 'w', 9, 0,
  /* 7785 */ 's', 'r', 'l', 'i', '.', 'w', 9, 0,
  /* 7793 */ 'b', 'i', 'n', 's', 'l', 'i', '.', 'w', 9, 0,
  /* 7803 */ 'c', 'e', 'q', 'i', '.', 'w', 9, 0,
  /* 7811 */ 's', 'r', 'a', 'r', 'i', '.', 'w', 9, 0,
  /* 7820 */ 'b', 'c', 'l', 'r', 'i', '.', 'w', 9, 0,
  /* 7829 */ 's', 'r', 'l', 'r', 'i', '.', 'w', 9, 0,
  /* 7838 */ 'b', 'i', 'n', 's', 'r', 'i', '.', 'w', 9, 0,
  /* 7848 */ 's', 'p', 'l', 'a', 't', 'i', '.', 'w', 9, 0,
  /* 7858 */ 'b', 's', 'e', 't', 'i', '.', 'w', 9, 0,
  /* 7867 */ 's', 'u', 'b', 'v', 'i', '.', 'w', 9, 0,
  /* 7876 */ 'a', 'd', 'd', 'v', 'i', '.', 'w', 9, 0,
  /* 7885 */ 'd', 'p', 'a', 'q', '_', 's', 'a', '.', 'l', '.', 'w', 9, 0,
  /* 7898 */ 'd', 'p', 's', 'q', '_', 's', 'a', '.', 'l', '.', 'w', 9, 0,
  /* 7911 */ 'f', 'i', 'l', 'l', '.', 'w', 9, 0,
  /* 7919 */ 's', 'l', 'l', '.', 'w', 9, 0,
  /* 7926 */ 'f', 'e', 'x', 'u', 'p', 'l', '.', 'w', 9, 0,
  /* 7936 */ 'f', 'f', 'q', 'l', '.', 'w', 9, 0,
  /* 7944 */ 's', 'r', 'l', '.', 'w', 9, 0,
  /* 7951 */ 'b', 'i', 'n', 's', 'l', '.', 'w', 9, 0,
  /* 7960 */ 'f', 'm', 'u', 'l', '.', 'w', 9, 0,
  /* 7968 */ 'i', 'l', 'v', 'l', '.', 'w', 9, 0,
  /* 7976 */ 'f', 'm', 'i', 'n', '.', 'w', 9, 0,
  /* 7984 */ 'f', 'c', 'u', 'n', '.', 'w', 9, 0,
  /* 7992 */ 'f', 's', 'u', 'n', '.', 'w', 9, 0,
  /* 8000 */ 'f', 'e', 'x', 'd', 'o', '.', 'w', 9, 0,
  /* 8009 */ 'f', 'r', 'c', 'p', '.', 'w', 9, 0,
  /* 8017 */ 'm', 's', 'u', 'b', '_', 'q', '.', 'w', 9, 0,
  /* 8027 */ 'm', 'a', 'd', 'd', '_', 'q', '.', 'w', 9, 0,
  /* 8037 */ 'm', 'u', 'l', '_', 'q', '.', 'w', 9, 0,
  /* 8046 */ 'm', 's', 'u', 'b', 'r', '_', 'q', '.', 'w', 9, 0,
  /* 8057 */ 'm', 'a', 'd', 'd', 'r', '_', 'q', '.', 'w', 9, 0,
  /* 8068 */ 'm', 'u', 'l', 'r', '_', 'q', '.', 'w', 9, 0,
  /* 8078 */ 'f', 'c', 'e', 'q', '.', 'w', 9, 0,
  /* 8086 */ 'f', 's', 'e', 'q', '.', 'w', 9, 0,
  /* 8094 */ 'f', 'c', 'u', 'e', 'q', '.', 'w', 9, 0,
  /* 8103 */ 'f', 's', 'u', 'e', 'q', '.', 'w', 9, 0,
  /* 8112 */ 'f', 't', 'q', '.', 'w', 9, 0,
  /* 8119 */ 's', 'h', 'r', 'a', '_', 'r', '.', 'w', 9, 0,
  /* 8129 */ 's', 'u', 'b', 'q', 'h', '_', 'r', '.', 'w', 9, 0,
  /* 8140 */ 'a', 'd', 'd', 'q', 'h', '_', 'r', '.', 'w', 9, 0,
  /* 8151 */ 'e', 'x', 't', 'r', '_', 'r', '.', 'w', 9, 0,
  /* 8161 */ 's', 'h', 'r', 'a', 'v', '_', 'r', '.', 'w', 9, 0,
  /* 8172 */ 'e', 'x', 't', 'r', 'v', '_', 'r', '.', 'w', 9, 0,
  /* 8183 */ 's', 'r', 'a', 'r', '.', 'w', 9, 0,
  /* 8191 */ 'b', 'c', 'l', 'r', '.', 'w', 9, 0,
  /* 8199 */ 's', 'r', 'l', 'r', '.', 'w', 9, 0,
  /* 8207 */ 'f', 'c', 'o', 'r', '.', 'w', 9, 0,
  /* 8215 */ 'f', 's', 'o', 'r', '.', 'w', 9, 0,
  /* 8223 */ 'f', 'e', 'x', 'u', 'p', 'r', '.', 'w', 9, 0,
  /* 8233 */ 'f', 'f', 'q', 'r', '.', 'w', 9, 0,
  /* 8241 */ 'b', 'i', 'n', 's', 'r', '.', 'w', 9, 0,
  /* 8250 */ 'e', 'x', 't', 'r', '.', 'w', 9, 0,
  /* 8258 */ 'i', 'l', 'v', 'r', '.', 'w', 9, 0,
  /* 8266 */ 'c', 'v', 't', '.', 's', '.', 'w', 9, 0,
  /* 8275 */ 'a', 's', 'u', 'b', '_', 's', '.', 'w', 9, 0,
  /* 8285 */ 'h', 's', 'u', 'b', '_', 's', '.', 'w', 9, 0,
  /* 8295 */ 'd', 'p', 's', 'u', 'b', '_', 's', '.', 'w', 9, 0,
  /* 8306 */ 'f', 't', 'r', 'u', 'n', 'c', '_', 's', '.', 'w', 9, 0,
  /* 8318 */ 'h', 'a', 'd', 'd', '_', 's', '.', 'w', 9, 0,
  /* 8328 */ 'd', 'p', 'a', 'd', 'd', '_', 's', '.', 'w', 9, 0,
  /* 8339 */ 'm', 'o', 'd', '_', 's', '.', 'w', 9, 0,
  /* 8348 */ 'c', 'l', 'e', '_', 's', '.', 'w', 9, 0,
  /* 8357 */ 'a', 'v', 'e', '_', 's', '.', 'w', 9, 0,
  /* 8366 */ 'c', 'l', 'e', 'i', '_', 's', '.', 'w', 9, 0,
  /* 8376 */ 'm', 'i', 'n', 'i', '_', 's', '.', 'w', 9, 0,
  /* 8386 */ 'c', 'l', 't', 'i', '_', 's', '.', 'w', 9, 0,
  /* 8396 */ 'm', 'a', 'x', 'i', '_', 's', '.', 'w', 9, 0,
  /* 8406 */ 's', 'h', 'l', 'l', '_', 's', '.', 'w', 9, 0,
  /* 8416 */ 'm', 'i', 'n', '_', 's', '.', 'w', 9, 0,
  /* 8425 */ 'd', 'o', 't', 'p', '_', 's', '.', 'w', 9, 0,
  /* 8435 */ 's', 'u', 'b', 'q', '_', 's', '.', 'w', 9, 0,
  /* 8445 */ 'a', 'd', 'd', 'q', '_', 's', '.', 'w', 9, 0,
  /* 8455 */ 'm', 'u', 'l', 'q', '_', 's', '.', 'w', 9, 0,
  /* 8465 */ 'a', 'b', 's', 'q', '_', 's', '.', 'w', 9, 0,
  /* 8475 */ 'a', 'v', 'e', 'r', '_', 's', '.', 'w', 9, 0,
  /* 8485 */ 's', 'u', 'b', 's', '_', 's', '.', 'w', 9, 0,
  /* 8495 */ 'a', 'd', 'd', 's', '_', 's', '.', 'w', 9, 0,
  /* 8505 */ 's', 'a', 't', '_', 's', '.', 'w', 9, 0,
  /* 8514 */ 'c', 'l', 't', '_', 's', '.', 'w', 9, 0,
  /* 8523 */ 'f', 'f', 'i', 'n', 't', '_', 's', '.', 'w', 9, 0,
  /* 8534 */ 'f', 't', 'i', 'n', 't', '_', 's', '.', 'w', 9, 0,
  /* 8545 */ 's', 'u', 'b', 's', 'u', 'u', '_', 's', '.', 'w', 9, 0,
  /* 8557 */ 'd', 'i', 'v', '_', 's', '.', 'w', 9, 0,
  /* 8566 */ 's', 'h', 'l', 'l', 'v', '_', 's', '.', 'w', 9, 0,
  /* 8577 */ 'm', 'a', 'x', '_', 's', '.', 'w', 9, 0,
  /* 8586 */ 'c', 'o', 'p', 'y', '_', 's', '.', 'w', 9, 0,
  /* 8596 */ 'm', 'u', 'l', 'q', '_', 'r', 's', '.', 'w', 9, 0,
  /* 8607 */ 'e', 'x', 't', 'r', '_', 'r', 's', '.', 'w', 9, 0,
  /* 8618 */ 'e', 'x', 't', 'r', 'v', '_', 'r', 's', '.', 'w', 9, 0,
  /* 8630 */ 'f', 'c', 'l', 'a', 's', 's', '.', 'w', 9, 0,
  /* 8640 */ 's', 'p', 'l', 'a', 't', '.', 'w', 9, 0,
  /* 8649 */ 'b', 's', 'e', 't', '.', 'w', 9, 0,
  /* 8657 */ 'f', 'c', 'l', 't', '.', 'w', 9, 0,
  /* 8665 */ 'f', 's', 'l', 't', '.', 'w', 9, 0,
  /* 8673 */ 'f', 'c', 'u', 'l', 't', '.', 'w', 9, 0,
  /* 8682 */ 'f', 's', 'u', 'l', 't', '.', 'w', 9, 0,
  /* 8691 */ 'p', 'c', 'n', 't', '.', 'w', 9, 0,
  /* 8699 */ 'f', 'r', 'i', 'n', 't', '.', 'w', 9, 0,
  /* 8708 */ 'i', 'n', 's', 'e', 'r', 't', '.', 'w', 9, 0,
  /* 8718 */ 'f', 's', 'q', 'r', 't', '.', 'w', 9, 0,
  /* 8727 */ 'f', 'r', 's', 'q', 'r', 't', '.', 'w', 9, 0,
  /* 8737 */ 's', 't', '.', 'w', 9, 0,
  /* 8743 */ 'a', 's', 'u', 'b', '_', 'u', '.', 'w', 9, 0,
  /* 8753 */ 'h', 's', 'u', 'b', '_', 'u', '.', 'w', 9, 0,
  /* 8763 */ 'd', 'p', 's', 'u', 'b', '_', 'u', '.', 'w', 9, 0,
  /* 8774 */ 'f', 't', 'r', 'u', 'n', 'c', '_', 'u', '.', 'w', 9, 0,
  /* 8786 */ 'h', 'a', 'd', 'd', '_', 'u', '.', 'w', 9, 0,
  /* 8796 */ 'd', 'p', 'a', 'd', 'd', '_', 'u', '.', 'w', 9, 0,
  /* 8807 */ 'm', 'o', 'd', '_', 'u', '.', 'w', 9, 0,
  /* 8816 */ 'c', 'l', 'e', '_', 'u', '.', 'w', 9, 0,
  /* 8825 */ 'a', 'v', 'e', '_', 'u', '.', 'w', 9, 0,
  /* 8834 */ 'c', 'l', 'e', 'i', '_', 'u', '.', 'w', 9, 0,
  /* 8844 */ 'm', 'i', 'n', 'i', '_', 'u', '.', 'w', 9, 0,
  /* 8854 */ 'c', 'l', 't', 'i', '_', 'u', '.', 'w', 9, 0,
  /* 8864 */ 'm', 'a', 'x', 'i', '_', 'u', '.', 'w', 9, 0,
  /* 8874 */ 'm', 'i', 'n', '_', 'u', '.', 'w', 9, 0,
  /* 8883 */ 'd', 'o', 't', 'p', '_', 'u', '.', 'w', 9, 0,
  /* 8893 */ 'a', 'v', 'e', 'r', '_', 'u', '.', 'w', 9, 0,
  /* 8903 */ 's', 'u', 'b', 's', '_', 'u', '.', 'w', 9, 0,
  /* 8913 */ 'a', 'd', 'd', 's', '_', 'u', '.', 'w', 9, 0,
  /* 8923 */ 's', 'u', 'b', 's', 'u', 's', '_', 'u', '.', 'w', 9, 0,
  /* 8935 */ 's', 'a', 't', '_', 'u', '.', 'w', 9, 0,
  /* 8944 */ 'c', 'l', 't', '_', 'u', '.', 'w', 9, 0,
  /* 8953 */ 'f', 'f', 'i', 'n', 't', '_', 'u', '.', 'w', 9, 0,
  /* 8964 */ 'f', 't', 'i', 'n', 't', '_', 'u', '.', 'w', 9, 0,
  /* 8975 */ 'd', 'i', 'v', '_', 'u', '.', 'w', 9, 0,
  /* 8984 */ 'm', 'a', 'x', '_', 'u', '.', 'w', 9, 0,
  /* 8993 */ 'c', 'o', 'p', 'y', '_', 'u', '.', 'w', 9, 0,
  /* 9003 */ 'm', 's', 'u', 'b', 'v', '.', 'w', 9, 0,
  /* 9012 */ 'm', 'a', 'd', 'd', 'v', '.', 'w', 9, 0,
  /* 9021 */ 'p', 'c', 'k', 'e', 'v', '.', 'w', 9, 0,
  /* 9030 */ 'i', 'l', 'v', 'e', 'v', '.', 'w', 9, 0,
  /* 9039 */ 'f', 'd', 'i', 'v', '.', 'w', 9, 0,
  /* 9047 */ 'm', 'u', 'l', 'v', '.', 'w', 9, 0,
  /* 9055 */ 'e', 'x', 't', 'r', 'v', '.', 'w', 9, 0,
  /* 9064 */ 'f', 'm', 'a', 'x', '.', 'w', 9, 0,
  /* 9072 */ 'b', 'z', '.', 'w', 9, 0,
  /* 9078 */ 'b', 'n', 'z', '.', 'w', 9, 0,
  /* 9085 */ 'l', 'w', 9, 0,
  /* 9089 */ 's', 'w', 9, 0,
  /* 9093 */ 'l', 'h', 'x', 9, 0,
  /* 9098 */ 'j', 'a', 'l', 'x', 9, 0,
  /* 9104 */ 'l', 'b', 'u', 'x', 9, 0,
  /* 9110 */ 'l', 'w', 'x', 9, 0,
  /* 9115 */ 'b', 'g', 'e', 'z', 9, 0,
  /* 9121 */ 'b', 'l', 'e', 'z', 9, 0,
  /* 9127 */ 'b', 'n', 'e', 'z', 9, 0,
  /* 9133 */ 's', 'e', 'l', 'n', 'e', 'z', 9, 0,
  /* 9141 */ 'b', 't', 'n', 'e', 'z', 9, 0,
  /* 9148 */ 'd', 'c', 'l', 'z', 9, 0,
  /* 9154 */ 'b', 'e', 'q', 'z', 9, 0,
  /* 9160 */ 's', 'e', 'l', 'e', 'q', 'z', 9, 0,
  /* 9168 */ 'b', 't', 'e', 'q', 'z', 9, 0,
  /* 9175 */ 'b', 'g', 't', 'z', 9, 0,
  /* 9181 */ 'b', 'l', 't', 'z', 9, 0,
  /* 9187 */ 'm', 'o', 'v', 'z', 9, 0,
  /* 9193 */ 's', 'e', 'b', 9, 32, 0,
  /* 9199 */ 'j', 'r', 'c', 9, 32, 0,
  /* 9205 */ 's', 'e', 'h', 9, 32, 0,
  /* 9211 */ 'd', 'd', 'i', 'v', 'u', 9, '$', 'z', 'e', 'r', 'o', ',', 32, 0,
  /* 9225 */ 'd', 'd', 'i', 'v', 9, '$', 'z', 'e', 'r', 'o', ',', 32, 0,
  /* 9238 */ 'a', 'd', 'd', 'i', 'u', 9, '$', 's', 'p', ',', 32, 0,
  /* 9250 */ 'c', 'i', 'n', 's', '3', '2', 32, 0,
  /* 9258 */ 'e', 'x', 't', 's', '3', '2', 32, 0,
  /* 9266 */ 's', 'y', 'n', 'c', 32, 0,
  /* 9272 */ 9, '.', 'w', 'o', 'r', 'd', 32, 0,
  /* 9280 */ 'd', 'i', 'n', 's', 'm', 32, 0,
  /* 9287 */ 'd', 'e', 'x', 't', 'm', 32, 0,
  /* 9294 */ 'c', 'i', 'n', 's', 32, 0,
  /* 9300 */ 'd', 'i', 'n', 's', 32, 0,
  /* 9306 */ 'e', 'x', 't', 's', 32, 0,
  /* 9312 */ 'd', 'e', 'x', 't', 32, 0,
  /* 9318 */ 'd', 'i', 'n', 's', 'u', 32, 0,
  /* 9325 */ 'd', 'e', 'x', 't', 'u', 32, 0,
  /* 9332 */ 'b', 'c', '1', 'n', 'e', 'z', 32, 0,
  /* 9340 */ 'b', 'c', '2', 'n', 'e', 'z', 32, 0,
  /* 9348 */ 'b', 'c', '1', 'e', 'q', 'z', 32, 0,
  /* 9356 */ 'b', 'c', '2', 'e', 'q', 'z', 32, 0,
  /* 9364 */ 'c', '.', 0,
  /* 9367 */ 'b', 'r', 'e', 'a', 'k', 32, '0', 0,
  /* 9375 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'E', 'N', 'D', 0,
  /* 9388 */ 'B', 'U', 'N', 'D', 'L', 'E', 0,
  /* 9395 */ 'D', 'B', 'G', '_', 'V', 'A', 'L', 'U', 'E', 0,
  /* 9405 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'S', 'T', 'A', 'R', 'T', 0,
  /* 9420 */ 'j', 'r', 'c', 9, 32, '$', 'r', 'a', 0,
  /* 9429 */ 'j', 'r', 9, 32, '$', 'r', 'a', 0,
  /* 9437 */ 'e', 'h', 'b', 0,
  /* 9441 */ 'p', 'a', 'u', 's', 'e', 0,
  /* 9447 */ 't', 'l', 'b', 'w', 'i', 0,
  /* 9453 */ 'f', 'o', 'o', 0,
  /* 9457 */ 't', 'l', 'b', 'p', 0,
  /* 9462 */ 's', 's', 'n', 'o', 'p', 0,
  /* 9468 */ 't', 'l', 'b', 'r', 0,
  /* 9473 */ 't', 'l', 'b', 'w', 'r', 0,
  /* 9479 */ 'd', 'e', 'r', 'e', 't', 0,
  /* 9485 */ 'w', 'a', 'i', 't', 0,
  };
#endif

  // Emit the opcode for the instruction.
  uint64_t Bits1 = OpInfo[MCInst_getOpcode(MI)];
  uint64_t Bits2 = OpInfo2[MCInst_getOpcode(MI)];
  uint64_t Bits = (Bits2 << 32) | Bits1;
  // assert(Bits != 0 && "Cannot print this instruction.");
#ifndef CAPSTONE_DIET
  SStream_concat0(O, AsmStrs+(Bits & 16383)-1);
#endif


  // Fragment 0 encoded into 4 bits for 11 unique commands.
  //printf("Frag-0: %"PRIu64"\n", (Bits >> 14) & 15);
  switch ((Bits >> 14) & 15) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // DBG_VALUE, BUNDLE, LIFETIME_START, LIFETIME_END, Break16, CONSTPOOL_EN...
    return;
    break;
  case 1:
    // ABSQ_S_PH, ABSQ_S_QB, ABSQ_S_W, ADD, ADDIUPC, ADDIUPC_MM, ADDIUR1SP_MM...
    printOperand(MI, 0, O); 
    break;
  case 2:
    // ADDIUS5_MM, CTC1, CTC1_MM, DAHI, DATI, DMTC1, MTC1, MTC1_MM, MTHI_DSP,...
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    break;
  case 3:
    // AND16_MM, MTHC1_D32, MTHC1_D64, MTHC1_MM, OR16_MM, XOR16_MM
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 4:
    // BREAK16_MM, SDBBP16_MM
    printUnsignedImm8(MI, 0, O); 
    return;
    break;
  case 5:
    // CACHE, CACHE_MM, CACHE_R6, PREF, PREF_MM, PREF_R6
    printUnsignedImm(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printMemOperand(MI, 0, O); 
    return;
    break;
  case 6:
    // FCMP_D32, FCMP_D32_MM, FCMP_D64, FCMP_S32, FCMP_S32_MM
    printFCCOperand(MI, 2, O); 
    break;
  case 7:
    // LWM16_MM, LWM32_MM, LWM_MM, MOVEP_MM, SWM16_MM, SWM32_MM, SWM_MM
    printRegisterList(MI, 0, O); 
    SStream_concat0(O, ", "); 
    break;
  case 8:
    // LWP_MM, SWP_MM
    printRegisterPair(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printMemOperand(MI, 2, O); 
    return;
    break;
  case 9:
    // SYNCI
    printMemOperand(MI, 0, O); 
    return;
    break;
  case 10:
    // SelBeqZ, SelBneZ, SelTBteqZCmp, SelTBteqZCmpi, SelTBteqZSlt, SelTBteqZ...
    printOperand(MI, 3, O); 
    break;
  }


  // Fragment 1 encoded into 5 bits for 17 unique commands.
  //printf("Frag-1: %"PRIu64"\n", (Bits >> 18) & 31);
  switch ((Bits >> 18) & 31) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // ABSQ_S_PH, ABSQ_S_QB, ABSQ_S_W, ADD, ADDIUPC, ADDIUPC_MM, ADDIUR1SP_MM...
    SStream_concat0(O, ", "); 
    break;
  case 1:
    // ADDIUS5_MM, DAHI, DATI, MOVEP_MM, MultRxRyRz16, MultuRxRyRz16, SltCCRx...
    printOperand(MI, 2, O); 
    break;
  case 2:
    // ADDIUSP_MM, AddiuSpImmX16, B16_MM, BAL, BALC, BC, BPOSGE32, B_MM_Pseud...
    return;
    break;
  case 3:
    // AND16_MM, OR16_MM, XOR16_MM
    printOperand(MI, 1, O); 
    return;
    break;
  case 4:
    // AddiuRxPcImmX16
    SStream_concat0(O, ", $pc, "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 5:
    // AddiuSpImm16, Bimm16
    SStream_concat0(O, " # 16 bit inst"); 
    return;
    break;
  case 6:
    // Bteqz16, Btnez16
    SStream_concat0(O, "  # 16 bit inst"); 
    return;
    break;
  case 7:
    // CTC1, CTC1_MM, DMTC1, MTC1, MTC1_MM, MTHC1_D32, MTHC1_D64, MTHC1_MM, M...
    printOperand(MI, 0, O); 
    return;
    break;
  case 8:
    // FCMP_D32, FCMP_D32_MM, FCMP_D64
    SStream_concat0(O, ".d\t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 9:
    // FCMP_S32, FCMP_S32_MM
    SStream_concat0(O, ".s\t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 10:
    // INSERT_B, INSERT_D, INSERT_H, INSERT_W, INSVE_B, INSVE_D, INSVE_H, INS...
    SStream_concat0(O, "["); 
    break;
  case 11:
    // Jal16
    SStream_concat0(O, "\n\tnop"); 
    return;
    break;
  case 12:
    // JalB16
    SStream_concat0(O, "\t# branch\n\tnop"); 
    return;
    break;
  case 13:
    // LWM16_MM, LWM32_MM, LWM_MM, SWM16_MM, SWM32_MM, SWM_MM
    printMemOperand(MI, 1, O); 
    return;
    break;
  case 14:
    // LwConstant32
    SStream_concat0(O, ", 1f\n\tb\t2f\n\t.align\t2\n1: \t.word\t"); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, "\n2:"); 
    return;
    break;
  case 15:
    // SC, SCD, SCD_R6, SC_MM, SC_R6
    printMemOperand(MI, 2, O); 
    return;
    break;
  case 16:
    // SelBeqZ, SelBneZ
    SStream_concat0(O, ", .+4\n\t\n\tmove "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    return;
    break;
  }


  // Fragment 2 encoded into 4 bits for 12 unique commands.
  //printf("Frag-2: %"PRIu64"\n", (Bits >> 23) & 15);
  switch ((Bits >> 23) & 15) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // ABSQ_S_PH, ABSQ_S_QB, ABSQ_S_W, ADD, ADDIUPC, ADDIUPC_MM, ADDIUR1SP_MM...
    printOperand(MI, 1, O); 
    break;
  case 1:
    // ADDIUS5_MM, DAHI, DATI
    return;
    break;
  case 2:
    // AddiuRxRxImm16, AddiuRxRxImmX16, AndRxRxRy16, BINSLI_B, BINSLI_D, BINS...
    printOperand(MI, 2, O); 
    break;
  case 3:
    // AddiuRxRyOffMemX16, LEA_ADDiu, LEA_ADDiu64, LEA_ADDiu_MM
    printMemOperandEA(MI, 1, O); 
    return;
    break;
  case 4:
    // BBIT0, BBIT032, BBIT1, BBIT132, LUi, LUi64, LUi_MM, LoadAddr32Imm, Loa...
    printUnsignedImm(MI, 1, O); 
    break;
  case 5:
    // INSERT_B, INSERT_D, INSERT_H, INSERT_W
    printUnsignedImm(MI, 3, O); 
    SStream_concat0(O, "], "); 
    printOperand(MI, 2, O); 
    return;
    break;
  case 6:
    // INSVE_B, INSVE_D, INSVE_H, INSVE_W
    printUnsignedImm(MI, 2, O); 
    SStream_concat0(O, "], "); 
    printOperand(MI, 3, O); 
    SStream_concat0(O, "["); 
    printUnsignedImm(MI, 4, O); 
    SStream_concat0(O, "]"); 
    return;
    break;
  case 7:
    // LB, LB64, LBU16_MM, LB_MM, LBu, LBu64, LBu_MM, LD, LDC1, LDC164, LDC1_...
    printMemOperand(MI, 1, O); 
    return;
    break;
  case 8:
    // MOVEP_MM
    SStream_concat0(O, ", "); 
    printOperand(MI, 3, O); 
    return;
    break;
  case 9:
    // MultRxRyRz16, MultuRxRyRz16
    SStream_concat0(O, "\n\tmflo\t"); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 10:
    // SelTBteqZCmp, SelTBteqZCmpi, SelTBteqZSlt, SelTBteqZSlti, SelTBteqZSlt...
    printOperand(MI, 4, O); 
    break;
  case 11:
    // SltCCRxRy16, SltiCCRxImmX16, SltiuCCRxImmX16, SltuCCRxRy16, SltuRxRyRz...
    SStream_concat0(O, "\n\tmove\t"); 
    printOperand(MI, 0, O); 
    SStream_concat0(O, ", $t8"); 
    return;
    break;
  }


  // Fragment 3 encoded into 4 bits for 15 unique commands.
  //printf("Frag-3: %"PRIu64"\n", (Bits >> 27) & 15);
  switch ((Bits >> 27) & 15) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // ABSQ_S_PH, ABSQ_S_QB, ABSQ_S_W, ADDIUPC, ADDIUPC_MM, ADDIUR1SP_MM, ALU...
    return;
    break;
  case 1:
    // ADD, ADDIUR2_MM, ADDQH_PH, ADDQH_R_PH, ADDQH_R_W, ADDQH_W, ADDQ_PH, AD...
    SStream_concat0(O, ", "); 
    break;
  case 2:
    // AddiuRxRxImm16, LwRxPcTcp16
    SStream_concat0(O, "\t# 16 bit inst"); 
    return;
    break;
  case 3:
    // BeqzRxImm16, BnezRxImm16
    SStream_concat0(O, "  # 16 bit inst"); 
    return;
    break;
  case 4:
    // BteqzT8CmpX16, BteqzT8CmpiX16, BteqzT8SltX16, BteqzT8SltiX16, BteqzT8S...
    SStream_concat0(O, "\n\tbteqz\t"); 
    printOperand(MI, 2, O); 
    return;
    break;
  case 5:
    // BtnezT8CmpX16, BtnezT8CmpiX16, BtnezT8SltX16, BtnezT8SltiX16, BtnezT8S...
    SStream_concat0(O, "\n\tbtnez\t"); 
    printOperand(MI, 2, O); 
    return;
    break;
  case 6:
    // COPY_S_B, COPY_S_D, COPY_S_H, COPY_S_W, COPY_U_B, COPY_U_D, COPY_U_H, ...
    SStream_concat0(O, "["); 
    break;
  case 7:
    // CmpiRxImm16, LiRxImm16, SltiRxImm16, SltiuRxImm16
    SStream_concat0(O, " \t# 16 bit inst"); 
    return;
    break;
  case 8:
    // DSLL64_32
    SStream_concat0(O, ", 32"); 
    return;
    break;
  case 9:
    // GotPrologue16
    SStream_concat0(O, "\n\taddiu\t"); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", $pc, "); 
    printOperand(MI, 3, O); 
    SStream_concat0(O, "\n "); 
    return;
    break;
  case 10:
    // LBUX, LDXC1, LDXC164, LHX, LUXC1, LUXC164, LUXC1_MM, LWX, LWXC1, LWXC1...
    SStream_concat0(O, "("); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ")"); 
    return;
    break;
  case 11:
    // LwRxSpImmX16, SwRxSpImmX16
    SStream_concat0(O, " ( "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, " ); "); 
    return;
    break;
  case 12:
    // SLL64_32, SLL64_64
    SStream_concat0(O, ", 0"); 
    return;
    break;
  case 13:
    // SelTBteqZCmp, SelTBteqZCmpi, SelTBteqZSlt, SelTBteqZSlti, SelTBteqZSlt...
    SStream_concat0(O, "\n\tbteqz\t.+4\n\tmove "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    return;
    break;
  case 14:
    // SelTBtneZCmp, SelTBtneZCmpi, SelTBtneZSlt, SelTBtneZSlti, SelTBtneZSlt...
    SStream_concat0(O, "\n\tbtnez\t.+4\n\tmove "); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    return;
    break;
  }


  // Fragment 4 encoded into 3 bits for 5 unique commands.
  //printf("Frag-4: %"PRIu64"\n", (Bits >> 31) & 7);
  switch ((Bits >> 31) & 7) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // ADD, ADDIUR2_MM, ADDQH_PH, ADDQH_R_PH, ADDQH_R_W, ADDQH_W, ADDQ_PH, AD...
    printOperand(MI, 2, O); 
    break;
  case 1:
    // ADDVI_B, ADDVI_D, ADDVI_H, ADDVI_W, ANDI_B, BCLRI_B, BCLRI_D, BCLRI_H,...
    printUnsignedImm8(MI, 2, O); 
    break;
  case 2:
    // ANDi, ANDi64, ANDi_MM, APPEND, BALIGN, CINS, CINS32, DEXT, DEXTM, DEXT...
    printUnsignedImm(MI, 2, O); 
    break;
  case 3:
    // BINSLI_B, BINSLI_D, BINSLI_H, BINSLI_W, BINSRI_B, BINSRI_D, BINSRI_H, ...
    printUnsignedImm8(MI, 3, O); 
    break;
  case 4:
    // BINSL_B, BINSL_D, BINSL_H, BINSL_W, BINSR_B, BINSR_D, BINSR_H, BINSR_W...
    printOperand(MI, 3, O); 
    break;
  }


  // Fragment 5 encoded into 2 bits for 3 unique commands.
  //printf("Frag-5: %"PRIu64"\n", (Bits >> 34) & 3);
  switch ((Bits >> 34) & 3) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // ADD, ADDIUR2_MM, ADDQH_PH, ADDQH_R_PH, ADDQH_R_W, ADDQH_W, ADDQ_PH, AD...
    return;
    break;
  case 1:
    // ALIGN, CINS, CINS32, DALIGN, DEXT, DEXTM, DEXTU, DINS, DINSM, DINSU, D...
    SStream_concat0(O, ", "); 
    break;
  case 2:
    // COPY_S_B, COPY_S_D, COPY_S_H, COPY_S_W, COPY_U_B, COPY_U_D, COPY_U_H, ...
    SStream_concat0(O, "]"); 
    return;
    break;
  }


  // Fragment 6 encoded into 1 bits for 2 unique commands.
  //printf("Frag-6: %"PRIu64"\n", (Bits >> 36) & 1);
  if ((Bits >> 36) & 1) {
    // DEXT, DEXTM, DEXTU, DINS, DINSM, DINSU, EXT, EXT_MM, INS, INS_MM, MADD...
    printOperand(MI, 3, O); 
    return;
  } else {
    // ALIGN, CINS, CINS32, DALIGN, DLSA, DLSA_R6, EXTS, EXTS32, LSA, LSA_R6
    printUnsignedImm(MI, 3, O); 
    return;
  }
}


/// getRegisterName - This method is automatically generated by tblgen
/// from the register set description.  This returns the assembler name
/// for the specified register.
static const char *getRegisterName(unsigned RegNo)
{
  // assert(RegNo && RegNo < 394 && "Invalid register number!");

#ifndef CAPSTONE_DIET
  static const char AsmStrs[] = {
  /* 0 */ 'f', '1', '0', 0,
  /* 4 */ 'w', '1', '0', 0,
  /* 8 */ 'f', '2', '0', 0,
  /* 12 */ 'D', 'S', 'P', 'O', 'u', 't', 'F', 'l', 'a', 'g', '2', '0', 0,
  /* 25 */ 'w', '2', '0', 0,
  /* 29 */ 'f', '3', '0', 0,
  /* 33 */ 'w', '3', '0', 0,
  /* 37 */ 'a', '0', 0,
  /* 40 */ 'a', 'c', '0', 0,
  /* 44 */ 'f', 'c', 'c', '0', 0,
  /* 49 */ 'f', '0', 0,
  /* 52 */ 'k', '0', 0,
  /* 55 */ 'm', 'p', 'l', '0', 0,
  /* 60 */ 'p', '0', 0,
  /* 63 */ 's', '0', 0,
  /* 66 */ 't', '0', 0,
  /* 69 */ 'v', '0', 0,
  /* 72 */ 'w', '0', 0,
  /* 75 */ 'f', '1', '1', 0,
  /* 79 */ 'w', '1', '1', 0,
  /* 83 */ 'f', '2', '1', 0,
  /* 87 */ 'D', 'S', 'P', 'O', 'u', 't', 'F', 'l', 'a', 'g', '2', '1', 0,
  /* 100 */ 'w', '2', '1', 0,
  /* 104 */ 'f', '3', '1', 0,
  /* 108 */ 'w', '3', '1', 0,
  /* 112 */ 'a', '1', 0,
  /* 115 */ 'a', 'c', '1', 0,
  /* 119 */ 'f', 'c', 'c', '1', 0,
  /* 124 */ 'f', '1', 0,
  /* 127 */ 'k', '1', 0,
  /* 130 */ 'm', 'p', 'l', '1', 0,
  /* 135 */ 'p', '1', 0,
  /* 138 */ 's', '1', 0,
  /* 141 */ 't', '1', 0,
  /* 144 */ 'v', '1', 0,
  /* 147 */ 'w', '1', 0,
  /* 150 */ 'f', '1', '2', 0,
  /* 154 */ 'w', '1', '2', 0,
  /* 158 */ 'f', '2', '2', 0,
  /* 162 */ 'D', 'S', 'P', 'O', 'u', 't', 'F', 'l', 'a', 'g', '2', '2', 0,
  /* 175 */ 'w', '2', '2', 0,
  /* 179 */ 'a', '2', 0,
  /* 182 */ 'a', 'c', '2', 0,
  /* 186 */ 'f', 'c', 'c', '2', 0,
  /* 191 */ 'f', '2', 0,
  /* 194 */ 'm', 'p', 'l', '2', 0,
  /* 199 */ 'p', '2', 0,
  /* 202 */ 's', '2', 0,
  /* 205 */ 't', '2', 0,
  /* 208 */ 'w', '2', 0,
  /* 211 */ 'f', '1', '3', 0,
  /* 215 */ 'w', '1', '3', 0,
  /* 219 */ 'f', '2', '3', 0,
  /* 223 */ 'D', 'S', 'P', 'O', 'u', 't', 'F', 'l', 'a', 'g', '2', '3', 0,
  /* 236 */ 'w', '2', '3', 0,
  /* 240 */ 'a', '3', 0,
  /* 243 */ 'a', 'c', '3', 0,
  /* 247 */ 'f', 'c', 'c', '3', 0,
  /* 252 */ 'f', '3', 0,
  /* 255 */ 's', '3', 0,
  /* 258 */ 't', '3', 0,
  /* 261 */ 'w', '3', 0,
  /* 264 */ 'f', '1', '4', 0,
  /* 268 */ 'w', '1', '4', 0,
  /* 272 */ 'f', '2', '4', 0,
  /* 276 */ 'w', '2', '4', 0,
  /* 280 */ 'f', 'c', 'c', '4', 0,
  /* 285 */ 'f', '4', 0,
  /* 288 */ 's', '4', 0,
  /* 291 */ 't', '4', 0,
  /* 294 */ 'w', '4', 0,
  /* 297 */ 'f', '1', '5', 0,
  /* 301 */ 'w', '1', '5', 0,
  /* 305 */ 'f', '2', '5', 0,
  /* 309 */ 'w', '2', '5', 0,
  /* 313 */ 'f', 'c', 'c', '5', 0,
  /* 318 */ 'f', '5', 0,
  /* 321 */ 's', '5', 0,
  /* 324 */ 't', '5', 0,
  /* 327 */ 'w', '5', 0,
  /* 330 */ 'f', '1', '6', 0,
  /* 334 */ 'w', '1', '6', 0,
  /* 338 */ 'f', '2', '6', 0,
  /* 342 */ 'w', '2', '6', 0,
  /* 346 */ 'f', 'c', 'c', '6', 0,
  /* 351 */ 'f', '6', 0,
  /* 354 */ 's', '6', 0,
  /* 357 */ 't', '6', 0,
  /* 360 */ 'w', '6', 0,
  /* 363 */ 'f', '1', '7', 0,
  /* 367 */ 'w', '1', '7', 0,
  /* 371 */ 'f', '2', '7', 0,
  /* 375 */ 'w', '2', '7', 0,
  /* 379 */ 'f', 'c', 'c', '7', 0,
  /* 384 */ 'f', '7', 0,
  /* 387 */ 's', '7', 0,
  /* 390 */ 't', '7', 0,
  /* 393 */ 'w', '7', 0,
  /* 396 */ 'f', '1', '8', 0,
  /* 400 */ 'w', '1', '8', 0,
  /* 404 */ 'f', '2', '8', 0,
  /* 408 */ 'w', '2', '8', 0,
  /* 412 */ 'f', '8', 0,
  /* 415 */ 't', '8', 0,
  /* 418 */ 'w', '8', 0,
  /* 421 */ 'D', 'S', 'P', 'O', 'u', 't', 'F', 'l', 'a', 'g', '1', '6', '_', '1', '9', 0,
  /* 437 */ 'f', '1', '9', 0,
  /* 441 */ 'w', '1', '9', 0,
  /* 445 */ 'f', '2', '9', 0,
  /* 449 */ 'w', '2', '9', 0,
  /* 453 */ 'f', '9', 0,
  /* 456 */ 't', '9', 0,
  /* 459 */ 'w', '9', 0,
  /* 462 */ 'D', 'S', 'P', 'E', 'F', 'I', 0,
  /* 469 */ 'r', 'a', 0,
  /* 472 */ 'h', 'w', 'r', '_', 'c', 'c', 0,
  /* 479 */ 'p', 'c', 0,
  /* 482 */ 'D', 'S', 'P', 'C', 'C', 'o', 'n', 'd', 0,
  /* 491 */ 'D', 'S', 'P', 'O', 'u', 't', 'F', 'l', 'a', 'g', 0,
  /* 502 */ 'h', 'i', 0,
  /* 505 */ 'h', 'w', 'r', '_', 'c', 'p', 'u', 'n', 'u', 'm', 0,
  /* 516 */ 'l', 'o', 0,
  /* 519 */ 'z', 'e', 'r', 'o', 0,
  /* 524 */ 'h', 'w', 'r', '_', 's', 'y', 'n', 'c', 'i', '_', 's', 't', 'e', 'p', 0,
  /* 539 */ 'f', 'p', 0,
  /* 542 */ 'g', 'p', 0,
  /* 545 */ 's', 'p', 0,
  /* 548 */ 'h', 'w', 'r', '_', 'c', 'c', 'r', 'e', 's', 0,
  /* 558 */ 'D', 'S', 'P', 'P', 'o', 's', 0,
  /* 565 */ 'a', 't', 0,
  /* 568 */ 'D', 'S', 'P', 'S', 'C', 'o', 'u', 'n', 't', 0,
  /* 578 */ 'D', 'S', 'P', 'C', 'a', 'r', 'r', 'y', 0,
  };

  static const uint16_t RegAsmOffset[] = {
    565, 482, 578, 462, 491, 558, 568, 539, 542, 152, 77, 2, 332, 266, 
    299, 213, 365, 479, 469, 545, 519, 37, 112, 179, 240, 40, 115, 182, 
    243, 565, 45, 120, 187, 248, 281, 314, 347, 380, 2, 77, 152, 213, 
    266, 299, 332, 365, 398, 435, 2, 77, 152, 213, 266, 299, 332, 365, 
    398, 435, 1, 76, 151, 212, 265, 298, 331, 364, 397, 434, 9, 84, 
    159, 220, 273, 306, 339, 372, 405, 446, 30, 105, 1, 76, 151, 212, 
    265, 298, 331, 364, 397, 434, 9, 84, 159, 220, 273, 306, 339, 372, 
    405, 446, 30, 105, 49, 191, 285, 351, 412, 0, 150, 264, 330, 396, 
    8, 158, 272, 338, 404, 29, 12, 87, 162, 223, 49, 124, 191, 252, 
    285, 318, 351, 384, 412, 453, 0, 75, 150, 211, 264, 297, 330, 363, 
    396, 437, 8, 83, 158, 219, 272, 305, 338, 371, 404, 445, 29, 104, 
    44, 119, 186, 247, 280, 313, 346, 379, 2, 77, 152, 213, 266, 299, 
    332, 365, 398, 435, 1, 76, 151, 212, 265, 298, 331, 364, 397, 434, 
    9, 84, 159, 220, 273, 306, 339, 372, 405, 446, 30, 105, 539, 49, 
    124, 191, 252, 285, 318, 351, 384, 412, 453, 0, 75, 150, 211, 264, 
    297, 330, 363, 396, 437, 8, 83, 158, 219, 272, 305, 338, 371, 404, 
    445, 29, 104, 542, 40, 115, 182, 243, 505, 524, 472, 548, 266, 299, 
    332, 365, 398, 435, 1, 76, 151, 212, 265, 298, 331, 364, 397, 434, 
    9, 84, 159, 220, 273, 306, 339, 372, 405, 446, 30, 105, 52, 127, 
    40, 115, 182, 243, 55, 130, 194, 60, 135, 199, 469, 63, 138, 202, 
    255, 288, 321, 354, 387, 545, 66, 141, 205, 258, 291, 324, 357, 390, 
    415, 456, 69, 144, 72, 147, 208, 261, 294, 327, 360, 393, 418, 459, 
    4, 79, 154, 215, 268, 301, 334, 367, 400, 441, 25, 100, 175, 236, 
    276, 309, 342, 375, 408, 449, 33, 108, 519, 37, 112, 179, 240, 40, 
    49, 124, 191, 252, 285, 318, 351, 384, 412, 453, 0, 75, 150, 211, 
    264, 297, 330, 363, 396, 437, 8, 83, 158, 219, 272, 305, 338, 371, 
    404, 445, 29, 104, 421, 502, 52, 127, 516, 63, 138, 202, 255, 288, 
    321, 354, 387, 66, 141, 205, 258, 291, 324, 357, 390, 415, 456, 69, 
    144, 
  };

  //printf("==== RegNo = %u, id = %s\n", RegNo, AsmStrs+RegAsmOffset[RegNo-1]);
  //int i;
  //for (i = 0; i < sizeof(RegAsmOffset)/2; i++)
  //     printf("%s = %u\n", AsmStrs+RegAsmOffset[i], i + 1);
  //printf("-------------------------\n");
  return AsmStrs+RegAsmOffset[RegNo-1];
#else
  return NULL;
#endif
}

#ifdef PRINT_ALIAS_INSTR
#undef PRINT_ALIAS_INSTR

static void printCustomAliasOperand(MCInst *MI, unsigned OpIdx,
  unsigned PrintMethodIdx, SStream *OS)
{
}

static char *printAliasInstr(MCInst *MI, SStream *OS, void *info)
{
  #define GETREGCLASS_CONTAIN(_class, _reg) MCRegisterClass_contains(MCRegisterInfo_getRegClass(MRI, _class), MCOperand_getReg(MCInst_getOperand(MI, _reg)))
  const char *AsmString;
  char *tmp, *AsmMnem, *AsmOps, *c;
  int OpIdx, PrintMethodIdx;
  MCRegisterInfo *MRI = (MCRegisterInfo *)info;
  switch (MCInst_getOpcode(MI)) {
  default: return NULL;
  case Mips_ADDu:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == Mips_ZERO) {
      // (ADDu GPR32Opnd:$dst, GPR32Opnd:$src, ZERO)
      AsmString = "move $\x01, $\x02";
      break;
    }
    return NULL;
  case Mips_BC0F:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_CC0) {
      // (BC0F CC0, brtarget:$offset)
      AsmString = "bc0f $\x02";
      break;
    }
    return NULL;
  case Mips_BC0FL:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_CC0) {
      // (BC0FL CC0, brtarget:$offset)
      AsmString = "bc0fl $\x02";
      break;
    }
    return NULL;
  case Mips_BC0T:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_CC0) {
      // (BC0T CC0, brtarget:$offset)
      AsmString = "bc0t $\x02";
      break;
    }
    return NULL;
  case Mips_BC0TL:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_CC0) {
      // (BC0TL CC0, brtarget:$offset)
      AsmString = "bc0tl $\x02";
      break;
    }
    return NULL;
  case Mips_BC1F:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_FCC0) {
      // (BC1F FCC0, brtarget:$offset)
      AsmString = "bc1f $\x02";
      break;
    }
    return NULL;
  case Mips_BC1FL:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_FCC0) {
      // (BC1FL FCC0, brtarget:$offset)
      AsmString = "bc1fl $\x02";
      break;
    }
    return NULL;
  case Mips_BC1T:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_FCC0) {
      // (BC1T FCC0, brtarget:$offset)
      AsmString = "bc1t $\x02";
      break;
    }
    return NULL;
  case Mips_BC1TL:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_FCC0) {
      // (BC1TL FCC0, brtarget:$offset)
      AsmString = "bc1tl $\x02";
      break;
    }
    return NULL;
  case Mips_BC2F:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_CC0) {
      // (BC2F CC0, brtarget:$offset)
      AsmString = "bc2f $\x02";
      break;
    }
    return NULL;
  case Mips_BC2FL:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_CC0) {
      // (BC2FL CC0, brtarget:$offset)
      AsmString = "bc2fl $\x02";
      break;
    }
    return NULL;
  case Mips_BC2T:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_CC0) {
      // (BC2T CC0, brtarget:$offset)
      AsmString = "bc2t $\x02";
      break;
    }
    return NULL;
  case Mips_BC2TL:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_CC0) {
      // (BC2TL CC0, brtarget:$offset)
      AsmString = "bc2tl $\x02";
      break;
    }
    return NULL;
  case Mips_BC3F:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_CC0) {
      // (BC3F CC0, brtarget:$offset)
      AsmString = "bc3f $\x02";
      break;
    }
    return NULL;
  case Mips_BC3FL:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_CC0) {
      // (BC3FL CC0, brtarget:$offset)
      AsmString = "bc3fl $\x02";
      break;
    }
    return NULL;
  case Mips_BC3T:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_CC0) {
      // (BC3T CC0, brtarget:$offset)
      AsmString = "bc3t $\x02";
      break;
    }
    return NULL;
  case Mips_BC3TL:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_CC0) {
      // (BC3TL CC0, brtarget:$offset)
      AsmString = "bc3tl $\x02";
      break;
    }
    return NULL;
  case Mips_BREAK:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 0) {
      // (BREAK 0, 0)
      AsmString = "break";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 0) {
      // (BREAK uimm10:$imm, 0)
      AsmString = "break $\x01";
      break;
    }
    return NULL;
  case Mips_DADDu:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(Mips_GPR64RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(Mips_GPR64RegClassID, 1) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == Mips_ZERO_64) {
      // (DADDu GPR64Opnd:$dst, GPR64Opnd:$src, ZERO_64)
      AsmString = "move $\x01, $\x02";
      break;
    }
    return NULL;
  case Mips_DI:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_ZERO) {
      // (DI ZERO)
      AsmString = "di";
      break;
    }
    return NULL;
  case Mips_EI:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_ZERO) {
      // (EI ZERO)
      AsmString = "ei";
      break;
    }
    return NULL;
  case Mips_JALR:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_ZERO &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 1)) {
      // (JALR ZERO, GPR32Opnd:$rs)
      AsmString = "jr $\x02";
      break;
    }
    return NULL;
  case Mips_JALR64:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_ZERO_64 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(Mips_GPR64RegClassID, 1)) {
      // (JALR64 ZERO_64, GPR64Opnd:$rs)
      AsmString = "jr $\x02";
      break;
    }
    return NULL;
  case Mips_JALR_HB:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_RA &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 1)) {
      // (JALR_HB RA, GPR32Opnd:$rs)
      AsmString = "jalr.hb $\x02";
      break;
    }
    return NULL;
  case Mips_MOVE16_MM:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_ZERO &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == Mips_ZERO) {
      // (MOVE16_MM ZERO, ZERO)
      AsmString = "nop";
      break;
    }
    return NULL;
  case Mips_SDBBP:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (SDBBP 0)
      AsmString = "sdbbp";
      break;
    }
    return NULL;
  case Mips_SDBBP_R6:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (SDBBP_R6 0)
      AsmString = "sdbbp";
      break;
    }
    return NULL;
  case Mips_SLL:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_ZERO &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == Mips_ZERO &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (SLL ZERO, ZERO, 0)
      AsmString = "nop";
      break;
    }
    return NULL;
  case Mips_SLL_MM:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == Mips_ZERO &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == Mips_ZERO &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (SLL_MM ZERO, ZERO, 0)
      AsmString = "nop";
      break;
    }
    return NULL;
  case Mips_SUB:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == Mips_ZERO &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 2)) {
      // (SUB GPR32Opnd:$rt, ZERO, GPR32Opnd:$rs)
      AsmString = "neg $\x01, $\x03";
      break;
    }
    return NULL;
  case Mips_SUBu:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 0) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == Mips_ZERO &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 2)) {
      // (SUBu GPR32Opnd:$rt, ZERO, GPR32Opnd:$rs)
      AsmString = "negu $\x01, $\x03";
      break;
    }
    return NULL;
  case Mips_SYNC:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (SYNC 0)
      AsmString = "sync";
      break;
    }
    return NULL;
  case Mips_SYSCALL:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (SYSCALL 0)
      AsmString = "syscall";
      break;
    }
    return NULL;
  case Mips_TEQ:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (TEQ GPR32Opnd:$rs, GPR32Opnd:$rt, 0)
      AsmString = "teq $\x01, $\x02";
      break;
    }
    return NULL;
  case Mips_TGE:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (TGE GPR32Opnd:$rs, GPR32Opnd:$rt, 0)
      AsmString = "tge $\x01, $\x02";
      break;
    }
    return NULL;
  case Mips_TGEU:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (TGEU GPR32Opnd:$rs, GPR32Opnd:$rt, 0)
      AsmString = "tgeu $\x01, $\x02";
      break;
    }
    return NULL;
  case Mips_TLT:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (TLT GPR32Opnd:$rs, GPR32Opnd:$rt, 0)
      AsmString = "tlt $\x01, $\x02";
      break;
    }
    return NULL;
  case Mips_TLTU:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (TLTU GPR32Opnd:$rs, GPR32Opnd:$rt, 0)
      AsmString = "tltu $\x01, $\x02";
      break;
    }
    return NULL;
  case Mips_TNE:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(Mips_GPR32RegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (TNE GPR32Opnd:$rs, GPR32Opnd:$rt, 0)
      AsmString = "tne $\x01, $\x02";
      break;
    }
    return NULL;
  case Mips_WAIT_MM:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (WAIT_MM 0)
      AsmString = "wait";
      break;
    }
    return NULL;
  }

  tmp = cs_strdup(AsmString);
  AsmMnem = tmp;
  for(AsmOps = tmp; *AsmOps; AsmOps++) {
    if (*AsmOps == ' ' || *AsmOps == '\t') {
      *AsmOps = '\0';
      AsmOps++;
      break;
    }
  }
  SStream_concat0(OS, AsmMnem);
  if (*AsmOps) {
    SStream_concat0(OS, "\t");
    for (c = AsmOps; *c; c++) {
      if (*c == '$') {
        c += 1;
        if (*c == (char)0xff) {
          c += 1;
          OpIdx = *c - 1;
          c += 1;
          PrintMethodIdx = *c - 1;
          printCustomAliasOperand(MI, OpIdx, PrintMethodIdx, OS);
        } else
          printOperand(MI, *c - 1, OS);
      } else {
        SStream_concat(OS, "%c", *c);
      }
    }
  }
  return tmp;
}

#endif // PRINT_ALIAS_INSTR
