// For Capstone Engine. AUTO-GENERATED FILE, DO NOT EDIT
package capstone;

public class TMS320C64x_const {

	public static final int TMS320C64X_OP_INVALID = 0;
	public static final int TMS320C64X_OP_REG = 1;
	public static final int TMS320C64X_OP_IMM = 2;
	public static final int TMS320C64X_OP_MEM = 3;
	public static final int TMS320C64X_OP_REGPAIR = 64;

	public static final int TMS320C64X_MEM_DISP_INVALID = 0;
	public static final int TMS320C64X_MEM_DISP_CONSTANT = 1;
	public static final int TMS320C64X_MEM_DISP_REGISTER = 2;

	public static final int TMS320C64X_MEM_DIR_INVALID = 0;
	public static final int TMS320C64X_MEM_DIR_FW = 1;
	public static final int TMS320C64X_MEM_DIR_BW = 2;

	public static final int TMS320C64X_MEM_MOD_INVALID = 0;
	public static final int TMS320C64X_MEM_MOD_NO = 1;
	public static final int TMS320C64X_MEM_MOD_PRE = 2;
	public static final int TMS320C64X_MEM_MOD_POST = 3;

	public static final int TMS320C64X_REG_INVALID = 0;
	public static final int TMS320C64X_REG_AMR = 1;
	public static final int TMS320C64X_REG_CSR = 2;
	public static final int TMS320C64X_REG_DIER = 3;
	public static final int TMS320C64X_REG_DNUM = 4;
	public static final int TMS320C64X_REG_ECR = 5;
	public static final int TMS320C64X_REG_GFPGFR = 6;
	public static final int TMS320C64X_REG_GPLYA = 7;
	public static final int TMS320C64X_REG_GPLYB = 8;
	public static final int TMS320C64X_REG_ICR = 9;
	public static final int TMS320C64X_REG_IER = 10;
	public static final int TMS320C64X_REG_IERR = 11;
	public static final int TMS320C64X_REG_ILC = 12;
	public static final int TMS320C64X_REG_IRP = 13;
	public static final int TMS320C64X_REG_ISR = 14;
	public static final int TMS320C64X_REG_ISTP = 15;
	public static final int TMS320C64X_REG_ITSR = 16;
	public static final int TMS320C64X_REG_NRP = 17;
	public static final int TMS320C64X_REG_NTSR = 18;
	public static final int TMS320C64X_REG_REP = 19;
	public static final int TMS320C64X_REG_RILC = 20;
	public static final int TMS320C64X_REG_SSR = 21;
	public static final int TMS320C64X_REG_TSCH = 22;
	public static final int TMS320C64X_REG_TSCL = 23;
	public static final int TMS320C64X_REG_TSR = 24;
	public static final int TMS320C64X_REG_A0 = 25;
	public static final int TMS320C64X_REG_A1 = 26;
	public static final int TMS320C64X_REG_A2 = 27;
	public static final int TMS320C64X_REG_A3 = 28;
	public static final int TMS320C64X_REG_A4 = 29;
	public static final int TMS320C64X_REG_A5 = 30;
	public static final int TMS320C64X_REG_A6 = 31;
	public static final int TMS320C64X_REG_A7 = 32;
	public static final int TMS320C64X_REG_A8 = 33;
	public static final int TMS320C64X_REG_A9 = 34;
	public static final int TMS320C64X_REG_A10 = 35;
	public static final int TMS320C64X_REG_A11 = 36;
	public static final int TMS320C64X_REG_A12 = 37;
	public static final int TMS320C64X_REG_A13 = 38;
	public static final int TMS320C64X_REG_A14 = 39;
	public static final int TMS320C64X_REG_A15 = 40;
	public static final int TMS320C64X_REG_A16 = 41;
	public static final int TMS320C64X_REG_A17 = 42;
	public static final int TMS320C64X_REG_A18 = 43;
	public static final int TMS320C64X_REG_A19 = 44;
	public static final int TMS320C64X_REG_A20 = 45;
	public static final int TMS320C64X_REG_A21 = 46;
	public static final int TMS320C64X_REG_A22 = 47;
	public static final int TMS320C64X_REG_A23 = 48;
	public static final int TMS320C64X_REG_A24 = 49;
	public static final int TMS320C64X_REG_A25 = 50;
	public static final int TMS320C64X_REG_A26 = 51;
	public static final int TMS320C64X_REG_A27 = 52;
	public static final int TMS320C64X_REG_A28 = 53;
	public static final int TMS320C64X_REG_A29 = 54;
	public static final int TMS320C64X_REG_A30 = 55;
	public static final int TMS320C64X_REG_A31 = 56;
	public static final int TMS320C64X_REG_B0 = 57;
	public static final int TMS320C64X_REG_B1 = 58;
	public static final int TMS320C64X_REG_B2 = 59;
	public static final int TMS320C64X_REG_B3 = 60;
	public static final int TMS320C64X_REG_B4 = 61;
	public static final int TMS320C64X_REG_B5 = 62;
	public static final int TMS320C64X_REG_B6 = 63;
	public static final int TMS320C64X_REG_B7 = 64;
	public static final int TMS320C64X_REG_B8 = 65;
	public static final int TMS320C64X_REG_B9 = 66;
	public static final int TMS320C64X_REG_B10 = 67;
	public static final int TMS320C64X_REG_B11 = 68;
	public static final int TMS320C64X_REG_B12 = 69;
	public static final int TMS320C64X_REG_B13 = 70;
	public static final int TMS320C64X_REG_B14 = 71;
	public static final int TMS320C64X_REG_B15 = 72;
	public static final int TMS320C64X_REG_B16 = 73;
	public static final int TMS320C64X_REG_B17 = 74;
	public static final int TMS320C64X_REG_B18 = 75;
	public static final int TMS320C64X_REG_B19 = 76;
	public static final int TMS320C64X_REG_B20 = 77;
	public static final int TMS320C64X_REG_B21 = 78;
	public static final int TMS320C64X_REG_B22 = 79;
	public static final int TMS320C64X_REG_B23 = 80;
	public static final int TMS320C64X_REG_B24 = 81;
	public static final int TMS320C64X_REG_B25 = 82;
	public static final int TMS320C64X_REG_B26 = 83;
	public static final int TMS320C64X_REG_B27 = 84;
	public static final int TMS320C64X_REG_B28 = 85;
	public static final int TMS320C64X_REG_B29 = 86;
	public static final int TMS320C64X_REG_B30 = 87;
	public static final int TMS320C64X_REG_B31 = 88;
	public static final int TMS320C64X_REG_PCE1 = 89;
	public static final int TMS320C64X_REG_ENDING = 90;
	public static final int TMS320C64X_REG_EFR = TMS320C64X_REG_ECR;
	public static final int TMS320C64X_REG_IFR = TMS320C64X_REG_ISR;

	public static final int TMS320C64X_INS_INVALID = 0;
	public static final int TMS320C64X_INS_ABS = 1;
	public static final int TMS320C64X_INS_ABS2 = 2;
	public static final int TMS320C64X_INS_ADD = 3;
	public static final int TMS320C64X_INS_ADD2 = 4;
	public static final int TMS320C64X_INS_ADD4 = 5;
	public static final int TMS320C64X_INS_ADDAB = 6;
	public static final int TMS320C64X_INS_ADDAD = 7;
	public static final int TMS320C64X_INS_ADDAH = 8;
	public static final int TMS320C64X_INS_ADDAW = 9;
	public static final int TMS320C64X_INS_ADDK = 10;
	public static final int TMS320C64X_INS_ADDKPC = 11;
	public static final int TMS320C64X_INS_ADDU = 12;
	public static final int TMS320C64X_INS_AND = 13;
	public static final int TMS320C64X_INS_ANDN = 14;
	public static final int TMS320C64X_INS_AVG2 = 15;
	public static final int TMS320C64X_INS_AVGU4 = 16;
	public static final int TMS320C64X_INS_B = 17;
	public static final int TMS320C64X_INS_BDEC = 18;
	public static final int TMS320C64X_INS_BITC4 = 19;
	public static final int TMS320C64X_INS_BNOP = 20;
	public static final int TMS320C64X_INS_BPOS = 21;
	public static final int TMS320C64X_INS_CLR = 22;
	public static final int TMS320C64X_INS_CMPEQ = 23;
	public static final int TMS320C64X_INS_CMPEQ2 = 24;
	public static final int TMS320C64X_INS_CMPEQ4 = 25;
	public static final int TMS320C64X_INS_CMPGT = 26;
	public static final int TMS320C64X_INS_CMPGT2 = 27;
	public static final int TMS320C64X_INS_CMPGTU4 = 28;
	public static final int TMS320C64X_INS_CMPLT = 29;
	public static final int TMS320C64X_INS_CMPLTU = 30;
	public static final int TMS320C64X_INS_DEAL = 31;
	public static final int TMS320C64X_INS_DOTP2 = 32;
	public static final int TMS320C64X_INS_DOTPN2 = 33;
	public static final int TMS320C64X_INS_DOTPNRSU2 = 34;
	public static final int TMS320C64X_INS_DOTPRSU2 = 35;
	public static final int TMS320C64X_INS_DOTPSU4 = 36;
	public static final int TMS320C64X_INS_DOTPU4 = 37;
	public static final int TMS320C64X_INS_EXT = 38;
	public static final int TMS320C64X_INS_EXTU = 39;
	public static final int TMS320C64X_INS_GMPGTU = 40;
	public static final int TMS320C64X_INS_GMPY4 = 41;
	public static final int TMS320C64X_INS_LDB = 42;
	public static final int TMS320C64X_INS_LDBU = 43;
	public static final int TMS320C64X_INS_LDDW = 44;
	public static final int TMS320C64X_INS_LDH = 45;
	public static final int TMS320C64X_INS_LDHU = 46;
	public static final int TMS320C64X_INS_LDNDW = 47;
	public static final int TMS320C64X_INS_LDNW = 48;
	public static final int TMS320C64X_INS_LDW = 49;
	public static final int TMS320C64X_INS_LMBD = 50;
	public static final int TMS320C64X_INS_MAX2 = 51;
	public static final int TMS320C64X_INS_MAXU4 = 52;
	public static final int TMS320C64X_INS_MIN2 = 53;
	public static final int TMS320C64X_INS_MINU4 = 54;
	public static final int TMS320C64X_INS_MPY = 55;
	public static final int TMS320C64X_INS_MPY2 = 56;
	public static final int TMS320C64X_INS_MPYH = 57;
	public static final int TMS320C64X_INS_MPYHI = 58;
	public static final int TMS320C64X_INS_MPYHIR = 59;
	public static final int TMS320C64X_INS_MPYHL = 60;
	public static final int TMS320C64X_INS_MPYHLU = 61;
	public static final int TMS320C64X_INS_MPYHSLU = 62;
	public static final int TMS320C64X_INS_MPYHSU = 63;
	public static final int TMS320C64X_INS_MPYHU = 64;
	public static final int TMS320C64X_INS_MPYHULS = 65;
	public static final int TMS320C64X_INS_MPYHUS = 66;
	public static final int TMS320C64X_INS_MPYLH = 67;
	public static final int TMS320C64X_INS_MPYLHU = 68;
	public static final int TMS320C64X_INS_MPYLI = 69;
	public static final int TMS320C64X_INS_MPYLIR = 70;
	public static final int TMS320C64X_INS_MPYLSHU = 71;
	public static final int TMS320C64X_INS_MPYLUHS = 72;
	public static final int TMS320C64X_INS_MPYSU = 73;
	public static final int TMS320C64X_INS_MPYSU4 = 74;
	public static final int TMS320C64X_INS_MPYU = 75;
	public static final int TMS320C64X_INS_MPYU4 = 76;
	public static final int TMS320C64X_INS_MPYUS = 77;
	public static final int TMS320C64X_INS_MVC = 78;
	public static final int TMS320C64X_INS_MVD = 79;
	public static final int TMS320C64X_INS_MVK = 80;
	public static final int TMS320C64X_INS_MVKL = 81;
	public static final int TMS320C64X_INS_MVKLH = 82;
	public static final int TMS320C64X_INS_NOP = 83;
	public static final int TMS320C64X_INS_NORM = 84;
	public static final int TMS320C64X_INS_OR = 85;
	public static final int TMS320C64X_INS_PACK2 = 86;
	public static final int TMS320C64X_INS_PACKH2 = 87;
	public static final int TMS320C64X_INS_PACKH4 = 88;
	public static final int TMS320C64X_INS_PACKHL2 = 89;
	public static final int TMS320C64X_INS_PACKL4 = 90;
	public static final int TMS320C64X_INS_PACKLH2 = 91;
	public static final int TMS320C64X_INS_ROTL = 92;
	public static final int TMS320C64X_INS_SADD = 93;
	public static final int TMS320C64X_INS_SADD2 = 94;
	public static final int TMS320C64X_INS_SADDU4 = 95;
	public static final int TMS320C64X_INS_SADDUS2 = 96;
	public static final int TMS320C64X_INS_SAT = 97;
	public static final int TMS320C64X_INS_SET = 98;
	public static final int TMS320C64X_INS_SHFL = 99;
	public static final int TMS320C64X_INS_SHL = 100;
	public static final int TMS320C64X_INS_SHLMB = 101;
	public static final int TMS320C64X_INS_SHR = 102;
	public static final int TMS320C64X_INS_SHR2 = 103;
	public static final int TMS320C64X_INS_SHRMB = 104;
	public static final int TMS320C64X_INS_SHRU = 105;
	public static final int TMS320C64X_INS_SHRU2 = 106;
	public static final int TMS320C64X_INS_SMPY = 107;
	public static final int TMS320C64X_INS_SMPY2 = 108;
	public static final int TMS320C64X_INS_SMPYH = 109;
	public static final int TMS320C64X_INS_SMPYHL = 110;
	public static final int TMS320C64X_INS_SMPYLH = 111;
	public static final int TMS320C64X_INS_SPACK2 = 112;
	public static final int TMS320C64X_INS_SPACKU4 = 113;
	public static final int TMS320C64X_INS_SSHL = 114;
	public static final int TMS320C64X_INS_SSHVL = 115;
	public static final int TMS320C64X_INS_SSHVR = 116;
	public static final int TMS320C64X_INS_SSUB = 117;
	public static final int TMS320C64X_INS_STB = 118;
	public static final int TMS320C64X_INS_STDW = 119;
	public static final int TMS320C64X_INS_STH = 120;
	public static final int TMS320C64X_INS_STNDW = 121;
	public static final int TMS320C64X_INS_STNW = 122;
	public static final int TMS320C64X_INS_STW = 123;
	public static final int TMS320C64X_INS_SUB = 124;
	public static final int TMS320C64X_INS_SUB2 = 125;
	public static final int TMS320C64X_INS_SUB4 = 126;
	public static final int TMS320C64X_INS_SUBAB = 127;
	public static final int TMS320C64X_INS_SUBABS4 = 128;
	public static final int TMS320C64X_INS_SUBAH = 129;
	public static final int TMS320C64X_INS_SUBAW = 130;
	public static final int TMS320C64X_INS_SUBC = 131;
	public static final int TMS320C64X_INS_SUBU = 132;
	public static final int TMS320C64X_INS_SWAP4 = 133;
	public static final int TMS320C64X_INS_UNPKHU4 = 134;
	public static final int TMS320C64X_INS_UNPKLU4 = 135;
	public static final int TMS320C64X_INS_XOR = 136;
	public static final int TMS320C64X_INS_XPND2 = 137;
	public static final int TMS320C64X_INS_XPND4 = 138;
	public static final int TMS320C64X_INS_IDLE = 139;
	public static final int TMS320C64X_INS_MV = 140;
	public static final int TMS320C64X_INS_NEG = 141;
	public static final int TMS320C64X_INS_NOT = 142;
	public static final int TMS320C64X_INS_SWAP2 = 143;
	public static final int TMS320C64X_INS_ZERO = 144;
	public static final int TMS320C64X_INS_ENDING = 145;

	public static final int TMS320C64X_GRP_INVALID = 0;
	public static final int TMS320C64X_GRP_JUMP = 1;
	public static final int TMS320C64X_GRP_FUNIT_D = 128;
	public static final int TMS320C64X_GRP_FUNIT_L = 129;
	public static final int TMS320C64X_GRP_FUNIT_M = 130;
	public static final int TMS320C64X_GRP_FUNIT_S = 131;
	public static final int TMS320C64X_GRP_FUNIT_NO = 132;
	public static final int TMS320C64X_GRP_ENDING = 133;

	public static final int TMS320C64X_FUNIT_INVALID = 0;
	public static final int TMS320C64X_FUNIT_D = 1;
	public static final int TMS320C64X_FUNIT_L = 2;
	public static final int TMS320C64X_FUNIT_M = 3;
	public static final int TMS320C64X_FUNIT_S = 4;
	public static final int TMS320C64X_FUNIT_NO = 5;
}