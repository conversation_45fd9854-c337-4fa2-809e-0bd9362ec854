
// CPU12 instructions on PAGE1
static const inst_page1 g_cpu12_inst_page1_table[256] = {
	// 0x0x
	{ M680X_INS_BGND, inh_hid, inh_hid },
	{ M680X_INS_MEM, inh_hid, inh_hid },
	{ M680X_INS_INY, inh_hid, inh_hid },
	{ M680X_INS_DEY, inh_hid, inh_hid },
	{ M680X_INS_DBEQ, loop_hid, inh_hid }, // or DBNE/IBEQ/IBNE/TBEQ/TBNE
	{ M680X_INS_JMP, idx12_hid, inh_hid },
	{ M680X_INS_JMP, ext_hid, inh_hid },
	{ M680X_INS_BSR, rel8_hid, inh_hid },
	{ M680X_INS_INX, inh_hid, inh_hid },
	{ M680X_INS_DEX, inh_hid, inh_hid },
	{ M680X_INS_RTC, inh_hid, inh_hid },
	{ M680X_INS_RTI, inh_hid, inh_hid },
	{ M680X_INS_BSET, idx12_hid, imm8_hid },
	{ M680X_INS_BCLR, idx12_hid, imm8_hid },
	{ M680X_INS_BRSET, idx12_hid, imm8rel_hid },
	{ M680X_INS_BRCLR, idx12_hid, imm8rel_hid },
	// 0x1x
	{ M680X_INS_ANDCC, imm8_hid, inh_hid },
	{ M680X_INS_EDIV, inh_hid, inh_hid },
	{ M680X_INS_MUL, inh_hid, inh_hid },
	{ M680X_INS_EMUL, inh_hid, inh_hid },
	{ M680X_INS_ORCC, imm8_hid, inh_hid },
	{ M680X_INS_JSR, idx12_hid, inh_hid },
	{ M680X_INS_JSR, ext_hid, inh_hid },
	{ M680X_INS_JSR, dir_hid, inh_hid },
	{ M680X_INS_ILLGL, illgl_hid, inh_hid },
	{ M680X_INS_LEAY, idx12_hid, inh_hid },
	{ M680X_INS_LEAX, idx12_hid, inh_hid },
	{ M680X_INS_LEAS, idx12_hid, inh_hid },
	{ M680X_INS_BSET, ext_hid, imm8_hid },
	{ M680X_INS_BCLR, ext_hid, imm8_hid },
	{ M680X_INS_BRSET, ext_hid, imm8rel_hid },
	{ M680X_INS_BRCLR, ext_hid, imm8rel_hid },
	// 0x2x, relative branch instructions
	{ M680X_INS_BRA, rel8_hid, inh_hid },
	{ M680X_INS_BRN, rel8_hid, inh_hid },
	{ M680X_INS_BHI, rel8_hid, inh_hid },
	{ M680X_INS_BLS, rel8_hid, inh_hid },
	{ M680X_INS_BCC, rel8_hid, inh_hid },
	{ M680X_INS_BCS, rel8_hid, inh_hid },
	{ M680X_INS_BNE, rel8_hid, inh_hid },
	{ M680X_INS_BEQ, rel8_hid, inh_hid },
	{ M680X_INS_BVC, rel8_hid, inh_hid },
	{ M680X_INS_BVS, rel8_hid, inh_hid },
	{ M680X_INS_BPL, rel8_hid, inh_hid },
	{ M680X_INS_BMI, rel8_hid, inh_hid },
	{ M680X_INS_BGE, rel8_hid, inh_hid },
	{ M680X_INS_BLT, rel8_hid, inh_hid },
	{ M680X_INS_BGT, rel8_hid, inh_hid },
	{ M680X_INS_BLE, rel8_hid, inh_hid },
	// 0x3x
	{ M680X_INS_PULX, inh_hid, inh_hid },
	{ M680X_INS_PULY, inh_hid, inh_hid },
	{ M680X_INS_PULA, inh_hid, inh_hid },
	{ M680X_INS_PULB, inh_hid, inh_hid },
	{ M680X_INS_PSHX, inh_hid, inh_hid },
	{ M680X_INS_PSHY, inh_hid, inh_hid },
	{ M680X_INS_PSHA, inh_hid, inh_hid },
	{ M680X_INS_PSHB, inh_hid, inh_hid },
	{ M680X_INS_PULC, inh_hid, inh_hid },
	{ M680X_INS_PSHC, inh_hid, inh_hid },
	{ M680X_INS_PULD, inh_hid, inh_hid },
	{ M680X_INS_PSHD, inh_hid, inh_hid },
	{ M680X_INS_WAVR, inh_hid, inh_hid },
	{ M680X_INS_RTS, inh_hid, inh_hid },
	{ M680X_INS_WAI, inh_hid, inh_hid },
	{ M680X_INS_SWI, inh_hid, inh_hid },
	// 0x4x
	{ M680X_INS_NEGA, inh_hid, inh_hid },
	{ M680X_INS_COMA, inh_hid, inh_hid },
	{ M680X_INS_INCA, inh_hid, inh_hid },
	{ M680X_INS_DECA, inh_hid, inh_hid },
	{ M680X_INS_LSRA, inh_hid, inh_hid },
	{ M680X_INS_ROLA, inh_hid, inh_hid },
	{ M680X_INS_RORA, inh_hid, inh_hid },
	{ M680X_INS_ASRA, inh_hid, inh_hid },
	{ M680X_INS_ASLA, inh_hid, inh_hid },
	{ M680X_INS_LSRD, inh_hid, inh_hid },
	{ M680X_INS_CALL, ext_hid, index_hid },
	{ M680X_INS_CALL, idx12_hid, index_hid },
	{ M680X_INS_BSET, dir_hid, imm8_hid },
	{ M680X_INS_BCLR, dir_hid, imm8_hid },
	{ M680X_INS_BRSET, dir_hid, imm8rel_hid },
	{ M680X_INS_BRCLR, dir_hid, imm8rel_hid },
	// 0x5x
	{ M680X_INS_NEGB, inh_hid, inh_hid },
	{ M680X_INS_COMB, inh_hid, inh_hid },
	{ M680X_INS_INCB, inh_hid, inh_hid },
	{ M680X_INS_DECB, inh_hid, inh_hid },
	{ M680X_INS_LSRB, inh_hid, inh_hid },
	{ M680X_INS_ROLB, inh_hid, inh_hid },
	{ M680X_INS_RORB, inh_hid, inh_hid },
	{ M680X_INS_ASRB, inh_hid, inh_hid },
	{ M680X_INS_ASLB, inh_hid, inh_hid },
	{ M680X_INS_ASLD, inh_hid, inh_hid },
	{ M680X_INS_STAA, dir_hid, inh_hid },
	{ M680X_INS_STAB, dir_hid, inh_hid },
	{ M680X_INS_STD, dir_hid, inh_hid },
	{ M680X_INS_STY, dir_hid, inh_hid },
	{ M680X_INS_STX, dir_hid, inh_hid },
	{ M680X_INS_STS, dir_hid, inh_hid },
	// 0x6x
	{ M680X_INS_NEG, idx12_hid, inh_hid },
	{ M680X_INS_COM, idx12_hid, inh_hid },
	{ M680X_INS_INC, idx12_hid, inh_hid },
	{ M680X_INS_DEC, idx12_hid, inh_hid },
	{ M680X_INS_LSR, idx12_hid, inh_hid },
	{ M680X_INS_ROL, idx12_hid, inh_hid },
	{ M680X_INS_ROR, idx12_hid, inh_hid },
	{ M680X_INS_ASR, idx12_hid, inh_hid },
	{ M680X_INS_ASL, idx12_hid, inh_hid },
	{ M680X_INS_CLR, idx12_hid, inh_hid },
	{ M680X_INS_STAA, idx12_hid, inh_hid },
	{ M680X_INS_STAB, idx12_hid, inh_hid },
	{ M680X_INS_STD, idx12_hid, inh_hid },
	{ M680X_INS_STY, idx12_hid, inh_hid },
	{ M680X_INS_STX, idx12_hid, inh_hid },
	{ M680X_INS_STS, idx12_hid, inh_hid },
	// 0x7x
	{ M680X_INS_NEG, ext_hid, inh_hid },
	{ M680X_INS_COM, ext_hid, inh_hid },
	{ M680X_INS_INC, ext_hid, inh_hid },
	{ M680X_INS_DEC, ext_hid, inh_hid },
	{ M680X_INS_LSR, ext_hid, inh_hid },
	{ M680X_INS_ROL, ext_hid, inh_hid },
	{ M680X_INS_ROR, ext_hid, inh_hid },
	{ M680X_INS_ASR, ext_hid, inh_hid },
	{ M680X_INS_ASL, ext_hid, inh_hid },
	{ M680X_INS_CLR, ext_hid, inh_hid },
	{ M680X_INS_STAA, ext_hid, inh_hid },
	{ M680X_INS_STAB, ext_hid, inh_hid },
	{ M680X_INS_STD, ext_hid, inh_hid },
	{ M680X_INS_STY, ext_hid, inh_hid },
	{ M680X_INS_STX, ext_hid, inh_hid },
	{ M680X_INS_STS, ext_hid, inh_hid },
	// 0x8x
	{ M680X_INS_SUBA, imm8_hid, inh_hid },
	{ M680X_INS_CMPA, imm8_hid, inh_hid },
	{ M680X_INS_SBCA, imm8_hid, inh_hid },
	{ M680X_INS_SUBD, imm16_hid, inh_hid },
	{ M680X_INS_ANDA, imm8_hid, inh_hid },
	{ M680X_INS_BITA, imm8_hid, inh_hid },
	{ M680X_INS_LDAA, imm8_hid, inh_hid },
	{ M680X_INS_CLRA, inh_hid, inh_hid },
	{ M680X_INS_EORA, imm8_hid, inh_hid },
	{ M680X_INS_ADCA, imm8_hid, inh_hid },
	{ M680X_INS_ORAA, imm8_hid, inh_hid },
	{ M680X_INS_ADDA, imm8_hid, inh_hid },
	{ M680X_INS_CPD, imm16_hid, inh_hid },
	{ M680X_INS_CPY, imm16_hid, inh_hid },
	{ M680X_INS_CPX, imm16_hid, inh_hid },
	{ M680X_INS_CPS, imm16_hid, inh_hid },
	// 0x9x
	{ M680X_INS_SUBA, dir_hid, inh_hid },
	{ M680X_INS_CMPA, dir_hid, inh_hid },
	{ M680X_INS_SBCA, dir_hid, inh_hid },
	{ M680X_INS_SUBD, dir_hid, inh_hid },
	{ M680X_INS_ANDA, dir_hid, inh_hid },
	{ M680X_INS_BITA, dir_hid, inh_hid },
	{ M680X_INS_LDAA, dir_hid, inh_hid },
	{ M680X_INS_TSTA, inh_hid, inh_hid },
	{ M680X_INS_EORA, dir_hid, inh_hid },
	{ M680X_INS_ADCA, dir_hid, inh_hid },
	{ M680X_INS_ORAA, dir_hid, inh_hid },
	{ M680X_INS_ADDA, dir_hid, inh_hid },
	{ M680X_INS_CPD, dir_hid, inh_hid },
	{ M680X_INS_CPY, dir_hid, inh_hid },
	{ M680X_INS_CPX, dir_hid, inh_hid },
	{ M680X_INS_CPS, dir_hid, inh_hid },
	// 0xAx
	{ M680X_INS_SUBA, idx12_hid, inh_hid },
	{ M680X_INS_CMPA, idx12_hid, inh_hid },
	{ M680X_INS_SBCA, idx12_hid, inh_hid },
	{ M680X_INS_SUBD, idx12_hid, inh_hid },
	{ M680X_INS_ANDA, idx12_hid, inh_hid },
	{ M680X_INS_BITA, idx12_hid, inh_hid },
	{ M680X_INS_LDAA, idx12_hid, inh_hid },
	{ M680X_INS_NOP, inh_hid, inh_hid },
	{ M680X_INS_EORA, idx12_hid, inh_hid },
	{ M680X_INS_ADCA, idx12_hid, inh_hid },
	{ M680X_INS_ORAA, idx12_hid, inh_hid },
	{ M680X_INS_ADDA, idx12_hid, inh_hid },
	{ M680X_INS_CPD, idx12_hid, inh_hid },
	{ M680X_INS_CPY, idx12_hid, inh_hid },
	{ M680X_INS_CPX, idx12_hid, inh_hid },
	{ M680X_INS_CPS, idx12_hid, inh_hid },
	// 0xBx
	{ M680X_INS_SUBA, ext_hid, inh_hid },
	{ M680X_INS_CMPA, ext_hid, inh_hid },
	{ M680X_INS_SBCA, ext_hid, inh_hid },
	{ M680X_INS_SUBD, ext_hid, inh_hid },
	{ M680X_INS_ANDA, ext_hid, inh_hid },
	{ M680X_INS_BITA, ext_hid, inh_hid },
	{ M680X_INS_LDAA, ext_hid, inh_hid },
	{ M680X_INS_TFR, rr12_hid, inh_hid }, // or EXG
	{ M680X_INS_EORA, ext_hid, inh_hid },
	{ M680X_INS_ADCA, ext_hid, inh_hid },
	{ M680X_INS_ORAA, ext_hid, inh_hid },
	{ M680X_INS_ADDA, ext_hid, inh_hid },
	{ M680X_INS_CPD, ext_hid, inh_hid },
	{ M680X_INS_CPY, ext_hid, inh_hid },
	{ M680X_INS_CPX, ext_hid, inh_hid },
	{ M680X_INS_CPS, ext_hid, inh_hid },
	// 0xCx
	{ M680X_INS_SUBB, imm8_hid, inh_hid },
	{ M680X_INS_CMPB, imm8_hid, inh_hid },
	{ M680X_INS_SBCB, imm8_hid, inh_hid },
	{ M680X_INS_ADDD, imm16_hid, inh_hid },
	{ M680X_INS_ANDB, imm8_hid, inh_hid },
	{ M680X_INS_BITB, imm8_hid, inh_hid },
	{ M680X_INS_LDAB, imm8_hid, inh_hid },
	{ M680X_INS_CLRB, inh_hid, inh_hid },
	{ M680X_INS_EORB, imm8_hid, inh_hid },
	{ M680X_INS_ADCB, imm8_hid, inh_hid },
	{ M680X_INS_ORAB, imm8_hid, inh_hid },
	{ M680X_INS_ADDB, imm8_hid, inh_hid },
	{ M680X_INS_LDD, imm16_hid, inh_hid },
	{ M680X_INS_LDY, imm16_hid, inh_hid },
	{ M680X_INS_LDX, imm16_hid, inh_hid },
	{ M680X_INS_LDS, imm16_hid, inh_hid },
	// 0xDx
	{ M680X_INS_SUBB, dir_hid, inh_hid },
	{ M680X_INS_CMPB, dir_hid, inh_hid },
	{ M680X_INS_SBCB, dir_hid, inh_hid },
	{ M680X_INS_ADDD, dir_hid, inh_hid },
	{ M680X_INS_ANDB, dir_hid, inh_hid },
	{ M680X_INS_BITB, dir_hid, inh_hid },
	{ M680X_INS_LDAB, dir_hid, inh_hid },
	{ M680X_INS_TSTB, inh_hid, inh_hid },
	{ M680X_INS_EORB, dir_hid, inh_hid },
	{ M680X_INS_ADCB, dir_hid, inh_hid },
	{ M680X_INS_ORAB, dir_hid, inh_hid },
	{ M680X_INS_ADDB, dir_hid, inh_hid },
	{ M680X_INS_LDD, dir_hid, inh_hid },
	{ M680X_INS_LDY, dir_hid, inh_hid },
	{ M680X_INS_LDX, dir_hid, inh_hid },
	{ M680X_INS_LDS, dir_hid, inh_hid },
	// 0xEx
	{ M680X_INS_SUBB, idx12_hid, inh_hid },
	{ M680X_INS_CMPB, idx12_hid, inh_hid },
	{ M680X_INS_SBCB, idx12_hid, inh_hid },
	{ M680X_INS_ADDD, idx12_hid, inh_hid },
	{ M680X_INS_ANDB, idx12_hid, inh_hid },
	{ M680X_INS_BITB, idx12_hid, inh_hid },
	{ M680X_INS_LDAB, idx12_hid, inh_hid },
	{ M680X_INS_TST, idx12_hid, inh_hid },
	{ M680X_INS_EORB, idx12_hid, inh_hid },
	{ M680X_INS_ADCB, idx12_hid, inh_hid },
	{ M680X_INS_ORAB, idx12_hid, inh_hid },
	{ M680X_INS_ADDB, idx12_hid, inh_hid },
	{ M680X_INS_LDD, idx12_hid, inh_hid },
	{ M680X_INS_LDY, idx12_hid, inh_hid },
	{ M680X_INS_LDX, idx12_hid, inh_hid },
	{ M680X_INS_LDS, idx12_hid, inh_hid },
	// 0xFx
	{ M680X_INS_SUBA, ext_hid, inh_hid },
	{ M680X_INS_CMPA, ext_hid, inh_hid },
	{ M680X_INS_SBCA, ext_hid, inh_hid },
	{ M680X_INS_ADDD, ext_hid, inh_hid },
	{ M680X_INS_ANDA, ext_hid, inh_hid },
	{ M680X_INS_BITA, ext_hid, inh_hid },
	{ M680X_INS_LDAA, ext_hid, inh_hid },
	{ M680X_INS_TST, ext_hid, inh_hid },
	{ M680X_INS_EORA, ext_hid, inh_hid },
	{ M680X_INS_ADCA, ext_hid, inh_hid },
	{ M680X_INS_ORAA, ext_hid, inh_hid },
	{ M680X_INS_ADDA, ext_hid, inh_hid },
	{ M680X_INS_LDD, ext_hid, inh_hid },
	{ M680X_INS_LDY, ext_hid, inh_hid },
	{ M680X_INS_LDX, ext_hid, inh_hid },
	{ M680X_INS_LDS, ext_hid, inh_hid },
};

// CPU12 instructions on PAGE2
static const inst_pageX g_cpu12_inst_page2_table[] = {
	{ 0x00, M680X_INS_MOVW, imm16i12x_hid, inh_hid },
	{ 0x01, M680X_INS_MOVW, exti12x_hid, inh_hid },
	{ 0x02, M680X_INS_MOVW, idx12_hid, idx12_hid },
	{ 0x03, M680X_INS_MOVW, imm16_hid,  ext_hid },
	{ 0x04, M680X_INS_MOVW, ext_hid, ext_hid },
	{ 0x05, M680X_INS_MOVW, idx12_hid, ext_hid },
	{ 0x06, M680X_INS_ABA, inh_hid, inh_hid },
	{ 0x07, M680X_INS_DAA, inh_hid, inh_hid },
	{ 0x08, M680X_INS_MOVB, imm8i12x_hid, inh_hid },
	{ 0x09, M680X_INS_MOVB, exti12x_hid, inh_hid },
	{ 0x0a, M680X_INS_MOVB, idx12_hid, idx12_hid },
	{ 0x0b, M680X_INS_MOVB, imm8_hid, ext_hid },
	{ 0x0c, M680X_INS_MOVB, ext_hid, ext_hid },
	{ 0x0d, M680X_INS_MOVB, idx12_hid, ext_hid },
	{ 0x0e, M680X_INS_TAB, inh_hid, inh_hid },
	{ 0x0f, M680X_INS_TBA, inh_hid, inh_hid },
	{ 0x10, M680X_INS_IDIV, inh_hid, inh_hid },
	{ 0x11, M680X_INS_FDIV, inh_hid, inh_hid },
	{ 0x12, M680X_INS_EMACS, ext_hid, inh_hid },
	{ 0x13, M680X_INS_EMULS, inh_hid, inh_hid },
	{ 0x14, M680X_INS_EDIVS, inh_hid, inh_hid },
	{ 0x15, M680X_INS_IDIVS, inh_hid, inh_hid },
	{ 0x16, M680X_INS_SBA, inh_hid, inh_hid },
	{ 0x17, M680X_INS_CBA, inh_hid, inh_hid },
	{ 0x18, M680X_INS_MAXA, idx12_hid, inh_hid },
	{ 0x19, M680X_INS_MINA, idx12_hid, inh_hid },
	{ 0x1a, M680X_INS_EMAXD, idx12_hid, inh_hid },
	{ 0x1b, M680X_INS_EMIND, idx12_hid, inh_hid },
	{ 0x1c, M680X_INS_MAXM, idx12_hid, inh_hid },
	{ 0x1d, M680X_INS_MINM, idx12_hid, inh_hid },
	{ 0x1e, M680X_INS_EMAXM, idx12_hid, inh_hid },
	{ 0x1f, M680X_INS_EMINM, idx12_hid, inh_hid },
	{ 0x20, M680X_INS_LBRA, rel16_hid, inh_hid },
	{ 0x21, M680X_INS_LBRN, rel16_hid, inh_hid },
	{ 0x22, M680X_INS_LBHI, rel16_hid, inh_hid },
	{ 0x23, M680X_INS_LBLS, rel16_hid, inh_hid },
	{ 0x24, M680X_INS_LBCC, rel16_hid, inh_hid },
	{ 0x25, M680X_INS_LBCS, rel16_hid, inh_hid },
	{ 0x26, M680X_INS_LBNE, rel16_hid, inh_hid },
	{ 0x27, M680X_INS_LBEQ, rel16_hid, inh_hid },
	{ 0x28, M680X_INS_LBVC, rel16_hid, inh_hid },
	{ 0x29, M680X_INS_LBVS, rel16_hid, inh_hid },
	{ 0x2a, M680X_INS_LBPL, rel16_hid, inh_hid },
	{ 0x2b, M680X_INS_LBMI, rel16_hid, inh_hid },
	{ 0x2c, M680X_INS_LBGE, rel16_hid, inh_hid },
	{ 0x2d, M680X_INS_LBLT, rel16_hid, inh_hid },
	{ 0x2e, M680X_INS_LBGT, rel16_hid, inh_hid },
	{ 0x2f, M680X_INS_LBLE, rel16_hid, inh_hid },
	{ 0x3a, M680X_INS_REV, inh_hid, inh_hid },
	{ 0x3b, M680X_INS_REVW, inh_hid, inh_hid },
	{ 0x3c, M680X_INS_WAV, inh_hid, inh_hid },
	{ 0x3d, M680X_INS_TBL, idx12s_hid, inh_hid },
	{ 0x3e, M680X_INS_STOP, inh_hid, inh_hid },
	{ 0x3f, M680X_INS_ETBL, idx12s_hid, inh_hid },
};

