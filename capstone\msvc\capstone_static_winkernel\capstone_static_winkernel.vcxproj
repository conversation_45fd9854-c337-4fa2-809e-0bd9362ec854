﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\arch\AArch64\AArch64BaseInfo.c" />
    <ClCompile Include="..\..\arch\AArch64\AArch64Disassembler.c" />
    <ClCompile Include="..\..\arch\AArch64\AArch64InstPrinter.c" />
    <ClCompile Include="..\..\arch\AArch64\AArch64Mapping.c" />
    <ClCompile Include="..\..\arch\AArch64\AArch64Module.c" />
    <ClCompile Include="..\..\arch\ARM\ARMDisassembler.c" />
    <ClCompile Include="..\..\arch\ARM\ARMInstPrinter.c" />
    <ClCompile Include="..\..\arch\ARM\ARMMapping.c" />
    <ClCompile Include="..\..\arch\ARM\ARMModule.c" />
    <ClCompile Include="..\..\arch\M68K\M68KDisassembler.c" />
    <ClCompile Include="..\..\arch\M68K\M68KInstPrinter.c" />
    <ClCompile Include="..\..\arch\M68K\M68KModule.c" />
    <ClCompile Include="..\..\arch\Mips\MipsDisassembler.c" />
    <ClCompile Include="..\..\arch\Mips\MipsInstPrinter.c" />
    <ClCompile Include="..\..\arch\Mips\MipsMapping.c" />
    <ClCompile Include="..\..\arch\Mips\MipsModule.c" />
    <ClCompile Include="..\..\arch\PowerPC\PPCDisassembler.c" />
    <ClCompile Include="..\..\arch\PowerPC\PPCInstPrinter.c" />
    <ClCompile Include="..\..\arch\PowerPC\PPCMapping.c" />
    <ClCompile Include="..\..\arch\PowerPC\PPCModule.c" />
    <ClCompile Include="..\..\arch\Sparc\SparcDisassembler.c" />
    <ClCompile Include="..\..\arch\Sparc\SparcInstPrinter.c" />
    <ClCompile Include="..\..\arch\Sparc\SparcMapping.c" />
    <ClCompile Include="..\..\arch\Sparc\SparcModule.c" />
    <ClCompile Include="..\..\arch\SystemZ\SystemZDisassembler.c" />
    <ClCompile Include="..\..\arch\SystemZ\SystemZInstPrinter.c" />
    <ClCompile Include="..\..\arch\SystemZ\SystemZMapping.c" />
    <ClCompile Include="..\..\arch\SystemZ\SystemZMCTargetDesc.c" />
    <ClCompile Include="..\..\arch\SystemZ\SystemZModule.c" />
    <ClCompile Include="..\..\arch\X86\X86ATTInstPrinter.c" />
    <ClCompile Include="..\..\arch\X86\X86Disassembler.c" />
    <ClCompile Include="..\..\arch\X86\X86DisassemblerDecoder.c" />
    <ClCompile Include="..\..\arch\X86\X86IntelInstPrinter.c" />
    <ClCompile Include="..\..\arch\X86\X86Mapping.c" />
    <ClCompile Include="..\..\arch\X86\X86Module.c" />
    <ClCompile Include="..\..\arch\XCore\XCoreInstPrinter.c" />
    <ClCompile Include="..\..\arch\XCore\XCoreDisassembler.c" />
    <ClCompile Include="..\..\arch\XCore\XCoreMapping.c" />
    <ClCompile Include="..\..\arch\XCore\XCoreModule.c" />
    <ClCompile Include="..\..\cs.c" />
    <ClCompile Include="..\..\MCInst.c" />
    <ClCompile Include="..\..\MCInstrDesc.c" />
    <ClCompile Include="..\..\MCRegisterInfo.c" />
    <ClCompile Include="..\..\SStream.c" />
    <ClCompile Include="..\..\utils.c" />
    <ClCompile Include="..\..\windows\winkernel_mm.c" />
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{FE197816-EF84-4E8D-B29D-E0A6BA2B144B}</ProjectGuid>
    <TemplateGuid>{1bc93793-694f-48fe-9372-81e2b05556fd}</TemplateGuid>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <MinimumVisualStudioVersion>11.0</MinimumVisualStudioVersion>
    <Configuration>Win8.1 Debug</Configuration>
    <Platform Condition="'$(Platform)' == ''">Win32</Platform>
    <RootNamespace>capstone_static_winkernel</RootNamespace>
    <ProjectName>capstone_static_winkernel</ProjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <TargetVersion>Windows7</TargetVersion>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <DriverType>KMDF</DriverType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <TargetVersion>Windows7</TargetVersion>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <DriverType>KMDF</DriverType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <TargetVersion>Windows7</TargetVersion>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <DriverType>KMDF</DriverType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <TargetVersion>Windows7</TargetVersion>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <DriverType>KMDF</DriverType>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <DebuggerFlavor>DbgengKernelDebugger</DebuggerFlavor>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <DebuggerFlavor>DbgengKernelDebugger</DebuggerFlavor>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <DebuggerFlavor>DbgengKernelDebugger</DebuggerFlavor>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <DebuggerFlavor>DbgengKernelDebugger</DebuggerFlavor>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WppScanConfigurationData Condition="'%(ClCompile. ScanConfigurationData)'  == ''">trace.h</WppScanConfigurationData>
      <WppKernelMode>true</WppKernelMode>
      <AdditionalIncludeDirectories>..\..\include;..\headers;$(IntDir);C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\km;C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared;C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\ucrt;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>CAPSTONE_X86_ATT_DISABLE_NO;CAPSTONE_DIET_NO;CAPSTONE_X86_REDUCE_NO;CAPSTONE_HAS_ARM;CAPSTONE_HAS_ARM64;CAPSTONE_HAS_MIPS;CAPSTONE_HAS_M68K;CAPSTONE_HAS_POWERPC;CAPSTONE_HAS_SPARC;CAPSTONE_HAS_SYSZ;CAPSTONE_HAS_X86;CAPSTONE_HAS_XCORE;CAPSTONE_USE_SYS_DYN_MEM;_WIN64;_AMD64_;AMD64;_M_X64;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <TreatWarningAsError>false</TreatWarningAsError>
      <WarningLevel>Level3</WarningLevel>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WppScanConfigurationData Condition="'%(ClCompile. ScanConfigurationData)'  == ''">trace.h</WppScanConfigurationData>
      <WppKernelMode>true</WppKernelMode>
      <AdditionalIncludeDirectories>..\..\include;..\headers;$(IntDir);C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\km;C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared;C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\ucrt;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>CAPSTONE_X86_ATT_DISABLE;CAPSTONE_DIET;CAPSTONE_HAS_X86;CAPSTONE_USE_SYS_DYN_MEM;_WIN64;_AMD64_;AMD64;_M_X64;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <TreatWarningAsError>false</TreatWarningAsError>
      <WarningLevel>Level3</WarningLevel>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WppScanConfigurationData Condition="'%(ClCompile. ScanConfigurationData)'  == ''">trace.h</WppScanConfigurationData>
      <WppKernelMode>true</WppKernelMode>
      <AdditionalIncludeDirectories>..\..\include;..\headers;$(IntDir);C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\km;C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared;C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\ucrt;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>CAPSTONE_X86_ATT_DISABLE_NO;CAPSTONE_DIET_NO;CAPSTONE_X86_REDUCE_NO;CAPSTONE_HAS_ARM;CAPSTONE_HAS_ARM64;CAPSTONE_HAS_MIPS;CAPSTONE_HAS_M68K;CAPSTONE_HAS_POWERPC;CAPSTONE_HAS_SPARC;CAPSTONE_HAS_SYSZ;CAPSTONE_HAS_X86;CAPSTONE_HAS_XCORE;CAPSTONE_USE_SYS_DYN_MEM;_WIN64;_AMD64_;AMD64;_M_X64;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <TreatWarningAsError>false</TreatWarningAsError>
      <WarningLevel>Level3</WarningLevel>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WppScanConfigurationData Condition="'%(ClCompile. ScanConfigurationData)'  == ''">trace.h</WppScanConfigurationData>
      <WppKernelMode>true</WppKernelMode>
      <AdditionalIncludeDirectories>..\..\include;..\headers;$(IntDir);C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\km;C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared;C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\ucrt;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>CAPSTONE_X86_ATT_DISABLE;CAPSTONE_DIET;CAPSTONE_HAS_X86;CAPSTONE_USE_SYS_DYN_MEM;_WIN64;_AMD64_;AMD64;_M_X64;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <TreatWarningAsError>false</TreatWarningAsError>
      <WarningLevel>Level3</WarningLevel>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <FilesToPackage Include="$(TargetPath)" />
    <FilesToPackage Include="@(Inf->'%(CopyOutput)')" Condition="'@(Inf)'!=''" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>