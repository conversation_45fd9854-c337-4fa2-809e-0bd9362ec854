// For Capstone Engine. AUTO-GENERATED FILE, DO NOT EDIT
package capstone;

public class Mips_const {

	// Operand type for instruction's operands

	public static final int MIPS_OP_INVALID = 0;
	public static final int MIPS_OP_REG = 1;
	public static final int MIPS_OP_IMM = 2;
	public static final int MIPS_OP_MEM = 3;

	// MIPS registers

	public static final int MIPS_REG_INVALID = 0;

	// General purpose registers
	public static final int MIPS_REG_PC = 1;
	public static final int MIPS_REG_0 = 2;
	public static final int MIPS_REG_1 = 3;
	public static final int MIPS_REG_2 = 4;
	public static final int MIPS_REG_3 = 5;
	public static final int MIPS_REG_4 = 6;
	public static final int MIPS_REG_5 = 7;
	public static final int MIPS_REG_6 = 8;
	public static final int MIPS_REG_7 = 9;
	public static final int MIPS_REG_8 = 10;
	public static final int MIPS_REG_9 = 11;
	public static final int MIPS_REG_10 = 12;
	public static final int MIPS_REG_11 = 13;
	public static final int MIPS_REG_12 = 14;
	public static final int MIPS_REG_13 = 15;
	public static final int MIPS_REG_14 = 16;
	public static final int MIPS_REG_15 = 17;
	public static final int MIPS_REG_16 = 18;
	public static final int MIPS_REG_17 = 19;
	public static final int MIPS_REG_18 = 20;
	public static final int MIPS_REG_19 = 21;
	public static final int MIPS_REG_20 = 22;
	public static final int MIPS_REG_21 = 23;
	public static final int MIPS_REG_22 = 24;
	public static final int MIPS_REG_23 = 25;
	public static final int MIPS_REG_24 = 26;
	public static final int MIPS_REG_25 = 27;
	public static final int MIPS_REG_26 = 28;
	public static final int MIPS_REG_27 = 29;
	public static final int MIPS_REG_28 = 30;
	public static final int MIPS_REG_29 = 31;
	public static final int MIPS_REG_30 = 32;
	public static final int MIPS_REG_31 = 33;

	// DSP registers
	public static final int MIPS_REG_DSPCCOND = 34;
	public static final int MIPS_REG_DSPCARRY = 35;
	public static final int MIPS_REG_DSPEFI = 36;
	public static final int MIPS_REG_DSPOUTFLAG = 37;
	public static final int MIPS_REG_DSPOUTFLAG16_19 = 38;
	public static final int MIPS_REG_DSPOUTFLAG20 = 39;
	public static final int MIPS_REG_DSPOUTFLAG21 = 40;
	public static final int MIPS_REG_DSPOUTFLAG22 = 41;
	public static final int MIPS_REG_DSPOUTFLAG23 = 42;
	public static final int MIPS_REG_DSPPOS = 43;
	public static final int MIPS_REG_DSPSCOUNT = 44;

	// ACC registers
	public static final int MIPS_REG_AC0 = 45;
	public static final int MIPS_REG_AC1 = 46;
	public static final int MIPS_REG_AC2 = 47;
	public static final int MIPS_REG_AC3 = 48;

	// COP registers
	public static final int MIPS_REG_CC0 = 49;
	public static final int MIPS_REG_CC1 = 50;
	public static final int MIPS_REG_CC2 = 51;
	public static final int MIPS_REG_CC3 = 52;
	public static final int MIPS_REG_CC4 = 53;
	public static final int MIPS_REG_CC5 = 54;
	public static final int MIPS_REG_CC6 = 55;
	public static final int MIPS_REG_CC7 = 56;

	// FPU registers
	public static final int MIPS_REG_F0 = 57;
	public static final int MIPS_REG_F1 = 58;
	public static final int MIPS_REG_F2 = 59;
	public static final int MIPS_REG_F3 = 60;
	public static final int MIPS_REG_F4 = 61;
	public static final int MIPS_REG_F5 = 62;
	public static final int MIPS_REG_F6 = 63;
	public static final int MIPS_REG_F7 = 64;
	public static final int MIPS_REG_F8 = 65;
	public static final int MIPS_REG_F9 = 66;
	public static final int MIPS_REG_F10 = 67;
	public static final int MIPS_REG_F11 = 68;
	public static final int MIPS_REG_F12 = 69;
	public static final int MIPS_REG_F13 = 70;
	public static final int MIPS_REG_F14 = 71;
	public static final int MIPS_REG_F15 = 72;
	public static final int MIPS_REG_F16 = 73;
	public static final int MIPS_REG_F17 = 74;
	public static final int MIPS_REG_F18 = 75;
	public static final int MIPS_REG_F19 = 76;
	public static final int MIPS_REG_F20 = 77;
	public static final int MIPS_REG_F21 = 78;
	public static final int MIPS_REG_F22 = 79;
	public static final int MIPS_REG_F23 = 80;
	public static final int MIPS_REG_F24 = 81;
	public static final int MIPS_REG_F25 = 82;
	public static final int MIPS_REG_F26 = 83;
	public static final int MIPS_REG_F27 = 84;
	public static final int MIPS_REG_F28 = 85;
	public static final int MIPS_REG_F29 = 86;
	public static final int MIPS_REG_F30 = 87;
	public static final int MIPS_REG_F31 = 88;
	public static final int MIPS_REG_FCC0 = 89;
	public static final int MIPS_REG_FCC1 = 90;
	public static final int MIPS_REG_FCC2 = 91;
	public static final int MIPS_REG_FCC3 = 92;
	public static final int MIPS_REG_FCC4 = 93;
	public static final int MIPS_REG_FCC5 = 94;
	public static final int MIPS_REG_FCC6 = 95;
	public static final int MIPS_REG_FCC7 = 96;

	// AFPR128
	public static final int MIPS_REG_W0 = 97;
	public static final int MIPS_REG_W1 = 98;
	public static final int MIPS_REG_W2 = 99;
	public static final int MIPS_REG_W3 = 100;
	public static final int MIPS_REG_W4 = 101;
	public static final int MIPS_REG_W5 = 102;
	public static final int MIPS_REG_W6 = 103;
	public static final int MIPS_REG_W7 = 104;
	public static final int MIPS_REG_W8 = 105;
	public static final int MIPS_REG_W9 = 106;
	public static final int MIPS_REG_W10 = 107;
	public static final int MIPS_REG_W11 = 108;
	public static final int MIPS_REG_W12 = 109;
	public static final int MIPS_REG_W13 = 110;
	public static final int MIPS_REG_W14 = 111;
	public static final int MIPS_REG_W15 = 112;
	public static final int MIPS_REG_W16 = 113;
	public static final int MIPS_REG_W17 = 114;
	public static final int MIPS_REG_W18 = 115;
	public static final int MIPS_REG_W19 = 116;
	public static final int MIPS_REG_W20 = 117;
	public static final int MIPS_REG_W21 = 118;
	public static final int MIPS_REG_W22 = 119;
	public static final int MIPS_REG_W23 = 120;
	public static final int MIPS_REG_W24 = 121;
	public static final int MIPS_REG_W25 = 122;
	public static final int MIPS_REG_W26 = 123;
	public static final int MIPS_REG_W27 = 124;
	public static final int MIPS_REG_W28 = 125;
	public static final int MIPS_REG_W29 = 126;
	public static final int MIPS_REG_W30 = 127;
	public static final int MIPS_REG_W31 = 128;
	public static final int MIPS_REG_HI = 129;
	public static final int MIPS_REG_LO = 130;
	public static final int MIPS_REG_P0 = 131;
	public static final int MIPS_REG_P1 = 132;
	public static final int MIPS_REG_P2 = 133;
	public static final int MIPS_REG_MPL0 = 134;
	public static final int MIPS_REG_MPL1 = 135;
	public static final int MIPS_REG_MPL2 = 136;
	public static final int MIPS_REG_ENDING = 137;
	public static final int MIPS_REG_ZERO = MIPS_REG_0;
	public static final int MIPS_REG_AT = MIPS_REG_1;
	public static final int MIPS_REG_V0 = MIPS_REG_2;
	public static final int MIPS_REG_V1 = MIPS_REG_3;
	public static final int MIPS_REG_A0 = MIPS_REG_4;
	public static final int MIPS_REG_A1 = MIPS_REG_5;
	public static final int MIPS_REG_A2 = MIPS_REG_6;
	public static final int MIPS_REG_A3 = MIPS_REG_7;
	public static final int MIPS_REG_T0 = MIPS_REG_8;
	public static final int MIPS_REG_T1 = MIPS_REG_9;
	public static final int MIPS_REG_T2 = MIPS_REG_10;
	public static final int MIPS_REG_T3 = MIPS_REG_11;
	public static final int MIPS_REG_T4 = MIPS_REG_12;
	public static final int MIPS_REG_T5 = MIPS_REG_13;
	public static final int MIPS_REG_T6 = MIPS_REG_14;
	public static final int MIPS_REG_T7 = MIPS_REG_15;
	public static final int MIPS_REG_S0 = MIPS_REG_16;
	public static final int MIPS_REG_S1 = MIPS_REG_17;
	public static final int MIPS_REG_S2 = MIPS_REG_18;
	public static final int MIPS_REG_S3 = MIPS_REG_19;
	public static final int MIPS_REG_S4 = MIPS_REG_20;
	public static final int MIPS_REG_S5 = MIPS_REG_21;
	public static final int MIPS_REG_S6 = MIPS_REG_22;
	public static final int MIPS_REG_S7 = MIPS_REG_23;
	public static final int MIPS_REG_T8 = MIPS_REG_24;
	public static final int MIPS_REG_T9 = MIPS_REG_25;
	public static final int MIPS_REG_K0 = MIPS_REG_26;
	public static final int MIPS_REG_K1 = MIPS_REG_27;
	public static final int MIPS_REG_GP = MIPS_REG_28;
	public static final int MIPS_REG_SP = MIPS_REG_29;
	public static final int MIPS_REG_FP = MIPS_REG_30;
	public static final int MIPS_REG_S8 = MIPS_REG_30;
	public static final int MIPS_REG_RA = MIPS_REG_31;
	public static final int MIPS_REG_HI0 = MIPS_REG_AC0;
	public static final int MIPS_REG_HI1 = MIPS_REG_AC1;
	public static final int MIPS_REG_HI2 = MIPS_REG_AC2;
	public static final int MIPS_REG_HI3 = MIPS_REG_AC3;
	public static final int MIPS_REG_LO0 = MIPS_REG_HI0;
	public static final int MIPS_REG_LO1 = MIPS_REG_HI1;
	public static final int MIPS_REG_LO2 = MIPS_REG_HI2;
	public static final int MIPS_REG_LO3 = MIPS_REG_HI3;

	// MIPS instruction

	public static final int MIPS_INS_INVALID = 0;
	public static final int MIPS_INS_ABSQ_S = 1;
	public static final int MIPS_INS_ADD = 2;
	public static final int MIPS_INS_ADDIUPC = 3;
	public static final int MIPS_INS_ADDIUR1SP = 4;
	public static final int MIPS_INS_ADDIUR2 = 5;
	public static final int MIPS_INS_ADDIUS5 = 6;
	public static final int MIPS_INS_ADDIUSP = 7;
	public static final int MIPS_INS_ADDQH = 8;
	public static final int MIPS_INS_ADDQH_R = 9;
	public static final int MIPS_INS_ADDQ = 10;
	public static final int MIPS_INS_ADDQ_S = 11;
	public static final int MIPS_INS_ADDSC = 12;
	public static final int MIPS_INS_ADDS_A = 13;
	public static final int MIPS_INS_ADDS_S = 14;
	public static final int MIPS_INS_ADDS_U = 15;
	public static final int MIPS_INS_ADDU16 = 16;
	public static final int MIPS_INS_ADDUH = 17;
	public static final int MIPS_INS_ADDUH_R = 18;
	public static final int MIPS_INS_ADDU = 19;
	public static final int MIPS_INS_ADDU_S = 20;
	public static final int MIPS_INS_ADDVI = 21;
	public static final int MIPS_INS_ADDV = 22;
	public static final int MIPS_INS_ADDWC = 23;
	public static final int MIPS_INS_ADD_A = 24;
	public static final int MIPS_INS_ADDI = 25;
	public static final int MIPS_INS_ADDIU = 26;
	public static final int MIPS_INS_ALIGN = 27;
	public static final int MIPS_INS_ALUIPC = 28;
	public static final int MIPS_INS_AND = 29;
	public static final int MIPS_INS_AND16 = 30;
	public static final int MIPS_INS_ANDI16 = 31;
	public static final int MIPS_INS_ANDI = 32;
	public static final int MIPS_INS_APPEND = 33;
	public static final int MIPS_INS_ASUB_S = 34;
	public static final int MIPS_INS_ASUB_U = 35;
	public static final int MIPS_INS_AUI = 36;
	public static final int MIPS_INS_AUIPC = 37;
	public static final int MIPS_INS_AVER_S = 38;
	public static final int MIPS_INS_AVER_U = 39;
	public static final int MIPS_INS_AVE_S = 40;
	public static final int MIPS_INS_AVE_U = 41;
	public static final int MIPS_INS_B16 = 42;
	public static final int MIPS_INS_BADDU = 43;
	public static final int MIPS_INS_BAL = 44;
	public static final int MIPS_INS_BALC = 45;
	public static final int MIPS_INS_BALIGN = 46;
	public static final int MIPS_INS_BBIT0 = 47;
	public static final int MIPS_INS_BBIT032 = 48;
	public static final int MIPS_INS_BBIT1 = 49;
	public static final int MIPS_INS_BBIT132 = 50;
	public static final int MIPS_INS_BC = 51;
	public static final int MIPS_INS_BC0F = 52;
	public static final int MIPS_INS_BC0FL = 53;
	public static final int MIPS_INS_BC0T = 54;
	public static final int MIPS_INS_BC0TL = 55;
	public static final int MIPS_INS_BC1EQZ = 56;
	public static final int MIPS_INS_BC1F = 57;
	public static final int MIPS_INS_BC1FL = 58;
	public static final int MIPS_INS_BC1NEZ = 59;
	public static final int MIPS_INS_BC1T = 60;
	public static final int MIPS_INS_BC1TL = 61;
	public static final int MIPS_INS_BC2EQZ = 62;
	public static final int MIPS_INS_BC2F = 63;
	public static final int MIPS_INS_BC2FL = 64;
	public static final int MIPS_INS_BC2NEZ = 65;
	public static final int MIPS_INS_BC2T = 66;
	public static final int MIPS_INS_BC2TL = 67;
	public static final int MIPS_INS_BC3F = 68;
	public static final int MIPS_INS_BC3FL = 69;
	public static final int MIPS_INS_BC3T = 70;
	public static final int MIPS_INS_BC3TL = 71;
	public static final int MIPS_INS_BCLRI = 72;
	public static final int MIPS_INS_BCLR = 73;
	public static final int MIPS_INS_BEQ = 74;
	public static final int MIPS_INS_BEQC = 75;
	public static final int MIPS_INS_BEQL = 76;
	public static final int MIPS_INS_BEQZ16 = 77;
	public static final int MIPS_INS_BEQZALC = 78;
	public static final int MIPS_INS_BEQZC = 79;
	public static final int MIPS_INS_BGEC = 80;
	public static final int MIPS_INS_BGEUC = 81;
	public static final int MIPS_INS_BGEZ = 82;
	public static final int MIPS_INS_BGEZAL = 83;
	public static final int MIPS_INS_BGEZALC = 84;
	public static final int MIPS_INS_BGEZALL = 85;
	public static final int MIPS_INS_BGEZALS = 86;
	public static final int MIPS_INS_BGEZC = 87;
	public static final int MIPS_INS_BGEZL = 88;
	public static final int MIPS_INS_BGTZ = 89;
	public static final int MIPS_INS_BGTZALC = 90;
	public static final int MIPS_INS_BGTZC = 91;
	public static final int MIPS_INS_BGTZL = 92;
	public static final int MIPS_INS_BINSLI = 93;
	public static final int MIPS_INS_BINSL = 94;
	public static final int MIPS_INS_BINSRI = 95;
	public static final int MIPS_INS_BINSR = 96;
	public static final int MIPS_INS_BITREV = 97;
	public static final int MIPS_INS_BITSWAP = 98;
	public static final int MIPS_INS_BLEZ = 99;
	public static final int MIPS_INS_BLEZALC = 100;
	public static final int MIPS_INS_BLEZC = 101;
	public static final int MIPS_INS_BLEZL = 102;
	public static final int MIPS_INS_BLTC = 103;
	public static final int MIPS_INS_BLTUC = 104;
	public static final int MIPS_INS_BLTZ = 105;
	public static final int MIPS_INS_BLTZAL = 106;
	public static final int MIPS_INS_BLTZALC = 107;
	public static final int MIPS_INS_BLTZALL = 108;
	public static final int MIPS_INS_BLTZALS = 109;
	public static final int MIPS_INS_BLTZC = 110;
	public static final int MIPS_INS_BLTZL = 111;
	public static final int MIPS_INS_BMNZI = 112;
	public static final int MIPS_INS_BMNZ = 113;
	public static final int MIPS_INS_BMZI = 114;
	public static final int MIPS_INS_BMZ = 115;
	public static final int MIPS_INS_BNE = 116;
	public static final int MIPS_INS_BNEC = 117;
	public static final int MIPS_INS_BNEGI = 118;
	public static final int MIPS_INS_BNEG = 119;
	public static final int MIPS_INS_BNEL = 120;
	public static final int MIPS_INS_BNEZ16 = 121;
	public static final int MIPS_INS_BNEZALC = 122;
	public static final int MIPS_INS_BNEZC = 123;
	public static final int MIPS_INS_BNVC = 124;
	public static final int MIPS_INS_BNZ = 125;
	public static final int MIPS_INS_BOVC = 126;
	public static final int MIPS_INS_BPOSGE32 = 127;
	public static final int MIPS_INS_BREAK = 128;
	public static final int MIPS_INS_BREAK16 = 129;
	public static final int MIPS_INS_BSELI = 130;
	public static final int MIPS_INS_BSEL = 131;
	public static final int MIPS_INS_BSETI = 132;
	public static final int MIPS_INS_BSET = 133;
	public static final int MIPS_INS_BZ = 134;
	public static final int MIPS_INS_BEQZ = 135;
	public static final int MIPS_INS_B = 136;
	public static final int MIPS_INS_BNEZ = 137;
	public static final int MIPS_INS_BTEQZ = 138;
	public static final int MIPS_INS_BTNEZ = 139;
	public static final int MIPS_INS_CACHE = 140;
	public static final int MIPS_INS_CEIL = 141;
	public static final int MIPS_INS_CEQI = 142;
	public static final int MIPS_INS_CEQ = 143;
	public static final int MIPS_INS_CFC1 = 144;
	public static final int MIPS_INS_CFCMSA = 145;
	public static final int MIPS_INS_CINS = 146;
	public static final int MIPS_INS_CINS32 = 147;
	public static final int MIPS_INS_CLASS = 148;
	public static final int MIPS_INS_CLEI_S = 149;
	public static final int MIPS_INS_CLEI_U = 150;
	public static final int MIPS_INS_CLE_S = 151;
	public static final int MIPS_INS_CLE_U = 152;
	public static final int MIPS_INS_CLO = 153;
	public static final int MIPS_INS_CLTI_S = 154;
	public static final int MIPS_INS_CLTI_U = 155;
	public static final int MIPS_INS_CLT_S = 156;
	public static final int MIPS_INS_CLT_U = 157;
	public static final int MIPS_INS_CLZ = 158;
	public static final int MIPS_INS_CMPGDU = 159;
	public static final int MIPS_INS_CMPGU = 160;
	public static final int MIPS_INS_CMPU = 161;
	public static final int MIPS_INS_CMP = 162;
	public static final int MIPS_INS_COPY_S = 163;
	public static final int MIPS_INS_COPY_U = 164;
	public static final int MIPS_INS_CTC1 = 165;
	public static final int MIPS_INS_CTCMSA = 166;
	public static final int MIPS_INS_CVT = 167;
	public static final int MIPS_INS_C = 168;
	public static final int MIPS_INS_CMPI = 169;
	public static final int MIPS_INS_DADD = 170;
	public static final int MIPS_INS_DADDI = 171;
	public static final int MIPS_INS_DADDIU = 172;
	public static final int MIPS_INS_DADDU = 173;
	public static final int MIPS_INS_DAHI = 174;
	public static final int MIPS_INS_DALIGN = 175;
	public static final int MIPS_INS_DATI = 176;
	public static final int MIPS_INS_DAUI = 177;
	public static final int MIPS_INS_DBITSWAP = 178;
	public static final int MIPS_INS_DCLO = 179;
	public static final int MIPS_INS_DCLZ = 180;
	public static final int MIPS_INS_DDIV = 181;
	public static final int MIPS_INS_DDIVU = 182;
	public static final int MIPS_INS_DERET = 183;
	public static final int MIPS_INS_DEXT = 184;
	public static final int MIPS_INS_DEXTM = 185;
	public static final int MIPS_INS_DEXTU = 186;
	public static final int MIPS_INS_DI = 187;
	public static final int MIPS_INS_DINS = 188;
	public static final int MIPS_INS_DINSM = 189;
	public static final int MIPS_INS_DINSU = 190;
	public static final int MIPS_INS_DIV = 191;
	public static final int MIPS_INS_DIVU = 192;
	public static final int MIPS_INS_DIV_S = 193;
	public static final int MIPS_INS_DIV_U = 194;
	public static final int MIPS_INS_DLSA = 195;
	public static final int MIPS_INS_DMFC0 = 196;
	public static final int MIPS_INS_DMFC1 = 197;
	public static final int MIPS_INS_DMFC2 = 198;
	public static final int MIPS_INS_DMOD = 199;
	public static final int MIPS_INS_DMODU = 200;
	public static final int MIPS_INS_DMTC0 = 201;
	public static final int MIPS_INS_DMTC1 = 202;
	public static final int MIPS_INS_DMTC2 = 203;
	public static final int MIPS_INS_DMUH = 204;
	public static final int MIPS_INS_DMUHU = 205;
	public static final int MIPS_INS_DMUL = 206;
	public static final int MIPS_INS_DMULT = 207;
	public static final int MIPS_INS_DMULTU = 208;
	public static final int MIPS_INS_DMULU = 209;
	public static final int MIPS_INS_DOTP_S = 210;
	public static final int MIPS_INS_DOTP_U = 211;
	public static final int MIPS_INS_DPADD_S = 212;
	public static final int MIPS_INS_DPADD_U = 213;
	public static final int MIPS_INS_DPAQX_SA = 214;
	public static final int MIPS_INS_DPAQX_S = 215;
	public static final int MIPS_INS_DPAQ_SA = 216;
	public static final int MIPS_INS_DPAQ_S = 217;
	public static final int MIPS_INS_DPAU = 218;
	public static final int MIPS_INS_DPAX = 219;
	public static final int MIPS_INS_DPA = 220;
	public static final int MIPS_INS_DPOP = 221;
	public static final int MIPS_INS_DPSQX_SA = 222;
	public static final int MIPS_INS_DPSQX_S = 223;
	public static final int MIPS_INS_DPSQ_SA = 224;
	public static final int MIPS_INS_DPSQ_S = 225;
	public static final int MIPS_INS_DPSUB_S = 226;
	public static final int MIPS_INS_DPSUB_U = 227;
	public static final int MIPS_INS_DPSU = 228;
	public static final int MIPS_INS_DPSX = 229;
	public static final int MIPS_INS_DPS = 230;
	public static final int MIPS_INS_DROTR = 231;
	public static final int MIPS_INS_DROTR32 = 232;
	public static final int MIPS_INS_DROTRV = 233;
	public static final int MIPS_INS_DSBH = 234;
	public static final int MIPS_INS_DSHD = 235;
	public static final int MIPS_INS_DSLL = 236;
	public static final int MIPS_INS_DSLL32 = 237;
	public static final int MIPS_INS_DSLLV = 238;
	public static final int MIPS_INS_DSRA = 239;
	public static final int MIPS_INS_DSRA32 = 240;
	public static final int MIPS_INS_DSRAV = 241;
	public static final int MIPS_INS_DSRL = 242;
	public static final int MIPS_INS_DSRL32 = 243;
	public static final int MIPS_INS_DSRLV = 244;
	public static final int MIPS_INS_DSUB = 245;
	public static final int MIPS_INS_DSUBU = 246;
	public static final int MIPS_INS_EHB = 247;
	public static final int MIPS_INS_EI = 248;
	public static final int MIPS_INS_ERET = 249;
	public static final int MIPS_INS_EXT = 250;
	public static final int MIPS_INS_EXTP = 251;
	public static final int MIPS_INS_EXTPDP = 252;
	public static final int MIPS_INS_EXTPDPV = 253;
	public static final int MIPS_INS_EXTPV = 254;
	public static final int MIPS_INS_EXTRV_RS = 255;
	public static final int MIPS_INS_EXTRV_R = 256;
	public static final int MIPS_INS_EXTRV_S = 257;
	public static final int MIPS_INS_EXTRV = 258;
	public static final int MIPS_INS_EXTR_RS = 259;
	public static final int MIPS_INS_EXTR_R = 260;
	public static final int MIPS_INS_EXTR_S = 261;
	public static final int MIPS_INS_EXTR = 262;
	public static final int MIPS_INS_EXTS = 263;
	public static final int MIPS_INS_EXTS32 = 264;
	public static final int MIPS_INS_ABS = 265;
	public static final int MIPS_INS_FADD = 266;
	public static final int MIPS_INS_FCAF = 267;
	public static final int MIPS_INS_FCEQ = 268;
	public static final int MIPS_INS_FCLASS = 269;
	public static final int MIPS_INS_FCLE = 270;
	public static final int MIPS_INS_FCLT = 271;
	public static final int MIPS_INS_FCNE = 272;
	public static final int MIPS_INS_FCOR = 273;
	public static final int MIPS_INS_FCUEQ = 274;
	public static final int MIPS_INS_FCULE = 275;
	public static final int MIPS_INS_FCULT = 276;
	public static final int MIPS_INS_FCUNE = 277;
	public static final int MIPS_INS_FCUN = 278;
	public static final int MIPS_INS_FDIV = 279;
	public static final int MIPS_INS_FEXDO = 280;
	public static final int MIPS_INS_FEXP2 = 281;
	public static final int MIPS_INS_FEXUPL = 282;
	public static final int MIPS_INS_FEXUPR = 283;
	public static final int MIPS_INS_FFINT_S = 284;
	public static final int MIPS_INS_FFINT_U = 285;
	public static final int MIPS_INS_FFQL = 286;
	public static final int MIPS_INS_FFQR = 287;
	public static final int MIPS_INS_FILL = 288;
	public static final int MIPS_INS_FLOG2 = 289;
	public static final int MIPS_INS_FLOOR = 290;
	public static final int MIPS_INS_FMADD = 291;
	public static final int MIPS_INS_FMAX_A = 292;
	public static final int MIPS_INS_FMAX = 293;
	public static final int MIPS_INS_FMIN_A = 294;
	public static final int MIPS_INS_FMIN = 295;
	public static final int MIPS_INS_MOV = 296;
	public static final int MIPS_INS_FMSUB = 297;
	public static final int MIPS_INS_FMUL = 298;
	public static final int MIPS_INS_MUL = 299;
	public static final int MIPS_INS_NEG = 300;
	public static final int MIPS_INS_FRCP = 301;
	public static final int MIPS_INS_FRINT = 302;
	public static final int MIPS_INS_FRSQRT = 303;
	public static final int MIPS_INS_FSAF = 304;
	public static final int MIPS_INS_FSEQ = 305;
	public static final int MIPS_INS_FSLE = 306;
	public static final int MIPS_INS_FSLT = 307;
	public static final int MIPS_INS_FSNE = 308;
	public static final int MIPS_INS_FSOR = 309;
	public static final int MIPS_INS_FSQRT = 310;
	public static final int MIPS_INS_SQRT = 311;
	public static final int MIPS_INS_FSUB = 312;
	public static final int MIPS_INS_SUB = 313;
	public static final int MIPS_INS_FSUEQ = 314;
	public static final int MIPS_INS_FSULE = 315;
	public static final int MIPS_INS_FSULT = 316;
	public static final int MIPS_INS_FSUNE = 317;
	public static final int MIPS_INS_FSUN = 318;
	public static final int MIPS_INS_FTINT_S = 319;
	public static final int MIPS_INS_FTINT_U = 320;
	public static final int MIPS_INS_FTQ = 321;
	public static final int MIPS_INS_FTRUNC_S = 322;
	public static final int MIPS_INS_FTRUNC_U = 323;
	public static final int MIPS_INS_HADD_S = 324;
	public static final int MIPS_INS_HADD_U = 325;
	public static final int MIPS_INS_HSUB_S = 326;
	public static final int MIPS_INS_HSUB_U = 327;
	public static final int MIPS_INS_ILVEV = 328;
	public static final int MIPS_INS_ILVL = 329;
	public static final int MIPS_INS_ILVOD = 330;
	public static final int MIPS_INS_ILVR = 331;
	public static final int MIPS_INS_INS = 332;
	public static final int MIPS_INS_INSERT = 333;
	public static final int MIPS_INS_INSV = 334;
	public static final int MIPS_INS_INSVE = 335;
	public static final int MIPS_INS_J = 336;
	public static final int MIPS_INS_JAL = 337;
	public static final int MIPS_INS_JALR = 338;
	public static final int MIPS_INS_JALRS16 = 339;
	public static final int MIPS_INS_JALRS = 340;
	public static final int MIPS_INS_JALS = 341;
	public static final int MIPS_INS_JALX = 342;
	public static final int MIPS_INS_JIALC = 343;
	public static final int MIPS_INS_JIC = 344;
	public static final int MIPS_INS_JR = 345;
	public static final int MIPS_INS_JR16 = 346;
	public static final int MIPS_INS_JRADDIUSP = 347;
	public static final int MIPS_INS_JRC = 348;
	public static final int MIPS_INS_JALRC = 349;
	public static final int MIPS_INS_LB = 350;
	public static final int MIPS_INS_LBU16 = 351;
	public static final int MIPS_INS_LBUX = 352;
	public static final int MIPS_INS_LBU = 353;
	public static final int MIPS_INS_LD = 354;
	public static final int MIPS_INS_LDC1 = 355;
	public static final int MIPS_INS_LDC2 = 356;
	public static final int MIPS_INS_LDC3 = 357;
	public static final int MIPS_INS_LDI = 358;
	public static final int MIPS_INS_LDL = 359;
	public static final int MIPS_INS_LDPC = 360;
	public static final int MIPS_INS_LDR = 361;
	public static final int MIPS_INS_LDXC1 = 362;
	public static final int MIPS_INS_LH = 363;
	public static final int MIPS_INS_LHU16 = 364;
	public static final int MIPS_INS_LHX = 365;
	public static final int MIPS_INS_LHU = 366;
	public static final int MIPS_INS_LI16 = 367;
	public static final int MIPS_INS_LL = 368;
	public static final int MIPS_INS_LLD = 369;
	public static final int MIPS_INS_LSA = 370;
	public static final int MIPS_INS_LUXC1 = 371;
	public static final int MIPS_INS_LUI = 372;
	public static final int MIPS_INS_LW = 373;
	public static final int MIPS_INS_LW16 = 374;
	public static final int MIPS_INS_LWC1 = 375;
	public static final int MIPS_INS_LWC2 = 376;
	public static final int MIPS_INS_LWC3 = 377;
	public static final int MIPS_INS_LWL = 378;
	public static final int MIPS_INS_LWM16 = 379;
	public static final int MIPS_INS_LWM32 = 380;
	public static final int MIPS_INS_LWPC = 381;
	public static final int MIPS_INS_LWP = 382;
	public static final int MIPS_INS_LWR = 383;
	public static final int MIPS_INS_LWUPC = 384;
	public static final int MIPS_INS_LWU = 385;
	public static final int MIPS_INS_LWX = 386;
	public static final int MIPS_INS_LWXC1 = 387;
	public static final int MIPS_INS_LWXS = 388;
	public static final int MIPS_INS_LI = 389;
	public static final int MIPS_INS_MADD = 390;
	public static final int MIPS_INS_MADDF = 391;
	public static final int MIPS_INS_MADDR_Q = 392;
	public static final int MIPS_INS_MADDU = 393;
	public static final int MIPS_INS_MADDV = 394;
	public static final int MIPS_INS_MADD_Q = 395;
	public static final int MIPS_INS_MAQ_SA = 396;
	public static final int MIPS_INS_MAQ_S = 397;
	public static final int MIPS_INS_MAXA = 398;
	public static final int MIPS_INS_MAXI_S = 399;
	public static final int MIPS_INS_MAXI_U = 400;
	public static final int MIPS_INS_MAX_A = 401;
	public static final int MIPS_INS_MAX = 402;
	public static final int MIPS_INS_MAX_S = 403;
	public static final int MIPS_INS_MAX_U = 404;
	public static final int MIPS_INS_MFC0 = 405;
	public static final int MIPS_INS_MFC1 = 406;
	public static final int MIPS_INS_MFC2 = 407;
	public static final int MIPS_INS_MFHC1 = 408;
	public static final int MIPS_INS_MFHI = 409;
	public static final int MIPS_INS_MFLO = 410;
	public static final int MIPS_INS_MINA = 411;
	public static final int MIPS_INS_MINI_S = 412;
	public static final int MIPS_INS_MINI_U = 413;
	public static final int MIPS_INS_MIN_A = 414;
	public static final int MIPS_INS_MIN = 415;
	public static final int MIPS_INS_MIN_S = 416;
	public static final int MIPS_INS_MIN_U = 417;
	public static final int MIPS_INS_MOD = 418;
	public static final int MIPS_INS_MODSUB = 419;
	public static final int MIPS_INS_MODU = 420;
	public static final int MIPS_INS_MOD_S = 421;
	public static final int MIPS_INS_MOD_U = 422;
	public static final int MIPS_INS_MOVE = 423;
	public static final int MIPS_INS_MOVEP = 424;
	public static final int MIPS_INS_MOVF = 425;
	public static final int MIPS_INS_MOVN = 426;
	public static final int MIPS_INS_MOVT = 427;
	public static final int MIPS_INS_MOVZ = 428;
	public static final int MIPS_INS_MSUB = 429;
	public static final int MIPS_INS_MSUBF = 430;
	public static final int MIPS_INS_MSUBR_Q = 431;
	public static final int MIPS_INS_MSUBU = 432;
	public static final int MIPS_INS_MSUBV = 433;
	public static final int MIPS_INS_MSUB_Q = 434;
	public static final int MIPS_INS_MTC0 = 435;
	public static final int MIPS_INS_MTC1 = 436;
	public static final int MIPS_INS_MTC2 = 437;
	public static final int MIPS_INS_MTHC1 = 438;
	public static final int MIPS_INS_MTHI = 439;
	public static final int MIPS_INS_MTHLIP = 440;
	public static final int MIPS_INS_MTLO = 441;
	public static final int MIPS_INS_MTM0 = 442;
	public static final int MIPS_INS_MTM1 = 443;
	public static final int MIPS_INS_MTM2 = 444;
	public static final int MIPS_INS_MTP0 = 445;
	public static final int MIPS_INS_MTP1 = 446;
	public static final int MIPS_INS_MTP2 = 447;
	public static final int MIPS_INS_MUH = 448;
	public static final int MIPS_INS_MUHU = 449;
	public static final int MIPS_INS_MULEQ_S = 450;
	public static final int MIPS_INS_MULEU_S = 451;
	public static final int MIPS_INS_MULQ_RS = 452;
	public static final int MIPS_INS_MULQ_S = 453;
	public static final int MIPS_INS_MULR_Q = 454;
	public static final int MIPS_INS_MULSAQ_S = 455;
	public static final int MIPS_INS_MULSA = 456;
	public static final int MIPS_INS_MULT = 457;
	public static final int MIPS_INS_MULTU = 458;
	public static final int MIPS_INS_MULU = 459;
	public static final int MIPS_INS_MULV = 460;
	public static final int MIPS_INS_MUL_Q = 461;
	public static final int MIPS_INS_MUL_S = 462;
	public static final int MIPS_INS_NLOC = 463;
	public static final int MIPS_INS_NLZC = 464;
	public static final int MIPS_INS_NMADD = 465;
	public static final int MIPS_INS_NMSUB = 466;
	public static final int MIPS_INS_NOR = 467;
	public static final int MIPS_INS_NORI = 468;
	public static final int MIPS_INS_NOT16 = 469;
	public static final int MIPS_INS_NOT = 470;
	public static final int MIPS_INS_OR = 471;
	public static final int MIPS_INS_OR16 = 472;
	public static final int MIPS_INS_ORI = 473;
	public static final int MIPS_INS_PACKRL = 474;
	public static final int MIPS_INS_PAUSE = 475;
	public static final int MIPS_INS_PCKEV = 476;
	public static final int MIPS_INS_PCKOD = 477;
	public static final int MIPS_INS_PCNT = 478;
	public static final int MIPS_INS_PICK = 479;
	public static final int MIPS_INS_POP = 480;
	public static final int MIPS_INS_PRECEQU = 481;
	public static final int MIPS_INS_PRECEQ = 482;
	public static final int MIPS_INS_PRECEU = 483;
	public static final int MIPS_INS_PRECRQU_S = 484;
	public static final int MIPS_INS_PRECRQ = 485;
	public static final int MIPS_INS_PRECRQ_RS = 486;
	public static final int MIPS_INS_PRECR = 487;
	public static final int MIPS_INS_PRECR_SRA = 488;
	public static final int MIPS_INS_PRECR_SRA_R = 489;
	public static final int MIPS_INS_PREF = 490;
	public static final int MIPS_INS_PREPEND = 491;
	public static final int MIPS_INS_RADDU = 492;
	public static final int MIPS_INS_RDDSP = 493;
	public static final int MIPS_INS_RDHWR = 494;
	public static final int MIPS_INS_REPLV = 495;
	public static final int MIPS_INS_REPL = 496;
	public static final int MIPS_INS_RINT = 497;
	public static final int MIPS_INS_ROTR = 498;
	public static final int MIPS_INS_ROTRV = 499;
	public static final int MIPS_INS_ROUND = 500;
	public static final int MIPS_INS_SAT_S = 501;
	public static final int MIPS_INS_SAT_U = 502;
	public static final int MIPS_INS_SB = 503;
	public static final int MIPS_INS_SB16 = 504;
	public static final int MIPS_INS_SC = 505;
	public static final int MIPS_INS_SCD = 506;
	public static final int MIPS_INS_SD = 507;
	public static final int MIPS_INS_SDBBP = 508;
	public static final int MIPS_INS_SDBBP16 = 509;
	public static final int MIPS_INS_SDC1 = 510;
	public static final int MIPS_INS_SDC2 = 511;
	public static final int MIPS_INS_SDC3 = 512;
	public static final int MIPS_INS_SDL = 513;
	public static final int MIPS_INS_SDR = 514;
	public static final int MIPS_INS_SDXC1 = 515;
	public static final int MIPS_INS_SEB = 516;
	public static final int MIPS_INS_SEH = 517;
	public static final int MIPS_INS_SELEQZ = 518;
	public static final int MIPS_INS_SELNEZ = 519;
	public static final int MIPS_INS_SEL = 520;
	public static final int MIPS_INS_SEQ = 521;
	public static final int MIPS_INS_SEQI = 522;
	public static final int MIPS_INS_SH = 523;
	public static final int MIPS_INS_SH16 = 524;
	public static final int MIPS_INS_SHF = 525;
	public static final int MIPS_INS_SHILO = 526;
	public static final int MIPS_INS_SHILOV = 527;
	public static final int MIPS_INS_SHLLV = 528;
	public static final int MIPS_INS_SHLLV_S = 529;
	public static final int MIPS_INS_SHLL = 530;
	public static final int MIPS_INS_SHLL_S = 531;
	public static final int MIPS_INS_SHRAV = 532;
	public static final int MIPS_INS_SHRAV_R = 533;
	public static final int MIPS_INS_SHRA = 534;
	public static final int MIPS_INS_SHRA_R = 535;
	public static final int MIPS_INS_SHRLV = 536;
	public static final int MIPS_INS_SHRL = 537;
	public static final int MIPS_INS_SLDI = 538;
	public static final int MIPS_INS_SLD = 539;
	public static final int MIPS_INS_SLL = 540;
	public static final int MIPS_INS_SLL16 = 541;
	public static final int MIPS_INS_SLLI = 542;
	public static final int MIPS_INS_SLLV = 543;
	public static final int MIPS_INS_SLT = 544;
	public static final int MIPS_INS_SLTI = 545;
	public static final int MIPS_INS_SLTIU = 546;
	public static final int MIPS_INS_SLTU = 547;
	public static final int MIPS_INS_SNE = 548;
	public static final int MIPS_INS_SNEI = 549;
	public static final int MIPS_INS_SPLATI = 550;
	public static final int MIPS_INS_SPLAT = 551;
	public static final int MIPS_INS_SRA = 552;
	public static final int MIPS_INS_SRAI = 553;
	public static final int MIPS_INS_SRARI = 554;
	public static final int MIPS_INS_SRAR = 555;
	public static final int MIPS_INS_SRAV = 556;
	public static final int MIPS_INS_SRL = 557;
	public static final int MIPS_INS_SRL16 = 558;
	public static final int MIPS_INS_SRLI = 559;
	public static final int MIPS_INS_SRLRI = 560;
	public static final int MIPS_INS_SRLR = 561;
	public static final int MIPS_INS_SRLV = 562;
	public static final int MIPS_INS_SSNOP = 563;
	public static final int MIPS_INS_ST = 564;
	public static final int MIPS_INS_SUBQH = 565;
	public static final int MIPS_INS_SUBQH_R = 566;
	public static final int MIPS_INS_SUBQ = 567;
	public static final int MIPS_INS_SUBQ_S = 568;
	public static final int MIPS_INS_SUBSUS_U = 569;
	public static final int MIPS_INS_SUBSUU_S = 570;
	public static final int MIPS_INS_SUBS_S = 571;
	public static final int MIPS_INS_SUBS_U = 572;
	public static final int MIPS_INS_SUBU16 = 573;
	public static final int MIPS_INS_SUBUH = 574;
	public static final int MIPS_INS_SUBUH_R = 575;
	public static final int MIPS_INS_SUBU = 576;
	public static final int MIPS_INS_SUBU_S = 577;
	public static final int MIPS_INS_SUBVI = 578;
	public static final int MIPS_INS_SUBV = 579;
	public static final int MIPS_INS_SUXC1 = 580;
	public static final int MIPS_INS_SW = 581;
	public static final int MIPS_INS_SW16 = 582;
	public static final int MIPS_INS_SWC1 = 583;
	public static final int MIPS_INS_SWC2 = 584;
	public static final int MIPS_INS_SWC3 = 585;
	public static final int MIPS_INS_SWL = 586;
	public static final int MIPS_INS_SWM16 = 587;
	public static final int MIPS_INS_SWM32 = 588;
	public static final int MIPS_INS_SWP = 589;
	public static final int MIPS_INS_SWR = 590;
	public static final int MIPS_INS_SWXC1 = 591;
	public static final int MIPS_INS_SYNC = 592;
	public static final int MIPS_INS_SYNCI = 593;
	public static final int MIPS_INS_SYSCALL = 594;
	public static final int MIPS_INS_TEQ = 595;
	public static final int MIPS_INS_TEQI = 596;
	public static final int MIPS_INS_TGE = 597;
	public static final int MIPS_INS_TGEI = 598;
	public static final int MIPS_INS_TGEIU = 599;
	public static final int MIPS_INS_TGEU = 600;
	public static final int MIPS_INS_TLBP = 601;
	public static final int MIPS_INS_TLBR = 602;
	public static final int MIPS_INS_TLBWI = 603;
	public static final int MIPS_INS_TLBWR = 604;
	public static final int MIPS_INS_TLT = 605;
	public static final int MIPS_INS_TLTI = 606;
	public static final int MIPS_INS_TLTIU = 607;
	public static final int MIPS_INS_TLTU = 608;
	public static final int MIPS_INS_TNE = 609;
	public static final int MIPS_INS_TNEI = 610;
	public static final int MIPS_INS_TRUNC = 611;
	public static final int MIPS_INS_V3MULU = 612;
	public static final int MIPS_INS_VMM0 = 613;
	public static final int MIPS_INS_VMULU = 614;
	public static final int MIPS_INS_VSHF = 615;
	public static final int MIPS_INS_WAIT = 616;
	public static final int MIPS_INS_WRDSP = 617;
	public static final int MIPS_INS_WSBH = 618;
	public static final int MIPS_INS_XOR = 619;
	public static final int MIPS_INS_XOR16 = 620;
	public static final int MIPS_INS_XORI = 621;

	// some alias instructions
	public static final int MIPS_INS_NOP = 622;
	public static final int MIPS_INS_NEGU = 623;

	// special instructions
	public static final int MIPS_INS_JALR_HB = 624;
	public static final int MIPS_INS_JR_HB = 625;
	public static final int MIPS_INS_ENDING = 626;

	// Group of MIPS instructions

	public static final int MIPS_GRP_INVALID = 0;

	// Generic groups
	public static final int MIPS_GRP_JUMP = 1;
	public static final int MIPS_GRP_CALL = 2;
	public static final int MIPS_GRP_RET = 3;
	public static final int MIPS_GRP_INT = 4;
	public static final int MIPS_GRP_IRET = 5;
	public static final int MIPS_GRP_PRIVILEGE = 6;
	public static final int MIPS_GRP_BRANCH_RELATIVE = 7;

	// Architecture-specific groups
	public static final int MIPS_GRP_BITCOUNT = 128;
	public static final int MIPS_GRP_DSP = 129;
	public static final int MIPS_GRP_DSPR2 = 130;
	public static final int MIPS_GRP_FPIDX = 131;
	public static final int MIPS_GRP_MSA = 132;
	public static final int MIPS_GRP_MIPS32R2 = 133;
	public static final int MIPS_GRP_MIPS64 = 134;
	public static final int MIPS_GRP_MIPS64R2 = 135;
	public static final int MIPS_GRP_SEINREG = 136;
	public static final int MIPS_GRP_STDENC = 137;
	public static final int MIPS_GRP_SWAP = 138;
	public static final int MIPS_GRP_MICROMIPS = 139;
	public static final int MIPS_GRP_MIPS16MODE = 140;
	public static final int MIPS_GRP_FP64BIT = 141;
	public static final int MIPS_GRP_NONANSFPMATH = 142;
	public static final int MIPS_GRP_NOTFP64BIT = 143;
	public static final int MIPS_GRP_NOTINMICROMIPS = 144;
	public static final int MIPS_GRP_NOTNACL = 145;
	public static final int MIPS_GRP_NOTMIPS32R6 = 146;
	public static final int MIPS_GRP_NOTMIPS64R6 = 147;
	public static final int MIPS_GRP_CNMIPS = 148;
	public static final int MIPS_GRP_MIPS32 = 149;
	public static final int MIPS_GRP_MIPS32R6 = 150;
	public static final int MIPS_GRP_MIPS64R6 = 151;
	public static final int MIPS_GRP_MIPS2 = 152;
	public static final int MIPS_GRP_MIPS3 = 153;
	public static final int MIPS_GRP_MIPS3_32 = 154;
	public static final int MIPS_GRP_MIPS3_32R2 = 155;
	public static final int MIPS_GRP_MIPS4_32 = 156;
	public static final int MIPS_GRP_MIPS4_32R2 = 157;
	public static final int MIPS_GRP_MIPS5_32R2 = 158;
	public static final int MIPS_GRP_GP32BIT = 159;
	public static final int MIPS_GRP_GP64BIT = 160;
	public static final int MIPS_GRP_ENDING = 161;
}