To compile Ocaml binding, Ocaml toolchain is needed. On Ubuntu Linux,
you can install Ocaml with:

    $ sudo apt-get install ocaml-nox

To compile Ocaml binding, simply run "make" on the command line.


This directory also contains some test code to show how to use Capstone API.

- test_basic.ml
  This code shows the most simple form of API where we only want to get basic
  information out of disassembled instruction, such as address, mnemonic and
  operand string.

- test_detail.ml:
  This code shows how to access to architecture-neutral information in disassembled
  instructions, such as implicit registers read/written, or groups of instructions
  that this instruction belong to.

- test_<arch>.ml
  These code show how to access architecture-specific information for each
  architecture.
