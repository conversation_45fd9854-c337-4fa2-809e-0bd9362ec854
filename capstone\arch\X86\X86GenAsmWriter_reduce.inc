/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Assembly Writer Source Fragment                                             *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine, http://www.capstone-engine.org */
/* By <PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2015 */

/// printInstruction - This method is automatically generated by tablegen
/// from the instruction set description.
static void printInstruction(MCInst *MI, SStream *O, MCRegisterInfo *MRI)
{
  static const uint32_t OpInfo[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    4714U,	// DBG_VALUE
    0U,	// REG_SEQUENCE
    0U,	// COPY
    4707U,	// BUNDLE
    4770U,	// LIFETIME_START
    4694U,	// LIFETIME_END
    0U,	// STACKMAP
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    0U,	// STATEPOINT
    0U,	// FRAME_ALLOC
    4785U,	// AAA
    8535U,	// AAD8i8
    9494U,	// AAM8i8
    5356U,	// AAS
    4385U,	// ACQUIRE_MOV16rm
    4385U,	// ACQUIRE_MOV32rm
    4385U,	// ACQUIRE_MOV64rm
    4385U,	// ACQUIRE_MOV8rm
    534777U,	// ADC16i16
    1067257U,	// ADC16mi
    1067257U,	// ADC16mi8
    1067257U,	// ADC16mr
    1599737U,	// ADC16ri
    1599737U,	// ADC16ri8
    1607929U,	// ADC16rm
    1599737U,	// ADC16rr
    2124025U,	// ADC16rr_REV
    2630195U,	// ADC32i32
    3162675U,	// ADC32mi
    3162675U,	// ADC32mi8
    3162675U,	// ADC32mr
    1598003U,	// ADC32ri
    1598003U,	// ADC32ri8
    1614387U,	// ADC32rm
    1598003U,	// ADC32rr
    2122291U,	// ADC32rr_REV
    3679654U,	// ADC64i32
    4212134U,	// ADC64mi32
    4212134U,	// ADC64mi8
    4212134U,	// ADC64mr
    1598886U,	// ADC64ri32
    1598886U,	// ADC64ri8
    1623462U,	// ADC64rm
    1598886U,	// ADC64rr
    2123174U,	// ADC64rr_REV
    4726892U,	// ADC8i8
    5259372U,	// ADC8mi
    5259372U,	// ADC8mi8
    5259372U,	// ADC8mr
    1597548U,	// ADC8ri
    1597548U,	// ADC8ri8
    57452U,	// ADC8rm
    1597548U,	// ADC8rr
    2121836U,	// ADC8rr_REV
    2139365U,	// ADCX32rm
    2122981U,	// ADCX32rr
    2148376U,	// ADCX64rm
    2123800U,	// ADCX64rr
    534802U,	// ADD16i16
    1067282U,	// ADD16mi
    1067282U,	// ADD16mi8
    1067282U,	// ADD16mr
    1599762U,	// ADD16ri
    1599762U,	// ADD16ri8
    0U,	// ADD16ri8_DB
    0U,	// ADD16ri_DB
    1607954U,	// ADD16rm
    1599762U,	// ADD16rr
    0U,	// ADD16rr_DB
    2124050U,	// ADD16rr_REV
    2630229U,	// ADD32i32
    3162709U,	// ADD32mi
    3162709U,	// ADD32mi8
    3162709U,	// ADD32mr
    1598037U,	// ADD32ri
    1598037U,	// ADD32ri8
    0U,	// ADD32ri8_DB
    0U,	// ADD32ri_DB
    1614421U,	// ADD32rm
    1598037U,	// ADD32rr
    0U,	// ADD32rr_DB
    2122325U,	// ADD32rr_REV
    3679688U,	// ADD64i32
    4212168U,	// ADD64mi32
    4212168U,	// ADD64mi8
    4212168U,	// ADD64mr
    1598920U,	// ADD64ri32
    0U,	// ADD64ri32_DB
    1598920U,	// ADD64ri8
    0U,	// ADD64ri8_DB
    1623496U,	// ADD64rm
    1598920U,	// ADD64rr
    0U,	// ADD64rr_DB
    2123208U,	// ADD64rr_REV
    4726911U,	// ADD8i8
    5259391U,	// ADD8mi
    5259391U,	// ADD8mi8
    5259391U,	// ADD8mr
    1597567U,	// ADD8ri
    1597567U,	// ADD8ri8
    57471U,	// ADD8rm
    1597567U,	// ADD8rr
    2121855U,	// ADD8rr_REV
    4724U,	// ADJCALLSTACKDOWN32
    4724U,	// ADJCALLSTACKDOWN64
    4742U,	// ADJCALLSTACKUP32
    4742U,	// ADJCALLSTACKUP64
    66810U,	// ADOX32rm
    22619386U,	// ADOX32rr
    84013U,	// ADOX64rm
    22620205U,	// ADOX64rr
    534827U,	// AND16i16
    1067307U,	// AND16mi
    1067307U,	// AND16mi8
    1067307U,	// AND16mr
    1599787U,	// AND16ri
    1599787U,	// AND16ri8
    1607979U,	// AND16rm
    1599787U,	// AND16rr
    2124075U,	// AND16rr_REV
    2630254U,	// AND32i32
    3162734U,	// AND32mi
    3162734U,	// AND32mi8
    3162734U,	// AND32mr
    1598062U,	// AND32ri
    1598062U,	// AND32ri8
    1614446U,	// AND32rm
    1598062U,	// AND32rr
    2122350U,	// AND32rr_REV
    3679713U,	// AND64i32
    4212193U,	// AND64mi32
    4212193U,	// AND64mi8
    4212193U,	// AND64mr
    1598945U,	// AND64ri32
    1598945U,	// AND64ri8
    1623521U,	// AND64rm
    1598945U,	// AND64rr
    2123233U,	// AND64rr_REV
    4726917U,	// AND8i8
    5259397U,	// AND8mi
    5259397U,	// AND8mi8
    5259397U,	// AND8mr
    1597573U,	// AND8ri
    1597573U,	// AND8ri8
    57477U,	// AND8rm
    1597573U,	// AND8rr
    2121861U,	// AND8rr_REV
    35169133U,	// ANDN32rm
    35152749U,	// ANDN32rr
    35178193U,	// ANDN64rm
    35153617U,	// ANDN64rr
    1065915U,	// ARPL16mr
    22619067U,	// ARPL16rr
    6382598U,	// BEXTR32rm
    35152902U,	// BEXTR32rr
    6907730U,	// BEXTR64rm
    35153746U,	// BEXTR64rr
    6383719U,	// BEXTRI32mi
    35154023U,	// BEXTRI32ri
    6908007U,	// BEXTRI64mi
    35154023U,	// BEXTRI64ri
    66360U,	// BLCFILL32rm
    22618936U,	// BLCFILL32rr
    82744U,	// BLCFILL64rm
    22618936U,	// BLCFILL64rr
    66028U,	// BLCI32rm
    22618604U,	// BLCI32rr
    82412U,	// BLCI64rm
    22618604U,	// BLCI64rr
    65857U,	// BLCIC32rm
    22618433U,	// BLCIC32rr
    82241U,	// BLCIC64rm
    22618433U,	// BLCIC64rr
    66034U,	// BLCMSK32rm
    22618610U,	// BLCMSK32rr
    82418U,	// BLCMSK64rm
    22618610U,	// BLCMSK64rr
    67694U,	// BLCS32rm
    22620270U,	// BLCS32rr
    84078U,	// BLCS64rm
    22620270U,	// BLCS64rr
    66369U,	// BLSFILL32rm
    22618945U,	// BLSFILL32rr
    82753U,	// BLSFILL64rm
    22618945U,	// BLSFILL64rr
    66322U,	// BLSI32rm
    22618898U,	// BLSI32rr
    83589U,	// BLSI64rm
    22619781U,	// BLSI64rr
    65864U,	// BLSIC32rm
    22618440U,	// BLSIC32rr
    82248U,	// BLSIC64rm
    22618440U,	// BLSIC64rr
    66333U,	// BLSMSK32rm
    22618909U,	// BLSMSK32rr
    83596U,	// BLSMSK64rm
    22619788U,	// BLSMSK64rr
    66547U,	// BLSR32rm
    22619123U,	// BLSR32rr
    83775U,	// BLSR64rm
    22619967U,	// BLSR64rr
    65911U,	// BOUNDS16rm
    82295U,	// BOUNDS32rm
    100717U,	// BSF16rm
    22620525U,	// BSF16rr
    66278U,	// BSF32rm
    22618854U,	// BSF32rr
    83545U,	// BSF64rm
    22619737U,	// BSF64rr
    100913U,	// BSR16rm
    22620721U,	// BSR16rr
    66541U,	// BSR32rm
    22619117U,	// BSR32rr
    83769U,	// BSR64rm
    22619961U,	// BSR64rr
    9098U,	// BSWAP32r
    9961U,	// BSWAP64r
    1067683U,	// BT16mi8
    1067683U,	// BT16mr
    22620835U,	// BT16ri8
    22620835U,	// BT16rr
    3163240U,	// BT32mi8
    3163240U,	// BT32mr
    22619240U,	// BT32ri8
    22619240U,	// BT32rr
    4212641U,	// BT64mi8
    4212641U,	// BT64mr
    22620065U,	// BT64ri8
    22620065U,	// BT64rr
    1067275U,	// BTC16mi8
    1067275U,	// BTC16mr
    22620427U,	// BTC16ri8
    22620427U,	// BTC16rr
    3162693U,	// BTC32mi8
    3162693U,	// BTC32mr
    22618693U,	// BTC32ri8
    22618693U,	// BTC32rr
    4212152U,	// BTC64mi8
    4212152U,	// BTC64mr
    22619576U,	// BTC64ri8
    22619576U,	// BTC64rr
    1067575U,	// BTR16mi8
    1067575U,	// BTR16mr
    22620727U,	// BTR16ri8
    22620727U,	// BTR16rr
    3163130U,	// BTR32mi8
    3163130U,	// BTR32mr
    22619130U,	// BTR32ri8
    22619130U,	// BTR32rr
    4212550U,	// BTR64mi8
    4212550U,	// BTR64mr
    22619974U,	// BTR64ri8
    22619974U,	// BTR64rr
    1067662U,	// BTS16mi8
    1067662U,	// BTS16mr
    22620814U,	// BTS16ri8
    22620814U,	// BTS16rr
    3163219U,	// BTS32mi8
    3163219U,	// BTS32mr
    22619219U,	// BTS32ri8
    22619219U,	// BTS32rr
    4212627U,	// BTS64mi8
    4212627U,	// BTS64mr
    22620051U,	// BTS64ri8
    22620051U,	// BTS64rr
    6382347U,	// BZHI32rm
    35152651U,	// BZHI32rr
    6907518U,	// BZHI64rm
    35153534U,	// BZHI64rr
    110971U,	// CALL16m
    12667U,	// CALL16r
    119116U,	// CALL32m
    12620U,	// CALL32r
    127325U,	// CALL64m
    132775U,	// CALL64pcrel32
    12637U,	// CALL64r
    133541U,	// CALLpcrel16
    131915U,	// CALLpcrel32
    5741U,	// CBW
    4927U,	// CDQ
    5320U,	// CDQE
    4854U,	// CLAC
    4886U,	// CLC
    4923U,	// CLD
    141490U,	// CLFLUSHOPT
    5033U,	// CLGI
    5043U,	// CLI
    5664U,	// CLTS
    139571U,	// CLWB
    4890U,	// CMC
    2132173U,	// CMOVA16rm
    2123981U,	// CMOVA16rr
    2138631U,	// CMOVA32rm
    2122247U,	// CMOVA32rr
    2147706U,	// CMOVA64rm
    2123130U,	// CMOVA64rr
    2132280U,	// CMOVAE16rm
    2124088U,	// CMOVAE16rr
    2138747U,	// CMOVAE32rm
    2122363U,	// CMOVAE32rr
    2147822U,	// CMOVAE64rm
    2123246U,	// CMOVAE64rr
    2132201U,	// CMOVB16rm
    2124009U,	// CMOVB16rr
    2138659U,	// CMOVB32rm
    2122275U,	// CMOVB32rr
    2147734U,	// CMOVB64rm
    2123158U,	// CMOVB64rr
    2132289U,	// CMOVBE16rm
    2124097U,	// CMOVBE16rr
    2138756U,	// CMOVBE32rm
    2122372U,	// CMOVBE32rr
    2147831U,	// CMOVBE64rm
    2123255U,	// CMOVBE64rr
    2132325U,	// CMOVE16rm
    2124133U,	// CMOVE16rr
    2138846U,	// CMOVE32rm
    2122462U,	// CMOVE32rr
    2147921U,	// CMOVE64rm
    2123345U,	// CMOVE64rr
    2132355U,	// CMOVG16rm
    2124163U,	// CMOVG16rr
    2138876U,	// CMOVG32rm
    2122492U,	// CMOVG32rr
    2147951U,	// CMOVG64rm
    2123375U,	// CMOVG64rr
    2132298U,	// CMOVGE16rm
    2124106U,	// CMOVGE16rr
    2138765U,	// CMOVGE32rm
    2122381U,	// CMOVGE32rr
    2147840U,	// CMOVGE64rm
    2123264U,	// CMOVGE64rr
    2132415U,	// CMOVL16rm
    2124223U,	// CMOVL16rr
    2138981U,	// CMOVL32rm
    2122597U,	// CMOVL32rr
    2148041U,	// CMOVL64rm
    2123465U,	// CMOVL64rr
    2132307U,	// CMOVLE16rm
    2124115U,	// CMOVLE16rr
    2138774U,	// CMOVLE32rm
    2122390U,	// CMOVLE32rr
    2147849U,	// CMOVLE64rm
    2123273U,	// CMOVLE64rr
    2132316U,	// CMOVNE16rm
    2124124U,	// CMOVNE16rr
    2138783U,	// CMOVNE32rm
    2122399U,	// CMOVNE32rr
    2147858U,	// CMOVNE64rm
    2123282U,	// CMOVNE64rr
    2132428U,	// CMOVNO16rm
    2124236U,	// CMOVNO16rr
    2139001U,	// CMOVNO32rm
    2122617U,	// CMOVNO32rr
    2148056U,	// CMOVNO64rm
    2123480U,	// CMOVNO64rr
    2132458U,	// CMOVNP16rm
    2124266U,	// CMOVNP16rr
    2139046U,	// CMOVNP32rm
    2122662U,	// CMOVNP32rr
    2148094U,	// CMOVNP64rm
    2123518U,	// CMOVNP64rr
    2132600U,	// CMOVNS16rm
    2124408U,	// CMOVNS16rr
    2139197U,	// CMOVNS32rm
    2122813U,	// CMOVNS32rr
    2148221U,	// CMOVNS64rm
    2123645U,	// CMOVNS64rr
    2132437U,	// CMOVO16rm
    2124245U,	// CMOVO16rr
    2139010U,	// CMOVO32rm
    2122626U,	// CMOVO32rr
    2148065U,	// CMOVO64rm
    2123489U,	// CMOVO64rr
    2132479U,	// CMOVP16rm
    2124287U,	// CMOVP16rr
    2139073U,	// CMOVP32rm
    2122689U,	// CMOVP32rr
    2148109U,	// CMOVP64rm
    2123533U,	// CMOVP64rr
    2132635U,	// CMOVS16rm
    2124443U,	// CMOVS16rr
    2139232U,	// CMOVS32rm
    2122848U,	// CMOVS32rr
    2148249U,	// CMOVS64rm
    2123673U,	// CMOVS64rr
    4094U,	// CMOV_FR32
    4281U,	// CMOV_FR64
    4301U,	// CMOV_GR16
    4114U,	// CMOV_GR32
    4321U,	// CMOV_GR8
    4073U,	// CMOV_RFP32
    4260U,	// CMOV_RFP64
    3988U,	// CMOV_RFP80
    4030U,	// CMOV_V16F32
    4134U,	// CMOV_V2F64
    4197U,	// CMOV_V2I64
    4009U,	// CMOV_V4F32
    4155U,	// CMOV_V4F64
    4218U,	// CMOV_V4I64
    4052U,	// CMOV_V8F32
    4176U,	// CMOV_V8F64
    4239U,	// CMOV_V8I64
    535005U,	// CMP16i16
    1067485U,	// CMP16mi
    1067485U,	// CMP16mi8
    1067485U,	// CMP16mr
    22620637U,	// CMP16ri
    22620637U,	// CMP16ri8
    100829U,	// CMP16rm
    22620637U,	// CMP16rr
    22620637U,	// CMP16rr_REV
    2630553U,	// CMP32i32
    3163033U,	// CMP32mi
    3163033U,	// CMP32mi8
    3163033U,	// CMP32mr
    22619033U,	// CMP32ri
    22619033U,	// CMP32ri8
    66457U,	// CMP32rm
    22619033U,	// CMP32rr
    22619033U,	// CMP32rr_REV
    3679992U,	// CMP64i32
    4212472U,	// CMP64mi32
    4212472U,	// CMP64mi8
    4212472U,	// CMP64mr
    22619896U,	// CMP64ri32
    22619896U,	// CMP64ri8
    83704U,	// CMP64rm
    22619896U,	// CMP64rr
    22619896U,	// CMP64rr_REV
    4726979U,	// CMP8i8
    5259459U,	// CMP8mi
    5259459U,	// CMP8mi8
    5259459U,	// CMP8mr
    22618307U,	// CMP8ri
    22618307U,	// CMP8ri8
    147651U,	// CMP8rm
    22618307U,	// CMP8rr
    22618307U,	// CMP8rr_REV
    56254718U,	// CMPSB
    73040966U,	// CMPSL
    89827206U,	// CMPSQ
    106613377U,	// CMPSW
    188489U,	// CMPXCHG16B
    1067385U,	// CMPXCHG16rm
    22620537U,	// CMPXCHG16rr
    3162866U,	// CMPXCHG32rm
    22618866U,	// CMPXCHG32rr
    4212325U,	// CMPXCHG64rm
    22619749U,	// CMPXCHG64rr
    122965U,	// CMPXCHG8B
    5259409U,	// CMPXCHG8rm
    22618257U,	// CMPXCHG8rr
    4917U,	// CPUID
    5238U,	// CQO
    4936U,	// CWD
    5182U,	// CWDE
    4789U,	// DAA
    5360U,	// DAS
    4679U,	// DATA16_PREFIX
    108799U,	// DEC16m
    10495U,	// DEC16r
    10495U,	// DEC16r_alt
    115257U,	// DEC32m
    8761U,	// DEC32r
    8761U,	// DEC32r_alt
    124332U,	// DEC64m
    9644U,	// DEC64r
    139378U,	// DEC8m
    8306U,	// DEC8r
    109303U,	// DIV16m
    10999U,	// DIV16r
    115906U,	// DIV32m
    9410U,	// DIV32r
    124917U,	// DIV64m
    10229U,	// DIV64r
    139559U,	// DIV8m
    8487U,	// DIV8r
    12021U,	// EH_RETURN
    12021U,	// EH_RETURN64
    4502U,	// EH_SjLj_LongJmp32
    4592U,	// EH_SjLj_LongJmp64
    4521U,	// EH_SjLj_SetJmp32
    4611U,	// EH_SjLj_SetJmp64
    132452U,	// EH_SjLj_Setup
    123218002U,	// ENTER
    7416228U,	// FARCALL16i
    201082U,	// FARCALL16m
    7414602U,	// FARCALL32i
    201035U,	// FARCALL32m
    201052U,	// FARCALL64
    7416291U,	// FARJMP16i
    201091U,	// FARJMP16m
    7414687U,	// FARJMP32i
    201044U,	// FARJMP32m
    201074U,	// FARJMP64
    5195U,	// FSETPM
    4874U,	// GETSEC
    5677U,	// HLT
    109302U,	// IDIV16m
    10998U,	// IDIV16r
    115905U,	// IDIV32m
    9409U,	// IDIV32r
    124916U,	// IDIV64m
    10228U,	// IDIV64r
    139558U,	// IDIV8m
    8486U,	// IDIV8r
    108984U,	// IMUL16m
    10680U,	// IMUL16r
    2132408U,	// IMUL16rm
    7956920U,	// IMUL16rmi
    7956920U,	// IMUL16rmi8
    2124216U,	// IMUL16rr
    35154360U,	// IMUL16rri
    35154360U,	// IMUL16rri8
    115550U,	// IMUL32m
    9054U,	// IMUL32r
    2138974U,	// IMUL32rm
    6382430U,	// IMUL32rmi
    6382430U,	// IMUL32rmi8
    2122590U,	// IMUL32rr
    35152734U,	// IMUL32rri
    35152734U,	// IMUL32rri8
    124610U,	// IMUL64m
    9922U,	// IMUL64r
    2148034U,	// IMUL64rm
    6907586U,	// IMUL64rmi32
    6907586U,	// IMUL64rmi8
    2123458U,	// IMUL64rr
    35153602U,	// IMUL64rri32
    35153602U,	// IMUL64rri8
    139447U,	// IMUL8m
    8375U,	// IMUL8r
    534983U,	// IN16ri
    5758U,	// IN16rr
    2630516U,	// IN32ri
    5837U,	// IN32rr
    4726974U,	// IN8ri
    5071U,	// IN8rr
    108805U,	// INC16m
    10501U,	// INC16r
    10501U,	// INC16r_alt
    115263U,	// INC32m
    8767U,	// INC32r
    8767U,	// INC32r_alt
    124338U,	// INC64m
    9650U,	// INC64r
    139384U,	// INC8m
    8312U,	// INC8r
    159433U,	// INSB
    167636U,	// INSL
    184031U,	// INSW
    10395U,	// INT
    4497U,	// INT1
    4587U,	// INT3
    5233U,	// INTO
    4943U,	// INVD
    207008U,	// INVEPT32
    207008U,	// INVEPT64
    139742U,	// INVLPG
    5818U,	// INVLPGA32
    5886U,	// INVLPGA64
    205148U,	// INVPCID32
    205148U,	// INVPCID64
    205157U,	// INVVPID32
    205157U,	// INVVPID64
    5746U,	// IRET16
    5153U,	// IRET32
    5291U,	// IRET64
    4758U,	// Int_MemBarrier
    131454U,	// JAE_1
    131454U,	// JAE_2
    131454U,	// JAE_4
    131135U,	// JA_1
    131135U,	// JA_2
    131135U,	// JA_4
    131466U,	// JBE_1
    131466U,	// JBE_2
    131466U,	// JBE_4
    131227U,	// JB_1
    131227U,	// JB_2
    131227U,	// JB_4
    133912U,	// JCXZ
    133905U,	// JECXZ
    131490U,	// JE_1
    131490U,	// JE_2
    131490U,	// JE_4
    131478U,	// JGE_1
    131478U,	// JGE_2
    131478U,	// JGE_4
    131546U,	// JG_1
    131546U,	// JG_2
    131546U,	// JG_4
    131494U,	// JLE_1
    131494U,	// JLE_2
    131494U,	// JLE_4
    131865U,	// JL_1
    131865U,	// JL_2
    131865U,	// JL_4
    110980U,	// JMP16m
    12676U,	// JMP16r
    119125U,	// JMP32m
    12629U,	// JMP32r
    127339U,	// JMP64m
    12651U,	// JMP64r
    132418U,	// JMP_1
    132418U,	// JMP_2
    132418U,	// JMP_4
    131506U,	// JNE_1
    131506U,	// JNE_2
    131506U,	// JNE_4
    132390U,	// JNO_1
    132390U,	// JNO_2
    132390U,	// JNO_4
    132423U,	// JNP_1
    132423U,	// JNP_2
    132423U,	// JNP_4
    133248U,	// JNS_1
    133248U,	// JNS_2
    133248U,	// JNS_4
    132386U,	// JO_1
    132386U,	// JO_2
    132386U,	// JO_4
    132408U,	// JP_1
    132408U,	// JP_2
    132408U,	// JP_4
    133918U,	// JRCXZ
    133244U,	// JS_1
    133244U,	// JS_2
    133244U,	// JS_4
    5014U,	// LAHF
    100871U,	// LAR16rm
    22620679U,	// LAR16rr
    99273U,	// LAR32rm
    22619081U,	// LAR32rr
    100117U,	// LAR64rm
    22619925U,	// LAR64rr
    1067385U,	// LCMPXCHG16
    188489U,	// LCMPXCHG16B
    3162866U,	// LCMPXCHG32
    4212325U,	// LCMPXCHG64
    5259409U,	// LCMPXCHG8
    122965U,	// LCMPXCHG8B
    215641U,	// LDS16rm
    214046U,	// LDS32rm
    223431U,	// LEA16r
    221697U,	// LEA32r
    221697U,	// LEA64_32r
    222580U,	// LEA64r
    5001U,	// LEAVE
    5001U,	// LEAVE64
    215654U,	// LES16rm
    214059U,	// LES32rm
    215660U,	// LFS16rm
    214065U,	// LFS32rm
    214897U,	// LFS64rm
    199336U,	// LGDT16m
    197741U,	// LGDT32m
    198566U,	// LGDT64m
    215666U,	// LGS16rm
    214071U,	// LGS32rm
    214903U,	// LGS64rm
    199350U,	// LIDT16m
    197755U,	// LIDT32m
    198580U,	// LIDT64m
    109252U,	// LLDT16m
    10948U,	// LLDT16r
    109315U,	// LMSW16m
    11011U,	// LMSW16r
    1067282U,	// LOCK_ADD16mi
    1067282U,	// LOCK_ADD16mi8
    1067282U,	// LOCK_ADD16mr
    3162709U,	// LOCK_ADD32mi
    3162709U,	// LOCK_ADD32mi8
    3162709U,	// LOCK_ADD32mr
    4212168U,	// LOCK_ADD64mi32
    4212168U,	// LOCK_ADD64mi8
    4212168U,	// LOCK_ADD64mr
    5259391U,	// LOCK_ADD8mi
    5259391U,	// LOCK_ADD8mr
    1067307U,	// LOCK_AND16mi
    1067307U,	// LOCK_AND16mi8
    1067307U,	// LOCK_AND16mr
    3162734U,	// LOCK_AND32mi
    3162734U,	// LOCK_AND32mi8
    3162734U,	// LOCK_AND32mr
    4212193U,	// LOCK_AND64mi32
    4212193U,	// LOCK_AND64mi8
    4212193U,	// LOCK_AND64mr
    5259397U,	// LOCK_AND8mi
    5259397U,	// LOCK_AND8mr
    108799U,	// LOCK_DEC16m
    115257U,	// LOCK_DEC32m
    124332U,	// LOCK_DEC64m
    139378U,	// LOCK_DEC8m
    108805U,	// LOCK_INC16m
    115263U,	// LOCK_INC32m
    124338U,	// LOCK_INC64m
    139384U,	// LOCK_INC8m
    1067558U,	// LOCK_OR16mi
    1067558U,	// LOCK_OR16mi8
    1067558U,	// LOCK_OR16mr
    3163106U,	// LOCK_OR32mi
    3163106U,	// LOCK_OR32mi8
    3163106U,	// LOCK_OR32mr
    4212526U,	// LOCK_OR64mi32
    4212526U,	// LOCK_OR64mi8
    4212526U,	// LOCK_OR64mr
    5259484U,	// LOCK_OR8mi
    5259484U,	// LOCK_OR8mr
    5066U,	// LOCK_PREFIX
    1067235U,	// LOCK_SUB16mi
    1067235U,	// LOCK_SUB16mi8
    1067235U,	// LOCK_SUB16mr
    3162653U,	// LOCK_SUB32mi
    3162653U,	// LOCK_SUB32mi8
    3162653U,	// LOCK_SUB32mr
    4212112U,	// LOCK_SUB64mi32
    4212112U,	// LOCK_SUB64mi8
    4212112U,	// LOCK_SUB64mr
    5259366U,	// LOCK_SUB8mi
    5259366U,	// LOCK_SUB8mr
    1067563U,	// LOCK_XOR16mi
    1067563U,	// LOCK_XOR16mi8
    1067563U,	// LOCK_XOR16mr
    3163111U,	// LOCK_XOR32mi
    3163111U,	// LOCK_XOR32mi8
    3163111U,	// LOCK_XOR32mr
    4212531U,	// LOCK_XOR64mi32
    4212531U,	// LOCK_XOR64mi8
    4212531U,	// LOCK_XOR64mr
    5259489U,	// LOCK_XOR8mi
    5259489U,	// LOCK_XOR8mr
    4948215U,	// LODSB
    2860068U,	// LODSL
    247658U,	// LODSQ
    780895U,	// LODSW
    132440U,	// LOOP
    131526U,	// LOOPE
    131511U,	// LOOPNE
    9360U,	// LRETIL
    10185U,	// LRETIQ
    10962U,	// LRETIW
    5159U,	// LRETL
    5297U,	// LRETQ
    5752U,	// LRETW
    100786U,	// LSL16rm
    22620594U,	// LSL16rr
    66392U,	// LSL32rm
    22618968U,	// LSL32rr
    83636U,	// LSL64rm
    22619828U,	// LSL64rr
    215688U,	// LSS16rm
    214093U,	// LSS32rm
    214925U,	// LSS64rm
    109117U,	// LTRm
    10813U,	// LTRr
    140060945U,	// LXADD16
    156836436U,	// LXADD32
    173614535U,	// LXADD64
    190390398U,	// LXADD8
    101081U,	// LZCNT16rm
    22620889U,	// LZCNT16rr
    66717U,	// LZCNT32rm
    22619293U,	// LZCNT32rr
    83920U,	// LZCNT64rm
    22620112U,	// LZCNT64rr
    5187U,	// MONTMUL
    0U,	// MORESTACK_RET
    0U,	// MORESTACK_RET_RESTORE_R10
    789245U,	// MOV16ao16
    789245U,	// MOV16ao32
    789072U,	// MOV16ao64
    1067773U,	// MOV16mi
    1067773U,	// MOV16mr
    1067773U,	// MOV16ms
    265826U,	// MOV16o16a
    265826U,	// MOV16o32a
    265789U,	// MOV16o64a
    22620925U,	// MOV16ri
    22620925U,	// MOV16ri_alt
    101117U,	// MOV16rm
    22620925U,	// MOV16rr
    22620925U,	// MOV16rr_REV
    22620925U,	// MOV16rs
    101117U,	// MOV16sm
    22620925U,	// MOV16sr
    2893000U,	// MOV32ao16
    2893000U,	// MOV32ao32
    2892821U,	// MOV32ao64
    22619336U,	// MOV32cr
    22619336U,	// MOV32dr
    3163336U,	// MOV32mi
    3163336U,	// MOV32mr
    1066184U,	// MOV32ms
    274069U,	// MOV32o16a
    274069U,	// MOV32o32a
    274029U,	// MOV32o64a
    0U,	// MOV32r0
    22619336U,	// MOV32rc
    22619336U,	// MOV32rd
    22619336U,	// MOV32ri
    0U,	// MOV32ri64
    22619336U,	// MOV32ri_alt
    66760U,	// MOV32rm
    22619336U,	// MOV32rr
    22619336U,	// MOV32rr_REV
    22619336U,	// MOV32rs
    99528U,	// MOV32sm
    22619336U,	// MOV32sr
    3950587U,	// MOV64ao32
    3950433U,	// MOV64ao64
    22620155U,	// MOV64cr
    22620155U,	// MOV64dr
    4212731U,	// MOV64mi32
    4212731U,	// MOV64mr
    1067003U,	// MOV64ms
    282301U,	// MOV64o32a
    282273U,	// MOV64o64a
    22620155U,	// MOV64rc
    22620155U,	// MOV64rd
    22620001U,	// MOV64ri
    22620155U,	// MOV64ri32
    83963U,	// MOV64rm
    22620155U,	// MOV64rr
    22620155U,	// MOV64rr_REV
    22620155U,	// MOV64rs
    100347U,	// MOV64sm
    22620155U,	// MOV64sr
    5005613U,	// MOV8ao16
    5005613U,	// MOV8ao32
    5005550U,	// MOV8ao64
    5259565U,	// MOV8mi
    5259565U,	// MOV8mr
    5259565U,	// MOV8mr_NOREX
    289930U,	// MOV8o16a
    289930U,	// MOV8o32a
    289893U,	// MOV8o64a
    22618413U,	// MOV8ri
    22618413U,	// MOV8ri_alt
    147757U,	// MOV8rm
    147757U,	// MOV8rm_NOREX
    22618413U,	// MOV8rr
    22618413U,	// MOV8rr_NOREX
    22618413U,	// MOV8rr_REV
    1067330U,	// MOVBE16mr
    100674U,	// MOVBE16rm
    3162757U,	// MOVBE32mr
    66181U,	// MOVBE32rm
    4212216U,	// MOVBE64mr
    83448U,	// MOVBE64rm
    0U,	// MOVPC32r
    295180U,	// MOVSB
    304225U,	// MOVSL
    313242U,	// MOVSQ
    322204U,	// MOVSW
    149723U,	// MOVSX16rm8
    22620379U,	// MOVSX16rr8
    147989U,	// MOVSX32_NOREXrm8
    22618645U,	// MOVSX32_NOREXrr8
    99541U,	// MOVSX32rm16
    147989U,	// MOVSX32rm8
    22619349U,	// MOVSX32rr16
    22618645U,	// MOVSX32rr8
    22619834U,	// MOVSX64_NOREXrr32
    100360U,	// MOVSX64rm16
    67258U,	// MOVSX64rm32
    67258U,	// MOVSX64rm32_alt
    148872U,	// MOVSX64rm8
    22620168U,	// MOVSX64rr16
    22619834U,	// MOVSX64rr32
    22619528U,	// MOVSX64rr8
    149745U,	// MOVZX16rm8
    22620401U,	// MOVZX16rr8
    148011U,	// MOVZX32_NOREXrm8
    22618667U,	// MOVZX32_NOREXrr8
    99549U,	// MOVZX32rm16
    148011U,	// MOVZX32rm8
    22619357U,	// MOVZX32rr16
    22618667U,	// MOVZX32rr8
    100368U,	// MOVZX64rm16_Q
    148894U,	// MOVZX64rm8_Q
    22620176U,	// MOVZX64rr16_Q
    22619550U,	// MOVZX64rr8_Q
    108985U,	// MUL16m
    10681U,	// MUL16r
    115551U,	// MUL32m
    9055U,	// MUL32r
    124611U,	// MUL64m
    9923U,	// MUL64r
    139448U,	// MUL8m
    8376U,	// MUL8r
    35169523U,	// MULX32rm
    35153139U,	// MULX32rr
    35178534U,	// MULX64rm
    35153958U,	// MULX64rr
    108915U,	// NEG16m
    10611U,	// NEG16r
    115436U,	// NEG32m
    8940U,	// NEG32r
    124511U,	// NEG64m
    9823U,	// NEG64r
    139403U,	// NEG8m
    8331U,	// NEG8r
    5254U,	// NOOP
    109043U,	// NOOP18_16m4
    109043U,	// NOOP18_16m5
    109043U,	// NOOP18_16m6
    109043U,	// NOOP18_16m7
    10739U,	// NOOP18_16r4
    10739U,	// NOOP18_16r5
    10739U,	// NOOP18_16r6
    10739U,	// NOOP18_16r7
    115631U,	// NOOP18_m4
    115631U,	// NOOP18_m5
    115631U,	// NOOP18_m6
    115631U,	// NOOP18_m7
    9135U,	// NOOP18_r4
    9135U,	// NOOP18_r5
    9135U,	// NOOP18_r6
    9135U,	// NOOP18_r7
    123217235U,	// NOOP19rr
    115631U,	// NOOPL
    115631U,	// NOOPL_19
    115631U,	// NOOPL_1a
    115631U,	// NOOPL_1b
    115631U,	// NOOPL_1c
    115631U,	// NOOPL_1d
    115631U,	// NOOPL_1e
    109043U,	// NOOPW
    109043U,	// NOOPW_19
    109043U,	// NOOPW_1a
    109043U,	// NOOPW_1b
    109043U,	// NOOPW_1c
    109043U,	// NOOPW_1d
    109043U,	// NOOPW_1e
    109289U,	// NOT16m
    10985U,	// NOT16r
    115885U,	// NOT32m
    9389U,	// NOT32r
    124896U,	// NOT64m
    10208U,	// NOT64r
    139545U,	// NOT8m
    8473U,	// NOT8r
    535078U,	// OR16i16
    1067558U,	// OR16mi
    1067558U,	// OR16mi8
    1067558U,	// OR16mr
    1600038U,	// OR16ri
    1600038U,	// OR16ri8
    1608230U,	// OR16rm
    1600038U,	// OR16rr
    2124326U,	// OR16rr_REV
    2630626U,	// OR32i32
    3163106U,	// OR32mi
    3163106U,	// OR32mi8
    3163106U,	// OR32mr
    3163106U,	// OR32mrLocked
    1598434U,	// OR32ri
    1598434U,	// OR32ri8
    1614818U,	// OR32rm
    1598434U,	// OR32rr
    2122722U,	// OR32rr_REV
    3680046U,	// OR64i32
    4212526U,	// OR64mi32
    4212526U,	// OR64mi8
    4212526U,	// OR64mr
    1599278U,	// OR64ri32
    1599278U,	// OR64ri8
    1623854U,	// OR64rm
    1599278U,	// OR64rr
    2123566U,	// OR64rr_REV
    4727004U,	// OR8i8
    5259484U,	// OR8mi
    5259484U,	// OR8mi8
    5259484U,	// OR8mr
    1597660U,	// OR8ri
    1597660U,	// OR8ri8
    57564U,	// OR8rm
    1597660U,	// OR8rr
    2121948U,	// OR8rr_REV
    11863U,	// OUT16ir
    5919U,	// OUT16rr
    11913U,	// OUT32ir
    5933U,	// OUT32rr
    11391U,	// OUT8ir
    5905U,	// OUT8rr
    8618245U,	// OUTSB
    8627289U,	// OUTSL
    8645268U,	// OUTSW
    5669U,	// PCOMMIT
    35169170U,	// PDEP32rm
    35152786U,	// PDEP32rr
    35178225U,	// PDEP64rm
    35153649U,	// PDEP64rr
    35169466U,	// PEXT32rm
    35153082U,	// PEXT32rr
    35178477U,	// PEXT64rm
    35153901U,	// PEXT64rr
    10745U,	// POP16r
    109049U,	// POP16rmm
    10745U,	// POP16rmr
    9141U,	// POP32r
    115637U,	// POP32rmm
    9141U,	// POP32rmr
    9991U,	// POP64r
    124679U,	// POP64rmm
    9991U,	// POP64rmr
    5702U,	// POPA16
    5091U,	// POPA32
    5413U,	// POPDS16
    5394U,	// POPDS32
    5451U,	// POPES16
    5432U,	// POPES32
    5715U,	// POPF16
    5104U,	// POPF32
    5265U,	// POPF64
    5508U,	// POPFS16
    5470U,	// POPFS32
    5489U,	// POPFS64
    5565U,	// POPGS16
    5527U,	// POPGS32
    5546U,	// POPGS64
    5655U,	// POPSS16
    5636U,	// POPSS32
    10635U,	// PUSH16i8
    10635U,	// PUSH16r
    108939U,	// PUSH16rmm
    10635U,	// PUSH16rmr
    8964U,	// PUSH32i8
    8964U,	// PUSH32r
    115460U,	// PUSH32rmm
    8964U,	// PUSH32rmr
    10635U,	// PUSH64i16
    9847U,	// PUSH64i32
    9847U,	// PUSH64i8
    9847U,	// PUSH64r
    124535U,	// PUSH64rmm
    9847U,	// PUSH64rmr
    5695U,	// PUSHA16
    5084U,	// PUSHA32
    5374U,	// PUSHCS16
    5364U,	// PUSHCS32
    5403U,	// PUSHDS16
    5384U,	// PUSHDS32
    5441U,	// PUSHES16
    5422U,	// PUSHES32
    5708U,	// PUSHF16
    5097U,	// PUSHF32
    5258U,	// PUSHF64
    5498U,	// PUSHFS16
    5460U,	// PUSHFS32
    5479U,	// PUSHFS64
    5555U,	// PUSHGS16
    5517U,	// PUSHGS32
    5536U,	// PUSHGS64
    5645U,	// PUSHSS16
    5626U,	// PUSHSS32
    10635U,	// PUSHi16
    8964U,	// PUSHi32
    109599U,	// RCL16m1
    110064U,	// RCL16mCL
    1067416U,	// RCL16mi
    11295U,	// RCL16r1
    11760U,	// RCL16rCL
    2124184U,	// RCL16ri
    117631U,	// RCL32m1
    118032U,	// RCL32mCL
    3162924U,	// RCL32mi
    11135U,	// RCL32r1
    11536U,	// RCL32rCL
    2122540U,	// RCL32ri
    125903U,	// RCL64m1
    126336U,	// RCL64mCL
    4212379U,	// RCL64mi
    11215U,	// RCL64r1
    11648U,	// RCL64rCL
    2123419U,	// RCL64ri
    142127U,	// RCL8m1
    142496U,	// RCL8mCL
    5259429U,	// RCL8mi
    11055U,	// RCL8r1
    11424U,	// RCL8rCL
    2121893U,	// RCL8ri
    109639U,	// RCR16m1
    110108U,	// RCR16mCL
    1067539U,	// RCR16mi
    11335U,	// RCR16r1
    11804U,	// RCR16rCL
    2124307U,	// RCR16ri
    117671U,	// RCR32m1
    118076U,	// RCR32mCL
    3163093U,	// RCR32mi
    11175U,	// RCR32r1
    11580U,	// RCR32rCL
    2122709U,	// RCR32ri
    125943U,	// RCR64m1
    126380U,	// RCR64mCL
    4212513U,	// RCR64mi
    11255U,	// RCR64r1
    11692U,	// RCR64rCL
    2123553U,	// RCR64ri
    142167U,	// RCR8m1
    142540U,	// RCR8mCL
    5259471U,	// RCR8mi
    11095U,	// RCR8r1
    11468U,	// RCR8rCL
    2121935U,	// RCR8ri
    8872U,	// RDFSBASE
    9755U,	// RDFSBASE64
    8894U,	// RDGSBASE
    9777U,	// RDGSBASE64
    5334U,	// RDMSR
    4894U,	// RDPMC
    10536U,	// RDRAND16r
    8811U,	// RDRAND32r
    9694U,	// RDRAND64r
    10520U,	// RDSEED16r
    8795U,	// RDSEED32r
    9678U,	// RDSEED64r
    4907U,	// RDTSC
    5243U,	// RDTSCP
    4340U,	// RELEASE_ADD32mi
    4340U,	// RELEASE_ADD64mi32
    4340U,	// RELEASE_ADD8mi
    4340U,	// RELEASE_AND32mi
    4340U,	// RELEASE_AND64mi32
    4340U,	// RELEASE_AND8mi
    4363U,	// RELEASE_DEC16m
    4363U,	// RELEASE_DEC32m
    4363U,	// RELEASE_DEC64m
    4363U,	// RELEASE_DEC8m
    4363U,	// RELEASE_INC16m
    4363U,	// RELEASE_INC32m
    4363U,	// RELEASE_INC64m
    4363U,	// RELEASE_INC8m
    3966U,	// RELEASE_MOV16mi
    4406U,	// RELEASE_MOV16mr
    3966U,	// RELEASE_MOV32mi
    4406U,	// RELEASE_MOV32mr
    3966U,	// RELEASE_MOV64mi32
    4406U,	// RELEASE_MOV64mr
    3966U,	// RELEASE_MOV8mi
    4406U,	// RELEASE_MOV8mr
    4340U,	// RELEASE_OR32mi
    4340U,	// RELEASE_OR64mi32
    4340U,	// RELEASE_OR8mi
    4340U,	// RELEASE_XOR32mi
    4340U,	// RELEASE_XOR64mi32
    4340U,	// RELEASE_XOR8mi
    4957U,	// REPNE_PREFIX
    4838U,	// REP_MOVSB_32
    4838U,	// REP_MOVSB_64
    5143U,	// REP_MOVSD_32
    5143U,	// REP_MOVSD_64
    5281U,	// REP_MOVSQ_64
    5731U,	// REP_MOVSW_32
    5731U,	// REP_MOVSW_64
    5250U,	// REP_PREFIX
    4828U,	// REP_STOSB_32
    4828U,	// REP_STOSB_64
    5133U,	// REP_STOSD_32
    5133U,	// REP_STOSD_64
    5271U,	// REP_STOSQ_64
    5721U,	// REP_STOSW_32
    5721U,	// REP_STOSW_64
    9361U,	// RETIL
    10186U,	// RETIQ
    10963U,	// RETIW
    5154U,	// RETL
    5292U,	// RETQ
    5747U,	// RETW
    4673U,	// REX64_PREFIX
    109619U,	// ROL16m1
    110086U,	// ROL16mCL
    1067436U,	// ROL16mi
    11315U,	// ROL16r1
    11782U,	// ROL16rCL
    2124204U,	// ROL16ri
    117651U,	// ROL32m1
    118054U,	// ROL32mCL
    3162962U,	// ROL32mi
    11155U,	// ROL32r1
    11558U,	// ROL32rCL
    2122578U,	// ROL32ri
    125923U,	// ROL64m1
    126358U,	// ROL64mCL
    4212398U,	// ROL64mi
    11235U,	// ROL64r1
    11670U,	// ROL64rCL
    2123438U,	// ROL64ri
    142147U,	// ROL8m1
    142518U,	// ROL8mCL
    5259441U,	// ROL8mi
    11075U,	// ROL8r1
    11446U,	// ROL8rCL
    2121905U,	// ROL8ri
    109659U,	// ROR16m1
    110130U,	// ROR16mCL
    1067557U,	// ROR16mi
    11355U,	// ROR16r1
    11826U,	// ROR16rCL
    2124325U,	// ROR16ri
    117691U,	// ROR32m1
    118098U,	// ROR32mCL
    3163105U,	// ROR32mi
    11195U,	// ROR32r1
    11602U,	// ROR32rCL
    2122721U,	// ROR32ri
    125963U,	// ROR64m1
    126402U,	// ROR64mCL
    4212525U,	// ROR64mi
    11275U,	// ROR64r1
    11714U,	// ROR64rCL
    2123565U,	// ROR64ri
    142187U,	// ROR8m1
    142562U,	// ROR8mCL
    5259483U,	// ROR8mi
    11115U,	// ROR8r1
    11490U,	// ROR8rCL
    2121947U,	// ROR8ri
    6382863U,	// RORX32mi
    35153167U,	// RORX32ri
    6907970U,	// RORX64mi
    35153986U,	// RORX64ri
    5202U,	// RSM
    5019U,	// SAHF
    109589U,	// SAL16m1
    110053U,	// SAL16mCL
    1067410U,	// SAL16mi
    11285U,	// SAL16r1
    11749U,	// SAL16rCL
    2124178U,	// SAL16ri
    117621U,	// SAL32m1
    118021U,	// SAL32mCL
    3162918U,	// SAL32mi
    11125U,	// SAL32r1
    11525U,	// SAL32rCL
    2122534U,	// SAL32ri
    125893U,	// SAL64m1
    126325U,	// SAL64mCL
    4212373U,	// SAL64mi
    11205U,	// SAL64r1
    11637U,	// SAL64rCL
    2123413U,	// SAL64ri
    142117U,	// SAL8m1
    142485U,	// SAL8mCL
    5259423U,	// SAL8mi
    11045U,	// SAL8r1
    11413U,	// SAL8rCL
    2121887U,	// SAL8ri
    4881U,	// SALC
    109629U,	// SAR16m1
    110097U,	// SAR16mCL
    1067533U,	// SAR16mi
    11325U,	// SAR16r1
    11793U,	// SAR16rCL
    2124301U,	// SAR16ri
    117661U,	// SAR32m1
    118065U,	// SAR32mCL
    3163087U,	// SAR32mi
    11165U,	// SAR32r1
    11569U,	// SAR32rCL
    2122703U,	// SAR32ri
    125933U,	// SAR64m1
    126369U,	// SAR64mCL
    4212507U,	// SAR64mi
    11245U,	// SAR64r1
    11681U,	// SAR64rCL
    2123547U,	// SAR64ri
    142157U,	// SAR8m1
    142529U,	// SAR8mCL
    5259465U,	// SAR8mi
    11085U,	// SAR8r1
    11457U,	// SAR8rCL
    2121929U,	// SAR8ri
    6382849U,	// SARX32rm
    35153153U,	// SARX32rr
    6907956U,	// SARX64rm
    35153972U,	// SARX64rr
    534741U,	// SBB16i16
    1067221U,	// SBB16mi
    1067221U,	// SBB16mi8
    1067221U,	// SBB16mr
    1599701U,	// SBB16ri
    1599701U,	// SBB16ri8
    1607893U,	// SBB16rm
    1599701U,	// SBB16rr
    2123989U,	// SBB16rr_REV
    2630159U,	// SBB32i32
    3162639U,	// SBB32mi
    3162639U,	// SBB32mi8
    3162639U,	// SBB32mr
    1597967U,	// SBB32ri
    1597967U,	// SBB32ri8
    1614351U,	// SBB32rm
    1597967U,	// SBB32rr
    2122255U,	// SBB32rr_REV
    3679618U,	// SBB64i32
    4212098U,	// SBB64mi32
    4212098U,	// SBB64mi8
    4212098U,	// SBB64mr
    1598850U,	// SBB64ri32
    1598850U,	// SBB64ri8
    1623426U,	// SBB64rm
    1598850U,	// SBB64rr
    2123138U,	// SBB64rr_REV
    4726880U,	// SBB8i8
    5259360U,	// SBB8mi
    5259360U,	// SBB8mi8
    5259360U,	// SBB8mr
    1597536U,	// SBB8ri
    1597536U,	// SBB8ri8
    57440U,	// SBB8rm
    1597536U,	// SBB8rr
    2121824U,	// SBB8rr_REV
    4874471U,	// SCASB
    2786318U,	// SCASL
    3843930U,	// SCASQ
    707145U,	// SCASW
    5581U,	// SEG_ALLOCA_32
    5581U,	// SEG_ALLOCA_64
    4984U,	// SEH_EndPrologue
    4970U,	// SEH_Epilogue
    12099U,	// SEH_PushFrame
    12144U,	// SEH_PushReg
    123219810U,	// SEH_SaveReg
    123219724U,	// SEH_SaveXMM
    123219795U,	// SEH_SetFrame
    12082U,	// SEH_StackAlloc
    139651U,	// SETAEm
    8579U,	// SETAEr
    139331U,	// SETAm
    8259U,	// SETAr
    139663U,	// SETBEm
    8591U,	// SETBEr
    0U,	// SETB_C16r
    0U,	// SETB_C32r
    0U,	// SETB_C64r
    0U,	// SETB_C8r
    139539U,	// SETBm
    8467U,	// SETBr
    139725U,	// SETEm
    8653U,	// SETEr
    139675U,	// SETGEm
    8603U,	// SETGEr
    139750U,	// SETGm
    8678U,	// SETGr
    139691U,	// SETLEm
    8619U,	// SETLEr
    140439U,	// SETLm
    9367U,	// SETLr
    139711U,	// SETNEm
    8639U,	// SETNEr
    140587U,	// SETNOm
    9515U,	// SETNOr
    140620U,	// SETNPm
    9548U,	// SETNPr
    141445U,	// SETNSm
    10373U,	// SETNSr
    140594U,	// SETOm
    9522U,	// SETOr
    140638U,	// SETPm
    9566U,	// SETPr
    141461U,	// SETSm
    10389U,	// SETSr
    199343U,	// SGDT16m
    197748U,	// SGDT32m
    198573U,	// SGDT64m
    109609U,	// SHL16m1
    110075U,	// SHL16mCL
    1067422U,	// SHL16mi
    11305U,	// SHL16r1
    11771U,	// SHL16rCL
    2124190U,	// SHL16ri
    117641U,	// SHL32m1
    118043U,	// SHL32mCL
    3162930U,	// SHL32mi
    11145U,	// SHL32r1
    11547U,	// SHL32rCL
    2122546U,	// SHL32ri
    125913U,	// SHL64m1
    126347U,	// SHL64mCL
    4212385U,	// SHL64mi
    11225U,	// SHL64r1
    11659U,	// SHL64rCL
    2123425U,	// SHL64ri
    142137U,	// SHL8m1
    142507U,	// SHL8mCL
    5259435U,	// SHL8mi
    11065U,	// SHL8r1
    11435U,	// SHL8rCL
    2121899U,	// SHL8ri
    1068493U,	// SHLD16mrCL
    210331937U,	// SHLD16mri8
    2125261U,	// SHLD16rrCL
    330017U,	// SHLD16rri8
    3165421U,	// SHLD32mrCL
    227107428U,	// SHLD32mri8
    2125037U,	// SHLD32rrCL
    328292U,	// SHLD32rri8
    4214109U,	// SHLD64mrCL
    243885527U,	// SHLD64mri8
    2125149U,	// SHLD64rrCL
    329175U,	// SHLD64rri8
    6382828U,	// SHLX32rm
    35153132U,	// SHLX32rr
    6907935U,	// SHLX64rm
    35153951U,	// SHLX64rr
    109649U,	// SHR16m1
    110119U,	// SHR16mCL
    1067551U,	// SHR16mi
    11345U,	// SHR16r1
    11815U,	// SHR16rCL
    2124319U,	// SHR16ri
    117681U,	// SHR32m1
    118087U,	// SHR32mCL
    3163099U,	// SHR32mi
    11185U,	// SHR32r1
    11591U,	// SHR32rCL
    2122715U,	// SHR32ri
    125953U,	// SHR64m1
    126391U,	// SHR64mCL
    4212519U,	// SHR64mi
    11265U,	// SHR64r1
    11703U,	// SHR64rCL
    2123559U,	// SHR64ri
    142177U,	// SHR8m1
    142551U,	// SHR8mCL
    5259477U,	// SHR8mi
    11105U,	// SHR8r1
    11479U,	// SHR8rCL
    2121941U,	// SHR8ri
    1068505U,	// SHRD16mrCL
    210331953U,	// SHRD16mri8
    2125273U,	// SHRD16rrCL
    330033U,	// SHRD16rri8
    3165433U,	// SHRD32mrCL
    227107444U,	// SHRD32mri8
    2125049U,	// SHRD32rrCL
    328308U,	// SHRD32rri8
    4214121U,	// SHRD64mrCL
    243885543U,	// SHRD64mri8
    2125161U,	// SHRD64rrCL
    329191U,	// SHRD64rri8
    6382856U,	// SHRX32rm
    35153160U,	// SHRX32rr
    6907963U,	// SHRX64rm
    35153979U,	// SHRX64rr
    199357U,	// SIDT16m
    197762U,	// SIDT32m
    198587U,	// SIDT64m
    5806U,	// SKINIT
    109259U,	// SLDT16m
    10955U,	// SLDT16r
    9353U,	// SLDT32r
    108482U,	// SLDT64m
    10178U,	// SLDT64r
    109322U,	// SMSW16m
    11018U,	// SMSW16r
    9422U,	// SMSW32r
    10241U,	// SMSW64r
    4859U,	// STAC
    4913U,	// STC
    4932U,	// STD
    5038U,	// STGI
    5047U,	// STI
    158835U,	// STOSB
    167548U,	// STOSL
    175792U,	// STOSQ
    183883U,	// STOSW
    10819U,	// STR16r
    9216U,	// STR32r
    10060U,	// STR64r
    109123U,	// STRm
    534755U,	// SUB16i16
    1067235U,	// SUB16mi
    1067235U,	// SUB16mi8
    1067235U,	// SUB16mr
    1599715U,	// SUB16ri
    1599715U,	// SUB16ri8
    1607907U,	// SUB16rm
    1599715U,	// SUB16rr
    2124003U,	// SUB16rr_REV
    2630173U,	// SUB32i32
    3162653U,	// SUB32mi
    3162653U,	// SUB32mi8
    3162653U,	// SUB32mr
    1597981U,	// SUB32ri
    1597981U,	// SUB32ri8
    1614365U,	// SUB32rm
    1597981U,	// SUB32rr
    2122269U,	// SUB32rr_REV
    3679632U,	// SUB64i32
    4212112U,	// SUB64mi32
    4212112U,	// SUB64mi8
    4212112U,	// SUB64mr
    1598864U,	// SUB64ri32
    1598864U,	// SUB64ri8
    1623440U,	// SUB64rm
    1598864U,	// SUB64rr
    2123152U,	// SUB64rr_REV
    4726886U,	// SUB8i8
    5259366U,	// SUB8mi
    5259366U,	// SUB8mi8
    5259366U,	// SUB8mr
    1597542U,	// SUB8ri
    1597542U,	// SUB8ri8
    57446U,	// SUB8rm
    1597542U,	// SUB8rr
    2121830U,	// SUB8rr_REV
    5574U,	// SWAPGS
    5125U,	// SYSCALL
    5325U,	// SYSENTER
    5173U,	// SYSEXIT
    5311U,	// SYSEXIT64
    5165U,	// SYSRET
    5303U,	// SYSRET64
    65871U,	// T1MSKC32rm
    22618447U,	// T1MSKC32rr
    82255U,	// T1MSKC64rm
    22618447U,	// T1MSKC64rr
    132418U,	// TAILJMPd
    132418U,	// TAILJMPd64
    132412U,	// TAILJMPd64_REX
    119125U,	// TAILJMPm
    127339U,	// TAILJMPm64
    127333U,	// TAILJMPm64_REX
    0U,	// TAILJMPr
    12651U,	// TAILJMPr64
    12645U,	// TAILJMPr64_REX
    0U,	// TCRETURNdi
    0U,	// TCRETURNdi64
    0U,	// TCRETURNmi
    0U,	// TCRETURNmi64
    0U,	// TCRETURNri
    0U,	// TCRETURNri64
    535279U,	// TEST16i16
    1067759U,	// TEST16mi
    1067759U,	// TEST16mi_alt
    22620911U,	// TEST16ri
    22620911U,	// TEST16ri_alt
    257436399U,	// TEST16rm
    22620911U,	// TEST16rr
    2630835U,	// TEST32i32
    3163315U,	// TEST32mi
    3163315U,	// TEST32mi_alt
    22619315U,	// TEST32ri
    22619315U,	// TEST32ri_alt
    274212019U,	// TEST32rm
    22619315U,	// TEST32rr
    3680230U,	// TEST64i32
    4212710U,	// TEST64mi32
    4212710U,	// TEST64mi32_alt
    22620134U,	// TEST64ri32
    22620134U,	// TEST64ri32_alt
    290990054U,	// TEST64rm
    22620134U,	// TEST64rr
    4727071U,	// TEST8i8
    5259551U,	// TEST8mi
    5259551U,	// TEST8mi_alt
    22618399U,	// TEST8ri
    0U,	// TEST8ri_NOREX
    22618399U,	// TEST8ri_alt
    307765535U,	// TEST8rm
    22618399U,	// TEST8rr
    4539U,	// TLSCall_32
    4629U,	// TLSCall_64
    4552U,	// TLS_addr32
    4642U,	// TLS_addr64
    4565U,	// TLS_base_addr32
    4655U,	// TLS_base_addr64
    4583U,	// TRAP
    101089U,	// TZCNT16rm
    22620897U,	// TZCNT16rr
    66725U,	// TZCNT32rm
    22619301U,	// TZCNT32rr
    83928U,	// TZCNT64rm
    22620120U,	// TZCNT64rr
    66042U,	// TZMSK32rm
    22618618U,	// TZMSK32rr
    82426U,	// TZMSK64rm
    22618618U,	// TZMSK64rr
    4793U,	// UD2B
    844639978U,	// VAARG_64
    1733832474U,	// VASTART_SAVE_XMM_REGS
    108641U,	// VERRm
    10337U,	// VERRr
    109081U,	// VERWm
    10777U,	// VERWr
    5118U,	// VMCALL
    125001U,	// VMCLEARm
    4900U,	// VMFUNC
    5024U,	// VMLAUNCH
    5771U,	// VMLOAD32
    5851U,	// VMLOAD64
    5110U,	// VMMCALL
    123246U,	// VMPTRLDm
    125118U,	// VMPTRSTm
    3162699U,	// VMREAD32rm
    22618699U,	// VMREAD32rr
    4212158U,	// VMREAD64rm
    22619582U,	// VMREAD64rr
    4948U,	// VMRESUME
    5795U,	// VMRUN32
    5875U,	// VMRUN64
    5783U,	// VMSAVE32
    5863U,	// VMSAVE64
    66260U,	// VMWRITE32rm
    22618836U,	// VMWRITE32rr
    83527U,	// VMWRITE64rm
    22619719U,	// VMWRITE64rr
    5007U,	// VMXOFF
    124187U,	// VMXON
    4941U,	// WBINVD
    5206U,	// WIN_ALLOCA
    5051U,	// WIN_FTOL_32
    5051U,	// WIN_FTOL_64
    8883U,	// WRFSBASE
    9766U,	// WRFSBASE64
    8905U,	// WRGSBASE
    9788U,	// WRGSBASE64
    5340U,	// WRMSR
    1067281U,	// XADD16rm
    22620433U,	// XADD16rr
    3162708U,	// XADD32rm
    22618708U,	// XADD32rr
    4212167U,	// XADD64rm
    22619591U,	// XADD64rr
    5259390U,	// XADD8rm
    22618238U,	// XADD8rr
    534908U,	// XCHG16ar
    140061052U,	// XCHG16rm
    324610428U,	// XCHG16rr
    2630389U,	// XCHG32ar
    2630389U,	// XCHG32ar64
    156836597U,	// XCHG32rm
    324608757U,	// XCHG32rr
    3679848U,	// XCHG64ar
    173614696U,	// XCHG64rm
    324609640U,	// XCHG64rr
    190390420U,	// XCHG8rm
    324608148U,	// XCHG8rr
    4864U,	// XCRYPTCBC
    4808U,	// XCRYPTCFB
    5346U,	// XCRYPTCTR
    4798U,	// XCRYPTECB
    4818U,	// XCRYPTOFB
    5681U,	// XGETBV
    4848U,	// XLAT
    535083U,	// XOR16i16
    1067563U,	// XOR16mi
    1067563U,	// XOR16mi8
    1067563U,	// XOR16mr
    1600043U,	// XOR16ri
    1600043U,	// XOR16ri8
    1608235U,	// XOR16rm
    1600043U,	// XOR16rr
    2124331U,	// XOR16rr_REV
    2630631U,	// XOR32i32
    3163111U,	// XOR32mi
    3163111U,	// XOR32mi8
    3163111U,	// XOR32mr
    1598439U,	// XOR32ri
    1598439U,	// XOR32ri8
    1614823U,	// XOR32rm
    1598439U,	// XOR32rr
    2122727U,	// XOR32rr_REV
    3680051U,	// XOR64i32
    4212531U,	// XOR64mi32
    4212531U,	// XOR64mi8
    4212531U,	// XOR64mr
    1599283U,	// XOR64ri32
    1599283U,	// XOR64ri8
    1623859U,	// XOR64rm
    1599283U,	// XOR64rr
    2123571U,	// XOR64rr_REV
    4727009U,	// XOR8i8
    5259489U,	// XOR8mi
    5259489U,	// XOR8mi8
    5259489U,	// XOR8mr
    1597665U,	// XOR8ri
    1597665U,	// XOR8ri8
    57569U,	// XOR8rm
    1597665U,	// XOR8rr
    2121953U,	// XOR8rr_REV
    198745U,	// XRSTOR
    196628U,	// XRSTOR64
    198796U,	// XRSTORS
    196648U,	// XRSTORS64
    197075U,	// XSAVE
    196619U,	// XSAVE64
    196921U,	// XSAVEC
    196609U,	// XSAVEC64
    198824U,	// XSAVEOPT
    196659U,	// XSAVEOPT64
    198772U,	// XSAVES
    196638U,	// XSAVES64
    5688U,	// XSETBV
    4491U,	// XSHA1
    4686U,	// XSHA256
    4963U,	// XSTORE
    0U
  };

#ifndef CAPSTONE_DIET
  static char AsmStrs[] = {
  /* 0 */ 'x', 's', 'a', 'v', 'e', 'c', '6', '4', 9, 0,
  /* 10 */ 'x', 's', 'a', 'v', 'e', '6', '4', 9, 0,
  /* 19 */ 'x', 'r', 's', 't', 'o', 'r', '6', '4', 9, 0,
  /* 29 */ 'x', 's', 'a', 'v', 'e', 's', '6', '4', 9, 0,
  /* 39 */ 'x', 'r', 's', 't', 'o', 'r', 's', '6', '4', 9, 0,
  /* 50 */ 'x', 's', 'a', 'v', 'e', 'o', 'p', 't', '6', '4', 9, 0,
  /* 62 */ 'j', 'a', 9, 0,
  /* 66 */ 's', 'e', 't', 'a', 9, 0,
  /* 72 */ 'c', 'm', 'p', 'x', 'c', 'h', 'g', '1', '6', 'b', 9, 0,
  /* 84 */ 'c', 'm', 'p', 'x', 'c', 'h', 'g', '8', 'b', 9, 0,
  /* 95 */ 's', 'b', 'b', 'b', 9, 0,
  /* 101 */ 's', 'u', 'b', 'b', 9, 0,
  /* 107 */ 'a', 'd', 'c', 'b', 9, 0,
  /* 113 */ 'd', 'e', 'c', 'b', 9, 0,
  /* 119 */ 'i', 'n', 'c', 'b', 9, 0,
  /* 125 */ 'x', 'a', 'd', 'd', 'b', 9, 0,
  /* 132 */ 'a', 'n', 'd', 'b', 9, 0,
  /* 138 */ 'n', 'e', 'g', 'b', 9, 0,
  /* 144 */ 'c', 'm', 'p', 'x', 'c', 'h', 'g', 'b', 9, 0,
  /* 154 */ 'j', 'b', 9, 0,
  /* 158 */ 's', 'a', 'l', 'b', 9, 0,
  /* 164 */ 'r', 'c', 'l', 'b', 9, 0,
  /* 170 */ 's', 'h', 'l', 'b', 9, 0,
  /* 176 */ 'r', 'o', 'l', 'b', 9, 0,
  /* 182 */ 'i', 'm', 'u', 'l', 'b', 9, 0,
  /* 189 */ 'i', 'n', 'b', 9, 0,
  /* 194 */ 'c', 'm', 'p', 'b', 9, 0,
  /* 200 */ 's', 'a', 'r', 'b', 9, 0,
  /* 206 */ 'r', 'c', 'r', 'b', 9, 0,
  /* 212 */ 's', 'h', 'r', 'b', 9, 0,
  /* 218 */ 'r', 'o', 'r', 'b', 9, 0,
  /* 224 */ 'x', 'o', 'r', 'b', 9, 0,
  /* 230 */ 's', 'c', 'a', 's', 'b', 9, 0,
  /* 237 */ 'm', 'o', 'v', 'a', 'b', 's', 'b', 9, 0,
  /* 246 */ 'l', 'o', 'd', 's', 'b', 9, 0,
  /* 253 */ 'c', 'm', 'p', 's', 'b', 9, 0,
  /* 260 */ 'o', 'u', 't', 's', 'b', 9, 0,
  /* 267 */ 'm', 'o', 'v', 's', 'b', 9, 0,
  /* 274 */ 's', 'e', 't', 'b', 9, 0,
  /* 280 */ 'n', 'o', 't', 'b', 9, 0,
  /* 286 */ 't', 'e', 's', 't', 'b', 9, 0,
  /* 293 */ 'i', 'd', 'i', 'v', 'b', 9, 0,
  /* 300 */ 'm', 'o', 'v', 'b', 9, 0,
  /* 306 */ 'c', 'l', 'w', 'b', 9, 0,
  /* 312 */ 'x', 's', 'a', 'v', 'e', 'c', 9, 0,
  /* 320 */ 'b', 'l', 'c', 'i', 'c', 9, 0,
  /* 327 */ 'b', 'l', 's', 'i', 'c', 9, 0,
  /* 334 */ 't', '1', 'm', 's', 'k', 'c', 9, 0,
  /* 342 */ 'a', 'a', 'd', 9, 0,
  /* 347 */ 'i', 'n', 'v', 'p', 'c', 'i', 'd', 9, 0,
  /* 356 */ 'i', 'n', 'v', 'v', 'p', 'i', 'd', 9, 0,
  /* 365 */ 'v', 'm', 'p', 't', 'r', 'l', 'd', 9, 0,
  /* 374 */ 'b', 'o', 'u', 'n', 'd', 9, 0,
  /* 381 */ 'j', 'a', 'e', 9, 0,
  /* 386 */ 's', 'e', 't', 'a', 'e', 9, 0,
  /* 393 */ 'j', 'b', 'e', 9, 0,
  /* 398 */ 's', 'e', 't', 'b', 'e', 9, 0,
  /* 405 */ 'j', 'g', 'e', 9, 0,
  /* 410 */ 's', 'e', 't', 'g', 'e', 9, 0,
  /* 417 */ 'j', 'e', 9, 0,
  /* 421 */ 'j', 'l', 'e', 9, 0,
  /* 426 */ 's', 'e', 't', 'l', 'e', 9, 0,
  /* 433 */ 'j', 'n', 'e', 9, 0,
  /* 438 */ 'l', 'o', 'o', 'p', 'n', 'e', 9, 0,
  /* 446 */ 's', 'e', 't', 'n', 'e', 9, 0,
  /* 453 */ 'l', 'o', 'o', 'p', 'e', 9, 0,
  /* 460 */ 's', 'e', 't', 'e', 9, 0,
  /* 466 */ 'x', 's', 'a', 'v', 'e', 9, 0,
  /* 473 */ 'j', 'g', 9, 0,
  /* 477 */ 'i', 'n', 'v', 'l', 'p', 'g', 9, 0,
  /* 485 */ 's', 'e', 't', 'g', 9, 0,
  /* 491 */ 'b', 'l', 'c', 'i', 9, 0,
  /* 497 */ 'b', 'l', 'c', 'm', 's', 'k', 9, 0,
  /* 505 */ 't', 'z', 'm', 's', 'k', 9, 0,
  /* 512 */ 'l', 'e', 'a', 'l', 9, 0,
  /* 518 */ 'c', 'm', 'o', 'v', 'a', 'l', 9, 0,
  /* 526 */ 's', 'b', 'b', 'l', 9, 0,
  /* 532 */ 'm', 'o', 'v', 's', 'b', 'l', 9, 0,
  /* 540 */ 's', 'u', 'b', 'l', 9, 0,
  /* 546 */ 'c', 'm', 'o', 'v', 'b', 'l', 9, 0,
  /* 554 */ 'm', 'o', 'v', 'z', 'b', 'l', 9, 0,
  /* 562 */ 'a', 'd', 'c', 'l', 9, 0,
  /* 568 */ 'd', 'e', 'c', 'l', 9, 0,
  /* 574 */ 'i', 'n', 'c', 'l', 9, 0,
  /* 580 */ 'b', 't', 'c', 'l', 9, 0,
  /* 586 */ 'v', 'm', 'r', 'e', 'a', 'd', 'l', 9, 0,
  /* 595 */ 'x', 'a', 'd', 'd', 'l', 9, 0,
  /* 602 */ 'r', 'd', 's', 'e', 'e', 'd', 'l', 9, 0,
  /* 611 */ 's', 'h', 'l', 'd', 'l', 9, 0,
  /* 618 */ 'r', 'd', 'r', 'a', 'n', 'd', 'l', 9, 0,
  /* 627 */ 's', 'h', 'r', 'd', 'l', 9, 0,
  /* 634 */ 'c', 'm', 'o', 'v', 'a', 'e', 'l', 9, 0,
  /* 643 */ 'c', 'm', 'o', 'v', 'b', 'e', 'l', 9, 0,
  /* 652 */ 'c', 'm', 'o', 'v', 'g', 'e', 'l', 9, 0,
  /* 661 */ 'c', 'm', 'o', 'v', 'l', 'e', 'l', 9, 0,
  /* 670 */ 'c', 'm', 'o', 'v', 'n', 'e', 'l', 9, 0,
  /* 679 */ 'r', 'd', 'f', 's', 'b', 'a', 's', 'e', 'l', 9, 0,
  /* 690 */ 'w', 'r', 'f', 's', 'b', 'a', 's', 'e', 'l', 9, 0,
  /* 701 */ 'r', 'd', 'g', 's', 'b', 'a', 's', 'e', 'l', 9, 0,
  /* 712 */ 'w', 'r', 'g', 's', 'b', 'a', 's', 'e', 'l', 9, 0,
  /* 723 */ 'v', 'm', 'w', 'r', 'i', 't', 'e', 'l', 9, 0,
  /* 733 */ 'c', 'm', 'o', 'v', 'e', 'l', 9, 0,
  /* 741 */ 'b', 's', 'f', 'l', 9, 0,
  /* 747 */ 'n', 'e', 'g', 'l', 9, 0,
  /* 753 */ 'c', 'm', 'p', 'x', 'c', 'h', 'g', 'l', 9, 0,
  /* 763 */ 'c', 'm', 'o', 'v', 'g', 'l', 9, 0,
  /* 771 */ 'p', 'u', 's', 'h', 'l', 9, 0,
  /* 778 */ 'b', 'z', 'h', 'i', 'l', 9, 0,
  /* 785 */ 'b', 'l', 's', 'i', 'l', 9, 0,
  /* 792 */ 'j', 'l', 9, 0,
  /* 796 */ 'b', 'l', 's', 'm', 's', 'k', 'l', 9, 0,
  /* 805 */ 's', 'a', 'l', 'l', 9, 0,
  /* 811 */ 'r', 'c', 'l', 'l', 9, 0,
  /* 817 */ 's', 'h', 'l', 'l', 9, 0,
  /* 823 */ 'b', 'l', 'c', 'f', 'i', 'l', 'l', 9, 0,
  /* 832 */ 'b', 'l', 's', 'f', 'i', 'l', 'l', 9, 0,
  /* 841 */ 'l', 'c', 'a', 'l', 'l', 'l', 9, 0,
  /* 849 */ 'r', 'o', 'l', 'l', 9, 0,
  /* 855 */ 'l', 's', 'l', 'l', 9, 0,
  /* 861 */ 'i', 'm', 'u', 'l', 'l', 9, 0,
  /* 868 */ 'c', 'm', 'o', 'v', 'l', 'l', 9, 0,
  /* 876 */ 'a', 'n', 'd', 'n', 'l', 9, 0,
  /* 883 */ 'i', 'n', 'l', 9, 0,
  /* 888 */ 'c', 'm', 'o', 'v', 'n', 'o', 'l', 9, 0,
  /* 897 */ 'c', 'm', 'o', 'v', 'o', 'l', 9, 0,
  /* 905 */ 'b', 's', 'w', 'a', 'p', 'l', 9, 0,
  /* 913 */ 'p', 'd', 'e', 'p', 'l', 9, 0,
  /* 920 */ 'c', 'm', 'p', 'l', 9, 0,
  /* 926 */ 'l', 'j', 'm', 'p', 'l', 9, 0,
  /* 933 */ 'c', 'm', 'o', 'v', 'n', 'p', 'l', 9, 0,
  /* 942 */ 'n', 'o', 'p', 'l', 9, 0,
  /* 948 */ 'p', 'o', 'p', 'l', 9, 0,
  /* 954 */ 'a', 'r', 'p', 'l', 9, 0,
  /* 960 */ 'c', 'm', 'o', 'v', 'p', 'l', 9, 0,
  /* 968 */ 'l', 'a', 'r', 'l', 9, 0,
  /* 974 */ 's', 'a', 'r', 'l', 9, 0,
  /* 980 */ 'r', 'c', 'r', 'l', 9, 0,
  /* 986 */ 's', 'h', 'r', 'l', 9, 0,
  /* 992 */ 'r', 'o', 'r', 'l', 9, 0,
  /* 998 */ 'x', 'o', 'r', 'l', 9, 0,
  /* 1004 */ 'b', 's', 'r', 'l', 9, 0,
  /* 1010 */ 'b', 'l', 's', 'r', 'l', 9, 0,
  /* 1017 */ 'b', 't', 'r', 'l', 9, 0,
  /* 1023 */ 's', 't', 'r', 'l', 9, 0,
  /* 1029 */ 'b', 'e', 'x', 't', 'r', 'l', 9, 0,
  /* 1037 */ 's', 'c', 'a', 's', 'l', 9, 0,
  /* 1044 */ 'm', 'o', 'v', 'a', 'b', 's', 'l', 9, 0,
  /* 1053 */ 'l', 'd', 's', 'l', 9, 0,
  /* 1059 */ 'l', 'o', 'd', 's', 'l', 9, 0,
  /* 1066 */ 'l', 'e', 's', 'l', 9, 0,
  /* 1072 */ 'l', 'f', 's', 'l', 9, 0,
  /* 1078 */ 'l', 'g', 's', 'l', 9, 0,
  /* 1084 */ 'c', 'm', 'o', 'v', 'n', 's', 'l', 9, 0,
  /* 1093 */ 'c', 'm', 'p', 's', 'l', 9, 0,
  /* 1100 */ 'l', 's', 's', 'l', 9, 0,
  /* 1106 */ 'b', 't', 's', 'l', 9, 0,
  /* 1112 */ 'o', 'u', 't', 's', 'l', 9, 0,
  /* 1119 */ 'c', 'm', 'o', 'v', 's', 'l', 9, 0,
  /* 1127 */ 'b', 't', 'l', 9, 0,
  /* 1132 */ 'l', 'g', 'd', 't', 'l', 9, 0,
  /* 1139 */ 's', 'g', 'd', 't', 'l', 9, 0,
  /* 1146 */ 'l', 'i', 'd', 't', 'l', 9, 0,
  /* 1153 */ 's', 'i', 'd', 't', 'l', 9, 0,
  /* 1160 */ 's', 'l', 'd', 't', 'l', 9, 0,
  /* 1167 */ 'l', 'r', 'e', 't', 'l', 9, 0,
  /* 1174 */ 's', 'e', 't', 'l', 9, 0,
  /* 1180 */ 'l', 'z', 'c', 'n', 't', 'l', 9, 0,
  /* 1188 */ 't', 'z', 'c', 'n', 't', 'l', 9, 0,
  /* 1196 */ 'n', 'o', 't', 'l', 9, 0,
  /* 1202 */ 't', 'e', 's', 't', 'l', 9, 0,
  /* 1209 */ 'p', 'e', 'x', 't', 'l', 9, 0,
  /* 1216 */ 'i', 'd', 'i', 'v', 'l', 9, 0,
  /* 1223 */ 'm', 'o', 'v', 'l', 9, 0,
  /* 1229 */ 's', 'm', 's', 'w', 'l', 9, 0,
  /* 1236 */ 'm', 'o', 'v', 's', 'w', 'l', 9, 0,
  /* 1244 */ 'm', 'o', 'v', 'z', 'w', 'l', 9, 0,
  /* 1252 */ 'a', 'd', 'c', 'x', 'l', 9, 0,
  /* 1259 */ 's', 'h', 'l', 'x', 'l', 9, 0,
  /* 1266 */ 'm', 'u', 'l', 'x', 'l', 9, 0,
  /* 1273 */ 'a', 'd', 'o', 'x', 'l', 9, 0,
  /* 1280 */ 's', 'a', 'r', 'x', 'l', 9, 0,
  /* 1287 */ 's', 'h', 'r', 'x', 'l', 9, 0,
  /* 1294 */ 'r', 'o', 'r', 'x', 'l', 9, 0,
  /* 1301 */ 'a', 'a', 'm', 9, 0,
  /* 1306 */ 'v', 'm', 'x', 'o', 'n', 9, 0,
  /* 1313 */ 'j', 'o', 9, 0,
  /* 1317 */ 'j', 'n', 'o', 9, 0,
  /* 1322 */ 's', 'e', 't', 'n', 'o', 9, 0,
  /* 1329 */ 's', 'e', 't', 'o', 9, 0,
  /* 1335 */ 'j', 'p', 9, 0,
  /* 1339 */ 'r', 'e', 'x', '6', '4', 32, 'j', 'm', 'p', 9, 0,
  /* 1350 */ 'j', 'n', 'p', 9, 0,
  /* 1355 */ 's', 'e', 't', 'n', 'p', 9, 0,
  /* 1362 */ 'n', 'o', 'p', 9, 0,
  /* 1367 */ 'l', 'o', 'o', 'p', 9, 0,
  /* 1373 */ 's', 'e', 't', 'p', 9, 0,
  /* 1379 */ '#', 'E', 'H', '_', 'S', 'j', 'L', 'j', '_', 'S', 'e', 't', 'u', 'p', 9, 0,
  /* 1395 */ 'l', 'e', 'a', 'q', 9, 0,
  /* 1401 */ 'c', 'm', 'o', 'v', 'a', 'q', 9, 0,
  /* 1409 */ 's', 'b', 'b', 'q', 9, 0,
  /* 1415 */ 'm', 'o', 'v', 's', 'b', 'q', 9, 0,
  /* 1423 */ 's', 'u', 'b', 'q', 9, 0,
  /* 1429 */ 'c', 'm', 'o', 'v', 'b', 'q', 9, 0,
  /* 1437 */ 'm', 'o', 'v', 'z', 'b', 'q', 9, 0,
  /* 1445 */ 'a', 'd', 'c', 'q', 9, 0,
  /* 1451 */ 'd', 'e', 'c', 'q', 9, 0,
  /* 1457 */ 'i', 'n', 'c', 'q', 9, 0,
  /* 1463 */ 'b', 't', 'c', 'q', 9, 0,
  /* 1469 */ 'v', 'm', 'r', 'e', 'a', 'd', 'q', 9, 0,
  /* 1478 */ 'x', 'a', 'd', 'd', 'q', 9, 0,
  /* 1485 */ 'r', 'd', 's', 'e', 'e', 'd', 'q', 9, 0,
  /* 1494 */ 's', 'h', 'l', 'd', 'q', 9, 0,
  /* 1501 */ 'r', 'd', 'r', 'a', 'n', 'd', 'q', 9, 0,
  /* 1510 */ 's', 'h', 'r', 'd', 'q', 9, 0,
  /* 1517 */ 'c', 'm', 'o', 'v', 'a', 'e', 'q', 9, 0,
  /* 1526 */ 'c', 'm', 'o', 'v', 'b', 'e', 'q', 9, 0,
  /* 1535 */ 'c', 'm', 'o', 'v', 'g', 'e', 'q', 9, 0,
  /* 1544 */ 'c', 'm', 'o', 'v', 'l', 'e', 'q', 9, 0,
  /* 1553 */ 'c', 'm', 'o', 'v', 'n', 'e', 'q', 9, 0,
  /* 1562 */ 'r', 'd', 'f', 's', 'b', 'a', 's', 'e', 'q', 9, 0,
  /* 1573 */ 'w', 'r', 'f', 's', 'b', 'a', 's', 'e', 'q', 9, 0,
  /* 1584 */ 'r', 'd', 'g', 's', 'b', 'a', 's', 'e', 'q', 9, 0,
  /* 1595 */ 'w', 'r', 'g', 's', 'b', 'a', 's', 'e', 'q', 9, 0,
  /* 1606 */ 'v', 'm', 'w', 'r', 'i', 't', 'e', 'q', 9, 0,
  /* 1616 */ 'c', 'm', 'o', 'v', 'e', 'q', 9, 0,
  /* 1624 */ 'b', 's', 'f', 'q', 9, 0,
  /* 1630 */ 'n', 'e', 'g', 'q', 9, 0,
  /* 1636 */ 'c', 'm', 'p', 'x', 'c', 'h', 'g', 'q', 9, 0,
  /* 1646 */ 'c', 'm', 'o', 'v', 'g', 'q', 9, 0,
  /* 1654 */ 'p', 'u', 's', 'h', 'q', 9, 0,
  /* 1661 */ 'b', 'z', 'h', 'i', 'q', 9, 0,
  /* 1668 */ 'b', 'l', 's', 'i', 'q', 9, 0,
  /* 1675 */ 'b', 'l', 's', 'm', 's', 'k', 'q', 9, 0,
  /* 1684 */ 's', 'a', 'l', 'q', 9, 0,
  /* 1690 */ 'r', 'c', 'l', 'q', 9, 0,
  /* 1696 */ 's', 'h', 'l', 'q', 9, 0,
  /* 1702 */ 'c', 'a', 'l', 'l', 'q', 9, 0,
  /* 1709 */ 'r', 'o', 'l', 'q', 9, 0,
  /* 1715 */ 'l', 's', 'l', 'q', 9, 0,
  /* 1721 */ 'm', 'o', 'v', 's', 'l', 'q', 9, 0,
  /* 1729 */ 'i', 'm', 'u', 'l', 'q', 9, 0,
  /* 1736 */ 'c', 'm', 'o', 'v', 'l', 'q', 9, 0,
  /* 1744 */ 'a', 'n', 'd', 'n', 'q', 9, 0,
  /* 1751 */ 'c', 'm', 'o', 'v', 'n', 'o', 'q', 9, 0,
  /* 1760 */ 'c', 'm', 'o', 'v', 'o', 'q', 9, 0,
  /* 1768 */ 'b', 's', 'w', 'a', 'p', 'q', 9, 0,
  /* 1776 */ 'p', 'd', 'e', 'p', 'q', 9, 0,
  /* 1783 */ 'c', 'm', 'p', 'q', 9, 0,
  /* 1789 */ 'c', 'm', 'o', 'v', 'n', 'p', 'q', 9, 0,
  /* 1798 */ 'p', 'o', 'p', 'q', 9, 0,
  /* 1804 */ 'c', 'm', 'o', 'v', 'p', 'q', 9, 0,
  /* 1812 */ 'l', 'a', 'r', 'q', 9, 0,
  /* 1818 */ 's', 'a', 'r', 'q', 9, 0,
  /* 1824 */ 'r', 'c', 'r', 'q', 9, 0,
  /* 1830 */ 's', 'h', 'r', 'q', 9, 0,
  /* 1836 */ 'r', 'o', 'r', 'q', 9, 0,
  /* 1842 */ 'x', 'o', 'r', 'q', 9, 0,
  /* 1848 */ 'b', 's', 'r', 'q', 9, 0,
  /* 1854 */ 'b', 'l', 's', 'r', 'q', 9, 0,
  /* 1861 */ 'b', 't', 'r', 'q', 9, 0,
  /* 1867 */ 's', 't', 'r', 'q', 9, 0,
  /* 1873 */ 'b', 'e', 'x', 't', 'r', 'q', 9, 0,
  /* 1881 */ 's', 'c', 'a', 's', 'q', 9, 0,
  /* 1888 */ 'm', 'o', 'v', 'a', 'b', 's', 'q', 9, 0,
  /* 1897 */ 'l', 'o', 'd', 's', 'q', 9, 0,
  /* 1904 */ 'l', 'f', 's', 'q', 9, 0,
  /* 1910 */ 'l', 'g', 's', 'q', 9, 0,
  /* 1916 */ 'c', 'm', 'o', 'v', 'n', 's', 'q', 9, 0,
  /* 1925 */ 'c', 'm', 'p', 's', 'q', 9, 0,
  /* 1932 */ 'l', 's', 's', 'q', 9, 0,
  /* 1938 */ 'b', 't', 's', 'q', 9, 0,
  /* 1944 */ 'c', 'm', 'o', 'v', 's', 'q', 9, 0,
  /* 1952 */ 'b', 't', 'q', 9, 0,
  /* 1957 */ 'l', 'g', 'd', 't', 'q', 9, 0,
  /* 1964 */ 's', 'g', 'd', 't', 'q', 9, 0,
  /* 1971 */ 'l', 'i', 'd', 't', 'q', 9, 0,
  /* 1978 */ 's', 'i', 'd', 't', 'q', 9, 0,
  /* 1985 */ 's', 'l', 'd', 't', 'q', 9, 0,
  /* 1992 */ 'l', 'r', 'e', 't', 'q', 9, 0,
  /* 1999 */ 'l', 'z', 'c', 'n', 't', 'q', 9, 0,
  /* 2007 */ 't', 'z', 'c', 'n', 't', 'q', 9, 0,
  /* 2015 */ 'n', 'o', 't', 'q', 9, 0,
  /* 2021 */ 't', 'e', 's', 't', 'q', 9, 0,
  /* 2028 */ 'p', 'e', 'x', 't', 'q', 9, 0,
  /* 2035 */ 'i', 'd', 'i', 'v', 'q', 9, 0,
  /* 2042 */ 'm', 'o', 'v', 'q', 9, 0,
  /* 2048 */ 's', 'm', 's', 'w', 'q', 9, 0,
  /* 2055 */ 'm', 'o', 'v', 's', 'w', 'q', 9, 0,
  /* 2063 */ 'm', 'o', 'v', 'z', 'w', 'q', 9, 0,
  /* 2071 */ 'a', 'd', 'c', 'x', 'q', 9, 0,
  /* 2078 */ 's', 'h', 'l', 'x', 'q', 9, 0,
  /* 2085 */ 'm', 'u', 'l', 'x', 'q', 9, 0,
  /* 2092 */ 'a', 'd', 'o', 'x', 'q', 9, 0,
  /* 2099 */ 's', 'a', 'r', 'x', 'q', 9, 0,
  /* 2106 */ 's', 'h', 'r', 'x', 'q', 9, 0,
  /* 2113 */ 'r', 'o', 'r', 'x', 'q', 9, 0,
  /* 2120 */ 'v', 'm', 'c', 'l', 'e', 'a', 'r', 9, 0,
  /* 2129 */ 'e', 'n', 't', 'e', 'r', 9, 0,
  /* 2136 */ 'x', 'r', 's', 't', 'o', 'r', 9, 0,
  /* 2144 */ 'v', 'e', 'r', 'r', 9, 0,
  /* 2150 */ 'b', 'e', 'x', 't', 'r', 9, 0,
  /* 2157 */ 'b', 'l', 'c', 's', 9, 0,
  /* 2163 */ 'x', 's', 'a', 'v', 'e', 's', 9, 0,
  /* 2171 */ 'j', 's', 9, 0,
  /* 2175 */ 'j', 'n', 's', 9, 0,
  /* 2180 */ 's', 'e', 't', 'n', 's', 9, 0,
  /* 2187 */ 'x', 'r', 's', 't', 'o', 'r', 's', 9, 0,
  /* 2196 */ 's', 'e', 't', 's', 9, 0,
  /* 2202 */ 'i', 'n', 't', 9, 0,
  /* 2207 */ 'i', 'n', 'v', 'e', 'p', 't', 9, 0,
  /* 2215 */ 'x', 's', 'a', 'v', 'e', 'o', 'p', 't', 9, 0,
  /* 2225 */ 'c', 'l', 'f', 'l', 'u', 's', 'h', 'o', 'p', 't', 9, 0,
  /* 2237 */ 'v', 'm', 'p', 't', 'r', 's', 't', 9, 0,
  /* 2246 */ 'l', 'e', 'a', 'w', 9, 0,
  /* 2252 */ 'c', 'm', 'o', 'v', 'a', 'w', 9, 0,
  /* 2260 */ 's', 'b', 'b', 'w', 9, 0,
  /* 2266 */ 'm', 'o', 'v', 's', 'b', 'w', 9, 0,
  /* 2274 */ 's', 'u', 'b', 'w', 9, 0,
  /* 2280 */ 'c', 'm', 'o', 'v', 'b', 'w', 9, 0,
  /* 2288 */ 'm', 'o', 'v', 'z', 'b', 'w', 9, 0,
  /* 2296 */ 'a', 'd', 'c', 'w', 9, 0,
  /* 2302 */ 'd', 'e', 'c', 'w', 9, 0,
  /* 2308 */ 'i', 'n', 'c', 'w', 9, 0,
  /* 2314 */ 'b', 't', 'c', 'w', 9, 0,
  /* 2320 */ 'x', 'a', 'd', 'd', 'w', 9, 0,
  /* 2327 */ 'r', 'd', 's', 'e', 'e', 'd', 'w', 9, 0,
  /* 2336 */ 's', 'h', 'l', 'd', 'w', 9, 0,
  /* 2343 */ 'r', 'd', 'r', 'a', 'n', 'd', 'w', 9, 0,
  /* 2352 */ 's', 'h', 'r', 'd', 'w', 9, 0,
  /* 2359 */ 'c', 'm', 'o', 'v', 'a', 'e', 'w', 9, 0,
  /* 2368 */ 'c', 'm', 'o', 'v', 'b', 'e', 'w', 9, 0,
  /* 2377 */ 'c', 'm', 'o', 'v', 'g', 'e', 'w', 9, 0,
  /* 2386 */ 'c', 'm', 'o', 'v', 'l', 'e', 'w', 9, 0,
  /* 2395 */ 'c', 'm', 'o', 'v', 'n', 'e', 'w', 9, 0,
  /* 2404 */ 'c', 'm', 'o', 'v', 'e', 'w', 9, 0,
  /* 2412 */ 'b', 's', 'f', 'w', 9, 0,
  /* 2418 */ 'n', 'e', 'g', 'w', 9, 0,
  /* 2424 */ 'c', 'm', 'p', 'x', 'c', 'h', 'g', 'w', 9, 0,
  /* 2434 */ 'c', 'm', 'o', 'v', 'g', 'w', 9, 0,
  /* 2442 */ 'p', 'u', 's', 'h', 'w', 9, 0,
  /* 2449 */ 's', 'a', 'l', 'w', 9, 0,
  /* 2455 */ 'r', 'c', 'l', 'w', 9, 0,
  /* 2461 */ 's', 'h', 'l', 'w', 9, 0,
  /* 2467 */ 'l', 'c', 'a', 'l', 'l', 'w', 9, 0,
  /* 2475 */ 'r', 'o', 'l', 'w', 9, 0,
  /* 2481 */ 'l', 's', 'l', 'w', 9, 0,
  /* 2487 */ 'i', 'm', 'u', 'l', 'w', 9, 0,
  /* 2494 */ 'c', 'm', 'o', 'v', 'l', 'w', 9, 0,
  /* 2502 */ 'i', 'n', 'w', 9, 0,
  /* 2507 */ 'c', 'm', 'o', 'v', 'n', 'o', 'w', 9, 0,
  /* 2516 */ 'c', 'm', 'o', 'v', 'o', 'w', 9, 0,
  /* 2524 */ 'c', 'm', 'p', 'w', 9, 0,
  /* 2530 */ 'l', 'j', 'm', 'p', 'w', 9, 0,
  /* 2537 */ 'c', 'm', 'o', 'v', 'n', 'p', 'w', 9, 0,
  /* 2546 */ 'n', 'o', 'p', 'w', 9, 0,
  /* 2552 */ 'p', 'o', 'p', 'w', 9, 0,
  /* 2558 */ 'c', 'm', 'o', 'v', 'p', 'w', 9, 0,
  /* 2566 */ 'l', 'a', 'r', 'w', 9, 0,
  /* 2572 */ 's', 'a', 'r', 'w', 9, 0,
  /* 2578 */ 'r', 'c', 'r', 'w', 9, 0,
  /* 2584 */ 'v', 'e', 'r', 'w', 9, 0,
  /* 2590 */ 's', 'h', 'r', 'w', 9, 0,
  /* 2596 */ 'r', 'o', 'r', 'w', 9, 0,
  /* 2602 */ 'x', 'o', 'r', 'w', 9, 0,
  /* 2608 */ 'b', 's', 'r', 'w', 9, 0,
  /* 2614 */ 'b', 't', 'r', 'w', 9, 0,
  /* 2620 */ 'l', 't', 'r', 'w', 9, 0,
  /* 2626 */ 's', 't', 'r', 'w', 9, 0,
  /* 2632 */ 's', 'c', 'a', 's', 'w', 9, 0,
  /* 2639 */ 'm', 'o', 'v', 'a', 'b', 's', 'w', 9, 0,
  /* 2648 */ 'l', 'd', 's', 'w', 9, 0,
  /* 2654 */ 'l', 'o', 'd', 's', 'w', 9, 0,
  /* 2661 */ 'l', 'e', 's', 'w', 9, 0,
  /* 2667 */ 'l', 'f', 's', 'w', 9, 0,
  /* 2673 */ 'l', 'g', 's', 'w', 9, 0,
  /* 2679 */ 'c', 'm', 'o', 'v', 'n', 's', 'w', 9, 0,
  /* 2688 */ 'c', 'm', 'p', 's', 'w', 9, 0,
  /* 2695 */ 'l', 's', 's', 'w', 9, 0,
  /* 2701 */ 'b', 't', 's', 'w', 9, 0,
  /* 2707 */ 'o', 'u', 't', 's', 'w', 9, 0,
  /* 2714 */ 'c', 'm', 'o', 'v', 's', 'w', 9, 0,
  /* 2722 */ 'b', 't', 'w', 9, 0,
  /* 2727 */ 'l', 'g', 'd', 't', 'w', 9, 0,
  /* 2734 */ 's', 'g', 'd', 't', 'w', 9, 0,
  /* 2741 */ 'l', 'i', 'd', 't', 'w', 9, 0,
  /* 2748 */ 's', 'i', 'd', 't', 'w', 9, 0,
  /* 2755 */ 'l', 'l', 'd', 't', 'w', 9, 0,
  /* 2762 */ 's', 'l', 'd', 't', 'w', 9, 0,
  /* 2769 */ 'l', 'r', 'e', 't', 'w', 9, 0,
  /* 2776 */ 'l', 'z', 'c', 'n', 't', 'w', 9, 0,
  /* 2784 */ 't', 'z', 'c', 'n', 't', 'w', 9, 0,
  /* 2792 */ 'n', 'o', 't', 'w', 9, 0,
  /* 2798 */ 't', 'e', 's', 't', 'w', 9, 0,
  /* 2805 */ 'i', 'd', 'i', 'v', 'w', 9, 0,
  /* 2812 */ 'm', 'o', 'v', 'w', 9, 0,
  /* 2818 */ 'l', 'm', 's', 'w', 'w', 9, 0,
  /* 2825 */ 's', 'm', 's', 'w', 'w', 9, 0,
  /* 2832 */ 'j', 'e', 'c', 'x', 'z', 9, 0,
  /* 2839 */ 'j', 'c', 'x', 'z', 9, 0,
  /* 2845 */ 'j', 'r', 'c', 'x', 'z', 9, 0,
  /* 2852 */ 's', 'a', 'l', 'b', 9, '$', '1', ',', 32, 0,
  /* 2862 */ 'r', 'c', 'l', 'b', 9, '$', '1', ',', 32, 0,
  /* 2872 */ 's', 'h', 'l', 'b', 9, '$', '1', ',', 32, 0,
  /* 2882 */ 'r', 'o', 'l', 'b', 9, '$', '1', ',', 32, 0,
  /* 2892 */ 's', 'a', 'r', 'b', 9, '$', '1', ',', 32, 0,
  /* 2902 */ 'r', 'c', 'r', 'b', 9, '$', '1', ',', 32, 0,
  /* 2912 */ 's', 'h', 'r', 'b', 9, '$', '1', ',', 32, 0,
  /* 2922 */ 'r', 'o', 'r', 'b', 9, '$', '1', ',', 32, 0,
  /* 2932 */ 's', 'a', 'l', 'l', 9, '$', '1', ',', 32, 0,
  /* 2942 */ 'r', 'c', 'l', 'l', 9, '$', '1', ',', 32, 0,
  /* 2952 */ 's', 'h', 'l', 'l', 9, '$', '1', ',', 32, 0,
  /* 2962 */ 'r', 'o', 'l', 'l', 9, '$', '1', ',', 32, 0,
  /* 2972 */ 's', 'a', 'r', 'l', 9, '$', '1', ',', 32, 0,
  /* 2982 */ 'r', 'c', 'r', 'l', 9, '$', '1', ',', 32, 0,
  /* 2992 */ 's', 'h', 'r', 'l', 9, '$', '1', ',', 32, 0,
  /* 3002 */ 'r', 'o', 'r', 'l', 9, '$', '1', ',', 32, 0,
  /* 3012 */ 's', 'a', 'l', 'q', 9, '$', '1', ',', 32, 0,
  /* 3022 */ 'r', 'c', 'l', 'q', 9, '$', '1', ',', 32, 0,
  /* 3032 */ 's', 'h', 'l', 'q', 9, '$', '1', ',', 32, 0,
  /* 3042 */ 'r', 'o', 'l', 'q', 9, '$', '1', ',', 32, 0,
  /* 3052 */ 's', 'a', 'r', 'q', 9, '$', '1', ',', 32, 0,
  /* 3062 */ 'r', 'c', 'r', 'q', 9, '$', '1', ',', 32, 0,
  /* 3072 */ 's', 'h', 'r', 'q', 9, '$', '1', ',', 32, 0,
  /* 3082 */ 'r', 'o', 'r', 'q', 9, '$', '1', ',', 32, 0,
  /* 3092 */ 's', 'a', 'l', 'w', 9, '$', '1', ',', 32, 0,
  /* 3102 */ 'r', 'c', 'l', 'w', 9, '$', '1', ',', 32, 0,
  /* 3112 */ 's', 'h', 'l', 'w', 9, '$', '1', ',', 32, 0,
  /* 3122 */ 'r', 'o', 'l', 'w', 9, '$', '1', ',', 32, 0,
  /* 3132 */ 's', 'a', 'r', 'w', 9, '$', '1', ',', 32, 0,
  /* 3142 */ 'r', 'c', 'r', 'w', 9, '$', '1', ',', 32, 0,
  /* 3152 */ 's', 'h', 'r', 'w', 9, '$', '1', ',', 32, 0,
  /* 3162 */ 'r', 'o', 'r', 'w', 9, '$', '1', ',', 32, 0,
  /* 3172 */ 'm', 'o', 'v', 'a', 'b', 's', 'b', 9, '%', 'a', 'l', ',', 32, 0,
  /* 3186 */ 's', 't', 'o', 's', 'b', 9, '%', 'a', 'l', ',', 32, 0,
  /* 3198 */ 'o', 'u', 't', 'b', 9, '%', 'a', 'l', ',', 32, 0,
  /* 3209 */ 'm', 'o', 'v', 'b', 9, '%', 'a', 'l', ',', 32, 0,
  /* 3220 */ 's', 'a', 'l', 'b', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3231 */ 'r', 'c', 'l', 'b', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3242 */ 's', 'h', 'l', 'b', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3253 */ 'r', 'o', 'l', 'b', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3264 */ 's', 'a', 'r', 'b', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3275 */ 'r', 'c', 'r', 'b', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3286 */ 's', 'h', 'r', 'b', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3297 */ 'r', 'o', 'r', 'b', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3308 */ 's', 'h', 'l', 'd', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3320 */ 's', 'h', 'r', 'd', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3332 */ 's', 'a', 'l', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3343 */ 'r', 'c', 'l', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3354 */ 's', 'h', 'l', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3365 */ 'r', 'o', 'l', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3376 */ 's', 'a', 'r', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3387 */ 'r', 'c', 'r', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3398 */ 's', 'h', 'r', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3409 */ 'r', 'o', 'r', 'l', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3420 */ 's', 'h', 'l', 'd', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3432 */ 's', 'h', 'r', 'd', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3444 */ 's', 'a', 'l', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3455 */ 'r', 'c', 'l', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3466 */ 's', 'h', 'l', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3477 */ 'r', 'o', 'l', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3488 */ 's', 'a', 'r', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3499 */ 'r', 'c', 'r', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3510 */ 's', 'h', 'r', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3521 */ 'r', 'o', 'r', 'q', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3532 */ 's', 'h', 'l', 'd', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3544 */ 's', 'h', 'r', 'd', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3556 */ 's', 'a', 'l', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3567 */ 'r', 'c', 'l', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3578 */ 's', 'h', 'l', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3589 */ 'r', 'o', 'l', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3600 */ 's', 'a', 'r', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3611 */ 'r', 'c', 'r', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3622 */ 's', 'h', 'r', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3633 */ 'r', 'o', 'r', 'w', 9, '%', 'c', 'l', ',', 32, 0,
  /* 3644 */ 'm', 'o', 'v', 'a', 'b', 's', 'w', 9, '%', 'a', 'x', ',', 32, 0,
  /* 3658 */ 's', 't', 'o', 's', 'w', 9, '%', 'a', 'x', ',', 32, 0,
  /* 3670 */ 'o', 'u', 't', 'w', 9, '%', 'a', 'x', ',', 32, 0,
  /* 3681 */ 'm', 'o', 'v', 'w', 9, '%', 'a', 'x', ',', 32, 0,
  /* 3692 */ 'm', 'o', 'v', 'a', 'b', 's', 'l', 9, '%', 'e', 'a', 'x', ',', 32, 0,
  /* 3707 */ 's', 't', 'o', 's', 'l', 9, '%', 'e', 'a', 'x', ',', 32, 0,
  /* 3720 */ 'o', 'u', 't', 'l', 9, '%', 'e', 'a', 'x', ',', 32, 0,
  /* 3732 */ 'm', 'o', 'v', 'l', 9, '%', 'e', 'a', 'x', ',', 32, 0,
  /* 3744 */ 'm', 'o', 'v', 'a', 'b', 's', 'q', 9, '%', 'r', 'a', 'x', ',', 32, 0,
  /* 3759 */ 's', 't', 'o', 's', 'q', 9, '%', 'r', 'a', 'x', ',', 32, 0,
  /* 3772 */ 'm', 'o', 'v', 'q', 9, '%', 'r', 'a', 'x', ',', 32, 0,
  /* 3784 */ 'i', 'n', 's', 'b', 9, '%', 'd', 'x', ',', 32, 0,
  /* 3795 */ 'i', 'n', 's', 'l', 9, '%', 'd', 'x', ',', 32, 0,
  /* 3806 */ 'i', 'n', 's', 'w', 9, '%', 'd', 'x', ',', 32, 0,
  /* 3817 */ '#', 'V', 'A', 'A', 'R', 'G', '_', '6', '4', 32, 0,
  /* 3828 */ 'r', 'e', 't', 9, '#', 'e', 'h', '_', 'r', 'e', 't', 'u', 'r', 'n', ',', 32, 'a', 'd', 'd', 'r', ':', 32, 0,
  /* 3851 */ '#', 'S', 'E', 'H', '_', 'S', 'a', 'v', 'e', 'X', 'M', 'M', 32, 0,
  /* 3865 */ '#', 'V', 'A', 'S', 'T', 'A', 'R', 'T', '_', 'S', 'A', 'V', 'E', '_', 'X', 'M', 'M', '_', 'R', 'E', 'G', 'S', 32, 0,
  /* 3889 */ '#', 'S', 'E', 'H', '_', 'S', 't', 'a', 'c', 'k', 'A', 'l', 'l', 'o', 'c', 32, 0,
  /* 3906 */ '#', 'S', 'E', 'H', '_', 'P', 'u', 's', 'h', 'F', 'r', 'a', 'm', 'e', 32, 0,
  /* 3922 */ '#', 'S', 'E', 'H', '_', 'S', 'e', 't', 'F', 'r', 'a', 'm', 'e', 32, 0,
  /* 3937 */ '#', 'S', 'E', 'H', '_', 'S', 'a', 'v', 'e', 'R', 'e', 'g', 32, 0,
  /* 3951 */ '#', 'S', 'E', 'H', '_', 'P', 'u', 's', 'h', 'R', 'e', 'g', 32, 0,
  /* 3965 */ '#', 'R', 'E', 'L', 'E', 'A', 'S', 'E', '_', 'M', 'O', 'V', 32, 'P', 'S', 'E', 'U', 'D', 'O', 32, '!', 0,
  /* 3987 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'R', 'F', 'P', '8', '0', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4008 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'V', '4', 'F', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4029 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'V', '1', '6', 'F', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4051 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'V', '8', 'F', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4072 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'R', 'F', 'P', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4093 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'F', 'R', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4113 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'G', 'R', '3', '2', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4133 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'V', '2', 'F', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4154 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'V', '4', 'F', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4175 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'V', '8', 'F', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4196 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'V', '2', 'I', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4217 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'V', '4', 'I', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4238 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'V', '8', 'I', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4259 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'R', 'F', 'P', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4280 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'F', 'R', '6', '4', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4300 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'G', 'R', '1', '6', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4320 */ '#', 'C', 'M', 'O', 'V', '_', '_', 'G', 'R', '8', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4339 */ '#', 'R', 'E', 'L', 'E', 'A', 'S', 'E', '_', 'B', 'I', 'N', 'O', 'P', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4362 */ '#', 'R', 'E', 'L', 'E', 'A', 'S', 'E', '_', 'U', 'N', 'O', 'P', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4384 */ '#', 'A', 'C', 'Q', 'U', 'I', 'R', 'E', '_', 'M', 'O', 'V', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4405 */ '#', 'R', 'E', 'L', 'E', 'A', 'S', 'E', '_', 'M', 'O', 'V', 32, 'P', 'S', 'E', 'U', 'D', 'O', '!', 0,
  /* 4426 */ 'l', 'c', 'a', 'l', 'l', 'l', 9, '*', 0,
  /* 4435 */ 'l', 'j', 'm', 'p', 'l', 9, '*', 0,
  /* 4443 */ 'l', 'c', 'a', 'l', 'l', 'q', 9, '*', 0,
  /* 4452 */ 'r', 'e', 'x', '6', '4', 32, 'j', 'm', 'p', 'q', 9, '*', 0,
  /* 4465 */ 'l', 'j', 'm', 'p', 'q', 9, '*', 0,
  /* 4473 */ 'l', 'c', 'a', 'l', 'l', 'w', 9, '*', 0,
  /* 4482 */ 'l', 'j', 'm', 'p', 'w', 9, '*', 0,
  /* 4490 */ 'x', 's', 'h', 'a', '1', 0,
  /* 4496 */ 'i', 'n', 't', '1', 0,
  /* 4501 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'L', 'O', 'N', 'G', 'J', 'M', 'P', '3', '2', 0,
  /* 4520 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'S', 'E', 'T', 'J', 'M', 'P', '3', '2', 0,
  /* 4538 */ '#', 32, 'T', 'L', 'S', 'C', 'a', 'l', 'l', '_', '3', '2', 0,
  /* 4551 */ '#', 32, 'T', 'L', 'S', '_', 'a', 'd', 'd', 'r', '3', '2', 0,
  /* 4564 */ '#', 32, 'T', 'L', 'S', '_', 'b', 'a', 's', 'e', '_', 'a', 'd', 'd', 'r', '3', '2', 0,
  /* 4582 */ 'u', 'd', '2', 0,
  /* 4586 */ 'i', 'n', 't', '3', 0,
  /* 4591 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'L', 'O', 'N', 'G', 'J', 'M', 'P', '6', '4', 0,
  /* 4610 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'S', 'E', 'T', 'J', 'M', 'P', '6', '4', 0,
  /* 4628 */ '#', 32, 'T', 'L', 'S', 'C', 'a', 'l', 'l', '_', '6', '4', 0,
  /* 4641 */ '#', 32, 'T', 'L', 'S', '_', 'a', 'd', 'd', 'r', '6', '4', 0,
  /* 4654 */ '#', 32, 'T', 'L', 'S', '_', 'b', 'a', 's', 'e', '_', 'a', 'd', 'd', 'r', '6', '4', 0,
  /* 4672 */ 'r', 'e', 'x', '6', '4', 0,
  /* 4678 */ 'd', 'a', 't', 'a', '1', '6', 0,
  /* 4685 */ 'x', 's', 'h', 'a', '2', '5', '6', 0,
  /* 4693 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'E', 'N', 'D', 0,
  /* 4706 */ 'B', 'U', 'N', 'D', 'L', 'E', 0,
  /* 4713 */ 'D', 'B', 'G', '_', 'V', 'A', 'L', 'U', 'E', 0,
  /* 4723 */ '#', 'A', 'D', 'J', 'C', 'A', 'L', 'L', 'S', 'T', 'A', 'C', 'K', 'D', 'O', 'W', 'N', 0,
  /* 4741 */ '#', 'A', 'D', 'J', 'C', 'A', 'L', 'L', 'S', 'T', 'A', 'C', 'K', 'U', 'P', 0,
  /* 4757 */ '#', 'M', 'E', 'M', 'B', 'A', 'R', 'R', 'I', 'E', 'R', 0,
  /* 4769 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'S', 'T', 'A', 'R', 'T', 0,
  /* 4784 */ 'a', 'a', 'a', 0,
  /* 4788 */ 'd', 'a', 'a', 0,
  /* 4792 */ 'u', 'd', '2', 'b', 0,
  /* 4797 */ 'x', 'c', 'r', 'y', 'p', 't', 'e', 'c', 'b', 0,
  /* 4807 */ 'x', 'c', 'r', 'y', 'p', 't', 'c', 'f', 'b', 0,
  /* 4817 */ 'x', 'c', 'r', 'y', 'p', 't', 'o', 'f', 'b', 0,
  /* 4827 */ 'r', 'e', 'p', ';', 's', 't', 'o', 's', 'b', 0,
  /* 4837 */ 'r', 'e', 'p', ';', 'm', 'o', 'v', 's', 'b', 0,
  /* 4847 */ 'x', 'l', 'a', 't', 'b', 0,
  /* 4853 */ 'c', 'l', 'a', 'c', 0,
  /* 4858 */ 's', 't', 'a', 'c', 0,
  /* 4863 */ 'x', 'c', 'r', 'y', 'p', 't', 'c', 'b', 'c', 0,
  /* 4873 */ 'g', 'e', 't', 's', 'e', 'c', 0,
  /* 4880 */ 's', 'a', 'l', 'c', 0,
  /* 4885 */ 'c', 'l', 'c', 0,
  /* 4889 */ 'c', 'm', 'c', 0,
  /* 4893 */ 'r', 'd', 'p', 'm', 'c', 0,
  /* 4899 */ 'v', 'm', 'f', 'u', 'n', 'c', 0,
  /* 4906 */ 'r', 'd', 't', 's', 'c', 0,
  /* 4912 */ 's', 't', 'c', 0,
  /* 4916 */ 'c', 'p', 'u', 'i', 'd', 0,
  /* 4922 */ 'c', 'l', 'd', 0,
  /* 4926 */ 'c', 'l', 't', 'd', 0,
  /* 4931 */ 's', 't', 'd', 0,
  /* 4935 */ 'c', 'w', 't', 'd', 0,
  /* 4940 */ 'w', 'b', 'i', 'n', 'v', 'd', 0,
  /* 4947 */ 'v', 'm', 'r', 'e', 's', 'u', 'm', 'e', 0,
  /* 4956 */ 'r', 'e', 'p', 'n', 'e', 0,
  /* 4962 */ 'x', 's', 't', 'o', 'r', 'e', 0,
  /* 4969 */ '#', 'S', 'E', 'H', '_', 'E', 'p', 'i', 'l', 'o', 'g', 'u', 'e', 0,
  /* 4983 */ '#', 'S', 'E', 'H', '_', 'E', 'n', 'd', 'P', 'r', 'o', 'l', 'o', 'g', 'u', 'e', 0,
  /* 5000 */ 'l', 'e', 'a', 'v', 'e', 0,
  /* 5006 */ 'v', 'm', 'x', 'o', 'f', 'f', 0,
  /* 5013 */ 'l', 'a', 'h', 'f', 0,
  /* 5018 */ 's', 'a', 'h', 'f', 0,
  /* 5023 */ 'v', 'm', 'l', 'a', 'u', 'n', 'c', 'h', 0,
  /* 5032 */ 'c', 'l', 'g', 'i', 0,
  /* 5037 */ 's', 't', 'g', 'i', 0,
  /* 5042 */ 'c', 'l', 'i', 0,
  /* 5046 */ 's', 't', 'i', 0,
  /* 5050 */ '#', 32, 'w', 'i', 'n', '3', '2', 32, 'f', 'p', 't', 'o', 'u', 'i', 0,
  /* 5065 */ 'l', 'o', 'c', 'k', 0,
  /* 5070 */ 'i', 'n', 'b', 9, '%', 'd', 'x', ',', 32, '%', 'a', 'l', 0,
  /* 5083 */ 'p', 'u', 's', 'h', 'a', 'l', 0,
  /* 5090 */ 'p', 'o', 'p', 'a', 'l', 0,
  /* 5096 */ 'p', 'u', 's', 'h', 'f', 'l', 0,
  /* 5103 */ 'p', 'o', 'p', 'f', 'l', 0,
  /* 5109 */ 'v', 'm', 'm', 'c', 'a', 'l', 'l', 0,
  /* 5117 */ 'v', 'm', 'c', 'a', 'l', 'l', 0,
  /* 5124 */ 's', 'y', 's', 'c', 'a', 'l', 'l', 0,
  /* 5132 */ 'r', 'e', 'p', ';', 's', 't', 'o', 's', 'l', 0,
  /* 5142 */ 'r', 'e', 'p', ';', 'm', 'o', 'v', 's', 'l', 0,
  /* 5152 */ 'i', 'r', 'e', 't', 'l', 0,
  /* 5158 */ 'l', 'r', 'e', 't', 'l', 0,
  /* 5164 */ 's', 'y', 's', 'r', 'e', 't', 'l', 0,
  /* 5172 */ 's', 'y', 's', 'e', 'x', 'i', 't', 'l', 0,
  /* 5181 */ 'c', 'w', 't', 'l', 0,
  /* 5186 */ 'm', 'o', 'n', 't', 'm', 'u', 'l', 0,
  /* 5194 */ 'f', 's', 'e', 't', 'p', 'm', 0,
  /* 5201 */ 'r', 's', 'm', 0,
  /* 5205 */ '#', 32, 'd', 'y', 'n', 'a', 'm', 'i', 'c', 32, 's', 't', 'a', 'c', 'k', 32, 'a', 'l', 'l', 'o', 'c', 'a', 't', 'i', 'o', 'n', 0,
  /* 5232 */ 'i', 'n', 't', 'o', 0,
  /* 5237 */ 'c', 'q', 't', 'o', 0,
  /* 5242 */ 'r', 'd', 't', 's', 'c', 'p', 0,
  /* 5249 */ 'r', 'e', 'p', 0,
  /* 5253 */ 'n', 'o', 'p', 0,
  /* 5257 */ 'p', 'u', 's', 'h', 'f', 'q', 0,
  /* 5264 */ 'p', 'o', 'p', 'f', 'q', 0,
  /* 5270 */ 'r', 'e', 'p', ';', 's', 't', 'o', 's', 'q', 0,
  /* 5280 */ 'r', 'e', 'p', ';', 'm', 'o', 'v', 's', 'q', 0,
  /* 5290 */ 'i', 'r', 'e', 't', 'q', 0,
  /* 5296 */ 'l', 'r', 'e', 't', 'q', 0,
  /* 5302 */ 's', 'y', 's', 'r', 'e', 't', 'q', 0,
  /* 5310 */ 's', 'y', 's', 'e', 'x', 'i', 't', 'q', 0,
  /* 5319 */ 'c', 'l', 't', 'q', 0,
  /* 5324 */ 's', 'y', 's', 'e', 'n', 't', 'e', 'r', 0,
  /* 5333 */ 'r', 'd', 'm', 's', 'r', 0,
  /* 5339 */ 'w', 'r', 'm', 's', 'r', 0,
  /* 5345 */ 'x', 'c', 'r', 'y', 'p', 't', 'c', 't', 'r', 0,
  /* 5355 */ 'a', 'a', 's', 0,
  /* 5359 */ 'd', 'a', 's', 0,
  /* 5363 */ 'p', 'u', 's', 'h', 'l', 9, '%', 'c', 's', 0,
  /* 5373 */ 'p', 'u', 's', 'h', 'w', 9, '%', 'c', 's', 0,
  /* 5383 */ 'p', 'u', 's', 'h', 'l', 9, '%', 'd', 's', 0,
  /* 5393 */ 'p', 'o', 'p', 'l', 9, '%', 'd', 's', 0,
  /* 5402 */ 'p', 'u', 's', 'h', 'w', 9, '%', 'd', 's', 0,
  /* 5412 */ 'p', 'o', 'p', 'w', 9, '%', 'd', 's', 0,
  /* 5421 */ 'p', 'u', 's', 'h', 'l', 9, '%', 'e', 's', 0,
  /* 5431 */ 'p', 'o', 'p', 'l', 9, '%', 'e', 's', 0,
  /* 5440 */ 'p', 'u', 's', 'h', 'w', 9, '%', 'e', 's', 0,
  /* 5450 */ 'p', 'o', 'p', 'w', 9, '%', 'e', 's', 0,
  /* 5459 */ 'p', 'u', 's', 'h', 'l', 9, '%', 'f', 's', 0,
  /* 5469 */ 'p', 'o', 'p', 'l', 9, '%', 'f', 's', 0,
  /* 5478 */ 'p', 'u', 's', 'h', 'q', 9, '%', 'f', 's', 0,
  /* 5488 */ 'p', 'o', 'p', 'q', 9, '%', 'f', 's', 0,
  /* 5497 */ 'p', 'u', 's', 'h', 'w', 9, '%', 'f', 's', 0,
  /* 5507 */ 'p', 'o', 'p', 'w', 9, '%', 'f', 's', 0,
  /* 5516 */ 'p', 'u', 's', 'h', 'l', 9, '%', 'g', 's', 0,
  /* 5526 */ 'p', 'o', 'p', 'l', 9, '%', 'g', 's', 0,
  /* 5535 */ 'p', 'u', 's', 'h', 'q', 9, '%', 'g', 's', 0,
  /* 5545 */ 'p', 'o', 'p', 'q', 9, '%', 'g', 's', 0,
  /* 5554 */ 'p', 'u', 's', 'h', 'w', 9, '%', 'g', 's', 0,
  /* 5564 */ 'p', 'o', 'p', 'w', 9, '%', 'g', 's', 0,
  /* 5573 */ 's', 'w', 'a', 'p', 'g', 's', 0,
  /* 5580 */ '#', 32, 'v', 'a', 'r', 'i', 'a', 'b', 'l', 'e', 32, 's', 'i', 'z', 'e', 'd', 32, 'a', 'l', 'l', 'o', 'c', 'a', 32, 'f', 'o', 'r', 32, 's', 'e', 'g', 'm', 'e', 'n', 't', 'e', 'd', 32, 's', 't', 'a', 'c', 'k', 's', 0,
  /* 5625 */ 'p', 'u', 's', 'h', 'l', 9, '%', 's', 's', 0,
  /* 5635 */ 'p', 'o', 'p', 'l', 9, '%', 's', 's', 0,
  /* 5644 */ 'p', 'u', 's', 'h', 'w', 9, '%', 's', 's', 0,
  /* 5654 */ 'p', 'o', 'p', 'w', 9, '%', 's', 's', 0,
  /* 5663 */ 'c', 'l', 't', 's', 0,
  /* 5668 */ 'p', 'c', 'o', 'm', 'm', 'i', 't', 0,
  /* 5676 */ 'h', 'l', 't', 0,
  /* 5680 */ 'x', 'g', 'e', 't', 'b', 'v', 0,
  /* 5687 */ 'x', 's', 'e', 't', 'b', 'v', 0,
  /* 5694 */ 'p', 'u', 's', 'h', 'a', 'w', 0,
  /* 5701 */ 'p', 'o', 'p', 'a', 'w', 0,
  /* 5707 */ 'p', 'u', 's', 'h', 'f', 'w', 0,
  /* 5714 */ 'p', 'o', 'p', 'f', 'w', 0,
  /* 5720 */ 'r', 'e', 'p', ';', 's', 't', 'o', 's', 'w', 0,
  /* 5730 */ 'r', 'e', 'p', ';', 'm', 'o', 'v', 's', 'w', 0,
  /* 5740 */ 'c', 'b', 't', 'w', 0,
  /* 5745 */ 'i', 'r', 'e', 't', 'w', 0,
  /* 5751 */ 'l', 'r', 'e', 't', 'w', 0,
  /* 5757 */ 'i', 'n', 'w', 9, '%', 'd', 'x', ',', 32, '%', 'a', 'x', 0,
  /* 5770 */ 'v', 'm', 'l', 'o', 'a', 'd', 9, '%', 'e', 'a', 'x', 0,
  /* 5782 */ 'v', 'm', 's', 'a', 'v', 'e', 9, '%', 'e', 'a', 'x', 0,
  /* 5794 */ 'v', 'm', 'r', 'u', 'n', 9, '%', 'e', 'a', 'x', 0,
  /* 5805 */ 's', 'k', 'i', 'n', 'i', 't', 9, '%', 'e', 'a', 'x', 0,
  /* 5817 */ 'i', 'n', 'v', 'l', 'p', 'g', 'a', 9, '%', 'e', 'c', 'x', ',', 32, '%', 'e', 'a', 'x', 0,
  /* 5836 */ 'i', 'n', 'l', 9, '%', 'd', 'x', ',', 32, '%', 'e', 'a', 'x', 0,
  /* 5850 */ 'v', 'm', 'l', 'o', 'a', 'd', 9, '%', 'r', 'a', 'x', 0,
  /* 5862 */ 'v', 'm', 's', 'a', 'v', 'e', 9, '%', 'r', 'a', 'x', 0,
  /* 5874 */ 'v', 'm', 'r', 'u', 'n', 9, '%', 'r', 'a', 'x', 0,
  /* 5885 */ 'i', 'n', 'v', 'l', 'p', 'g', 'a', 9, '%', 'e', 'c', 'x', ',', 32, '%', 'r', 'a', 'x', 0,
  /* 5904 */ 'o', 'u', 't', 'b', 9, '%', 'a', 'l', ',', 32, '%', 'd', 'x', 0,
  /* 5918 */ 'o', 'u', 't', 'w', 9, '%', 'a', 'x', ',', 32, '%', 'd', 'x', 0,
  /* 5932 */ 'o', 'u', 't', 'l', 9, '%', 'e', 'a', 'x', ',', 32, '%', 'd', 'x', 0,
  };
#endif

  // Emit the opcode for the instruction.
  uint32_t Bits = OpInfo[MCInst_getOpcode(MI)];
  // assert(Bits != 0 && "Cannot print this instruction.");
#ifndef CAPSTONE_DIET
  SStream_concat0(O, AsmStrs+(Bits & 8191)-1);
#endif


  // Fragment 0 encoded into 6 bits for 41 unique commands.
  //printf("Frag-0: %"PRIu64"\n", (Bits >> 13) & 63);
  switch ((Bits >> 13) & 63) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // DBG_VALUE, BUNDLE, LIFETIME_START, LIFETIME_END, AAA, AAS, ACQUIRE_MOV...
    return;
    break;
  case 1:
    // AAD8i8, AAM8i8, ADC16i16, ADC32i32, ADC64i32, ADC8i8, ADD16i16, ADD32i...
    printOperand(MI, 0, O); 
    break;
  case 2:
    // ADC16mi, ADC16mi8, ADC16mr, ADC32mi, ADC32mi8, ADC32mr, ADC64mi32, ADC...
    printOperand(MI, 5, O); 
    SStream_concat0(O, ", "); 
    break;
  case 3:
    // ADC16ri, ADC16ri8, ADC16rr, ADC16rr_REV, ADC32ri, ADC32ri8, ADC32rr, A...
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 4:
    // ADC16rm, ADD16rm, AND16rm, CMOVA16rm, CMOVAE16rm, CMOVB16rm, CMOVBE16r...
    printi16mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 5:
    // ADC32rm, ADCX32rm, ADD32rm, AND32rm, ANDN32rm, CMOVA32rm, CMOVAE32rm, ...
    printi32mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 6:
    // ADC64rm, ADCX64rm, ADD64rm, AND64rm, ANDN64rm, CMOVA64rm, CMOVAE64rm, ...
    printi64mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 7:
    // ADC8rm, ADD8rm, AND8rm, OR8rm, SBB8rm, SUB8rm, XOR8rm
    printi8mem(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 8:
    // ADOX32rm, BLCFILL32rm, BLCI32rm, BLCIC32rm, BLCMSK32rm, BLCS32rm, BLSF...
    printi32mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 9:
    // ADOX32rr, ADOX64rr, ARPL16rr, BLCFILL32rr, BLCFILL64rr, BLCI32rr, BLCI...
    printOperand(MI, 1, O); 
    break;
  case 10:
    // ADOX64rm, BLCFILL64rm, BLCI64rm, BLCIC64rm, BLCMSK64rm, BLCS64rm, BLSF...
    printi64mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 11:
    // BEXTR32rm, BEXTR64rm, BEXTRI32mi, BEXTRI64mi, BZHI32rm, BZHI64rm, IMUL...
    printOperand(MI, 6, O); 
    SStream_concat0(O, ", "); 
    break;
  case 12:
    // BSF16rm, BSR16rm, CMP16rm, LAR16rm, LAR32rm, LAR64rm, LSL16rm, LZCNT16...
    printi16mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 13:
    // CALL16m, DEC16m, DIV16m, IDIV16m, IMUL16m, INC16m, JMP16m, LLDT16m, LM...
    printi16mem(MI, 0, O); 
    return;
    break;
  case 14:
    // CALL32m, DEC32m, DIV32m, IDIV32m, IMUL32m, INC32m, JMP32m, LOCK_DEC32m...
    printi32mem(MI, 0, O); 
    return;
    break;
  case 15:
    // CALL64m, CMPXCHG8B, DEC64m, DIV64m, IDIV64m, IMUL64m, INC64m, JMP64m, ...
    printi64mem(MI, 0, O); 
    return;
    break;
  case 16:
    // CALL64pcrel32, CALLpcrel16, CALLpcrel32, EH_SjLj_Setup, JAE_1, JAE_2, ...
    printPCRelImm(MI, 0, O); 
    return;
    break;
  case 17:
    // CLFLUSHOPT, CLWB, DEC8m, DIV8m, IDIV8m, IMUL8m, INC8m, INVLPG, LOCK_DE...
    printi8mem(MI, 0, O); 
    return;
    break;
  case 18:
    // CMP8rm, MOV8rm, MOV8rm_NOREX, MOVSX16rm8, MOVSX32_NOREXrm8, MOVSX32rm8...
    printi8mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 19:
    // CMPSB, INSB, SCASB, STOSB
    printDstIdx8(MI, 0, O); 
    break;
  case 20:
    // CMPSL, INSL, SCASL, STOSL
    printDstIdx32(MI, 0, O); 
    break;
  case 21:
    // CMPSQ, SCASQ, STOSQ
    printDstIdx64(MI, 0, O); 
    break;
  case 22:
    // CMPSW, INSW, SCASW, STOSW
    printDstIdx16(MI, 0, O); 
    break;
  case 23:
    // CMPXCHG16B, LCMPXCHG16B
    printi128mem(MI, 0, O); 
    return;
    break;
  case 24:
    // FARCALL16m, FARCALL32m, FARCALL64, FARJMP16m, FARJMP32m, FARJMP64, LGD...
    printopaquemem(MI, 0, O); 
    return;
    break;
  case 25:
    // INVEPT32, INVEPT64, INVPCID32, INVPCID64, INVVPID32, INVVPID64
    printi128mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 26:
    // LDS16rm, LDS32rm, LES16rm, LES32rm, LFS16rm, LFS32rm, LFS64rm, LGS16rm...
    printopaquemem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 27:
    // LEA16r, LEA32r, LEA64_32r, LEA64r
    printanymem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 28:
    // LODSB, OUTSB
    printSrcIdx8(MI, 0, O); 
    break;
  case 29:
    // LODSL, OUTSL
    printSrcIdx32(MI, 0, O); 
    break;
  case 30:
    // LODSQ
    printSrcIdx64(MI, 0, O); 
    SStream_concat0(O, ", %rax"); 
    op_addReg(MI, X86_REG_RAX);
    return;
    break;
  case 31:
    // LODSW, OUTSW
    printSrcIdx16(MI, 0, O); 
    break;
  case 32:
    // MOV16ao16, MOV16ao32, MOV16ao64, MOV16o16a, MOV16o32a, MOV16o64a
    printMemOffs16(MI, 0, O); 
    break;
  case 33:
    // MOV32ao16, MOV32ao32, MOV32ao64, MOV32o16a, MOV32o32a, MOV32o64a
    printMemOffs32(MI, 0, O); 
    break;
  case 34:
    // MOV64ao32, MOV64ao64, MOV64o32a, MOV64o64a
    printMemOffs64(MI, 0, O); 
    break;
  case 35:
    // MOV8ao16, MOV8ao32, MOV8ao64, MOV8o16a, MOV8o32a, MOV8o64a
    printMemOffs8(MI, 0, O); 
    break;
  case 36:
    // MOVSB
    printSrcIdx8(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printDstIdx8(MI, 0, O); 
    return;
    break;
  case 37:
    // MOVSL
    printSrcIdx32(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printDstIdx32(MI, 0, O); 
    return;
    break;
  case 38:
    // MOVSQ
    printSrcIdx64(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printDstIdx64(MI, 0, O); 
    return;
    break;
  case 39:
    // MOVSW
    printSrcIdx16(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printDstIdx16(MI, 0, O); 
    return;
    break;
  case 40:
    // SHLD16rri8, SHLD32rri8, SHLD64rri8, SHRD16rri8, SHRD32rri8, SHRD64rri8
    printOperand(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  }


  // Fragment 1 encoded into 5 bits for 18 unique commands.
  //printf("Frag-1: %"PRIu64"\n", (Bits >> 19) & 31);
  switch ((Bits >> 19) & 31) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // AAD8i8, AAM8i8, BSWAP32r, BSWAP64r, CALL16r, CALL32r, CALL64r, DEC16r,...
    return;
    break;
  case 1:
    // ADC16i16, ADD16i16, AND16i16, CMP16i16, IN16ri, LODSW, MOV16ao16, MOV1...
    SStream_concat0(O, ", %ax"); 
    op_addReg(MI, X86_REG_AX);
    return;
    break;
  case 2:
    // ADC16mi, ADC16mi8, ADC16mr, ADD16mi, ADD16mi8, ADD16mr, AND16mi, AND16...
    printi16mem(MI, 0, O); 
    return;
    break;
  case 3:
    // ADC16ri, ADC16ri8, ADC16rm, ADC16rr, ADC32ri, ADC32ri8, ADC32rm, ADC32...
    printOperand(MI, 1, O); 
    break;
  case 4:
    // ADC16rr_REV, ADC32rr_REV, ADC64rr_REV, ADC8rr_REV, ADCX32rm, ADCX32rr,...
    printOperand(MI, 0, O); 
    return;
    break;
  case 5:
    // ADC32i32, ADD32i32, AND32i32, CMP32i32, IN32ri, LODSL, MOV32ao16, MOV3...
    SStream_concat0(O, ", %eax"); 
    op_addReg(MI, X86_REG_EAX);
    return;
    break;
  case 6:
    // ADC32mi, ADC32mi8, ADC32mr, ADD32mi, ADD32mi8, ADD32mr, AND32mi, AND32...
    printi32mem(MI, 0, O); 
    return;
    break;
  case 7:
    // ADC64i32, ADD64i32, AND64i32, CMP64i32, MOV64ao32, MOV64ao64, OR64i32,...
    SStream_concat0(O, ", %rax"); 
    op_addReg(MI, X86_REG_RAX);
    return;
    break;
  case 8:
    // ADC64mi32, ADC64mi8, ADC64mr, ADD64mi32, ADD64mi8, ADD64mr, AND64mi32,...
    printi64mem(MI, 0, O); 
    return;
    break;
  case 9:
    // ADC8i8, ADD8i8, AND8i8, CMP8i8, IN8ri, LODSB, MOV8ao16, MOV8ao32, MOV8...
    SStream_concat0(O, ", %al"); 
    op_addReg(MI, X86_REG_AL);
    return;
    break;
  case 10:
    // ADC8mi, ADC8mi8, ADC8mr, ADD8mi, ADD8mi8, ADD8mr, AND8mi, AND8mi8, AND...
    printi8mem(MI, 0, O); 
    return;
    break;
  case 11:
    // ADOX32rr, ADOX64rr, ARPL16rr, BLCFILL32rr, BLCFILL64rr, BLCI32rr, BLCI...
    SStream_concat0(O, ", "); 
    break;
  case 12:
    // BEXTR32rm, BEXTRI32mi, BZHI32rm, IMUL32rmi, IMUL32rmi8, RORX32mi, SARX...
    printi32mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 13:
    // BEXTR64rm, BEXTRI64mi, BZHI64rm, IMUL64rmi32, IMUL64rmi8, RORX64mi, SA...
    printi64mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 14:
    // FARCALL16i, FARCALL32i, FARJMP16i, FARJMP32i
    SStream_concat0(O, ":"); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 15:
    // IMUL16rmi, IMUL16rmi8
    printi16mem(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 16:
    // OUTSB, OUTSL, OUTSW
    SStream_concat0(O, ", %dx"); 
    op_addReg(MI, X86_REG_DX);
    return;
    break;
  case 17:
    // SHLD16mri8, SHLD32mri8, SHLD64mri8, SHRD16mri8, SHRD32mri8, SHRD64mri8
    printOperand(MI, 5, O); 
    SStream_concat0(O, ", "); 
    break;
  }


  // Fragment 2 encoded into 5 bits for 20 unique commands.
  //printf("Frag-2: %"PRIu64"\n", (Bits >> 24) & 31);
  switch ((Bits >> 24) & 31) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // ADC16ri, ADC16ri8, ADC16rm, ADC16rr, ADC32ri, ADC32ri8, ADC32rm, ADC32...
    return;
    break;
  case 1:
    // ADOX32rr, ADOX64rr, ARPL16rr, BLCFILL32rr, BLCFILL64rr, BLCI32rr, BLCI...
    printOperand(MI, 0, O); 
    return;
    break;
  case 2:
    // ANDN32rm, ANDN32rr, ANDN64rm, ANDN64rr, BEXTR32rr, BEXTR64rr, BEXTRI32...
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 3:
    // CMPSB
    printSrcIdx8(MI, 1, O); 
    return;
    break;
  case 4:
    // CMPSL
    printSrcIdx32(MI, 1, O); 
    return;
    break;
  case 5:
    // CMPSQ
    printSrcIdx64(MI, 1, O); 
    return;
    break;
  case 6:
    // CMPSW
    printSrcIdx16(MI, 1, O); 
    return;
    break;
  case 7:
    // ENTER, NOOP19rr, SEH_SaveReg, SEH_SaveXMM, SEH_SetFrame, VASTART_SAVE_...
    printOperand(MI, 1, O); 
    break;
  case 8:
    // LXADD16, XCHG16rm
    printi16mem(MI, 2, O); 
    return;
    break;
  case 9:
    // LXADD32, XCHG32rm
    printi32mem(MI, 2, O); 
    return;
    break;
  case 10:
    // LXADD64, XCHG64rm
    printi64mem(MI, 2, O); 
    return;
    break;
  case 11:
    // LXADD8, XCHG8rm
    printi8mem(MI, 2, O); 
    return;
    break;
  case 12:
    // SHLD16mri8, SHRD16mri8
    printi16mem(MI, 0, O); 
    return;
    break;
  case 13:
    // SHLD32mri8, SHRD32mri8
    printi32mem(MI, 0, O); 
    return;
    break;
  case 14:
    // SHLD64mri8, SHRD64mri8
    printi64mem(MI, 0, O); 
    return;
    break;
  case 15:
    // TEST16rm
    printi16mem(MI, 1, O); 
    return;
    break;
  case 16:
    // TEST32rm
    printi32mem(MI, 1, O); 
    return;
    break;
  case 17:
    // TEST64rm
    printi64mem(MI, 1, O); 
    return;
    break;
  case 18:
    // TEST8rm, VAARG_64
    printi8mem(MI, 1, O); 
    break;
  case 19:
    // XCHG16rr, XCHG32rr, XCHG64rr, XCHG8rr
    printOperand(MI, 2, O); 
    return;
    break;
  }


  // Fragment 3 encoded into 1 bits for 2 unique commands.
  //printf("Frag-3: %"PRIu64"\n", (Bits >> 29) & 1);
  if ((Bits >> 29) & 1) {
    // VAARG_64, VASTART_SAVE_XMM_REGS
    SStream_concat0(O, ", "); 
  } else {
    // ENTER, NOOP19rr, SEH_SaveReg, SEH_SaveXMM, SEH_SetFrame, TEST8rm
    return;
  }


  // Fragment 4 encoded into 1 bits for 2 unique commands.
  //printf("Frag-4: %"PRIu64"\n", (Bits >> 30) & 1);
  if ((Bits >> 30) & 1) {
    // VASTART_SAVE_XMM_REGS
    printOperand(MI, 2, O); 
    return;
  } else {
    // VAARG_64
    printOperand(MI, 6, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 7, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 8, O); 
    return;
  }
}


/// getRegisterName - This method is automatically generated by tblgen
/// from the register set description.  This returns the assembler name
/// for the specified register.
static const char *getRegisterName(unsigned RegNo)
{
  // assert(RegNo && RegNo < 242 && "Invalid register number!");

#ifndef CAPSTONE_DIET
  static const char AsmStrs[] = {
  /* 0 */ 's', 't', '(', '0', ')', 0,
  /* 6 */ 's', 't', '(', '1', ')', 0,
  /* 12 */ 's', 't', '(', '2', ')', 0,
  /* 18 */ 's', 't', '(', '3', ')', 0,
  /* 24 */ 's', 't', '(', '4', ')', 0,
  /* 30 */ 's', 't', '(', '5', ')', 0,
  /* 36 */ 's', 't', '(', '6', ')', 0,
  /* 42 */ 's', 't', '(', '7', ')', 0,
  /* 48 */ 'x', 'm', 'm', '1', '0', 0,
  /* 54 */ 'y', 'm', 'm', '1', '0', 0,
  /* 60 */ 'z', 'm', 'm', '1', '0', 0,
  /* 66 */ 'c', 'r', '1', '0', 0,
  /* 71 */ 'd', 'r', '1', '0', 0,
  /* 76 */ 'x', 'm', 'm', '2', '0', 0,
  /* 82 */ 'y', 'm', 'm', '2', '0', 0,
  /* 88 */ 'z', 'm', 'm', '2', '0', 0,
  /* 94 */ 'x', 'm', 'm', '3', '0', 0,
  /* 100 */ 'y', 'm', 'm', '3', '0', 0,
  /* 106 */ 'z', 'm', 'm', '3', '0', 0,
  /* 112 */ 'k', '0', 0,
  /* 115 */ 'x', 'm', 'm', '0', 0,
  /* 120 */ 'y', 'm', 'm', '0', 0,
  /* 125 */ 'z', 'm', 'm', '0', 0,
  /* 130 */ 'f', 'p', '0', 0,
  /* 134 */ 'c', 'r', '0', 0,
  /* 138 */ 'd', 'r', '0', 0,
  /* 142 */ 'x', 'm', 'm', '1', '1', 0,
  /* 148 */ 'y', 'm', 'm', '1', '1', 0,
  /* 154 */ 'z', 'm', 'm', '1', '1', 0,
  /* 160 */ 'c', 'r', '1', '1', 0,
  /* 165 */ 'd', 'r', '1', '1', 0,
  /* 170 */ 'x', 'm', 'm', '2', '1', 0,
  /* 176 */ 'y', 'm', 'm', '2', '1', 0,
  /* 182 */ 'z', 'm', 'm', '2', '1', 0,
  /* 188 */ 'x', 'm', 'm', '3', '1', 0,
  /* 194 */ 'y', 'm', 'm', '3', '1', 0,
  /* 200 */ 'z', 'm', 'm', '3', '1', 0,
  /* 206 */ 'k', '1', 0,
  /* 209 */ 'x', 'm', 'm', '1', 0,
  /* 214 */ 'y', 'm', 'm', '1', 0,
  /* 219 */ 'z', 'm', 'm', '1', 0,
  /* 224 */ 'f', 'p', '1', 0,
  /* 228 */ 'c', 'r', '1', 0,
  /* 232 */ 'd', 'r', '1', 0,
  /* 236 */ 'x', 'm', 'm', '1', '2', 0,
  /* 242 */ 'y', 'm', 'm', '1', '2', 0,
  /* 248 */ 'z', 'm', 'm', '1', '2', 0,
  /* 254 */ 'c', 'r', '1', '2', 0,
  /* 259 */ 'd', 'r', '1', '2', 0,
  /* 264 */ 'x', 'm', 'm', '2', '2', 0,
  /* 270 */ 'y', 'm', 'm', '2', '2', 0,
  /* 276 */ 'z', 'm', 'm', '2', '2', 0,
  /* 282 */ 'k', '2', 0,
  /* 285 */ 'x', 'm', 'm', '2', 0,
  /* 290 */ 'y', 'm', 'm', '2', 0,
  /* 295 */ 'z', 'm', 'm', '2', 0,
  /* 300 */ 'f', 'p', '2', 0,
  /* 304 */ 'c', 'r', '2', 0,
  /* 308 */ 'd', 'r', '2', 0,
  /* 312 */ 'x', 'm', 'm', '1', '3', 0,
  /* 318 */ 'y', 'm', 'm', '1', '3', 0,
  /* 324 */ 'z', 'm', 'm', '1', '3', 0,
  /* 330 */ 'c', 'r', '1', '3', 0,
  /* 335 */ 'd', 'r', '1', '3', 0,
  /* 340 */ 'x', 'm', 'm', '2', '3', 0,
  /* 346 */ 'y', 'm', 'm', '2', '3', 0,
  /* 352 */ 'z', 'm', 'm', '2', '3', 0,
  /* 358 */ 'k', '3', 0,
  /* 361 */ 'x', 'm', 'm', '3', 0,
  /* 366 */ 'y', 'm', 'm', '3', 0,
  /* 371 */ 'z', 'm', 'm', '3', 0,
  /* 376 */ 'f', 'p', '3', 0,
  /* 380 */ 'c', 'r', '3', 0,
  /* 384 */ 'd', 'r', '3', 0,
  /* 388 */ 'x', 'm', 'm', '1', '4', 0,
  /* 394 */ 'y', 'm', 'm', '1', '4', 0,
  /* 400 */ 'z', 'm', 'm', '1', '4', 0,
  /* 406 */ 'c', 'r', '1', '4', 0,
  /* 411 */ 'd', 'r', '1', '4', 0,
  /* 416 */ 'x', 'm', 'm', '2', '4', 0,
  /* 422 */ 'y', 'm', 'm', '2', '4', 0,
  /* 428 */ 'z', 'm', 'm', '2', '4', 0,
  /* 434 */ 'k', '4', 0,
  /* 437 */ 'x', 'm', 'm', '4', 0,
  /* 442 */ 'y', 'm', 'm', '4', 0,
  /* 447 */ 'z', 'm', 'm', '4', 0,
  /* 452 */ 'f', 'p', '4', 0,
  /* 456 */ 'c', 'r', '4', 0,
  /* 460 */ 'd', 'r', '4', 0,
  /* 464 */ 'x', 'm', 'm', '1', '5', 0,
  /* 470 */ 'y', 'm', 'm', '1', '5', 0,
  /* 476 */ 'z', 'm', 'm', '1', '5', 0,
  /* 482 */ 'c', 'r', '1', '5', 0,
  /* 487 */ 'd', 'r', '1', '5', 0,
  /* 492 */ 'x', 'm', 'm', '2', '5', 0,
  /* 498 */ 'y', 'm', 'm', '2', '5', 0,
  /* 504 */ 'z', 'm', 'm', '2', '5', 0,
  /* 510 */ 'k', '5', 0,
  /* 513 */ 'x', 'm', 'm', '5', 0,
  /* 518 */ 'y', 'm', 'm', '5', 0,
  /* 523 */ 'z', 'm', 'm', '5', 0,
  /* 528 */ 'f', 'p', '5', 0,
  /* 532 */ 'c', 'r', '5', 0,
  /* 536 */ 'd', 'r', '5', 0,
  /* 540 */ 'x', 'm', 'm', '1', '6', 0,
  /* 546 */ 'y', 'm', 'm', '1', '6', 0,
  /* 552 */ 'z', 'm', 'm', '1', '6', 0,
  /* 558 */ 'x', 'm', 'm', '2', '6', 0,
  /* 564 */ 'y', 'm', 'm', '2', '6', 0,
  /* 570 */ 'z', 'm', 'm', '2', '6', 0,
  /* 576 */ 'k', '6', 0,
  /* 579 */ 'x', 'm', 'm', '6', 0,
  /* 584 */ 'y', 'm', 'm', '6', 0,
  /* 589 */ 'z', 'm', 'm', '6', 0,
  /* 594 */ 'f', 'p', '6', 0,
  /* 598 */ 'c', 'r', '6', 0,
  /* 602 */ 'd', 'r', '6', 0,
  /* 606 */ 'x', 'm', 'm', '1', '7', 0,
  /* 612 */ 'y', 'm', 'm', '1', '7', 0,
  /* 618 */ 'z', 'm', 'm', '1', '7', 0,
  /* 624 */ 'x', 'm', 'm', '2', '7', 0,
  /* 630 */ 'y', 'm', 'm', '2', '7', 0,
  /* 636 */ 'z', 'm', 'm', '2', '7', 0,
  /* 642 */ 'k', '7', 0,
  /* 645 */ 'x', 'm', 'm', '7', 0,
  /* 650 */ 'y', 'm', 'm', '7', 0,
  /* 655 */ 'z', 'm', 'm', '7', 0,
  /* 660 */ 'f', 'p', '7', 0,
  /* 664 */ 'c', 'r', '7', 0,
  /* 668 */ 'd', 'r', '7', 0,
  /* 672 */ 'x', 'm', 'm', '1', '8', 0,
  /* 678 */ 'y', 'm', 'm', '1', '8', 0,
  /* 684 */ 'z', 'm', 'm', '1', '8', 0,
  /* 690 */ 'x', 'm', 'm', '2', '8', 0,
  /* 696 */ 'y', 'm', 'm', '2', '8', 0,
  /* 702 */ 'z', 'm', 'm', '2', '8', 0,
  /* 708 */ 'x', 'm', 'm', '8', 0,
  /* 713 */ 'y', 'm', 'm', '8', 0,
  /* 718 */ 'z', 'm', 'm', '8', 0,
  /* 723 */ 'c', 'r', '8', 0,
  /* 727 */ 'd', 'r', '8', 0,
  /* 731 */ 'x', 'm', 'm', '1', '9', 0,
  /* 737 */ 'y', 'm', 'm', '1', '9', 0,
  /* 743 */ 'z', 'm', 'm', '1', '9', 0,
  /* 749 */ 'x', 'm', 'm', '2', '9', 0,
  /* 755 */ 'y', 'm', 'm', '2', '9', 0,
  /* 761 */ 'z', 'm', 'm', '2', '9', 0,
  /* 767 */ 'x', 'm', 'm', '9', 0,
  /* 772 */ 'y', 'm', 'm', '9', 0,
  /* 777 */ 'z', 'm', 'm', '9', 0,
  /* 782 */ 'c', 'r', '9', 0,
  /* 786 */ 'd', 'r', '9', 0,
  /* 790 */ 'r', '1', '0', 'b', 0,
  /* 795 */ 'r', '1', '1', 'b', 0,
  /* 800 */ 'r', '1', '2', 'b', 0,
  /* 805 */ 'r', '1', '3', 'b', 0,
  /* 810 */ 'r', '1', '4', 'b', 0,
  /* 815 */ 'r', '1', '5', 'b', 0,
  /* 820 */ 'r', '8', 'b', 0,
  /* 824 */ 'r', '9', 'b', 0,
  /* 828 */ 'r', '1', '0', 'd', 0,
  /* 833 */ 'r', '1', '1', 'd', 0,
  /* 838 */ 'r', '1', '2', 'd', 0,
  /* 843 */ 'r', '1', '3', 'd', 0,
  /* 848 */ 'r', '1', '4', 'd', 0,
  /* 853 */ 'r', '1', '5', 'd', 0,
  /* 858 */ 'r', '8', 'd', 0,
  /* 862 */ 'r', '9', 'd', 0,
  /* 866 */ 'a', 'h', 0,
  /* 869 */ 'b', 'h', 0,
  /* 872 */ 'c', 'h', 0,
  /* 875 */ 'd', 'h', 0,
  /* 878 */ 'e', 'd', 'i', 0,
  /* 882 */ 'r', 'd', 'i', 0,
  /* 886 */ 'e', 's', 'i', 0,
  /* 890 */ 'r', 's', 'i', 0,
  /* 894 */ 'a', 'l', 0,
  /* 897 */ 'b', 'l', 0,
  /* 900 */ 'c', 'l', 0,
  /* 903 */ 'd', 'l', 0,
  /* 906 */ 'd', 'i', 'l', 0,
  /* 910 */ 's', 'i', 'l', 0,
  /* 914 */ 'b', 'p', 'l', 0,
  /* 918 */ 's', 'p', 'l', 0,
  /* 922 */ 'e', 'b', 'p', 0,
  /* 926 */ 'r', 'b', 'p', 0,
  /* 930 */ 'e', 'i', 'p', 0,
  /* 934 */ 'r', 'i', 'p', 0,
  /* 938 */ 'e', 's', 'p', 0,
  /* 942 */ 'r', 's', 'p', 0,
  /* 946 */ 'c', 's', 0,
  /* 949 */ 'd', 's', 0,
  /* 952 */ 'e', 's', 0,
  /* 955 */ 'f', 's', 0,
  /* 958 */ 'f', 'l', 'a', 'g', 's', 0,
  /* 964 */ 's', 's', 0,
  /* 967 */ 'r', '1', '0', 'w', 0,
  /* 972 */ 'r', '1', '1', 'w', 0,
  /* 977 */ 'r', '1', '2', 'w', 0,
  /* 982 */ 'r', '1', '3', 'w', 0,
  /* 987 */ 'r', '1', '4', 'w', 0,
  /* 992 */ 'r', '1', '5', 'w', 0,
  /* 997 */ 'r', '8', 'w', 0,
  /* 1001 */ 'r', '9', 'w', 0,
  /* 1005 */ 'f', 'p', 's', 'w', 0,
  /* 1010 */ 'e', 'a', 'x', 0,
  /* 1014 */ 'r', 'a', 'x', 0,
  /* 1018 */ 'e', 'b', 'x', 0,
  /* 1022 */ 'r', 'b', 'x', 0,
  /* 1026 */ 'e', 'c', 'x', 0,
  /* 1030 */ 'r', 'c', 'x', 0,
  /* 1034 */ 'e', 'd', 'x', 0,
  /* 1038 */ 'r', 'd', 'x', 0,
  /* 1042 */ 'e', 'i', 'z', 0,
  /* 1046 */ 'r', 'i', 'z', 0,
  };

  static const uint16_t RegAsmOffset[] = {
    866, 894, 1011, 869, 897, 923, 914, 1019, 872, 900, 946, 1027, 875, 879, 
    906, 903, 949, 1035, 1010, 922, 1018, 1026, 878, 1034, 958, 930, 1042, 952, 
    886, 938, 1005, 955, 961, 931, 1014, 926, 1022, 1030, 882, 1038, 934, 1046, 
    890, 942, 887, 910, 939, 918, 964, 134, 228, 304, 380, 456, 532, 598, 
    664, 723, 782, 66, 160, 254, 330, 406, 482, 138, 232, 308, 384, 460, 
    536, 602, 668, 727, 786, 71, 165, 259, 335, 411, 487, 130, 224, 300, 
    376, 452, 528, 594, 660, 112, 206, 282, 358, 434, 510, 576, 642, 116, 
    210, 286, 362, 438, 514, 580, 646, 724, 783, 67, 161, 255, 331, 407, 
    483, 0, 6, 12, 18, 24, 30, 36, 42, 115, 209, 285, 361, 437, 
    513, 579, 645, 708, 767, 48, 142, 236, 312, 388, 464, 540, 606, 672, 
    731, 76, 170, 264, 340, 416, 492, 558, 624, 690, 749, 94, 188, 120, 
    214, 290, 366, 442, 518, 584, 650, 713, 772, 54, 148, 242, 318, 394, 
    470, 546, 612, 678, 737, 82, 176, 270, 346, 422, 498, 564, 630, 696, 
    755, 100, 194, 125, 219, 295, 371, 447, 523, 589, 655, 718, 777, 60, 
    154, 248, 324, 400, 476, 552, 618, 684, 743, 88, 182, 276, 352, 428, 
    504, 570, 636, 702, 761, 106, 200, 820, 824, 790, 795, 800, 805, 810, 
    815, 858, 862, 828, 833, 838, 843, 848, 853, 997, 1001, 967, 972, 977, 
    982, 987, 992, 
  };

  //int i;
  //for (i = 0; i < sizeof(RegAsmOffset)/2; i++)
  //     printf("%s = %u\n", AsmStrs+RegAsmOffset[i], i + 1);
  //printf("*************************\n");
  return AsmStrs+RegAsmOffset[RegNo-1];
#else
  return NULL;
#endif
}

#ifdef PRINT_ALIAS_INSTR
#undef PRINT_ALIAS_INSTR

static void printCustomAliasOperand(MCInst *MI, unsigned OpIdx,
  unsigned PrintMethodIdx, SStream *OS)
{
}

static char *printAliasInstr(MCInst *MI, SStream *OS, void *info)
{
  #define GETREGCLASS_CONTAIN(_class, _reg) MCRegisterClass_contains(MCRegisterInfo_getRegClass(MRI, _class), MCOperand_getReg(MCInst_getOperand(MI, _reg)))
  const char *AsmString;
  char *tmp, *AsmMnem, *AsmOps, *c;
  int OpIdx, PrintMethodIdx;
  switch (MCInst_getOpcode(MI)) {
  default: return NULL;
  case X86_AAD8i8:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 10) {
      // (AAD8i8 10)
      AsmString = "aad";
      break;
    }
    return NULL;
  case X86_AAM8i8:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 10) {
      // (AAM8i8 10)
      AsmString = "aam";
      break;
    }
    return NULL;
  case X86_XSTORE:
    if (MCInst_getNumOperands(MI) == 0) {
      // (XSTORE)
      AsmString = "xstorerng";
      break;
    }
    return NULL;
  }

  tmp = cs_strdup(AsmString);
  AsmMnem = tmp;
  for(AsmOps = tmp; *AsmOps; AsmOps++) {
    if (*AsmOps == ' ' || *AsmOps == '\t') {
      *AsmOps = '\0';
      AsmOps++;
      break;
    }
  }
  SStream_concat0(OS, AsmMnem);
  if (*AsmOps) {
    SStream_concat0(OS, "\t");
    for (c = AsmOps; *c; c++) {
      if (*c == '$') {
        c += 1;
        if (*c == (char)0xff) {
          c += 1;
          OpIdx = *c - 1;
          c += 1;
          PrintMethodIdx = *c - 1;
          printCustomAliasOperand(MI, OpIdx, PrintMethodIdx, OS);
        } else
          printOperand(MI, *c - 1, OS);
      } else {
        SStream_concat(OS, "%c", *c);
      }
    }
  }
  return tmp;
}

#endif // PRINT_ALIAS_INSTR
