/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Subtarget Enumeration Source Fragment                                       *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine, http://www.capstone-engine.org */
/* By <PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2013-2015 */


#ifdef GET_SUBTARGETINFO_ENUM
#undef GET_SUBTARGETINFO_ENUM

enum {
  SystemZ_FeatureDistinctOps =  1ULL << 0,
  SystemZ_FeatureFPExtension =  1ULL << 1,
  SystemZ_FeatureFastSerialization =  1ULL << 2,
  SystemZ_FeatureHighWord =  1ULL << 3,
  SystemZ_FeatureInterlockedAccess1 =  1ULL << 4,
  SystemZ_FeatureLoadStoreOnCond =  1ULL << 5
};

#endif // GET_SUBTARGETINFO_ENUM

