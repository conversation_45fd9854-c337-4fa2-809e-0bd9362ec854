Documentation of Capstone disassembly framework.

* Switching to 2.1 engine.

	http://capstone-engine.org/version_2.1_API.html

* How to compile & install Capstone.

	http://capstone-engine.org/documentation.html

* Programming with C language.

	http://capstone-engine.org/lang_c.html

* Programming with Python language.

	http://capstone-engine.org/lang_python.html

* Programming with Java language.

	http://capstone-engine.org/lang_java.html

* Customize instruction mnemonics at run-time.

	http://capstone-engine.org/mnemonic.html

* Retrieve access information of instruction operands.

	http://capstone-engine.org/op_access.html

* Build compact engine with only selected architectures.

	http://capstone-engine.org/compile.html

* Build "diet" engine for even smaller libraries.

	http://capstone-engine.org/diet.html

* Build embedded engine for firmware/OS kernel.

	http://capstone-engine.org/embed.html

* SKIPDATA mode to keep disassembling after hitting a broken instruction.

	http://capstone-engine.org/skipdata.html

* Quickly iterate instructions with cs_disasm_iter().

	http://capstone-engine.org/iteration.html

* Build X86-reduce engine for firmware/OS kernel.

	http://capstone-engine.org/x86reduce.html

* Sample applications on how to embed Capstone into Windows kernel driver.

	https://github.com/aquynh/capstone/tree/master/contrib/cs_driver (in C, basic)
	https://github.com/aquynh/KernelProject (in C++)

* Sample application on how to embed Capstone into Mac OSX Kext (kernel).

	https://github.com/aquynh/CapstoneTest
