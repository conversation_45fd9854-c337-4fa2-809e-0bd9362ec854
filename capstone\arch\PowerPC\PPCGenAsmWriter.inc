/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|*Assembly Writer Source Fragment                                             *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

/* Capstone Disassembly Engine */
/* By <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2013-2015 */

#include <stdio.h>	// debug
#include <capstone/platform.h>

/// printInstruction - This method is automatically generated by tablegen
/// from the instruction set description.
static void printInstruction(MCInst *MI, SStream *O, const MCRegisterInfo *MRI)
{
  static const uint32_t OpInfo[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    10419U,	// DBG_VALUE
    0U,	// REG_SEQUENCE
    0U,	// COPY
    10412U,	// BUNDLE
    10746U,	// LIFETIME_START
    10399U,	// LIFETIME_END
    0U,	// STACKMAP
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    0U,	// STATEPOINT
    0U,	// FRAME_ALLOC
    19093U,	// ADD4
    19093U,	// ADD4TLS
    16801U,	// ADD4o
    19093U,	// ADD8
    19093U,	// ADD8TLS
    19093U,	// ADD8TLS_
    16801U,	// ADD8o
    18937U,	// ADDC
    18937U,	// ADDC8
    16741U,	// ADDC8o
    16741U,	// ADDCo
    19414U,	// ADDE
    19414U,	// ADDE8
    16924U,	// ADDE8o
    16924U,	// ADDEo
    19978U,	// ADDI
    19978U,	// ADDI8
    18982U,	// ADDIC
    18982U,	// ADDIC8
    16771U,	// ADDICo
    22885U,	// ADDIS
    22885U,	// ADDIS8
    10190U,	// ADDISdtprelHA
    9129U,	// ADDISdtprelHA32
    10173U,	// ADDISgotTprelHA
    10145U,	// ADDIStlsgdHA
    10159U,	// ADDIStlsldHA
    10133U,	// ADDIStocHA
    10524U,	// ADDIdtprelL
    9332U,	// ADDIdtprelL32
    10487U,	// ADDItlsgdL
    9289U,	// ADDItlsgdL32
    10570U,	// ADDItlsgdLADDR
    9384U,	// ADDItlsgdLADDR32
    10499U,	// ADDItlsldL
    9303U,	// ADDItlsldL32
    10586U,	// ADDItlsldLADDR
    9402U,	// ADDItlsldLADDR32
    10477U,	// ADDItocL
    268454930U,	// ADDME
    268454930U,	// ADDME8
    268452395U,	// ADDME8o
    268452395U,	// ADDMEo
    268454994U,	// ADDZE
    268454994U,	// ADDZE8
    268452428U,	// ADDZE8o
    268452428U,	// ADDZEo
    296482U,	// ADJCALLSTACKDOWN
    8947253U,	// ADJCALLSTACKUP
    19252U,	// AND
    19252U,	// AND8
    16863U,	// AND8o
    18946U,	// ANDC
    18946U,	// ANDC8
    16748U,	// ANDC8o
    16748U,	// ANDCo
    17561U,	// ANDISo
    17561U,	// ANDISo8
    17080U,	// ANDIo
    17080U,	// ANDIo8
    10681U,	// ANDIo_1_EQ_BIT
    10083U,	// ANDIo_1_EQ_BIT8
    10697U,	// ANDIo_1_GT_BIT
    10100U,	// ANDIo_1_GT_BIT8
    16863U,	// ANDo
    554190291U,	// ATOMIC_CMP_SWAP_I16
    554190269U,	// ATOMIC_CMP_SWAP_I32
    9529U,	// ATOMIC_CMP_SWAP_I64
    10001U,	// ATOMIC_CMP_SWAP_I8
    9748U,	// ATOMIC_LOAD_ADD_I16
    9167U,	// ATOMIC_LOAD_ADD_I32
    9469U,	// ATOMIC_LOAD_ADD_I64
    9940U,	// ATOMIC_LOAD_ADD_I8
    9791U,	// ATOMIC_LOAD_AND_I16
    9210U,	// ATOMIC_LOAD_AND_I32
    9628U,	// ATOMIC_LOAD_AND_I64
    9981U,	// ATOMIC_LOAD_AND_I8
    9769U,	// ATOMIC_LOAD_NAND_I16
    9188U,	// ATOMIC_LOAD_NAND_I32
    9490U,	// ATOMIC_LOAD_NAND_I64
    9960U,	// ATOMIC_LOAD_NAND_I8
    9850U,	// ATOMIC_LOAD_OR_I16
    9269U,	// ATOMIC_LOAD_OR_I32
    9571U,	// ATOMIC_LOAD_OR_I64
    10040U,	// ATOMIC_LOAD_OR_I8
    9727U,	// ATOMIC_LOAD_SUB_I16
    9146U,	// ATOMIC_LOAD_SUB_I32
    9448U,	// ATOMIC_LOAD_SUB_I64
    9906U,	// ATOMIC_LOAD_SUB_I8
    9829U,	// ATOMIC_LOAD_XOR_I16
    9248U,	// ATOMIC_LOAD_XOR_I32
    9550U,	// ATOMIC_LOAD_XOR_I64
    10021U,	// ATOMIC_LOAD_XOR_I8
    9812U,	// ATOMIC_SWAP_I16
    9231U,	// ATOMIC_SWAP_I32
    9512U,	// ATOMIC_SWAP_I64
    10117U,	// ATOMIC_SWAP_I8
    10880U,	// ATTN
    313588U,	// B
    329423U,	// BA
    25182312U,	// BC
    879125U,	// BCC
    1141269U,	// BCCA
    1403413U,	// BCCCTR
    1403413U,	// BCCCTR8
    1665557U,	// BCCCTRL
    1665557U,	// BCCCTRL8
    1927701U,	// BCCL
    2189845U,	// BCCLA
    2451989U,	// BCCLR
    2714133U,	// BCCLRL
    2900122U,	// BCCTR
    2900122U,	// BCCTR8
    2900178U,	// BCCTR8n
    2900100U,	// BCCTRL
    2900100U,	// BCCTRL8
    2900158U,	// BCCTRL8n
    2900158U,	// BCCTRLn
    2900178U,	// BCCTRn
    25182320U,	// BCL
    2900112U,	// BCLR
    2900089U,	// BCLRL
    2900148U,	// BCLRLn
    2900169U,	// BCLRn
    311373U,	// BCLalways
    25182380U,	// BCLn
    10917U,	// BCTR
    10917U,	// BCTR8
    10874U,	// BCTRL
    10874U,	// BCTRL8
    98394U,	// BCTRL8_LDinto_toc
    25182373U,	// BCn
    320294U,	// BDNZ
    320294U,	// BDNZ8
    329961U,	// BDNZA
    327936U,	// BDNZAm
    327721U,	// BDNZAp
    315560U,	// BDNZL
    329734U,	// BDNZLA
    327920U,	// BDNZLAm
    327705U,	// BDNZLAp
    10910U,	// BDNZLR
    10910U,	// BDNZLR8
    10866U,	// BDNZLRL
    9081U,	// BDNZLRLm
    9049U,	// BDNZLRLp
    9097U,	// BDNZLRm
    9065U,	// BDNZLRp
    311567U,	// BDNZLm
    311352U,	// BDNZLp
    311581U,	// BDNZm
    311366U,	// BDNZp
    320232U,	// BDZ
    320232U,	// BDZ8
    329955U,	// BDZA
    327929U,	// BDZAm
    327714U,	// BDZAp
    315554U,	// BDZL
    329727U,	// BDZLA
    327912U,	// BDZLAm
    327697U,	// BDZLAp
    10904U,	// BDZLR
    10904U,	// BDZLR8
    10859U,	// BDZLRL
    9073U,	// BDZLRLm
    9041U,	// BDZLRLp
    9090U,	// BDZLRm
    9058U,	// BDZLRp
    311560U,	// BDZLm
    311345U,	// BDZLp
    311575U,	// BDZm
    311360U,	// BDZp
    315437U,	// BL
    315437U,	// BL8
    3199021U,	// BL8_NOP
    3264557U,	// BL8_NOP_TLS
    380973U,	// BL8_TLS
    380973U,	// BL8_TLS_
    329716U,	// BLA
    329716U,	// BLA8
    3213300U,	// BLA8_NOP
    10900U,	// BLR
    10900U,	// BLR8
    10854U,	// BLRL
    380973U,	// BL_TLS
    19031U,	// BRINC
    19992U,	// CLRLSLDI
    17060U,	// CLRLSLDIo
    20334U,	// CLRLSLWI
    17158U,	// CLRLSLWIo
    20027U,	// CLRRDI
    17087U,	// CLRRDIo
    20375U,	// CLRRWI
    17187U,	// CLRRWIo
    18707U,	// CMPB
    18707U,	// CMPB8
    19296U,	// CMPD
    20020U,	// CMPDI
    19230U,	// CMPLD
    19984U,	// CMPLDI
    24018U,	// CMPLW
    20318U,	// CMPLWI
    24258U,	// CMPW
    20368U,	// CMPWI
    268454862U,	// CNTLZD
    268452371U,	// CNTLZDo
    268459932U,	// CNTLZW
    268459932U,	// CNTLZW8
    268453215U,	// CNTLZW8o
    268453215U,	// CNTLZWo
    9713U,	// CR6SET
    9699U,	// CR6UNSET
    19282U,	// CRAND
    18952U,	// CRANDC
    23565U,	// CREQV
    19266U,	// CRNAND
    22356U,	// CRNOR
    22370U,	// CROR
    19052U,	// CRORC
    33577997U,	// CRSET
    33576822U,	// CRUNSET
    22390U,	// CRXOR
    132813U,	// DCBA
    134241U,	// DCBF
    134619U,	// DCBI
    138004U,	// DCBST
    137959U,	// DCBT
    138016U,	// DCBTST
    139997U,	// DCBZ
    135323U,	// DCBZL
    268455405U,	// DCCCI
    19393U,	// DIVD
    23421U,	// DIVDU
    17630U,	// DIVDUo
    16908U,	// DIVDo
    24461U,	// DIVW
    23526U,	// DIVWU
    17647U,	// DIVWUo
    17752U,	// DIVWo
    416157U,	// DSS
    10847U,	// DSSALL
    847420187U,	// DST
    847420187U,	// DST64
    847420200U,	// DSTST
    847420200U,	// DSTST64
    847420213U,	// DSTSTT
    847420213U,	// DSTSTT64
    847420207U,	// DSTT
    847420207U,	// DSTT64
    10213U,	// DYNALLOC
    9870U,	// DYNALLOC8
    9347U,	// EH_SjLj_LongJmp32
    9591U,	// EH_SjLj_LongJmp64
    9366U,	// EH_SjLj_SetJmp32
    9610U,	// EH_SjLj_SetJmp64
    311297U,	// EH_SjLj_Setup
    23560U,	// EQV
    23560U,	// EQV8
    17662U,	// EQV8o
    17662U,	// EQVo
    268457944U,	// EVABS
    50355624U,	// EVADDIW
    268459087U,	// EVADDSMIAAW
    268459219U,	// EVADDSSIAAW
    268459153U,	// EVADDUMIAAW
    268459285U,	// EVADDUSIAAW
    23902U,	// EVADDW
    19289U,	// EVAND
    18960U,	// EVANDC
    22237U,	// EVCMPEQ
    22964U,	// EVCMPGTS
    23472U,	// EVCMPGTU
    22974U,	// EVCMPLTS
    23482U,	// EVCMPLTU
    268459753U,	// EVCNTLSW
    268459930U,	// EVCNTLZW
    23109U,	// EVDIVWS
    23524U,	// EVDIVWU
    23572U,	// EVEQV
    268454247U,	// EVEXTSB
    268455278U,	// EVEXTSH
    58739421U,	// EVLDD
    24589U,	// EVLDDX
    58739957U,	// EVLDH
    24693U,	// EVLDHX
    58744166U,	// EVLDW
    25233U,	// EVLDWX
    58743462U,	// EVLHHESPLAT
    24986U,	// EVLHHESPLATX
    58743487U,	// EVLHHOSSPLAT
    25013U,	// EVLHHOSSPLATX
    58743501U,	// EVLHHOUSPLAT
    25028U,	// EVLHHOUSPLATX
    58739699U,	// EVLWHE
    24664U,	// EVLWHEX
    58743188U,	// EVLWHOS
    24966U,	// EVLWHOSX
    58743699U,	// EVLWHOU
    25144U,	// EVLWHOUX
    58743475U,	// EVLWHSPLAT
    25000U,	// EVLWHSPLATX
    58743515U,	// EVLWWSPLAT
    25043U,	// EVLWWSPLATX
    20091U,	// EVMERGEHI
    21072U,	// EVMERGEHILO
    21061U,	// EVMERGELO
    20102U,	// EVMERGELOHI
    18003U,	// EVMHEGSMFAA
    20878U,	// EVMHEGSMFAN
    18051U,	// EVMHEGSMIAA
    20926U,	// EVMHEGSMIAN
    18088U,	// EVMHEGUMIAA
    20963U,	// EVMHEGUMIAN
    19565U,	// EVMHESMF
    18136U,	// EVMHESMFA
    23579U,	// EVMHESMFAAW
    24050U,	// EVMHESMFANW
    20145U,	// EVMHESMI
    18227U,	// EVMHESMIA
    23644U,	// EVMHESMIAAW
    24102U,	// EVMHESMIANW
    19640U,	// EVMHESSF
    18179U,	// EVMHESSFA
    23605U,	// EVMHESSFAAW
    24076U,	// EVMHESSFANW
    23776U,	// EVMHESSIAAW
    24180U,	// EVMHESSIANW
    20184U,	// EVMHEUMI
    18270U,	// EVMHEUMIA
    23710U,	// EVMHEUMIAAW
    24141U,	// EVMHEUMIANW
    23842U,	// EVMHEUSIAAW
    24219U,	// EVMHEUSIANW
    18016U,	// EVMHOGSMFAA
    20891U,	// EVMHOGSMFAN
    18064U,	// EVMHOGSMIAA
    20939U,	// EVMHOGSMIAN
    18101U,	// EVMHOGUMIAA
    20976U,	// EVMHOGUMIAN
    19585U,	// EVMHOSMF
    18158U,	// EVMHOSMFA
    23592U,	// EVMHOSMFAAW
    24063U,	// EVMHOSMFANW
    20165U,	// EVMHOSMI
    18249U,	// EVMHOSMIA
    23684U,	// EVMHOSMIAAW
    24128U,	// EVMHOSMIANW
    19660U,	// EVMHOSSF
    18201U,	// EVMHOSSFA
    23618U,	// EVMHOSSFAAW
    24089U,	// EVMHOSSFANW
    23816U,	// EVMHOSSIAAW
    24206U,	// EVMHOSSIANW
    20214U,	// EVMHOUMI
    18303U,	// EVMHOUMIA
    23750U,	// EVMHOUMIAAW
    24167U,	// EVMHOUMIANW
    23882U,	// EVMHOUSIAAW
    24245U,	// EVMHOUSIANW
    268453902U,	// EVMRA
    19575U,	// EVMWHSMF
    18147U,	// EVMWHSMFA
    20155U,	// EVMWHSMI
    18238U,	// EVMWHSMIA
    19650U,	// EVMWHSSF
    18190U,	// EVMWHSSFA
    20194U,	// EVMWHUMI
    18281U,	// EVMWHUMIA
    23671U,	// EVMWLSMIAAW
    24115U,	// EVMWLSMIANW
    23803U,	// EVMWLSSIAAW
    24193U,	// EVMWLSSIANW
    20204U,	// EVMWLUMI
    18292U,	// EVMWLUMIA
    23737U,	// EVMWLUMIAAW
    24154U,	// EVMWLUMIANW
    23869U,	// EVMWLUSIAAW
    24232U,	// EVMWLUSIANW
    19595U,	// EVMWSMF
    18169U,	// EVMWSMFA
    18029U,	// EVMWSMFAA
    20904U,	// EVMWSMFAN
    20175U,	// EVMWSMI
    18260U,	// EVMWSMIA
    18077U,	// EVMWSMIAA
    20952U,	// EVMWSMIAN
    19670U,	// EVMWSSF
    18212U,	// EVMWSSFA
    18040U,	// EVMWSSFAA
    20915U,	// EVMWSSFAN
    20224U,	// EVMWUMI
    18314U,	// EVMWUMIA
    18114U,	// EVMWUMIAA
    20989U,	// EVMWUMIAN
    19274U,	// EVNAND
    268455143U,	// EVNEG
    22363U,	// EVNOR
    22376U,	// EVOR
    19059U,	// EVORC
    24025U,	// EVRLW
    20326U,	// EVRLWI
    268459373U,	// EVRNDW
    24032U,	// EVSLW
    20352U,	// EVSLWI
    268455536U,	// EVSPLATFI
    268455748U,	// EVSPLATI
    22904U,	// EVSRWIS
    23434U,	// EVSRWIU
    23046U,	// EVSRWS
    23510U,	// EVSRWU
    58739428U,	// EVSTDD
    24597U,	// EVSTDDX
    58739964U,	// EVSTDH
    24701U,	// EVSTDHX
    58744181U,	// EVSTDW
    25241U,	// EVSTDWX
    58739707U,	// EVSTWHE
    24673U,	// EVSTWHEX
    58741308U,	// EVSTWHO
    24794U,	// EVSTWHOX
    58739785U,	// EVSTWWE
    24683U,	// EVSTWWEX
    58741353U,	// EVSTWWO
    24804U,	// EVSTWWOX
    268459113U,	// EVSUBFSMIAAW
    268459245U,	// EVSUBFSSIAAW
    268459179U,	// EVSUBFUMIAAW
    268459311U,	// EVSUBFUSIAAW
    23933U,	// EVSUBFW
    67132806U,	// EVSUBIFW
    22397U,	// EVXOR
    20002U,	// EXTLDI
    17071U,	// EXTLDIo
    20360U,	// EXTLWI
    17178U,	// EXTLWIo
    20051U,	// EXTRDI
    17114U,	// EXTRDIo
    20399U,	// EXTRWI
    17214U,	// EXTRWIo
    268454249U,	// EXTSB
    268454249U,	// EXTSB8
    268454249U,	// EXTSB8_32_64
    268452143U,	// EXTSB8o
    268452143U,	// EXTSBo
    268455280U,	// EXTSH
    268455280U,	// EXTSH8
    268455280U,	// EXTSH8_32_64
    268452478U,	// EXTSH8o
    268452478U,	// EXTSHo
    268459790U,	// EXTSW
    268459790U,	// EXTSW_32_64
    268453178U,	// EXTSW_32_64o
    268453178U,	// EXTSWo
    10885U,	// EnforceIEIO
    268457929U,	// FABSD
    268452914U,	// FABSDo
    268457929U,	// FABSS
    268452914U,	// FABSSo
    19092U,	// FADD
    22583U,	// FADDS
    17500U,	// FADDSo
    16800U,	// FADDo
    0U,	// FADDrtz
    268454656U,	// FCFID
    268458121U,	// FCFIDS
    268452983U,	// FCFIDSo
    268458848U,	// FCFIDU
    268458450U,	// FCFIDUS
    268453042U,	// FCFIDUSo
    268453077U,	// FCFIDUo
    268452288U,	// FCFIDo
    23452U,	// FCMPUD
    23452U,	// FCMPUS
    21013U,	// FCPSGND
    17278U,	// FCPSGNDo
    21013U,	// FCPSGNS
    17278U,	// FCPSGNSo
    268454665U,	// FCTID
    268460846U,	// FCTIDUZ
    268453264U,	// FCTIDUZo
    268460783U,	// FCTIDZ
    268453248U,	// FCTIDZo
    268452296U,	// FCTIDo
    268459443U,	// FCTIW
    268460857U,	// FCTIWUZ
    268453274U,	// FCTIWUZo
    268460868U,	// FCTIWZ
    268453284U,	// FCTIWZo
    268453139U,	// FCTIWo
    23551U,	// FDIV
    23039U,	// FDIVS
    17596U,	// FDIVSo
    17655U,	// FDIVo
    19100U,	// FMADD
    22592U,	// FMADDS
    17508U,	// FMADDSo
    16807U,	// FMADDo
    268457792U,	// FMR
    268452896U,	// FMRo
    18891U,	// FMSUB
    22562U,	// FMSUBS
    17481U,	// FMSUBSo
    16713U,	// FMSUBo
    20607U,	// FMUL
    22915U,	// FMULS
    17569U,	// FMULSo
    17247U,	// FMULo
    268457937U,	// FNABSD
    268452921U,	// FNABSDo
    268457937U,	// FNABSS
    268452921U,	// FNABSSo
    268455137U,	// FNEGD
    268452460U,	// FNEGDo
    268455137U,	// FNEGS
    268452460U,	// FNEGSo
    19109U,	// FNMADD
    22602U,	// FNMADDS
    17517U,	// FNMADDSo
    16815U,	// FNMADDo
    18900U,	// FNMSUB
    22572U,	// FNMSUBS
    17490U,	// FNMSUBSo
    16721U,	// FNMSUBo
    268454954U,	// FRE
    268458203U,	// FRES
    268452992U,	// FRESo
    268452412U,	// FREo
    268456252U,	// FRIMD
    268452710U,	// FRIMDo
    268456252U,	// FRIMS
    268452710U,	// FRIMSo
    268456486U,	// FRIND
    268452743U,	// FRINDo
    268456486U,	// FRINS
    268452743U,	// FRINSo
    268457336U,	// FRIPD
    268452829U,	// FRIPDo
    268457336U,	// FRIPS
    268452829U,	// FRIPSo
    268460832U,	// FRIZD
    268453257U,	// FRIZDo
    268460832U,	// FRIZS
    268453257U,	// FRIZSo
    268457576U,	// FRSP
    268452860U,	// FRSPo
    268454969U,	// FRSQRTE
    268458211U,	// FRSQRTES
    268452999U,	// FRSQRTESo
    268452418U,	// FRSQRTEo
    20559U,	// FSELD
    17240U,	// FSELDo
    20559U,	// FSELS
    17240U,	// FSELSo
    268458765U,	// FSQRT
    268458440U,	// FSQRTS
    268453033U,	// FSQRTSo
    268453060U,	// FSQRTo
    18883U,	// FSUB
    22553U,	// FSUBS
    17473U,	// FSUBSo
    16706U,	// FSUBo
    10616U,	// GETtlsADDR
    9435U,	// GETtlsADDR32
    10602U,	// GETtlsldADDR
    9420U,	// GETtlsldADDR32
    134625U,	// ICBI
    187117U,	// ICBT
    268455412U,	// ICCCI
    20344U,	// INSLWI
    17169U,	// INSLWIo
    20035U,	// INSRDI
    17096U,	// INSRDIo
    20383U,	// INSRWI
    17196U,	// INSRWIo
    20565U,	// ISEL
    20565U,	// ISEL8
    10783U,	// ISYNC
    75515893U,	// LA
    58738677U,	// LAx
    58745571U,	// LBZ
    58745571U,	// LBZ8
    24770U,	// LBZCIX
    83909613U,	// LBZU
    83909613U,	// LBZU8
    92299889U,	// LBZUX
    92299889U,	// LBZUX8
    285237953U,	// LBZX
    285237953U,	// LBZX8
    58739475U,	// LD
    285237511U,	// LDARX
    285237525U,	// LDBRX
    24739U,	// LDCIX
    83909490U,	// LDU
    92299805U,	// LDUX
    285237300U,	// LDX
    10511U,	// LDgotTprelL
    9317U,	// LDgotTprelL32
    10795U,	// LDtoc
    10736U,	// LDtocBA
    10736U,	// LDtocCPT
    10459U,	// LDtocJTI
    10469U,	// LDtocL
    58739436U,	// LFD
    83909449U,	// LFDU
    92299788U,	// LFDUX
    285237280U,	// LFDX
    285237207U,	// LFIWAX
    285237967U,	// LFIWZX
    58743027U,	// LFS
    83909539U,	// LFSU
    92299865U,	// LFSUX
    285237613U,	// LFSX
    58738478U,	// LHA
    58738478U,	// LHA8
    83909437U,	// LHAU
    83909437U,	// LHAU8
    92299744U,	// LHAUX
    92299744U,	// LHAUX8
    285237190U,	// LHAX
    285237190U,	// LHAX8
    285237540U,	// LHBRX
    285237540U,	// LHBRX8
    58745591U,	// LHZ
    58745591U,	// LHZ8
    24778U,	// LHZCIX
    83909619U,	// LHZU
    83909619U,	// LHZU8
    92299896U,	// LHZUX
    92299896U,	// LHZUX8
    285237959U,	// LHZX
    285237959U,	// LHZX8
    100683414U,	// LI
    100683414U,	// LI8
    100686188U,	// LIS
    100686188U,	// LIS8
    58744295U,	// LMW
    20407U,	// LSWI
    285237221U,	// LVEBX
    285237382U,	// LVEHX
    285237922U,	// LVEWX
    285233271U,	// LVSL
    285235116U,	// LVSR
    285237894U,	// LVX
    285233294U,	// LVXL
    58738709U,	// LWA
    285237518U,	// LWARX
    92299751U,	// LWAUX
    285237215U,	// LWAX
    285237215U,	// LWAX_32
    58738709U,	// LWA_32
    285237555U,	// LWBRX
    285237555U,	// LWBRX8
    58745676U,	// LWZ
    58745676U,	// LWZ8
    24786U,	// LWZCIX
    83909625U,	// LWZU
    83909625U,	// LWZU8
    92299903U,	// LWZUX
    92299903U,	// LWZUX8
    285237975U,	// LWZX
    285237975U,	// LWZX8
    10802U,	// LWZtoc
    285237315U,	// LXSDX
    285237156U,	// LXVD2X
    285237596U,	// LXVDSX
    285237173U,	// LXVW4X
    415475U,	// MBAR
    268455060U,	// MCRF
    268458232U,	// MCRFS
    284430U,	// MFCR
    284430U,	// MFCR8
    284601U,	// MFCTR
    284601U,	// MFCTR8
    268457721U,	// MFDCR
    284909U,	// MFFS
    279698U,	// MFFSo
    284466U,	// MFLR
    284466U,	// MFLR8
    284568U,	// MFMSR
    109071514U,	// MFOCRF
    109071514U,	// MFOCRF8
    268457860U,	// MFSPR
    117462930U,	// MFSR
    268456492U,	// MFSRIN
    268454264U,	// MFTB
    3430276U,	// MFTB8
    3692420U,	// MFVRSAVE
    3692420U,	// MFVRSAVEv
    284444U,	// MFVSCR
    10789U,	// MSYNC
    268455082U,	// MTCRF
    268455082U,	// MTCRF8
    284608U,	// MTCTR
    284608U,	// MTCTR8
    284608U,	// MTCTR8loop
    284608U,	// MTCTRloop
    302159623U,	// MTDCR
    411053U,	// MTFSB0
    411061U,	// MTFSB1
    19633U,	// MTFSF
    20072U,	// MTFSFI
    17123U,	// MTFSFIo
    268455089U,	// MTFSFb
    16996U,	// MTFSFo
    284472U,	// MTLR
    284472U,	// MTLR8
    268457887U,	// MTMSR
    268454758U,	// MTMSRD
    199842U,	// MTOCRF
    199842U,	// MTOCRF8
    268457867U,	// MTSPR
    219046U,	// MTSR
    268456500U,	// MTSRIN
    278748U,	// MTVRSAVE
    426204U,	// MTVRSAVEv
    284452U,	// MTVSCR
    19191U,	// MULHD
    23382U,	// MULHDU
    17612U,	// MULHDUo
    16824U,	// MULHDo
    23969U,	// MULHW
    23492U,	// MULHWU
    17638U,	// MULHWUo
    17675U,	// MULHWo
    19223U,	// MULLD
    16848U,	// MULLDo
    20122U,	// MULLI
    20122U,	// MULLI8
    24011U,	// MULLW
    17691U,	// MULLWo
    10640U,	// MoveGOTtoLR
    10628U,	// MovePCtoLR
    10070U,	// MovePCtoLR8
    19260U,	// NAND
    19260U,	// NAND8
    16862U,	// NAND8o
    16862U,	// NANDo
    268455138U,	// NEG
    268455138U,	// NEG8
    268452461U,	// NEG8o
    268452461U,	// NEGo
    10896U,	// NOP
    9105U,	// NOP_GT_PWR6
    9117U,	// NOP_GT_PWR7
    22351U,	// NOR
    22351U,	// NOR8
    17446U,	// NOR8o
    17446U,	// NORo
    22344U,	// OR
    22344U,	// OR8
    17447U,	// OR8o
    19047U,	// ORC
    19047U,	// ORC8
    16787U,	// ORC8o
    16787U,	// ORCo
    20276U,	// ORI
    20276U,	// ORI8
    22898U,	// ORIS
    22898U,	// ORIS8
    17447U,	// ORo
    268454799U,	// POPCNTD
    268459823U,	// POPCNTW
    10713U,	// PPC32GOT
    10723U,	// PPC32PICGOT
    20233U,	// QVALIGNI
    20233U,	// QVALIGNIb
    20233U,	// QVALIGNIs
    20281U,	// QVESPLATI
    20281U,	// QVESPLATIb
    20281U,	// QVESPLATIs
    268457927U,	// QVFABS
    268457927U,	// QVFABSs
    19090U,	// QVFADD
    22581U,	// QVFADDS
    22581U,	// QVFADDSs
    268454654U,	// QVFCFID
    268458119U,	// QVFCFIDS
    268458846U,	// QVFCFIDU
    268458448U,	// QVFCFIDUS
    268454654U,	// QVFCFIDb
    22227U,	// QVFCMPEQ
    22227U,	// QVFCMPEQb
    22227U,	// QVFCMPEQbs
    23283U,	// QVFCMPGT
    23283U,	// QVFCMPGTb
    23283U,	// QVFCMPGTbs
    23299U,	// QVFCMPLT
    23299U,	// QVFCMPLTb
    23299U,	// QVFCMPLTbs
    21011U,	// QVFCPSGN
    21011U,	// QVFCPSGNs
    268454663U,	// QVFCTID
    268458856U,	// QVFCTIDU
    268460844U,	// QVFCTIDUZ
    268460781U,	// QVFCTIDZ
    268454663U,	// QVFCTIDb
    268459441U,	// QVFCTIW
    268458956U,	// QVFCTIWU
    268460855U,	// QVFCTIWUZ
    268460866U,	// QVFCTIWZ
    20513U,	// QVFLOGICAL
    20513U,	// QVFLOGICALb
    20513U,	// QVFLOGICALs
    19098U,	// QVFMADD
    22590U,	// QVFMADDS
    22590U,	// QVFMADDSs
    268457790U,	// QVFMR
    268457790U,	// QVFMRb
    268457790U,	// QVFMRs
    18889U,	// QVFMSUB
    22560U,	// QVFMSUBS
    22560U,	// QVFMSUBSs
    20605U,	// QVFMUL
    22913U,	// QVFMULS
    22913U,	// QVFMULSs
    268457935U,	// QVFNABS
    268457935U,	// QVFNABSs
    268455135U,	// QVFNEG
    268455135U,	// QVFNEGs
    19107U,	// QVFNMADD
    22600U,	// QVFNMADDS
    22600U,	// QVFNMADDSs
    18898U,	// QVFNMSUB
    22570U,	// QVFNMSUBS
    22570U,	// QVFNMSUBSs
    20817U,	// QVFPERM
    20817U,	// QVFPERMs
    268454952U,	// QVFRE
    268458201U,	// QVFRES
    268458201U,	// QVFRESs
    268456250U,	// QVFRIM
    268456250U,	// QVFRIMs
    268456484U,	// QVFRIN
    268456484U,	// QVFRINs
    268457334U,	// QVFRIP
    268457334U,	// QVFRIPs
    268460830U,	// QVFRIZ
    268460830U,	// QVFRIZs
    268457574U,	// QVFRSP
    268457574U,	// QVFRSPs
    268454967U,	// QVFRSQRTE
    268458209U,	// QVFRSQRTES
    268458209U,	// QVFRSQRTESs
    20557U,	// QVFSEL
    20557U,	// QVFSELb
    20557U,	// QVFSELbb
    20557U,	// QVFSELbs
    18881U,	// QVFSUB
    22551U,	// QVFSUBS
    22551U,	// QVFSUBSs
    21000U,	// QVFTSTNAN
    21000U,	// QVFTSTNANb
    21000U,	// QVFTSTNANbs
    19144U,	// QVFXMADD
    22640U,	// QVFXMADDS
    20613U,	// QVFXMUL
    22922U,	// QVFXMULS
    19117U,	// QVFXXCPNMADD
    22611U,	// QVFXXCPNMADDS
    19154U,	// QVFXXMADD
    22651U,	// QVFXXMADDS
    19131U,	// QVFXXNPMADD
    22626U,	// QVFXXNPMADDS
    125849083U,	// QVGPCI
    285237749U,	// QVLFCDUX
    285231221U,	// QVLFCDUXA
    285237242U,	// QVLFCDX
    285231141U,	// QVLFCDXA
    285237826U,	// QVLFCSUX
    285231265U,	// QVLFCSUXA
    285237577U,	// QVLFCSX
    285231181U,	// QVLFCSXA
    285237577U,	// QVLFCSXs
    92299786U,	// QVLFDUX
    285231244U,	// QVLFDUXA
    285237278U,	// QVLFDX
    285231162U,	// QVLFDXA
    285237278U,	// QVLFDXb
    285237205U,	// QVLFIWAX
    285231130U,	// QVLFIWAXA
    285237965U,	// QVLFIWZX
    285231320U,	// QVLFIWZXA
    92299863U,	// QVLFSUX
    285231288U,	// QVLFSUXA
    285237611U,	// QVLFSX
    285231202U,	// QVLFSXA
    285237611U,	// QVLFSXb
    285237611U,	// QVLFSXs
    285237295U,	// QVLPCLDX
    285237628U,	// QVLPCLSX
    3957116U,	// QVLPCLSXint
    285237305U,	// QVLPCRDX
    285237648U,	// QVLPCRSX
    285237759U,	// QVSTFCDUX
    285231232U,	// QVSTFCDUXA
    285233139U,	// QVSTFCDUXI
    285231042U,	// QVSTFCDUXIA
    285237251U,	// QVSTFCDX
    285231151U,	// QVSTFCDXA
    285233097U,	// QVSTFCDXI
    285230996U,	// QVSTFCDXIA
    285237836U,	// QVSTFCSUX
    285231276U,	// QVSTFCSUXA
    285233162U,	// QVSTFCSUXI
    285231067U,	// QVSTFCSUXIA
    285237586U,	// QVSTFCSX
    285231191U,	// QVSTFCSXA
    285233118U,	// QVSTFCSXI
    285231019U,	// QVSTFCSXIA
    285237586U,	// QVSTFCSXs
    92447251U,	// QVSTFDUX
    285231254U,	// QVSTFDUXA
    285233151U,	// QVSTFDUXI
    285231055U,	// QVSTFDUXIA
    285237286U,	// QVSTFDX
    285231171U,	// QVSTFDXA
    285233108U,	// QVSTFDXI
    285231008U,	// QVSTFDXIA
    285237286U,	// QVSTFDXb
    285237937U,	// QVSTFIWX
    285231309U,	// QVSTFIWXA
    92447328U,	// QVSTFSUX
    285231298U,	// QVSTFSUXA
    285233174U,	// QVSTFSUXI
    285231080U,	// QVSTFSUXIA
    92447328U,	// QVSTFSUXs
    285237619U,	// QVSTFSX
    285231211U,	// QVSTFSXA
    285233129U,	// QVSTFSXI
    285231031U,	// QVSTFSXIA
    285237619U,	// QVSTFSXs
    10548U,	// RESTORE_CR
    10653U,	// RESTORE_CRBIT
    10429U,	// RESTORE_VRSAVE
    10827U,	// RFCI
    10838U,	// RFDI
    10843U,	// RFI
    10810U,	// RFID
    10832U,	// RFMCI
    20534U,	// RLDCL
    17223U,	// RLDCLo
    22272U,	// RLDCR
    17423U,	// RLDCRo
    18989U,	// RLDIC
    20541U,	// RLDICL
    20541U,	// RLDICL_32_64
    17231U,	// RLDICLo
    22292U,	// RLDICR
    17431U,	// RLDICRo
    16779U,	// RLDICo
    1115704993U,	// RLDIMI
    1115701996U,	// RLDIMIo
    1384140457U,	// RLWIMI
    1384140457U,	// RLWIMI8
    1384137461U,	// RLWIMI8o
    1384137461U,	// RLWIMIo
    20802U,	// RLWINM
    20802U,	// RLWINM8
    17261U,	// RLWINM8o
    17261U,	// RLWINMo
    20810U,	// RLWNM
    20810U,	// RLWNM8
    17270U,	// RLWNM8o
    17270U,	// RLWNMo
    20043U,	// ROTRDI
    17105U,	// ROTRDIo
    20391U,	// ROTRWI
    17205U,	// ROTRWIo
    10205U,	// ReadTB
    281210U,	// SC
    9649U,	// SELECT_CC_F4
    9881U,	// SELECT_CC_F8
    9674U,	// SELECT_CC_I4
    9926U,	// SELECT_CC_I8
    10223U,	// SELECT_CC_QBRC
    10252U,	// SELECT_CC_QFRC
    10341U,	// SELECT_CC_QSRC
    10312U,	// SELECT_CC_VRRC
    10281U,	// SELECT_CC_VSFRC
    10370U,	// SELECT_CC_VSRC
    9663U,	// SELECT_F4
    9895U,	// SELECT_F8
    9688U,	// SELECT_I4
    10059U,	// SELECT_I8
    10239U,	// SELECT_QBRC
    10268U,	// SELECT_QFRC
    10357U,	// SELECT_QSRC
    10328U,	// SELECT_VRRC
    10298U,	// SELECT_VSFRC
    10386U,	// SELECT_VSRC
    10761U,	// SLBIA
    281604U,	// SLBIE
    268454876U,	// SLBMFEE
    268454959U,	// SLBMTE
    19244U,	// SLD
    19996U,	// SLDI
    17064U,	// SLDIo
    16856U,	// SLDo
    24034U,	// SLW
    24034U,	// SLW8
    17699U,	// SLW8o
    20338U,	// SLWI
    17162U,	// SLWIo
    17699U,	// SLWo
    10560U,	// SPILL_CR
    10668U,	// SPILL_CRBIT
    10445U,	// SPILL_VRSAVE
    19084U,	// SRAD
    19971U,	// SRADI
    17052U,	// SRADIo
    16793U,	// SRADo
    23896U,	// SRAW
    20302U,	// SRAWI
    17150U,	// SRAWIo
    17668U,	// SRAWo
    19305U,	// SRD
    20037U,	// SRDI
    17098U,	// SRDIo
    16869U,	// SRDo
    24265U,	// SRW
    24265U,	// SRW8
    17705U,	// SRW8o
    20385U,	// SRWI
    17198U,	// SRWIo
    17705U,	// SRWo
    58739088U,	// STB
    58739088U,	// STB8
    24731U,	// STBCIX
    84056899U,	// STBU
    84056899U,	// STBU8
    92447214U,	// STBUX
    92447214U,	// STBUX8
    285237236U,	// STBX
    285237236U,	// STBX8
    58739608U,	// STD
    285237532U,	// STDBRX
    24746U,	// STDCIX
    285230440U,	// STDCX
    84056951U,	// STDU
    92447267U,	// STDUX
    285237330U,	// STDX
    58739441U,	// STFD
    84056911U,	// STFDU
    92447253U,	// STFDUX
    285237288U,	// STFDX
    285237939U,	// STFIWX
    58743039U,	// STFS
    84057001U,	// STFSU
    92447330U,	// STFSUX
    285237621U,	// STFSX
    58740113U,	// STH
    58740113U,	// STH8
    285237547U,	// STHBRX
    24754U,	// STHCIX
    84056964U,	// STHU
    84056964U,	// STHU8
    92447281U,	// STHUX
    92447281U,	// STHUX8
    285237397U,	// STHX
    285237397U,	// STHX8
    58744300U,	// STMW
    20413U,	// STSWI
    285237228U,	// STVEBX
    285237389U,	// STVEHX
    285237929U,	// STVEWX
    285237899U,	// STVX
    285233300U,	// STVXL
    58744632U,	// STW
    58744632U,	// STW8
    285237562U,	// STWBRX
    24762U,	// STWCIX
    285230448U,	// STWCX
    84057054U,	// STWU
    84057054U,	// STWU8
    92447338U,	// STWUX
    92447338U,	// STWUX8
    285237947U,	// STWX
    285237947U,	// STWX8
    285237322U,	// STXSDX
    285237164U,	// STXVD2X
    285237181U,	// STXVW4X
    19559U,	// SUBF
    19559U,	// SUBF8
    16989U,	// SUBF8o
    18968U,	// SUBFC
    18968U,	// SUBFC8
    16755U,	// SUBFC8o
    16755U,	// SUBFCo
    19436U,	// SUBFE
    19436U,	// SUBFE8
    16931U,	// SUBFE8o
    16931U,	// SUBFEo
    18996U,	// SUBFIC
    18996U,	// SUBFIC8
    268454937U,	// SUBFME
    268454937U,	// SUBFME8
    268452403U,	// SUBFME8o
    268452403U,	// SUBFMEo
    268455001U,	// SUBFZE
    268455001U,	// SUBFZE8
    268452436U,	// SUBFZE8o
    268452436U,	// SUBFZEo
    16989U,	// SUBFo
    19943U,	// SUBI
    18975U,	// SUBIC
    16763U,	// SUBICo
    22878U,	// SUBIS
    281182U,	// SYNC
    313588U,	// TAILB
    313588U,	// TAILB8
    329423U,	// TAILBA
    329423U,	// TAILBA8
    10917U,	// TAILBCTR
    10917U,	// TAILBCTR8
    269026886U,	// TCRETURNai
    269026793U,	// TCRETURNai8
    269011582U,	// TCRETURNdi
    269010423U,	// TCRETURNdi8
    268981990U,	// TCRETURNri
    268977669U,	// TCRETURNri8
    150420U,	// TD
    151131U,	// TDI
    10767U,	// TLBIA
    4361227U,	// TLBIE
    282693U,	// TLBIEL
    268459980U,	// TLBIVAX
    281360U,	// TLBLD
    282259U,	// TLBLI
    10815U,	// TLBRE
    19489U,	// TLBRE2
    268460354U,	// TLBSX
    24898U,	// TLBSX2
    17784U,	// TLBSX2D
    10775U,	// TLBSYNC
    10821U,	// TLBWE
    19522U,	// TLBWE2
    10891U,	// TRAP
    155425U,	// TW
    151492U,	// TWI
    268453395U,	// UPDATE_VRSAVE
    10537U,	// UpdateGBR
    24390U,	// VADDCUW
    21753U,	// VADDFP
    22514U,	// VADDSBS
    22831U,	// VADDSHS
    23073U,	// VADDSWS
    20674U,	// VADDUBM
    22542U,	// VADDUBS
    20702U,	// VADDUDM
    20741U,	// VADDUHM
    22859U,	// VADDUHS
    20860U,	// VADDUWM
    23100U,	// VADDUWS
    19290U,	// VAND
    18961U,	// VANDC
    18728U,	// VAVGSB
    19759U,	// VAVGSH
    24279U,	// VAVGSW
    18846U,	// VAVGUB
    19871U,	// VAVGUH
    24408U,	// VAVGUW
    1652580708U,	// VCFSX
    1879073124U,	// VCFSX_0
    1652580906U,	// VCFUX
    1879073322U,	// VCFUX_0
    268454382U,	// VCLZB
    268454855U,	// VCLZD
    268455380U,	// VCLZH
    268459923U,	// VCLZW
    21717U,	// VCMPBFP
    17330U,	// VCMPBFPo
    21816U,	// VCMPEQFP
    17351U,	// VCMPEQFPo
    18871U,	// VCMPEQUB
    16695U,	// VCMPEQUBo
    19365U,	// VCMPEQUD
    16886U,	// VCMPEQUDo
    19896U,	// VCMPEQUH
    17030U,	// VCMPEQUHo
    24433U,	// VCMPEQUW
    17730U,	// VCMPEQUWo
    21770U,	// VCMPGEFP
    17340U,	// VCMPGEFPo
    21826U,	// VCMPGTFP
    17362U,	// VCMPGTFPo
    18781U,	// VCMPGTSB
    16676U,	// VCMPGTSBo
    19324U,	// VCMPGTSD
    16875U,	// VCMPGTSDo
    19812U,	// VCMPGTSH
    17011U,	// VCMPGTSHo
    24324U,	// VCMPGTSW
    17711U,	// VCMPGTSWo
    18908U,	// VCMPGTUB
    16730U,	// VCMPGTUBo
    19375U,	// VCMPGTUD
    16897U,	// VCMPGTUDo
    19906U,	// VCMPGTUH
    17041U,	// VCMPGTUHo
    24443U,	// VCMPGTUW
    17741U,	// VCMPGTUWo
    1652578966U,	// VCTSXS
    1879071382U,	// VCTSXS_0
    1652578974U,	// VCTUXS
    1879071390U,	// VCTUXS_0
    23573U,	// VEQV
    268457243U,	// VEXPTEFP
    268457217U,	// VLOGEFP
    21744U,	// VMADDFP
    21836U,	// VMAXFP
    18800U,	// VMAXSB
    19334U,	// VMAXSD
    19831U,	// VMAXSH
    24341U,	// VMAXSW
    18918U,	// VMAXUB
    19385U,	// VMAXUD
    19916U,	// VMAXUH
    24453U,	// VMAXUW
    22808U,	// VMHADDSHS
    22819U,	// VMHRADDSHS
    19357U,	// VMIDUD
    21808U,	// VMINFP
    18764U,	// VMINSB
    19316U,	// VMINSD
    19795U,	// VMINSH
    24307U,	// VMINSW
    18854U,	// VMINUB
    19879U,	// VMINUH
    24416U,	// VMINUW
    20730U,	// VMLADDUHM
    18679U,	// VMRGHB
    19716U,	// VMRGHH
    23952U,	// VMRGHW
    18687U,	// VMRGLB
    19724U,	// VMRGLH
    23994U,	// VMRGLW
    20655U,	// VMSUMMBM
    20711U,	// VMSUMSHM
    22840U,	// VMSUMSHS
    20683U,	// VMSUMUBM
    20750U,	// VMSUMUHM
    22868U,	// VMSUMUHS
    18719U,	// VMULESB
    19750U,	// VMULESH
    24270U,	// VMULESW
    18837U,	// VMULEUB
    19862U,	// VMULEUH
    24399U,	// VMULEUW
    18772U,	// VMULOSB
    19803U,	// VMULOSH
    24315U,	// VMULOSW
    18862U,	// VMULOUB
    19887U,	// VMULOUH
    24424U,	// VMULOUW
    20869U,	// VMULUWM
    19275U,	// VNAND
    21726U,	// VNMSUBFP
    22364U,	// VNOR
    22377U,	// VOR
    19060U,	// VORC
    20826U,	// VPERM
    24823U,	// VPKPX
    22946U,	// VPKSHSS
    23003U,	// VPKSHUS
    22955U,	// VPKSWSS
    23021U,	// VPKSWUS
    20833U,	// VPKUHUM
    23012U,	// VPKUHUS
    20842U,	// VPKUWUM
    23030U,	// VPKUWUS
    268454278U,	// VPOPCNTB
    268454798U,	// VPOPCNTD
    268455303U,	// VPOPCNTH
    268459822U,	// VPOPCNTW
    268457236U,	// VREFP
    268456216U,	// VRFIM
    268456477U,	// VRFIN
    268457300U,	// VRFIP
    268460796U,	// VRFIZ
    18695U,	// VRLB
    19237U,	// VRLD
    19732U,	// VRLH
    24026U,	// VRLW
    268457253U,	// VRSQRTEFP
    20571U,	// VSEL
    20600U,	// VSL
    18701U,	// VSLB
    19243U,	// VSLD
    20243U,	// VSLDOI
    19738U,	// VSLH
    21085U,	// VSLO
    24033U,	// VSLW
    1652574590U,	// VSPLTB
    1652575615U,	// VSPLTH
    134236473U,	// VSPLTISB
    134237504U,	// VSPLTISH
    134242015U,	// VSPLTISW
    1652580125U,	// VSPLTW
    22445U,	// VSR
    18672U,	// VSRAB
    19083U,	// VSRAD
    19694U,	// VSRAH
    23895U,	// VSRAW
    18713U,	// VSRB
    19310U,	// VSRD
    19744U,	// VSRH
    21091U,	// VSRO
    24264U,	// VSRW
    24381U,	// VSUBCUW
    21736U,	// VSUBFP
    22505U,	// VSUBSBS
    22799U,	// VSUBSHS
    23064U,	// VSUBSWS
    20665U,	// VSUBUBM
    22533U,	// VSUBUBS
    20693U,	// VSUBUDM
    20721U,	// VSUBUHM
    22850U,	// VSUBUHS
    20851U,	// VSUBUWM
    23091U,	// VSUBUWS
    23054U,	// VSUM2SWS
    22495U,	// VSUM4SBS
    22789U,	// VSUM4SHS
    22523U,	// VSUM4UBS
    23082U,	// VSUMSWS
    268460270U,	// VUPKHPX
    268454192U,	// VUPKHSB
    268455223U,	// VUPKHSH
    268460286U,	// VUPKLPX
    268454211U,	// VUPKLSB
    268455242U,	// VUPKLSH
    22398U,	// VXOR
    33576830U,	// V_SET0
    33576830U,	// V_SET0B
    33576830U,	// V_SET0H
    4480735U,	// V_SETALLONES
    4480735U,	// V_SETALLONESB
    4480735U,	// V_SETALLONESH
    285437U,	// WAIT
    281573U,	// WRTEE
    282208U,	// WRTEEI
    22385U,	// XOR
    22385U,	// XOR8
    17452U,	// XOR8o
    20275U,	// XORI
    20275U,	// XORI8
    22897U,	// XORIS
    22897U,	// XORIS8
    17452U,	// XORo
    268457014U,	// XSABSDP
    21216U,	// XSADDDP
    21497U,	// XSCMPODP
    21629U,	// XSCMPUDP
    21457U,	// XSCPSGNDP
    268457543U,	// XSCVDPSP
    268458129U,	// XSCVDPSXDS
    268458574U,	// XSCVDPSXWS
    268458165U,	// XSCVDPUXDS
    268458610U,	// XSCVDPUXWS
    268456963U,	// XSCVSPDP
    268456690U,	// XSCVSXDDP
    268456712U,	// XSCVUXDDP
    21639U,	// XSDIVDP
    2189447864U,	// XSMADDADP
    2189448123U,	// XSMADDMDP
    21699U,	// XSMAXDP
    21479U,	// XSMINDP
    2189447818U,	// XSMSUBADP
    2189448077U,	// XSMSUBMDP
    21347U,	// XSMULDP
    268456994U,	// XSNABSDP
    268456785U,	// XSNEGDP
    2189447840U,	// XSNMADDADP
    2189448099U,	// XSNMADDMDP
    2189447794U,	// XSNMSUBADP
    2189448053U,	// XSNMSUBMDP
    268455707U,	// XSRDPI
    268454460U,	// XSRDPIC
    268456223U,	// XSRDPIM
    268457307U,	// XSRDPIP
    268460803U,	// XSRDPIZ
    268456745U,	// XSREDP
    268456761U,	// XSRSQRTEDP
    268457043U,	// XSSQRTDP
    21198U,	// XSSUBDP
    21648U,	// XSTDIVDP
    268457053U,	// XSTSQRTDP
    268457023U,	// XVABSDP
    268457592U,	// XVABSSP
    21225U,	// XVADDDP
    21941U,	// XVADDSP
    21527U,	// XVCMPEQDP
    17306U,	// XVCMPEQDPo
    22107U,	// XVCMPEQSP
    17392U,	// XVCMPEQSPo
    21278U,	// XVCMPGEDP
    17294U,	// XVCMPGEDPo
    21972U,	// XVCMPGESP
    17380U,	// XVCMPGESPo
    21576U,	// XVCMPGTDP
    17318U,	// XVCMPGTDPo
    22145U,	// XVCMPGTSP
    17411U,	// XVCMPGTSPo
    21468U,	// XVCPSGNDP
    22067U,	// XVCPSGNSP
    268457553U,	// XVCVDPSP
    268458141U,	// XVCVDPSXDS
    268458586U,	// XVCVDPSXWS
    268458177U,	// XVCVDPUXDS
    268458622U,	// XVCVDPUXWS
    268456973U,	// XVCVSPDP
    268458153U,	// XVCVSPSXDS
    268458598U,	// XVCVSPSXWS
    268458189U,	// XVCVSPUXDS
    268458634U,	// XVCVSPUXWS
    268456701U,	// XVCVSXDDP
    268457406U,	// XVCVSXDSP
    268457133U,	// XVCVSXWDP
    268457652U,	// XVCVSXWSP
    268456723U,	// XVCVUXDDP
    268457417U,	// XVCVUXDSP
    268457144U,	// XVCVUXWDP
    268457663U,	// XVCVUXWSP
    21668U,	// XVDIVDP
    22187U,	// XVDIVSP
    2189447875U,	// XVMADDADP
    2189448609U,	// XVMADDASP
    2189448134U,	// XVMADDMDP
    2189448744U,	// XVMADDMSP
    21708U,	// XVMAXDP
    22218U,	// XVMAXSP
    21488U,	// XVMINDP
    22078U,	// XVMINSP
    2189447829U,	// XVMSUBADP
    2189448586U,	// XVMSUBASP
    2189448088U,	// XVMSUBMDP
    2189448721U,	// XVMSUBMSP
    21356U,	// XVMULDP
    22012U,	// XVMULSP
    268457004U,	// XVNABSDP
    268457582U,	// XVNABSSP
    268456794U,	// XVNEGDP
    268457459U,	// XVNEGSP
    2189447852U,	// XVNMADDADP
    2189448597U,	// XVNMADDASP
    2189448111U,	// XVNMADDMDP
    2189448732U,	// XVNMADDMSP
    2189447806U,	// XVNMSUBADP
    2189448574U,	// XVNMSUBASP
    2189448065U,	// XVNMSUBMDP
    2189448709U,	// XVNMSUBMSP
    268455715U,	// XVRDPI
    268454469U,	// XVRDPIC
    268456232U,	// XVRDPIM
    268457316U,	// XVRDPIP
    268460812U,	// XVRDPIZ
    268456753U,	// XVREDP
    268457439U,	// XVRESP
    268455723U,	// XVRSPI
    268454478U,	// XVRSPIC
    268456241U,	// XVRSPIM
    268457325U,	// XVRSPIP
    268460821U,	// XVRSPIZ
    268456773U,	// XVRSQRTEDP
    268457447U,	// XVRSQRTESP
    268457075U,	// XVSQRTDP
    268457623U,	// XVSQRTSP
    21207U,	// XVSUBDP
    21932U,	// XVSUBSP
    21658U,	// XVTDIVDP
    22177U,	// XVTDIVSP
    268457064U,	// XVTSQRTDP
    268457612U,	// XVTSQRTSP
    19249U,	// XXLAND
    18943U,	// XXLANDC
    23557U,	// XXLEQV
    19257U,	// XXLNAND
    22348U,	// XXLNOR
    22341U,	// XXLOR
    19044U,	// XXLORC
    22341U,	// XXLORf
    22382U,	// XXLXOR
    23960U,	// XXMRGHW
    24002U,	// XXMRGLW
    20010U,	// XXPERMDI
    20577U,	// XXSEL
    20309U,	// XXSLDWI
    24357U,	// XXSPLTW
    150005U,	// gBC
    149203U,	// gBCA
    153522U,	// gBCCTR
    151663U,	// gBCCTRL
    151601U,	// gBCL
    149497U,	// gBCLA
    153388U,	// gBCLR
    151656U,	// gBCLRL
    0U
  };

  static const uint16_t OpInfo2[] = {
    0U,	// PHI
    0U,	// INLINEASM
    0U,	// CFI_INSTRUCTION
    0U,	// EH_LABEL
    0U,	// GC_LABEL
    0U,	// KILL
    0U,	// EXTRACT_SUBREG
    0U,	// INSERT_SUBREG
    0U,	// IMPLICIT_DEF
    0U,	// SUBREG_TO_REG
    0U,	// COPY_TO_REGCLASS
    0U,	// DBG_VALUE
    0U,	// REG_SEQUENCE
    0U,	// COPY
    0U,	// BUNDLE
    0U,	// LIFETIME_START
    0U,	// LIFETIME_END
    0U,	// STACKMAP
    0U,	// PATCHPOINT
    0U,	// LOAD_STACK_GUARD
    0U,	// STATEPOINT
    0U,	// FRAME_ALLOC
    0U,	// ADD4
    0U,	// ADD4TLS
    0U,	// ADD4o
    0U,	// ADD8
    0U,	// ADD8TLS
    0U,	// ADD8TLS_
    0U,	// ADD8o
    0U,	// ADDC
    0U,	// ADDC8
    0U,	// ADDC8o
    0U,	// ADDCo
    0U,	// ADDE
    0U,	// ADDE8
    0U,	// ADDE8o
    0U,	// ADDEo
    1U,	// ADDI
    1U,	// ADDI8
    1U,	// ADDIC
    1U,	// ADDIC8
    1U,	// ADDICo
    1U,	// ADDIS
    1U,	// ADDIS8
    0U,	// ADDISdtprelHA
    0U,	// ADDISdtprelHA32
    0U,	// ADDISgotTprelHA
    0U,	// ADDIStlsgdHA
    0U,	// ADDIStlsldHA
    0U,	// ADDIStocHA
    0U,	// ADDIdtprelL
    0U,	// ADDIdtprelL32
    0U,	// ADDItlsgdL
    0U,	// ADDItlsgdL32
    0U,	// ADDItlsgdLADDR
    0U,	// ADDItlsgdLADDR32
    0U,	// ADDItlsldL
    0U,	// ADDItlsldL32
    0U,	// ADDItlsldLADDR
    0U,	// ADDItlsldLADDR32
    0U,	// ADDItocL
    0U,	// ADDME
    0U,	// ADDME8
    0U,	// ADDME8o
    0U,	// ADDMEo
    0U,	// ADDZE
    0U,	// ADDZE8
    0U,	// ADDZE8o
    0U,	// ADDZEo
    0U,	// ADJCALLSTACKDOWN
    0U,	// ADJCALLSTACKUP
    0U,	// AND
    0U,	// AND8
    0U,	// AND8o
    0U,	// ANDC
    0U,	// ANDC8
    0U,	// ANDC8o
    0U,	// ANDCo
    2U,	// ANDISo
    2U,	// ANDISo8
    2U,	// ANDIo
    2U,	// ANDIo8
    0U,	// ANDIo_1_EQ_BIT
    0U,	// ANDIo_1_EQ_BIT8
    0U,	// ANDIo_1_GT_BIT
    0U,	// ANDIo_1_GT_BIT8
    0U,	// ANDo
    0U,	// ATOMIC_CMP_SWAP_I16
    0U,	// ATOMIC_CMP_SWAP_I32
    0U,	// ATOMIC_CMP_SWAP_I64
    0U,	// ATOMIC_CMP_SWAP_I8
    0U,	// ATOMIC_LOAD_ADD_I16
    0U,	// ATOMIC_LOAD_ADD_I32
    0U,	// ATOMIC_LOAD_ADD_I64
    0U,	// ATOMIC_LOAD_ADD_I8
    0U,	// ATOMIC_LOAD_AND_I16
    0U,	// ATOMIC_LOAD_AND_I32
    0U,	// ATOMIC_LOAD_AND_I64
    0U,	// ATOMIC_LOAD_AND_I8
    0U,	// ATOMIC_LOAD_NAND_I16
    0U,	// ATOMIC_LOAD_NAND_I32
    0U,	// ATOMIC_LOAD_NAND_I64
    0U,	// ATOMIC_LOAD_NAND_I8
    0U,	// ATOMIC_LOAD_OR_I16
    0U,	// ATOMIC_LOAD_OR_I32
    0U,	// ATOMIC_LOAD_OR_I64
    0U,	// ATOMIC_LOAD_OR_I8
    0U,	// ATOMIC_LOAD_SUB_I16
    0U,	// ATOMIC_LOAD_SUB_I32
    0U,	// ATOMIC_LOAD_SUB_I64
    0U,	// ATOMIC_LOAD_SUB_I8
    0U,	// ATOMIC_LOAD_XOR_I16
    0U,	// ATOMIC_LOAD_XOR_I32
    0U,	// ATOMIC_LOAD_XOR_I64
    0U,	// ATOMIC_LOAD_XOR_I8
    0U,	// ATOMIC_SWAP_I16
    0U,	// ATOMIC_SWAP_I32
    0U,	// ATOMIC_SWAP_I64
    0U,	// ATOMIC_SWAP_I8
    0U,	// ATTN
    0U,	// B
    0U,	// BA
    0U,	// BC
    0U,	// BCC
    0U,	// BCCA
    0U,	// BCCCTR
    0U,	// BCCCTR8
    0U,	// BCCCTRL
    0U,	// BCCCTRL8
    0U,	// BCCL
    0U,	// BCCLA
    0U,	// BCCLR
    0U,	// BCCLRL
    0U,	// BCCTR
    0U,	// BCCTR8
    0U,	// BCCTR8n
    0U,	// BCCTRL
    0U,	// BCCTRL8
    0U,	// BCCTRL8n
    0U,	// BCCTRLn
    0U,	// BCCTRn
    0U,	// BCL
    0U,	// BCLR
    0U,	// BCLRL
    0U,	// BCLRLn
    0U,	// BCLRn
    0U,	// BCLalways
    0U,	// BCLn
    0U,	// BCTR
    0U,	// BCTR8
    0U,	// BCTRL
    0U,	// BCTRL8
    0U,	// BCTRL8_LDinto_toc
    0U,	// BCn
    0U,	// BDNZ
    0U,	// BDNZ8
    0U,	// BDNZA
    0U,	// BDNZAm
    0U,	// BDNZAp
    0U,	// BDNZL
    0U,	// BDNZLA
    0U,	// BDNZLAm
    0U,	// BDNZLAp
    0U,	// BDNZLR
    0U,	// BDNZLR8
    0U,	// BDNZLRL
    0U,	// BDNZLRLm
    0U,	// BDNZLRLp
    0U,	// BDNZLRm
    0U,	// BDNZLRp
    0U,	// BDNZLm
    0U,	// BDNZLp
    0U,	// BDNZm
    0U,	// BDNZp
    0U,	// BDZ
    0U,	// BDZ8
    0U,	// BDZA
    0U,	// BDZAm
    0U,	// BDZAp
    0U,	// BDZL
    0U,	// BDZLA
    0U,	// BDZLAm
    0U,	// BDZLAp
    0U,	// BDZLR
    0U,	// BDZLR8
    0U,	// BDZLRL
    0U,	// BDZLRLm
    0U,	// BDZLRLp
    0U,	// BDZLRm
    0U,	// BDZLRp
    0U,	// BDZLm
    0U,	// BDZLp
    0U,	// BDZm
    0U,	// BDZp
    0U,	// BL
    0U,	// BL8
    0U,	// BL8_NOP
    0U,	// BL8_NOP_TLS
    0U,	// BL8_TLS
    0U,	// BL8_TLS_
    0U,	// BLA
    0U,	// BLA8
    0U,	// BLA8_NOP
    0U,	// BLR
    0U,	// BLR8
    0U,	// BLRL
    0U,	// BL_TLS
    0U,	// BRINC
    19U,	// CLRLSLDI
    19U,	// CLRLSLDIo
    52U,	// CLRLSLWI
    52U,	// CLRLSLWIo
    3U,	// CLRRDI
    3U,	// CLRRDIo
    4U,	// CLRRWI
    4U,	// CLRRWIo
    0U,	// CMPB
    0U,	// CMPB8
    0U,	// CMPD
    1U,	// CMPDI
    0U,	// CMPLD
    2U,	// CMPLDI
    0U,	// CMPLW
    2U,	// CMPLWI
    0U,	// CMPW
    1U,	// CMPWI
    0U,	// CNTLZD
    0U,	// CNTLZDo
    0U,	// CNTLZW
    0U,	// CNTLZW8
    0U,	// CNTLZW8o
    0U,	// CNTLZWo
    0U,	// CR6SET
    0U,	// CR6UNSET
    0U,	// CRAND
    0U,	// CRANDC
    0U,	// CREQV
    0U,	// CRNAND
    0U,	// CRNOR
    0U,	// CROR
    0U,	// CRORC
    5U,	// CRSET
    5U,	// CRUNSET
    0U,	// CRXOR
    0U,	// DCBA
    0U,	// DCBF
    0U,	// DCBI
    0U,	// DCBST
    0U,	// DCBT
    0U,	// DCBTST
    0U,	// DCBZ
    0U,	// DCBZL
    0U,	// DCCCI
    0U,	// DIVD
    0U,	// DIVDU
    0U,	// DIVDUo
    0U,	// DIVDo
    0U,	// DIVW
    0U,	// DIVWU
    0U,	// DIVWUo
    0U,	// DIVWo
    0U,	// DSS
    0U,	// DSSALL
    0U,	// DST
    0U,	// DST64
    0U,	// DSTST
    0U,	// DSTST64
    0U,	// DSTSTT
    0U,	// DSTSTT64
    0U,	// DSTT
    0U,	// DSTT64
    0U,	// DYNALLOC
    0U,	// DYNALLOC8
    0U,	// EH_SjLj_LongJmp32
    0U,	// EH_SjLj_LongJmp64
    0U,	// EH_SjLj_SetJmp32
    0U,	// EH_SjLj_SetJmp64
    0U,	// EH_SjLj_Setup
    0U,	// EQV
    0U,	// EQV8
    0U,	// EQV8o
    0U,	// EQVo
    0U,	// EVABS
    0U,	// EVADDIW
    0U,	// EVADDSMIAAW
    0U,	// EVADDSSIAAW
    0U,	// EVADDUMIAAW
    0U,	// EVADDUSIAAW
    0U,	// EVADDW
    0U,	// EVAND
    0U,	// EVANDC
    0U,	// EVCMPEQ
    0U,	// EVCMPGTS
    0U,	// EVCMPGTU
    0U,	// EVCMPLTS
    0U,	// EVCMPLTU
    0U,	// EVCNTLSW
    0U,	// EVCNTLZW
    0U,	// EVDIVWS
    0U,	// EVDIVWU
    0U,	// EVEQV
    0U,	// EVEXTSB
    0U,	// EVEXTSH
    0U,	// EVLDD
    0U,	// EVLDDX
    0U,	// EVLDH
    0U,	// EVLDHX
    0U,	// EVLDW
    0U,	// EVLDWX
    0U,	// EVLHHESPLAT
    0U,	// EVLHHESPLATX
    0U,	// EVLHHOSSPLAT
    0U,	// EVLHHOSSPLATX
    0U,	// EVLHHOUSPLAT
    0U,	// EVLHHOUSPLATX
    0U,	// EVLWHE
    0U,	// EVLWHEX
    0U,	// EVLWHOS
    0U,	// EVLWHOSX
    0U,	// EVLWHOU
    0U,	// EVLWHOUX
    0U,	// EVLWHSPLAT
    0U,	// EVLWHSPLATX
    0U,	// EVLWWSPLAT
    0U,	// EVLWWSPLATX
    0U,	// EVMERGEHI
    0U,	// EVMERGEHILO
    0U,	// EVMERGELO
    0U,	// EVMERGELOHI
    0U,	// EVMHEGSMFAA
    0U,	// EVMHEGSMFAN
    0U,	// EVMHEGSMIAA
    0U,	// EVMHEGSMIAN
    0U,	// EVMHEGUMIAA
    0U,	// EVMHEGUMIAN
    0U,	// EVMHESMF
    0U,	// EVMHESMFA
    0U,	// EVMHESMFAAW
    0U,	// EVMHESMFANW
    0U,	// EVMHESMI
    0U,	// EVMHESMIA
    0U,	// EVMHESMIAAW
    0U,	// EVMHESMIANW
    0U,	// EVMHESSF
    0U,	// EVMHESSFA
    0U,	// EVMHESSFAAW
    0U,	// EVMHESSFANW
    0U,	// EVMHESSIAAW
    0U,	// EVMHESSIANW
    0U,	// EVMHEUMI
    0U,	// EVMHEUMIA
    0U,	// EVMHEUMIAAW
    0U,	// EVMHEUMIANW
    0U,	// EVMHEUSIAAW
    0U,	// EVMHEUSIANW
    0U,	// EVMHOGSMFAA
    0U,	// EVMHOGSMFAN
    0U,	// EVMHOGSMIAA
    0U,	// EVMHOGSMIAN
    0U,	// EVMHOGUMIAA
    0U,	// EVMHOGUMIAN
    0U,	// EVMHOSMF
    0U,	// EVMHOSMFA
    0U,	// EVMHOSMFAAW
    0U,	// EVMHOSMFANW
    0U,	// EVMHOSMI
    0U,	// EVMHOSMIA
    0U,	// EVMHOSMIAAW
    0U,	// EVMHOSMIANW
    0U,	// EVMHOSSF
    0U,	// EVMHOSSFA
    0U,	// EVMHOSSFAAW
    0U,	// EVMHOSSFANW
    0U,	// EVMHOSSIAAW
    0U,	// EVMHOSSIANW
    0U,	// EVMHOUMI
    0U,	// EVMHOUMIA
    0U,	// EVMHOUMIAAW
    0U,	// EVMHOUMIANW
    0U,	// EVMHOUSIAAW
    0U,	// EVMHOUSIANW
    0U,	// EVMRA
    0U,	// EVMWHSMF
    0U,	// EVMWHSMFA
    0U,	// EVMWHSMI
    0U,	// EVMWHSMIA
    0U,	// EVMWHSSF
    0U,	// EVMWHSSFA
    0U,	// EVMWHUMI
    0U,	// EVMWHUMIA
    0U,	// EVMWLSMIAAW
    0U,	// EVMWLSMIANW
    0U,	// EVMWLSSIAAW
    0U,	// EVMWLSSIANW
    0U,	// EVMWLUMI
    0U,	// EVMWLUMIA
    0U,	// EVMWLUMIAAW
    0U,	// EVMWLUMIANW
    0U,	// EVMWLUSIAAW
    0U,	// EVMWLUSIANW
    0U,	// EVMWSMF
    0U,	// EVMWSMFA
    0U,	// EVMWSMFAA
    0U,	// EVMWSMFAN
    0U,	// EVMWSMI
    0U,	// EVMWSMIA
    0U,	// EVMWSMIAA
    0U,	// EVMWSMIAN
    0U,	// EVMWSSF
    0U,	// EVMWSSFA
    0U,	// EVMWSSFAA
    0U,	// EVMWSSFAN
    0U,	// EVMWUMI
    0U,	// EVMWUMIA
    0U,	// EVMWUMIAA
    0U,	// EVMWUMIAN
    0U,	// EVNAND
    0U,	// EVNEG
    0U,	// EVNOR
    0U,	// EVOR
    0U,	// EVORC
    0U,	// EVRLW
    4U,	// EVRLWI
    0U,	// EVRNDW
    0U,	// EVSLW
    4U,	// EVSLWI
    0U,	// EVSPLATFI
    0U,	// EVSPLATI
    4U,	// EVSRWIS
    4U,	// EVSRWIU
    0U,	// EVSRWS
    0U,	// EVSRWU
    0U,	// EVSTDD
    0U,	// EVSTDDX
    0U,	// EVSTDH
    0U,	// EVSTDHX
    0U,	// EVSTDW
    0U,	// EVSTDWX
    0U,	// EVSTWHE
    0U,	// EVSTWHEX
    0U,	// EVSTWHO
    0U,	// EVSTWHOX
    0U,	// EVSTWWE
    0U,	// EVSTWWEX
    0U,	// EVSTWWO
    0U,	// EVSTWWOX
    0U,	// EVSUBFSMIAAW
    0U,	// EVSUBFSSIAAW
    0U,	// EVSUBFUMIAAW
    0U,	// EVSUBFUSIAAW
    0U,	// EVSUBFW
    0U,	// EVSUBIFW
    0U,	// EVXOR
    19U,	// EXTLDI
    19U,	// EXTLDIo
    52U,	// EXTLWI
    52U,	// EXTLWIo
    19U,	// EXTRDI
    19U,	// EXTRDIo
    52U,	// EXTRWI
    52U,	// EXTRWIo
    0U,	// EXTSB
    0U,	// EXTSB8
    0U,	// EXTSB8_32_64
    0U,	// EXTSB8o
    0U,	// EXTSBo
    0U,	// EXTSH
    0U,	// EXTSH8
    0U,	// EXTSH8_32_64
    0U,	// EXTSH8o
    0U,	// EXTSHo
    0U,	// EXTSW
    0U,	// EXTSW_32_64
    0U,	// EXTSW_32_64o
    0U,	// EXTSWo
    0U,	// EnforceIEIO
    0U,	// FABSD
    0U,	// FABSDo
    0U,	// FABSS
    0U,	// FABSSo
    0U,	// FADD
    0U,	// FADDS
    0U,	// FADDSo
    0U,	// FADDo
    0U,	// FADDrtz
    0U,	// FCFID
    0U,	// FCFIDS
    0U,	// FCFIDSo
    0U,	// FCFIDU
    0U,	// FCFIDUS
    0U,	// FCFIDUSo
    0U,	// FCFIDUo
    0U,	// FCFIDo
    0U,	// FCMPUD
    0U,	// FCMPUS
    0U,	// FCPSGND
    0U,	// FCPSGNDo
    0U,	// FCPSGNS
    0U,	// FCPSGNSo
    0U,	// FCTID
    0U,	// FCTIDUZ
    0U,	// FCTIDUZo
    0U,	// FCTIDZ
    0U,	// FCTIDZo
    0U,	// FCTIDo
    0U,	// FCTIW
    0U,	// FCTIWUZ
    0U,	// FCTIWUZo
    0U,	// FCTIWZ
    0U,	// FCTIWZo
    0U,	// FCTIWo
    0U,	// FDIV
    0U,	// FDIVS
    0U,	// FDIVSo
    0U,	// FDIVo
    80U,	// FMADD
    80U,	// FMADDS
    80U,	// FMADDSo
    80U,	// FMADDo
    0U,	// FMR
    0U,	// FMRo
    80U,	// FMSUB
    80U,	// FMSUBS
    80U,	// FMSUBSo
    80U,	// FMSUBo
    0U,	// FMUL
    0U,	// FMULS
    0U,	// FMULSo
    0U,	// FMULo
    0U,	// FNABSD
    0U,	// FNABSDo
    0U,	// FNABSS
    0U,	// FNABSSo
    0U,	// FNEGD
    0U,	// FNEGDo
    0U,	// FNEGS
    0U,	// FNEGSo
    80U,	// FNMADD
    80U,	// FNMADDS
    80U,	// FNMADDSo
    80U,	// FNMADDo
    80U,	// FNMSUB
    80U,	// FNMSUBS
    80U,	// FNMSUBSo
    80U,	// FNMSUBo
    0U,	// FRE
    0U,	// FRES
    0U,	// FRESo
    0U,	// FREo
    0U,	// FRIMD
    0U,	// FRIMDo
    0U,	// FRIMS
    0U,	// FRIMSo
    0U,	// FRIND
    0U,	// FRINDo
    0U,	// FRINS
    0U,	// FRINSo
    0U,	// FRIPD
    0U,	// FRIPDo
    0U,	// FRIPS
    0U,	// FRIPSo
    0U,	// FRIZD
    0U,	// FRIZDo
    0U,	// FRIZS
    0U,	// FRIZSo
    0U,	// FRSP
    0U,	// FRSPo
    0U,	// FRSQRTE
    0U,	// FRSQRTES
    0U,	// FRSQRTESo
    0U,	// FRSQRTEo
    80U,	// FSELD
    80U,	// FSELDo
    80U,	// FSELS
    80U,	// FSELSo
    0U,	// FSQRT
    0U,	// FSQRTS
    0U,	// FSQRTSo
    0U,	// FSQRTo
    0U,	// FSUB
    0U,	// FSUBS
    0U,	// FSUBSo
    0U,	// FSUBo
    0U,	// GETtlsADDR
    0U,	// GETtlsADDR32
    0U,	// GETtlsldADDR
    0U,	// GETtlsldADDR32
    0U,	// ICBI
    0U,	// ICBT
    0U,	// ICCCI
    52U,	// INSLWI
    52U,	// INSLWIo
    19U,	// INSRDI
    19U,	// INSRDIo
    52U,	// INSRWI
    52U,	// INSRWIo
    80U,	// ISEL
    80U,	// ISEL8
    0U,	// ISYNC
    0U,	// LA
    0U,	// LAx
    0U,	// LBZ
    0U,	// LBZ8
    0U,	// LBZCIX
    0U,	// LBZU
    0U,	// LBZU8
    0U,	// LBZUX
    0U,	// LBZUX8
    0U,	// LBZX
    0U,	// LBZX8
    0U,	// LD
    0U,	// LDARX
    0U,	// LDBRX
    0U,	// LDCIX
    0U,	// LDU
    0U,	// LDUX
    0U,	// LDX
    0U,	// LDgotTprelL
    0U,	// LDgotTprelL32
    0U,	// LDtoc
    0U,	// LDtocBA
    0U,	// LDtocCPT
    0U,	// LDtocJTI
    0U,	// LDtocL
    0U,	// LFD
    0U,	// LFDU
    0U,	// LFDUX
    0U,	// LFDX
    0U,	// LFIWAX
    0U,	// LFIWZX
    0U,	// LFS
    0U,	// LFSU
    0U,	// LFSUX
    0U,	// LFSX
    0U,	// LHA
    0U,	// LHA8
    0U,	// LHAU
    0U,	// LHAU8
    0U,	// LHAUX
    0U,	// LHAUX8
    0U,	// LHAX
    0U,	// LHAX8
    0U,	// LHBRX
    0U,	// LHBRX8
    0U,	// LHZ
    0U,	// LHZ8
    0U,	// LHZCIX
    0U,	// LHZU
    0U,	// LHZU8
    0U,	// LHZUX
    0U,	// LHZUX8
    0U,	// LHZX
    0U,	// LHZX8
    0U,	// LI
    0U,	// LI8
    0U,	// LIS
    0U,	// LIS8
    0U,	// LMW
    4U,	// LSWI
    0U,	// LVEBX
    0U,	// LVEHX
    0U,	// LVEWX
    0U,	// LVSL
    0U,	// LVSR
    0U,	// LVX
    0U,	// LVXL
    0U,	// LWA
    0U,	// LWARX
    0U,	// LWAUX
    0U,	// LWAX
    0U,	// LWAX_32
    0U,	// LWA_32
    0U,	// LWBRX
    0U,	// LWBRX8
    0U,	// LWZ
    0U,	// LWZ8
    0U,	// LWZCIX
    0U,	// LWZU
    0U,	// LWZU8
    0U,	// LWZUX
    0U,	// LWZUX8
    0U,	// LWZX
    0U,	// LWZX8
    0U,	// LWZtoc
    0U,	// LXSDX
    0U,	// LXVD2X
    0U,	// LXVDSX
    0U,	// LXVW4X
    0U,	// MBAR
    0U,	// MCRF
    0U,	// MCRFS
    0U,	// MFCR
    0U,	// MFCR8
    0U,	// MFCTR
    0U,	// MFCTR8
    0U,	// MFDCR
    0U,	// MFFS
    0U,	// MFFSo
    0U,	// MFLR
    0U,	// MFLR8
    0U,	// MFMSR
    0U,	// MFOCRF
    0U,	// MFOCRF8
    0U,	// MFSPR
    0U,	// MFSR
    0U,	// MFSRIN
    0U,	// MFTB
    0U,	// MFTB8
    0U,	// MFVRSAVE
    0U,	// MFVRSAVEv
    0U,	// MFVSCR
    0U,	// MSYNC
    0U,	// MTCRF
    0U,	// MTCRF8
    0U,	// MTCTR
    0U,	// MTCTR8
    0U,	// MTCTR8loop
    0U,	// MTCTRloop
    0U,	// MTDCR
    0U,	// MTFSB0
    0U,	// MTFSB1
    80U,	// MTFSF
    0U,	// MTFSFI
    0U,	// MTFSFIo
    0U,	// MTFSFb
    80U,	// MTFSFo
    0U,	// MTLR
    0U,	// MTLR8
    0U,	// MTMSR
    0U,	// MTMSRD
    0U,	// MTOCRF
    0U,	// MTOCRF8
    0U,	// MTSPR
    0U,	// MTSR
    0U,	// MTSRIN
    0U,	// MTVRSAVE
    0U,	// MTVRSAVEv
    0U,	// MTVSCR
    0U,	// MULHD
    0U,	// MULHDU
    0U,	// MULHDUo
    0U,	// MULHDo
    0U,	// MULHW
    0U,	// MULHWU
    0U,	// MULHWUo
    0U,	// MULHWo
    0U,	// MULLD
    0U,	// MULLDo
    1U,	// MULLI
    1U,	// MULLI8
    0U,	// MULLW
    0U,	// MULLWo
    0U,	// MoveGOTtoLR
    0U,	// MovePCtoLR
    0U,	// MovePCtoLR8
    0U,	// NAND
    0U,	// NAND8
    0U,	// NAND8o
    0U,	// NANDo
    0U,	// NEG
    0U,	// NEG8
    0U,	// NEG8o
    0U,	// NEGo
    0U,	// NOP
    0U,	// NOP_GT_PWR6
    0U,	// NOP_GT_PWR7
    0U,	// NOR
    0U,	// NOR8
    0U,	// NOR8o
    0U,	// NORo
    0U,	// OR
    0U,	// OR8
    0U,	// OR8o
    0U,	// ORC
    0U,	// ORC8
    0U,	// ORC8o
    0U,	// ORCo
    2U,	// ORI
    2U,	// ORI8
    2U,	// ORIS
    2U,	// ORIS8
    0U,	// ORo
    0U,	// POPCNTD
    0U,	// POPCNTW
    0U,	// PPC32GOT
    0U,	// PPC32PICGOT
    112U,	// QVALIGNI
    112U,	// QVALIGNIb
    112U,	// QVALIGNIs
    6U,	// QVESPLATI
    6U,	// QVESPLATIb
    6U,	// QVESPLATIs
    0U,	// QVFABS
    0U,	// QVFABSs
    0U,	// QVFADD
    0U,	// QVFADDS
    0U,	// QVFADDSs
    0U,	// QVFCFID
    0U,	// QVFCFIDS
    0U,	// QVFCFIDU
    0U,	// QVFCFIDUS
    0U,	// QVFCFIDb
    0U,	// QVFCMPEQ
    0U,	// QVFCMPEQb
    0U,	// QVFCMPEQbs
    0U,	// QVFCMPGT
    0U,	// QVFCMPGTb
    0U,	// QVFCMPGTbs
    0U,	// QVFCMPLT
    0U,	// QVFCMPLTb
    0U,	// QVFCMPLTbs
    0U,	// QVFCPSGN
    0U,	// QVFCPSGNs
    0U,	// QVFCTID
    0U,	// QVFCTIDU
    0U,	// QVFCTIDUZ
    0U,	// QVFCTIDZ
    0U,	// QVFCTIDb
    0U,	// QVFCTIW
    0U,	// QVFCTIWU
    0U,	// QVFCTIWUZ
    0U,	// QVFCTIWZ
    144U,	// QVFLOGICAL
    144U,	// QVFLOGICALb
    144U,	// QVFLOGICALs
    7U,	// QVFMADD
    7U,	// QVFMADDS
    7U,	// QVFMADDSs
    0U,	// QVFMR
    0U,	// QVFMRb
    0U,	// QVFMRs
    7U,	// QVFMSUB
    7U,	// QVFMSUBS
    7U,	// QVFMSUBSs
    0U,	// QVFMUL
    0U,	// QVFMULS
    0U,	// QVFMULSs
    0U,	// QVFNABS
    0U,	// QVFNABSs
    0U,	// QVFNEG
    0U,	// QVFNEGs
    7U,	// QVFNMADD
    7U,	// QVFNMADDS
    7U,	// QVFNMADDSs
    7U,	// QVFNMSUB
    7U,	// QVFNMSUBS
    7U,	// QVFNMSUBSs
    80U,	// QVFPERM
    80U,	// QVFPERMs
    0U,	// QVFRE
    0U,	// QVFRES
    0U,	// QVFRESs
    0U,	// QVFRIM
    0U,	// QVFRIMs
    0U,	// QVFRIN
    0U,	// QVFRINs
    0U,	// QVFRIP
    0U,	// QVFRIPs
    0U,	// QVFRIZ
    0U,	// QVFRIZs
    0U,	// QVFRSP
    0U,	// QVFRSPs
    0U,	// QVFRSQRTE
    0U,	// QVFRSQRTES
    0U,	// QVFRSQRTESs
    7U,	// QVFSEL
    7U,	// QVFSELb
    7U,	// QVFSELbb
    7U,	// QVFSELbs
    0U,	// QVFSUB
    0U,	// QVFSUBS
    0U,	// QVFSUBSs
    0U,	// QVFTSTNAN
    0U,	// QVFTSTNANb
    0U,	// QVFTSTNANbs
    7U,	// QVFXMADD
    7U,	// QVFXMADDS
    0U,	// QVFXMUL
    0U,	// QVFXMULS
    7U,	// QVFXXCPNMADD
    7U,	// QVFXXCPNMADDS
    7U,	// QVFXXMADD
    7U,	// QVFXXMADDS
    7U,	// QVFXXNPMADD
    7U,	// QVFXXNPMADDS
    0U,	// QVGPCI
    0U,	// QVLFCDUX
    0U,	// QVLFCDUXA
    0U,	// QVLFCDX
    0U,	// QVLFCDXA
    0U,	// QVLFCSUX
    0U,	// QVLFCSUXA
    0U,	// QVLFCSX
    0U,	// QVLFCSXA
    0U,	// QVLFCSXs
    0U,	// QVLFDUX
    0U,	// QVLFDUXA
    0U,	// QVLFDX
    0U,	// QVLFDXA
    0U,	// QVLFDXb
    0U,	// QVLFIWAX
    0U,	// QVLFIWAXA
    0U,	// QVLFIWZX
    0U,	// QVLFIWZXA
    0U,	// QVLFSUX
    0U,	// QVLFSUXA
    0U,	// QVLFSX
    0U,	// QVLFSXA
    0U,	// QVLFSXb
    0U,	// QVLFSXs
    0U,	// QVLPCLDX
    0U,	// QVLPCLSX
    0U,	// QVLPCLSXint
    0U,	// QVLPCRDX
    0U,	// QVLPCRSX
    0U,	// QVSTFCDUX
    0U,	// QVSTFCDUXA
    0U,	// QVSTFCDUXI
    0U,	// QVSTFCDUXIA
    0U,	// QVSTFCDX
    0U,	// QVSTFCDXA
    0U,	// QVSTFCDXI
    0U,	// QVSTFCDXIA
    0U,	// QVSTFCSUX
    0U,	// QVSTFCSUXA
    0U,	// QVSTFCSUXI
    0U,	// QVSTFCSUXIA
    0U,	// QVSTFCSX
    0U,	// QVSTFCSXA
    0U,	// QVSTFCSXI
    0U,	// QVSTFCSXIA
    0U,	// QVSTFCSXs
    0U,	// QVSTFDUX
    0U,	// QVSTFDUXA
    0U,	// QVSTFDUXI
    0U,	// QVSTFDUXIA
    0U,	// QVSTFDX
    0U,	// QVSTFDXA
    0U,	// QVSTFDXI
    0U,	// QVSTFDXIA
    0U,	// QVSTFDXb
    0U,	// QVSTFIWX
    0U,	// QVSTFIWXA
    0U,	// QVSTFSUX
    0U,	// QVSTFSUXA
    0U,	// QVSTFSUXI
    0U,	// QVSTFSUXIA
    0U,	// QVSTFSUXs
    0U,	// QVSTFSX
    0U,	// QVSTFSXA
    0U,	// QVSTFSXI
    0U,	// QVSTFSXIA
    0U,	// QVSTFSXs
    0U,	// RESTORE_CR
    0U,	// RESTORE_CRBIT
    0U,	// RESTORE_VRSAVE
    0U,	// RFCI
    0U,	// RFDI
    0U,	// RFI
    0U,	// RFID
    0U,	// RFMCI
    16U,	// RLDCL
    16U,	// RLDCLo
    16U,	// RLDCR
    16U,	// RLDCRo
    19U,	// RLDIC
    19U,	// RLDICL
    19U,	// RLDICL_32_64
    19U,	// RLDICLo
    19U,	// RLDICR
    19U,	// RLDICRo
    19U,	// RLDICo
    0U,	// RLDIMI
    0U,	// RLDIMIo
    0U,	// RLWIMI
    0U,	// RLWIMI8
    0U,	// RLWIMI8o
    0U,	// RLWIMIo
    308U,	// RLWINM
    308U,	// RLWINM8
    308U,	// RLWINM8o
    308U,	// RLWINMo
    304U,	// RLWNM
    304U,	// RLWNM8
    304U,	// RLWNM8o
    304U,	// RLWNMo
    3U,	// ROTRDI
    3U,	// ROTRDIo
    4U,	// ROTRWI
    4U,	// ROTRWIo
    0U,	// ReadTB
    0U,	// SC
    0U,	// SELECT_CC_F4
    0U,	// SELECT_CC_F8
    0U,	// SELECT_CC_I4
    0U,	// SELECT_CC_I8
    0U,	// SELECT_CC_QBRC
    0U,	// SELECT_CC_QFRC
    0U,	// SELECT_CC_QSRC
    0U,	// SELECT_CC_VRRC
    0U,	// SELECT_CC_VSFRC
    0U,	// SELECT_CC_VSRC
    0U,	// SELECT_F4
    0U,	// SELECT_F8
    0U,	// SELECT_I4
    0U,	// SELECT_I8
    0U,	// SELECT_QBRC
    0U,	// SELECT_QFRC
    0U,	// SELECT_QSRC
    0U,	// SELECT_VRRC
    0U,	// SELECT_VSFRC
    0U,	// SELECT_VSRC
    0U,	// SLBIA
    0U,	// SLBIE
    0U,	// SLBMFEE
    0U,	// SLBMTE
    0U,	// SLD
    3U,	// SLDI
    3U,	// SLDIo
    0U,	// SLDo
    0U,	// SLW
    0U,	// SLW8
    0U,	// SLW8o
    4U,	// SLWI
    4U,	// SLWIo
    0U,	// SLWo
    0U,	// SPILL_CR
    0U,	// SPILL_CRBIT
    0U,	// SPILL_VRSAVE
    0U,	// SRAD
    3U,	// SRADI
    3U,	// SRADIo
    0U,	// SRADo
    0U,	// SRAW
    4U,	// SRAWI
    4U,	// SRAWIo
    0U,	// SRAWo
    0U,	// SRD
    3U,	// SRDI
    3U,	// SRDIo
    0U,	// SRDo
    0U,	// SRW
    0U,	// SRW8
    0U,	// SRW8o
    4U,	// SRWI
    4U,	// SRWIo
    0U,	// SRWo
    0U,	// STB
    0U,	// STB8
    0U,	// STBCIX
    0U,	// STBU
    0U,	// STBU8
    0U,	// STBUX
    0U,	// STBUX8
    0U,	// STBX
    0U,	// STBX8
    0U,	// STD
    0U,	// STDBRX
    0U,	// STDCIX
    0U,	// STDCX
    0U,	// STDU
    0U,	// STDUX
    0U,	// STDX
    0U,	// STFD
    0U,	// STFDU
    0U,	// STFDUX
    0U,	// STFDX
    0U,	// STFIWX
    0U,	// STFS
    0U,	// STFSU
    0U,	// STFSUX
    0U,	// STFSX
    0U,	// STH
    0U,	// STH8
    0U,	// STHBRX
    0U,	// STHCIX
    0U,	// STHU
    0U,	// STHU8
    0U,	// STHUX
    0U,	// STHUX8
    0U,	// STHX
    0U,	// STHX8
    0U,	// STMW
    4U,	// STSWI
    0U,	// STVEBX
    0U,	// STVEHX
    0U,	// STVEWX
    0U,	// STVX
    0U,	// STVXL
    0U,	// STW
    0U,	// STW8
    0U,	// STWBRX
    0U,	// STWCIX
    0U,	// STWCX
    0U,	// STWU
    0U,	// STWU8
    0U,	// STWUX
    0U,	// STWUX8
    0U,	// STWX
    0U,	// STWX8
    0U,	// STXSDX
    0U,	// STXVD2X
    0U,	// STXVW4X
    0U,	// SUBF
    0U,	// SUBF8
    0U,	// SUBF8o
    0U,	// SUBFC
    0U,	// SUBFC8
    0U,	// SUBFC8o
    0U,	// SUBFCo
    0U,	// SUBFE
    0U,	// SUBFE8
    0U,	// SUBFE8o
    0U,	// SUBFEo
    1U,	// SUBFIC
    1U,	// SUBFIC8
    0U,	// SUBFME
    0U,	// SUBFME8
    0U,	// SUBFME8o
    0U,	// SUBFMEo
    0U,	// SUBFZE
    0U,	// SUBFZE8
    0U,	// SUBFZE8o
    0U,	// SUBFZEo
    0U,	// SUBFo
    1U,	// SUBI
    1U,	// SUBIC
    1U,	// SUBICo
    1U,	// SUBIS
    0U,	// SYNC
    0U,	// TAILB
    0U,	// TAILB8
    0U,	// TAILBA
    0U,	// TAILBA8
    0U,	// TAILBCTR
    0U,	// TAILBCTR8
    0U,	// TCRETURNai
    0U,	// TCRETURNai8
    0U,	// TCRETURNdi
    0U,	// TCRETURNdi8
    0U,	// TCRETURNri
    0U,	// TCRETURNri8
    0U,	// TD
    1U,	// TDI
    0U,	// TLBIA
    0U,	// TLBIE
    0U,	// TLBIEL
    0U,	// TLBIVAX
    0U,	// TLBLD
    0U,	// TLBLI
    0U,	// TLBRE
    0U,	// TLBRE2
    0U,	// TLBSX
    0U,	// TLBSX2
    0U,	// TLBSX2D
    0U,	// TLBSYNC
    0U,	// TLBWE
    0U,	// TLBWE2
    0U,	// TRAP
    0U,	// TW
    1U,	// TWI
    0U,	// UPDATE_VRSAVE
    0U,	// UpdateGBR
    0U,	// VADDCUW
    0U,	// VADDFP
    0U,	// VADDSBS
    0U,	// VADDSHS
    0U,	// VADDSWS
    0U,	// VADDUBM
    0U,	// VADDUBS
    0U,	// VADDUDM
    0U,	// VADDUHM
    0U,	// VADDUHS
    0U,	// VADDUWM
    0U,	// VADDUWS
    0U,	// VAND
    0U,	// VANDC
    0U,	// VAVGSB
    0U,	// VAVGSH
    0U,	// VAVGSW
    0U,	// VAVGUB
    0U,	// VAVGUH
    0U,	// VAVGUW
    0U,	// VCFSX
    0U,	// VCFSX_0
    0U,	// VCFUX
    0U,	// VCFUX_0
    0U,	// VCLZB
    0U,	// VCLZD
    0U,	// VCLZH
    0U,	// VCLZW
    0U,	// VCMPBFP
    0U,	// VCMPBFPo
    0U,	// VCMPEQFP
    0U,	// VCMPEQFPo
    0U,	// VCMPEQUB
    0U,	// VCMPEQUBo
    0U,	// VCMPEQUD
    0U,	// VCMPEQUDo
    0U,	// VCMPEQUH
    0U,	// VCMPEQUHo
    0U,	// VCMPEQUW
    0U,	// VCMPEQUWo
    0U,	// VCMPGEFP
    0U,	// VCMPGEFPo
    0U,	// VCMPGTFP
    0U,	// VCMPGTFPo
    0U,	// VCMPGTSB
    0U,	// VCMPGTSBo
    0U,	// VCMPGTSD
    0U,	// VCMPGTSDo
    0U,	// VCMPGTSH
    0U,	// VCMPGTSHo
    0U,	// VCMPGTSW
    0U,	// VCMPGTSWo
    0U,	// VCMPGTUB
    0U,	// VCMPGTUBo
    0U,	// VCMPGTUD
    0U,	// VCMPGTUDo
    0U,	// VCMPGTUH
    0U,	// VCMPGTUHo
    0U,	// VCMPGTUW
    0U,	// VCMPGTUWo
    0U,	// VCTSXS
    0U,	// VCTSXS_0
    0U,	// VCTUXS
    0U,	// VCTUXS_0
    0U,	// VEQV
    0U,	// VEXPTEFP
    0U,	// VLOGEFP
    80U,	// VMADDFP
    0U,	// VMAXFP
    0U,	// VMAXSB
    0U,	// VMAXSD
    0U,	// VMAXSH
    0U,	// VMAXSW
    0U,	// VMAXUB
    0U,	// VMAXUD
    0U,	// VMAXUH
    0U,	// VMAXUW
    80U,	// VMHADDSHS
    80U,	// VMHRADDSHS
    0U,	// VMIDUD
    0U,	// VMINFP
    0U,	// VMINSB
    0U,	// VMINSD
    0U,	// VMINSH
    0U,	// VMINSW
    0U,	// VMINUB
    0U,	// VMINUH
    0U,	// VMINUW
    80U,	// VMLADDUHM
    0U,	// VMRGHB
    0U,	// VMRGHH
    0U,	// VMRGHW
    0U,	// VMRGLB
    0U,	// VMRGLH
    0U,	// VMRGLW
    80U,	// VMSUMMBM
    80U,	// VMSUMSHM
    80U,	// VMSUMSHS
    80U,	// VMSUMUBM
    80U,	// VMSUMUHM
    80U,	// VMSUMUHS
    0U,	// VMULESB
    0U,	// VMULESH
    0U,	// VMULESW
    0U,	// VMULEUB
    0U,	// VMULEUH
    0U,	// VMULEUW
    0U,	// VMULOSB
    0U,	// VMULOSH
    0U,	// VMULOSW
    0U,	// VMULOUB
    0U,	// VMULOUH
    0U,	// VMULOUW
    0U,	// VMULUWM
    0U,	// VNAND
    80U,	// VNMSUBFP
    0U,	// VNOR
    0U,	// VOR
    0U,	// VORC
    80U,	// VPERM
    0U,	// VPKPX
    0U,	// VPKSHSS
    0U,	// VPKSHUS
    0U,	// VPKSWSS
    0U,	// VPKSWUS
    0U,	// VPKUHUM
    0U,	// VPKUHUS
    0U,	// VPKUWUM
    0U,	// VPKUWUS
    0U,	// VPOPCNTB
    0U,	// VPOPCNTD
    0U,	// VPOPCNTH
    0U,	// VPOPCNTW
    0U,	// VREFP
    0U,	// VRFIM
    0U,	// VRFIN
    0U,	// VRFIP
    0U,	// VRFIZ
    0U,	// VRLB
    0U,	// VRLD
    0U,	// VRLH
    0U,	// VRLW
    0U,	// VRSQRTEFP
    80U,	// VSEL
    0U,	// VSL
    0U,	// VSLB
    0U,	// VSLD
    48U,	// VSLDOI
    0U,	// VSLH
    0U,	// VSLO
    0U,	// VSLW
    0U,	// VSPLTB
    0U,	// VSPLTH
    0U,	// VSPLTISB
    0U,	// VSPLTISH
    0U,	// VSPLTISW
    0U,	// VSPLTW
    0U,	// VSR
    0U,	// VSRAB
    0U,	// VSRAD
    0U,	// VSRAH
    0U,	// VSRAW
    0U,	// VSRB
    0U,	// VSRD
    0U,	// VSRH
    0U,	// VSRO
    0U,	// VSRW
    0U,	// VSUBCUW
    0U,	// VSUBFP
    0U,	// VSUBSBS
    0U,	// VSUBSHS
    0U,	// VSUBSWS
    0U,	// VSUBUBM
    0U,	// VSUBUBS
    0U,	// VSUBUDM
    0U,	// VSUBUHM
    0U,	// VSUBUHS
    0U,	// VSUBUWM
    0U,	// VSUBUWS
    0U,	// VSUM2SWS
    0U,	// VSUM4SBS
    0U,	// VSUM4SHS
    0U,	// VSUM4UBS
    0U,	// VSUMSWS
    0U,	// VUPKHPX
    0U,	// VUPKHSB
    0U,	// VUPKHSH
    0U,	// VUPKLPX
    0U,	// VUPKLSB
    0U,	// VUPKLSH
    0U,	// VXOR
    5U,	// V_SET0
    5U,	// V_SET0B
    5U,	// V_SET0H
    0U,	// V_SETALLONES
    0U,	// V_SETALLONESB
    0U,	// V_SETALLONESH
    0U,	// WAIT
    0U,	// WRTEE
    0U,	// WRTEEI
    0U,	// XOR
    0U,	// XOR8
    0U,	// XOR8o
    2U,	// XORI
    2U,	// XORI8
    2U,	// XORIS
    2U,	// XORIS8
    0U,	// XORo
    0U,	// XSABSDP
    0U,	// XSADDDP
    0U,	// XSCMPODP
    0U,	// XSCMPUDP
    0U,	// XSCPSGNDP
    0U,	// XSCVDPSP
    0U,	// XSCVDPSXDS
    0U,	// XSCVDPSXWS
    0U,	// XSCVDPUXDS
    0U,	// XSCVDPUXWS
    0U,	// XSCVSPDP
    0U,	// XSCVSXDDP
    0U,	// XSCVUXDDP
    0U,	// XSDIVDP
    0U,	// XSMADDADP
    0U,	// XSMADDMDP
    0U,	// XSMAXDP
    0U,	// XSMINDP
    0U,	// XSMSUBADP
    0U,	// XSMSUBMDP
    0U,	// XSMULDP
    0U,	// XSNABSDP
    0U,	// XSNEGDP
    0U,	// XSNMADDADP
    0U,	// XSNMADDMDP
    0U,	// XSNMSUBADP
    0U,	// XSNMSUBMDP
    0U,	// XSRDPI
    0U,	// XSRDPIC
    0U,	// XSRDPIM
    0U,	// XSRDPIP
    0U,	// XSRDPIZ
    0U,	// XSREDP
    0U,	// XSRSQRTEDP
    0U,	// XSSQRTDP
    0U,	// XSSUBDP
    0U,	// XSTDIVDP
    0U,	// XSTSQRTDP
    0U,	// XVABSDP
    0U,	// XVABSSP
    0U,	// XVADDDP
    0U,	// XVADDSP
    0U,	// XVCMPEQDP
    0U,	// XVCMPEQDPo
    0U,	// XVCMPEQSP
    0U,	// XVCMPEQSPo
    0U,	// XVCMPGEDP
    0U,	// XVCMPGEDPo
    0U,	// XVCMPGESP
    0U,	// XVCMPGESPo
    0U,	// XVCMPGTDP
    0U,	// XVCMPGTDPo
    0U,	// XVCMPGTSP
    0U,	// XVCMPGTSPo
    0U,	// XVCPSGNDP
    0U,	// XVCPSGNSP
    0U,	// XVCVDPSP
    0U,	// XVCVDPSXDS
    0U,	// XVCVDPSXWS
    0U,	// XVCVDPUXDS
    0U,	// XVCVDPUXWS
    0U,	// XVCVSPDP
    0U,	// XVCVSPSXDS
    0U,	// XVCVSPSXWS
    0U,	// XVCVSPUXDS
    0U,	// XVCVSPUXWS
    0U,	// XVCVSXDDP
    0U,	// XVCVSXDSP
    0U,	// XVCVSXWDP
    0U,	// XVCVSXWSP
    0U,	// XVCVUXDDP
    0U,	// XVCVUXDSP
    0U,	// XVCVUXWDP
    0U,	// XVCVUXWSP
    0U,	// XVDIVDP
    0U,	// XVDIVSP
    0U,	// XVMADDADP
    0U,	// XVMADDASP
    0U,	// XVMADDMDP
    0U,	// XVMADDMSP
    0U,	// XVMAXDP
    0U,	// XVMAXSP
    0U,	// XVMINDP
    0U,	// XVMINSP
    0U,	// XVMSUBADP
    0U,	// XVMSUBASP
    0U,	// XVMSUBMDP
    0U,	// XVMSUBMSP
    0U,	// XVMULDP
    0U,	// XVMULSP
    0U,	// XVNABSDP
    0U,	// XVNABSSP
    0U,	// XVNEGDP
    0U,	// XVNEGSP
    0U,	// XVNMADDADP
    0U,	// XVNMADDASP
    0U,	// XVNMADDMDP
    0U,	// XVNMADDMSP
    0U,	// XVNMSUBADP
    0U,	// XVNMSUBASP
    0U,	// XVNMSUBMDP
    0U,	// XVNMSUBMSP
    0U,	// XVRDPI
    0U,	// XVRDPIC
    0U,	// XVRDPIM
    0U,	// XVRDPIP
    0U,	// XVRDPIZ
    0U,	// XVREDP
    0U,	// XVRESP
    0U,	// XVRSPI
    0U,	// XVRSPIC
    0U,	// XVRSPIM
    0U,	// XVRSPIP
    0U,	// XVRSPIZ
    0U,	// XVRSQRTEDP
    0U,	// XVRSQRTESP
    0U,	// XVSQRTDP
    0U,	// XVSQRTSP
    0U,	// XVSUBDP
    0U,	// XVSUBSP
    0U,	// XVTDIVDP
    0U,	// XVTDIVSP
    0U,	// XVTSQRTDP
    0U,	// XVTSQRTSP
    0U,	// XXLAND
    0U,	// XXLANDC
    0U,	// XXLEQV
    0U,	// XXLNAND
    0U,	// XXLNOR
    0U,	// XXLOR
    0U,	// XXLORC
    0U,	// XXLORf
    0U,	// XXLXOR
    0U,	// XXMRGHW
    0U,	// XXMRGLW
    112U,	// XXPERMDI
    80U,	// XXSEL
    112U,	// XXSLDWI
    6U,	// XXSPLTW
    8U,	// gBC
    9U,	// gBCA
    0U,	// gBCCTR
    0U,	// gBCCTRL
    8U,	// gBCL
    9U,	// gBCLA
    0U,	// gBCLR
    0U,	// gBCLRL
    0U
  };

#ifndef CAPSTONE_DIET
  static const char AsmStrs[] = {
  /* 0 */ '#', 'E', 'H', '_', 'S', 'j', 'L', 'j', '_', 'S', 'e', 't', 'u', 'p', 9, 0,
  /* 16 */ 'b', 'd', 'z', 'l', 'a', '+', 32, 0,
  /* 24 */ 'b', 'd', 'n', 'z', 'l', 'a', '+', 32, 0,
  /* 33 */ 'b', 'd', 'z', 'a', '+', 32, 0,
  /* 40 */ 'b', 'd', 'n', 'z', 'a', '+', 32, 0,
  /* 48 */ 'b', 'd', 'z', 'l', '+', 32, 0,
  /* 55 */ 'b', 'd', 'n', 'z', 'l', '+', 32, 0,
  /* 63 */ 'b', 'd', 'z', '+', 32, 0,
  /* 69 */ 'b', 'd', 'n', 'z', '+', 32, 0,
  /* 76 */ 'b', 'c', 'l', 32, '2', '0', ',', 32, '3', '1', ',', 32, 0,
  /* 89 */ 'b', 'c', 't', 'r', 'l', 10, 9, 'l', 'd', 32, '2', ',', 32, 0,
  /* 103 */ 'b', 'c', 32, '1', '2', ',', 32, 0,
  /* 111 */ 'b', 'c', 'l', 32, '1', '2', ',', 32, 0,
  /* 120 */ 'b', 'c', 'l', 'r', 'l', 32, '1', '2', ',', 32, 0,
  /* 131 */ 'b', 'c', 'c', 't', 'r', 'l', 32, '1', '2', ',', 32, 0,
  /* 143 */ 'b', 'c', 'l', 'r', 32, '1', '2', ',', 32, 0,
  /* 153 */ 'b', 'c', 'c', 't', 'r', 32, '1', '2', ',', 32, 0,
  /* 164 */ 'b', 'c', 32, '4', ',', 32, 0,
  /* 171 */ 'b', 'c', 'l', 32, '4', ',', 32, 0,
  /* 179 */ 'b', 'c', 'l', 'r', 'l', 32, '4', ',', 32, 0,
  /* 189 */ 'b', 'c', 'c', 't', 'r', 'l', 32, '4', ',', 32, 0,
  /* 200 */ 'b', 'c', 'l', 'r', 32, '4', ',', 32, 0,
  /* 209 */ 'b', 'c', 'c', 't', 'r', 32, '4', ',', 32, 0,
  /* 219 */ 'm', 't', 's', 'p', 'r', 32, '2', '5', '6', ',', 32, 0,
  /* 231 */ 'b', 'd', 'z', 'l', 'a', '-', 32, 0,
  /* 239 */ 'b', 'd', 'n', 'z', 'l', 'a', '-', 32, 0,
  /* 248 */ 'b', 'd', 'z', 'a', '-', 32, 0,
  /* 255 */ 'b', 'd', 'n', 'z', 'a', '-', 32, 0,
  /* 263 */ 'b', 'd', 'z', 'l', '-', 32, 0,
  /* 270 */ 'b', 'd', 'n', 'z', 'l', '-', 32, 0,
  /* 278 */ 'b', 'd', 'z', '-', 32, 0,
  /* 284 */ 'b', 'd', 'n', 'z', '-', 32, 0,
  /* 291 */ 'v', 'c', 'm', 'p', 'g', 't', 's', 'b', '.', 32, 0,
  /* 302 */ 'e', 'x', 't', 's', 'b', '.', 32, 0,
  /* 310 */ 'v', 'c', 'm', 'p', 'e', 'q', 'u', 'b', '.', 32, 0,
  /* 321 */ 'f', 's', 'u', 'b', '.', 32, 0,
  /* 328 */ 'f', 'm', 's', 'u', 'b', '.', 32, 0,
  /* 336 */ 'f', 'n', 'm', 's', 'u', 'b', '.', 32, 0,
  /* 345 */ 'v', 'c', 'm', 'p', 'g', 't', 'u', 'b', '.', 32, 0,
  /* 356 */ 'a', 'd', 'd', 'c', '.', 32, 0,
  /* 363 */ 'a', 'n', 'd', 'c', '.', 32, 0,
  /* 370 */ 's', 'u', 'b', 'f', 'c', '.', 32, 0,
  /* 378 */ 's', 'u', 'b', 'i', 'c', '.', 32, 0,
  /* 386 */ 'a', 'd', 'd', 'i', 'c', '.', 32, 0,
  /* 394 */ 'r', 'l', 'd', 'i', 'c', '.', 32, 0,
  /* 402 */ 'o', 'r', 'c', '.', 32, 0,
  /* 408 */ 's', 'r', 'a', 'd', '.', 32, 0,
  /* 415 */ 'f', 'a', 'd', 'd', '.', 32, 0,
  /* 422 */ 'f', 'm', 'a', 'd', 'd', '.', 32, 0,
  /* 430 */ 'f', 'n', 'm', 'a', 'd', 'd', '.', 32, 0,
  /* 439 */ 'm', 'u', 'l', 'h', 'd', '.', 32, 0,
  /* 447 */ 'f', 'c', 'f', 'i', 'd', '.', 32, 0,
  /* 455 */ 'f', 'c', 't', 'i', 'd', '.', 32, 0,
  /* 463 */ 'm', 'u', 'l', 'l', 'd', '.', 32, 0,
  /* 471 */ 's', 'l', 'd', '.', 32, 0,
  /* 477 */ 'n', 'a', 'n', 'd', '.', 32, 0,
  /* 484 */ 's', 'r', 'd', '.', 32, 0,
  /* 490 */ 'v', 'c', 'm', 'p', 'g', 't', 's', 'd', '.', 32, 0,
  /* 501 */ 'v', 'c', 'm', 'p', 'e', 'q', 'u', 'd', '.', 32, 0,
  /* 512 */ 'v', 'c', 'm', 'p', 'g', 't', 'u', 'd', '.', 32, 0,
  /* 523 */ 'd', 'i', 'v', 'd', '.', 32, 0,
  /* 530 */ 'c', 'n', 't', 'l', 'z', 'd', '.', 32, 0,
  /* 539 */ 'a', 'd', 'd', 'e', '.', 32, 0,
  /* 546 */ 's', 'u', 'b', 'f', 'e', '.', 32, 0,
  /* 554 */ 'a', 'd', 'd', 'm', 'e', '.', 32, 0,
  /* 562 */ 's', 'u', 'b', 'f', 'm', 'e', '.', 32, 0,
  /* 571 */ 'f', 'r', 'e', '.', 32, 0,
  /* 577 */ 'f', 'r', 's', 'q', 'r', 't', 'e', '.', 32, 0,
  /* 587 */ 'a', 'd', 'd', 'z', 'e', '.', 32, 0,
  /* 595 */ 's', 'u', 'b', 'f', 'z', 'e', '.', 32, 0,
  /* 604 */ 's', 'u', 'b', 'f', '.', 32, 0,
  /* 611 */ 'm', 't', 'f', 's', 'f', '.', 32, 0,
  /* 619 */ 'f', 'n', 'e', 'g', '.', 32, 0,
  /* 626 */ 'v', 'c', 'm', 'p', 'g', 't', 's', 'h', '.', 32, 0,
  /* 637 */ 'e', 'x', 't', 's', 'h', '.', 32, 0,
  /* 645 */ 'v', 'c', 'm', 'p', 'e', 'q', 'u', 'h', '.', 32, 0,
  /* 656 */ 'v', 'c', 'm', 'p', 'g', 't', 'u', 'h', '.', 32, 0,
  /* 667 */ 's', 'r', 'a', 'd', 'i', '.', 32, 0,
  /* 675 */ 'c', 'l', 'r', 'l', 's', 'l', 'd', 'i', '.', 32, 0,
  /* 686 */ 'e', 'x', 't', 'l', 'd', 'i', '.', 32, 0,
  /* 695 */ 'a', 'n', 'd', 'i', '.', 32, 0,
  /* 702 */ 'c', 'l', 'r', 'r', 'd', 'i', '.', 32, 0,
  /* 711 */ 'i', 'n', 's', 'r', 'd', 'i', '.', 32, 0,
  /* 720 */ 'r', 'o', 't', 'r', 'd', 'i', '.', 32, 0,
  /* 729 */ 'e', 'x', 't', 'r', 'd', 'i', '.', 32, 0,
  /* 738 */ 'm', 't', 'f', 's', 'f', 'i', '.', 32, 0,
  /* 747 */ 'r', 'l', 'd', 'i', 'm', 'i', '.', 32, 0,
  /* 756 */ 'r', 'l', 'w', 'i', 'm', 'i', '.', 32, 0,
  /* 765 */ 's', 'r', 'a', 'w', 'i', '.', 32, 0,
  /* 773 */ 'c', 'l', 'r', 'l', 's', 'l', 'w', 'i', '.', 32, 0,
  /* 784 */ 'i', 'n', 's', 'l', 'w', 'i', '.', 32, 0,
  /* 793 */ 'e', 'x', 't', 'l', 'w', 'i', '.', 32, 0,
  /* 802 */ 'c', 'l', 'r', 'r', 'w', 'i', '.', 32, 0,
  /* 811 */ 'i', 'n', 's', 'r', 'w', 'i', '.', 32, 0,
  /* 820 */ 'r', 'o', 't', 'r', 'w', 'i', '.', 32, 0,
  /* 829 */ 'e', 'x', 't', 'r', 'w', 'i', '.', 32, 0,
  /* 838 */ 'r', 'l', 'd', 'c', 'l', '.', 32, 0,
  /* 846 */ 'r', 'l', 'd', 'i', 'c', 'l', '.', 32, 0,
  /* 855 */ 'f', 's', 'e', 'l', '.', 32, 0,
  /* 862 */ 'f', 'm', 'u', 'l', '.', 32, 0,
  /* 869 */ 'f', 'r', 'i', 'm', '.', 32, 0,
  /* 876 */ 'r', 'l', 'w', 'i', 'n', 'm', '.', 32, 0,
  /* 885 */ 'r', 'l', 'w', 'n', 'm', '.', 32, 0,
  /* 893 */ 'f', 'c', 'p', 's', 'g', 'n', '.', 32, 0,
  /* 902 */ 'f', 'r', 'i', 'n', '.', 32, 0,
  /* 909 */ 'x', 'v', 'c', 'm', 'p', 'g', 'e', 'd', 'p', '.', 32, 0,
  /* 921 */ 'x', 'v', 'c', 'm', 'p', 'e', 'q', 'd', 'p', '.', 32, 0,
  /* 933 */ 'x', 'v', 'c', 'm', 'p', 'g', 't', 'd', 'p', '.', 32, 0,
  /* 945 */ 'v', 'c', 'm', 'p', 'b', 'f', 'p', '.', 32, 0,
  /* 955 */ 'v', 'c', 'm', 'p', 'g', 'e', 'f', 'p', '.', 32, 0,
  /* 966 */ 'v', 'c', 'm', 'p', 'e', 'q', 'f', 'p', '.', 32, 0,
  /* 977 */ 'v', 'c', 'm', 'p', 'g', 't', 'f', 'p', '.', 32, 0,
  /* 988 */ 'f', 'r', 'i', 'p', '.', 32, 0,
  /* 995 */ 'x', 'v', 'c', 'm', 'p', 'g', 'e', 's', 'p', '.', 32, 0,
  /* 1007 */ 'x', 'v', 'c', 'm', 'p', 'e', 'q', 's', 'p', '.', 32, 0,
  /* 1019 */ 'f', 'r', 's', 'p', '.', 32, 0,
  /* 1026 */ 'x', 'v', 'c', 'm', 'p', 'g', 't', 's', 'p', '.', 32, 0,
  /* 1038 */ 'r', 'l', 'd', 'c', 'r', '.', 32, 0,
  /* 1046 */ 'r', 'l', 'd', 'i', 'c', 'r', '.', 32, 0,
  /* 1055 */ 'f', 'm', 'r', '.', 32, 0,
  /* 1061 */ 'n', 'o', 'r', '.', 32, 0,
  /* 1067 */ 'x', 'o', 'r', '.', 32, 0,
  /* 1073 */ 'f', 'a', 'b', 's', '.', 32, 0,
  /* 1080 */ 'f', 'n', 'a', 'b', 's', '.', 32, 0,
  /* 1088 */ 'f', 's', 'u', 'b', 's', '.', 32, 0,
  /* 1096 */ 'f', 'm', 's', 'u', 'b', 's', '.', 32, 0,
  /* 1105 */ 'f', 'n', 'm', 's', 'u', 'b', 's', '.', 32, 0,
  /* 1115 */ 'f', 'a', 'd', 'd', 's', '.', 32, 0,
  /* 1123 */ 'f', 'm', 'a', 'd', 'd', 's', '.', 32, 0,
  /* 1132 */ 'f', 'n', 'm', 'a', 'd', 'd', 's', '.', 32, 0,
  /* 1142 */ 'f', 'c', 'f', 'i', 'd', 's', '.', 32, 0,
  /* 1151 */ 'f', 'r', 'e', 's', '.', 32, 0,
  /* 1158 */ 'f', 'r', 's', 'q', 'r', 't', 'e', 's', '.', 32, 0,
  /* 1169 */ 'm', 'f', 'f', 's', '.', 32, 0,
  /* 1176 */ 'a', 'n', 'd', 'i', 's', '.', 32, 0,
  /* 1184 */ 'f', 'm', 'u', 'l', 's', '.', 32, 0,
  /* 1192 */ 'f', 's', 'q', 'r', 't', 's', '.', 32, 0,
  /* 1201 */ 'f', 'c', 'f', 'i', 'd', 'u', 's', '.', 32, 0,
  /* 1211 */ 'f', 'd', 'i', 'v', 's', '.', 32, 0,
  /* 1219 */ 'f', 's', 'q', 'r', 't', '.', 32, 0,
  /* 1227 */ 'm', 'u', 'l', 'h', 'd', 'u', '.', 32, 0,
  /* 1236 */ 'f', 'c', 'f', 'i', 'd', 'u', '.', 32, 0,
  /* 1245 */ 'd', 'i', 'v', 'd', 'u', '.', 32, 0,
  /* 1253 */ 'm', 'u', 'l', 'h', 'w', 'u', '.', 32, 0,
  /* 1262 */ 'd', 'i', 'v', 'w', 'u', '.', 32, 0,
  /* 1270 */ 'f', 'd', 'i', 'v', '.', 32, 0,
  /* 1277 */ 'e', 'q', 'v', '.', 32, 0,
  /* 1283 */ 's', 'r', 'a', 'w', '.', 32, 0,
  /* 1290 */ 'm', 'u', 'l', 'h', 'w', '.', 32, 0,
  /* 1298 */ 'f', 'c', 't', 'i', 'w', '.', 32, 0,
  /* 1306 */ 'm', 'u', 'l', 'l', 'w', '.', 32, 0,
  /* 1314 */ 's', 'l', 'w', '.', 32, 0,
  /* 1320 */ 's', 'r', 'w', '.', 32, 0,
  /* 1326 */ 'v', 'c', 'm', 'p', 'g', 't', 's', 'w', '.', 32, 0,
  /* 1337 */ 'e', 'x', 't', 's', 'w', '.', 32, 0,
  /* 1345 */ 'v', 'c', 'm', 'p', 'e', 'q', 'u', 'w', '.', 32, 0,
  /* 1356 */ 'v', 'c', 'm', 'p', 'g', 't', 'u', 'w', '.', 32, 0,
  /* 1367 */ 'd', 'i', 'v', 'w', '.', 32, 0,
  /* 1374 */ 'c', 'n', 't', 'l', 'z', 'w', '.', 32, 0,
  /* 1383 */ 's', 't', 'd', 'c', 'x', '.', 32, 0,
  /* 1391 */ 's', 't', 'w', 'c', 'x', '.', 32, 0,
  /* 1399 */ 't', 'l', 'b', 's', 'x', '.', 32, 0,
  /* 1407 */ 'f', 'c', 't', 'i', 'd', 'z', '.', 32, 0,
  /* 1416 */ 'f', 'r', 'i', 'z', '.', 32, 0,
  /* 1423 */ 'f', 'c', 't', 'i', 'd', 'u', 'z', '.', 32, 0,
  /* 1433 */ 'f', 'c', 't', 'i', 'w', 'u', 'z', '.', 32, 0,
  /* 1443 */ 'f', 'c', 't', 'i', 'w', 'z', '.', 32, 0,
  /* 1452 */ 'm', 't', 'f', 's', 'b', '0', 32, 0,
  /* 1460 */ 'm', 't', 'f', 's', 'b', '1', 32, 0,
  /* 1468 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'C', 'M', 'P', '_', 'S', 'W', 'A', 'P', '_', 'I', '3', '2', 32, 0,
  /* 1490 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'C', 'M', 'P', '_', 'S', 'W', 'A', 'P', '_', 'I', '1', '6', 32, 0,
  /* 1512 */ '#', 'T', 'C', '_', 'R', 'E', 'T', 'U', 'R', 'N', 'a', '8', 32, 0,
  /* 1526 */ '#', 'T', 'C', '_', 'R', 'E', 'T', 'U', 'R', 'N', 'd', '8', 32, 0,
  /* 1540 */ '#', 'T', 'C', '_', 'R', 'E', 'T', 'U', 'R', 'N', 'r', '8', 32, 0,
  /* 1554 */ 'U', 'P', 'D', 'A', 'T', 'E', '_', 'V', 'R', 'S', 'A', 'V', 'E', 32, 0,
  /* 1569 */ '#', 'A', 'D', 'J', 'C', 'A', 'L', 'L', 'S', 'T', 'A', 'C', 'K', 'D', 'O', 'W', 'N', 32, 0,
  /* 1588 */ '#', 'A', 'D', 'J', 'C', 'A', 'L', 'L', 'S', 'T', 'A', 'C', 'K', 'U', 'P', 32, 0,
  /* 1605 */ '#', 'T', 'C', '_', 'R', 'E', 'T', 'U', 'R', 'N', 'a', 32, 0,
  /* 1618 */ 'e', 'v', 'm', 'h', 'e', 'g', 's', 'm', 'f', 'a', 'a', 32, 0,
  /* 1631 */ 'e', 'v', 'm', 'h', 'o', 'g', 's', 'm', 'f', 'a', 'a', 32, 0,
  /* 1644 */ 'e', 'v', 'm', 'w', 's', 'm', 'f', 'a', 'a', 32, 0,
  /* 1655 */ 'e', 'v', 'm', 'w', 's', 's', 'f', 'a', 'a', 32, 0,
  /* 1666 */ 'e', 'v', 'm', 'h', 'e', 'g', 's', 'm', 'i', 'a', 'a', 32, 0,
  /* 1679 */ 'e', 'v', 'm', 'h', 'o', 'g', 's', 'm', 'i', 'a', 'a', 32, 0,
  /* 1692 */ 'e', 'v', 'm', 'w', 's', 'm', 'i', 'a', 'a', 32, 0,
  /* 1703 */ 'e', 'v', 'm', 'h', 'e', 'g', 'u', 'm', 'i', 'a', 'a', 32, 0,
  /* 1716 */ 'e', 'v', 'm', 'h', 'o', 'g', 'u', 'm', 'i', 'a', 'a', 32, 0,
  /* 1729 */ 'e', 'v', 'm', 'w', 'u', 'm', 'i', 'a', 'a', 32, 0,
  /* 1740 */ 'd', 'c', 'b', 'a', 32, 0,
  /* 1746 */ 'b', 'c', 'a', 32, 0,
  /* 1751 */ 'e', 'v', 'm', 'h', 'e', 's', 'm', 'f', 'a', 32, 0,
  /* 1762 */ 'e', 'v', 'm', 'w', 'h', 's', 'm', 'f', 'a', 32, 0,
  /* 1773 */ 'e', 'v', 'm', 'h', 'o', 's', 'm', 'f', 'a', 32, 0,
  /* 1784 */ 'e', 'v', 'm', 'w', 's', 'm', 'f', 'a', 32, 0,
  /* 1794 */ 'e', 'v', 'm', 'h', 'e', 's', 's', 'f', 'a', 32, 0,
  /* 1805 */ 'e', 'v', 'm', 'w', 'h', 's', 's', 'f', 'a', 32, 0,
  /* 1816 */ 'e', 'v', 'm', 'h', 'o', 's', 's', 'f', 'a', 32, 0,
  /* 1827 */ 'e', 'v', 'm', 'w', 's', 's', 'f', 'a', 32, 0,
  /* 1837 */ 'l', 'h', 'a', 32, 0,
  /* 1842 */ 'e', 'v', 'm', 'h', 'e', 's', 'm', 'i', 'a', 32, 0,
  /* 1853 */ 'e', 'v', 'm', 'w', 'h', 's', 'm', 'i', 'a', 32, 0,
  /* 1864 */ 'e', 'v', 'm', 'h', 'o', 's', 'm', 'i', 'a', 32, 0,
  /* 1875 */ 'e', 'v', 'm', 'w', 's', 'm', 'i', 'a', 32, 0,
  /* 1885 */ 'e', 'v', 'm', 'h', 'e', 'u', 'm', 'i', 'a', 32, 0,
  /* 1896 */ 'e', 'v', 'm', 'w', 'h', 'u', 'm', 'i', 'a', 32, 0,
  /* 1907 */ 'e', 'v', 'm', 'w', 'l', 'u', 'm', 'i', 'a', 32, 0,
  /* 1918 */ 'e', 'v', 'm', 'h', 'o', 'u', 'm', 'i', 'a', 32, 0,
  /* 1929 */ 'e', 'v', 'm', 'w', 'u', 'm', 'i', 'a', 32, 0,
  /* 1939 */ 'q', 'v', 's', 't', 'f', 'c', 'd', 'x', 'i', 'a', 32, 0,
  /* 1951 */ 'q', 'v', 's', 't', 'f', 'd', 'x', 'i', 'a', 32, 0,
  /* 1962 */ 'q', 'v', 's', 't', 'f', 'c', 's', 'x', 'i', 'a', 32, 0,
  /* 1974 */ 'q', 'v', 's', 't', 'f', 's', 'x', 'i', 'a', 32, 0,
  /* 1985 */ 'q', 'v', 's', 't', 'f', 'c', 'd', 'u', 'x', 'i', 'a', 32, 0,
  /* 1998 */ 'q', 'v', 's', 't', 'f', 'd', 'u', 'x', 'i', 'a', 32, 0,
  /* 2010 */ 'q', 'v', 's', 't', 'f', 'c', 's', 'u', 'x', 'i', 'a', 32, 0,
  /* 2023 */ 'q', 'v', 's', 't', 'f', 's', 'u', 'x', 'i', 'a', 32, 0,
  /* 2035 */ 'b', 'l', 'a', 32, 0,
  /* 2040 */ 'b', 'c', 'l', 'a', 32, 0,
  /* 2046 */ 'b', 'd', 'z', 'l', 'a', 32, 0,
  /* 2053 */ 'b', 'd', 'n', 'z', 'l', 'a', 32, 0,
  /* 2061 */ 'e', 'v', 'm', 'r', 'a', 32, 0,
  /* 2068 */ 'l', 'w', 'a', 32, 0,
  /* 2073 */ 'q', 'v', 'l', 'f', 'i', 'w', 'a', 'x', 'a', 32, 0,
  /* 2084 */ 'q', 'v', 'l', 'f', 'c', 'd', 'x', 'a', 32, 0,
  /* 2094 */ 'q', 'v', 's', 't', 'f', 'c', 'd', 'x', 'a', 32, 0,
  /* 2105 */ 'q', 'v', 'l', 'f', 'd', 'x', 'a', 32, 0,
  /* 2114 */ 'q', 'v', 's', 't', 'f', 'd', 'x', 'a', 32, 0,
  /* 2124 */ 'q', 'v', 'l', 'f', 'c', 's', 'x', 'a', 32, 0,
  /* 2134 */ 'q', 'v', 's', 't', 'f', 'c', 's', 'x', 'a', 32, 0,
  /* 2145 */ 'q', 'v', 'l', 'f', 's', 'x', 'a', 32, 0,
  /* 2154 */ 'q', 'v', 's', 't', 'f', 's', 'x', 'a', 32, 0,
  /* 2164 */ 'q', 'v', 'l', 'f', 'c', 'd', 'u', 'x', 'a', 32, 0,
  /* 2175 */ 'q', 'v', 's', 't', 'f', 'c', 'd', 'u', 'x', 'a', 32, 0,
  /* 2187 */ 'q', 'v', 'l', 'f', 'd', 'u', 'x', 'a', 32, 0,
  /* 2197 */ 'q', 'v', 's', 't', 'f', 'd', 'u', 'x', 'a', 32, 0,
  /* 2208 */ 'q', 'v', 'l', 'f', 'c', 's', 'u', 'x', 'a', 32, 0,
  /* 2219 */ 'q', 'v', 's', 't', 'f', 'c', 's', 'u', 'x', 'a', 32, 0,
  /* 2231 */ 'q', 'v', 'l', 'f', 's', 'u', 'x', 'a', 32, 0,
  /* 2241 */ 'q', 'v', 's', 't', 'f', 's', 'u', 'x', 'a', 32, 0,
  /* 2252 */ 'q', 'v', 's', 't', 'f', 'i', 'w', 'x', 'a', 32, 0,
  /* 2263 */ 'q', 'v', 'l', 'f', 'i', 'w', 'z', 'x', 'a', 32, 0,
  /* 2274 */ 'b', 'd', 'z', 'a', 32, 0,
  /* 2280 */ 'b', 'd', 'n', 'z', 'a', 32, 0,
  /* 2287 */ 'v', 's', 'r', 'a', 'b', 32, 0,
  /* 2294 */ 'v', 'm', 'r', 'g', 'h', 'b', 32, 0,
  /* 2302 */ 'v', 'm', 'r', 'g', 'l', 'b', 32, 0,
  /* 2310 */ 'v', 'r', 'l', 'b', 32, 0,
  /* 2316 */ 'v', 's', 'l', 'b', 32, 0,
  /* 2322 */ 'c', 'm', 'p', 'b', 32, 0,
  /* 2328 */ 'v', 's', 'r', 'b', 32, 0,
  /* 2334 */ 'v', 'm', 'u', 'l', 'e', 's', 'b', 32, 0,
  /* 2343 */ 'v', 'a', 'v', 'g', 's', 'b', 32, 0,
  /* 2351 */ 'v', 'u', 'p', 'k', 'h', 's', 'b', 32, 0,
  /* 2360 */ 'v', 's', 'p', 'l', 't', 'i', 's', 'b', 32, 0,
  /* 2370 */ 'v', 'u', 'p', 'k', 'l', 's', 'b', 32, 0,
  /* 2379 */ 'v', 'm', 'i', 'n', 's', 'b', 32, 0,
  /* 2387 */ 'v', 'm', 'u', 'l', 'o', 's', 'b', 32, 0,
  /* 2396 */ 'v', 'c', 'm', 'p', 'g', 't', 's', 'b', 32, 0,
  /* 2406 */ 'e', 'v', 'e', 'x', 't', 's', 'b', 32, 0,
  /* 2415 */ 'v', 'm', 'a', 'x', 's', 'b', 32, 0,
  /* 2423 */ 'm', 'f', 't', 'b', 32, 0,
  /* 2429 */ 'v', 's', 'p', 'l', 't', 'b', 32, 0,
  /* 2437 */ 'v', 'p', 'o', 'p', 'c', 'n', 't', 'b', 32, 0,
  /* 2447 */ 's', 't', 'b', 32, 0,
  /* 2452 */ 'v', 'm', 'u', 'l', 'e', 'u', 'b', 32, 0,
  /* 2461 */ 'v', 'a', 'v', 'g', 'u', 'b', 32, 0,
  /* 2469 */ 'v', 'm', 'i', 'n', 'u', 'b', 32, 0,
  /* 2477 */ 'v', 'm', 'u', 'l', 'o', 'u', 'b', 32, 0,
  /* 2486 */ 'v', 'c', 'm', 'p', 'e', 'q', 'u', 'b', 32, 0,
  /* 2496 */ 'q', 'v', 'f', 's', 'u', 'b', 32, 0,
  /* 2504 */ 'q', 'v', 'f', 'm', 's', 'u', 'b', 32, 0,
  /* 2513 */ 'q', 'v', 'f', 'n', 'm', 's', 'u', 'b', 32, 0,
  /* 2523 */ 'v', 'c', 'm', 'p', 'g', 't', 'u', 'b', 32, 0,
  /* 2533 */ 'v', 'm', 'a', 'x', 'u', 'b', 32, 0,
  /* 2541 */ 'v', 'c', 'l', 'z', 'b', 32, 0,
  /* 2548 */ 'b', 'c', 32, 0,
  /* 2552 */ 'a', 'd', 'd', 'c', 32, 0,
  /* 2558 */ 'x', 'x', 'l', 'a', 'n', 'd', 'c', 32, 0,
  /* 2567 */ 'c', 'r', 'a', 'n', 'd', 'c', 32, 0,
  /* 2575 */ 'e', 'v', 'a', 'n', 'd', 'c', 32, 0,
  /* 2583 */ 's', 'u', 'b', 'f', 'c', 32, 0,
  /* 2590 */ 's', 'u', 'b', 'i', 'c', 32, 0,
  /* 2597 */ 'a', 'd', 'd', 'i', 'c', 32, 0,
  /* 2604 */ 'r', 'l', 'd', 'i', 'c', 32, 0,
  /* 2611 */ 's', 'u', 'b', 'f', 'i', 'c', 32, 0,
  /* 2619 */ 'x', 's', 'r', 'd', 'p', 'i', 'c', 32, 0,
  /* 2628 */ 'x', 'v', 'r', 'd', 'p', 'i', 'c', 32, 0,
  /* 2637 */ 'x', 'v', 'r', 's', 'p', 'i', 'c', 32, 0,
  /* 2646 */ 'b', 'r', 'i', 'n', 'c', 32, 0,
  /* 2653 */ 's', 'y', 'n', 'c', 32, 0,
  /* 2659 */ 'x', 'x', 'l', 'o', 'r', 'c', 32, 0,
  /* 2667 */ 'c', 'r', 'o', 'r', 'c', 32, 0,
  /* 2674 */ 'e', 'v', 'o', 'r', 'c', 32, 0,
  /* 2681 */ 's', 'c', 32, 0,
  /* 2685 */ '#', 'T', 'C', '_', 'R', 'E', 'T', 'U', 'R', 'N', 'd', 32, 0,
  /* 2698 */ 'v', 's', 'r', 'a', 'd', 32, 0,
  /* 2705 */ 'q', 'v', 'f', 'a', 'd', 'd', 32, 0,
  /* 2713 */ 'q', 'v', 'f', 'm', 'a', 'd', 'd', 32, 0,
  /* 2722 */ 'q', 'v', 'f', 'n', 'm', 'a', 'd', 'd', 32, 0,
  /* 2732 */ 'q', 'v', 'f', 'x', 'x', 'c', 'p', 'n', 'm', 'a', 'd', 'd', 32, 0,
  /* 2746 */ 'q', 'v', 'f', 'x', 'x', 'n', 'p', 'm', 'a', 'd', 'd', 32, 0,
  /* 2759 */ 'q', 'v', 'f', 'x', 'm', 'a', 'd', 'd', 32, 0,
  /* 2769 */ 'q', 'v', 'f', 'x', 'x', 'm', 'a', 'd', 'd', 32, 0,
  /* 2780 */ 'e', 'v', 'l', 'd', 'd', 32, 0,
  /* 2787 */ 'e', 'v', 's', 't', 'd', 'd', 32, 0,
  /* 2795 */ 'l', 'f', 'd', 32, 0,
  /* 2800 */ 's', 't', 'f', 'd', 32, 0,
  /* 2806 */ 'm', 'u', 'l', 'h', 'd', 32, 0,
  /* 2813 */ 'q', 'v', 'f', 'c', 'f', 'i', 'd', 32, 0,
  /* 2822 */ 'q', 'v', 'f', 'c', 't', 'i', 'd', 32, 0,
  /* 2831 */ 't', 'l', 'b', 'l', 'd', 32, 0,
  /* 2838 */ 'm', 'u', 'l', 'l', 'd', 32, 0,
  /* 2845 */ 'c', 'm', 'p', 'l', 'd', 32, 0,
  /* 2852 */ 'v', 'r', 'l', 'd', 32, 0,
  /* 2858 */ 'v', 's', 'l', 'd', 32, 0,
  /* 2864 */ 'x', 'x', 'l', 'a', 'n', 'd', 32, 0,
  /* 2872 */ 'x', 'x', 'l', 'n', 'a', 'n', 'd', 32, 0,
  /* 2881 */ 'c', 'r', 'n', 'a', 'n', 'd', 32, 0,
  /* 2889 */ 'e', 'v', 'n', 'a', 'n', 'd', 32, 0,
  /* 2897 */ 'c', 'r', 'a', 'n', 'd', 32, 0,
  /* 2904 */ 'e', 'v', 'a', 'n', 'd', 32, 0,
  /* 2911 */ 'c', 'm', 'p', 'd', 32, 0,
  /* 2917 */ 'm', 't', 'm', 's', 'r', 'd', 32, 0,
  /* 2925 */ 'v', 's', 'r', 'd', 32, 0,
  /* 2931 */ 'v', 'm', 'i', 'n', 's', 'd', 32, 0,
  /* 2939 */ 'v', 'c', 'm', 'p', 'g', 't', 's', 'd', 32, 0,
  /* 2949 */ 'v', 'm', 'a', 'x', 's', 'd', 32, 0,
  /* 2957 */ 'v', 'p', 'o', 'p', 'c', 'n', 't', 'd', 32, 0,
  /* 2967 */ 's', 't', 'd', 32, 0,
  /* 2972 */ 'v', 'm', 'i', 'n', 'u', 'd', 32, 0,
  /* 2980 */ 'v', 'c', 'm', 'p', 'e', 'q', 'u', 'd', 32, 0,
  /* 2990 */ 'v', 'c', 'm', 'p', 'g', 't', 'u', 'd', 32, 0,
  /* 3000 */ 'v', 'm', 'a', 'x', 'u', 'd', 32, 0,
  /* 3008 */ 'd', 'i', 'v', 'd', 32, 0,
  /* 3014 */ 'v', 'c', 'l', 'z', 'd', 32, 0,
  /* 3021 */ 'c', 'n', 't', 'l', 'z', 'd', 32, 0,
  /* 3029 */ 'a', 'd', 'd', 'e', 32, 0,
  /* 3035 */ 's', 'l', 'b', 'm', 'f', 'e', 'e', 32, 0,
  /* 3044 */ 'w', 'r', 't', 'e', 'e', 32, 0,
  /* 3051 */ 's', 'u', 'b', 'f', 'e', 32, 0,
  /* 3058 */ 'e', 'v', 'l', 'w', 'h', 'e', 32, 0,
  /* 3066 */ 'e', 'v', 's', 't', 'w', 'h', 'e', 32, 0,
  /* 3075 */ 's', 'l', 'b', 'i', 'e', 32, 0,
  /* 3082 */ 't', 'l', 'b', 'i', 'e', 32, 0,
  /* 3089 */ 'a', 'd', 'd', 'm', 'e', 32, 0,
  /* 3096 */ 's', 'u', 'b', 'f', 'm', 'e', 32, 0,
  /* 3104 */ 't', 'l', 'b', 'r', 'e', 32, 0,
  /* 3111 */ 'q', 'v', 'f', 'r', 'e', 32, 0,
  /* 3118 */ 's', 'l', 'b', 'm', 't', 'e', 32, 0,
  /* 3126 */ 'q', 'v', 'f', 'r', 's', 'q', 'r', 't', 'e', 32, 0,
  /* 3137 */ 't', 'l', 'b', 'w', 'e', 32, 0,
  /* 3144 */ 'e', 'v', 's', 't', 'w', 'w', 'e', 32, 0,
  /* 3153 */ 'a', 'd', 'd', 'z', 'e', 32, 0,
  /* 3160 */ 's', 'u', 'b', 'f', 'z', 'e', 32, 0,
  /* 3168 */ 'd', 'c', 'b', 'f', 32, 0,
  /* 3174 */ 's', 'u', 'b', 'f', 32, 0,
  /* 3180 */ 'e', 'v', 'm', 'h', 'e', 's', 'm', 'f', 32, 0,
  /* 3190 */ 'e', 'v', 'm', 'w', 'h', 's', 'm', 'f', 32, 0,
  /* 3200 */ 'e', 'v', 'm', 'h', 'o', 's', 'm', 'f', 32, 0,
  /* 3210 */ 'e', 'v', 'm', 'w', 's', 'm', 'f', 32, 0,
  /* 3219 */ 'm', 'c', 'r', 'f', 32, 0,
  /* 3225 */ 'm', 'f', 'o', 'c', 'r', 'f', 32, 0,
  /* 3233 */ 'm', 't', 'o', 'c', 'r', 'f', 32, 0,
  /* 3241 */ 'm', 't', 'c', 'r', 'f', 32, 0,
  /* 3248 */ 'm', 't', 'f', 's', 'f', 32, 0,
  /* 3255 */ 'e', 'v', 'm', 'h', 'e', 's', 's', 'f', 32, 0,
  /* 3265 */ 'e', 'v', 'm', 'w', 'h', 's', 's', 'f', 32, 0,
  /* 3275 */ 'e', 'v', 'm', 'h', 'o', 's', 's', 'f', 32, 0,
  /* 3285 */ 'e', 'v', 'm', 'w', 's', 's', 'f', 32, 0,
  /* 3294 */ 'q', 'v', 'f', 'n', 'e', 'g', 32, 0,
  /* 3302 */ 'e', 'v', 'n', 'e', 'g', 32, 0,
  /* 3309 */ 'v', 's', 'r', 'a', 'h', 32, 0,
  /* 3316 */ 'e', 'v', 'l', 'd', 'h', 32, 0,
  /* 3323 */ 'e', 'v', 's', 't', 'd', 'h', 32, 0,
  /* 3331 */ 'v', 'm', 'r', 'g', 'h', 'h', 32, 0,
  /* 3339 */ 'v', 'm', 'r', 'g', 'l', 'h', 32, 0,
  /* 3347 */ 'v', 'r', 'l', 'h', 32, 0,
  /* 3353 */ 'v', 's', 'l', 'h', 32, 0,
  /* 3359 */ 'v', 's', 'r', 'h', 32, 0,
  /* 3365 */ 'v', 'm', 'u', 'l', 'e', 's', 'h', 32, 0,
  /* 3374 */ 'v', 'a', 'v', 'g', 's', 'h', 32, 0,
  /* 3382 */ 'v', 'u', 'p', 'k', 'h', 's', 'h', 32, 0,
  /* 3391 */ 'v', 's', 'p', 'l', 't', 'i', 's', 'h', 32, 0,
  /* 3401 */ 'v', 'u', 'p', 'k', 'l', 's', 'h', 32, 0,
  /* 3410 */ 'v', 'm', 'i', 'n', 's', 'h', 32, 0,
  /* 3418 */ 'v', 'm', 'u', 'l', 'o', 's', 'h', 32, 0,
  /* 3427 */ 'v', 'c', 'm', 'p', 'g', 't', 's', 'h', 32, 0,
  /* 3437 */ 'e', 'v', 'e', 'x', 't', 's', 'h', 32, 0,
  /* 3446 */ 'v', 'm', 'a', 'x', 's', 'h', 32, 0,
  /* 3454 */ 'v', 's', 'p', 'l', 't', 'h', 32, 0,
  /* 3462 */ 'v', 'p', 'o', 'p', 'c', 'n', 't', 'h', 32, 0,
  /* 3472 */ 's', 't', 'h', 32, 0,
  /* 3477 */ 'v', 'm', 'u', 'l', 'e', 'u', 'h', 32, 0,
  /* 3486 */ 'v', 'a', 'v', 'g', 'u', 'h', 32, 0,
  /* 3494 */ 'v', 'm', 'i', 'n', 'u', 'h', 32, 0,
  /* 3502 */ 'v', 'm', 'u', 'l', 'o', 'u', 'h', 32, 0,
  /* 3511 */ 'v', 'c', 'm', 'p', 'e', 'q', 'u', 'h', 32, 0,
  /* 3521 */ 'v', 'c', 'm', 'p', 'g', 't', 'u', 'h', 32, 0,
  /* 3531 */ 'v', 'm', 'a', 'x', 'u', 'h', 32, 0,
  /* 3539 */ 'v', 'c', 'l', 'z', 'h', 32, 0,
  /* 3546 */ 'd', 'c', 'b', 'i', 32, 0,
  /* 3552 */ 'i', 'c', 'b', 'i', 32, 0,
  /* 3558 */ 's', 'u', 'b', 'i', 32, 0,
  /* 3564 */ 'd', 'c', 'c', 'c', 'i', 32, 0,
  /* 3571 */ 'i', 'c', 'c', 'c', 'i', 32, 0,
  /* 3578 */ 'q', 'v', 'g', 'p', 'c', 'i', 32, 0,
  /* 3586 */ 's', 'r', 'a', 'd', 'i', 32, 0,
  /* 3593 */ 'a', 'd', 'd', 'i', 32, 0,
  /* 3599 */ 'c', 'm', 'p', 'l', 'd', 'i', 32, 0,
  /* 3607 */ 'c', 'l', 'r', 'l', 's', 'l', 'd', 'i', 32, 0,
  /* 3617 */ 'e', 'x', 't', 'l', 'd', 'i', 32, 0,
  /* 3625 */ 'x', 'x', 'p', 'e', 'r', 'm', 'd', 'i', 32, 0,
  /* 3635 */ 'c', 'm', 'p', 'd', 'i', 32, 0,
  /* 3642 */ 'c', 'l', 'r', 'r', 'd', 'i', 32, 0,
  /* 3650 */ 'i', 'n', 's', 'r', 'd', 'i', 32, 0,
  /* 3658 */ 'r', 'o', 't', 'r', 'd', 'i', 32, 0,
  /* 3666 */ 'e', 'x', 't', 'r', 'd', 'i', 32, 0,
  /* 3674 */ 't', 'd', 'i', 32, 0,
  /* 3679 */ 'w', 'r', 't', 'e', 'e', 'i', 32, 0,
  /* 3687 */ 'm', 't', 'f', 's', 'f', 'i', 32, 0,
  /* 3695 */ 'e', 'v', 's', 'p', 'l', 'a', 't', 'f', 'i', 32, 0,
  /* 3706 */ 'e', 'v', 'm', 'e', 'r', 'g', 'e', 'h', 'i', 32, 0,
  /* 3717 */ 'e', 'v', 'm', 'e', 'r', 'g', 'e', 'l', 'o', 'h', 'i', 32, 0,
  /* 3730 */ 't', 'l', 'b', 'l', 'i', 32, 0,
  /* 3737 */ 'm', 'u', 'l', 'l', 'i', 32, 0,
  /* 3744 */ 'r', 'l', 'd', 'i', 'm', 'i', 32, 0,
  /* 3752 */ 'r', 'l', 'w', 'i', 'm', 'i', 32, 0,
  /* 3760 */ 'e', 'v', 'm', 'h', 'e', 's', 'm', 'i', 32, 0,
  /* 3770 */ 'e', 'v', 'm', 'w', 'h', 's', 'm', 'i', 32, 0,
  /* 3780 */ 'e', 'v', 'm', 'h', 'o', 's', 'm', 'i', 32, 0,
  /* 3790 */ 'e', 'v', 'm', 'w', 's', 'm', 'i', 32, 0,
  /* 3799 */ 'e', 'v', 'm', 'h', 'e', 'u', 'm', 'i', 32, 0,
  /* 3809 */ 'e', 'v', 'm', 'w', 'h', 'u', 'm', 'i', 32, 0,
  /* 3819 */ 'e', 'v', 'm', 'w', 'l', 'u', 'm', 'i', 32, 0,
  /* 3829 */ 'e', 'v', 'm', 'h', 'o', 'u', 'm', 'i', 32, 0,
  /* 3839 */ 'e', 'v', 'm', 'w', 'u', 'm', 'i', 32, 0,
  /* 3848 */ 'q', 'v', 'a', 'l', 'i', 'g', 'n', 'i', 32, 0,
  /* 3858 */ 'v', 's', 'l', 'd', 'o', 'i', 32, 0,
  /* 3866 */ 'x', 's', 'r', 'd', 'p', 'i', 32, 0,
  /* 3874 */ 'x', 'v', 'r', 'd', 'p', 'i', 32, 0,
  /* 3882 */ 'x', 'v', 'r', 's', 'p', 'i', 32, 0,
  /* 3890 */ 'x', 'o', 'r', 'i', 32, 0,
  /* 3896 */ 'q', 'v', 'e', 's', 'p', 'l', 'a', 't', 'i', 32, 0,
  /* 3907 */ 'e', 'v', 's', 'p', 'l', 'a', 't', 'i', 32, 0,
  /* 3917 */ 's', 'r', 'a', 'w', 'i', 32, 0,
  /* 3924 */ 'x', 'x', 's', 'l', 'd', 'w', 'i', 32, 0,
  /* 3933 */ 'c', 'm', 'p', 'l', 'w', 'i', 32, 0,
  /* 3941 */ 'e', 'v', 'r', 'l', 'w', 'i', 32, 0,
  /* 3949 */ 'c', 'l', 'r', 'l', 's', 'l', 'w', 'i', 32, 0,
  /* 3959 */ 'i', 'n', 's', 'l', 'w', 'i', 32, 0,
  /* 3967 */ 'e', 'v', 's', 'l', 'w', 'i', 32, 0,
  /* 3975 */ 'e', 'x', 't', 'l', 'w', 'i', 32, 0,
  /* 3983 */ 'c', 'm', 'p', 'w', 'i', 32, 0,
  /* 3990 */ 'c', 'l', 'r', 'r', 'w', 'i', 32, 0,
  /* 3998 */ 'i', 'n', 's', 'r', 'w', 'i', 32, 0,
  /* 4006 */ 'r', 'o', 't', 'r', 'w', 'i', 32, 0,
  /* 4014 */ 'e', 'x', 't', 'r', 'w', 'i', 32, 0,
  /* 4022 */ 'l', 's', 'w', 'i', 32, 0,
  /* 4028 */ 's', 't', 's', 'w', 'i', 32, 0,
  /* 4035 */ 't', 'w', 'i', 32, 0,
  /* 4040 */ 'q', 'v', 's', 't', 'f', 'c', 'd', 'x', 'i', 32, 0,
  /* 4051 */ 'q', 'v', 's', 't', 'f', 'd', 'x', 'i', 32, 0,
  /* 4061 */ 'q', 'v', 's', 't', 'f', 'c', 's', 'x', 'i', 32, 0,
  /* 4072 */ 'q', 'v', 's', 't', 'f', 's', 'x', 'i', 32, 0,
  /* 4082 */ 'q', 'v', 's', 't', 'f', 'c', 'd', 'u', 'x', 'i', 32, 0,
  /* 4094 */ 'q', 'v', 's', 't', 'f', 'd', 'u', 'x', 'i', 32, 0,
  /* 4105 */ 'q', 'v', 's', 't', 'f', 'c', 's', 'u', 'x', 'i', 32, 0,
  /* 4117 */ 'q', 'v', 's', 't', 'f', 's', 'u', 'x', 'i', 32, 0,
  /* 4128 */ 'q', 'v', 'f', 'l', 'o', 'g', 'i', 'c', 'a', 'l', 32, 0,
  /* 4140 */ 'b', 'l', 32, 0,
  /* 4144 */ 'b', 'c', 'l', 32, 0,
  /* 4149 */ 'r', 'l', 'd', 'c', 'l', 32, 0,
  /* 4156 */ 'r', 'l', 'd', 'i', 'c', 'l', 32, 0,
  /* 4164 */ 't', 'l', 'b', 'i', 'e', 'l', 32, 0,
  /* 4172 */ 'q', 'v', 'f', 's', 'e', 'l', 32, 0,
  /* 4180 */ 'i', 's', 'e', 'l', 32, 0,
  /* 4186 */ 'v', 's', 'e', 'l', 32, 0,
  /* 4192 */ 'x', 'x', 's', 'e', 'l', 32, 0,
  /* 4199 */ 'b', 'c', 'l', 'r', 'l', 32, 0,
  /* 4206 */ 'b', 'c', 'c', 't', 'r', 'l', 32, 0,
  /* 4214 */ 'l', 'v', 's', 'l', 32, 0,
  /* 4220 */ 'q', 'v', 'f', 'm', 'u', 'l', 32, 0,
  /* 4228 */ 'q', 'v', 'f', 'x', 'm', 'u', 'l', 32, 0,
  /* 4237 */ 'l', 'v', 'x', 'l', 32, 0,
  /* 4243 */ 's', 't', 'v', 'x', 'l', 32, 0,
  /* 4250 */ 'd', 'c', 'b', 'z', 'l', 32, 0,
  /* 4257 */ 'b', 'd', 'z', 'l', 32, 0,
  /* 4263 */ 'b', 'd', 'n', 'z', 'l', 32, 0,
  /* 4270 */ 'v', 'm', 's', 'u', 'm', 'm', 'b', 'm', 32, 0,
  /* 4280 */ 'v', 's', 'u', 'b', 'u', 'b', 'm', 32, 0,
  /* 4289 */ 'v', 'a', 'd', 'd', 'u', 'b', 'm', 32, 0,
  /* 4298 */ 'v', 'm', 's', 'u', 'm', 'u', 'b', 'm', 32, 0,
  /* 4308 */ 'v', 's', 'u', 'b', 'u', 'd', 'm', 32, 0,
  /* 4317 */ 'v', 'a', 'd', 'd', 'u', 'd', 'm', 32, 0,
  /* 4326 */ 'v', 'm', 's', 'u', 'm', 's', 'h', 'm', 32, 0,
  /* 4336 */ 'v', 's', 'u', 'b', 'u', 'h', 'm', 32, 0,
  /* 4345 */ 'v', 'm', 'l', 'a', 'd', 'd', 'u', 'h', 'm', 32, 0,
  /* 4356 */ 'v', 'a', 'd', 'd', 'u', 'h', 'm', 32, 0,
  /* 4365 */ 'v', 'm', 's', 'u', 'm', 'u', 'h', 'm', 32, 0,
  /* 4375 */ 'v', 'r', 'f', 'i', 'm', 32, 0,
  /* 4382 */ 'x', 's', 'r', 'd', 'p', 'i', 'm', 32, 0,
  /* 4391 */ 'x', 'v', 'r', 'd', 'p', 'i', 'm', 32, 0,
  /* 4400 */ 'x', 'v', 'r', 's', 'p', 'i', 'm', 32, 0,
  /* 4409 */ 'q', 'v', 'f', 'r', 'i', 'm', 32, 0,
  /* 4417 */ 'r', 'l', 'w', 'i', 'n', 'm', 32, 0,
  /* 4425 */ 'r', 'l', 'w', 'n', 'm', 32, 0,
  /* 4432 */ 'q', 'v', 'f', 'p', 'e', 'r', 'm', 32, 0,
  /* 4441 */ 'v', 'p', 'e', 'r', 'm', 32, 0,
  /* 4448 */ 'v', 'p', 'k', 'u', 'h', 'u', 'm', 32, 0,
  /* 4457 */ 'v', 'p', 'k', 'u', 'w', 'u', 'm', 32, 0,
  /* 4466 */ 'v', 's', 'u', 'b', 'u', 'w', 'm', 32, 0,
  /* 4475 */ 'v', 'a', 'd', 'd', 'u', 'w', 'm', 32, 0,
  /* 4484 */ 'v', 'm', 'u', 'l', 'u', 'w', 'm', 32, 0,
  /* 4493 */ 'e', 'v', 'm', 'h', 'e', 'g', 's', 'm', 'f', 'a', 'n', 32, 0,
  /* 4506 */ 'e', 'v', 'm', 'h', 'o', 'g', 's', 'm', 'f', 'a', 'n', 32, 0,
  /* 4519 */ 'e', 'v', 'm', 'w', 's', 'm', 'f', 'a', 'n', 32, 0,
  /* 4530 */ 'e', 'v', 'm', 'w', 's', 's', 'f', 'a', 'n', 32, 0,
  /* 4541 */ 'e', 'v', 'm', 'h', 'e', 'g', 's', 'm', 'i', 'a', 'n', 32, 0,
  /* 4554 */ 'e', 'v', 'm', 'h', 'o', 'g', 's', 'm', 'i', 'a', 'n', 32, 0,
  /* 4567 */ 'e', 'v', 'm', 'w', 's', 'm', 'i', 'a', 'n', 32, 0,
  /* 4578 */ 'e', 'v', 'm', 'h', 'e', 'g', 'u', 'm', 'i', 'a', 'n', 32, 0,
  /* 4591 */ 'e', 'v', 'm', 'h', 'o', 'g', 'u', 'm', 'i', 'a', 'n', 32, 0,
  /* 4604 */ 'e', 'v', 'm', 'w', 'u', 'm', 'i', 'a', 'n', 32, 0,
  /* 4615 */ 'q', 'v', 'f', 't', 's', 't', 'n', 'a', 'n', 32, 0,
  /* 4626 */ 'q', 'v', 'f', 'c', 'p', 's', 'g', 'n', 32, 0,
  /* 4636 */ 'v', 'r', 'f', 'i', 'n', 32, 0,
  /* 4643 */ 'q', 'v', 'f', 'r', 'i', 'n', 32, 0,
  /* 4651 */ 'm', 'f', 's', 'r', 'i', 'n', 32, 0,
  /* 4659 */ 'm', 't', 's', 'r', 'i', 'n', 32, 0,
  /* 4667 */ 'e', 'v', 's', 't', 'w', 'h', 'o', 32, 0,
  /* 4676 */ 'e', 'v', 'm', 'e', 'r', 'g', 'e', 'l', 'o', 32, 0,
  /* 4687 */ 'e', 'v', 'm', 'e', 'r', 'g', 'e', 'h', 'i', 'l', 'o', 32, 0,
  /* 4700 */ 'v', 's', 'l', 'o', 32, 0,
  /* 4706 */ 'v', 's', 'r', 'o', 32, 0,
  /* 4712 */ 'e', 'v', 's', 't', 'w', 'w', 'o', 32, 0,
  /* 4721 */ 'x', 's', 'n', 'm', 's', 'u', 'b', 'a', 'd', 'p', 32, 0,
  /* 4733 */ 'x', 'v', 'n', 'm', 's', 'u', 'b', 'a', 'd', 'p', 32, 0,
  /* 4745 */ 'x', 's', 'm', 's', 'u', 'b', 'a', 'd', 'p', 32, 0,
  /* 4756 */ 'x', 'v', 'm', 's', 'u', 'b', 'a', 'd', 'p', 32, 0,
  /* 4767 */ 'x', 's', 'n', 'm', 'a', 'd', 'd', 'a', 'd', 'p', 32, 0,
  /* 4779 */ 'x', 'v', 'n', 'm', 'a', 'd', 'd', 'a', 'd', 'p', 32, 0,
  /* 4791 */ 'x', 's', 'm', 'a', 'd', 'd', 'a', 'd', 'p', 32, 0,
  /* 4802 */ 'x', 'v', 'm', 'a', 'd', 'd', 'a', 'd', 'p', 32, 0,
  /* 4813 */ 'x', 's', 's', 'u', 'b', 'd', 'p', 32, 0,
  /* 4822 */ 'x', 'v', 's', 'u', 'b', 'd', 'p', 32, 0,
  /* 4831 */ 'x', 's', 'a', 'd', 'd', 'd', 'p', 32, 0,
  /* 4840 */ 'x', 'v', 'a', 'd', 'd', 'd', 'p', 32, 0,
  /* 4849 */ 'x', 's', 'c', 'v', 's', 'x', 'd', 'd', 'p', 32, 0,
  /* 4860 */ 'x', 'v', 'c', 'v', 's', 'x', 'd', 'd', 'p', 32, 0,
  /* 4871 */ 'x', 's', 'c', 'v', 'u', 'x', 'd', 'd', 'p', 32, 0,
  /* 4882 */ 'x', 'v', 'c', 'v', 'u', 'x', 'd', 'd', 'p', 32, 0,
  /* 4893 */ 'x', 'v', 'c', 'm', 'p', 'g', 'e', 'd', 'p', 32, 0,
  /* 4904 */ 'x', 's', 'r', 'e', 'd', 'p', 32, 0,
  /* 4912 */ 'x', 'v', 'r', 'e', 'd', 'p', 32, 0,
  /* 4920 */ 'x', 's', 'r', 's', 'q', 'r', 't', 'e', 'd', 'p', 32, 0,
  /* 4932 */ 'x', 'v', 'r', 's', 'q', 'r', 't', 'e', 'd', 'p', 32, 0,
  /* 4944 */ 'x', 's', 'n', 'e', 'g', 'd', 'p', 32, 0,
  /* 4953 */ 'x', 'v', 'n', 'e', 'g', 'd', 'p', 32, 0,
  /* 4962 */ 'x', 's', 'm', 'u', 'l', 'd', 'p', 32, 0,
  /* 4971 */ 'x', 'v', 'm', 'u', 'l', 'd', 'p', 32, 0,
  /* 4980 */ 'x', 's', 'n', 'm', 's', 'u', 'b', 'm', 'd', 'p', 32, 0,
  /* 4992 */ 'x', 'v', 'n', 'm', 's', 'u', 'b', 'm', 'd', 'p', 32, 0,
  /* 5004 */ 'x', 's', 'm', 's', 'u', 'b', 'm', 'd', 'p', 32, 0,
  /* 5015 */ 'x', 'v', 'm', 's', 'u', 'b', 'm', 'd', 'p', 32, 0,
  /* 5026 */ 'x', 's', 'n', 'm', 'a', 'd', 'd', 'm', 'd', 'p', 32, 0,
  /* 5038 */ 'x', 'v', 'n', 'm', 'a', 'd', 'd', 'm', 'd', 'p', 32, 0,
  /* 5050 */ 'x', 's', 'm', 'a', 'd', 'd', 'm', 'd', 'p', 32, 0,
  /* 5061 */ 'x', 'v', 'm', 'a', 'd', 'd', 'm', 'd', 'p', 32, 0,
  /* 5072 */ 'x', 's', 'c', 'p', 's', 'g', 'n', 'd', 'p', 32, 0,
  /* 5083 */ 'x', 'v', 'c', 'p', 's', 'g', 'n', 'd', 'p', 32, 0,
  /* 5094 */ 'x', 's', 'm', 'i', 'n', 'd', 'p', 32, 0,
  /* 5103 */ 'x', 'v', 'm', 'i', 'n', 'd', 'p', 32, 0,
  /* 5112 */ 'x', 's', 'c', 'm', 'p', 'o', 'd', 'p', 32, 0,
  /* 5122 */ 'x', 's', 'c', 'v', 's', 'p', 'd', 'p', 32, 0,
  /* 5132 */ 'x', 'v', 'c', 'v', 's', 'p', 'd', 'p', 32, 0,
  /* 5142 */ 'x', 'v', 'c', 'm', 'p', 'e', 'q', 'd', 'p', 32, 0,
  /* 5153 */ 'x', 's', 'n', 'a', 'b', 's', 'd', 'p', 32, 0,
  /* 5163 */ 'x', 'v', 'n', 'a', 'b', 's', 'd', 'p', 32, 0,
  /* 5173 */ 'x', 's', 'a', 'b', 's', 'd', 'p', 32, 0,
  /* 5182 */ 'x', 'v', 'a', 'b', 's', 'd', 'p', 32, 0,
  /* 5191 */ 'x', 'v', 'c', 'm', 'p', 'g', 't', 'd', 'p', 32, 0,
  /* 5202 */ 'x', 's', 's', 'q', 'r', 't', 'd', 'p', 32, 0,
  /* 5212 */ 'x', 's', 't', 's', 'q', 'r', 't', 'd', 'p', 32, 0,
  /* 5223 */ 'x', 'v', 't', 's', 'q', 'r', 't', 'd', 'p', 32, 0,
  /* 5234 */ 'x', 'v', 's', 'q', 'r', 't', 'd', 'p', 32, 0,
  /* 5244 */ 'x', 's', 'c', 'm', 'p', 'u', 'd', 'p', 32, 0,
  /* 5254 */ 'x', 's', 'd', 'i', 'v', 'd', 'p', 32, 0,
  /* 5263 */ 'x', 's', 't', 'd', 'i', 'v', 'd', 'p', 32, 0,
  /* 5273 */ 'x', 'v', 't', 'd', 'i', 'v', 'd', 'p', 32, 0,
  /* 5283 */ 'x', 'v', 'd', 'i', 'v', 'd', 'p', 32, 0,
  /* 5292 */ 'x', 'v', 'c', 'v', 's', 'x', 'w', 'd', 'p', 32, 0,
  /* 5303 */ 'x', 'v', 'c', 'v', 'u', 'x', 'w', 'd', 'p', 32, 0,
  /* 5314 */ 'x', 's', 'm', 'a', 'x', 'd', 'p', 32, 0,
  /* 5323 */ 'x', 'v', 'm', 'a', 'x', 'd', 'p', 32, 0,
  /* 5332 */ 'v', 'c', 'm', 'p', 'b', 'f', 'p', 32, 0,
  /* 5341 */ 'v', 'n', 'm', 's', 'u', 'b', 'f', 'p', 32, 0,
  /* 5351 */ 'v', 's', 'u', 'b', 'f', 'p', 32, 0,
  /* 5359 */ 'v', 'm', 'a', 'd', 'd', 'f', 'p', 32, 0,
  /* 5368 */ 'v', 'a', 'd', 'd', 'f', 'p', 32, 0,
  /* 5376 */ 'v', 'l', 'o', 'g', 'e', 'f', 'p', 32, 0,
  /* 5385 */ 'v', 'c', 'm', 'p', 'g', 'e', 'f', 'p', 32, 0,
  /* 5395 */ 'v', 'r', 'e', 'f', 'p', 32, 0,
  /* 5402 */ 'v', 'e', 'x', 'p', 't', 'e', 'f', 'p', 32, 0,
  /* 5412 */ 'v', 'r', 's', 'q', 'r', 't', 'e', 'f', 'p', 32, 0,
  /* 5423 */ 'v', 'm', 'i', 'n', 'f', 'p', 32, 0,
  /* 5431 */ 'v', 'c', 'm', 'p', 'e', 'q', 'f', 'p', 32, 0,
  /* 5441 */ 'v', 'c', 'm', 'p', 'g', 't', 'f', 'p', 32, 0,
  /* 5451 */ 'v', 'm', 'a', 'x', 'f', 'p', 32, 0,
  /* 5459 */ 'v', 'r', 'f', 'i', 'p', 32, 0,
  /* 5466 */ 'x', 's', 'r', 'd', 'p', 'i', 'p', 32, 0,
  /* 5475 */ 'x', 'v', 'r', 'd', 'p', 'i', 'p', 32, 0,
  /* 5484 */ 'x', 'v', 'r', 's', 'p', 'i', 'p', 32, 0,
  /* 5493 */ 'q', 'v', 'f', 'r', 'i', 'p', 32, 0,
  /* 5501 */ 'x', 'v', 'n', 'm', 's', 'u', 'b', 'a', 's', 'p', 32, 0,
  /* 5513 */ 'x', 'v', 'm', 's', 'u', 'b', 'a', 's', 'p', 32, 0,
  /* 5524 */ 'x', 'v', 'n', 'm', 'a', 'd', 'd', 'a', 's', 'p', 32, 0,
  /* 5536 */ 'x', 'v', 'm', 'a', 'd', 'd', 'a', 's', 'p', 32, 0,
  /* 5547 */ 'x', 'v', 's', 'u', 'b', 's', 'p', 32, 0,
  /* 5556 */ 'x', 'v', 'a', 'd', 'd', 's', 'p', 32, 0,
  /* 5565 */ 'x', 'v', 'c', 'v', 's', 'x', 'd', 's', 'p', 32, 0,
  /* 5576 */ 'x', 'v', 'c', 'v', 'u', 'x', 'd', 's', 'p', 32, 0,
  /* 5587 */ 'x', 'v', 'c', 'm', 'p', 'g', 'e', 's', 'p', 32, 0,
  /* 5598 */ 'x', 'v', 'r', 'e', 's', 'p', 32, 0,
  /* 5606 */ 'x', 'v', 'r', 's', 'q', 'r', 't', 'e', 's', 'p', 32, 0,
  /* 5618 */ 'x', 'v', 'n', 'e', 'g', 's', 'p', 32, 0,
  /* 5627 */ 'x', 'v', 'm', 'u', 'l', 's', 'p', 32, 0,
  /* 5636 */ 'x', 'v', 'n', 'm', 's', 'u', 'b', 'm', 's', 'p', 32, 0,
  /* 5648 */ 'x', 'v', 'm', 's', 'u', 'b', 'm', 's', 'p', 32, 0,
  /* 5659 */ 'x', 'v', 'n', 'm', 'a', 'd', 'd', 'm', 's', 'p', 32, 0,
  /* 5671 */ 'x', 'v', 'm', 'a', 'd', 'd', 'm', 's', 'p', 32, 0,
  /* 5682 */ 'x', 'v', 'c', 'p', 's', 'g', 'n', 's', 'p', 32, 0,
  /* 5693 */ 'x', 'v', 'm', 'i', 'n', 's', 'p', 32, 0,
  /* 5702 */ 'x', 's', 'c', 'v', 'd', 'p', 's', 'p', 32, 0,
  /* 5712 */ 'x', 'v', 'c', 'v', 'd', 'p', 's', 'p', 32, 0,
  /* 5722 */ 'x', 'v', 'c', 'm', 'p', 'e', 'q', 's', 'p', 32, 0,
  /* 5733 */ 'q', 'v', 'f', 'r', 's', 'p', 32, 0,
  /* 5741 */ 'x', 'v', 'n', 'a', 'b', 's', 's', 'p', 32, 0,
  /* 5751 */ 'x', 'v', 'a', 'b', 's', 's', 'p', 32, 0,
  /* 5760 */ 'x', 'v', 'c', 'm', 'p', 'g', 't', 's', 'p', 32, 0,
  /* 5771 */ 'x', 'v', 't', 's', 'q', 'r', 't', 's', 'p', 32, 0,
  /* 5782 */ 'x', 'v', 's', 'q', 'r', 't', 's', 'p', 32, 0,
  /* 5792 */ 'x', 'v', 't', 'd', 'i', 'v', 's', 'p', 32, 0,
  /* 5802 */ 'x', 'v', 'd', 'i', 'v', 's', 'p', 32, 0,
  /* 5811 */ 'x', 'v', 'c', 'v', 's', 'x', 'w', 's', 'p', 32, 0,
  /* 5822 */ 'x', 'v', 'c', 'v', 'u', 'x', 'w', 's', 'p', 32, 0,
  /* 5833 */ 'x', 'v', 'm', 'a', 'x', 's', 'p', 32, 0,
  /* 5842 */ 'q', 'v', 'f', 'c', 'm', 'p', 'e', 'q', 32, 0,
  /* 5852 */ 'e', 'v', 'c', 'm', 'p', 'e', 'q', 32, 0,
  /* 5861 */ '#', 'T', 'C', '_', 'R', 'E', 'T', 'U', 'R', 'N', 'r', 32, 0,
  /* 5874 */ 'm', 'b', 'a', 'r', 32, 0,
  /* 5880 */ 'm', 'f', 'd', 'c', 'r', 32, 0,
  /* 5887 */ 'r', 'l', 'd', 'c', 'r', 32, 0,
  /* 5894 */ 'm', 't', 'd', 'c', 'r', 32, 0,
  /* 5901 */ 'm', 'f', 'c', 'r', 32, 0,
  /* 5907 */ 'r', 'l', 'd', 'i', 'c', 'r', 32, 0,
  /* 5915 */ 'm', 'f', 'v', 's', 'c', 'r', 32, 0,
  /* 5923 */ 'm', 't', 'v', 's', 'c', 'r', 32, 0,
  /* 5931 */ 'b', 'c', 'l', 'r', 32, 0,
  /* 5937 */ 'm', 'f', 'l', 'r', 32, 0,
  /* 5943 */ 'm', 't', 'l', 'r', 32, 0,
  /* 5949 */ 'q', 'v', 'f', 'm', 'r', 32, 0,
  /* 5956 */ 'x', 'x', 'l', 'o', 'r', 32, 0,
  /* 5963 */ 'x', 'x', 'l', 'n', 'o', 'r', 32, 0,
  /* 5971 */ 'c', 'r', 'n', 'o', 'r', 32, 0,
  /* 5978 */ 'e', 'v', 'n', 'o', 'r', 32, 0,
  /* 5985 */ 'c', 'r', 'o', 'r', 32, 0,
  /* 5991 */ 'e', 'v', 'o', 'r', 32, 0,
  /* 5997 */ 'x', 'x', 'l', 'x', 'o', 'r', 32, 0,
  /* 6005 */ 'c', 'r', 'x', 'o', 'r', 32, 0,
  /* 6012 */ 'e', 'v', 'x', 'o', 'r', 32, 0,
  /* 6019 */ 'm', 'f', 's', 'p', 'r', 32, 0,
  /* 6026 */ 'm', 't', 's', 'p', 'r', 32, 0,
  /* 6033 */ 'm', 'f', 's', 'r', 32, 0,
  /* 6039 */ 'm', 'f', 'm', 's', 'r', 32, 0,
  /* 6046 */ 'm', 't', 'm', 's', 'r', 32, 0,
  /* 6053 */ 'm', 't', 's', 'r', 32, 0,
  /* 6059 */ 'l', 'v', 's', 'r', 32, 0,
  /* 6065 */ 'b', 'c', 'c', 't', 'r', 32, 0,
  /* 6072 */ 'm', 'f', 'c', 't', 'r', 32, 0,
  /* 6079 */ 'm', 't', 'c', 't', 'r', 32, 0,
  /* 6086 */ 'q', 'v', 'f', 'a', 'b', 's', 32, 0,
  /* 6094 */ 'q', 'v', 'f', 'n', 'a', 'b', 's', 32, 0,
  /* 6103 */ 'e', 'v', 'a', 'b', 's', 32, 0,
  /* 6110 */ 'v', 's', 'u', 'm', '4', 's', 'b', 's', 32, 0,
  /* 6120 */ 'v', 's', 'u', 'b', 's', 'b', 's', 32, 0,
  /* 6129 */ 'v', 'a', 'd', 'd', 's', 'b', 's', 32, 0,
  /* 6138 */ 'v', 's', 'u', 'm', '4', 'u', 'b', 's', 32, 0,
  /* 6148 */ 'v', 's', 'u', 'b', 'u', 'b', 's', 32, 0,
  /* 6157 */ 'v', 'a', 'd', 'd', 'u', 'b', 's', 32, 0,
  /* 6166 */ 'q', 'v', 'f', 's', 'u', 'b', 's', 32, 0,
  /* 6175 */ 'q', 'v', 'f', 'm', 's', 'u', 'b', 's', 32, 0,
  /* 6185 */ 'q', 'v', 'f', 'n', 'm', 's', 'u', 'b', 's', 32, 0,
  /* 6196 */ 'q', 'v', 'f', 'a', 'd', 'd', 's', 32, 0,
  /* 6205 */ 'q', 'v', 'f', 'm', 'a', 'd', 'd', 's', 32, 0,
  /* 6215 */ 'q', 'v', 'f', 'n', 'm', 'a', 'd', 'd', 's', 32, 0,
  /* 6226 */ 'q', 'v', 'f', 'x', 'x', 'c', 'p', 'n', 'm', 'a', 'd', 'd', 's', 32, 0,
  /* 6241 */ 'q', 'v', 'f', 'x', 'x', 'n', 'p', 'm', 'a', 'd', 'd', 's', 32, 0,
  /* 6255 */ 'q', 'v', 'f', 'x', 'm', 'a', 'd', 'd', 's', 32, 0,
  /* 6266 */ 'q', 'v', 'f', 'x', 'x', 'm', 'a', 'd', 'd', 's', 32, 0,
  /* 6278 */ 'q', 'v', 'f', 'c', 'f', 'i', 'd', 's', 32, 0,
  /* 6288 */ 'x', 's', 'c', 'v', 'd', 'p', 's', 'x', 'd', 's', 32, 0,
  /* 6300 */ 'x', 'v', 'c', 'v', 'd', 'p', 's', 'x', 'd', 's', 32, 0,
  /* 6312 */ 'x', 'v', 'c', 'v', 's', 'p', 's', 'x', 'd', 's', 32, 0,
  /* 6324 */ 'x', 's', 'c', 'v', 'd', 'p', 'u', 'x', 'd', 's', 32, 0,
  /* 6336 */ 'x', 'v', 'c', 'v', 'd', 'p', 'u', 'x', 'd', 's', 32, 0,
  /* 6348 */ 'x', 'v', 'c', 'v', 's', 'p', 'u', 'x', 'd', 's', 32, 0,
  /* 6360 */ 'q', 'v', 'f', 'r', 'e', 's', 32, 0,
  /* 6368 */ 'q', 'v', 'f', 'r', 's', 'q', 'r', 't', 'e', 's', 32, 0,
  /* 6380 */ 'm', 'f', 'f', 's', 32, 0,
  /* 6386 */ 'l', 'f', 's', 32, 0,
  /* 6391 */ 'm', 'c', 'r', 'f', 's', 32, 0,
  /* 6398 */ 's', 't', 'f', 's', 32, 0,
  /* 6404 */ 'v', 's', 'u', 'm', '4', 's', 'h', 's', 32, 0,
  /* 6414 */ 'v', 's', 'u', 'b', 's', 'h', 's', 32, 0,
  /* 6423 */ 'v', 'm', 'h', 'a', 'd', 'd', 's', 'h', 's', 32, 0,
  /* 6434 */ 'v', 'm', 'h', 'r', 'a', 'd', 'd', 's', 'h', 's', 32, 0,
  /* 6446 */ 'v', 'a', 'd', 'd', 's', 'h', 's', 32, 0,
  /* 6455 */ 'v', 'm', 's', 'u', 'm', 's', 'h', 's', 32, 0,
  /* 6465 */ 'v', 's', 'u', 'b', 'u', 'h', 's', 32, 0,
  /* 6474 */ 'v', 'a', 'd', 'd', 'u', 'h', 's', 32, 0,
  /* 6483 */ 'v', 'm', 's', 'u', 'm', 'u', 'h', 's', 32, 0,
  /* 6493 */ 's', 'u', 'b', 'i', 's', 32, 0,
  /* 6500 */ 'a', 'd', 'd', 'i', 's', 32, 0,
  /* 6507 */ 'l', 'i', 's', 32, 0,
  /* 6512 */ 'x', 'o', 'r', 'i', 's', 32, 0,
  /* 6519 */ 'e', 'v', 's', 'r', 'w', 'i', 's', 32, 0,
  /* 6528 */ 'q', 'v', 'f', 'm', 'u', 'l', 's', 32, 0,
  /* 6537 */ 'q', 'v', 'f', 'x', 'm', 'u', 'l', 's', 32, 0,
  /* 6547 */ 'e', 'v', 'l', 'w', 'h', 'o', 's', 32, 0,
  /* 6556 */ 'd', 's', 's', 32, 0,
  /* 6561 */ 'v', 'p', 'k', 's', 'h', 's', 's', 32, 0,
  /* 6570 */ 'v', 'p', 'k', 's', 'w', 's', 's', 32, 0,
  /* 6579 */ 'e', 'v', 'c', 'm', 'p', 'g', 't', 's', 32, 0,
  /* 6589 */ 'e', 'v', 'c', 'm', 'p', 'l', 't', 's', 32, 0,
  /* 6599 */ 'f', 's', 'q', 'r', 't', 's', 32, 0,
  /* 6607 */ 'q', 'v', 'f', 'c', 'f', 'i', 'd', 'u', 's', 32, 0,
  /* 6618 */ 'v', 'p', 'k', 's', 'h', 'u', 's', 32, 0,
  /* 6627 */ 'v', 'p', 'k', 'u', 'h', 'u', 's', 32, 0,
  /* 6636 */ 'v', 'p', 'k', 's', 'w', 'u', 's', 32, 0,
  /* 6645 */ 'v', 'p', 'k', 'u', 'w', 'u', 's', 32, 0,
  /* 6654 */ 'f', 'd', 'i', 'v', 's', 32, 0,
  /* 6661 */ 'e', 'v', 's', 'r', 'w', 's', 32, 0,
  /* 6669 */ 'v', 's', 'u', 'm', '2', 's', 'w', 's', 32, 0,
  /* 6679 */ 'v', 's', 'u', 'b', 's', 'w', 's', 32, 0,
  /* 6688 */ 'v', 'a', 'd', 'd', 's', 'w', 's', 32, 0,
  /* 6697 */ 'v', 's', 'u', 'm', 's', 'w', 's', 32, 0,
  /* 6706 */ 'v', 's', 'u', 'b', 'u', 'w', 's', 32, 0,
  /* 6715 */ 'v', 'a', 'd', 'd', 'u', 'w', 's', 32, 0,
  /* 6724 */ 'e', 'v', 'd', 'i', 'v', 'w', 's', 32, 0,
  /* 6733 */ 'x', 's', 'c', 'v', 'd', 'p', 's', 'x', 'w', 's', 32, 0,
  /* 6745 */ 'x', 'v', 'c', 'v', 'd', 'p', 's', 'x', 'w', 's', 32, 0,
  /* 6757 */ 'x', 'v', 'c', 'v', 's', 'p', 's', 'x', 'w', 's', 32, 0,
  /* 6769 */ 'x', 's', 'c', 'v', 'd', 'p', 'u', 'x', 'w', 's', 32, 0,
  /* 6781 */ 'x', 'v', 'c', 'v', 'd', 'p', 'u', 'x', 'w', 's', 32, 0,
  /* 6793 */ 'x', 'v', 'c', 'v', 's', 'p', 'u', 'x', 'w', 's', 32, 0,
  /* 6805 */ 'v', 'c', 't', 's', 'x', 's', 32, 0,
  /* 6813 */ 'v', 'c', 't', 'u', 'x', 's', 32, 0,
  /* 6821 */ 'e', 'v', 'l', 'h', 'h', 'e', 's', 'p', 'l', 'a', 't', 32, 0,
  /* 6834 */ 'e', 'v', 'l', 'w', 'h', 's', 'p', 'l', 'a', 't', 32, 0,
  /* 6846 */ 'e', 'v', 'l', 'h', 'h', 'o', 's', 's', 'p', 'l', 'a', 't', 32, 0,
  /* 6860 */ 'e', 'v', 'l', 'h', 'h', 'o', 'u', 's', 'p', 'l', 'a', 't', 32, 0,
  /* 6874 */ 'e', 'v', 'l', 'w', 'w', 's', 'p', 'l', 'a', 't', 32, 0,
  /* 6886 */ 'd', 'c', 'b', 't', 32, 0,
  /* 6892 */ 'i', 'c', 'b', 't', 32, 0,
  /* 6898 */ 'q', 'v', 'f', 'c', 'm', 'p', 'g', 't', 32, 0,
  /* 6908 */ 'w', 'a', 'i', 't', 32, 0,
  /* 6914 */ 'q', 'v', 'f', 'c', 'm', 'p', 'l', 't', 32, 0,
  /* 6924 */ 'f', 's', 'q', 'r', 't', 32, 0,
  /* 6931 */ 'd', 'c', 'b', 's', 't', 32, 0,
  /* 6938 */ 'd', 's', 't', 32, 0,
  /* 6943 */ 'd', 'c', 'b', 't', 's', 't', 32, 0,
  /* 6951 */ 'd', 's', 't', 's', 't', 32, 0,
  /* 6958 */ 'd', 's', 't', 't', 32, 0,
  /* 6964 */ 'd', 's', 't', 's', 't', 't', 32, 0,
  /* 6972 */ 'l', 'h', 'a', 'u', 32, 0,
  /* 6978 */ 's', 't', 'b', 'u', 32, 0,
  /* 6984 */ 'l', 'f', 'd', 'u', 32, 0,
  /* 6990 */ 's', 't', 'f', 'd', 'u', 32, 0,
  /* 6997 */ 'm', 'u', 'l', 'h', 'd', 'u', 32, 0,
  /* 7005 */ 'q', 'v', 'f', 'c', 'f', 'i', 'd', 'u', 32, 0,
  /* 7015 */ 'q', 'v', 'f', 'c', 't', 'i', 'd', 'u', 32, 0,
  /* 7025 */ 'l', 'd', 'u', 32, 0,
  /* 7030 */ 's', 't', 'd', 'u', 32, 0,
  /* 7036 */ 'd', 'i', 'v', 'd', 'u', 32, 0,
  /* 7043 */ 's', 't', 'h', 'u', 32, 0,
  /* 7049 */ 'e', 'v', 's', 'r', 'w', 'i', 'u', 32, 0,
  /* 7058 */ 'e', 'v', 'l', 'w', 'h', 'o', 'u', 32, 0,
  /* 7067 */ 'f', 'c', 'm', 'p', 'u', 32, 0,
  /* 7074 */ 'l', 'f', 's', 'u', 32, 0,
  /* 7080 */ 's', 't', 'f', 's', 'u', 32, 0,
  /* 7087 */ 'e', 'v', 'c', 'm', 'p', 'g', 't', 'u', 32, 0,
  /* 7097 */ 'e', 'v', 'c', 'm', 'p', 'l', 't', 'u', 32, 0,
  /* 7107 */ 'm', 'u', 'l', 'h', 'w', 'u', 32, 0,
  /* 7115 */ 'q', 'v', 'f', 'c', 't', 'i', 'w', 'u', 32, 0,
  /* 7125 */ 'e', 'v', 's', 'r', 'w', 'u', 32, 0,
  /* 7133 */ 's', 't', 'w', 'u', 32, 0,
  /* 7139 */ 'e', 'v', 'd', 'i', 'v', 'w', 'u', 32, 0,
  /* 7148 */ 'l', 'b', 'z', 'u', 32, 0,
  /* 7154 */ 'l', 'h', 'z', 'u', 32, 0,
  /* 7160 */ 'l', 'w', 'z', 'u', 32, 0,
  /* 7166 */ 'f', 'd', 'i', 'v', 32, 0,
  /* 7172 */ 'x', 'x', 'l', 'e', 'q', 'v', 32, 0,
  /* 7180 */ 'c', 'r', 'e', 'q', 'v', 32, 0,
  /* 7187 */ 'e', 'v', 'e', 'q', 'v', 32, 0,
  /* 7194 */ 'e', 'v', 'm', 'h', 'e', 's', 'm', 'f', 'a', 'a', 'w', 32, 0,
  /* 7207 */ 'e', 'v', 'm', 'h', 'o', 's', 'm', 'f', 'a', 'a', 'w', 32, 0,
  /* 7220 */ 'e', 'v', 'm', 'h', 'e', 's', 's', 'f', 'a', 'a', 'w', 32, 0,
  /* 7233 */ 'e', 'v', 'm', 'h', 'o', 's', 's', 'f', 'a', 'a', 'w', 32, 0,
  /* 7246 */ 'e', 'v', 'a', 'd', 'd', 's', 'm', 'i', 'a', 'a', 'w', 32, 0,
  /* 7259 */ 'e', 'v', 'm', 'h', 'e', 's', 'm', 'i', 'a', 'a', 'w', 32, 0,
  /* 7272 */ 'e', 'v', 's', 'u', 'b', 'f', 's', 'm', 'i', 'a', 'a', 'w', 32, 0,
  /* 7286 */ 'e', 'v', 'm', 'w', 'l', 's', 'm', 'i', 'a', 'a', 'w', 32, 0,
  /* 7299 */ 'e', 'v', 'm', 'h', 'o', 's', 'm', 'i', 'a', 'a', 'w', 32, 0,
  /* 7312 */ 'e', 'v', 'a', 'd', 'd', 'u', 'm', 'i', 'a', 'a', 'w', 32, 0,
  /* 7325 */ 'e', 'v', 'm', 'h', 'e', 'u', 'm', 'i', 'a', 'a', 'w', 32, 0,
  /* 7338 */ 'e', 'v', 's', 'u', 'b', 'f', 'u', 'm', 'i', 'a', 'a', 'w', 32, 0,
  /* 7352 */ 'e', 'v', 'm', 'w', 'l', 'u', 'm', 'i', 'a', 'a', 'w', 32, 0,
  /* 7365 */ 'e', 'v', 'm', 'h', 'o', 'u', 'm', 'i', 'a', 'a', 'w', 32, 0,
  /* 7378 */ 'e', 'v', 'a', 'd', 'd', 's', 's', 'i', 'a', 'a', 'w', 32, 0,
  /* 7391 */ 'e', 'v', 'm', 'h', 'e', 's', 's', 'i', 'a', 'a', 'w', 32, 0,
  /* 7404 */ 'e', 'v', 's', 'u', 'b', 'f', 's', 's', 'i', 'a', 'a', 'w', 32, 0,
  /* 7418 */ 'e', 'v', 'm', 'w', 'l', 's', 's', 'i', 'a', 'a', 'w', 32, 0,
  /* 7431 */ 'e', 'v', 'm', 'h', 'o', 's', 's', 'i', 'a', 'a', 'w', 32, 0,
  /* 7444 */ 'e', 'v', 'a', 'd', 'd', 'u', 's', 'i', 'a', 'a', 'w', 32, 0,
  /* 7457 */ 'e', 'v', 'm', 'h', 'e', 'u', 's', 'i', 'a', 'a', 'w', 32, 0,
  /* 7470 */ 'e', 'v', 's', 'u', 'b', 'f', 'u', 's', 'i', 'a', 'a', 'w', 32, 0,
  /* 7484 */ 'e', 'v', 'm', 'w', 'l', 'u', 's', 'i', 'a', 'a', 'w', 32, 0,
  /* 7497 */ 'e', 'v', 'm', 'h', 'o', 'u', 's', 'i', 'a', 'a', 'w', 32, 0,
  /* 7510 */ 'v', 's', 'r', 'a', 'w', 32, 0,
  /* 7517 */ 'e', 'v', 'a', 'd', 'd', 'w', 32, 0,
  /* 7525 */ 'e', 'v', 'l', 'd', 'w', 32, 0,
  /* 7532 */ 'e', 'v', 'r', 'n', 'd', 'w', 32, 0,
  /* 7540 */ 'e', 'v', 's', 't', 'd', 'w', 32, 0,
  /* 7548 */ 'e', 'v', 's', 'u', 'b', 'f', 'w', 32, 0,
  /* 7557 */ 'e', 'v', 's', 'u', 'b', 'i', 'f', 'w', 32, 0,
  /* 7567 */ 'v', 'm', 'r', 'g', 'h', 'w', 32, 0,
  /* 7575 */ 'x', 'x', 'm', 'r', 'g', 'h', 'w', 32, 0,
  /* 7584 */ 'm', 'u', 'l', 'h', 'w', 32, 0,
  /* 7591 */ 'e', 'v', 'a', 'd', 'd', 'i', 'w', 32, 0,
  /* 7600 */ 'q', 'v', 'f', 'c', 't', 'i', 'w', 32, 0,
  /* 7609 */ 'v', 'm', 'r', 'g', 'l', 'w', 32, 0,
  /* 7617 */ 'x', 'x', 'm', 'r', 'g', 'l', 'w', 32, 0,
  /* 7626 */ 'm', 'u', 'l', 'l', 'w', 32, 0,
  /* 7633 */ 'c', 'm', 'p', 'l', 'w', 32, 0,
  /* 7640 */ 'e', 'v', 'r', 'l', 'w', 32, 0,
  /* 7647 */ 'e', 'v', 's', 'l', 'w', 32, 0,
  /* 7654 */ 'l', 'm', 'w', 32, 0,
  /* 7659 */ 's', 't', 'm', 'w', 32, 0,
  /* 7665 */ 'e', 'v', 'm', 'h', 'e', 's', 'm', 'f', 'a', 'n', 'w', 32, 0,
  /* 7678 */ 'e', 'v', 'm', 'h', 'o', 's', 'm', 'f', 'a', 'n', 'w', 32, 0,
  /* 7691 */ 'e', 'v', 'm', 'h', 'e', 's', 's', 'f', 'a', 'n', 'w', 32, 0,
  /* 7704 */ 'e', 'v', 'm', 'h', 'o', 's', 's', 'f', 'a', 'n', 'w', 32, 0,
  /* 7717 */ 'e', 'v', 'm', 'h', 'e', 's', 'm', 'i', 'a', 'n', 'w', 32, 0,
  /* 7730 */ 'e', 'v', 'm', 'w', 'l', 's', 'm', 'i', 'a', 'n', 'w', 32, 0,
  /* 7743 */ 'e', 'v', 'm', 'h', 'o', 's', 'm', 'i', 'a', 'n', 'w', 32, 0,
  /* 7756 */ 'e', 'v', 'm', 'h', 'e', 'u', 'm', 'i', 'a', 'n', 'w', 32, 0,
  /* 7769 */ 'e', 'v', 'm', 'w', 'l', 'u', 'm', 'i', 'a', 'n', 'w', 32, 0,
  /* 7782 */ 'e', 'v', 'm', 'h', 'o', 'u', 'm', 'i', 'a', 'n', 'w', 32, 0,
  /* 7795 */ 'e', 'v', 'm', 'h', 'e', 's', 's', 'i', 'a', 'n', 'w', 32, 0,
  /* 7808 */ 'e', 'v', 'm', 'w', 'l', 's', 's', 'i', 'a', 'n', 'w', 32, 0,
  /* 7821 */ 'e', 'v', 'm', 'h', 'o', 's', 's', 'i', 'a', 'n', 'w', 32, 0,
  /* 7834 */ 'e', 'v', 'm', 'h', 'e', 'u', 's', 'i', 'a', 'n', 'w', 32, 0,
  /* 7847 */ 'e', 'v', 'm', 'w', 'l', 'u', 's', 'i', 'a', 'n', 'w', 32, 0,
  /* 7860 */ 'e', 'v', 'm', 'h', 'o', 'u', 's', 'i', 'a', 'n', 'w', 32, 0,
  /* 7873 */ 'c', 'm', 'p', 'w', 32, 0,
  /* 7879 */ 'v', 's', 'r', 'w', 32, 0,
  /* 7885 */ 'v', 'm', 'u', 'l', 'e', 's', 'w', 32, 0,
  /* 7894 */ 'v', 'a', 'v', 'g', 's', 'w', 32, 0,
  /* 7902 */ 'v', 's', 'p', 'l', 't', 'i', 's', 'w', 32, 0,
  /* 7912 */ 'e', 'v', 'c', 'n', 't', 'l', 's', 'w', 32, 0,
  /* 7922 */ 'v', 'm', 'i', 'n', 's', 'w', 32, 0,
  /* 7930 */ 'v', 'm', 'u', 'l', 'o', 's', 'w', 32, 0,
  /* 7939 */ 'v', 'c', 'm', 'p', 'g', 't', 's', 'w', 32, 0,
  /* 7949 */ 'e', 'x', 't', 's', 'w', 32, 0,
  /* 7956 */ 'v', 'm', 'a', 'x', 's', 'w', 32, 0,
  /* 7964 */ 'v', 's', 'p', 'l', 't', 'w', 32, 0,
  /* 7972 */ 'x', 'x', 's', 'p', 'l', 't', 'w', 32, 0,
  /* 7981 */ 'v', 'p', 'o', 'p', 'c', 'n', 't', 'w', 32, 0,
  /* 7991 */ 's', 't', 'w', 32, 0,
  /* 7996 */ 'v', 's', 'u', 'b', 'c', 'u', 'w', 32, 0,
  /* 8005 */ 'v', 'a', 'd', 'd', 'c', 'u', 'w', 32, 0,
  /* 8014 */ 'v', 'm', 'u', 'l', 'e', 'u', 'w', 32, 0,
  /* 8023 */ 'v', 'a', 'v', 'g', 'u', 'w', 32, 0,
  /* 8031 */ 'v', 'm', 'i', 'n', 'u', 'w', 32, 0,
  /* 8039 */ 'v', 'm', 'u', 'l', 'o', 'u', 'w', 32, 0,
  /* 8048 */ 'v', 'c', 'm', 'p', 'e', 'q', 'u', 'w', 32, 0,
  /* 8058 */ 'v', 'c', 'm', 'p', 'g', 't', 'u', 'w', 32, 0,
  /* 8068 */ 'v', 'm', 'a', 'x', 'u', 'w', 32, 0,
  /* 8076 */ 'd', 'i', 'v', 'w', 32, 0,
  /* 8082 */ 'v', 'c', 'l', 'z', 'w', 32, 0,
  /* 8089 */ 'e', 'v', 'c', 'n', 't', 'l', 'z', 'w', 32, 0,
  /* 8099 */ 'l', 'x', 'v', 'd', '2', 'x', 32, 0,
  /* 8107 */ 's', 't', 'x', 'v', 'd', '2', 'x', 32, 0,
  /* 8116 */ 'l', 'x', 'v', 'w', '4', 'x', 32, 0,
  /* 8124 */ 's', 't', 'x', 'v', 'w', '4', 'x', 32, 0,
  /* 8133 */ 'l', 'h', 'a', 'x', 32, 0,
  /* 8139 */ 't', 'l', 'b', 'i', 'v', 'a', 'x', 32, 0,
  /* 8148 */ 'q', 'v', 'l', 'f', 'i', 'w', 'a', 'x', 32, 0,
  /* 8158 */ 'l', 'w', 'a', 'x', 32, 0,
  /* 8164 */ 'l', 'v', 'e', 'b', 'x', 32, 0,
  /* 8171 */ 's', 't', 'v', 'e', 'b', 'x', 32, 0,
  /* 8179 */ 's', 't', 'b', 'x', 32, 0,
  /* 8185 */ 'q', 'v', 'l', 'f', 'c', 'd', 'x', 32, 0,
  /* 8194 */ 'q', 'v', 's', 't', 'f', 'c', 'd', 'x', 32, 0,
  /* 8204 */ 'e', 'v', 'l', 'd', 'd', 'x', 32, 0,
  /* 8212 */ 'e', 'v', 's', 't', 'd', 'd', 'x', 32, 0,
  /* 8221 */ 'q', 'v', 'l', 'f', 'd', 'x', 32, 0,
  /* 8229 */ 'q', 'v', 's', 't', 'f', 'd', 'x', 32, 0,
  /* 8238 */ 'q', 'v', 'l', 'p', 'c', 'l', 'd', 'x', 32, 0,
  /* 8248 */ 'q', 'v', 'l', 'p', 'c', 'r', 'd', 'x', 32, 0,
  /* 8258 */ 'l', 'x', 's', 'd', 'x', 32, 0,
  /* 8265 */ 's', 't', 'x', 's', 'd', 'x', 32, 0,
  /* 8273 */ 's', 't', 'd', 'x', 32, 0,
  /* 8279 */ 'e', 'v', 'l', 'w', 'h', 'e', 'x', 32, 0,
  /* 8288 */ 'e', 'v', 's', 't', 'w', 'h', 'e', 'x', 32, 0,
  /* 8298 */ 'e', 'v', 's', 't', 'w', 'w', 'e', 'x', 32, 0,
  /* 8308 */ 'e', 'v', 'l', 'd', 'h', 'x', 32, 0,
  /* 8316 */ 'e', 'v', 's', 't', 'd', 'h', 'x', 32, 0,
  /* 8325 */ 'l', 'v', 'e', 'h', 'x', 32, 0,
  /* 8332 */ 's', 't', 'v', 'e', 'h', 'x', 32, 0,
  /* 8340 */ 's', 't', 'h', 'x', 32, 0,
  /* 8346 */ 's', 't', 'b', 'c', 'i', 'x', 32, 0,
  /* 8354 */ 'l', 'd', 'c', 'i', 'x', 32, 0,
  /* 8361 */ 's', 't', 'd', 'c', 'i', 'x', 32, 0,
  /* 8369 */ 's', 't', 'h', 'c', 'i', 'x', 32, 0,
  /* 8377 */ 's', 't', 'w', 'c', 'i', 'x', 32, 0,
  /* 8385 */ 'l', 'b', 'z', 'c', 'i', 'x', 32, 0,
  /* 8393 */ 'l', 'h', 'z', 'c', 'i', 'x', 32, 0,
  /* 8401 */ 'l', 'w', 'z', 'c', 'i', 'x', 32, 0,
  /* 8409 */ 'e', 'v', 's', 't', 'w', 'h', 'o', 'x', 32, 0,
  /* 8419 */ 'e', 'v', 's', 't', 'w', 'w', 'o', 'x', 32, 0,
  /* 8429 */ 'v', 'u', 'p', 'k', 'h', 'p', 'x', 32, 0,
  /* 8438 */ 'v', 'p', 'k', 'p', 'x', 32, 0,
  /* 8445 */ 'v', 'u', 'p', 'k', 'l', 'p', 'x', 32, 0,
  /* 8454 */ 'l', 'd', 'a', 'r', 'x', 32, 0,
  /* 8461 */ 'l', 'w', 'a', 'r', 'x', 32, 0,
  /* 8468 */ 'l', 'd', 'b', 'r', 'x', 32, 0,
  /* 8475 */ 's', 't', 'd', 'b', 'r', 'x', 32, 0,
  /* 8483 */ 'l', 'h', 'b', 'r', 'x', 32, 0,
  /* 8490 */ 's', 't', 'h', 'b', 'r', 'x', 32, 0,
  /* 8498 */ 'l', 'w', 'b', 'r', 'x', 32, 0,
  /* 8505 */ 's', 't', 'w', 'b', 'r', 'x', 32, 0,
  /* 8513 */ 't', 'l', 'b', 's', 'x', 32, 0,
  /* 8520 */ 'q', 'v', 'l', 'f', 'c', 's', 'x', 32, 0,
  /* 8529 */ 'q', 'v', 's', 't', 'f', 'c', 's', 'x', 32, 0,
  /* 8539 */ 'l', 'x', 'v', 'd', 's', 'x', 32, 0,
  /* 8547 */ 'v', 'c', 'f', 's', 'x', 32, 0,
  /* 8554 */ 'q', 'v', 'l', 'f', 's', 'x', 32, 0,
  /* 8562 */ 'q', 'v', 's', 't', 'f', 's', 'x', 32, 0,
  /* 8571 */ 'q', 'v', 'l', 'p', 'c', 'l', 's', 'x', 32, 0,
  /* 8581 */ 'e', 'v', 'l', 'w', 'h', 'o', 's', 'x', 32, 0,
  /* 8591 */ 'q', 'v', 'l', 'p', 'c', 'r', 's', 'x', 32, 0,
  /* 8601 */ 'e', 'v', 'l', 'h', 'h', 'e', 's', 'p', 'l', 'a', 't', 'x', 32, 0,
  /* 8615 */ 'e', 'v', 'l', 'w', 'h', 's', 'p', 'l', 'a', 't', 'x', 32, 0,
  /* 8628 */ 'e', 'v', 'l', 'h', 'h', 'o', 's', 's', 'p', 'l', 'a', 't', 'x', 32, 0,
  /* 8643 */ 'e', 'v', 'l', 'h', 'h', 'o', 'u', 's', 'p', 'l', 'a', 't', 'x', 32, 0,
  /* 8658 */ 'e', 'v', 'l', 'w', 'w', 's', 'p', 'l', 'a', 't', 'x', 32, 0,
  /* 8671 */ 'l', 'h', 'a', 'u', 'x', 32, 0,
  /* 8678 */ 'l', 'w', 'a', 'u', 'x', 32, 0,
  /* 8685 */ 's', 't', 'b', 'u', 'x', 32, 0,
  /* 8692 */ 'q', 'v', 'l', 'f', 'c', 'd', 'u', 'x', 32, 0,
  /* 8702 */ 'q', 'v', 's', 't', 'f', 'c', 'd', 'u', 'x', 32, 0,
  /* 8713 */ 'q', 'v', 'l', 'f', 'd', 'u', 'x', 32, 0,
  /* 8722 */ 'q', 'v', 's', 't', 'f', 'd', 'u', 'x', 32, 0,
  /* 8732 */ 'l', 'd', 'u', 'x', 32, 0,
  /* 8738 */ 's', 't', 'd', 'u', 'x', 32, 0,
  /* 8745 */ 'v', 'c', 'f', 'u', 'x', 32, 0,
  /* 8752 */ 's', 't', 'h', 'u', 'x', 32, 0,
  /* 8759 */ 'e', 'v', 'l', 'w', 'h', 'o', 'u', 'x', 32, 0,
  /* 8769 */ 'q', 'v', 'l', 'f', 'c', 's', 'u', 'x', 32, 0,
  /* 8779 */ 'q', 'v', 's', 't', 'f', 'c', 's', 'u', 'x', 32, 0,
  /* 8790 */ 'q', 'v', 'l', 'f', 's', 'u', 'x', 32, 0,
  /* 8799 */ 'q', 'v', 's', 't', 'f', 's', 'u', 'x', 32, 0,
  /* 8809 */ 's', 't', 'w', 'u', 'x', 32, 0,
  /* 8816 */ 'l', 'b', 'z', 'u', 'x', 32, 0,
  /* 8823 */ 'l', 'h', 'z', 'u', 'x', 32, 0,
  /* 8830 */ 'l', 'w', 'z', 'u', 'x', 32, 0,
  /* 8837 */ 'l', 'v', 'x', 32, 0,
  /* 8842 */ 's', 't', 'v', 'x', 32, 0,
  /* 8848 */ 'e', 'v', 'l', 'd', 'w', 'x', 32, 0,
  /* 8856 */ 'e', 'v', 's', 't', 'd', 'w', 'x', 32, 0,
  /* 8865 */ 'l', 'v', 'e', 'w', 'x', 32, 0,
  /* 8872 */ 's', 't', 'v', 'e', 'w', 'x', 32, 0,
  /* 8880 */ 'q', 'v', 's', 't', 'f', 'i', 'w', 'x', 32, 0,
  /* 8890 */ 's', 't', 'w', 'x', 32, 0,
  /* 8896 */ 'l', 'b', 'z', 'x', 32, 0,
  /* 8902 */ 'l', 'h', 'z', 'x', 32, 0,
  /* 8908 */ 'q', 'v', 'l', 'f', 'i', 'w', 'z', 'x', 32, 0,
  /* 8918 */ 'l', 'w', 'z', 'x', 32, 0,
  /* 8924 */ 'd', 'c', 'b', 'z', 32, 0,
  /* 8930 */ 'l', 'b', 'z', 32, 0,
  /* 8935 */ 'b', 'd', 'z', 32, 0,
  /* 8940 */ 'q', 'v', 'f', 'c', 't', 'i', 'd', 'z', 32, 0,
  /* 8950 */ 'l', 'h', 'z', 32, 0,
  /* 8955 */ 'v', 'r', 'f', 'i', 'z', 32, 0,
  /* 8962 */ 'x', 's', 'r', 'd', 'p', 'i', 'z', 32, 0,
  /* 8971 */ 'x', 'v', 'r', 'd', 'p', 'i', 'z', 32, 0,
  /* 8980 */ 'x', 'v', 'r', 's', 'p', 'i', 'z', 32, 0,
  /* 8989 */ 'q', 'v', 'f', 'r', 'i', 'z', 32, 0,
  /* 8997 */ 'b', 'd', 'n', 'z', 32, 0,
  /* 9003 */ 'q', 'v', 'f', 'c', 't', 'i', 'd', 'u', 'z', 32, 0,
  /* 9014 */ 'q', 'v', 'f', 'c', 't', 'i', 'w', 'u', 'z', 32, 0,
  /* 9025 */ 'q', 'v', 'f', 'c', 't', 'i', 'w', 'z', 32, 0,
  /* 9035 */ 'l', 'w', 'z', 32, 0,
  /* 9040 */ 'b', 'd', 'z', 'l', 'r', 'l', '+', 0,
  /* 9048 */ 'b', 'd', 'n', 'z', 'l', 'r', 'l', '+', 0,
  /* 9057 */ 'b', 'd', 'z', 'l', 'r', '+', 0,
  /* 9064 */ 'b', 'd', 'n', 'z', 'l', 'r', '+', 0,
  /* 9072 */ 'b', 'd', 'z', 'l', 'r', 'l', '-', 0,
  /* 9080 */ 'b', 'd', 'n', 'z', 'l', 'r', 'l', '-', 0,
  /* 9089 */ 'b', 'd', 'z', 'l', 'r', '-', 0,
  /* 9096 */ 'b', 'd', 'n', 'z', 'l', 'r', '-', 0,
  /* 9104 */ 'o', 'r', 'i', 32, '1', ',', 32, '1', ',', 32, '0', 0,
  /* 9116 */ 'o', 'r', 'i', 32, '2', ',', 32, '2', ',', 32, '0', 0,
  /* 9128 */ '#', 'A', 'D', 'D', 'I', 'S', 'd', 't', 'p', 'r', 'e', 'l', 'H', 'A', '3', '2', 0,
  /* 9145 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'S', 'U', 'B', '_', 'I', '3', '2', 0,
  /* 9166 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'A', 'D', 'D', '_', 'I', '3', '2', 0,
  /* 9187 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'N', 'A', 'N', 'D', '_', 'I', '3', '2', 0,
  /* 9209 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'A', 'N', 'D', '_', 'I', '3', '2', 0,
  /* 9230 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'S', 'W', 'A', 'P', '_', 'I', '3', '2', 0,
  /* 9247 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'X', 'O', 'R', '_', 'I', '3', '2', 0,
  /* 9268 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'O', 'R', '_', 'I', '3', '2', 0,
  /* 9288 */ '#', 'A', 'D', 'D', 'I', 't', 'l', 's', 'g', 'd', 'L', '3', '2', 0,
  /* 9302 */ '#', 'A', 'D', 'D', 'I', 't', 'l', 's', 'l', 'd', 'L', '3', '2', 0,
  /* 9316 */ '#', 'L', 'D', 'g', 'o', 't', 'T', 'p', 'r', 'e', 'l', 'L', '3', '2', 0,
  /* 9331 */ '#', 'A', 'D', 'D', 'I', 'd', 't', 'p', 'r', 'e', 'l', 'L', '3', '2', 0,
  /* 9346 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'L', 'O', 'N', 'G', 'J', 'M', 'P', '3', '2', 0,
  /* 9365 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'S', 'E', 'T', 'J', 'M', 'P', '3', '2', 0,
  /* 9383 */ '#', 'A', 'D', 'D', 'I', 't', 'l', 's', 'g', 'd', 'L', 'A', 'D', 'D', 'R', '3', '2', 0,
  /* 9401 */ '#', 'A', 'D', 'D', 'I', 't', 'l', 's', 'l', 'd', 'L', 'A', 'D', 'D', 'R', '3', '2', 0,
  /* 9419 */ 'G', 'E', 'T', 't', 'l', 's', 'l', 'd', 'A', 'D', 'D', 'R', '3', '2', 0,
  /* 9434 */ 'G', 'E', 'T', 't', 'l', 's', 'A', 'D', 'D', 'R', '3', '2', 0,
  /* 9447 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'S', 'U', 'B', '_', 'I', '6', '4', 0,
  /* 9468 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'A', 'D', 'D', '_', 'I', '6', '4', 0,
  /* 9489 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'N', 'A', 'N', 'D', '_', 'I', '6', '4', 0,
  /* 9511 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'S', 'W', 'A', 'P', '_', 'I', '6', '4', 0,
  /* 9528 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'C', 'M', 'P', '_', 'S', 'W', 'A', 'P', '_', 'I', '6', '4', 0,
  /* 9549 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'X', 'O', 'R', '_', 'I', '6', '4', 0,
  /* 9570 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'O', 'R', '_', 'I', '6', '4', 0,
  /* 9590 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'L', 'O', 'N', 'G', 'J', 'M', 'P', '6', '4', 0,
  /* 9609 */ '#', 'E', 'H', '_', 'S', 'J', 'L', 'J', '_', 'S', 'E', 'T', 'J', 'M', 'P', '6', '4', 0,
  /* 9627 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'A', 'N', 'D', '_', 'i', '6', '4', 0,
  /* 9648 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'C', 'C', '_', 'F', '4', 0,
  /* 9662 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'F', '4', 0,
  /* 9673 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'C', 'C', '_', 'I', '4', 0,
  /* 9687 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'I', '4', 0,
  /* 9698 */ 'c', 'r', 'x', 'o', 'r', 32, '6', ',', 32, '6', ',', 32, '6', 0,
  /* 9712 */ 'c', 'r', 'e', 'q', 'v', 32, '6', ',', 32, '6', ',', 32, '6', 0,
  /* 9726 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'S', 'U', 'B', '_', 'I', '1', '6', 0,
  /* 9747 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'A', 'D', 'D', '_', 'I', '1', '6', 0,
  /* 9768 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'N', 'A', 'N', 'D', '_', 'I', '1', '6', 0,
  /* 9790 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'A', 'N', 'D', '_', 'I', '1', '6', 0,
  /* 9811 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'S', 'W', 'A', 'P', '_', 'I', '1', '6', 0,
  /* 9828 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'X', 'O', 'R', '_', 'I', '1', '6', 0,
  /* 9849 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'O', 'R', '_', 'I', '1', '6', 0,
  /* 9869 */ '#', 'D', 'Y', 'N', 'A', 'L', 'L', 'O', 'C', '8', 0,
  /* 9880 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'C', 'C', '_', 'F', '8', 0,
  /* 9894 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'F', '8', 0,
  /* 9905 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'S', 'U', 'B', '_', 'I', '8', 0,
  /* 9925 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'C', 'C', '_', 'I', '8', 0,
  /* 9939 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'A', 'D', 'D', '_', 'I', '8', 0,
  /* 9959 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'N', 'A', 'N', 'D', '_', 'I', '8', 0,
  /* 9980 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'A', 'N', 'D', '_', 'I', '8', 0,
  /* 10000 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'C', 'M', 'P', '_', 'S', 'W', 'A', 'P', '_', 'I', '8', 0,
  /* 10020 */ 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'X', 'O', 'R', '_', 'I', '8', 0,
  /* 10039 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'L', 'O', 'A', 'D', '_', 'O', 'R', '_', 'I', '8', 0,
  /* 10058 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'I', '8', 0,
  /* 10069 */ '#', 'M', 'o', 'v', 'e', 'P', 'C', 't', 'o', 'L', 'R', '8', 0,
  /* 10082 */ '#', 'A', 'N', 'D', 'I', 'o', '_', '1', '_', 'E', 'Q', '_', 'B', 'I', 'T', '8', 0,
  /* 10099 */ '#', 'A', 'N', 'D', 'I', 'o', '_', '1', '_', 'G', 'T', '_', 'B', 'I', 'T', '8', 0,
  /* 10116 */ '#', 'A', 'T', 'O', 'M', 'I', 'C', '_', 'S', 'W', 'A', 'P', '_', 'i', '8', 0,
  /* 10132 */ '#', 'A', 'D', 'D', 'I', 'S', 't', 'o', 'c', 'H', 'A', 0,
  /* 10144 */ '#', 'A', 'D', 'D', 'I', 'S', 't', 'l', 's', 'g', 'd', 'H', 'A', 0,
  /* 10158 */ '#', 'A', 'D', 'D', 'I', 'S', 't', 'l', 's', 'l', 'd', 'H', 'A', 0,
  /* 10172 */ '#', 'A', 'D', 'D', 'I', 'S', 'g', 'o', 't', 'T', 'p', 'r', 'e', 'l', 'H', 'A', 0,
  /* 10189 */ '#', 'A', 'D', 'D', 'I', 'S', 'd', 't', 'p', 'r', 'e', 'l', 'H', 'A', 0,
  /* 10204 */ '#', 'R', 'e', 'a', 'd', 'T', 'B', 0,
  /* 10212 */ '#', 'D', 'Y', 'N', 'A', 'L', 'L', 'O', 'C', 0,
  /* 10222 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'C', 'C', '_', 'Q', 'B', 'R', 'C', 0,
  /* 10238 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'Q', 'B', 'R', 'C', 0,
  /* 10251 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'C', 'C', '_', 'Q', 'F', 'R', 'C', 0,
  /* 10267 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'Q', 'F', 'R', 'C', 0,
  /* 10280 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'C', 'C', '_', 'V', 'S', 'F', 'R', 'C', 0,
  /* 10297 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'V', 'S', 'F', 'R', 'C', 0,
  /* 10311 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'C', 'C', '_', 'V', 'R', 'R', 'C', 0,
  /* 10327 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'V', 'R', 'R', 'C', 0,
  /* 10340 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'C', 'C', '_', 'Q', 'S', 'R', 'C', 0,
  /* 10356 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'Q', 'S', 'R', 'C', 0,
  /* 10369 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'C', 'C', '_', 'V', 'S', 'R', 'C', 0,
  /* 10385 */ '#', 'S', 'E', 'L', 'E', 'C', 'T', '_', 'V', 'S', 'R', 'C', 0,
  /* 10398 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'E', 'N', 'D', 0,
  /* 10411 */ 'B', 'U', 'N', 'D', 'L', 'E', 0,
  /* 10418 */ 'D', 'B', 'G', '_', 'V', 'A', 'L', 'U', 'E', 0,
  /* 10428 */ '#', 'R', 'E', 'S', 'T', 'O', 'R', 'E', '_', 'V', 'R', 'S', 'A', 'V', 'E', 0,
  /* 10444 */ '#', 'S', 'P', 'I', 'L', 'L', '_', 'V', 'R', 'S', 'A', 'V', 'E', 0,
  /* 10458 */ '#', 'L', 'D', 't', 'o', 'c', 'J', 'T', 'I', 0,
  /* 10468 */ '#', 'L', 'D', 't', 'o', 'c', 'L', 0,
  /* 10476 */ '#', 'A', 'D', 'D', 'I', 't', 'o', 'c', 'L', 0,
  /* 10486 */ '#', 'A', 'D', 'D', 'I', 't', 'l', 's', 'g', 'd', 'L', 0,
  /* 10498 */ '#', 'A', 'D', 'D', 'I', 't', 'l', 's', 'l', 'd', 'L', 0,
  /* 10510 */ '#', 'L', 'D', 'g', 'o', 't', 'T', 'p', 'r', 'e', 'l', 'L', 0,
  /* 10523 */ '#', 'A', 'D', 'D', 'I', 'd', 't', 'p', 'r', 'e', 'l', 'L', 0,
  /* 10536 */ '#', 'U', 'p', 'd', 'a', 't', 'e', 'G', 'B', 'R', 0,
  /* 10547 */ '#', 'R', 'E', 'S', 'T', 'O', 'R', 'E', '_', 'C', 'R', 0,
  /* 10559 */ '#', 'S', 'P', 'I', 'L', 'L', '_', 'C', 'R', 0,
  /* 10569 */ '#', 'A', 'D', 'D', 'I', 't', 'l', 's', 'g', 'd', 'L', 'A', 'D', 'D', 'R', 0,
  /* 10585 */ '#', 'A', 'D', 'D', 'I', 't', 'l', 's', 'l', 'd', 'L', 'A', 'D', 'D', 'R', 0,
  /* 10601 */ '#', 'G', 'E', 'T', 't', 'l', 's', 'l', 'd', 'A', 'D', 'D', 'R', 0,
  /* 10615 */ '#', 'G', 'E', 'T', 't', 'l', 's', 'A', 'D', 'D', 'R', 0,
  /* 10627 */ '#', 'M', 'o', 'v', 'e', 'P', 'C', 't', 'o', 'L', 'R', 0,
  /* 10639 */ '#', 'M', 'o', 'v', 'e', 'G', 'O', 'T', 't', 'o', 'L', 'R', 0,
  /* 10652 */ '#', 'R', 'E', 'S', 'T', 'O', 'R', 'E', '_', 'C', 'R', 'B', 'I', 'T', 0,
  /* 10667 */ '#', 'S', 'P', 'I', 'L', 'L', '_', 'C', 'R', 'B', 'I', 'T', 0,
  /* 10680 */ '#', 'A', 'N', 'D', 'I', 'o', '_', '1', '_', 'E', 'Q', '_', 'B', 'I', 'T', 0,
  /* 10696 */ '#', 'A', 'N', 'D', 'I', 'o', '_', '1', '_', 'G', 'T', '_', 'B', 'I', 'T', 0,
  /* 10712 */ '#', 'P', 'P', 'C', '3', '2', 'G', 'O', 'T', 0,
  /* 10722 */ '#', 'P', 'P', 'C', '3', '2', 'P', 'I', 'C', 'G', 'O', 'T', 0,
  /* 10735 */ '#', 'L', 'D', 't', 'o', 'c', 'C', 'P', 'T', 0,
  /* 10745 */ 'L', 'I', 'F', 'E', 'T', 'I', 'M', 'E', '_', 'S', 'T', 'A', 'R', 'T', 0,
  /* 10760 */ 's', 'l', 'b', 'i', 'a', 0,
  /* 10766 */ 't', 'l', 'b', 'i', 'a', 0,
  /* 10772 */ 'b', 0,
  /* 10774 */ 't', 'l', 'b', 's', 'y', 'n', 'c', 0,
  /* 10782 */ 'i', 's', 'y', 'n', 'c', 0,
  /* 10788 */ 'm', 's', 'y', 'n', 'c', 0,
  /* 10794 */ '#', 'L', 'D', 't', 'o', 'c', 0,
  /* 10801 */ '#', 'L', 'W', 'Z', 't', 'o', 'c', 0,
  /* 10809 */ 'r', 'f', 'i', 'd', 0,
  /* 10814 */ 't', 'l', 'b', 'r', 'e', 0,
  /* 10820 */ 't', 'l', 'b', 'w', 'e', 0,
  /* 10826 */ 'r', 'f', 'c', 'i', 0,
  /* 10831 */ 'r', 'f', 'm', 'c', 'i', 0,
  /* 10837 */ 'r', 'f', 'd', 'i', 0,
  /* 10842 */ 'r', 'f', 'i', 0,
  /* 10846 */ 'd', 's', 's', 'a', 'l', 'l', 0,
  /* 10853 */ 'b', 'l', 'r', 'l', 0,
  /* 10858 */ 'b', 'd', 'z', 'l', 'r', 'l', 0,
  /* 10865 */ 'b', 'd', 'n', 'z', 'l', 'r', 'l', 0,
  /* 10873 */ 'b', 'c', 't', 'r', 'l', 0,
  /* 10879 */ 'a', 't', 't', 'n', 0,
  /* 10884 */ 'e', 'i', 'e', 'i', 'o', 0,
  /* 10890 */ 't', 'r', 'a', 'p', 0,
  /* 10895 */ 'n', 'o', 'p', 0,
  /* 10899 */ 'b', 'l', 'r', 0,
  /* 10903 */ 'b', 'd', 'z', 'l', 'r', 0,
  /* 10909 */ 'b', 'd', 'n', 'z', 'l', 'r', 0,
  /* 10916 */ 'b', 'c', 't', 'r', 0,
  };
#endif

  // Emit the opcode for the instruction.
  unsigned int opcode = MCInst_getOpcode(MI);
  uint64_t Bits1 = OpInfo[opcode];
  uint64_t Bits2 = OpInfo2[opcode];
  uint64_t Bits = (Bits2 << 32) | Bits1;
  // assert(Bits != 0 && "Cannot print this instruction.");
#ifndef CAPSTONE_DIET
  SStream_concat0(O, AsmStrs+(Bits & 16383)-1);
#endif

  // Fragment 0 encoded into 4 bits for 14 unique commands.
  //printf("Frag-0: %"PRIu64"\n", (Bits >> 14) & 15);
  switch ((Bits >> 14) & 15) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // DBG_VALUE, BUNDLE, LIFETIME_START, LIFETIME_END, ADDISdtprelHA, ADDISd...
    return;
    break;
  case 1:
    // ADD4, ADD4TLS, ADD4o, ADD8, ADD8TLS, ADD8TLS_, ADD8o, ADDC, ADDC8, ADD...
    printOperand(MI, 0, O); 
    break;
  case 2:
    // ADJCALLSTACKDOWN, ADJCALLSTACKUP
    printU16ImmOperand(MI, 0, O); 
    break;
  case 3:
    // B, BCLalways, BDNZ, BDNZ8, BDNZL, BDNZLm, BDNZLp, BDNZm, BDNZp, BDZ, B...
    printBranchOperand(MI, 0, O); 
    break;
  case 4:
    // BA, BDNZA, BDNZAm, BDNZAp, BDNZLA, BDNZLAm, BDNZLAp, BDZA, BDZAm, BDZA...
    printAbsBranchOperand(MI, 0, O); 
    break;
  case 5:
    // BCC, BCCA, BCCCTR, BCCCTR8, BCCCTRL, BCCCTRL8, BCCL, BCCLA, BCCLR, BCC...
    printPredicateOperand(MI, 0, O, "cc"); 
    break;
  case 6:
    // BCTRL8_LDinto_toc
    printMemRegImm(MI, 0, O); 
    return;
    break;
  case 7:
    // BL8_NOP_TLS, BL8_TLS, BL8_TLS_, BL_TLS
    printTLSCall(MI, 0, O); 
    break;
  case 8:
    // DCBA, DCBF, DCBI, DCBST, DCBT, DCBTST, DCBZ, DCBZL, ICBI
    printMemRegReg(MI, 0, O); 
    return;
    break;
  case 9:
    // DSS, MBAR, MTFSB0, MTFSB1, TD, TDI, TW, TWI, gBC, gBCA, gBCCTR, gBCCTR...
    printU5ImmOperand(MI, 0, O); 
    break;
  case 10:
    // DST, DST64, DSTST, DSTST64, DSTSTT, DSTSTT64, DSTT, DSTT64, MTDCR, MTV...
    printOperand(MI, 1, O); 
    break;
  case 11:
    // ICBT
    printU4ImmOperand(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printMemRegReg(MI, 1, O); 
    return;
    break;
  case 12:
    // MTOCRF, MTOCRF8
    printcrbitm(MI, 0, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 13:
    // MTSR
    printU4ImmOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 0, O); 
    return;
    break;
  }


  // Fragment 1 encoded into 5 bits for 18 unique commands.
  //printf("Frag-1: %"PRIu64"\n", (Bits >> 18) & 31);
  switch ((Bits >> 18) & 31) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // ADD4, ADD4TLS, ADD4o, ADD8, ADD8TLS, ADD8TLS_, ADD8o, ADDC, ADDC8, ADD...
    SStream_concat0(O, ", "); 
    break;
  case 1:
    // ADJCALLSTACKDOWN, B, BA, BCLalways, BDNZ, BDNZ8, BDNZA, BDNZAm, BDNZAp...
    return;
    break;
  case 2:
    // ADJCALLSTACKUP, ATOMIC_CMP_SWAP_I16, ATOMIC_CMP_SWAP_I32, TCRETURNai, ...
    SStream_concat0(O, " "); 
    break;
  case 3:
    // BCC
    printPredicateOperand(MI, 0, O, "pm"); 
    SStream_concat0(O, " "); 
    printPredicateOperand(MI, 0, O, "reg"); 
    SStream_concat0(O, ", "); 
    printBranchOperand(MI, 2, O); 
    return;
    break;
  case 4:
    // BCCA
    SStream_concat0(O, "a"); 
    printPredicateOperand(MI, 0, O, "pm"); 
    SStream_concat0(O, " "); 
    printPredicateOperand(MI, 0, O, "reg"); 
    SStream_concat0(O, ", "); 
    printAbsBranchOperand(MI, 2, O); 
    return;
    break;
  case 5:
    // BCCCTR, BCCCTR8
    SStream_concat0(O, "ctr"); 
    printPredicateOperand(MI, 0, O, "pm"); 
    SStream_concat0(O, " "); 
    printPredicateOperand(MI, 0, O, "reg"); 
    return;
    break;
  case 6:
    // BCCCTRL, BCCCTRL8
    SStream_concat0(O, "ctrl"); 
    printPredicateOperand(MI, 0, O, "pm"); 
    SStream_concat0(O, " "); 
    printPredicateOperand(MI, 0, O, "reg"); 
    return;
    break;
  case 7:
    // BCCL
    SStream_concat0(O, "l"); 
    printPredicateOperand(MI, 0, O, "pm"); 
    SStream_concat0(O, " "); 
    printPredicateOperand(MI, 0, O, "reg"); 
    SStream_concat0(O, ", "); 
    printBranchOperand(MI, 2, O); 
    return;
    break;
  case 8:
    // BCCLA
    SStream_concat0(O, "la"); 
    printPredicateOperand(MI, 0, O, "pm"); 
    SStream_concat0(O, " "); 
    printPredicateOperand(MI, 0, O, "reg"); 
    SStream_concat0(O, ", "); 
    printAbsBranchOperand(MI, 2, O); 
    return;
    break;
  case 9:
    // BCCLR
    SStream_concat0(O, "lr"); 
    printPredicateOperand(MI, 0, O, "pm"); 
    SStream_concat0(O, " "); 
    printPredicateOperand(MI, 0, O, "reg"); 
    return;
    break;
  case 10:
    // BCCLRL
    SStream_concat0(O, "lrl"); 
    printPredicateOperand(MI, 0, O, "pm"); 
    SStream_concat0(O, " "); 
    printPredicateOperand(MI, 0, O, "reg"); 
    return;
    break;
  case 11:
    // BCCTR, BCCTR8, BCCTR8n, BCCTRL, BCCTRL8, BCCTRL8n, BCCTRLn, BCCTRn, BC...
    SStream_concat0(O, ", 0"); 
    return;
    break;
  case 12:
    // BL8_NOP, BL8_NOP_TLS, BLA8_NOP
    SStream_concat0(O, "\n\tnop"); 	// qq
    return;
    break;
  case 13:
    // MFTB8
    SStream_concat0(O, ", 268"); 
    op_addImm(MI, 268);
    return;
    break;
  case 14:
    // MFVRSAVE, MFVRSAVEv
    SStream_concat0(O, ", 256"); 
    op_addImm(MI, 256);
    return;
    break;
  case 15:
    // QVLPCLSXint
    SStream_concat0(O, ", 0, "); 
    op_addImm(MI, 0);
    printOperand(MI, 1, O); 
    return;
    break;
  case 16:
    // TLBIE
    SStream_concat0(O, ","); 
    printOperand(MI, 0, O); 
    return;
    break;
  case 17:
    // V_SETALLONES, V_SETALLONESB, V_SETALLONESH
    SStream_concat0(O, ", -1"); 
    op_addImm(MI, -1);
    return;
    break;
  }


  // Fragment 2 encoded into 5 bits for 17 unique commands.
  //printf("Frag-2: %"PRIu64"\n", (Bits >> 23) & 31);
  switch ((Bits >> 23) & 31) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // ADD4, ADD4TLS, ADD4o, ADD8, ADD8TLS, ADD8TLS_, ADD8o, ADDC, ADDC8, ADD...
    printOperand(MI, 1, O); 
    break;
  case 1:
    // ADJCALLSTACKUP
    printU16ImmOperand(MI, 1, O); 
    return;
    break;
  case 2:
    // ATOMIC_CMP_SWAP_I16, ATOMIC_CMP_SWAP_I32, LBZX, LBZX8, LDARX, LDBRX, L...
    printMemRegReg(MI, 1, O); 
    break;
  case 3:
    // BC, BCL, BCLn, BCn
    printBranchOperand(MI, 1, O); 
    return;
    break;
  case 4:
    // CRSET, CRUNSET, MTDCR, V_SET0, V_SET0B, V_SET0H
    printOperand(MI, 0, O); 
    break;
  case 5:
    // DST, DST64, DSTST, DSTST64, DSTSTT, DSTSTT64, DSTT, DSTT64, RLDIMI, RL...
    printOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    break;
  case 6:
    // EVADDIW
    printU5ImmOperand(MI, 2, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 1, O); 
    return;
    break;
  case 7:
    // EVLDD, EVLDH, EVLDW, EVLHHESPLAT, EVLHHOSSPLAT, EVLHHOUSPLAT, EVLWHE, ...
    printMemRegImm(MI, 1, O); 
    return;
    break;
  case 8:
    // EVSUBIFW
    printU5ImmOperand(MI, 1, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    return;
    break;
  case 9:
    // LA
    printS16ImmOperand(MI, 2, O); 
    SStream_concat0(O, "("); 
    printOperand(MI, 1, O); 
    SStream_concat0(O, ")"); 
    return;
    break;
  case 10:
    // LBZU, LBZU8, LDU, LFDU, LFSU, LHAU, LHAU8, LHZU, LHZU8, LWZU, LWZU8, S...
    printMemRegImm(MI, 2, O); 
    return;
    break;
  case 11:
    // LBZUX, LBZUX8, LDUX, LFDUX, LFSUX, LHAUX, LHAUX8, LHZUX, LHZUX8, LWAUX...
    printMemRegReg(MI, 2, O); 
    return;
    break;
  case 12:
    // LI, LI8, LIS, LIS8
    printS16ImmOperand(MI, 1, O); 
    return;
    break;
  case 13:
    // MFOCRF, MFOCRF8
    printcrbitm(MI, 1, O); 
    return;
    break;
  case 14:
    // MFSR
    printU4ImmOperand(MI, 1, O); 
    return;
    break;
  case 15:
    // QVGPCI
    printU12ImmOperand(MI, 1, O); 
    return;
    break;
  case 16:
    // VSPLTISB, VSPLTISH, VSPLTISW
    printS5ImmOperand(MI, 1, O); 
    return;
    break;
  }


  // Fragment 3 encoded into 4 bits for 9 unique commands.
  //printf("Frag-3: %"PRIu64"\n", (Bits >> 28) & 15);
  switch ((Bits >> 28) & 15) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // ADD4, ADD4TLS, ADD4o, ADD8, ADD8TLS, ADD8TLS_, ADD8o, ADDC, ADDC8, ADD...
    SStream_concat0(O, ", "); 
    break;
  case 1:
    // ADDME, ADDME8, ADDME8o, ADDMEo, ADDZE, ADDZE8, ADDZE8o, ADDZEo, CNTLZD...
    return;
    break;
  case 2:
    // ATOMIC_CMP_SWAP_I16, ATOMIC_CMP_SWAP_I32
    SStream_concat0(O, " "); 
    printOperand(MI, 3, O); 
    SStream_concat0(O, " "); 
    printOperand(MI, 4, O); 
    return;
    break;
  case 3:
    // DST, DST64, DSTST, DSTST64, DSTSTT, DSTSTT64, DSTT, DSTT64
    printU5ImmOperand(MI, 0, O); 
    return;
    break;
  case 4:
    // RLDIMI, RLDIMIo
    printU6ImmOperand(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printU6ImmOperand(MI, 4, O); 
    return;
    break;
  case 5:
    // RLWIMI, RLWIMI8, RLWIMI8o, RLWIMIo
    printU5ImmOperand(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printU5ImmOperand(MI, 4, O); 
    SStream_concat0(O, ", "); 
    printU5ImmOperand(MI, 5, O); 
    return;
    break;
  case 6:
    // VCFSX, VCFUX, VCTSXS, VCTUXS, VSPLTB, VSPLTH, VSPLTW
    printU5ImmOperand(MI, 1, O); 
    return;
    break;
  case 7:
    // VCFSX_0, VCFUX_0, VCTSXS_0, VCTUXS_0
    SStream_concat0(O, ", 0"); 
    return;
    break;
  case 8:
    // XSMADDADP, XSMADDMDP, XSMSUBADP, XSMSUBMDP, XSNMADDADP, XSNMADDMDP, XS...
    printOperand(MI, 3, O); 
    return;
    break;
  }


  // Fragment 4 encoded into 4 bits for 10 unique commands.
  //printf("Frag-4: %"PRIu64"\n", (Bits >> 32) & 15);
  switch ((Bits >> 32) & 15) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // ADD4, ADD4TLS, ADD4o, ADD8, ADD8TLS, ADD8TLS_, ADD8o, ADDC, ADDC8, ADD...
    printOperand(MI, 2, O); 
    break;
  case 1:
    // ADDI, ADDI8, ADDIC, ADDIC8, ADDICo, ADDIS, ADDIS8, CMPDI, CMPWI, MULLI...
    printS16ImmOperand(MI, 2, O); 
    return;
    break;
  case 2:
    // ANDISo, ANDISo8, ANDIo, ANDIo8, CMPLDI, CMPLWI, ORI, ORI8, ORIS, ORIS8...
    printU16ImmOperand(MI, 2, O); 
    return;
    break;
  case 3:
    // CLRLSLDI, CLRLSLDIo, CLRRDI, CLRRDIo, EXTLDI, EXTLDIo, EXTRDI, EXTRDIo...
    printU6ImmOperand(MI, 2, O); 
    break;
  case 4:
    // CLRLSLWI, CLRLSLWIo, CLRRWI, CLRRWIo, EVRLWI, EVSLWI, EVSRWIS, EVSRWIU...
    printU5ImmOperand(MI, 2, O); 
    break;
  case 5:
    // CRSET, CRUNSET, V_SET0, V_SET0B, V_SET0H
    printOperand(MI, 0, O); 
    return;
    break;
  case 6:
    // QVESPLATI, QVESPLATIb, QVESPLATIs, XXSPLTW
    printU2ImmOperand(MI, 2, O); 
    return;
    break;
  case 7:
    // QVFMADD, QVFMADDS, QVFMADDSs, QVFMSUB, QVFMSUBS, QVFMSUBSs, QVFNMADD, ...
    printOperand(MI, 3, O); 
    SStream_concat0(O, ", "); 
    printOperand(MI, 2, O); 
    return;
    break;
  case 8:
    // gBC, gBCL
    printBranchOperand(MI, 2, O); 
    return;
    break;
  case 9:
    // gBCA, gBCLA
    printAbsBranchOperand(MI, 2, O); 
    return;
    break;
  }


  // Fragment 5 encoded into 1 bits for 2 unique commands.
  //printf("Frag-5: %"PRIu64"\n", (Bits >> 36) & 1);
  if ((Bits >> 36) & 1) {
    // CLRLSLDI, CLRLSLDIo, CLRLSLWI, CLRLSLWIo, EXTLDI, EXTLDIo, EXTLWI, EXT...
    SStream_concat0(O, ", "); 
  } else {
    // ADD4, ADD4TLS, ADD4o, ADD8, ADD8TLS, ADD8TLS_, ADD8o, ADDC, ADDC8, ADD...
    return;
  }


  // Fragment 6 encoded into 3 bits for 5 unique commands.
  //printf("Frag-6: %"PRIu64"\n", (Bits >> 37) & 7);
  switch ((Bits >> 37) & 7) {
  default: // llvm_unreachable("Invalid command number.");
  case 0:
    // CLRLSLDI, CLRLSLDIo, EXTLDI, EXTLDIo, EXTRDI, EXTRDIo, INSRDI, INSRDIo...
    printU6ImmOperand(MI, 3, O); 
    return;
    break;
  case 1:
    // CLRLSLWI, CLRLSLWIo, EXTLWI, EXTLWIo, EXTRWI, EXTRWIo, INSLWI, INSLWIo...
    printU5ImmOperand(MI, 3, O); 
    break;
  case 2:
    // FMADD, FMADDS, FMADDSo, FMADDo, FMSUB, FMSUBS, FMSUBSo, FMSUBo, FNMADD...
    printOperand(MI, 3, O); 
    return;
    break;
  case 3:
    // QVALIGNI, QVALIGNIb, QVALIGNIs, XXPERMDI, XXSLDWI
    printU2ImmOperand(MI, 3, O); 
    return;
    break;
  case 4:
    // QVFLOGICAL, QVFLOGICALb, QVFLOGICALs
    printU12ImmOperand(MI, 3, O); 
    return;
    break;
  }


  // Fragment 7 encoded into 1 bits for 2 unique commands.
  //printf("Frag-7: %"PRIu64"\n", (Bits >> 40) & 1);
  if ((Bits >> 40) & 1) {
    // RLWINM, RLWINM8, RLWINM8o, RLWINMo, RLWNM, RLWNM8, RLWNM8o, RLWNMo
    SStream_concat0(O, ", "); 
    printU5ImmOperand(MI, 4, O); 
    return;
  } else {
    // CLRLSLWI, CLRLSLWIo, EXTLWI, EXTLWIo, EXTRWI, EXTRWIo, INSLWI, INSLWIo...
    return;
  }
}


#ifndef CAPSTONE_DIET
/// getRegisterName - This method is automatically generated by tblgen
/// from the register set description.  This returns the assembler name
/// for the specified register.
static const char *getRegisterName(unsigned RegNo)
{
  // assert(RegNo && RegNo < 310 && "Invalid register number!");

  static const char AsmStrs[] = {
  /* 0 */ '*', '*', 'R', 'O', 'U', 'N', 'D', 'I', 'N', 'G', 32, 'M', 'O', 'D', 'E', '*', '*', 0,
  /* 18 */ '*', '*', 'F', 'R', 'A', 'M', 'E', 32, 'P', 'O', 'I', 'N', 'T', 'E', 'R', '*', '*', 0,
  /* 36 */ '*', '*', 'B', 'A', 'S', 'E', 32, 'P', 'O', 'I', 'N', 'T', 'E', 'R', '*', '*', 0,
  /* 53 */ 'f', '1', '0', 0,
  /* 57 */ 'q', '1', '0', 0,
  /* 61 */ 'r', '1', '0', 0,
  /* 65 */ 'v', 's', '1', '0', 0,
  /* 70 */ 'v', '1', '0', 0,
  /* 74 */ 'f', '2', '0', 0,
  /* 78 */ 'q', '2', '0', 0,
  /* 82 */ 'r', '2', '0', 0,
  /* 86 */ 'v', 's', '2', '0', 0,
  /* 91 */ 'v', '2', '0', 0,
  /* 95 */ 'f', '3', '0', 0,
  /* 99 */ 'q', '3', '0', 0,
  /* 103 */ 'r', '3', '0', 0,
  /* 107 */ 'v', 's', '3', '0', 0,
  /* 112 */ 'v', '3', '0', 0,
  /* 116 */ 'v', 's', '4', '0', 0,
  /* 121 */ 'v', 's', '5', '0', 0,
  /* 126 */ 'v', 's', '6', '0', 0,
  /* 131 */ 'f', '0', 0,
  /* 134 */ 'q', '0', 0,
  /* 137 */ 'c', 'r', '0', 0,
  /* 141 */ 'v', 's', '0', 0,
  /* 145 */ 'v', '0', 0,
  /* 148 */ 'f', '1', '1', 0,
  /* 152 */ 'q', '1', '1', 0,
  /* 156 */ 'r', '1', '1', 0,
  /* 160 */ 'v', 's', '1', '1', 0,
  /* 165 */ 'v', '1', '1', 0,
  /* 169 */ 'f', '2', '1', 0,
  /* 173 */ 'q', '2', '1', 0,
  /* 177 */ 'r', '2', '1', 0,
  /* 181 */ 'v', 's', '2', '1', 0,
  /* 186 */ 'v', '2', '1', 0,
  /* 190 */ 'f', '3', '1', 0,
  /* 194 */ 'q', '3', '1', 0,
  /* 198 */ 'r', '3', '1', 0,
  /* 202 */ 'v', 's', '3', '1', 0,
  /* 207 */ 'v', '3', '1', 0,
  /* 211 */ 'v', 's', '4', '1', 0,
  /* 216 */ 'v', 's', '5', '1', 0,
  /* 221 */ 'v', 's', '6', '1', 0,
  /* 226 */ 'f', '1', 0,
  /* 229 */ 'q', '1', 0,
  /* 232 */ 'c', 'r', '1', 0,
  /* 236 */ 'v', 's', '1', 0,
  /* 240 */ 'v', '1', 0,
  /* 243 */ 'f', '1', '2', 0,
  /* 247 */ 'q', '1', '2', 0,
  /* 251 */ 'r', '1', '2', 0,
  /* 255 */ 'v', 's', '1', '2', 0,
  /* 260 */ 'v', '1', '2', 0,
  /* 264 */ 'f', '2', '2', 0,
  /* 268 */ 'q', '2', '2', 0,
  /* 272 */ 'r', '2', '2', 0,
  /* 276 */ 'v', 's', '2', '2', 0,
  /* 281 */ 'v', '2', '2', 0,
  /* 285 */ 'v', 's', '3', '2', 0,
  /* 290 */ 'v', 's', '4', '2', 0,
  /* 295 */ 'v', 's', '5', '2', 0,
  /* 300 */ 'v', 's', '6', '2', 0,
  /* 305 */ 'f', '2', 0,
  /* 308 */ 'q', '2', 0,
  /* 311 */ 'c', 'r', '2', 0,
  /* 315 */ 'v', 's', '2', 0,
  /* 319 */ 'v', '2', 0,
  /* 322 */ 'f', '1', '3', 0,
  /* 326 */ 'q', '1', '3', 0,
  /* 330 */ 'r', '1', '3', 0,
  /* 334 */ 'v', 's', '1', '3', 0,
  /* 339 */ 'v', '1', '3', 0,
  /* 343 */ 'f', '2', '3', 0,
  /* 347 */ 'q', '2', '3', 0,
  /* 351 */ 'r', '2', '3', 0,
  /* 355 */ 'v', 's', '2', '3', 0,
  /* 360 */ 'v', '2', '3', 0,
  /* 364 */ 'v', 's', '3', '3', 0,
  /* 369 */ 'v', 's', '4', '3', 0,
  /* 374 */ 'v', 's', '5', '3', 0,
  /* 379 */ 'v', 's', '6', '3', 0,
  /* 384 */ 'f', '3', 0,
  /* 387 */ 'q', '3', 0,
  /* 390 */ 'c', 'r', '3', 0,
  /* 394 */ 'v', 's', '3', 0,
  /* 398 */ 'v', '3', 0,
  /* 401 */ 'f', '1', '4', 0,
  /* 405 */ 'q', '1', '4', 0,
  /* 409 */ 'r', '1', '4', 0,
  /* 413 */ 'v', 's', '1', '4', 0,
  /* 418 */ 'v', '1', '4', 0,
  /* 422 */ 'f', '2', '4', 0,
  /* 426 */ 'q', '2', '4', 0,
  /* 430 */ 'r', '2', '4', 0,
  /* 434 */ 'v', 's', '2', '4', 0,
  /* 439 */ 'v', '2', '4', 0,
  /* 443 */ 'v', 's', '3', '4', 0,
  /* 448 */ 'v', 's', '4', '4', 0,
  /* 453 */ 'v', 's', '5', '4', 0,
  /* 458 */ 'f', '4', 0,
  /* 461 */ 'q', '4', 0,
  /* 464 */ 'c', 'r', '4', 0,
  /* 468 */ 'v', 's', '4', 0,
  /* 472 */ 'v', '4', 0,
  /* 475 */ 'f', '1', '5', 0,
  /* 479 */ 'q', '1', '5', 0,
  /* 483 */ 'r', '1', '5', 0,
  /* 487 */ 'v', 's', '1', '5', 0,
  /* 492 */ 'v', '1', '5', 0,
  /* 496 */ 'f', '2', '5', 0,
  /* 500 */ 'q', '2', '5', 0,
  /* 504 */ 'r', '2', '5', 0,
  /* 508 */ 'v', 's', '2', '5', 0,
  /* 513 */ 'v', '2', '5', 0,
  /* 517 */ 'v', 's', '3', '5', 0,
  /* 522 */ 'v', 's', '4', '5', 0,
  /* 527 */ 'v', 's', '5', '5', 0,
  /* 532 */ 'f', '5', 0,
  /* 535 */ 'q', '5', 0,
  /* 538 */ 'c', 'r', '5', 0,
  /* 542 */ 'v', 's', '5', 0,
  /* 546 */ 'v', '5', 0,
  /* 549 */ 'f', '1', '6', 0,
  /* 553 */ 'q', '1', '6', 0,
  /* 557 */ 'r', '1', '6', 0,
  /* 561 */ 'v', 's', '1', '6', 0,
  /* 566 */ 'v', '1', '6', 0,
  /* 570 */ 'f', '2', '6', 0,
  /* 574 */ 'q', '2', '6', 0,
  /* 578 */ 'r', '2', '6', 0,
  /* 582 */ 'v', 's', '2', '6', 0,
  /* 587 */ 'v', '2', '6', 0,
  /* 591 */ 'v', 's', '3', '6', 0,
  /* 596 */ 'v', 's', '4', '6', 0,
  /* 601 */ 'v', 's', '5', '6', 0,
  /* 606 */ 'f', '6', 0,
  /* 609 */ 'q', '6', 0,
  /* 612 */ 'c', 'r', '6', 0,
  /* 616 */ 'v', 's', '6', 0,
  /* 620 */ 'v', '6', 0,
  /* 623 */ 'f', '1', '7', 0,
  /* 627 */ 'q', '1', '7', 0,
  /* 631 */ 'r', '1', '7', 0,
  /* 635 */ 'v', 's', '1', '7', 0,
  /* 640 */ 'v', '1', '7', 0,
  /* 644 */ 'f', '2', '7', 0,
  /* 648 */ 'q', '2', '7', 0,
  /* 652 */ 'r', '2', '7', 0,
  /* 656 */ 'v', 's', '2', '7', 0,
  /* 661 */ 'v', '2', '7', 0,
  /* 665 */ 'v', 's', '3', '7', 0,
  /* 670 */ 'v', 's', '4', '7', 0,
  /* 675 */ 'v', 's', '5', '7', 0,
  /* 680 */ 'f', '7', 0,
  /* 683 */ 'q', '7', 0,
  /* 686 */ 'c', 'r', '7', 0,
  /* 690 */ 'v', 's', '7', 0,
  /* 694 */ 'v', '7', 0,
  /* 697 */ 'f', '1', '8', 0,
  /* 701 */ 'q', '1', '8', 0,
  /* 705 */ 'r', '1', '8', 0,
  /* 709 */ 'v', 's', '1', '8', 0,
  /* 714 */ 'v', '1', '8', 0,
  /* 718 */ 'f', '2', '8', 0,
  /* 722 */ 'q', '2', '8', 0,
  /* 726 */ 'r', '2', '8', 0,
  /* 730 */ 'v', 's', '2', '8', 0,
  /* 735 */ 'v', '2', '8', 0,
  /* 739 */ 'v', 's', '3', '8', 0,
  /* 744 */ 'v', 's', '4', '8', 0,
  /* 749 */ 'v', 's', '5', '8', 0,
  /* 754 */ 'f', '8', 0,
  /* 757 */ 'q', '8', 0,
  /* 760 */ 'r', '8', 0,
  /* 763 */ 'v', 's', '8', 0,
  /* 767 */ 'v', '8', 0,
  /* 770 */ 'f', '1', '9', 0,
  /* 774 */ 'q', '1', '9', 0,
  /* 778 */ 'r', '1', '9', 0,
  /* 782 */ 'v', 's', '1', '9', 0,
  /* 787 */ 'v', '1', '9', 0,
  /* 791 */ 'f', '2', '9', 0,
  /* 795 */ 'q', '2', '9', 0,
  /* 799 */ 'r', '2', '9', 0,
  /* 803 */ 'v', 's', '2', '9', 0,
  /* 808 */ 'v', '2', '9', 0,
  /* 812 */ 'v', 's', '3', '9', 0,
  /* 817 */ 'v', 's', '4', '9', 0,
  /* 822 */ 'v', 's', '5', '9', 0,
  /* 827 */ 'f', '9', 0,
  /* 830 */ 'q', '9', 0,
  /* 833 */ 'r', '9', 0,
  /* 836 */ 'v', 's', '9', 0,
  /* 840 */ 'v', '9', 0,
  /* 843 */ 'c', 'a', 0,
  /* 846 */ 'v', 'r', 's', 'a', 'v', 'e', 0,
  /* 853 */ 'l', 'r', 0,
  /* 856 */ 'c', 't', 'r', 0,
  };

  static const uint16_t RegAsmOffset[] = {
    36, 843, 856, 18, 853, 0, 846, 55, 36, 137, 232, 311, 390, 464, 
    538, 612, 686, 856, 131, 226, 305, 384, 458, 532, 606, 680, 754, 827, 
    53, 148, 243, 322, 401, 475, 549, 623, 697, 770, 74, 169, 264, 343, 
    422, 496, 570, 644, 718, 791, 95, 190, 18, 853, 134, 229, 308, 387, 
    461, 535, 609, 683, 757, 830, 57, 152, 247, 326, 405, 479, 553, 627, 
    701, 774, 78, 173, 268, 347, 426, 500, 574, 648, 722, 795, 99, 194, 
    138, 233, 312, 391, 465, 539, 613, 687, 760, 833, 61, 156, 251, 330, 
    409, 483, 557, 631, 705, 778, 82, 177, 272, 351, 430, 504, 578, 652, 
    726, 799, 103, 198, 145, 240, 319, 398, 472, 546, 620, 694, 767, 840, 
    70, 165, 260, 339, 418, 492, 566, 640, 714, 787, 91, 186, 281, 360, 
    439, 513, 587, 661, 735, 808, 112, 207, 285, 364, 443, 517, 591, 665, 
    739, 812, 116, 211, 290, 369, 448, 522, 596, 670, 744, 817, 121, 216, 
    295, 374, 453, 527, 601, 675, 749, 822, 126, 221, 300, 379, 285, 364, 
    443, 517, 591, 665, 739, 812, 116, 211, 290, 369, 448, 522, 596, 670, 
    744, 817, 121, 216, 295, 374, 453, 527, 601, 675, 749, 822, 126, 221, 
    300, 379, 141, 236, 315, 394, 468, 542, 616, 690, 763, 836, 65, 160, 
    255, 334, 413, 487, 561, 635, 709, 782, 86, 181, 276, 355, 434, 508, 
    582, 656, 730, 803, 107, 202, 138, 233, 312, 391, 465, 539, 613, 687, 
    760, 833, 61, 156, 251, 330, 409, 483, 557, 631, 705, 778, 82, 177, 
    272, 351, 430, 504, 578, 652, 726, 799, 103, 198, 55, 245, 551, 54, 
    402, 698, 265, 571, 96, 150, 477, 772, 323, 624, 170, 497, 792, 55, 
    403, 699, 244, 550, 75, 423, 719, 324, 625, 149, 476, 771, 344, 645, 
    191, 
  };

  //assert (*(AsmStrs+RegAsmOffset[RegNo-1]) &&
  //       "Invalid alt name index for register!");
  //int i;
  //for (i = 0; i < sizeof(RegAsmOffset)/2; i++)
  //	  printf("%s = %u\n", AsmStrs+RegAsmOffset[i], i + 1);
  //printf("*************************\n");
  return AsmStrs+RegAsmOffset[RegNo-1];
}
#endif

#ifdef PRINT_ALIAS_INSTR
#undef PRINT_ALIAS_INSTR

static void printCustomAliasOperand(MCInst *MI, unsigned OpIdx,
  unsigned PrintMethodIdx, SStream *OS)
{
  switch (PrintMethodIdx) {
  default:
    // llvm_unreachable("Unknown PrintMethod kind");
    break;
  case 0:
    printBranchOperand(MI, OpIdx, OS);
    break;
  case 1:
    printAbsBranchOperand(MI, OpIdx, OS);
    break;
  case 2:
    printS16ImmOperand(MI, OpIdx, OS);
    break;
  case 3:
    printU16ImmOperand(MI, OpIdx, OS);
    break;
  case 4:
    printU6ImmOperand(MI, OpIdx, OS);
    break;
  case 5:
    printU5ImmOperand(MI, OpIdx, OS);
    break;
  }
}

static char *printAliasInstr(MCInst *MI, SStream *OS, void *info)
{
  #define GETREGCLASS_CONTAIN(_class, _reg) MCRegisterClass_contains(MCRegisterInfo_getRegClass(MRI, _class), MCOperand_getReg(MCInst_getOperand(MI, _reg)))
  const char *AsmString;
  char *tmp, *AsmMnem, *AsmOps, *c;
  int OpIdx, PrintMethodIdx;
  MCRegisterInfo *MRI = (MCRegisterInfo *)info;
  switch (MCInst_getOpcode(MI)) {
  default: return NULL;
  case PPC_BCC:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCC 12, crrc:$cc, condbrtarget:$dst)
      AsmString = "blt $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCC 12, CR0, condbrtarget:$dst)
      AsmString = "blt $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCC 14, crrc:$cc, condbrtarget:$dst)
      AsmString = "blt- $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCC 14, CR0, condbrtarget:$dst)
      AsmString = "blt- $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCC 15, crrc:$cc, condbrtarget:$dst)
      AsmString = "blt+ $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCC 15, CR0, condbrtarget:$dst)
      AsmString = "blt+ $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCC 44, crrc:$cc, condbrtarget:$dst)
      AsmString = "bgt $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCC 44, CR0, condbrtarget:$dst)
      AsmString = "bgt $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCC 46, crrc:$cc, condbrtarget:$dst)
      AsmString = "bgt- $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCC 46, CR0, condbrtarget:$dst)
      AsmString = "bgt- $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCC 47, crrc:$cc, condbrtarget:$dst)
      AsmString = "bgt+ $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCC 47, CR0, condbrtarget:$dst)
      AsmString = "bgt+ $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCC 76, crrc:$cc, condbrtarget:$dst)
      AsmString = "beq $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCC 76, CR0, condbrtarget:$dst)
      AsmString = "beq $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCC 78, crrc:$cc, condbrtarget:$dst)
      AsmString = "beq- $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCC 78, CR0, condbrtarget:$dst)
      AsmString = "beq- $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCC 79, crrc:$cc, condbrtarget:$dst)
      AsmString = "beq+ $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCC 79, CR0, condbrtarget:$dst)
      AsmString = "beq+ $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCC 68, crrc:$cc, condbrtarget:$dst)
      AsmString = "bne $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCC 68, CR0, condbrtarget:$dst)
      AsmString = "bne $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCC 70, crrc:$cc, condbrtarget:$dst)
      AsmString = "bne- $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCC 70, CR0, condbrtarget:$dst)
      AsmString = "bne- $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCC 71, crrc:$cc, condbrtarget:$dst)
      AsmString = "bne+ $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCC 71, CR0, condbrtarget:$dst)
      AsmString = "bne+ $\xFF\x03\x01";
      break;
    }
    return NULL;
  case PPC_BCCA:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCA 12, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "blta $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCA 12, CR0, abscondbrtarget:$dst)
      AsmString = "blta $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCA 14, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "blta- $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCA 14, CR0, abscondbrtarget:$dst)
      AsmString = "blta- $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCA 15, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "blta+ $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCA 15, CR0, abscondbrtarget:$dst)
      AsmString = "blta+ $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCA 44, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bgta $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCA 44, CR0, abscondbrtarget:$dst)
      AsmString = "bgta $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCA 46, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bgta- $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCA 46, CR0, abscondbrtarget:$dst)
      AsmString = "bgta- $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCA 47, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bgta+ $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCA 47, CR0, abscondbrtarget:$dst)
      AsmString = "bgta+ $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCA 76, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "beqa $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCA 76, CR0, abscondbrtarget:$dst)
      AsmString = "beqa $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCA 78, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "beqa- $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCA 78, CR0, abscondbrtarget:$dst)
      AsmString = "beqa- $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCA 79, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "beqa+ $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCA 79, CR0, abscondbrtarget:$dst)
      AsmString = "beqa+ $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCA 68, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bnea $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCA 68, CR0, abscondbrtarget:$dst)
      AsmString = "bnea $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCA 70, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bnea- $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCA 70, CR0, abscondbrtarget:$dst)
      AsmString = "bnea- $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCA 71, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bnea+ $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCA 71, CR0, abscondbrtarget:$dst)
      AsmString = "bnea+ $\xFF\x03\x02";
      break;
    }
    return NULL;
  case PPC_BCCCTR:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTR 12, crrc:$cc)
      AsmString = "bltctr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTR 12, CR0)
      AsmString = "bltctr";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTR 14, crrc:$cc)
      AsmString = "bltctr- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTR 14, CR0)
      AsmString = "bltctr-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTR 15, crrc:$cc)
      AsmString = "bltctr+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTR 15, CR0)
      AsmString = "bltctr+";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTR 44, crrc:$cc)
      AsmString = "bgtctr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTR 44, CR0)
      AsmString = "bgtctr";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTR 46, crrc:$cc)
      AsmString = "bgtctr- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTR 46, CR0)
      AsmString = "bgtctr-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTR 47, crrc:$cc)
      AsmString = "bgtctr+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTR 47, CR0)
      AsmString = "bgtctr+";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTR 76, crrc:$cc)
      AsmString = "beqctr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTR 76, CR0)
      AsmString = "beqctr";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTR 78, crrc:$cc)
      AsmString = "beqctr- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTR 78, CR0)
      AsmString = "beqctr-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTR 79, crrc:$cc)
      AsmString = "beqctr+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTR 79, CR0)
      AsmString = "beqctr+";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTR 68, crrc:$cc)
      AsmString = "bnectr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTR 68, CR0)
      AsmString = "bnectr";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTR 70, crrc:$cc)
      AsmString = "bnectr- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTR 70, CR0)
      AsmString = "bnectr-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTR 71, crrc:$cc)
      AsmString = "bnectr+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTR 71, CR0)
      AsmString = "bnectr+";
      break;
    }
    return NULL;
  case PPC_BCCCTRL:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTRL 12, crrc:$cc)
      AsmString = "bltctrl $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTRL 12, CR0)
      AsmString = "bltctrl";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTRL 14, crrc:$cc)
      AsmString = "bltctrl- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTRL 14, CR0)
      AsmString = "bltctrl-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTRL 15, crrc:$cc)
      AsmString = "bltctrl+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTRL 15, CR0)
      AsmString = "bltctrl+";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTRL 44, crrc:$cc)
      AsmString = "bgtctrl $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTRL 44, CR0)
      AsmString = "bgtctrl";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTRL 46, crrc:$cc)
      AsmString = "bgtctrl- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTRL 46, CR0)
      AsmString = "bgtctrl-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTRL 47, crrc:$cc)
      AsmString = "bgtctrl+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTRL 47, CR0)
      AsmString = "bgtctrl+";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTRL 76, crrc:$cc)
      AsmString = "beqctrl $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTRL 76, CR0)
      AsmString = "beqctrl";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTRL 78, crrc:$cc)
      AsmString = "beqctrl- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTRL 78, CR0)
      AsmString = "beqctrl-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTRL 79, crrc:$cc)
      AsmString = "beqctrl+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTRL 79, CR0)
      AsmString = "beqctrl+";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTRL 68, crrc:$cc)
      AsmString = "bnectrl $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTRL 68, CR0)
      AsmString = "bnectrl";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTRL 70, crrc:$cc)
      AsmString = "bnectrl- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTRL 70, CR0)
      AsmString = "bnectrl-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCCTRL 71, crrc:$cc)
      AsmString = "bnectrl+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCCTRL 71, CR0)
      AsmString = "bnectrl+";
      break;
    }
    return NULL;
  case PPC_BCCL:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCL 12, crrc:$cc, condbrtarget:$dst)
      AsmString = "bltl $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCL 12, CR0, condbrtarget:$dst)
      AsmString = "bltl $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCL 14, crrc:$cc, condbrtarget:$dst)
      AsmString = "bltl- $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCL 14, CR0, condbrtarget:$dst)
      AsmString = "bltl- $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCL 15, crrc:$cc, condbrtarget:$dst)
      AsmString = "bltl+ $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCL 15, CR0, condbrtarget:$dst)
      AsmString = "bltl+ $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCL 44, crrc:$cc, condbrtarget:$dst)
      AsmString = "bgtl $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCL 44, CR0, condbrtarget:$dst)
      AsmString = "bgtl $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCL 46, crrc:$cc, condbrtarget:$dst)
      AsmString = "bgtl- $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCL 46, CR0, condbrtarget:$dst)
      AsmString = "bgtl- $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCL 47, crrc:$cc, condbrtarget:$dst)
      AsmString = "bgtl+ $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCL 47, CR0, condbrtarget:$dst)
      AsmString = "bgtl+ $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCL 76, crrc:$cc, condbrtarget:$dst)
      AsmString = "beql $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCL 76, CR0, condbrtarget:$dst)
      AsmString = "beql $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCL 78, crrc:$cc, condbrtarget:$dst)
      AsmString = "beql- $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCL 78, CR0, condbrtarget:$dst)
      AsmString = "beql- $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCL 79, crrc:$cc, condbrtarget:$dst)
      AsmString = "beql+ $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCL 79, CR0, condbrtarget:$dst)
      AsmString = "beql+ $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCL 68, crrc:$cc, condbrtarget:$dst)
      AsmString = "bnel $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCL 68, CR0, condbrtarget:$dst)
      AsmString = "bnel $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCL 70, crrc:$cc, condbrtarget:$dst)
      AsmString = "bnel- $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCL 70, CR0, condbrtarget:$dst)
      AsmString = "bnel- $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCL 71, crrc:$cc, condbrtarget:$dst)
      AsmString = "bnel+ $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCL 71, CR0, condbrtarget:$dst)
      AsmString = "bnel+ $\xFF\x03\x01";
      break;
    }
    return NULL;
  case PPC_BCCLA:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLA 12, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bltla $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLA 12, CR0, abscondbrtarget:$dst)
      AsmString = "bltla $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLA 14, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bltla- $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLA 14, CR0, abscondbrtarget:$dst)
      AsmString = "bltla- $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLA 15, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bltla+ $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLA 15, CR0, abscondbrtarget:$dst)
      AsmString = "bltla+ $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLA 44, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bgtla $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLA 44, CR0, abscondbrtarget:$dst)
      AsmString = "bgtla $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLA 46, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bgtla- $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLA 46, CR0, abscondbrtarget:$dst)
      AsmString = "bgtla- $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLA 47, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bgtla+ $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLA 47, CR0, abscondbrtarget:$dst)
      AsmString = "bgtla+ $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLA 76, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "beqla $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLA 76, CR0, abscondbrtarget:$dst)
      AsmString = "beqla $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLA 78, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "beqla- $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLA 78, CR0, abscondbrtarget:$dst)
      AsmString = "beqla- $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLA 79, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "beqla+ $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLA 79, CR0, abscondbrtarget:$dst)
      AsmString = "beqla+ $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLA 68, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bnela $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLA 68, CR0, abscondbrtarget:$dst)
      AsmString = "bnela $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLA 70, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bnela- $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLA 70, CR0, abscondbrtarget:$dst)
      AsmString = "bnela- $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLA 71, crrc:$cc, abscondbrtarget:$dst)
      AsmString = "bnela+ $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLA 71, CR0, abscondbrtarget:$dst)
      AsmString = "bnela+ $\xFF\x03\x02";
      break;
    }
    return NULL;
  case PPC_BCCLR:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLR 12, crrc:$cc)
      AsmString = "bltlr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLR 12, CR0)
      AsmString = "bltlr";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLR 14, crrc:$cc)
      AsmString = "bltlr- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLR 14, CR0)
      AsmString = "bltlr-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLR 15, crrc:$cc)
      AsmString = "bltlr+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLR 15, CR0)
      AsmString = "bltlr+";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLR 44, crrc:$cc)
      AsmString = "bgtlr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLR 44, CR0)
      AsmString = "bgtlr";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLR 46, crrc:$cc)
      AsmString = "bgtlr- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLR 46, CR0)
      AsmString = "bgtlr-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLR 47, crrc:$cc)
      AsmString = "bgtlr+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLR 47, CR0)
      AsmString = "bgtlr+";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLR 76, crrc:$cc)
      AsmString = "beqlr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLR 76, CR0)
      AsmString = "beqlr";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLR 78, crrc:$cc)
      AsmString = "beqlr- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLR 78, CR0)
      AsmString = "beqlr-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLR 79, crrc:$cc)
      AsmString = "beqlr+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLR 79, CR0)
      AsmString = "beqlr+";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLR 68, crrc:$cc)
      AsmString = "bnelr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLR 68, CR0)
      AsmString = "bnelr";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLR 70, crrc:$cc)
      AsmString = "bnelr- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLR 70, CR0)
      AsmString = "bnelr-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLR 71, crrc:$cc)
      AsmString = "bnelr+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLR 71, CR0)
      AsmString = "bnelr+";
      break;
    }
    return NULL;
  case PPC_BCCLRL:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLRL 12, crrc:$cc)
      AsmString = "bltlrl $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 12 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLRL 12, CR0)
      AsmString = "bltlrl";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLRL 14, crrc:$cc)
      AsmString = "bltlrl- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 14 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLRL 14, CR0)
      AsmString = "bltlrl-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLRL 15, crrc:$cc)
      AsmString = "bltlrl+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 15 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLRL 15, CR0)
      AsmString = "bltlrl+";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLRL 44, crrc:$cc)
      AsmString = "bgtlrl $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 44 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLRL 44, CR0)
      AsmString = "bgtlrl";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLRL 46, crrc:$cc)
      AsmString = "bgtlrl- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 46 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLRL 46, CR0)
      AsmString = "bgtlrl-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLRL 47, crrc:$cc)
      AsmString = "bgtlrl+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 47 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLRL 47, CR0)
      AsmString = "bgtlrl+";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLRL 76, crrc:$cc)
      AsmString = "beqlrl $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 76 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLRL 76, CR0)
      AsmString = "beqlrl";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLRL 78, crrc:$cc)
      AsmString = "beqlrl- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 78 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLRL 78, CR0)
      AsmString = "beqlrl-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLRL 79, crrc:$cc)
      AsmString = "beqlrl+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 79 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLRL 79, CR0)
      AsmString = "beqlrl+";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLRL 68, crrc:$cc)
      AsmString = "bnelrl $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 68 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLRL 68, CR0)
      AsmString = "bnelrl";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLRL 70, crrc:$cc)
      AsmString = "bnelrl- $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 70 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLRL 70, CR0)
      AsmString = "bnelrl-";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 1)) {
      // (BCCLRL 71, crrc:$cc)
      AsmString = "bnelrl+ $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 71 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_CR0) {
      // (BCCLRL 71, CR0)
      AsmString = "bnelrl+";
      break;
    }
    return NULL;
  case PPC_CMPD:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == PPC_CR0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 2)) {
      // (CMPD CR0, g8rc:$rA, g8rc:$rB)
      AsmString = "cmpd $\x02, $\x03";
      break;
    }
    return NULL;
  case PPC_CMPDI:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == PPC_CR0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1)) {
      // (CMPDI CR0, g8rc:$rA, s16imm64:$imm)
      AsmString = "cmpdi $\x02, $\xFF\x03\x03";
      break;
    }
    return NULL;
  case PPC_CMPLD:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == PPC_CR0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 2)) {
      // (CMPLD CR0, g8rc:$rA, g8rc:$rB)
      AsmString = "cmpld $\x02, $\x03";
      break;
    }
    return NULL;
  case PPC_CMPLDI:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == PPC_CR0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1)) {
      // (CMPLDI CR0, g8rc:$rA, u16imm64:$imm)
      AsmString = "cmpldi $\x02, $\xFF\x03\x04";
      break;
    }
    return NULL;
  case PPC_CMPLW:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == PPC_CR0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 2)) {
      // (CMPLW CR0, gprc:$rA, gprc:$rB)
      AsmString = "cmplw $\x02, $\x03";
      break;
    }
    return NULL;
  case PPC_CMPLWI:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == PPC_CR0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (CMPLWI CR0, gprc:$rA, u16imm:$imm)
      AsmString = "cmplwi $\x02, $\xFF\x03\x04";
      break;
    }
    return NULL;
  case PPC_CMPW:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == PPC_CR0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 2)) {
      // (CMPW CR0, gprc:$rA, gprc:$rB)
      AsmString = "cmpw $\x02, $\x03";
      break;
    }
    return NULL;
  case PPC_CMPWI:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == PPC_CR0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (CMPWI CR0, gprc:$rA, s16imm:$imm)
      AsmString = "cmpwi $\x02, $\xFF\x03\x03";
      break;
    }
    return NULL;
  case PPC_CNTLZW:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (CNTLZW gprc:$rA, gprc:$rS)
      AsmString = "cntlz $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_CNTLZWo:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (CNTLZWo gprc:$rA, gprc:$rS)
      AsmString = "cntlz. $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_CREQV:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0)) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (CREQV crbitrc:$bx, crbitrc:$bx, crbitrc:$bx)
      AsmString = "crset $\x01";
      break;
    }
    return NULL;
  case PPC_CRNOR:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1))) {
      // (CRNOR crbitrc:$bx, crbitrc:$by, crbitrc:$by)
      AsmString = "crnot $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_CROR:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1))) {
      // (CROR crbitrc:$bx, crbitrc:$by, crbitrc:$by)
      AsmString = "crmove $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_CRXOR:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0)) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 0))) {
      // (CRXOR crbitrc:$bx, crbitrc:$bx, crbitrc:$bx)
      AsmString = "crclr $\x01";
      break;
    }
    return NULL;
  case PPC_MBAR:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (MBAR 0)
      AsmString = "mbar";
      break;
    }
    return NULL;
  case PPC_MFDCR:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 128) {
      // (MFDCR gprc:$Rx, 128)
      AsmString = "mfbr0 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 129) {
      // (MFDCR gprc:$Rx, 129)
      AsmString = "mfbr1 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 130) {
      // (MFDCR gprc:$Rx, 130)
      AsmString = "mfbr2 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 131) {
      // (MFDCR gprc:$Rx, 131)
      AsmString = "mfbr3 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 132) {
      // (MFDCR gprc:$Rx, 132)
      AsmString = "mfbr4 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 133) {
      // (MFDCR gprc:$Rx, 133)
      AsmString = "mfbr5 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 134) {
      // (MFDCR gprc:$Rx, 134)
      AsmString = "mfbr6 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 135) {
      // (MFDCR gprc:$Rx, 135)
      AsmString = "mfbr7 $\x01";
      break;
    }
    return NULL;
  case PPC_MFSPR:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 1) {
      // (MFSPR gprc:$Rx, 1)
      AsmString = "mfxer $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 4) {
      // (MFSPR gprc:$Rx, 4)
      AsmString = "mfrtcu $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 5) {
      // (MFSPR gprc:$Rx, 5)
      AsmString = "mfrtcl $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 17) {
      // (MFSPR gprc:$Rx, 17)
      AsmString = "mfdscr $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 18) {
      // (MFSPR gprc:$Rx, 18)
      AsmString = "mfdsisr $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 19) {
      // (MFSPR gprc:$Rx, 19)
      AsmString = "mfdar $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 990) {
      // (MFSPR gprc:$Rx, 990)
      AsmString = "mfsrr2 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 991) {
      // (MFSPR gprc:$Rx, 991)
      AsmString = "mfsrr3 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 28) {
      // (MFSPR gprc:$Rx, 28)
      AsmString = "mfcfar $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 29) {
      // (MFSPR gprc:$Rx, 29)
      AsmString = "mfamr $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 48) {
      // (MFSPR gprc:$Rx, 48)
      AsmString = "mfpid $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 989) {
      // (MFSPR gprc:$Rx, 989)
      AsmString = "mftblo $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 988) {
      // (MFSPR gprc:$Rx, 988)
      AsmString = "mftbhi $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 536) {
      // (MFSPR gprc:$Rx, 536)
      AsmString = "mfdbatu $\x01, 0";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 538) {
      // (MFSPR gprc:$Rx, 538)
      AsmString = "mfdbatu $\x01, 1";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 540) {
      // (MFSPR gprc:$Rx, 540)
      AsmString = "mfdbatu $\x01, 2";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 542) {
      // (MFSPR gprc:$Rx, 542)
      AsmString = "mfdbatu $\x01, 3";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 537) {
      // (MFSPR gprc:$Rx, 537)
      AsmString = "mfdbatl $\x01, 0";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 539) {
      // (MFSPR gprc:$Rx, 539)
      AsmString = "mfdbatl $\x01, 1";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 541) {
      // (MFSPR gprc:$Rx, 541)
      AsmString = "mfdbatl $\x01, 2";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 543) {
      // (MFSPR gprc:$Rx, 543)
      AsmString = "mfdbatl $\x01, 3";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 528) {
      // (MFSPR gprc:$Rx, 528)
      AsmString = "mfibatu $\x01, 0";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 530) {
      // (MFSPR gprc:$Rx, 530)
      AsmString = "mfibatu $\x01, 1";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 532) {
      // (MFSPR gprc:$Rx, 532)
      AsmString = "mfibatu $\x01, 2";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 534) {
      // (MFSPR gprc:$Rx, 534)
      AsmString = "mfibatu $\x01, 3";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 529) {
      // (MFSPR gprc:$Rx, 529)
      AsmString = "mfibatl $\x01, 0";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 531) {
      // (MFSPR gprc:$Rx, 531)
      AsmString = "mfibatl $\x01, 1";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 533) {
      // (MFSPR gprc:$Rx, 533)
      AsmString = "mfibatl $\x01, 2";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 535) {
      // (MFSPR gprc:$Rx, 535)
      AsmString = "mfibatl $\x01, 3";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 1018) {
      // (MFSPR gprc:$Rx, 1018)
      AsmString = "mfdccr $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 1019) {
      // (MFSPR gprc:$Rx, 1019)
      AsmString = "mficcr $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 981) {
      // (MFSPR gprc:$Rx, 981)
      AsmString = "mfdear $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 980) {
      // (MFSPR gprc:$Rx, 980)
      AsmString = "mfesr $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 512) {
      // (MFSPR gprc:$Rx, 512)
      AsmString = "mfspefscr $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 986) {
      // (MFSPR gprc:$Rx, 986)
      AsmString = "mftcr $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 280) {
      // (MFSPR gprc:$RT, 280)
      AsmString = "mfasr $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 287) {
      // (MFSPR gprc:$RT, 287)
      AsmString = "mfpvr $\x01";
      break;
    }
    return NULL;
  case PPC_MFTB:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 269) {
      // (MFTB gprc:$Rx, 269)
      AsmString = "mftbu $\x01";
      break;
    }
    return NULL;
  case PPC_MTCRF8:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 255 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1)) {
      // (MTCRF8 255, g8rc:$rA)
      AsmString = "mtcr $\x02";
      break;
    }
    return NULL;
  case PPC_MTDCR:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 128) {
      // (MTDCR gprc:$Rx, 128)
      AsmString = "mtbr0 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 129) {
      // (MTDCR gprc:$Rx, 129)
      AsmString = "mtbr1 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 130) {
      // (MTDCR gprc:$Rx, 130)
      AsmString = "mtbr2 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 131) {
      // (MTDCR gprc:$Rx, 131)
      AsmString = "mtbr3 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 132) {
      // (MTDCR gprc:$Rx, 132)
      AsmString = "mtbr4 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 133) {
      // (MTDCR gprc:$Rx, 133)
      AsmString = "mtbr5 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 134) {
      // (MTDCR gprc:$Rx, 134)
      AsmString = "mtbr6 $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 135) {
      // (MTDCR gprc:$Rx, 135)
      AsmString = "mtbr7 $\x01";
      break;
    }
    return NULL;
  case PPC_MTFSF:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_F8RCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (MTFSF i32imm:$FLM, f8rc:$FRB, 0, 0)
      AsmString = "mtfsf $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_MTFSFI:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (MTFSFI crrc:$BF, i32imm:$U, 0)
      AsmString = "mtfsfi $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_MTFSFIo:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_CRRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (MTFSFIo crrc:$BF, i32imm:$U, 0)
      AsmString = "mtfsfi. $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_MTFSFo:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_F8RCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (MTFSFo i32imm:$FLM, f8rc:$FRB, 0, 0)
      AsmString = "mtfsf. $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_MTMSR:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 0) {
      // (MTMSR gprc:$RS, 0)
      AsmString = "mtmsr $\x01";
      break;
    }
    return NULL;
  case PPC_MTMSRD:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isImm(MCInst_getOperand(MI, 1)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 1)) == 0) {
      // (MTMSRD gprc:$RS, 0)
      AsmString = "mtmsrd $\x01";
      break;
    }
    return NULL;
  case PPC_MTSPR:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 1 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 1, gprc:$Rx)
      AsmString = "mtxer $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 17 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 17, gprc:$Rx)
      AsmString = "mtdscr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 18 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 18, gprc:$Rx)
      AsmString = "mtdsisr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 19 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 19, gprc:$Rx)
      AsmString = "mtdar $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 990 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 990, gprc:$Rx)
      AsmString = "mtsrr2 $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 991 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 991, gprc:$Rx)
      AsmString = "mtsrr3 $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 28 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 28, gprc:$Rx)
      AsmString = "mtcfar $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 29 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 29, gprc:$Rx)
      AsmString = "mtamr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 48 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 48, gprc:$Rx)
      AsmString = "mtpid $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 284 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 284, gprc:$Rx)
      AsmString = "mttbl $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 285 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 285, gprc:$Rx)
      AsmString = "mttbu $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 989 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 989, gprc:$Rx)
      AsmString = "mttblo $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 988 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 988, gprc:$Rx)
      AsmString = "mttbhi $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 536 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 536, gprc:$Rx)
      AsmString = "mtdbatu 0, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 538 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 538, gprc:$Rx)
      AsmString = "mtdbatu 1, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 540 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 540, gprc:$Rx)
      AsmString = "mtdbatu 2, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 542 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 542, gprc:$Rx)
      AsmString = "mtdbatu 3, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 537 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 537, gprc:$Rx)
      AsmString = "mtdbatl 0, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 539 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 539, gprc:$Rx)
      AsmString = "mtdbatl 1, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 541 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 541, gprc:$Rx)
      AsmString = "mtdbatl 2, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 543 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 543, gprc:$Rx)
      AsmString = "mtdbatl 3, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 528 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 528, gprc:$Rx)
      AsmString = "mtibatu 0, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 530 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 530, gprc:$Rx)
      AsmString = "mtibatu 1, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 532 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 532, gprc:$Rx)
      AsmString = "mtibatu 2, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 534 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 534, gprc:$Rx)
      AsmString = "mtibatu 3, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 529 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 529, gprc:$Rx)
      AsmString = "mtibatl 0, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 531 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 531, gprc:$Rx)
      AsmString = "mtibatl 1, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 533 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 533, gprc:$Rx)
      AsmString = "mtibatl 2, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 535 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 535, gprc:$Rx)
      AsmString = "mtibatl 3, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 1018 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 1018, gprc:$Rx)
      AsmString = "mtdccr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 1019 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 1019, gprc:$Rx)
      AsmString = "mticcr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 981 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 981, gprc:$Rx)
      AsmString = "mtdear $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 980 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 980, gprc:$Rx)
      AsmString = "mtesr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 512 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 512, gprc:$Rx)
      AsmString = "mtspefscr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 986 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (MTSPR 986, gprc:$Rx)
      AsmString = "mttcr $\x02";
      break;
    }
    return NULL;
  case PPC_NOR8:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1))) {
      // (NOR8 g8rc:$rA, g8rc:$rB, g8rc:$rB)
      AsmString = "not $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_NOR8o:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1))) {
      // (NOR8o g8rc:$rA, g8rc:$rB, g8rc:$rB)
      AsmString = "not. $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_OR8:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1))) {
      // (OR8 g8rc:$rA, g8rc:$rB, g8rc:$rB)
      AsmString = "mr $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_OR8o:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1))) {
      // (OR8o g8rc:$rA, g8rc:$rB, g8rc:$rB)
      AsmString = "mr. $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_QVFLOGICALb:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0)) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 0)) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (QVFLOGICALb qbrc:$FRT, qbrc:$FRT, qbrc:$FRT, 0)
      AsmString = "qvfclr $\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 1) {
      // (QVFLOGICALb qbrc:$FRT, qbrc:$FRA, qbrc:$FRB, 1)
      AsmString = "qvfand $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 4) {
      // (QVFLOGICALb qbrc:$FRT, qbrc:$FRA, qbrc:$FRB, 4)
      AsmString = "qvfandc $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 5) {
      // (QVFLOGICALb qbrc:$FRT, qbrc:$FRA, qbrc:$FRA, 5)
      AsmString = "qvfctfb $\x01, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 6) {
      // (QVFLOGICALb qbrc:$FRT, qbrc:$FRA, qbrc:$FRB, 6)
      AsmString = "qvfxor $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 7) {
      // (QVFLOGICALb qbrc:$FRT, qbrc:$FRA, qbrc:$FRB, 7)
      AsmString = "qvfor $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 8) {
      // (QVFLOGICALb qbrc:$FRT, qbrc:$FRA, qbrc:$FRB, 8)
      AsmString = "qvfnor $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 9) {
      // (QVFLOGICALb qbrc:$FRT, qbrc:$FRA, qbrc:$FRB, 9)
      AsmString = "qvfequ $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 10) {
      // (QVFLOGICALb qbrc:$FRT, qbrc:$FRA, qbrc:$FRA, 10)
      AsmString = "qvfnot $\x01, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 13) {
      // (QVFLOGICALb qbrc:$FRT, qbrc:$FRA, qbrc:$FRB, 13)
      AsmString = "qvforc $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 14) {
      // (QVFLOGICALb qbrc:$FRT, qbrc:$FRA, qbrc:$FRB, 14)
      AsmString = "qvfnand $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_QBRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == MCOperand_getReg(MCInst_getOperand(MI, 0)) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 0)) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 15) {
      // (QVFLOGICALb qbrc:$FRT, qbrc:$FRT, qbrc:$FRT, 15)
      AsmString = "qvfset $\x01";
      break;
    }
    return NULL;
  case PPC_RLDCL:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (RLDCL g8rc:$rA, g8rc:$rS, gprc:$rB, 0)
      AsmString = "rotld $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case PPC_RLDCLo:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (RLDCLo g8rc:$rA, g8rc:$rS, gprc:$rB, 0)
      AsmString = "rotld. $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case PPC_RLDICL:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (RLDICL g8rc:$rA, g8rc:$rS, u6imm:$n, 0)
      AsmString = "rotldi $\x01, $\x02, $\xFF\x03\x05";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (RLDICL g8rc:$rA, g8rc:$rS, 0, u6imm:$n)
      AsmString = "clrldi $\x01, $\x02, $\xFF\x04\x05";
      break;
    }
    return NULL;
  case PPC_RLDICLo:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (RLDICLo g8rc:$rA, g8rc:$rS, u6imm:$n, 0)
      AsmString = "rotldi. $\x01, $\x02, $\xFF\x03\x05";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (RLDICLo g8rc:$rA, g8rc:$rS, 0, u6imm:$n)
      AsmString = "clrldi. $\x01, $\x02, $\xFF\x04\x05";
      break;
    }
    return NULL;
  case PPC_RLWINM:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 31) {
      // (RLWINM gprc:$rA, gprc:$rS, u5imm:$n, 0, 31)
      AsmString = "rotlwi $\x01, $\x02, $\xFF\x03\x06";
      break;
    }
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 31) {
      // (RLWINM gprc:$rA, gprc:$rS, 0, u5imm:$n, 31)
      AsmString = "clrlwi $\x01, $\x02, $\xFF\x04\x06";
      break;
    }
    return NULL;
  case PPC_RLWINMo:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 31) {
      // (RLWINMo gprc:$rA, gprc:$rS, u5imm:$n, 0, 31)
      AsmString = "rotlwi. $\x01, $\x02, $\xFF\x03\x06";
      break;
    }
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 31) {
      // (RLWINMo gprc:$rA, gprc:$rS, 0, u5imm:$n, 31)
      AsmString = "clrlwi. $\x01, $\x02, $\xFF\x04\x06";
      break;
    }
    return NULL;
  case PPC_RLWNM:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 31) {
      // (RLWNM gprc:$rA, gprc:$rS, gprc:$rB, 0, 31)
      AsmString = "rotlw $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case PPC_RLWNMo:
    if (MCInst_getNumOperands(MI) == 5 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 4)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 4)) == 31) {
      // (RLWNMo gprc:$rA, gprc:$rS, gprc:$rB, 0, 31)
      AsmString = "rotlw. $\x01, $\x02, $\x03";
      break;
    }
    return NULL;
  case PPC_SC:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (SC 0)
      AsmString = "sc";
      break;
    }
    return NULL;
  case PPC_SUBF8:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 2)) {
      // (SUBF8 g8rc:$rA, g8rc:$rC, g8rc:$rB)
      AsmString = "sub $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case PPC_SUBF8o:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 2)) {
      // (SUBF8o g8rc:$rA, g8rc:$rC, g8rc:$rB)
      AsmString = "sub. $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case PPC_SUBFC8:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 2)) {
      // (SUBFC8 g8rc:$rA, g8rc:$rC, g8rc:$rB)
      AsmString = "subc $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case PPC_SUBFC8o:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 2)) {
      // (SUBFC8o g8rc:$rA, g8rc:$rC, g8rc:$rB)
      AsmString = "subc. $\x01, $\x03, $\x02";
      break;
    }
    return NULL;
  case PPC_SYNC:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 1) {
      // (SYNC 1)
      AsmString = "lwsync";
      break;
    }
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2) {
      // (SYNC 2)
      AsmString = "ptesync";
      break;
    }
    return NULL;
  case PPC_TD:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 16 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 2)) {
      // (TD 16, g8rc:$rA, g8rc:$rB)
      AsmString = "tdlt $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 2)) {
      // (TD 4, g8rc:$rA, g8rc:$rB)
      AsmString = "tdeq $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 2)) {
      // (TD 8, g8rc:$rA, g8rc:$rB)
      AsmString = "tdgt $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 24 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 2)) {
      // (TD 24, g8rc:$rA, g8rc:$rB)
      AsmString = "tdne $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 2)) {
      // (TD 2, g8rc:$rA, g8rc:$rB)
      AsmString = "tdllt $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 1 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 2)) {
      // (TD 1, g8rc:$rA, g8rc:$rB)
      AsmString = "tdlgt $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 31 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 2)) {
      // (TD 31, g8rc:$rA, g8rc:$rB)
      AsmString = "tdu $\x02, $\x03";
      break;
    }
    return NULL;
  case PPC_TDI:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 16 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1)) {
      // (TDI 16, g8rc:$rA, s16imm:$imm)
      AsmString = "tdlti $\x02, $\xFF\x03\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1)) {
      // (TDI 4, g8rc:$rA, s16imm:$imm)
      AsmString = "tdeqi $\x02, $\xFF\x03\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1)) {
      // (TDI 8, g8rc:$rA, s16imm:$imm)
      AsmString = "tdgti $\x02, $\xFF\x03\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 24 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1)) {
      // (TDI 24, g8rc:$rA, s16imm:$imm)
      AsmString = "tdnei $\x02, $\xFF\x03\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1)) {
      // (TDI 2, g8rc:$rA, s16imm:$imm)
      AsmString = "tdllti $\x02, $\xFF\x03\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 1 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1)) {
      // (TDI 1, g8rc:$rA, s16imm:$imm)
      AsmString = "tdlgti $\x02, $\xFF\x03\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 31 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_G8RCRegClassID, 1)) {
      // (TDI 31, g8rc:$rA, s16imm:$imm)
      AsmString = "tdui $\x02, $\xFF\x03\x03";
      break;
    }
    return NULL;
  case PPC_TLBIE:
    if (MCInst_getNumOperands(MI) == 2 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == PPC_R0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (TLBIE R0, gprc:$RB)
      AsmString = "tlbie $\x02";
      break;
    }
    return NULL;
  case PPC_TLBRE2:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (TLBRE2 gprc:$RS, gprc:$A, 0)
      AsmString = "tlbrehi $\x01, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 1) {
      // (TLBRE2 gprc:$RS, gprc:$A, 1)
      AsmString = "tlbrelo $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_TLBWE2:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (TLBWE2 gprc:$RS, gprc:$A, 0)
      AsmString = "tlbwehi $\x01, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 1) {
      // (TLBWE2 gprc:$RS, gprc:$A, 1)
      AsmString = "tlbwelo $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_TW:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 16 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 2)) {
      // (TW 16, gprc:$rA, gprc:$rB)
      AsmString = "twlt $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 2)) {
      // (TW 4, gprc:$rA, gprc:$rB)
      AsmString = "tweq $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 2)) {
      // (TW 8, gprc:$rA, gprc:$rB)
      AsmString = "twgt $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 24 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 2)) {
      // (TW 24, gprc:$rA, gprc:$rB)
      AsmString = "twne $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 2)) {
      // (TW 2, gprc:$rA, gprc:$rB)
      AsmString = "twllt $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 1 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 2)) {
      // (TW 1, gprc:$rA, gprc:$rB)
      AsmString = "twlgt $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 31 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 2)) {
      // (TW 31, gprc:$rA, gprc:$rB)
      AsmString = "twu $\x02, $\x03";
      break;
    }
    return NULL;
  case PPC_TWI:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 16 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (TWI 16, gprc:$rA, s16imm:$imm)
      AsmString = "twlti $\x02, $\xFF\x03\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (TWI 4, gprc:$rA, s16imm:$imm)
      AsmString = "tweqi $\x02, $\xFF\x03\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (TWI 8, gprc:$rA, s16imm:$imm)
      AsmString = "twgti $\x02, $\xFF\x03\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 24 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (TWI 24, gprc:$rA, s16imm:$imm)
      AsmString = "twnei $\x02, $\xFF\x03\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (TWI 2, gprc:$rA, s16imm:$imm)
      AsmString = "twllti $\x02, $\xFF\x03\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 1 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (TWI 1, gprc:$rA, s16imm:$imm)
      AsmString = "twlgti $\x02, $\xFF\x03\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 31 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_GPRCRegClassID, 1)) {
      // (TWI 31, gprc:$rA, s16imm:$imm)
      AsmString = "twui $\x02, $\xFF\x03\x03";
      break;
    }
    return NULL;
  case PPC_WAIT:
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0) {
      // (WAIT 0)
      AsmString = "wait";
      break;
    }
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 1) {
      // (WAIT 1)
      AsmString = "waitrsv";
      break;
    }
    if (MCInst_getNumOperands(MI) == 1 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2) {
      // (WAIT 2)
      AsmString = "waitimpl";
      break;
    }
    return NULL;
  case PPC_XORI:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_getReg(MCInst_getOperand(MI, 0)) == PPC_R0 &&
        MCOperand_getReg(MCInst_getOperand(MI, 1)) == PPC_R0 &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (XORI R0, R0, 0)
      AsmString = "xnop";
      break;
    }
    return NULL;
  case PPC_XVCPSGNDP:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1))) {
      // (XVCPSGNDP vsrc:$XT, vsrc:$XB, vsrc:$XB)
      AsmString = "xvmovdp $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_XVCPSGNSP:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1))) {
      // (XVCPSGNSP vsrc:$XT, vsrc:$XB, vsrc:$XB)
      AsmString = "xvmovsp $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_XXPERMDI:
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (XXPERMDI vsrc:$XT, vsrc:$XB, vsrc:$XB, 0)
      AsmString = "xxspltd $\x01, $\x02, 0";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 3) {
      // (XXPERMDI vsrc:$XT, vsrc:$XB, vsrc:$XB, 3)
      AsmString = "xxspltd $\x01, $\x02, 1";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 0) {
      // (XXPERMDI vsrc:$XT, vsrc:$XA, vsrc:$XB, 0)
      AsmString = "xxmrghd $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 2) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 3) {
      // (XXPERMDI vsrc:$XT, vsrc:$XA, vsrc:$XB, 3)
      AsmString = "xxmrgld $\x01, $\x02, $\x03";
      break;
    }
    if (MCInst_getNumOperands(MI) == 4 &&
        MCOperand_isReg(MCInst_getOperand(MI, 0)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 0) &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_VSRCRegClassID, 1) &&
        MCOperand_isReg(MCInst_getOperand(MI, 2)) &&
        MCOperand_getReg(MCInst_getOperand(MI, 2)) == MCOperand_getReg(MCInst_getOperand(MI, 1)) &&
        MCOperand_isImm(MCInst_getOperand(MI, 3)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 3)) == 2) {
      // (XXPERMDI vsrc:$XT, vsrc:$XB, vsrc:$XB, 2)
      AsmString = "xxswapd $\x01, $\x02";
      break;
    }
    return NULL;
  case PPC_gBC:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBC 8, crbitrc:$bi, condbrtarget:$dst)
      AsmString = "bdnzt $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBC 0, crbitrc:$bi, condbrtarget:$dst)
      AsmString = "bdnzf $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 10 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBC 10, crbitrc:$bi, condbrtarget:$dst)
      AsmString = "bdzt $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBC 2, crbitrc:$bi, condbrtarget:$dst)
      AsmString = "bdzf $\x02, $\xFF\x03\x01";
      break;
    }
    return NULL;
  case PPC_gBCA:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBCA 8, crbitrc:$bi, abscondbrtarget:$dst)
      AsmString = "bdnzta $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBCA 0, crbitrc:$bi, abscondbrtarget:$dst)
      AsmString = "bdnzfa $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 10 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBCA 10, crbitrc:$bi, abscondbrtarget:$dst)
      AsmString = "bdzta $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBCA 2, crbitrc:$bi, abscondbrtarget:$dst)
      AsmString = "bdzfa $\x02, $\xFF\x03\x02";
      break;
    }
    return NULL;
  case PPC_gBCCTR:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (gBCCTR u5imm:$bo, crbitrc:$bi, 0)
      AsmString = "bcctr $\xFF\x01\x06, $\x02";
      break;
    }
    return NULL;
  case PPC_gBCCTRL:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (gBCCTRL u5imm:$bo, crbitrc:$bi, 0)
      AsmString = "bcctrl $\xFF\x01\x06, $\x02";
      break;
    }
    return NULL;
  case PPC_gBCL:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBCL 8, crbitrc:$bi, condbrtarget:$dst)
      AsmString = "bdnztl $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBCL 0, crbitrc:$bi, condbrtarget:$dst)
      AsmString = "bdnzfl $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 10 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBCL 10, crbitrc:$bi, condbrtarget:$dst)
      AsmString = "bdztl $\x02, $\xFF\x03\x01";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBCL 2, crbitrc:$bi, condbrtarget:$dst)
      AsmString = "bdzfl $\x02, $\xFF\x03\x01";
      break;
    }
    return NULL;
  case PPC_gBCLA:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBCLA 8, crbitrc:$bi, abscondbrtarget:$dst)
      AsmString = "bdnztla $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBCLA 0, crbitrc:$bi, abscondbrtarget:$dst)
      AsmString = "bdnzfla $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 10 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBCLA 10, crbitrc:$bi, abscondbrtarget:$dst)
      AsmString = "bdztla $\x02, $\xFF\x03\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1)) {
      // (gBCLA 2, crbitrc:$bi, abscondbrtarget:$dst)
      AsmString = "bdzfla $\x02, $\xFF\x03\x02";
      break;
    }
    return NULL;
  case PPC_gBCLR:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (gBCLR u5imm:$bo, crbitrc:$bi, 0)
      AsmString = "bclr $\xFF\x01\x06, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (gBCLR 8, crbitrc:$bi, 0)
      AsmString = "bdnztlr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (gBCLR 0, crbitrc:$bi, 0)
      AsmString = "bdnzflr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 10 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (gBCLR 10, crbitrc:$bi, 0)
      AsmString = "bdztlr $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (gBCLR 2, crbitrc:$bi, 0)
      AsmString = "bdzflr $\x02";
      break;
    }
    return NULL;
  case PPC_gBCLRL:
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (gBCLRL u5imm:$bo, crbitrc:$bi, 0)
      AsmString = "bclrl $\xFF\x01\x06, $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 8 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (gBCLRL 8, crbitrc:$bi, 0)
      AsmString = "bdnztlrl $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 0 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (gBCLRL 0, crbitrc:$bi, 0)
      AsmString = "bdnzflrl $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 10 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (gBCLRL 10, crbitrc:$bi, 0)
      AsmString = "bdztlrl $\x02";
      break;
    }
    if (MCInst_getNumOperands(MI) == 3 &&
        MCOperand_isImm(MCInst_getOperand(MI, 0)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 0)) == 2 &&
        MCOperand_isReg(MCInst_getOperand(MI, 1)) &&
        GETREGCLASS_CONTAIN(PPC_CRBITRCRegClassID, 1) &&
        MCOperand_isImm(MCInst_getOperand(MI, 2)) &&
        MCOperand_getImm(MCInst_getOperand(MI, 2)) == 0) {
      // (gBCLRL 2, crbitrc:$bi, 0)
      AsmString = "bdzflrl $\x02";
      break;
    }
    return NULL;
  }

  tmp = cs_strdup(AsmString);
  AsmMnem = tmp;
  for(AsmOps = tmp; *AsmOps; AsmOps++) {
    if (*AsmOps == ' ' || *AsmOps == '\t') {
      *AsmOps = '\0';
      AsmOps++;
      break;
    }
  }
  SStream_concat0(OS, AsmMnem);
  if (*AsmOps) {
    SStream_concat0(OS, "\t");
    for (c = AsmOps; *c; c++) {
      if (*c == '$') {
        c += 1;
        if (*c == (char)0xff) {
          c += 1;
          OpIdx = *c - 1;
          c += 1;
          PrintMethodIdx = *c - 1;
          printCustomAliasOperand(MI, OpIdx, PrintMethodIdx, OS);
        } else
          printOperand(MI, *c - 1, OS);
      } else {
        SStream_concat(OS, "%c", *c);
      }
    }
  }
  return tmp;
}

#endif // PRINT_ALIAS_INSTR
