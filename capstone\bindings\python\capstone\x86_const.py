# For Capstone Engine. AUTO-GENERATED FILE, DO NOT EDIT [x86_const.py]

# X86 registers

X86_REG_INVALID = 0
X86_REG_AH = 1
X86_REG_AL = 2
X86_REG_AX = 3
X86_REG_BH = 4
X86_REG_BL = 5
X86_REG_BP = 6
X86_REG_BPL = 7
X86_REG_BX = 8
X86_REG_CH = 9
X86_REG_CL = 10
X86_REG_CS = 11
X86_REG_CX = 12
X86_REG_DH = 13
X86_REG_DI = 14
X86_REG_DIL = 15
X86_REG_DL = 16
X86_REG_DS = 17
X86_REG_DX = 18
X86_REG_EAX = 19
X86_REG_EBP = 20
X86_REG_EBX = 21
X86_REG_ECX = 22
X86_REG_EDI = 23
X86_REG_EDX = 24
X86_REG_EFLAGS = 25
X86_REG_EIP = 26
X86_REG_EIZ = 27
X86_REG_ES = 28
X86_REG_ESI = 29
X86_REG_ESP = 30
X86_REG_FPSW = 31
X86_REG_FS = 32
X86_REG_GS = 33
X86_REG_IP = 34
X86_REG_RAX = 35
X86_REG_RBP = 36
X86_REG_RBX = 37
X86_REG_RCX = 38
X86_REG_RDI = 39
X86_REG_RDX = 40
X86_REG_RIP = 41
X86_REG_RIZ = 42
X86_REG_RSI = 43
X86_REG_RSP = 44
X86_REG_SI = 45
X86_REG_SIL = 46
X86_REG_SP = 47
X86_REG_SPL = 48
X86_REG_SS = 49
X86_REG_CR0 = 50
X86_REG_CR1 = 51
X86_REG_CR2 = 52
X86_REG_CR3 = 53
X86_REG_CR4 = 54
X86_REG_CR5 = 55
X86_REG_CR6 = 56
X86_REG_CR7 = 57
X86_REG_CR8 = 58
X86_REG_CR9 = 59
X86_REG_CR10 = 60
X86_REG_CR11 = 61
X86_REG_CR12 = 62
X86_REG_CR13 = 63
X86_REG_CR14 = 64
X86_REG_CR15 = 65
X86_REG_DR0 = 66
X86_REG_DR1 = 67
X86_REG_DR2 = 68
X86_REG_DR3 = 69
X86_REG_DR4 = 70
X86_REG_DR5 = 71
X86_REG_DR6 = 72
X86_REG_DR7 = 73
X86_REG_DR8 = 74
X86_REG_DR9 = 75
X86_REG_DR10 = 76
X86_REG_DR11 = 77
X86_REG_DR12 = 78
X86_REG_DR13 = 79
X86_REG_DR14 = 80
X86_REG_DR15 = 81
X86_REG_FP0 = 82
X86_REG_FP1 = 83
X86_REG_FP2 = 84
X86_REG_FP3 = 85
X86_REG_FP4 = 86
X86_REG_FP5 = 87
X86_REG_FP6 = 88
X86_REG_FP7 = 89
X86_REG_K0 = 90
X86_REG_K1 = 91
X86_REG_K2 = 92
X86_REG_K3 = 93
X86_REG_K4 = 94
X86_REG_K5 = 95
X86_REG_K6 = 96
X86_REG_K7 = 97
X86_REG_MM0 = 98
X86_REG_MM1 = 99
X86_REG_MM2 = 100
X86_REG_MM3 = 101
X86_REG_MM4 = 102
X86_REG_MM5 = 103
X86_REG_MM6 = 104
X86_REG_MM7 = 105
X86_REG_R8 = 106
X86_REG_R9 = 107
X86_REG_R10 = 108
X86_REG_R11 = 109
X86_REG_R12 = 110
X86_REG_R13 = 111
X86_REG_R14 = 112
X86_REG_R15 = 113
X86_REG_ST0 = 114
X86_REG_ST1 = 115
X86_REG_ST2 = 116
X86_REG_ST3 = 117
X86_REG_ST4 = 118
X86_REG_ST5 = 119
X86_REG_ST6 = 120
X86_REG_ST7 = 121
X86_REG_XMM0 = 122
X86_REG_XMM1 = 123
X86_REG_XMM2 = 124
X86_REG_XMM3 = 125
X86_REG_XMM4 = 126
X86_REG_XMM5 = 127
X86_REG_XMM6 = 128
X86_REG_XMM7 = 129
X86_REG_XMM8 = 130
X86_REG_XMM9 = 131
X86_REG_XMM10 = 132
X86_REG_XMM11 = 133
X86_REG_XMM12 = 134
X86_REG_XMM13 = 135
X86_REG_XMM14 = 136
X86_REG_XMM15 = 137
X86_REG_XMM16 = 138
X86_REG_XMM17 = 139
X86_REG_XMM18 = 140
X86_REG_XMM19 = 141
X86_REG_XMM20 = 142
X86_REG_XMM21 = 143
X86_REG_XMM22 = 144
X86_REG_XMM23 = 145
X86_REG_XMM24 = 146
X86_REG_XMM25 = 147
X86_REG_XMM26 = 148
X86_REG_XMM27 = 149
X86_REG_XMM28 = 150
X86_REG_XMM29 = 151
X86_REG_XMM30 = 152
X86_REG_XMM31 = 153
X86_REG_YMM0 = 154
X86_REG_YMM1 = 155
X86_REG_YMM2 = 156
X86_REG_YMM3 = 157
X86_REG_YMM4 = 158
X86_REG_YMM5 = 159
X86_REG_YMM6 = 160
X86_REG_YMM7 = 161
X86_REG_YMM8 = 162
X86_REG_YMM9 = 163
X86_REG_YMM10 = 164
X86_REG_YMM11 = 165
X86_REG_YMM12 = 166
X86_REG_YMM13 = 167
X86_REG_YMM14 = 168
X86_REG_YMM15 = 169
X86_REG_YMM16 = 170
X86_REG_YMM17 = 171
X86_REG_YMM18 = 172
X86_REG_YMM19 = 173
X86_REG_YMM20 = 174
X86_REG_YMM21 = 175
X86_REG_YMM22 = 176
X86_REG_YMM23 = 177
X86_REG_YMM24 = 178
X86_REG_YMM25 = 179
X86_REG_YMM26 = 180
X86_REG_YMM27 = 181
X86_REG_YMM28 = 182
X86_REG_YMM29 = 183
X86_REG_YMM30 = 184
X86_REG_YMM31 = 185
X86_REG_ZMM0 = 186
X86_REG_ZMM1 = 187
X86_REG_ZMM2 = 188
X86_REG_ZMM3 = 189
X86_REG_ZMM4 = 190
X86_REG_ZMM5 = 191
X86_REG_ZMM6 = 192
X86_REG_ZMM7 = 193
X86_REG_ZMM8 = 194
X86_REG_ZMM9 = 195
X86_REG_ZMM10 = 196
X86_REG_ZMM11 = 197
X86_REG_ZMM12 = 198
X86_REG_ZMM13 = 199
X86_REG_ZMM14 = 200
X86_REG_ZMM15 = 201
X86_REG_ZMM16 = 202
X86_REG_ZMM17 = 203
X86_REG_ZMM18 = 204
X86_REG_ZMM19 = 205
X86_REG_ZMM20 = 206
X86_REG_ZMM21 = 207
X86_REG_ZMM22 = 208
X86_REG_ZMM23 = 209
X86_REG_ZMM24 = 210
X86_REG_ZMM25 = 211
X86_REG_ZMM26 = 212
X86_REG_ZMM27 = 213
X86_REG_ZMM28 = 214
X86_REG_ZMM29 = 215
X86_REG_ZMM30 = 216
X86_REG_ZMM31 = 217
X86_REG_R8B = 218
X86_REG_R9B = 219
X86_REG_R10B = 220
X86_REG_R11B = 221
X86_REG_R12B = 222
X86_REG_R13B = 223
X86_REG_R14B = 224
X86_REG_R15B = 225
X86_REG_R8D = 226
X86_REG_R9D = 227
X86_REG_R10D = 228
X86_REG_R11D = 229
X86_REG_R12D = 230
X86_REG_R13D = 231
X86_REG_R14D = 232
X86_REG_R15D = 233
X86_REG_R8W = 234
X86_REG_R9W = 235
X86_REG_R10W = 236
X86_REG_R11W = 237
X86_REG_R12W = 238
X86_REG_R13W = 239
X86_REG_R14W = 240
X86_REG_R15W = 241
X86_REG_ENDING = 242

# Sub-flags of EFLAGS
X86_EFLAGS_MODIFY_AF = 1<<0
X86_EFLAGS_MODIFY_CF = 1<<1
X86_EFLAGS_MODIFY_SF = 1<<2
X86_EFLAGS_MODIFY_ZF = 1<<3
X86_EFLAGS_MODIFY_PF = 1<<4
X86_EFLAGS_MODIFY_OF = 1<<5
X86_EFLAGS_MODIFY_TF = 1<<6
X86_EFLAGS_MODIFY_IF = 1<<7
X86_EFLAGS_MODIFY_DF = 1<<8
X86_EFLAGS_MODIFY_NT = 1<<9
X86_EFLAGS_MODIFY_RF = 1<<10
X86_EFLAGS_PRIOR_OF = 1<<11
X86_EFLAGS_PRIOR_SF = 1<<12
X86_EFLAGS_PRIOR_ZF = 1<<13
X86_EFLAGS_PRIOR_AF = 1<<14
X86_EFLAGS_PRIOR_PF = 1<<15
X86_EFLAGS_PRIOR_CF = 1<<16
X86_EFLAGS_PRIOR_TF = 1<<17
X86_EFLAGS_PRIOR_IF = 1<<18
X86_EFLAGS_PRIOR_DF = 1<<19
X86_EFLAGS_PRIOR_NT = 1<<20
X86_EFLAGS_RESET_OF = 1<<21
X86_EFLAGS_RESET_CF = 1<<22
X86_EFLAGS_RESET_DF = 1<<23
X86_EFLAGS_RESET_IF = 1<<24
X86_EFLAGS_RESET_SF = 1<<25
X86_EFLAGS_RESET_AF = 1<<26
X86_EFLAGS_RESET_TF = 1<<27
X86_EFLAGS_RESET_NT = 1<<28
X86_EFLAGS_RESET_PF = 1<<29
X86_EFLAGS_SET_CF = 1<<30
X86_EFLAGS_SET_DF = 1<<31
X86_EFLAGS_SET_IF = 1<<32
X86_EFLAGS_TEST_OF = 1<<33
X86_EFLAGS_TEST_SF = 1<<34
X86_EFLAGS_TEST_ZF = 1<<35
X86_EFLAGS_TEST_PF = 1<<36
X86_EFLAGS_TEST_CF = 1<<37
X86_EFLAGS_TEST_NT = 1<<38
X86_EFLAGS_TEST_DF = 1<<39
X86_EFLAGS_UNDEFINED_OF = 1<<40
X86_EFLAGS_UNDEFINED_SF = 1<<41
X86_EFLAGS_UNDEFINED_ZF = 1<<42
X86_EFLAGS_UNDEFINED_PF = 1<<43
X86_EFLAGS_UNDEFINED_AF = 1<<44
X86_EFLAGS_UNDEFINED_CF = 1<<45
X86_EFLAGS_RESET_RF = 1<<46
X86_EFLAGS_TEST_RF = 1<<47
X86_EFLAGS_TEST_IF = 1<<48
X86_EFLAGS_TEST_TF = 1<<49
X86_EFLAGS_TEST_AF = 1<<50
X86_EFLAGS_RESET_ZF = 1<<51
X86_EFLAGS_SET_OF = 1<<52
X86_EFLAGS_SET_SF = 1<<53
X86_EFLAGS_SET_ZF = 1<<54
X86_EFLAGS_SET_AF = 1<<55
X86_EFLAGS_SET_PF = 1<<56
X86_EFLAGS_RESET_0F = 1<<57
X86_EFLAGS_RESET_AC = 1<<58
X86_FPU_FLAGS_MODIFY_C0 = 1<<0
X86_FPU_FLAGS_MODIFY_C1 = 1<<1
X86_FPU_FLAGS_MODIFY_C2 = 1<<2
X86_FPU_FLAGS_MODIFY_C3 = 1<<3
X86_FPU_FLAGS_RESET_C0 = 1<<4
X86_FPU_FLAGS_RESET_C1 = 1<<5
X86_FPU_FLAGS_RESET_C2 = 1<<6
X86_FPU_FLAGS_RESET_C3 = 1<<7
X86_FPU_FLAGS_SET_C0 = 1<<8
X86_FPU_FLAGS_SET_C1 = 1<<9
X86_FPU_FLAGS_SET_C2 = 1<<10
X86_FPU_FLAGS_SET_C3 = 1<<11
X86_FPU_FLAGS_UNDEFINED_C0 = 1<<12
X86_FPU_FLAGS_UNDEFINED_C1 = 1<<13
X86_FPU_FLAGS_UNDEFINED_C2 = 1<<14
X86_FPU_FLAGS_UNDEFINED_C3 = 1<<15
X86_FPU_FLAGS_TEST_C0 = 1<<16
X86_FPU_FLAGS_TEST_C1 = 1<<17
X86_FPU_FLAGS_TEST_C2 = 1<<18
X86_FPU_FLAGS_TEST_C3 = 1<<19

# Operand type for instruction's operands

X86_OP_INVALID = 0
X86_OP_REG = 1
X86_OP_IMM = 2
X86_OP_MEM = 3

# XOP Code Condition type

X86_XOP_CC_INVALID = 0
X86_XOP_CC_LT = 1
X86_XOP_CC_LE = 2
X86_XOP_CC_GT = 3
X86_XOP_CC_GE = 4
X86_XOP_CC_EQ = 5
X86_XOP_CC_NEQ = 6
X86_XOP_CC_FALSE = 7
X86_XOP_CC_TRUE = 8

# AVX broadcast type

X86_AVX_BCAST_INVALID = 0
X86_AVX_BCAST_2 = 1
X86_AVX_BCAST_4 = 2
X86_AVX_BCAST_8 = 3
X86_AVX_BCAST_16 = 4

# SSE Code Condition type

X86_SSE_CC_INVALID = 0
X86_SSE_CC_EQ = 1
X86_SSE_CC_LT = 2
X86_SSE_CC_LE = 3
X86_SSE_CC_UNORD = 4
X86_SSE_CC_NEQ = 5
X86_SSE_CC_NLT = 6
X86_SSE_CC_NLE = 7
X86_SSE_CC_ORD = 8

# AVX Code Condition type

X86_AVX_CC_INVALID = 0
X86_AVX_CC_EQ = 1
X86_AVX_CC_LT = 2
X86_AVX_CC_LE = 3
X86_AVX_CC_UNORD = 4
X86_AVX_CC_NEQ = 5
X86_AVX_CC_NLT = 6
X86_AVX_CC_NLE = 7
X86_AVX_CC_ORD = 8
X86_AVX_CC_EQ_UQ = 9
X86_AVX_CC_NGE = 10
X86_AVX_CC_NGT = 11
X86_AVX_CC_FALSE = 12
X86_AVX_CC_NEQ_OQ = 13
X86_AVX_CC_GE = 14
X86_AVX_CC_GT = 15
X86_AVX_CC_TRUE = 16
X86_AVX_CC_EQ_OS = 17
X86_AVX_CC_LT_OQ = 18
X86_AVX_CC_LE_OQ = 19
X86_AVX_CC_UNORD_S = 20
X86_AVX_CC_NEQ_US = 21
X86_AVX_CC_NLT_UQ = 22
X86_AVX_CC_NLE_UQ = 23
X86_AVX_CC_ORD_S = 24
X86_AVX_CC_EQ_US = 25
X86_AVX_CC_NGE_UQ = 26
X86_AVX_CC_NGT_UQ = 27
X86_AVX_CC_FALSE_OS = 28
X86_AVX_CC_NEQ_OS = 29
X86_AVX_CC_GE_OQ = 30
X86_AVX_CC_GT_OQ = 31
X86_AVX_CC_TRUE_US = 32

# AVX static rounding mode type

X86_AVX_RM_INVALID = 0
X86_AVX_RM_RN = 1
X86_AVX_RM_RD = 2
X86_AVX_RM_RU = 3
X86_AVX_RM_RZ = 4

# Instruction prefixes - to be used in cs_x86.prefix[]
X86_PREFIX_LOCK = 0xf0
X86_PREFIX_REP = 0xf3
X86_PREFIX_REPE = 0xf3
X86_PREFIX_REPNE = 0xf2
X86_PREFIX_CS = 0x2e
X86_PREFIX_SS = 0x36
X86_PREFIX_DS = 0x3e
X86_PREFIX_ES = 0x26
X86_PREFIX_FS = 0x64
X86_PREFIX_GS = 0x65
X86_PREFIX_OPSIZE = 0x66
X86_PREFIX_ADDRSIZE = 0x67

# X86 instructions

X86_INS_INVALID = 0
X86_INS_AAA = 1
X86_INS_AAD = 2
X86_INS_AAM = 3
X86_INS_AAS = 4
X86_INS_FABS = 5
X86_INS_ADC = 6
X86_INS_ADCX = 7
X86_INS_ADD = 8
X86_INS_ADDPD = 9
X86_INS_ADDPS = 10
X86_INS_ADDSD = 11
X86_INS_ADDSS = 12
X86_INS_ADDSUBPD = 13
X86_INS_ADDSUBPS = 14
X86_INS_FADD = 15
X86_INS_FIADD = 16
X86_INS_FADDP = 17
X86_INS_ADOX = 18
X86_INS_AESDECLAST = 19
X86_INS_AESDEC = 20
X86_INS_AESENCLAST = 21
X86_INS_AESENC = 22
X86_INS_AESIMC = 23
X86_INS_AESKEYGENASSIST = 24
X86_INS_AND = 25
X86_INS_ANDN = 26
X86_INS_ANDNPD = 27
X86_INS_ANDNPS = 28
X86_INS_ANDPD = 29
X86_INS_ANDPS = 30
X86_INS_ARPL = 31
X86_INS_BEXTR = 32
X86_INS_BLCFILL = 33
X86_INS_BLCI = 34
X86_INS_BLCIC = 35
X86_INS_BLCMSK = 36
X86_INS_BLCS = 37
X86_INS_BLENDPD = 38
X86_INS_BLENDPS = 39
X86_INS_BLENDVPD = 40
X86_INS_BLENDVPS = 41
X86_INS_BLSFILL = 42
X86_INS_BLSI = 43
X86_INS_BLSIC = 44
X86_INS_BLSMSK = 45
X86_INS_BLSR = 46
X86_INS_BOUND = 47
X86_INS_BSF = 48
X86_INS_BSR = 49
X86_INS_BSWAP = 50
X86_INS_BT = 51
X86_INS_BTC = 52
X86_INS_BTR = 53
X86_INS_BTS = 54
X86_INS_BZHI = 55
X86_INS_CALL = 56
X86_INS_CBW = 57
X86_INS_CDQ = 58
X86_INS_CDQE = 59
X86_INS_FCHS = 60
X86_INS_CLAC = 61
X86_INS_CLC = 62
X86_INS_CLD = 63
X86_INS_CLFLUSH = 64
X86_INS_CLFLUSHOPT = 65
X86_INS_CLGI = 66
X86_INS_CLI = 67
X86_INS_CLTS = 68
X86_INS_CLWB = 69
X86_INS_CMC = 70
X86_INS_CMOVA = 71
X86_INS_CMOVAE = 72
X86_INS_CMOVB = 73
X86_INS_CMOVBE = 74
X86_INS_FCMOVBE = 75
X86_INS_FCMOVB = 76
X86_INS_CMOVE = 77
X86_INS_FCMOVE = 78
X86_INS_CMOVG = 79
X86_INS_CMOVGE = 80
X86_INS_CMOVL = 81
X86_INS_CMOVLE = 82
X86_INS_FCMOVNBE = 83
X86_INS_FCMOVNB = 84
X86_INS_CMOVNE = 85
X86_INS_FCMOVNE = 86
X86_INS_CMOVNO = 87
X86_INS_CMOVNP = 88
X86_INS_FCMOVNU = 89
X86_INS_CMOVNS = 90
X86_INS_CMOVO = 91
X86_INS_CMOVP = 92
X86_INS_FCMOVU = 93
X86_INS_CMOVS = 94
X86_INS_CMP = 95
X86_INS_CMPSB = 96
X86_INS_CMPSQ = 97
X86_INS_CMPSW = 98
X86_INS_CMPXCHG16B = 99
X86_INS_CMPXCHG = 100
X86_INS_CMPXCHG8B = 101
X86_INS_COMISD = 102
X86_INS_COMISS = 103
X86_INS_FCOMP = 104
X86_INS_FCOMIP = 105
X86_INS_FCOMI = 106
X86_INS_FCOM = 107
X86_INS_FCOS = 108
X86_INS_CPUID = 109
X86_INS_CQO = 110
X86_INS_CRC32 = 111
X86_INS_CVTDQ2PD = 112
X86_INS_CVTDQ2PS = 113
X86_INS_CVTPD2DQ = 114
X86_INS_CVTPD2PS = 115
X86_INS_CVTPS2DQ = 116
X86_INS_CVTPS2PD = 117
X86_INS_CVTSD2SI = 118
X86_INS_CVTSD2SS = 119
X86_INS_CVTSI2SD = 120
X86_INS_CVTSI2SS = 121
X86_INS_CVTSS2SD = 122
X86_INS_CVTSS2SI = 123
X86_INS_CVTTPD2DQ = 124
X86_INS_CVTTPS2DQ = 125
X86_INS_CVTTSD2SI = 126
X86_INS_CVTTSS2SI = 127
X86_INS_CWD = 128
X86_INS_CWDE = 129
X86_INS_DAA = 130
X86_INS_DAS = 131
X86_INS_DATA16 = 132
X86_INS_DEC = 133
X86_INS_DIV = 134
X86_INS_DIVPD = 135
X86_INS_DIVPS = 136
X86_INS_FDIVR = 137
X86_INS_FIDIVR = 138
X86_INS_FDIVRP = 139
X86_INS_DIVSD = 140
X86_INS_DIVSS = 141
X86_INS_FDIV = 142
X86_INS_FIDIV = 143
X86_INS_FDIVP = 144
X86_INS_DPPD = 145
X86_INS_DPPS = 146
X86_INS_RET = 147
X86_INS_ENCLS = 148
X86_INS_ENCLU = 149
X86_INS_ENTER = 150
X86_INS_EXTRACTPS = 151
X86_INS_EXTRQ = 152
X86_INS_F2XM1 = 153
X86_INS_LCALL = 154
X86_INS_LJMP = 155
X86_INS_FBLD = 156
X86_INS_FBSTP = 157
X86_INS_FCOMPP = 158
X86_INS_FDECSTP = 159
X86_INS_FEMMS = 160
X86_INS_FFREE = 161
X86_INS_FICOM = 162
X86_INS_FICOMP = 163
X86_INS_FINCSTP = 164
X86_INS_FLDCW = 165
X86_INS_FLDENV = 166
X86_INS_FLDL2E = 167
X86_INS_FLDL2T = 168
X86_INS_FLDLG2 = 169
X86_INS_FLDLN2 = 170
X86_INS_FLDPI = 171
X86_INS_FNCLEX = 172
X86_INS_FNINIT = 173
X86_INS_FNOP = 174
X86_INS_FNSTCW = 175
X86_INS_FNSTSW = 176
X86_INS_FPATAN = 177
X86_INS_FPREM = 178
X86_INS_FPREM1 = 179
X86_INS_FPTAN = 180
X86_INS_FFREEP = 181
X86_INS_FRNDINT = 182
X86_INS_FRSTOR = 183
X86_INS_FNSAVE = 184
X86_INS_FSCALE = 185
X86_INS_FSETPM = 186
X86_INS_FSINCOS = 187
X86_INS_FNSTENV = 188
X86_INS_FXAM = 189
X86_INS_FXRSTOR = 190
X86_INS_FXRSTOR64 = 191
X86_INS_FXSAVE = 192
X86_INS_FXSAVE64 = 193
X86_INS_FXTRACT = 194
X86_INS_FYL2X = 195
X86_INS_FYL2XP1 = 196
X86_INS_MOVAPD = 197
X86_INS_MOVAPS = 198
X86_INS_ORPD = 199
X86_INS_ORPS = 200
X86_INS_VMOVAPD = 201
X86_INS_VMOVAPS = 202
X86_INS_XORPD = 203
X86_INS_XORPS = 204
X86_INS_GETSEC = 205
X86_INS_HADDPD = 206
X86_INS_HADDPS = 207
X86_INS_HLT = 208
X86_INS_HSUBPD = 209
X86_INS_HSUBPS = 210
X86_INS_IDIV = 211
X86_INS_FILD = 212
X86_INS_IMUL = 213
X86_INS_IN = 214
X86_INS_INC = 215
X86_INS_INSB = 216
X86_INS_INSERTPS = 217
X86_INS_INSERTQ = 218
X86_INS_INSD = 219
X86_INS_INSW = 220
X86_INS_INT = 221
X86_INS_INT1 = 222
X86_INS_INT3 = 223
X86_INS_INTO = 224
X86_INS_INVD = 225
X86_INS_INVEPT = 226
X86_INS_INVLPG = 227
X86_INS_INVLPGA = 228
X86_INS_INVPCID = 229
X86_INS_INVVPID = 230
X86_INS_IRET = 231
X86_INS_IRETD = 232
X86_INS_IRETQ = 233
X86_INS_FISTTP = 234
X86_INS_FIST = 235
X86_INS_FISTP = 236
X86_INS_UCOMISD = 237
X86_INS_UCOMISS = 238
X86_INS_VCOMISD = 239
X86_INS_VCOMISS = 240
X86_INS_VCVTSD2SS = 241
X86_INS_VCVTSI2SD = 242
X86_INS_VCVTSI2SS = 243
X86_INS_VCVTSS2SD = 244
X86_INS_VCVTTSD2SI = 245
X86_INS_VCVTTSD2USI = 246
X86_INS_VCVTTSS2SI = 247
X86_INS_VCVTTSS2USI = 248
X86_INS_VCVTUSI2SD = 249
X86_INS_VCVTUSI2SS = 250
X86_INS_VUCOMISD = 251
X86_INS_VUCOMISS = 252
X86_INS_JAE = 253
X86_INS_JA = 254
X86_INS_JBE = 255
X86_INS_JB = 256
X86_INS_JCXZ = 257
X86_INS_JECXZ = 258
X86_INS_JE = 259
X86_INS_JGE = 260
X86_INS_JG = 261
X86_INS_JLE = 262
X86_INS_JL = 263
X86_INS_JMP = 264
X86_INS_JNE = 265
X86_INS_JNO = 266
X86_INS_JNP = 267
X86_INS_JNS = 268
X86_INS_JO = 269
X86_INS_JP = 270
X86_INS_JRCXZ = 271
X86_INS_JS = 272
X86_INS_KANDB = 273
X86_INS_KANDD = 274
X86_INS_KANDNB = 275
X86_INS_KANDND = 276
X86_INS_KANDNQ = 277
X86_INS_KANDNW = 278
X86_INS_KANDQ = 279
X86_INS_KANDW = 280
X86_INS_KMOVB = 281
X86_INS_KMOVD = 282
X86_INS_KMOVQ = 283
X86_INS_KMOVW = 284
X86_INS_KNOTB = 285
X86_INS_KNOTD = 286
X86_INS_KNOTQ = 287
X86_INS_KNOTW = 288
X86_INS_KORB = 289
X86_INS_KORD = 290
X86_INS_KORQ = 291
X86_INS_KORTESTB = 292
X86_INS_KORTESTD = 293
X86_INS_KORTESTQ = 294
X86_INS_KORTESTW = 295
X86_INS_KORW = 296
X86_INS_KSHIFTLB = 297
X86_INS_KSHIFTLD = 298
X86_INS_KSHIFTLQ = 299
X86_INS_KSHIFTLW = 300
X86_INS_KSHIFTRB = 301
X86_INS_KSHIFTRD = 302
X86_INS_KSHIFTRQ = 303
X86_INS_KSHIFTRW = 304
X86_INS_KUNPCKBW = 305
X86_INS_KXNORB = 306
X86_INS_KXNORD = 307
X86_INS_KXNORQ = 308
X86_INS_KXNORW = 309
X86_INS_KXORB = 310
X86_INS_KXORD = 311
X86_INS_KXORQ = 312
X86_INS_KXORW = 313
X86_INS_LAHF = 314
X86_INS_LAR = 315
X86_INS_LDDQU = 316
X86_INS_LDMXCSR = 317
X86_INS_LDS = 318
X86_INS_FLDZ = 319
X86_INS_FLD1 = 320
X86_INS_FLD = 321
X86_INS_LEA = 322
X86_INS_LEAVE = 323
X86_INS_LES = 324
X86_INS_LFENCE = 325
X86_INS_LFS = 326
X86_INS_LGDT = 327
X86_INS_LGS = 328
X86_INS_LIDT = 329
X86_INS_LLDT = 330
X86_INS_LMSW = 331
X86_INS_OR = 332
X86_INS_SUB = 333
X86_INS_XOR = 334
X86_INS_LODSB = 335
X86_INS_LODSD = 336
X86_INS_LODSQ = 337
X86_INS_LODSW = 338
X86_INS_LOOP = 339
X86_INS_LOOPE = 340
X86_INS_LOOPNE = 341
X86_INS_RETF = 342
X86_INS_RETFQ = 343
X86_INS_LSL = 344
X86_INS_LSS = 345
X86_INS_LTR = 346
X86_INS_XADD = 347
X86_INS_LZCNT = 348
X86_INS_MASKMOVDQU = 349
X86_INS_MAXPD = 350
X86_INS_MAXPS = 351
X86_INS_MAXSD = 352
X86_INS_MAXSS = 353
X86_INS_MFENCE = 354
X86_INS_MINPD = 355
X86_INS_MINPS = 356
X86_INS_MINSD = 357
X86_INS_MINSS = 358
X86_INS_CVTPD2PI = 359
X86_INS_CVTPI2PD = 360
X86_INS_CVTPI2PS = 361
X86_INS_CVTPS2PI = 362
X86_INS_CVTTPD2PI = 363
X86_INS_CVTTPS2PI = 364
X86_INS_EMMS = 365
X86_INS_MASKMOVQ = 366
X86_INS_MOVD = 367
X86_INS_MOVDQ2Q = 368
X86_INS_MOVNTQ = 369
X86_INS_MOVQ2DQ = 370
X86_INS_MOVQ = 371
X86_INS_PABSB = 372
X86_INS_PABSD = 373
X86_INS_PABSW = 374
X86_INS_PACKSSDW = 375
X86_INS_PACKSSWB = 376
X86_INS_PACKUSWB = 377
X86_INS_PADDB = 378
X86_INS_PADDD = 379
X86_INS_PADDQ = 380
X86_INS_PADDSB = 381
X86_INS_PADDSW = 382
X86_INS_PADDUSB = 383
X86_INS_PADDUSW = 384
X86_INS_PADDW = 385
X86_INS_PALIGNR = 386
X86_INS_PANDN = 387
X86_INS_PAND = 388
X86_INS_PAVGB = 389
X86_INS_PAVGW = 390
X86_INS_PCMPEQB = 391
X86_INS_PCMPEQD = 392
X86_INS_PCMPEQW = 393
X86_INS_PCMPGTB = 394
X86_INS_PCMPGTD = 395
X86_INS_PCMPGTW = 396
X86_INS_PEXTRW = 397
X86_INS_PHADDSW = 398
X86_INS_PHADDW = 399
X86_INS_PHADDD = 400
X86_INS_PHSUBD = 401
X86_INS_PHSUBSW = 402
X86_INS_PHSUBW = 403
X86_INS_PINSRW = 404
X86_INS_PMADDUBSW = 405
X86_INS_PMADDWD = 406
X86_INS_PMAXSW = 407
X86_INS_PMAXUB = 408
X86_INS_PMINSW = 409
X86_INS_PMINUB = 410
X86_INS_PMOVMSKB = 411
X86_INS_PMULHRSW = 412
X86_INS_PMULHUW = 413
X86_INS_PMULHW = 414
X86_INS_PMULLW = 415
X86_INS_PMULUDQ = 416
X86_INS_POR = 417
X86_INS_PSADBW = 418
X86_INS_PSHUFB = 419
X86_INS_PSHUFW = 420
X86_INS_PSIGNB = 421
X86_INS_PSIGND = 422
X86_INS_PSIGNW = 423
X86_INS_PSLLD = 424
X86_INS_PSLLQ = 425
X86_INS_PSLLW = 426
X86_INS_PSRAD = 427
X86_INS_PSRAW = 428
X86_INS_PSRLD = 429
X86_INS_PSRLQ = 430
X86_INS_PSRLW = 431
X86_INS_PSUBB = 432
X86_INS_PSUBD = 433
X86_INS_PSUBQ = 434
X86_INS_PSUBSB = 435
X86_INS_PSUBSW = 436
X86_INS_PSUBUSB = 437
X86_INS_PSUBUSW = 438
X86_INS_PSUBW = 439
X86_INS_PUNPCKHBW = 440
X86_INS_PUNPCKHDQ = 441
X86_INS_PUNPCKHWD = 442
X86_INS_PUNPCKLBW = 443
X86_INS_PUNPCKLDQ = 444
X86_INS_PUNPCKLWD = 445
X86_INS_PXOR = 446
X86_INS_MONITOR = 447
X86_INS_MONTMUL = 448
X86_INS_MOV = 449
X86_INS_MOVABS = 450
X86_INS_MOVBE = 451
X86_INS_MOVDDUP = 452
X86_INS_MOVDQA = 453
X86_INS_MOVDQU = 454
X86_INS_MOVHLPS = 455
X86_INS_MOVHPD = 456
X86_INS_MOVHPS = 457
X86_INS_MOVLHPS = 458
X86_INS_MOVLPD = 459
X86_INS_MOVLPS = 460
X86_INS_MOVMSKPD = 461
X86_INS_MOVMSKPS = 462
X86_INS_MOVNTDQA = 463
X86_INS_MOVNTDQ = 464
X86_INS_MOVNTI = 465
X86_INS_MOVNTPD = 466
X86_INS_MOVNTPS = 467
X86_INS_MOVNTSD = 468
X86_INS_MOVNTSS = 469
X86_INS_MOVSB = 470
X86_INS_MOVSD = 471
X86_INS_MOVSHDUP = 472
X86_INS_MOVSLDUP = 473
X86_INS_MOVSQ = 474
X86_INS_MOVSS = 475
X86_INS_MOVSW = 476
X86_INS_MOVSX = 477
X86_INS_MOVSXD = 478
X86_INS_MOVUPD = 479
X86_INS_MOVUPS = 480
X86_INS_MOVZX = 481
X86_INS_MPSADBW = 482
X86_INS_MUL = 483
X86_INS_MULPD = 484
X86_INS_MULPS = 485
X86_INS_MULSD = 486
X86_INS_MULSS = 487
X86_INS_MULX = 488
X86_INS_FMUL = 489
X86_INS_FIMUL = 490
X86_INS_FMULP = 491
X86_INS_MWAIT = 492
X86_INS_NEG = 493
X86_INS_NOP = 494
X86_INS_NOT = 495
X86_INS_OUT = 496
X86_INS_OUTSB = 497
X86_INS_OUTSD = 498
X86_INS_OUTSW = 499
X86_INS_PACKUSDW = 500
X86_INS_PAUSE = 501
X86_INS_PAVGUSB = 502
X86_INS_PBLENDVB = 503
X86_INS_PBLENDW = 504
X86_INS_PCLMULQDQ = 505
X86_INS_PCMPEQQ = 506
X86_INS_PCMPESTRI = 507
X86_INS_PCMPESTRM = 508
X86_INS_PCMPGTQ = 509
X86_INS_PCMPISTRI = 510
X86_INS_PCMPISTRM = 511
X86_INS_PCOMMIT = 512
X86_INS_PDEP = 513
X86_INS_PEXT = 514
X86_INS_PEXTRB = 515
X86_INS_PEXTRD = 516
X86_INS_PEXTRQ = 517
X86_INS_PF2ID = 518
X86_INS_PF2IW = 519
X86_INS_PFACC = 520
X86_INS_PFADD = 521
X86_INS_PFCMPEQ = 522
X86_INS_PFCMPGE = 523
X86_INS_PFCMPGT = 524
X86_INS_PFMAX = 525
X86_INS_PFMIN = 526
X86_INS_PFMUL = 527
X86_INS_PFNACC = 528
X86_INS_PFPNACC = 529
X86_INS_PFRCPIT1 = 530
X86_INS_PFRCPIT2 = 531
X86_INS_PFRCP = 532
X86_INS_PFRSQIT1 = 533
X86_INS_PFRSQRT = 534
X86_INS_PFSUBR = 535
X86_INS_PFSUB = 536
X86_INS_PHMINPOSUW = 537
X86_INS_PI2FD = 538
X86_INS_PI2FW = 539
X86_INS_PINSRB = 540
X86_INS_PINSRD = 541
X86_INS_PINSRQ = 542
X86_INS_PMAXSB = 543
X86_INS_PMAXSD = 544
X86_INS_PMAXUD = 545
X86_INS_PMAXUW = 546
X86_INS_PMINSB = 547
X86_INS_PMINSD = 548
X86_INS_PMINUD = 549
X86_INS_PMINUW = 550
X86_INS_PMOVSXBD = 551
X86_INS_PMOVSXBQ = 552
X86_INS_PMOVSXBW = 553
X86_INS_PMOVSXDQ = 554
X86_INS_PMOVSXWD = 555
X86_INS_PMOVSXWQ = 556
X86_INS_PMOVZXBD = 557
X86_INS_PMOVZXBQ = 558
X86_INS_PMOVZXBW = 559
X86_INS_PMOVZXDQ = 560
X86_INS_PMOVZXWD = 561
X86_INS_PMOVZXWQ = 562
X86_INS_PMULDQ = 563
X86_INS_PMULHRW = 564
X86_INS_PMULLD = 565
X86_INS_POP = 566
X86_INS_POPAW = 567
X86_INS_POPAL = 568
X86_INS_POPCNT = 569
X86_INS_POPF = 570
X86_INS_POPFD = 571
X86_INS_POPFQ = 572
X86_INS_PREFETCH = 573
X86_INS_PREFETCHNTA = 574
X86_INS_PREFETCHT0 = 575
X86_INS_PREFETCHT1 = 576
X86_INS_PREFETCHT2 = 577
X86_INS_PREFETCHW = 578
X86_INS_PSHUFD = 579
X86_INS_PSHUFHW = 580
X86_INS_PSHUFLW = 581
X86_INS_PSLLDQ = 582
X86_INS_PSRLDQ = 583
X86_INS_PSWAPD = 584
X86_INS_PTEST = 585
X86_INS_PUNPCKHQDQ = 586
X86_INS_PUNPCKLQDQ = 587
X86_INS_PUSH = 588
X86_INS_PUSHAW = 589
X86_INS_PUSHAL = 590
X86_INS_PUSHF = 591
X86_INS_PUSHFD = 592
X86_INS_PUSHFQ = 593
X86_INS_RCL = 594
X86_INS_RCPPS = 595
X86_INS_RCPSS = 596
X86_INS_RCR = 597
X86_INS_RDFSBASE = 598
X86_INS_RDGSBASE = 599
X86_INS_RDMSR = 600
X86_INS_RDPMC = 601
X86_INS_RDRAND = 602
X86_INS_RDSEED = 603
X86_INS_RDTSC = 604
X86_INS_RDTSCP = 605
X86_INS_ROL = 606
X86_INS_ROR = 607
X86_INS_RORX = 608
X86_INS_ROUNDPD = 609
X86_INS_ROUNDPS = 610
X86_INS_ROUNDSD = 611
X86_INS_ROUNDSS = 612
X86_INS_RSM = 613
X86_INS_RSQRTPS = 614
X86_INS_RSQRTSS = 615
X86_INS_SAHF = 616
X86_INS_SAL = 617
X86_INS_SALC = 618
X86_INS_SAR = 619
X86_INS_SARX = 620
X86_INS_SBB = 621
X86_INS_SCASB = 622
X86_INS_SCASD = 623
X86_INS_SCASQ = 624
X86_INS_SCASW = 625
X86_INS_SETAE = 626
X86_INS_SETA = 627
X86_INS_SETBE = 628
X86_INS_SETB = 629
X86_INS_SETE = 630
X86_INS_SETGE = 631
X86_INS_SETG = 632
X86_INS_SETLE = 633
X86_INS_SETL = 634
X86_INS_SETNE = 635
X86_INS_SETNO = 636
X86_INS_SETNP = 637
X86_INS_SETNS = 638
X86_INS_SETO = 639
X86_INS_SETP = 640
X86_INS_SETS = 641
X86_INS_SFENCE = 642
X86_INS_SGDT = 643
X86_INS_SHA1MSG1 = 644
X86_INS_SHA1MSG2 = 645
X86_INS_SHA1NEXTE = 646
X86_INS_SHA1RNDS4 = 647
X86_INS_SHA256MSG1 = 648
X86_INS_SHA256MSG2 = 649
X86_INS_SHA256RNDS2 = 650
X86_INS_SHL = 651
X86_INS_SHLD = 652
X86_INS_SHLX = 653
X86_INS_SHR = 654
X86_INS_SHRD = 655
X86_INS_SHRX = 656
X86_INS_SHUFPD = 657
X86_INS_SHUFPS = 658
X86_INS_SIDT = 659
X86_INS_FSIN = 660
X86_INS_SKINIT = 661
X86_INS_SLDT = 662
X86_INS_SMSW = 663
X86_INS_SQRTPD = 664
X86_INS_SQRTPS = 665
X86_INS_SQRTSD = 666
X86_INS_SQRTSS = 667
X86_INS_FSQRT = 668
X86_INS_STAC = 669
X86_INS_STC = 670
X86_INS_STD = 671
X86_INS_STGI = 672
X86_INS_STI = 673
X86_INS_STMXCSR = 674
X86_INS_STOSB = 675
X86_INS_STOSD = 676
X86_INS_STOSQ = 677
X86_INS_STOSW = 678
X86_INS_STR = 679
X86_INS_FST = 680
X86_INS_FSTP = 681
X86_INS_FSTPNCE = 682
X86_INS_FXCH = 683
X86_INS_SUBPD = 684
X86_INS_SUBPS = 685
X86_INS_FSUBR = 686
X86_INS_FISUBR = 687
X86_INS_FSUBRP = 688
X86_INS_SUBSD = 689
X86_INS_SUBSS = 690
X86_INS_FSUB = 691
X86_INS_FISUB = 692
X86_INS_FSUBP = 693
X86_INS_SWAPGS = 694
X86_INS_SYSCALL = 695
X86_INS_SYSENTER = 696
X86_INS_SYSEXIT = 697
X86_INS_SYSRET = 698
X86_INS_T1MSKC = 699
X86_INS_TEST = 700
X86_INS_UD2 = 701
X86_INS_FTST = 702
X86_INS_TZCNT = 703
X86_INS_TZMSK = 704
X86_INS_FUCOMIP = 705
X86_INS_FUCOMI = 706
X86_INS_FUCOMPP = 707
X86_INS_FUCOMP = 708
X86_INS_FUCOM = 709
X86_INS_UD2B = 710
X86_INS_UNPCKHPD = 711
X86_INS_UNPCKHPS = 712
X86_INS_UNPCKLPD = 713
X86_INS_UNPCKLPS = 714
X86_INS_VADDPD = 715
X86_INS_VADDPS = 716
X86_INS_VADDSD = 717
X86_INS_VADDSS = 718
X86_INS_VADDSUBPD = 719
X86_INS_VADDSUBPS = 720
X86_INS_VAESDECLAST = 721
X86_INS_VAESDEC = 722
X86_INS_VAESENCLAST = 723
X86_INS_VAESENC = 724
X86_INS_VAESIMC = 725
X86_INS_VAESKEYGENASSIST = 726
X86_INS_VALIGND = 727
X86_INS_VALIGNQ = 728
X86_INS_VANDNPD = 729
X86_INS_VANDNPS = 730
X86_INS_VANDPD = 731
X86_INS_VANDPS = 732
X86_INS_VBLENDMPD = 733
X86_INS_VBLENDMPS = 734
X86_INS_VBLENDPD = 735
X86_INS_VBLENDPS = 736
X86_INS_VBLENDVPD = 737
X86_INS_VBLENDVPS = 738
X86_INS_VBROADCASTF128 = 739
X86_INS_VBROADCASTI32X4 = 740
X86_INS_VBROADCASTI64X4 = 741
X86_INS_VBROADCASTSD = 742
X86_INS_VBROADCASTSS = 743
X86_INS_VCOMPRESSPD = 744
X86_INS_VCOMPRESSPS = 745
X86_INS_VCVTDQ2PD = 746
X86_INS_VCVTDQ2PS = 747
X86_INS_VCVTPD2DQX = 748
X86_INS_VCVTPD2DQ = 749
X86_INS_VCVTPD2PSX = 750
X86_INS_VCVTPD2PS = 751
X86_INS_VCVTPD2UDQ = 752
X86_INS_VCVTPH2PS = 753
X86_INS_VCVTPS2DQ = 754
X86_INS_VCVTPS2PD = 755
X86_INS_VCVTPS2PH = 756
X86_INS_VCVTPS2UDQ = 757
X86_INS_VCVTSD2SI = 758
X86_INS_VCVTSD2USI = 759
X86_INS_VCVTSS2SI = 760
X86_INS_VCVTSS2USI = 761
X86_INS_VCVTTPD2DQX = 762
X86_INS_VCVTTPD2DQ = 763
X86_INS_VCVTTPD2UDQ = 764
X86_INS_VCVTTPS2DQ = 765
X86_INS_VCVTTPS2UDQ = 766
X86_INS_VCVTUDQ2PD = 767
X86_INS_VCVTUDQ2PS = 768
X86_INS_VDIVPD = 769
X86_INS_VDIVPS = 770
X86_INS_VDIVSD = 771
X86_INS_VDIVSS = 772
X86_INS_VDPPD = 773
X86_INS_VDPPS = 774
X86_INS_VERR = 775
X86_INS_VERW = 776
X86_INS_VEXP2PD = 777
X86_INS_VEXP2PS = 778
X86_INS_VEXPANDPD = 779
X86_INS_VEXPANDPS = 780
X86_INS_VEXTRACTF128 = 781
X86_INS_VEXTRACTF32X4 = 782
X86_INS_VEXTRACTF64X4 = 783
X86_INS_VEXTRACTI128 = 784
X86_INS_VEXTRACTI32X4 = 785
X86_INS_VEXTRACTI64X4 = 786
X86_INS_VEXTRACTPS = 787
X86_INS_VFMADD132PD = 788
X86_INS_VFMADD132PS = 789
X86_INS_VFMADDPD = 790
X86_INS_VFMADD213PD = 791
X86_INS_VFMADD231PD = 792
X86_INS_VFMADDPS = 793
X86_INS_VFMADD213PS = 794
X86_INS_VFMADD231PS = 795
X86_INS_VFMADDSD = 796
X86_INS_VFMADD213SD = 797
X86_INS_VFMADD132SD = 798
X86_INS_VFMADD231SD = 799
X86_INS_VFMADDSS = 800
X86_INS_VFMADD213SS = 801
X86_INS_VFMADD132SS = 802
X86_INS_VFMADD231SS = 803
X86_INS_VFMADDSUB132PD = 804
X86_INS_VFMADDSUB132PS = 805
X86_INS_VFMADDSUBPD = 806
X86_INS_VFMADDSUB213PD = 807
X86_INS_VFMADDSUB231PD = 808
X86_INS_VFMADDSUBPS = 809
X86_INS_VFMADDSUB213PS = 810
X86_INS_VFMADDSUB231PS = 811
X86_INS_VFMSUB132PD = 812
X86_INS_VFMSUB132PS = 813
X86_INS_VFMSUBADD132PD = 814
X86_INS_VFMSUBADD132PS = 815
X86_INS_VFMSUBADDPD = 816
X86_INS_VFMSUBADD213PD = 817
X86_INS_VFMSUBADD231PD = 818
X86_INS_VFMSUBADDPS = 819
X86_INS_VFMSUBADD213PS = 820
X86_INS_VFMSUBADD231PS = 821
X86_INS_VFMSUBPD = 822
X86_INS_VFMSUB213PD = 823
X86_INS_VFMSUB231PD = 824
X86_INS_VFMSUBPS = 825
X86_INS_VFMSUB213PS = 826
X86_INS_VFMSUB231PS = 827
X86_INS_VFMSUBSD = 828
X86_INS_VFMSUB213SD = 829
X86_INS_VFMSUB132SD = 830
X86_INS_VFMSUB231SD = 831
X86_INS_VFMSUBSS = 832
X86_INS_VFMSUB213SS = 833
X86_INS_VFMSUB132SS = 834
X86_INS_VFMSUB231SS = 835
X86_INS_VFNMADD132PD = 836
X86_INS_VFNMADD132PS = 837
X86_INS_VFNMADDPD = 838
X86_INS_VFNMADD213PD = 839
X86_INS_VFNMADD231PD = 840
X86_INS_VFNMADDPS = 841
X86_INS_VFNMADD213PS = 842
X86_INS_VFNMADD231PS = 843
X86_INS_VFNMADDSD = 844
X86_INS_VFNMADD213SD = 845
X86_INS_VFNMADD132SD = 846
X86_INS_VFNMADD231SD = 847
X86_INS_VFNMADDSS = 848
X86_INS_VFNMADD213SS = 849
X86_INS_VFNMADD132SS = 850
X86_INS_VFNMADD231SS = 851
X86_INS_VFNMSUB132PD = 852
X86_INS_VFNMSUB132PS = 853
X86_INS_VFNMSUBPD = 854
X86_INS_VFNMSUB213PD = 855
X86_INS_VFNMSUB231PD = 856
X86_INS_VFNMSUBPS = 857
X86_INS_VFNMSUB213PS = 858
X86_INS_VFNMSUB231PS = 859
X86_INS_VFNMSUBSD = 860
X86_INS_VFNMSUB213SD = 861
X86_INS_VFNMSUB132SD = 862
X86_INS_VFNMSUB231SD = 863
X86_INS_VFNMSUBSS = 864
X86_INS_VFNMSUB213SS = 865
X86_INS_VFNMSUB132SS = 866
X86_INS_VFNMSUB231SS = 867
X86_INS_VFRCZPD = 868
X86_INS_VFRCZPS = 869
X86_INS_VFRCZSD = 870
X86_INS_VFRCZSS = 871
X86_INS_VORPD = 872
X86_INS_VORPS = 873
X86_INS_VXORPD = 874
X86_INS_VXORPS = 875
X86_INS_VGATHERDPD = 876
X86_INS_VGATHERDPS = 877
X86_INS_VGATHERPF0DPD = 878
X86_INS_VGATHERPF0DPS = 879
X86_INS_VGATHERPF0QPD = 880
X86_INS_VGATHERPF0QPS = 881
X86_INS_VGATHERPF1DPD = 882
X86_INS_VGATHERPF1DPS = 883
X86_INS_VGATHERPF1QPD = 884
X86_INS_VGATHERPF1QPS = 885
X86_INS_VGATHERQPD = 886
X86_INS_VGATHERQPS = 887
X86_INS_VHADDPD = 888
X86_INS_VHADDPS = 889
X86_INS_VHSUBPD = 890
X86_INS_VHSUBPS = 891
X86_INS_VINSERTF128 = 892
X86_INS_VINSERTF32X4 = 893
X86_INS_VINSERTF32X8 = 894
X86_INS_VINSERTF64X2 = 895
X86_INS_VINSERTF64X4 = 896
X86_INS_VINSERTI128 = 897
X86_INS_VINSERTI32X4 = 898
X86_INS_VINSERTI32X8 = 899
X86_INS_VINSERTI64X2 = 900
X86_INS_VINSERTI64X4 = 901
X86_INS_VINSERTPS = 902
X86_INS_VLDDQU = 903
X86_INS_VLDMXCSR = 904
X86_INS_VMASKMOVDQU = 905
X86_INS_VMASKMOVPD = 906
X86_INS_VMASKMOVPS = 907
X86_INS_VMAXPD = 908
X86_INS_VMAXPS = 909
X86_INS_VMAXSD = 910
X86_INS_VMAXSS = 911
X86_INS_VMCALL = 912
X86_INS_VMCLEAR = 913
X86_INS_VMFUNC = 914
X86_INS_VMINPD = 915
X86_INS_VMINPS = 916
X86_INS_VMINSD = 917
X86_INS_VMINSS = 918
X86_INS_VMLAUNCH = 919
X86_INS_VMLOAD = 920
X86_INS_VMMCALL = 921
X86_INS_VMOVQ = 922
X86_INS_VMOVDDUP = 923
X86_INS_VMOVD = 924
X86_INS_VMOVDQA32 = 925
X86_INS_VMOVDQA64 = 926
X86_INS_VMOVDQA = 927
X86_INS_VMOVDQU16 = 928
X86_INS_VMOVDQU32 = 929
X86_INS_VMOVDQU64 = 930
X86_INS_VMOVDQU8 = 931
X86_INS_VMOVDQU = 932
X86_INS_VMOVHLPS = 933
X86_INS_VMOVHPD = 934
X86_INS_VMOVHPS = 935
X86_INS_VMOVLHPS = 936
X86_INS_VMOVLPD = 937
X86_INS_VMOVLPS = 938
X86_INS_VMOVMSKPD = 939
X86_INS_VMOVMSKPS = 940
X86_INS_VMOVNTDQA = 941
X86_INS_VMOVNTDQ = 942
X86_INS_VMOVNTPD = 943
X86_INS_VMOVNTPS = 944
X86_INS_VMOVSD = 945
X86_INS_VMOVSHDUP = 946
X86_INS_VMOVSLDUP = 947
X86_INS_VMOVSS = 948
X86_INS_VMOVUPD = 949
X86_INS_VMOVUPS = 950
X86_INS_VMPSADBW = 951
X86_INS_VMPTRLD = 952
X86_INS_VMPTRST = 953
X86_INS_VMREAD = 954
X86_INS_VMRESUME = 955
X86_INS_VMRUN = 956
X86_INS_VMSAVE = 957
X86_INS_VMULPD = 958
X86_INS_VMULPS = 959
X86_INS_VMULSD = 960
X86_INS_VMULSS = 961
X86_INS_VMWRITE = 962
X86_INS_VMXOFF = 963
X86_INS_VMXON = 964
X86_INS_VPABSB = 965
X86_INS_VPABSD = 966
X86_INS_VPABSQ = 967
X86_INS_VPABSW = 968
X86_INS_VPACKSSDW = 969
X86_INS_VPACKSSWB = 970
X86_INS_VPACKUSDW = 971
X86_INS_VPACKUSWB = 972
X86_INS_VPADDB = 973
X86_INS_VPADDD = 974
X86_INS_VPADDQ = 975
X86_INS_VPADDSB = 976
X86_INS_VPADDSW = 977
X86_INS_VPADDUSB = 978
X86_INS_VPADDUSW = 979
X86_INS_VPADDW = 980
X86_INS_VPALIGNR = 981
X86_INS_VPANDD = 982
X86_INS_VPANDND = 983
X86_INS_VPANDNQ = 984
X86_INS_VPANDN = 985
X86_INS_VPANDQ = 986
X86_INS_VPAND = 987
X86_INS_VPAVGB = 988
X86_INS_VPAVGW = 989
X86_INS_VPBLENDD = 990
X86_INS_VPBLENDMB = 991
X86_INS_VPBLENDMD = 992
X86_INS_VPBLENDMQ = 993
X86_INS_VPBLENDMW = 994
X86_INS_VPBLENDVB = 995
X86_INS_VPBLENDW = 996
X86_INS_VPBROADCASTB = 997
X86_INS_VPBROADCASTD = 998
X86_INS_VPBROADCASTMB2Q = 999
X86_INS_VPBROADCASTMW2D = 1000
X86_INS_VPBROADCASTQ = 1001
X86_INS_VPBROADCASTW = 1002
X86_INS_VPCLMULQDQ = 1003
X86_INS_VPCMOV = 1004
X86_INS_VPCMPB = 1005
X86_INS_VPCMPD = 1006
X86_INS_VPCMPEQB = 1007
X86_INS_VPCMPEQD = 1008
X86_INS_VPCMPEQQ = 1009
X86_INS_VPCMPEQW = 1010
X86_INS_VPCMPESTRI = 1011
X86_INS_VPCMPESTRM = 1012
X86_INS_VPCMPGTB = 1013
X86_INS_VPCMPGTD = 1014
X86_INS_VPCMPGTQ = 1015
X86_INS_VPCMPGTW = 1016
X86_INS_VPCMPISTRI = 1017
X86_INS_VPCMPISTRM = 1018
X86_INS_VPCMPQ = 1019
X86_INS_VPCMPUB = 1020
X86_INS_VPCMPUD = 1021
X86_INS_VPCMPUQ = 1022
X86_INS_VPCMPUW = 1023
X86_INS_VPCMPW = 1024
X86_INS_VPCOMB = 1025
X86_INS_VPCOMD = 1026
X86_INS_VPCOMPRESSD = 1027
X86_INS_VPCOMPRESSQ = 1028
X86_INS_VPCOMQ = 1029
X86_INS_VPCOMUB = 1030
X86_INS_VPCOMUD = 1031
X86_INS_VPCOMUQ = 1032
X86_INS_VPCOMUW = 1033
X86_INS_VPCOMW = 1034
X86_INS_VPCONFLICTD = 1035
X86_INS_VPCONFLICTQ = 1036
X86_INS_VPERM2F128 = 1037
X86_INS_VPERM2I128 = 1038
X86_INS_VPERMD = 1039
X86_INS_VPERMI2D = 1040
X86_INS_VPERMI2PD = 1041
X86_INS_VPERMI2PS = 1042
X86_INS_VPERMI2Q = 1043
X86_INS_VPERMIL2PD = 1044
X86_INS_VPERMIL2PS = 1045
X86_INS_VPERMILPD = 1046
X86_INS_VPERMILPS = 1047
X86_INS_VPERMPD = 1048
X86_INS_VPERMPS = 1049
X86_INS_VPERMQ = 1050
X86_INS_VPERMT2D = 1051
X86_INS_VPERMT2PD = 1052
X86_INS_VPERMT2PS = 1053
X86_INS_VPERMT2Q = 1054
X86_INS_VPEXPANDD = 1055
X86_INS_VPEXPANDQ = 1056
X86_INS_VPEXTRB = 1057
X86_INS_VPEXTRD = 1058
X86_INS_VPEXTRQ = 1059
X86_INS_VPEXTRW = 1060
X86_INS_VPGATHERDD = 1061
X86_INS_VPGATHERDQ = 1062
X86_INS_VPGATHERQD = 1063
X86_INS_VPGATHERQQ = 1064
X86_INS_VPHADDBD = 1065
X86_INS_VPHADDBQ = 1066
X86_INS_VPHADDBW = 1067
X86_INS_VPHADDDQ = 1068
X86_INS_VPHADDD = 1069
X86_INS_VPHADDSW = 1070
X86_INS_VPHADDUBD = 1071
X86_INS_VPHADDUBQ = 1072
X86_INS_VPHADDUBW = 1073
X86_INS_VPHADDUDQ = 1074
X86_INS_VPHADDUWD = 1075
X86_INS_VPHADDUWQ = 1076
X86_INS_VPHADDWD = 1077
X86_INS_VPHADDWQ = 1078
X86_INS_VPHADDW = 1079
X86_INS_VPHMINPOSUW = 1080
X86_INS_VPHSUBBW = 1081
X86_INS_VPHSUBDQ = 1082
X86_INS_VPHSUBD = 1083
X86_INS_VPHSUBSW = 1084
X86_INS_VPHSUBWD = 1085
X86_INS_VPHSUBW = 1086
X86_INS_VPINSRB = 1087
X86_INS_VPINSRD = 1088
X86_INS_VPINSRQ = 1089
X86_INS_VPINSRW = 1090
X86_INS_VPLZCNTD = 1091
X86_INS_VPLZCNTQ = 1092
X86_INS_VPMACSDD = 1093
X86_INS_VPMACSDQH = 1094
X86_INS_VPMACSDQL = 1095
X86_INS_VPMACSSDD = 1096
X86_INS_VPMACSSDQH = 1097
X86_INS_VPMACSSDQL = 1098
X86_INS_VPMACSSWD = 1099
X86_INS_VPMACSSWW = 1100
X86_INS_VPMACSWD = 1101
X86_INS_VPMACSWW = 1102
X86_INS_VPMADCSSWD = 1103
X86_INS_VPMADCSWD = 1104
X86_INS_VPMADDUBSW = 1105
X86_INS_VPMADDWD = 1106
X86_INS_VPMASKMOVD = 1107
X86_INS_VPMASKMOVQ = 1108
X86_INS_VPMAXSB = 1109
X86_INS_VPMAXSD = 1110
X86_INS_VPMAXSQ = 1111
X86_INS_VPMAXSW = 1112
X86_INS_VPMAXUB = 1113
X86_INS_VPMAXUD = 1114
X86_INS_VPMAXUQ = 1115
X86_INS_VPMAXUW = 1116
X86_INS_VPMINSB = 1117
X86_INS_VPMINSD = 1118
X86_INS_VPMINSQ = 1119
X86_INS_VPMINSW = 1120
X86_INS_VPMINUB = 1121
X86_INS_VPMINUD = 1122
X86_INS_VPMINUQ = 1123
X86_INS_VPMINUW = 1124
X86_INS_VPMOVDB = 1125
X86_INS_VPMOVDW = 1126
X86_INS_VPMOVM2B = 1127
X86_INS_VPMOVM2D = 1128
X86_INS_VPMOVM2Q = 1129
X86_INS_VPMOVM2W = 1130
X86_INS_VPMOVMSKB = 1131
X86_INS_VPMOVQB = 1132
X86_INS_VPMOVQD = 1133
X86_INS_VPMOVQW = 1134
X86_INS_VPMOVSDB = 1135
X86_INS_VPMOVSDW = 1136
X86_INS_VPMOVSQB = 1137
X86_INS_VPMOVSQD = 1138
X86_INS_VPMOVSQW = 1139
X86_INS_VPMOVSXBD = 1140
X86_INS_VPMOVSXBQ = 1141
X86_INS_VPMOVSXBW = 1142
X86_INS_VPMOVSXDQ = 1143
X86_INS_VPMOVSXWD = 1144
X86_INS_VPMOVSXWQ = 1145
X86_INS_VPMOVUSDB = 1146
X86_INS_VPMOVUSDW = 1147
X86_INS_VPMOVUSQB = 1148
X86_INS_VPMOVUSQD = 1149
X86_INS_VPMOVUSQW = 1150
X86_INS_VPMOVZXBD = 1151
X86_INS_VPMOVZXBQ = 1152
X86_INS_VPMOVZXBW = 1153
X86_INS_VPMOVZXDQ = 1154
X86_INS_VPMOVZXWD = 1155
X86_INS_VPMOVZXWQ = 1156
X86_INS_VPMULDQ = 1157
X86_INS_VPMULHRSW = 1158
X86_INS_VPMULHUW = 1159
X86_INS_VPMULHW = 1160
X86_INS_VPMULLD = 1161
X86_INS_VPMULLQ = 1162
X86_INS_VPMULLW = 1163
X86_INS_VPMULUDQ = 1164
X86_INS_VPORD = 1165
X86_INS_VPORQ = 1166
X86_INS_VPOR = 1167
X86_INS_VPPERM = 1168
X86_INS_VPROTB = 1169
X86_INS_VPROTD = 1170
X86_INS_VPROTQ = 1171
X86_INS_VPROTW = 1172
X86_INS_VPSADBW = 1173
X86_INS_VPSCATTERDD = 1174
X86_INS_VPSCATTERDQ = 1175
X86_INS_VPSCATTERQD = 1176
X86_INS_VPSCATTERQQ = 1177
X86_INS_VPSHAB = 1178
X86_INS_VPSHAD = 1179
X86_INS_VPSHAQ = 1180
X86_INS_VPSHAW = 1181
X86_INS_VPSHLB = 1182
X86_INS_VPSHLD = 1183
X86_INS_VPSHLQ = 1184
X86_INS_VPSHLW = 1185
X86_INS_VPSHUFB = 1186
X86_INS_VPSHUFD = 1187
X86_INS_VPSHUFHW = 1188
X86_INS_VPSHUFLW = 1189
X86_INS_VPSIGNB = 1190
X86_INS_VPSIGND = 1191
X86_INS_VPSIGNW = 1192
X86_INS_VPSLLDQ = 1193
X86_INS_VPSLLD = 1194
X86_INS_VPSLLQ = 1195
X86_INS_VPSLLVD = 1196
X86_INS_VPSLLVQ = 1197
X86_INS_VPSLLW = 1198
X86_INS_VPSRAD = 1199
X86_INS_VPSRAQ = 1200
X86_INS_VPSRAVD = 1201
X86_INS_VPSRAVQ = 1202
X86_INS_VPSRAW = 1203
X86_INS_VPSRLDQ = 1204
X86_INS_VPSRLD = 1205
X86_INS_VPSRLQ = 1206
X86_INS_VPSRLVD = 1207
X86_INS_VPSRLVQ = 1208
X86_INS_VPSRLW = 1209
X86_INS_VPSUBB = 1210
X86_INS_VPSUBD = 1211
X86_INS_VPSUBQ = 1212
X86_INS_VPSUBSB = 1213
X86_INS_VPSUBSW = 1214
X86_INS_VPSUBUSB = 1215
X86_INS_VPSUBUSW = 1216
X86_INS_VPSUBW = 1217
X86_INS_VPTESTMD = 1218
X86_INS_VPTESTMQ = 1219
X86_INS_VPTESTNMD = 1220
X86_INS_VPTESTNMQ = 1221
X86_INS_VPTEST = 1222
X86_INS_VPUNPCKHBW = 1223
X86_INS_VPUNPCKHDQ = 1224
X86_INS_VPUNPCKHQDQ = 1225
X86_INS_VPUNPCKHWD = 1226
X86_INS_VPUNPCKLBW = 1227
X86_INS_VPUNPCKLDQ = 1228
X86_INS_VPUNPCKLQDQ = 1229
X86_INS_VPUNPCKLWD = 1230
X86_INS_VPXORD = 1231
X86_INS_VPXORQ = 1232
X86_INS_VPXOR = 1233
X86_INS_VRCP14PD = 1234
X86_INS_VRCP14PS = 1235
X86_INS_VRCP14SD = 1236
X86_INS_VRCP14SS = 1237
X86_INS_VRCP28PD = 1238
X86_INS_VRCP28PS = 1239
X86_INS_VRCP28SD = 1240
X86_INS_VRCP28SS = 1241
X86_INS_VRCPPS = 1242
X86_INS_VRCPSS = 1243
X86_INS_VRNDSCALEPD = 1244
X86_INS_VRNDSCALEPS = 1245
X86_INS_VRNDSCALESD = 1246
X86_INS_VRNDSCALESS = 1247
X86_INS_VROUNDPD = 1248
X86_INS_VROUNDPS = 1249
X86_INS_VROUNDSD = 1250
X86_INS_VROUNDSS = 1251
X86_INS_VRSQRT14PD = 1252
X86_INS_VRSQRT14PS = 1253
X86_INS_VRSQRT14SD = 1254
X86_INS_VRSQRT14SS = 1255
X86_INS_VRSQRT28PD = 1256
X86_INS_VRSQRT28PS = 1257
X86_INS_VRSQRT28SD = 1258
X86_INS_VRSQRT28SS = 1259
X86_INS_VRSQRTPS = 1260
X86_INS_VRSQRTSS = 1261
X86_INS_VSCATTERDPD = 1262
X86_INS_VSCATTERDPS = 1263
X86_INS_VSCATTERPF0DPD = 1264
X86_INS_VSCATTERPF0DPS = 1265
X86_INS_VSCATTERPF0QPD = 1266
X86_INS_VSCATTERPF0QPS = 1267
X86_INS_VSCATTERPF1DPD = 1268
X86_INS_VSCATTERPF1DPS = 1269
X86_INS_VSCATTERPF1QPD = 1270
X86_INS_VSCATTERPF1QPS = 1271
X86_INS_VSCATTERQPD = 1272
X86_INS_VSCATTERQPS = 1273
X86_INS_VSHUFPD = 1274
X86_INS_VSHUFPS = 1275
X86_INS_VSQRTPD = 1276
X86_INS_VSQRTPS = 1277
X86_INS_VSQRTSD = 1278
X86_INS_VSQRTSS = 1279
X86_INS_VSTMXCSR = 1280
X86_INS_VSUBPD = 1281
X86_INS_VSUBPS = 1282
X86_INS_VSUBSD = 1283
X86_INS_VSUBSS = 1284
X86_INS_VTESTPD = 1285
X86_INS_VTESTPS = 1286
X86_INS_VUNPCKHPD = 1287
X86_INS_VUNPCKHPS = 1288
X86_INS_VUNPCKLPD = 1289
X86_INS_VUNPCKLPS = 1290
X86_INS_VZEROALL = 1291
X86_INS_VZEROUPPER = 1292
X86_INS_WAIT = 1293
X86_INS_WBINVD = 1294
X86_INS_WRFSBASE = 1295
X86_INS_WRGSBASE = 1296
X86_INS_WRMSR = 1297
X86_INS_XABORT = 1298
X86_INS_XACQUIRE = 1299
X86_INS_XBEGIN = 1300
X86_INS_XCHG = 1301
X86_INS_XCRYPTCBC = 1302
X86_INS_XCRYPTCFB = 1303
X86_INS_XCRYPTCTR = 1304
X86_INS_XCRYPTECB = 1305
X86_INS_XCRYPTOFB = 1306
X86_INS_XEND = 1307
X86_INS_XGETBV = 1308
X86_INS_XLATB = 1309
X86_INS_XRELEASE = 1310
X86_INS_XRSTOR = 1311
X86_INS_XRSTOR64 = 1312
X86_INS_XRSTORS = 1313
X86_INS_XRSTORS64 = 1314
X86_INS_XSAVE = 1315
X86_INS_XSAVE64 = 1316
X86_INS_XSAVEC = 1317
X86_INS_XSAVEC64 = 1318
X86_INS_XSAVEOPT = 1319
X86_INS_XSAVEOPT64 = 1320
X86_INS_XSAVES = 1321
X86_INS_XSAVES64 = 1322
X86_INS_XSETBV = 1323
X86_INS_XSHA1 = 1324
X86_INS_XSHA256 = 1325
X86_INS_XSTORE = 1326
X86_INS_XTEST = 1327
X86_INS_FDISI8087_NOP = 1328
X86_INS_FENI8087_NOP = 1329
X86_INS_CMPSS = 1330
X86_INS_CMPEQSS = 1331
X86_INS_CMPLTSS = 1332
X86_INS_CMPLESS = 1333
X86_INS_CMPUNORDSS = 1334
X86_INS_CMPNEQSS = 1335
X86_INS_CMPNLTSS = 1336
X86_INS_CMPNLESS = 1337
X86_INS_CMPORDSS = 1338
X86_INS_CMPSD = 1339
X86_INS_CMPEQSD = 1340
X86_INS_CMPLTSD = 1341
X86_INS_CMPLESD = 1342
X86_INS_CMPUNORDSD = 1343
X86_INS_CMPNEQSD = 1344
X86_INS_CMPNLTSD = 1345
X86_INS_CMPNLESD = 1346
X86_INS_CMPORDSD = 1347
X86_INS_CMPPS = 1348
X86_INS_CMPEQPS = 1349
X86_INS_CMPLTPS = 1350
X86_INS_CMPLEPS = 1351
X86_INS_CMPUNORDPS = 1352
X86_INS_CMPNEQPS = 1353
X86_INS_CMPNLTPS = 1354
X86_INS_CMPNLEPS = 1355
X86_INS_CMPORDPS = 1356
X86_INS_CMPPD = 1357
X86_INS_CMPEQPD = 1358
X86_INS_CMPLTPD = 1359
X86_INS_CMPLEPD = 1360
X86_INS_CMPUNORDPD = 1361
X86_INS_CMPNEQPD = 1362
X86_INS_CMPNLTPD = 1363
X86_INS_CMPNLEPD = 1364
X86_INS_CMPORDPD = 1365
X86_INS_VCMPSS = 1366
X86_INS_VCMPEQSS = 1367
X86_INS_VCMPLTSS = 1368
X86_INS_VCMPLESS = 1369
X86_INS_VCMPUNORDSS = 1370
X86_INS_VCMPNEQSS = 1371
X86_INS_VCMPNLTSS = 1372
X86_INS_VCMPNLESS = 1373
X86_INS_VCMPORDSS = 1374
X86_INS_VCMPEQ_UQSS = 1375
X86_INS_VCMPNGESS = 1376
X86_INS_VCMPNGTSS = 1377
X86_INS_VCMPFALSESS = 1378
X86_INS_VCMPNEQ_OQSS = 1379
X86_INS_VCMPGESS = 1380
X86_INS_VCMPGTSS = 1381
X86_INS_VCMPTRUESS = 1382
X86_INS_VCMPEQ_OSSS = 1383
X86_INS_VCMPLT_OQSS = 1384
X86_INS_VCMPLE_OQSS = 1385
X86_INS_VCMPUNORD_SSS = 1386
X86_INS_VCMPNEQ_USSS = 1387
X86_INS_VCMPNLT_UQSS = 1388
X86_INS_VCMPNLE_UQSS = 1389
X86_INS_VCMPORD_SSS = 1390
X86_INS_VCMPEQ_USSS = 1391
X86_INS_VCMPNGE_UQSS = 1392
X86_INS_VCMPNGT_UQSS = 1393
X86_INS_VCMPFALSE_OSSS = 1394
X86_INS_VCMPNEQ_OSSS = 1395
X86_INS_VCMPGE_OQSS = 1396
X86_INS_VCMPGT_OQSS = 1397
X86_INS_VCMPTRUE_USSS = 1398
X86_INS_VCMPSD = 1399
X86_INS_VCMPEQSD = 1400
X86_INS_VCMPLTSD = 1401
X86_INS_VCMPLESD = 1402
X86_INS_VCMPUNORDSD = 1403
X86_INS_VCMPNEQSD = 1404
X86_INS_VCMPNLTSD = 1405
X86_INS_VCMPNLESD = 1406
X86_INS_VCMPORDSD = 1407
X86_INS_VCMPEQ_UQSD = 1408
X86_INS_VCMPNGESD = 1409
X86_INS_VCMPNGTSD = 1410
X86_INS_VCMPFALSESD = 1411
X86_INS_VCMPNEQ_OQSD = 1412
X86_INS_VCMPGESD = 1413
X86_INS_VCMPGTSD = 1414
X86_INS_VCMPTRUESD = 1415
X86_INS_VCMPEQ_OSSD = 1416
X86_INS_VCMPLT_OQSD = 1417
X86_INS_VCMPLE_OQSD = 1418
X86_INS_VCMPUNORD_SSD = 1419
X86_INS_VCMPNEQ_USSD = 1420
X86_INS_VCMPNLT_UQSD = 1421
X86_INS_VCMPNLE_UQSD = 1422
X86_INS_VCMPORD_SSD = 1423
X86_INS_VCMPEQ_USSD = 1424
X86_INS_VCMPNGE_UQSD = 1425
X86_INS_VCMPNGT_UQSD = 1426
X86_INS_VCMPFALSE_OSSD = 1427
X86_INS_VCMPNEQ_OSSD = 1428
X86_INS_VCMPGE_OQSD = 1429
X86_INS_VCMPGT_OQSD = 1430
X86_INS_VCMPTRUE_USSD = 1431
X86_INS_VCMPPS = 1432
X86_INS_VCMPEQPS = 1433
X86_INS_VCMPLTPS = 1434
X86_INS_VCMPLEPS = 1435
X86_INS_VCMPUNORDPS = 1436
X86_INS_VCMPNEQPS = 1437
X86_INS_VCMPNLTPS = 1438
X86_INS_VCMPNLEPS = 1439
X86_INS_VCMPORDPS = 1440
X86_INS_VCMPEQ_UQPS = 1441
X86_INS_VCMPNGEPS = 1442
X86_INS_VCMPNGTPS = 1443
X86_INS_VCMPFALSEPS = 1444
X86_INS_VCMPNEQ_OQPS = 1445
X86_INS_VCMPGEPS = 1446
X86_INS_VCMPGTPS = 1447
X86_INS_VCMPTRUEPS = 1448
X86_INS_VCMPEQ_OSPS = 1449
X86_INS_VCMPLT_OQPS = 1450
X86_INS_VCMPLE_OQPS = 1451
X86_INS_VCMPUNORD_SPS = 1452
X86_INS_VCMPNEQ_USPS = 1453
X86_INS_VCMPNLT_UQPS = 1454
X86_INS_VCMPNLE_UQPS = 1455
X86_INS_VCMPORD_SPS = 1456
X86_INS_VCMPEQ_USPS = 1457
X86_INS_VCMPNGE_UQPS = 1458
X86_INS_VCMPNGT_UQPS = 1459
X86_INS_VCMPFALSE_OSPS = 1460
X86_INS_VCMPNEQ_OSPS = 1461
X86_INS_VCMPGE_OQPS = 1462
X86_INS_VCMPGT_OQPS = 1463
X86_INS_VCMPTRUE_USPS = 1464
X86_INS_VCMPPD = 1465
X86_INS_VCMPEQPD = 1466
X86_INS_VCMPLTPD = 1467
X86_INS_VCMPLEPD = 1468
X86_INS_VCMPUNORDPD = 1469
X86_INS_VCMPNEQPD = 1470
X86_INS_VCMPNLTPD = 1471
X86_INS_VCMPNLEPD = 1472
X86_INS_VCMPORDPD = 1473
X86_INS_VCMPEQ_UQPD = 1474
X86_INS_VCMPNGEPD = 1475
X86_INS_VCMPNGTPD = 1476
X86_INS_VCMPFALSEPD = 1477
X86_INS_VCMPNEQ_OQPD = 1478
X86_INS_VCMPGEPD = 1479
X86_INS_VCMPGTPD = 1480
X86_INS_VCMPTRUEPD = 1481
X86_INS_VCMPEQ_OSPD = 1482
X86_INS_VCMPLT_OQPD = 1483
X86_INS_VCMPLE_OQPD = 1484
X86_INS_VCMPUNORD_SPD = 1485
X86_INS_VCMPNEQ_USPD = 1486
X86_INS_VCMPNLT_UQPD = 1487
X86_INS_VCMPNLE_UQPD = 1488
X86_INS_VCMPORD_SPD = 1489
X86_INS_VCMPEQ_USPD = 1490
X86_INS_VCMPNGE_UQPD = 1491
X86_INS_VCMPNGT_UQPD = 1492
X86_INS_VCMPFALSE_OSPD = 1493
X86_INS_VCMPNEQ_OSPD = 1494
X86_INS_VCMPGE_OQPD = 1495
X86_INS_VCMPGT_OQPD = 1496
X86_INS_VCMPTRUE_USPD = 1497
X86_INS_UD0 = 1498
X86_INS_ENDBR32 = 1499
X86_INS_ENDBR64 = 1500
X86_INS_ENDING = 1501

# Group of X86 instructions

X86_GRP_INVALID = 0

# Generic groups
X86_GRP_JUMP = 1
X86_GRP_CALL = 2
X86_GRP_RET = 3
X86_GRP_INT = 4
X86_GRP_IRET = 5
X86_GRP_PRIVILEGE = 6
X86_GRP_BRANCH_RELATIVE = 7

# Architecture-specific groups
X86_GRP_VM = 128
X86_GRP_3DNOW = 129
X86_GRP_AES = 130
X86_GRP_ADX = 131
X86_GRP_AVX = 132
X86_GRP_AVX2 = 133
X86_GRP_AVX512 = 134
X86_GRP_BMI = 135
X86_GRP_BMI2 = 136
X86_GRP_CMOV = 137
X86_GRP_F16C = 138
X86_GRP_FMA = 139
X86_GRP_FMA4 = 140
X86_GRP_FSGSBASE = 141
X86_GRP_HLE = 142
X86_GRP_MMX = 143
X86_GRP_MODE32 = 144
X86_GRP_MODE64 = 145
X86_GRP_RTM = 146
X86_GRP_SHA = 147
X86_GRP_SSE1 = 148
X86_GRP_SSE2 = 149
X86_GRP_SSE3 = 150
X86_GRP_SSE41 = 151
X86_GRP_SSE42 = 152
X86_GRP_SSE4A = 153
X86_GRP_SSSE3 = 154
X86_GRP_PCLMUL = 155
X86_GRP_XOP = 156
X86_GRP_CDI = 157
X86_GRP_ERI = 158
X86_GRP_TBM = 159
X86_GRP_16BITMODE = 160
X86_GRP_NOT64BITMODE = 161
X86_GRP_SGX = 162
X86_GRP_DQI = 163
X86_GRP_BWI = 164
X86_GRP_PFI = 165
X86_GRP_VLX = 166
X86_GRP_SMAP = 167
X86_GRP_NOVLX = 168
X86_GRP_FPU = 169
X86_GRP_ENDING = 170
