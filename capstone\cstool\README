This directory contains cstool of Capstone Engine.

Cstool is a command-line tool to disassemble assembly hex-string.
For example, to decode a hexcode string for Intel 32bit, run:

	$ cstool x32 "90 91"

	0	90	nop
	1	91	xchg	eax, ecx

Cstool disassembles the input and prints out the assembly instructions.
On each line, the first column is the instruction offset, the second
column is opcodes, and the rest is the instruction itself.

Cstool is flexible enough to accept all kind of hexcode format. The following
inputs have the same output with the example above.

	$ cstool x32 "0x90 0x91"
	$ cstool x32 "\x90\x91"
	$ cstool x32 "90,91"
	$ cstool x32 "90;91"
	$ cstool x32 "90+91"
	$ cstool x32 "90:91"

To print out instruction details, run Cstool with -d option, like below.

	$ cstool -d x32 "01 d8"
	0  01d8                              add	eax, ebx
	Prefix:0x00 0x00 0x00 0x00
	Opcode:0x01 0x00 0x00 0x00
	rex: 0x0
	addr_size: 4
	modrm: 0xd8
	disp: 0x0
	sib: 0x0
	op_count: 2
		operands[0].type: REG = eax
		operands[0].size: 4
		operands[0].access: READ | WRITE
		operands[1].type: REG = ebx
		operands[1].size: 4
		operands[1].access: READ
		Registers read: eax ebx
	Registers modified: eflags eax
	EFLAGS: MOD_AF MOD_CF MOD_SF MOD_ZF MOD_PF MOD_OF

To see all the supported options, run ./cstool
