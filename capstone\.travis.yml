language: cpp
sudo: false
before_install:
        - export LD_LIBRARY_PATH=`pwd`/tests/:$LD_LIBRARY_PATH
script:
        - ./make.sh
        - make check
        - if [[ "$TRAVIS_OS_NAME" == "linux" ]]; then cp libcapstone.so.* bindings/python/libcapstone.so; fi
        - if [[ "$TRAVIS_OS_NAME" == "osx" ]]; then cp libcapstone.*.dylib bindings/python/libcapstone.dylib; fi
        - cd bindings/python && make check
compiler:
        - clang
        - gcc
os:
        - linux
        - osx
