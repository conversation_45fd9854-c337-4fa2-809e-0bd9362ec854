name: C/C++ CI

on:
  workflow_dispatch:

jobs:
  build:

    runs-on: windows-latest
    # runs-on: windows-2019

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
       
    # - name: Install Windows SDK and WDK
    #   shell: powershell
    #   run: |
    #     # 下载 Windows 10 SDK (这是WDK的依赖)
    #     Invoke-WebRequest -Uri 'https://go.microsoft.com/fwlink/?linkid=2120843' -OutFile 'winsdksetup.exe'
    #     # 静默安装 SDK
    #     Start-Process -FilePath 'winsdksetup.exe' -ArgumentList '/install /quiet /norestart' -Wait

    #     # 下载 Windows 10 WDK
    #     Invoke-WebRequest -Uri 'https://go.microsoft.com/fwlink/?linkid=2120844' -OutFile 'wdksetup.exe'
    #     # 静默安装 WDK
    #     Start-Process -FilePath 'wdksetup.exe' -ArgumentList '/install /quiet /norestart' -Wait
    # - name: Install Windows SDK and WDK
    #   shell: powershell
    #   run: |
    #     # 下载适用于 Windows 11 / Server 2022 的 SDK
    #     Invoke-WebRequest -Uri 'https://go.microsoft.com/fwlink/?linkid=2196241' -OutFile 'winsdksetup.exe'
    #     Start-Process -FilePath 'winsdksetup.exe' -ArgumentList '/install /quiet /norestart' -Wait

    #     # 下载适用于 Windows 11 / Server 2022 的 WDK
    #     Invoke-WebRequest -Uri 'https://go.microsoft.com/fwlink/?linkid=2196231' -OutFile 'wdksetup.exe'
    #     Start-Process -FilePath 'wdksetup.exe' -ArgumentList '/install /quiet /norestart' -Wait
    - name: Install WDK Insider Preview
      shell: pwsh
      run: |
        choco install windowsdriverkit10 --yes --limit-output
        
    - name: Setup MSBuild
      uses: microsoft/setup-msbuild@v1.1

    - name: Build Solution
      run: msbuild VmLoader.sln /p:Configuration=Release /p:Platform=x64 /p:WindowsTargetPlatformVersion=10.0.19041.0
  
    # - name: Setup MSBuild
    #   uses: microsoft/setup-msbuild@v1.1
  
    # - name: Build Solution
      # run: msbuild VmLoader.sln /p:Configuration=Release
      # run: msbuild VmLoader.sln /p:Configuration=Release /p:WindowsTargetPlatformVersion=10.0.19041.0 /p:AdditionalIncludeDirectories="C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\km"
      
    # - name: Find WDK Path
    #   id: find-wdk
    #   run: |
    #     $wdk_path = (Get-ChildItem -Path "C:\Program Files (x86)\Windows Kits\10\Include" -Filter "km" -Recurse | Select-Object -First 1).DirectoryName
    #     echo "WDK_INCLUDE_PATH=$wdk_path" >> $env:GITHUB_OUTPUT

    # - name: Build Solution
    #   run: msbuild VmLoader.sln /p:Configuration=Release /p:WindowsTargetPlatformVersion=10.0.19041.0 /p:Platform=x64 /p:AdditionalIncludeDirectories="${{ steps.find-wdk.outputs.WDK_INCLUDE_PATH }}"
