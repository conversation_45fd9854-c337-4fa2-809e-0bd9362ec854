:: Capstone disassembler engine (www.capstone-engine.org)
:: Build Capstone libs for X86 only (capstone.dll & capstone.lib) on Windows with CMake & Nmake
:: By <PERSON><PERSON><PERSON>, 2017

:: cmake -DCMAKE_BUILD_TYPE=Release -DCAPSTONE_BUILD_DIET=ON -DCAPSTONE_ARM_SUPPORT=0 -DCAPSTONE_ARM64_SUPPORT=0 -DCAPSTONE_M68K_SUPPORT=0 -DCAPSTONE_MIPS_SUPPORT=0 -DCAPSTONE_PPC_SUPPORT=0 -DCAPSTONE_SPARC_SUPPORT=0 -DCAPSTONE_SYSZ_SUPPORT=0 -DCAPSTONE_XCORE_SUPPORT=0 -DCAPSTONE_TMS320C64X_SUPPORT=0 -DCAPSTONE_BUILD_STATIC_RUNTIME=OFF -G "NMake Makefiles" ..

cmake -DCMAKE_BUILD_TYPE=Release -DCAPSTONE_ARM_SUPPORT=0 -DCAPSTONE_ARM64_SUPPORT=0 -DCAPSTONE_M68K_SUPPORT=0 -DCAPSTONE_MIPS_SUPPORT=0 -DCAPSTONE_PPC_SUPPORT=0 -DCAPSTONE_SPARC_SUPPORT=0 -DCAPSTONE_SYSZ_SUPPORT=0 -DCAPSTONE_XCORE_SUPPORT=0 -DCAPSTONE_TMS320C64X_SUPPORT=0 -DCAPSTONE_BUILD_STATIC_RUNTIME=OFF -G "NMake Makefiles" ..

nmake

